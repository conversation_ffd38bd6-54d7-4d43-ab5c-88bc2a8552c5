<?php
// This file was auto-generated from sdk-root/src/data/sns/2010-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2010-03-31', 'endpointPrefix' => 'sns', 'protocol' => 'query', 'serviceAbbreviation' => 'Amazon SNS', 'serviceFullName' => 'Amazon Simple Notification Service', 'serviceId' => 'SNS', 'signatureVersion' => 'v4', 'uid' => 'sns-2010-03-31', 'xmlNamespace' => 'http://sns.amazonaws.com/doc/2010-03-31/', ], 'operations' => [ 'AddPermission' => [ 'name' => 'AddPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddPermissionInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CheckIfPhoneNumberIsOptedOut' => [ 'name' => 'CheckIfPhoneNumberIsOptedOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckIfPhoneNumberIsOptedOutInput', ], 'output' => [ 'shape' => 'CheckIfPhoneNumberIsOptedOutResponse', 'resultWrapper' => 'CheckIfPhoneNumberIsOptedOutResult', ], 'errors' => [ [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ConfirmSubscription' => [ 'name' => 'ConfirmSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConfirmSubscriptionInput', ], 'output' => [ 'shape' => 'ConfirmSubscriptionResponse', 'resultWrapper' => 'ConfirmSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'FilterPolicyLimitExceededException', ], [ 'shape' => 'ReplayLimitExceededException', ], ], ], 'CreatePlatformApplication' => [ 'name' => 'CreatePlatformApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePlatformApplicationInput', ], 'output' => [ 'shape' => 'CreatePlatformApplicationResponse', 'resultWrapper' => 'CreatePlatformApplicationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'CreatePlatformEndpoint' => [ 'name' => 'CreatePlatformEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePlatformEndpointInput', ], 'output' => [ 'shape' => 'CreateEndpointResponse', 'resultWrapper' => 'CreatePlatformEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CreateSMSSandboxPhoneNumber' => [ 'name' => 'CreateSMSSandboxPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSMSSandboxPhoneNumberInput', ], 'output' => [ 'shape' => 'CreateSMSSandboxPhoneNumberResult', 'resultWrapper' => 'CreateSMSSandboxPhoneNumberResult', ], 'errors' => [ [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OptedOutException', ], [ 'shape' => 'UserErrorException', ], [ 'shape' => 'ThrottledException', ], ], ], 'CreateTopic' => [ 'name' => 'CreateTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTopicInput', ], 'output' => [ 'shape' => 'CreateTopicResponse', 'resultWrapper' => 'CreateTopicResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TopicLimitExceededException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'StaleTagException', ], [ 'shape' => 'TagPolicyException', ], [ 'shape' => 'ConcurrentAccessException', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'DeletePlatformApplication' => [ 'name' => 'DeletePlatformApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePlatformApplicationInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'DeleteSMSSandboxPhoneNumber' => [ 'name' => 'DeleteSMSSandboxPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSMSSandboxPhoneNumberInput', ], 'output' => [ 'shape' => 'DeleteSMSSandboxPhoneNumberResult', 'resultWrapper' => 'DeleteSMSSandboxPhoneNumberResult', ], 'errors' => [ [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UserErrorException', ], [ 'shape' => 'ThrottledException', ], ], ], 'DeleteTopic' => [ 'name' => 'DeleteTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTopicInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'StaleTagException', ], [ 'shape' => 'TagPolicyException', ], [ 'shape' => 'ConcurrentAccessException', ], ], ], 'GetDataProtectionPolicy' => [ 'name' => 'GetDataProtectionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataProtectionPolicyInput', ], 'output' => [ 'shape' => 'GetDataProtectionPolicyResponse', 'resultWrapper' => 'GetDataProtectionPolicyResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'GetEndpointAttributes' => [ 'name' => 'GetEndpointAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEndpointAttributesInput', ], 'output' => [ 'shape' => 'GetEndpointAttributesResponse', 'resultWrapper' => 'GetEndpointAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetPlatformApplicationAttributes' => [ 'name' => 'GetPlatformApplicationAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPlatformApplicationAttributesInput', ], 'output' => [ 'shape' => 'GetPlatformApplicationAttributesResponse', 'resultWrapper' => 'GetPlatformApplicationAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetSMSAttributes' => [ 'name' => 'GetSMSAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSMSAttributesInput', ], 'output' => [ 'shape' => 'GetSMSAttributesResponse', 'resultWrapper' => 'GetSMSAttributesResult', ], 'errors' => [ [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GetSMSSandboxAccountStatus' => [ 'name' => 'GetSMSSandboxAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSMSSandboxAccountStatusInput', ], 'output' => [ 'shape' => 'GetSMSSandboxAccountStatusResult', 'resultWrapper' => 'GetSMSSandboxAccountStatusResult', ], 'errors' => [ [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetSubscriptionAttributes' => [ 'name' => 'GetSubscriptionAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSubscriptionAttributesInput', ], 'output' => [ 'shape' => 'GetSubscriptionAttributesResponse', 'resultWrapper' => 'GetSubscriptionAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'GetTopicAttributes' => [ 'name' => 'GetTopicAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTopicAttributesInput', ], 'output' => [ 'shape' => 'GetTopicAttributesResponse', 'resultWrapper' => 'GetTopicAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'ListEndpointsByPlatformApplication' => [ 'name' => 'ListEndpointsByPlatformApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointsByPlatformApplicationInput', ], 'output' => [ 'shape' => 'ListEndpointsByPlatformApplicationResponse', 'resultWrapper' => 'ListEndpointsByPlatformApplicationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListOriginationNumbers' => [ 'name' => 'ListOriginationNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOriginationNumbersRequest', ], 'output' => [ 'shape' => 'ListOriginationNumbersResult', 'resultWrapper' => 'ListOriginationNumbersResult', ], 'errors' => [ [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPhoneNumbersOptedOut' => [ 'name' => 'ListPhoneNumbersOptedOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPhoneNumbersOptedOutInput', ], 'output' => [ 'shape' => 'ListPhoneNumbersOptedOutResponse', 'resultWrapper' => 'ListPhoneNumbersOptedOutResult', ], 'errors' => [ [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListPlatformApplications' => [ 'name' => 'ListPlatformApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPlatformApplicationsInput', ], 'output' => [ 'shape' => 'ListPlatformApplicationsResponse', 'resultWrapper' => 'ListPlatformApplicationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'ListSMSSandboxPhoneNumbers' => [ 'name' => 'ListSMSSandboxPhoneNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSMSSandboxPhoneNumbersInput', ], 'output' => [ 'shape' => 'ListSMSSandboxPhoneNumbersResult', 'resultWrapper' => 'ListSMSSandboxPhoneNumbersResult', ], 'errors' => [ [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottledException', ], ], ], 'ListSubscriptions' => [ 'name' => 'ListSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSubscriptionsInput', ], 'output' => [ 'shape' => 'ListSubscriptionsResponse', 'resultWrapper' => 'ListSubscriptionsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'ListSubscriptionsByTopic' => [ 'name' => 'ListSubscriptionsByTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSubscriptionsByTopicInput', ], 'output' => [ 'shape' => 'ListSubscriptionsByTopicResponse', 'resultWrapper' => 'ListSubscriptionsByTopicResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', 'resultWrapper' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TagPolicyException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'ConcurrentAccessException', ], ], ], 'ListTopics' => [ 'name' => 'ListTopics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTopicsInput', ], 'output' => [ 'shape' => 'ListTopicsResponse', 'resultWrapper' => 'ListTopicsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'OptInPhoneNumber' => [ 'name' => 'OptInPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'OptInPhoneNumberInput', ], 'output' => [ 'shape' => 'OptInPhoneNumberResponse', 'resultWrapper' => 'OptInPhoneNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'Publish' => [ 'name' => 'Publish', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PublishInput', ], 'output' => [ 'shape' => 'PublishResponse', 'resultWrapper' => 'PublishResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'EndpointDisabledException', ], [ 'shape' => 'PlatformApplicationDisabledException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'KMSDisabledException', ], [ 'shape' => 'KMSInvalidStateException', ], [ 'shape' => 'KMSNotFoundException', ], [ 'shape' => 'KMSOptInRequired', ], [ 'shape' => 'KMSThrottlingException', ], [ 'shape' => 'KMSAccessDeniedException', ], [ 'shape' => 'InvalidSecurityException', ], [ 'shape' => 'ValidationException', ], ], ], 'PublishBatch' => [ 'name' => 'PublishBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PublishBatchInput', ], 'output' => [ 'shape' => 'PublishBatchResponse', 'resultWrapper' => 'PublishBatchResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'EndpointDisabledException', ], [ 'shape' => 'PlatformApplicationDisabledException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'BatchEntryIdsNotDistinctException', ], [ 'shape' => 'BatchRequestTooLongException', ], [ 'shape' => 'EmptyBatchRequestException', ], [ 'shape' => 'InvalidBatchEntryIdException', ], [ 'shape' => 'TooManyEntriesInBatchRequestException', ], [ 'shape' => 'KMSDisabledException', ], [ 'shape' => 'KMSInvalidStateException', ], [ 'shape' => 'KMSNotFoundException', ], [ 'shape' => 'KMSOptInRequired', ], [ 'shape' => 'KMSThrottlingException', ], [ 'shape' => 'KMSAccessDeniedException', ], [ 'shape' => 'InvalidSecurityException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutDataProtectionPolicy' => [ 'name' => 'PutDataProtectionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDataProtectionPolicyInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'RemovePermission' => [ 'name' => 'RemovePermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemovePermissionInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'SetEndpointAttributes' => [ 'name' => 'SetEndpointAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetEndpointAttributesInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'SetPlatformApplicationAttributes' => [ 'name' => 'SetPlatformApplicationAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetPlatformApplicationAttributesInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'SetSMSAttributes' => [ 'name' => 'SetSMSAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetSMSAttributesInput', ], 'output' => [ 'shape' => 'SetSMSAttributesResponse', 'resultWrapper' => 'SetSMSAttributesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'SetSubscriptionAttributes' => [ 'name' => 'SetSubscriptionAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetSubscriptionAttributesInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'FilterPolicyLimitExceededException', ], [ 'shape' => 'ReplayLimitExceededException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], ], ], 'SetTopicAttributes' => [ 'name' => 'SetTopicAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTopicAttributesInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'Subscribe' => [ 'name' => 'Subscribe', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SubscribeInput', ], 'output' => [ 'shape' => 'SubscribeResponse', 'resultWrapper' => 'SubscribeResult', ], 'errors' => [ [ 'shape' => 'SubscriptionLimitExceededException', ], [ 'shape' => 'FilterPolicyLimitExceededException', ], [ 'shape' => 'ReplayLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', 'resultWrapper' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'StaleTagException', ], [ 'shape' => 'TagPolicyException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'ConcurrentAccessException', ], ], ], 'Unsubscribe' => [ 'name' => 'Unsubscribe', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnsubscribeInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidSecurityException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', 'resultWrapper' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'StaleTagException', ], [ 'shape' => 'TagPolicyException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'ConcurrentAccessException', ], ], ], 'VerifySMSSandboxPhoneNumber' => [ 'name' => 'VerifySMSSandboxPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifySMSSandboxPhoneNumberInput', ], 'output' => [ 'shape' => 'VerifySMSSandboxPhoneNumberResult', 'resultWrapper' => 'VerifySMSSandboxPhoneNumberResult', ], 'errors' => [ [ 'shape' => 'AuthorizationErrorException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'VerificationException', ], [ 'shape' => 'ThrottledException', ], ], ], ], 'shapes' => [ 'ActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'action', ], ], 'AddPermissionInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'Label', 'AWSAccountId', 'ActionName', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'Label' => [ 'shape' => 'label', ], 'AWSAccountId' => [ 'shape' => 'DelegatesList', ], 'ActionName' => [ 'shape' => 'ActionsList', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AuthorizationErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'AuthorizationError', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BatchEntryIdsNotDistinctException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'BatchEntryIdsNotDistinct', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BatchRequestTooLongException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'BatchRequestTooLong', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BatchResultErrorEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'Code', 'SenderFault', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Code' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'SenderFault' => [ 'shape' => 'boolean', ], ], ], 'BatchResultErrorEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchResultErrorEntry', ], ], 'Binary' => [ 'type' => 'blob', ], 'CheckIfPhoneNumberIsOptedOutInput' => [ 'type' => 'structure', 'required' => [ 'phoneNumber', ], 'members' => [ 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'CheckIfPhoneNumberIsOptedOutResponse' => [ 'type' => 'structure', 'members' => [ 'isOptedOut' => [ 'shape' => 'boolean', ], ], ], 'ConcurrentAccessException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'ConcurrentAccess', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ConfirmSubscriptionInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'Token', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'Token' => [ 'shape' => 'token', ], 'AuthenticateOnUnsubscribe' => [ 'shape' => 'authenticateOnUnsubscribe', ], ], ], 'ConfirmSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], ], ], 'CreateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'CreatePlatformApplicationInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Platform', 'Attributes', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'CreatePlatformApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], ], ], 'CreatePlatformEndpointInput' => [ 'type' => 'structure', 'required' => [ 'PlatformApplicationArn', 'Token', ], 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], 'Token' => [ 'shape' => 'String', ], 'CustomUserData' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'CreateSMSSandboxPhoneNumberInput' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', ], 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumberString', ], 'LanguageCode' => [ 'shape' => 'LanguageCodeString', ], ], ], 'CreateSMSSandboxPhoneNumberResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateTopicInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'topicName', ], 'Attributes' => [ 'shape' => 'TopicAttributesMap', ], 'Tags' => [ 'shape' => 'TagList', ], 'DataProtectionPolicy' => [ 'shape' => 'attributeValue', ], ], ], 'CreateTopicResponse' => [ 'type' => 'structure', 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], ], ], 'DelegatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'delegate', ], ], 'DeleteEndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'DeletePlatformApplicationInput' => [ 'type' => 'structure', 'required' => [ 'PlatformApplicationArn', ], 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], ], ], 'DeleteSMSSandboxPhoneNumberInput' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', ], 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumberString', ], ], ], 'DeleteSMSSandboxPhoneNumberResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTopicInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], ], ], 'EmptyBatchRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'EmptyBatchRequest', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'EndpointDisabledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'EndpointDisabled', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'FilterPolicyLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'FilterPolicyLimitExceeded', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'GetDataProtectionPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'topicARN', ], ], ], 'GetDataProtectionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'DataProtectionPolicy' => [ 'shape' => 'attributeValue', ], ], ], 'GetEndpointAttributesInput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'GetEndpointAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'GetPlatformApplicationAttributesInput' => [ 'type' => 'structure', 'required' => [ 'PlatformApplicationArn', ], 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], ], ], 'GetPlatformApplicationAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'GetSMSAttributesInput' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'ListString', ], ], ], 'GetSMSAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'GetSMSSandboxAccountStatusInput' => [ 'type' => 'structure', 'members' => [], ], 'GetSMSSandboxAccountStatusResult' => [ 'type' => 'structure', 'required' => [ 'IsInSandbox', ], 'members' => [ 'IsInSandbox' => [ 'shape' => 'boolean', ], ], ], 'GetSubscriptionAttributesInput' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], ], ], 'GetSubscriptionAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'SubscriptionAttributesMap', ], ], ], 'GetTopicAttributesInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], ], ], 'GetTopicAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'TopicAttributesMap', ], ], ], 'InternalErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'InternalError', 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidBatchEntryIdException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'InvalidBatchEntryId', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'InvalidParameter', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'ParameterValueInvalid', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSecurityException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'InvalidSecurity', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'InvalidState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Iso2CountryCode' => [ 'type' => 'string', 'max' => 2, 'pattern' => '^[A-Za-z]{2}$', ], 'KMSAccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSAccessDenied', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KMSDisabledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSDisabled', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KMSInvalidStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSInvalidState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KMSNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KMSOptInRequired' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSOptInRequired', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'KMSThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'KMSThrottling', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LanguageCodeString' => [ 'type' => 'string', 'enum' => [ 'en-US', 'en-GB', 'es-419', 'es-ES', 'de-DE', 'fr-CA', 'fr-FR', 'it-IT', 'ja-JP', 'pt-BR', 'kr-KR', 'zh-CN', 'zh-TW', ], ], 'ListEndpointsByPlatformApplicationInput' => [ 'type' => 'structure', 'required' => [ 'PlatformApplicationArn', ], 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEndpointsByPlatformApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoints' => [ 'shape' => 'ListOfEndpoints', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListOfEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], ], 'ListOfPlatformApplications' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatformApplication', ], ], 'ListOriginationNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxItemsListOriginationNumbers', ], ], ], 'ListOriginationNumbersResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'nextToken', ], 'PhoneNumbers' => [ 'shape' => 'PhoneNumberInformationList', ], ], ], 'ListPhoneNumbersOptedOutInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'string', ], ], ], 'ListPhoneNumbersOptedOutResponse' => [ 'type' => 'structure', 'members' => [ 'phoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'ListPlatformApplicationsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPlatformApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'PlatformApplications' => [ 'shape' => 'ListOfPlatformApplications', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSMSSandboxPhoneNumbersInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxItems', ], ], ], 'ListSMSSandboxPhoneNumbersResult' => [ 'type' => 'structure', 'required' => [ 'PhoneNumbers', ], 'members' => [ 'PhoneNumbers' => [ 'shape' => 'SMSSandboxPhoneNumberList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListString' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListSubscriptionsByTopicInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListSubscriptionsByTopicResponse' => [ 'type' => 'structure', 'members' => [ 'Subscriptions' => [ 'shape' => 'SubscriptionsList', ], 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListSubscriptionsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'Subscriptions' => [ 'shape' => 'SubscriptionsList', ], 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTopicsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListTopicsResponse' => [ 'type' => 'structure', 'members' => [ 'Topics' => [ 'shape' => 'TopicsList', ], 'NextToken' => [ 'shape' => 'nextToken', ], ], ], 'MapStringToString' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'MaxItems' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxItemsListOriginationNumbers' => [ 'type' => 'integer', 'max' => 30, 'min' => 1, ], 'MessageAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', 'locationName' => 'Name', ], 'value' => [ 'shape' => 'MessageAttributeValue', 'locationName' => 'Value', ], ], 'MessageAttributeValue' => [ 'type' => 'structure', 'required' => [ 'DataType', ], 'members' => [ 'DataType' => [ 'shape' => 'String', ], 'StringValue' => [ 'shape' => 'String', ], 'BinaryValue' => [ 'shape' => 'Binary', ], ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'NotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NumberCapability' => [ 'type' => 'string', 'enum' => [ 'SMS', 'MMS', 'VOICE', ], ], 'NumberCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberCapability', ], ], 'OTPCode' => [ 'type' => 'string', 'max' => 8, 'min' => 5, 'pattern' => '^[0-9]+$', ], 'OptInPhoneNumberInput' => [ 'type' => 'structure', 'required' => [ 'phoneNumber', ], 'members' => [ 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'OptInPhoneNumberResponse' => [ 'type' => 'structure', 'members' => [], ], 'OptedOutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'OptedOut', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'PhoneNumber' => [ 'type' => 'string', 'sensitive' => true, ], 'PhoneNumberInformation' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'String', ], 'Iso2CountryCode' => [ 'shape' => 'Iso2CountryCode', ], 'RouteType' => [ 'shape' => 'RouteType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], ], ], 'PhoneNumberInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberInformation', ], ], 'PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], ], 'PhoneNumberString' => [ 'type' => 'string', 'max' => 20, 'pattern' => '^(\\+[0-9]{8,}|[0-9]{0,9})$', 'sensitive' => true, ], 'PlatformApplication' => [ 'type' => 'structure', 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'PlatformApplicationDisabledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'PlatformApplicationDisabled', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'PublishBatchInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'PublishBatchRequestEntries', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'PublishBatchRequestEntries' => [ 'shape' => 'PublishBatchRequestEntryList', ], ], ], 'PublishBatchRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'Message', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'message', ], 'Subject' => [ 'shape' => 'subject', ], 'MessageStructure' => [ 'shape' => 'messageStructure', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'MessageDeduplicationId' => [ 'shape' => 'String', ], 'MessageGroupId' => [ 'shape' => 'String', ], ], ], 'PublishBatchRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublishBatchRequestEntry', ], ], 'PublishBatchResponse' => [ 'type' => 'structure', 'members' => [ 'Successful' => [ 'shape' => 'PublishBatchResultEntryList', ], 'Failed' => [ 'shape' => 'BatchResultErrorEntryList', ], ], ], 'PublishBatchResultEntry' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], 'MessageId' => [ 'shape' => 'messageId', ], 'SequenceNumber' => [ 'shape' => 'String', ], ], ], 'PublishBatchResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublishBatchResultEntry', ], ], 'PublishInput' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'TargetArn' => [ 'shape' => 'String', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Message' => [ 'shape' => 'message', ], 'Subject' => [ 'shape' => 'subject', ], 'MessageStructure' => [ 'shape' => 'messageStructure', ], 'MessageAttributes' => [ 'shape' => 'MessageAttributeMap', ], 'MessageDeduplicationId' => [ 'shape' => 'String', ], 'MessageGroupId' => [ 'shape' => 'String', ], ], ], 'PublishResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'messageId', ], 'SequenceNumber' => [ 'shape' => 'String', ], ], ], 'PutDataProtectionPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'DataProtectionPolicy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'topicARN', ], 'DataProtectionPolicy' => [ 'shape' => 'attributeValue', ], ], ], 'RemovePermissionInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'Label', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'Label' => [ 'shape' => 'label', ], ], ], 'ReplayLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'ReplayLimitExceeded', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'ResourceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RouteType' => [ 'type' => 'string', 'enum' => [ 'Transactional', 'Promotional', 'Premium', ], ], 'SMSSandboxPhoneNumber' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumberString', ], 'Status' => [ 'shape' => 'SMSSandboxPhoneNumberVerificationStatus', ], ], ], 'SMSSandboxPhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SMSSandboxPhoneNumber', ], ], 'SMSSandboxPhoneNumberVerificationStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Verified', ], ], 'SetEndpointAttributesInput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', 'Attributes', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'SetPlatformApplicationAttributesInput' => [ 'type' => 'structure', 'required' => [ 'PlatformApplicationArn', 'Attributes', ], 'members' => [ 'PlatformApplicationArn' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'SetSMSAttributesInput' => [ 'type' => 'structure', 'required' => [ 'attributes', ], 'members' => [ 'attributes' => [ 'shape' => 'MapStringToString', ], ], ], 'SetSMSAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'SetSubscriptionAttributesInput' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', 'AttributeName', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], 'AttributeName' => [ 'shape' => 'attributeName', ], 'AttributeValue' => [ 'shape' => 'attributeValue', ], ], ], 'SetTopicAttributesInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'AttributeName', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'AttributeName' => [ 'shape' => 'attributeName', ], 'AttributeValue' => [ 'shape' => 'attributeValue', ], ], ], 'StaleTagException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'StaleTag', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'SubscribeInput' => [ 'type' => 'structure', 'required' => [ 'TopicArn', 'Protocol', ], 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], 'Protocol' => [ 'shape' => 'protocol', ], 'Endpoint' => [ 'shape' => 'endpoint', ], 'Attributes' => [ 'shape' => 'SubscriptionAttributesMap', ], 'ReturnSubscriptionArn' => [ 'shape' => 'boolean', ], ], ], 'SubscribeResponse' => [ 'type' => 'structure', 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], ], ], 'Subscription' => [ 'type' => 'structure', 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], 'Owner' => [ 'shape' => 'account', ], 'Protocol' => [ 'shape' => 'protocol', ], 'Endpoint' => [ 'shape' => 'endpoint', ], 'TopicArn' => [ 'shape' => 'topicARN', ], ], ], 'SubscriptionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'attributeName', ], 'value' => [ 'shape' => 'attributeValue', ], ], 'SubscriptionLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'SubscriptionLimitExceeded', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'SubscriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subscription', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'TagLimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'TagPolicy', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'Throttled', 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyEntriesInBatchRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'TooManyEntriesInBatchRequest', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Topic' => [ 'type' => 'structure', 'members' => [ 'TopicArn' => [ 'shape' => 'topicARN', ], ], ], 'TopicAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'attributeName', ], 'value' => [ 'shape' => 'attributeValue', ], ], 'TopicLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'TopicLimitExceeded', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'TopicsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Topic', ], ], 'UnsubscribeInput' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'subscriptionARN', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'UserError', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'code' => 'ValidationException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VerificationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Status', ], 'members' => [ 'Message' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'VerifySMSSandboxPhoneNumberInput' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', 'OneTimePassword', ], 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumberString', ], 'OneTimePassword' => [ 'shape' => 'OTPCode', ], ], ], 'VerifySMSSandboxPhoneNumberResult' => [ 'type' => 'structure', 'members' => [], ], 'account' => [ 'type' => 'string', ], 'action' => [ 'type' => 'string', ], 'attributeName' => [ 'type' => 'string', ], 'attributeValue' => [ 'type' => 'string', ], 'authenticateOnUnsubscribe' => [ 'type' => 'string', ], 'boolean' => [ 'type' => 'boolean', ], 'delegate' => [ 'type' => 'string', ], 'endpoint' => [ 'type' => 'string', ], 'label' => [ 'type' => 'string', ], 'message' => [ 'type' => 'string', ], 'messageId' => [ 'type' => 'string', ], 'messageStructure' => [ 'type' => 'string', ], 'nextToken' => [ 'type' => 'string', ], 'protocol' => [ 'type' => 'string', ], 'string' => [ 'type' => 'string', ], 'subject' => [ 'type' => 'string', ], 'subscriptionARN' => [ 'type' => 'string', ], 'token' => [ 'type' => 'string', ], 'topicARN' => [ 'type' => 'string', ], 'topicName' => [ 'type' => 'string', ], ],];
