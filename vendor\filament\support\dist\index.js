(()=>{var jo=Object.create;var Di=Object.defineProperty;var Bo=Object.getOwnPropertyDescriptor;var Ho=Object.getOwnPropertyNames;var $o=Object.getPrototypeOf,Wo=Object.prototype.hasOwnProperty;var Kr=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var Vo=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Ho(e))!Wo.call(t,i)&&i!==r&&Di(t,i,{get:()=>e[i],enumerable:!(n=Bo(e,i))||n.enumerable});return t};var zo=(t,e,r)=>(r=t!=null?jo($o(t)):{},Vo(e||!t||!t.__esModule?Di(r,"default",{value:t,enumerable:!0}):r,t));var oo=Kr(()=>{});var ao=Kr(()=>{});var so=Kr((Os,yr)=>{(function(){"use strict";var t="input is invalid type",e="finalize already called",r=typeof window=="object",n=r?window:{};n.JS_MD5_NO_WINDOW&&(r=!1);var i=!r&&typeof self=="object",o=!n.JS_MD5_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;o?n=global:i&&(n=self);var l=!n.JS_MD5_NO_COMMON_JS&&typeof yr=="object"&&yr.exports,h=typeof define=="function"&&define.amd,u=!n.JS_MD5_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",f="0123456789abcdef".split(""),y=[128,32768,8388608,-2147483648],b=[0,8,16,24],A=["hex","array","digest","buffer","arrayBuffer","base64"],E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),O=[],P;if(u){var R=new ArrayBuffer(68);P=new Uint8Array(R),O=new Uint32Array(R)}var $=Array.isArray;(n.JS_MD5_NO_NODE_JS||!$)&&($=function(s){return Object.prototype.toString.call(s)==="[object Array]"});var B=ArrayBuffer.isView;u&&(n.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW||!B)&&(B=function(s){return typeof s=="object"&&s.buffer&&s.buffer.constructor===ArrayBuffer});var K=function(s){var p=typeof s;if(p==="string")return[s,!0];if(p!=="object"||s===null)throw new Error(t);if(u&&s.constructor===ArrayBuffer)return[new Uint8Array(s),!1];if(!$(s)&&!B(s))throw new Error(t);return[s,!1]},X=function(s){return function(p){return new U(!0).update(p)[s]()}},ne=function(){var s=X("hex");o&&(s=J(s)),s.create=function(){return new U},s.update=function(d){return s.create().update(d)};for(var p=0;p<A.length;++p){var v=A[p];s[v]=X(v)}return s},J=function(s){var p=oo(),v=ao().Buffer,d;v.from&&!n.JS_MD5_NO_BUFFER_FROM?d=v.from:d=function(_){return new v(_)};var N=function(_){if(typeof _=="string")return p.createHash("md5").update(_,"utf8").digest("hex");if(_==null)throw new Error(t);return _.constructor===ArrayBuffer&&(_=new Uint8Array(_)),$(_)||B(_)||_.constructor===v?p.createHash("md5").update(d(_)).digest("hex"):s(_)};return N},V=function(s){return function(p,v){return new Z(p,!0).update(v)[s]()}},de=function(){var s=V("hex");s.create=function(d){return new Z(d)},s.update=function(d,N){return s.create(d).update(N)};for(var p=0;p<A.length;++p){var v=A[p];s[v]=V(v)}return s};function U(s){if(s)O[0]=O[16]=O[1]=O[2]=O[3]=O[4]=O[5]=O[6]=O[7]=O[8]=O[9]=O[10]=O[11]=O[12]=O[13]=O[14]=O[15]=0,this.blocks=O,this.buffer8=P;else if(u){var p=new ArrayBuffer(68);this.buffer8=new Uint8Array(p),this.blocks=new Uint32Array(p)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}U.prototype.update=function(s){if(this.finalized)throw new Error(e);var p=K(s);s=p[0];for(var v=p[1],d,N=0,_,M=s.length,Q=this.blocks,Ue=this.buffer8;N<M;){if(this.hashed&&(this.hashed=!1,Q[0]=Q[16],Q[16]=Q[1]=Q[2]=Q[3]=Q[4]=Q[5]=Q[6]=Q[7]=Q[8]=Q[9]=Q[10]=Q[11]=Q[12]=Q[13]=Q[14]=Q[15]=0),v)if(u)for(_=this.start;N<M&&_<64;++N)d=s.charCodeAt(N),d<128?Ue[_++]=d:d<2048?(Ue[_++]=192|d>>>6,Ue[_++]=128|d&63):d<55296||d>=57344?(Ue[_++]=224|d>>>12,Ue[_++]=128|d>>>6&63,Ue[_++]=128|d&63):(d=65536+((d&1023)<<10|s.charCodeAt(++N)&1023),Ue[_++]=240|d>>>18,Ue[_++]=128|d>>>12&63,Ue[_++]=128|d>>>6&63,Ue[_++]=128|d&63);else for(_=this.start;N<M&&_<64;++N)d=s.charCodeAt(N),d<128?Q[_>>>2]|=d<<b[_++&3]:d<2048?(Q[_>>>2]|=(192|d>>>6)<<b[_++&3],Q[_>>>2]|=(128|d&63)<<b[_++&3]):d<55296||d>=57344?(Q[_>>>2]|=(224|d>>>12)<<b[_++&3],Q[_>>>2]|=(128|d>>>6&63)<<b[_++&3],Q[_>>>2]|=(128|d&63)<<b[_++&3]):(d=65536+((d&1023)<<10|s.charCodeAt(++N)&1023),Q[_>>>2]|=(240|d>>>18)<<b[_++&3],Q[_>>>2]|=(128|d>>>12&63)<<b[_++&3],Q[_>>>2]|=(128|d>>>6&63)<<b[_++&3],Q[_>>>2]|=(128|d&63)<<b[_++&3]);else if(u)for(_=this.start;N<M&&_<64;++N)Ue[_++]=s[N];else for(_=this.start;N<M&&_<64;++N)Q[_>>>2]|=s[N]<<b[_++&3];this.lastByteIndex=_,this.bytes+=_-this.start,_>=64?(this.start=_-64,this.hash(),this.hashed=!0):this.start=_}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},U.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var s=this.blocks,p=this.lastByteIndex;s[p>>>2]|=y[p&3],p>=56&&(this.hashed||this.hash(),s[0]=s[16],s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),s[14]=this.bytes<<3,s[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},U.prototype.hash=function(){var s,p,v,d,N,_,M=this.blocks;this.first?(s=M[0]-680876937,s=(s<<7|s>>>25)-271733879<<0,d=(-1732584194^s&2004318071)+M[1]-117830708,d=(d<<12|d>>>20)+s<<0,v=(-271733879^d&(s^-271733879))+M[2]-1126478375,v=(v<<17|v>>>15)+d<<0,p=(s^v&(d^s))+M[3]-1316259209,p=(p<<22|p>>>10)+v<<0):(s=this.h0,p=this.h1,v=this.h2,d=this.h3,s+=(d^p&(v^d))+M[0]-680876936,s=(s<<7|s>>>25)+p<<0,d+=(v^s&(p^v))+M[1]-389564586,d=(d<<12|d>>>20)+s<<0,v+=(p^d&(s^p))+M[2]+606105819,v=(v<<17|v>>>15)+d<<0,p+=(s^v&(d^s))+M[3]-1044525330,p=(p<<22|p>>>10)+v<<0),s+=(d^p&(v^d))+M[4]-176418897,s=(s<<7|s>>>25)+p<<0,d+=(v^s&(p^v))+M[5]+1200080426,d=(d<<12|d>>>20)+s<<0,v+=(p^d&(s^p))+M[6]-1473231341,v=(v<<17|v>>>15)+d<<0,p+=(s^v&(d^s))+M[7]-45705983,p=(p<<22|p>>>10)+v<<0,s+=(d^p&(v^d))+M[8]+1770035416,s=(s<<7|s>>>25)+p<<0,d+=(v^s&(p^v))+M[9]-1958414417,d=(d<<12|d>>>20)+s<<0,v+=(p^d&(s^p))+M[10]-42063,v=(v<<17|v>>>15)+d<<0,p+=(s^v&(d^s))+M[11]-1990404162,p=(p<<22|p>>>10)+v<<0,s+=(d^p&(v^d))+M[12]+1804603682,s=(s<<7|s>>>25)+p<<0,d+=(v^s&(p^v))+M[13]-40341101,d=(d<<12|d>>>20)+s<<0,v+=(p^d&(s^p))+M[14]-1502002290,v=(v<<17|v>>>15)+d<<0,p+=(s^v&(d^s))+M[15]+1236535329,p=(p<<22|p>>>10)+v<<0,s+=(v^d&(p^v))+M[1]-165796510,s=(s<<5|s>>>27)+p<<0,d+=(p^v&(s^p))+M[6]-1069501632,d=(d<<9|d>>>23)+s<<0,v+=(s^p&(d^s))+M[11]+643717713,v=(v<<14|v>>>18)+d<<0,p+=(d^s&(v^d))+M[0]-373897302,p=(p<<20|p>>>12)+v<<0,s+=(v^d&(p^v))+M[5]-701558691,s=(s<<5|s>>>27)+p<<0,d+=(p^v&(s^p))+M[10]+38016083,d=(d<<9|d>>>23)+s<<0,v+=(s^p&(d^s))+M[15]-660478335,v=(v<<14|v>>>18)+d<<0,p+=(d^s&(v^d))+M[4]-405537848,p=(p<<20|p>>>12)+v<<0,s+=(v^d&(p^v))+M[9]+568446438,s=(s<<5|s>>>27)+p<<0,d+=(p^v&(s^p))+M[14]-1019803690,d=(d<<9|d>>>23)+s<<0,v+=(s^p&(d^s))+M[3]-187363961,v=(v<<14|v>>>18)+d<<0,p+=(d^s&(v^d))+M[8]+1163531501,p=(p<<20|p>>>12)+v<<0,s+=(v^d&(p^v))+M[13]-1444681467,s=(s<<5|s>>>27)+p<<0,d+=(p^v&(s^p))+M[2]-51403784,d=(d<<9|d>>>23)+s<<0,v+=(s^p&(d^s))+M[7]+1735328473,v=(v<<14|v>>>18)+d<<0,p+=(d^s&(v^d))+M[12]-1926607734,p=(p<<20|p>>>12)+v<<0,N=p^v,s+=(N^d)+M[5]-378558,s=(s<<4|s>>>28)+p<<0,d+=(N^s)+M[8]-2022574463,d=(d<<11|d>>>21)+s<<0,_=d^s,v+=(_^p)+M[11]+1839030562,v=(v<<16|v>>>16)+d<<0,p+=(_^v)+M[14]-35309556,p=(p<<23|p>>>9)+v<<0,N=p^v,s+=(N^d)+M[1]-1530992060,s=(s<<4|s>>>28)+p<<0,d+=(N^s)+M[4]+1272893353,d=(d<<11|d>>>21)+s<<0,_=d^s,v+=(_^p)+M[7]-155497632,v=(v<<16|v>>>16)+d<<0,p+=(_^v)+M[10]-1094730640,p=(p<<23|p>>>9)+v<<0,N=p^v,s+=(N^d)+M[13]+681279174,s=(s<<4|s>>>28)+p<<0,d+=(N^s)+M[0]-358537222,d=(d<<11|d>>>21)+s<<0,_=d^s,v+=(_^p)+M[3]-722521979,v=(v<<16|v>>>16)+d<<0,p+=(_^v)+M[6]+76029189,p=(p<<23|p>>>9)+v<<0,N=p^v,s+=(N^d)+M[9]-640364487,s=(s<<4|s>>>28)+p<<0,d+=(N^s)+M[12]-421815835,d=(d<<11|d>>>21)+s<<0,_=d^s,v+=(_^p)+M[15]+530742520,v=(v<<16|v>>>16)+d<<0,p+=(_^v)+M[2]-995338651,p=(p<<23|p>>>9)+v<<0,s+=(v^(p|~d))+M[0]-198630844,s=(s<<6|s>>>26)+p<<0,d+=(p^(s|~v))+M[7]+1126891415,d=(d<<10|d>>>22)+s<<0,v+=(s^(d|~p))+M[14]-1416354905,v=(v<<15|v>>>17)+d<<0,p+=(d^(v|~s))+M[5]-57434055,p=(p<<21|p>>>11)+v<<0,s+=(v^(p|~d))+M[12]+1700485571,s=(s<<6|s>>>26)+p<<0,d+=(p^(s|~v))+M[3]-1894986606,d=(d<<10|d>>>22)+s<<0,v+=(s^(d|~p))+M[10]-1051523,v=(v<<15|v>>>17)+d<<0,p+=(d^(v|~s))+M[1]-2054922799,p=(p<<21|p>>>11)+v<<0,s+=(v^(p|~d))+M[8]+1873313359,s=(s<<6|s>>>26)+p<<0,d+=(p^(s|~v))+M[15]-30611744,d=(d<<10|d>>>22)+s<<0,v+=(s^(d|~p))+M[6]-1560198380,v=(v<<15|v>>>17)+d<<0,p+=(d^(v|~s))+M[13]+1309151649,p=(p<<21|p>>>11)+v<<0,s+=(v^(p|~d))+M[4]-145523070,s=(s<<6|s>>>26)+p<<0,d+=(p^(s|~v))+M[11]-1120210379,d=(d<<10|d>>>22)+s<<0,v+=(s^(d|~p))+M[2]+718787259,v=(v<<15|v>>>17)+d<<0,p+=(d^(v|~s))+M[9]-343485551,p=(p<<21|p>>>11)+v<<0,this.first?(this.h0=s+1732584193<<0,this.h1=p-271733879<<0,this.h2=v-1732584194<<0,this.h3=d+271733878<<0,this.first=!1):(this.h0=this.h0+s<<0,this.h1=this.h1+p<<0,this.h2=this.h2+v<<0,this.h3=this.h3+d<<0)},U.prototype.hex=function(){this.finalize();var s=this.h0,p=this.h1,v=this.h2,d=this.h3;return f[s>>>4&15]+f[s&15]+f[s>>>12&15]+f[s>>>8&15]+f[s>>>20&15]+f[s>>>16&15]+f[s>>>28&15]+f[s>>>24&15]+f[p>>>4&15]+f[p&15]+f[p>>>12&15]+f[p>>>8&15]+f[p>>>20&15]+f[p>>>16&15]+f[p>>>28&15]+f[p>>>24&15]+f[v>>>4&15]+f[v&15]+f[v>>>12&15]+f[v>>>8&15]+f[v>>>20&15]+f[v>>>16&15]+f[v>>>28&15]+f[v>>>24&15]+f[d>>>4&15]+f[d&15]+f[d>>>12&15]+f[d>>>8&15]+f[d>>>20&15]+f[d>>>16&15]+f[d>>>28&15]+f[d>>>24&15]},U.prototype.toString=U.prototype.hex,U.prototype.digest=function(){this.finalize();var s=this.h0,p=this.h1,v=this.h2,d=this.h3;return[s&255,s>>>8&255,s>>>16&255,s>>>24&255,p&255,p>>>8&255,p>>>16&255,p>>>24&255,v&255,v>>>8&255,v>>>16&255,v>>>24&255,d&255,d>>>8&255,d>>>16&255,d>>>24&255]},U.prototype.array=U.prototype.digest,U.prototype.arrayBuffer=function(){this.finalize();var s=new ArrayBuffer(16),p=new Uint32Array(s);return p[0]=this.h0,p[1]=this.h1,p[2]=this.h2,p[3]=this.h3,s},U.prototype.buffer=U.prototype.arrayBuffer,U.prototype.base64=function(){for(var s,p,v,d="",N=this.array(),_=0;_<15;)s=N[_++],p=N[_++],v=N[_++],d+=E[s>>>2]+E[(s<<4|p>>>4)&63]+E[(p<<2|v>>>6)&63]+E[v&63];return s=N[_],d+=E[s>>>2]+E[s<<4&63]+"==",d};function Z(s,p){var v,d=K(s);if(s=d[0],d[1]){var N=[],_=s.length,M=0,Q;for(v=0;v<_;++v)Q=s.charCodeAt(v),Q<128?N[M++]=Q:Q<2048?(N[M++]=192|Q>>>6,N[M++]=128|Q&63):Q<55296||Q>=57344?(N[M++]=224|Q>>>12,N[M++]=128|Q>>>6&63,N[M++]=128|Q&63):(Q=65536+((Q&1023)<<10|s.charCodeAt(++v)&1023),N[M++]=240|Q>>>18,N[M++]=128|Q>>>12&63,N[M++]=128|Q>>>6&63,N[M++]=128|Q&63);s=N}s.length>64&&(s=new U(!0).update(s).array());var Ue=[],Rt=[];for(v=0;v<64;++v){var Vt=s[v]||0;Ue[v]=92^Vt,Rt[v]=54^Vt}U.call(this,p),this.update(Rt),this.oKeyPad=Ue,this.inner=!0,this.sharedMemory=p}Z.prototype=new U,Z.prototype.finalize=function(){if(U.prototype.finalize.call(this),this.inner){this.inner=!1;var s=this.array();U.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(s),U.prototype.finalize.call(this)}};var me=ne();me.md5=me,me.md5.hmac=de(),l?yr.exports=me:(n.md5=me,h&&define(function(){return me}))})()});var ji=["top","right","bottom","left"],Ti=["start","end"],_i=ji.reduce((t,e)=>t.concat(e,e+"-"+Ti[0],e+"-"+Ti[1]),[]),Et=Math.min,tt=Math.max,hr=Math.round,pr=Math.floor,nn=t=>({x:t,y:t}),Uo={left:"right",right:"left",bottom:"top",top:"bottom"},Yo={start:"end",end:"start"};function Jr(t,e,r){return tt(t,Et(e,r))}function jt(t,e){return typeof t=="function"?t(e):t}function pt(t){return t.split("-")[0]}function xt(t){return t.split("-")[1]}function Bi(t){return t==="x"?"y":"x"}function Zr(t){return t==="y"?"height":"width"}function Pn(t){return["top","bottom"].includes(pt(t))?"y":"x"}function Qr(t){return Bi(Pn(t))}function Hi(t,e,r){r===void 0&&(r=!1);let n=xt(t),i=Qr(t),o=Zr(i),l=i==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=mr(l)),[l,mr(l)]}function Xo(t){let e=mr(t);return[vr(t),e,vr(e)]}function vr(t){return t.replace(/start|end/g,e=>Yo[e])}function qo(t,e,r){let n=["left","right"],i=["right","left"],o=["top","bottom"],l=["bottom","top"];switch(t){case"top":case"bottom":return r?e?i:n:e?n:i;case"left":case"right":return e?o:l;default:return[]}}function Go(t,e,r,n){let i=xt(t),o=qo(pt(t),r==="start",n);return i&&(o=o.map(l=>l+"-"+i),e&&(o=o.concat(o.map(vr)))),o}function mr(t){return t.replace(/left|right|bottom|top/g,e=>Uo[e])}function Ko(t){return{top:0,right:0,bottom:0,left:0,...t}}function ei(t){return typeof t!="number"?Ko(t):{top:t,right:t,bottom:t,left:t}}function Dn(t){return{...t,top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height}}function Pi(t,e,r){let{reference:n,floating:i}=t,o=Pn(e),l=Qr(e),h=Zr(l),u=pt(e),f=o==="y",y=n.x+n.width/2-i.width/2,b=n.y+n.height/2-i.height/2,A=n[h]/2-i[h]/2,E;switch(u){case"top":E={x:y,y:n.y-i.height};break;case"bottom":E={x:y,y:n.y+n.height};break;case"right":E={x:n.x+n.width,y:b};break;case"left":E={x:n.x-i.width,y:b};break;default:E={x:n.x,y:n.y}}switch(xt(e)){case"start":E[l]-=A*(r&&f?-1:1);break;case"end":E[l]+=A*(r&&f?-1:1);break}return E}var Jo=async(t,e,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:l}=r,h=o.filter(Boolean),u=await(l.isRTL==null?void 0:l.isRTL(e)),f=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:y,y:b}=Pi(f,n,u),A=n,E={},O=0;for(let P=0;P<h.length;P++){let{name:R,fn:$}=h[P],{x:B,y:K,data:X,reset:ne}=await $({x:y,y:b,initialPlacement:n,placement:A,strategy:i,middlewareData:E,rects:f,platform:l,elements:{reference:t,floating:e}});y=B??y,b=K??b,E={...E,[R]:{...E[R],...X}},ne&&O<=50&&(O++,typeof ne=="object"&&(ne.placement&&(A=ne.placement),ne.rects&&(f=ne.rects===!0?await l.getElementRects({reference:t,floating:e,strategy:i}):ne.rects),{x:y,y:b}=Pi(f,A,u)),P=-1)}return{x:y,y:b,placement:A,strategy:i,middlewareData:E}};async function Tn(t,e){var r;e===void 0&&(e={});let{x:n,y:i,platform:o,rects:l,elements:h,strategy:u}=t,{boundary:f="clippingAncestors",rootBoundary:y="viewport",elementContext:b="floating",altBoundary:A=!1,padding:E=0}=jt(e,t),O=ei(E),R=h[A?b==="floating"?"reference":"floating":b],$=Dn(await o.getClippingRect({element:(r=await(o.isElement==null?void 0:o.isElement(R)))==null||r?R:R.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(h.floating)),boundary:f,rootBoundary:y,strategy:u})),B=b==="floating"?{...l.floating,x:n,y:i}:l.reference,K=await(o.getOffsetParent==null?void 0:o.getOffsetParent(h.floating)),X=await(o.isElement==null?void 0:o.isElement(K))?await(o.getScale==null?void 0:o.getScale(K))||{x:1,y:1}:{x:1,y:1},ne=Dn(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:B,offsetParent:K,strategy:u}):B);return{top:($.top-ne.top+O.top)/X.y,bottom:(ne.bottom-$.bottom+O.bottom)/X.y,left:($.left-ne.left+O.left)/X.x,right:(ne.right-$.right+O.right)/X.x}}var Zo=t=>({name:"arrow",options:t,async fn(e){let{x:r,y:n,placement:i,rects:o,platform:l,elements:h,middlewareData:u}=e,{element:f,padding:y=0}=jt(t,e)||{};if(f==null)return{};let b=ei(y),A={x:r,y:n},E=Qr(i),O=Zr(E),P=await l.getDimensions(f),R=E==="y",$=R?"top":"left",B=R?"bottom":"right",K=R?"clientHeight":"clientWidth",X=o.reference[O]+o.reference[E]-A[E]-o.floating[O],ne=A[E]-o.reference[E],J=await(l.getOffsetParent==null?void 0:l.getOffsetParent(f)),V=J?J[K]:0;(!V||!await(l.isElement==null?void 0:l.isElement(J)))&&(V=h.floating[K]||o.floating[O]);let de=X/2-ne/2,U=V/2-P[O]/2-1,Z=Et(b[$],U),me=Et(b[B],U),s=Z,p=V-P[O]-me,v=V/2-P[O]/2+de,d=Jr(s,v,p),N=!u.arrow&&xt(i)!=null&&v!==d&&o.reference[O]/2-(v<s?Z:me)-P[O]/2<0,_=N?v<s?v-s:v-p:0;return{[E]:A[E]+_,data:{[E]:d,centerOffset:v-d-_,...N&&{alignmentOffset:_}},reset:N}}});function Qo(t,e,r){return(t?[...r.filter(i=>xt(i)===t),...r.filter(i=>xt(i)!==t)]:r.filter(i=>pt(i)===i)).filter(i=>t?xt(i)===t||(e?vr(i)!==i:!1):!0)}var ea=function(t){return t===void 0&&(t={}),{name:"autoPlacement",options:t,async fn(e){var r,n,i;let{rects:o,middlewareData:l,placement:h,platform:u,elements:f}=e,{crossAxis:y=!1,alignment:b,allowedPlacements:A=_i,autoAlignment:E=!0,...O}=jt(t,e),P=b!==void 0||A===_i?Qo(b||null,E,A):A,R=await Tn(e,O),$=((r=l.autoPlacement)==null?void 0:r.index)||0,B=P[$];if(B==null)return{};let K=Hi(B,o,await(u.isRTL==null?void 0:u.isRTL(f.floating)));if(h!==B)return{reset:{placement:P[0]}};let X=[R[pt(B)],R[K[0]],R[K[1]]],ne=[...((n=l.autoPlacement)==null?void 0:n.overflows)||[],{placement:B,overflows:X}],J=P[$+1];if(J)return{data:{index:$+1,overflows:ne},reset:{placement:J}};let V=ne.map(Z=>{let me=xt(Z.placement);return[Z.placement,me&&y?Z.overflows.slice(0,2).reduce((s,p)=>s+p,0):Z.overflows[0],Z.overflows]}).sort((Z,me)=>Z[1]-me[1]),U=((i=V.filter(Z=>Z[2].slice(0,xt(Z[0])?2:3).every(me=>me<=0))[0])==null?void 0:i[0])||V[0][0];return U!==h?{data:{index:$+1,overflows:ne},reset:{placement:U}}:{}}}},ta=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var r,n;let{placement:i,middlewareData:o,rects:l,initialPlacement:h,platform:u,elements:f}=e,{mainAxis:y=!0,crossAxis:b=!0,fallbackPlacements:A,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:P=!0,...R}=jt(t,e);if((r=o.arrow)!=null&&r.alignmentOffset)return{};let $=pt(i),B=pt(h)===h,K=await(u.isRTL==null?void 0:u.isRTL(f.floating)),X=A||(B||!P?[mr(h)]:Xo(h));!A&&O!=="none"&&X.push(...Go(h,P,O,K));let ne=[h,...X],J=await Tn(e,R),V=[],de=((n=o.flip)==null?void 0:n.overflows)||[];if(y&&V.push(J[$]),b){let s=Hi(i,l,K);V.push(J[s[0]],J[s[1]])}if(de=[...de,{placement:i,overflows:V}],!V.every(s=>s<=0)){var U,Z;let s=(((U=o.flip)==null?void 0:U.index)||0)+1,p=ne[s];if(p)return{data:{index:s,overflows:de},reset:{placement:p}};let v=(Z=de.filter(d=>d.overflows[0]<=0).sort((d,N)=>d.overflows[1]-N.overflows[1])[0])==null?void 0:Z.placement;if(!v)switch(E){case"bestFit":{var me;let d=(me=de.map(N=>[N.placement,N.overflows.filter(_=>_>0).reduce((_,M)=>_+M,0)]).sort((N,_)=>N[1]-_[1])[0])==null?void 0:me[0];d&&(v=d);break}case"initialPlacement":v=h;break}if(i!==v)return{reset:{placement:v}}}return{}}}};function Mi(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Ri(t){return ji.some(e=>t[e]>=0)}var na=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){let{rects:r}=e,{strategy:n="referenceHidden",...i}=jt(t,e);switch(n){case"referenceHidden":{let o=await Tn(e,{...i,elementContext:"reference"}),l=Mi(o,r.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:Ri(l)}}}case"escaped":{let o=await Tn(e,{...i,altBoundary:!0}),l=Mi(o,r.floating);return{data:{escapedOffsets:l,escaped:Ri(l)}}}default:return{}}}}};function $i(t){let e=Et(...t.map(o=>o.left)),r=Et(...t.map(o=>o.top)),n=tt(...t.map(o=>o.right)),i=tt(...t.map(o=>o.bottom));return{x:e,y:r,width:n-e,height:i-r}}function ra(t){let e=t.slice().sort((i,o)=>i.y-o.y),r=[],n=null;for(let i=0;i<e.length;i++){let o=e[i];!n||o.y-n.y>n.height/2?r.push([o]):r[r.length-1].push(o),n=o}return r.map(i=>Dn($i(i)))}var ia=function(t){return t===void 0&&(t={}),{name:"inline",options:t,async fn(e){let{placement:r,elements:n,rects:i,platform:o,strategy:l}=e,{padding:h=2,x:u,y:f}=jt(t,e),y=Array.from(await(o.getClientRects==null?void 0:o.getClientRects(n.reference))||[]),b=ra(y),A=Dn($i(y)),E=ei(h);function O(){if(b.length===2&&b[0].left>b[1].right&&u!=null&&f!=null)return b.find(R=>u>R.left-E.left&&u<R.right+E.right&&f>R.top-E.top&&f<R.bottom+E.bottom)||A;if(b.length>=2){if(Pn(r)==="y"){let Z=b[0],me=b[b.length-1],s=pt(r)==="top",p=Z.top,v=me.bottom,d=s?Z.left:me.left,N=s?Z.right:me.right,_=N-d,M=v-p;return{top:p,bottom:v,left:d,right:N,width:_,height:M,x:d,y:p}}let R=pt(r)==="left",$=tt(...b.map(Z=>Z.right)),B=Et(...b.map(Z=>Z.left)),K=b.filter(Z=>R?Z.left===B:Z.right===$),X=K[0].top,ne=K[K.length-1].bottom,J=B,V=$,de=V-J,U=ne-X;return{top:X,bottom:ne,left:J,right:V,width:de,height:U,x:J,y:X}}return A}let P=await o.getElementRects({reference:{getBoundingClientRect:O},floating:n.floating,strategy:l});return i.reference.x!==P.reference.x||i.reference.y!==P.reference.y||i.reference.width!==P.reference.width||i.reference.height!==P.reference.height?{reset:{rects:P}}:{}}}};async function oa(t,e){let{placement:r,platform:n,elements:i}=t,o=await(n.isRTL==null?void 0:n.isRTL(i.floating)),l=pt(r),h=xt(r),u=Pn(r)==="y",f=["left","top"].includes(l)?-1:1,y=o&&u?-1:1,b=jt(e,t),{mainAxis:A,crossAxis:E,alignmentAxis:O}=typeof b=="number"?{mainAxis:b,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...b};return h&&typeof O=="number"&&(E=h==="end"?O*-1:O),u?{x:E*y,y:A*f}:{x:A*f,y:E*y}}var Wi=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var r,n;let{x:i,y:o,placement:l,middlewareData:h}=e,u=await oa(e,t);return l===((r=h.offset)==null?void 0:r.placement)&&(n=h.arrow)!=null&&n.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},aa=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:r,y:n,placement:i}=e,{mainAxis:o=!0,crossAxis:l=!1,limiter:h={fn:R=>{let{x:$,y:B}=R;return{x:$,y:B}}},...u}=jt(t,e),f={x:r,y:n},y=await Tn(e,u),b=Pn(pt(i)),A=Bi(b),E=f[A],O=f[b];if(o){let R=A==="y"?"top":"left",$=A==="y"?"bottom":"right",B=E+y[R],K=E-y[$];E=Jr(B,E,K)}if(l){let R=b==="y"?"top":"left",$=b==="y"?"bottom":"right",B=O+y[R],K=O-y[$];O=Jr(B,O,K)}let P=h.fn({...e,[A]:E,[b]:O});return{...P,data:{x:P.x-r,y:P.y-n}}}}},sa=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){let{placement:r,rects:n,platform:i,elements:o}=e,{apply:l=()=>{},...h}=jt(t,e),u=await Tn(e,h),f=pt(r),y=xt(r),b=Pn(r)==="y",{width:A,height:E}=n.floating,O,P;f==="top"||f==="bottom"?(O=f,P=y===(await(i.isRTL==null?void 0:i.isRTL(o.floating))?"start":"end")?"left":"right"):(P=f,O=y==="end"?"top":"bottom");let R=E-u[O],$=A-u[P],B=!e.middlewareData.shift,K=R,X=$;if(b){let J=A-u.left-u.right;X=y||B?Et($,J):J}else{let J=E-u.top-u.bottom;K=y||B?Et(R,J):J}if(B&&!y){let J=tt(u.left,0),V=tt(u.right,0),de=tt(u.top,0),U=tt(u.bottom,0);b?X=A-2*(J!==0||V!==0?J+V:tt(u.left,u.right)):K=E-2*(de!==0||U!==0?de+U:tt(u.top,u.bottom))}await l({...e,availableWidth:X,availableHeight:K});let ne=await i.getDimensions(o.floating);return A!==ne.width||E!==ne.height?{reset:{rects:!0}}:{}}}};function rn(t){return Vi(t)?(t.nodeName||"").toLowerCase():"#document"}function lt(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Bt(t){var e;return(e=(Vi(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Vi(t){return t instanceof Node||t instanceof lt(t).Node}function kt(t){return t instanceof Element||t instanceof lt(t).Element}function _t(t){return t instanceof HTMLElement||t instanceof lt(t).HTMLElement}function Ii(t){return typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof lt(t).ShadowRoot}function Un(t){let{overflow:e,overflowX:r,overflowY:n,display:i}=ht(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+r)&&!["inline","contents"].includes(i)}function la(t){return["table","td","th"].includes(rn(t))}function ti(t){let e=ni(),r=ht(t);return r.transform!=="none"||r.perspective!=="none"||(r.containerType?r.containerType!=="normal":!1)||!e&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!e&&(r.filter?r.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(r.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(r.contain||"").includes(n))}function ca(t){let e=_n(t);for(;_t(e)&&!gr(e);){if(ti(e))return e;e=_n(e)}return null}function ni(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function gr(t){return["html","body","#document"].includes(rn(t))}function ht(t){return lt(t).getComputedStyle(t)}function br(t){return kt(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function _n(t){if(rn(t)==="html")return t;let e=t.assignedSlot||t.parentNode||Ii(t)&&t.host||Bt(t);return Ii(e)?e.host:e}function zi(t){let e=_n(t);return gr(e)?t.ownerDocument?t.ownerDocument.body:t.body:_t(e)&&Un(e)?e:zi(e)}function zn(t,e,r){var n;e===void 0&&(e=[]),r===void 0&&(r=!0);let i=zi(t),o=i===((n=t.ownerDocument)==null?void 0:n.body),l=lt(i);return o?e.concat(l,l.visualViewport||[],Un(i)?i:[],l.frameElement&&r?zn(l.frameElement):[]):e.concat(i,zn(i,[],r))}function Ui(t){let e=ht(t),r=parseFloat(e.width)||0,n=parseFloat(e.height)||0,i=_t(t),o=i?t.offsetWidth:r,l=i?t.offsetHeight:n,h=hr(r)!==o||hr(n)!==l;return h&&(r=o,n=l),{width:r,height:n,$:h}}function ri(t){return kt(t)?t:t.contextElement}function Cn(t){let e=ri(t);if(!_t(e))return nn(1);let r=e.getBoundingClientRect(),{width:n,height:i,$:o}=Ui(e),l=(o?hr(r.width):r.width)/n,h=(o?hr(r.height):r.height)/i;return(!l||!Number.isFinite(l))&&(l=1),(!h||!Number.isFinite(h))&&(h=1),{x:l,y:h}}var fa=nn(0);function Yi(t){let e=lt(t);return!ni()||!e.visualViewport?fa:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function ua(t,e,r){return e===void 0&&(e=!1),!r||e&&r!==lt(t)?!1:e}function vn(t,e,r,n){e===void 0&&(e=!1),r===void 0&&(r=!1);let i=t.getBoundingClientRect(),o=ri(t),l=nn(1);e&&(n?kt(n)&&(l=Cn(n)):l=Cn(t));let h=ua(o,r,n)?Yi(o):nn(0),u=(i.left+h.x)/l.x,f=(i.top+h.y)/l.y,y=i.width/l.x,b=i.height/l.y;if(o){let A=lt(o),E=n&&kt(n)?lt(n):n,O=A,P=O.frameElement;for(;P&&n&&E!==O;){let R=Cn(P),$=P.getBoundingClientRect(),B=ht(P),K=$.left+(P.clientLeft+parseFloat(B.paddingLeft))*R.x,X=$.top+(P.clientTop+parseFloat(B.paddingTop))*R.y;u*=R.x,f*=R.y,y*=R.x,b*=R.y,u+=K,f+=X,O=lt(P),P=O.frameElement}}return Dn({width:y,height:b,x:u,y:f})}var da=[":popover-open",":modal"];function Xi(t){return da.some(e=>{try{return t.matches(e)}catch{return!1}})}function pa(t){let{elements:e,rect:r,offsetParent:n,strategy:i}=t,o=i==="fixed",l=Bt(n),h=e?Xi(e.floating):!1;if(n===l||h&&o)return r;let u={scrollLeft:0,scrollTop:0},f=nn(1),y=nn(0),b=_t(n);if((b||!b&&!o)&&((rn(n)!=="body"||Un(l))&&(u=br(n)),_t(n))){let A=vn(n);f=Cn(n),y.x=A.x+n.clientLeft,y.y=A.y+n.clientTop}return{width:r.width*f.x,height:r.height*f.y,x:r.x*f.x-u.scrollLeft*f.x+y.x,y:r.y*f.y-u.scrollTop*f.y+y.y}}function ha(t){return Array.from(t.getClientRects())}function qi(t){return vn(Bt(t)).left+br(t).scrollLeft}function va(t){let e=Bt(t),r=br(t),n=t.ownerDocument.body,i=tt(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),o=tt(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+qi(t),h=-r.scrollTop;return ht(n).direction==="rtl"&&(l+=tt(e.clientWidth,n.clientWidth)-i),{width:i,height:o,x:l,y:h}}function ma(t,e){let r=lt(t),n=Bt(t),i=r.visualViewport,o=n.clientWidth,l=n.clientHeight,h=0,u=0;if(i){o=i.width,l=i.height;let f=ni();(!f||f&&e==="fixed")&&(h=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:h,y:u}}function ga(t,e){let r=vn(t,!0,e==="fixed"),n=r.top+t.clientTop,i=r.left+t.clientLeft,o=_t(t)?Cn(t):nn(1),l=t.clientWidth*o.x,h=t.clientHeight*o.y,u=i*o.x,f=n*o.y;return{width:l,height:h,x:u,y:f}}function Fi(t,e,r){let n;if(e==="viewport")n=ma(t,r);else if(e==="document")n=va(Bt(t));else if(kt(e))n=ga(e,r);else{let i=Yi(t);n={...e,x:e.x-i.x,y:e.y-i.y}}return Dn(n)}function Gi(t,e){let r=_n(t);return r===e||!kt(r)||gr(r)?!1:ht(r).position==="fixed"||Gi(r,e)}function ba(t,e){let r=e.get(t);if(r)return r;let n=zn(t,[],!1).filter(h=>kt(h)&&rn(h)!=="body"),i=null,o=ht(t).position==="fixed",l=o?_n(t):t;for(;kt(l)&&!gr(l);){let h=ht(l),u=ti(l);!u&&h.position==="fixed"&&(i=null),(o?!u&&!i:!u&&h.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||Un(l)&&!u&&Gi(t,l))?n=n.filter(y=>y!==l):i=h,l=_n(l)}return e.set(t,n),n}function ya(t){let{element:e,boundary:r,rootBoundary:n,strategy:i}=t,l=[...r==="clippingAncestors"?ba(e,this._c):[].concat(r),n],h=l[0],u=l.reduce((f,y)=>{let b=Fi(e,y,i);return f.top=tt(b.top,f.top),f.right=Et(b.right,f.right),f.bottom=Et(b.bottom,f.bottom),f.left=tt(b.left,f.left),f},Fi(e,h,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function wa(t){let{width:e,height:r}=Ui(t);return{width:e,height:r}}function xa(t,e,r){let n=_t(e),i=Bt(e),o=r==="fixed",l=vn(t,!0,o,e),h={scrollLeft:0,scrollTop:0},u=nn(0);if(n||!n&&!o)if((rn(e)!=="body"||Un(i))&&(h=br(e)),n){let b=vn(e,!0,o,e);u.x=b.x+e.clientLeft,u.y=b.y+e.clientTop}else i&&(u.x=qi(i));let f=l.left+h.scrollLeft-u.x,y=l.top+h.scrollTop-u.y;return{x:f,y,width:l.width,height:l.height}}function Li(t,e){return!_t(t)||ht(t).position==="fixed"?null:e?e(t):t.offsetParent}function Ki(t,e){let r=lt(t);if(!_t(t)||Xi(t))return r;let n=Li(t,e);for(;n&&la(n)&&ht(n).position==="static";)n=Li(n,e);return n&&(rn(n)==="html"||rn(n)==="body"&&ht(n).position==="static"&&!ti(n))?r:n||ca(t)||r}var Ea=async function(t){let e=this.getOffsetParent||Ki,r=this.getDimensions;return{reference:xa(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,...await r(t.floating)}}};function Oa(t){return ht(t).direction==="rtl"}var Sa={convertOffsetParentRelativeRectToViewportRelativeRect:pa,getDocumentElement:Bt,getClippingRect:ya,getOffsetParent:Ki,getElementRects:Ea,getClientRects:ha,getDimensions:wa,getScale:Cn,isElement:kt,isRTL:Oa};function Aa(t,e){let r=null,n,i=Bt(t);function o(){var h;clearTimeout(n),(h=r)==null||h.disconnect(),r=null}function l(h,u){h===void 0&&(h=!1),u===void 0&&(u=1),o();let{left:f,top:y,width:b,height:A}=t.getBoundingClientRect();if(h||e(),!b||!A)return;let E=pr(y),O=pr(i.clientWidth-(f+b)),P=pr(i.clientHeight-(y+A)),R=pr(f),B={rootMargin:-E+"px "+-O+"px "+-P+"px "+-R+"px",threshold:tt(0,Et(1,u))||1},K=!0;function X(ne){let J=ne[0].intersectionRatio;if(J!==u){if(!K)return l();J?l(!1,J):n=setTimeout(()=>{l(!1,1e-7)},100)}K=!1}try{r=new IntersectionObserver(X,{...B,root:i.ownerDocument})}catch{r=new IntersectionObserver(X,B)}r.observe(t)}return l(!0),o}function Ni(t,e,r,n){n===void 0&&(n={});let{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:u=!1}=n,f=ri(t),y=i||o?[...f?zn(f):[],...zn(e)]:[];y.forEach($=>{i&&$.addEventListener("scroll",r,{passive:!0}),o&&$.addEventListener("resize",r)});let b=f&&h?Aa(f,r):null,A=-1,E=null;l&&(E=new ResizeObserver($=>{let[B]=$;B&&B.target===f&&E&&(E.unobserve(e),cancelAnimationFrame(A),A=requestAnimationFrame(()=>{var K;(K=E)==null||K.observe(e)})),r()}),f&&!u&&E.observe(f),E.observe(e));let O,P=u?vn(t):null;u&&R();function R(){let $=vn(t);P&&($.x!==P.x||$.y!==P.y||$.width!==P.width||$.height!==P.height)&&r(),P=$,O=requestAnimationFrame(R)}return r(),()=>{var $;y.forEach(B=>{i&&B.removeEventListener("scroll",r),o&&B.removeEventListener("resize",r)}),b?.(),($=E)==null||$.disconnect(),E=null,u&&cancelAnimationFrame(O)}}var ii=ea,Ji=aa,Zi=ta,Qi=sa,eo=na,to=Zo,no=ia,ki=(t,e,r)=>{let n=new Map,i={platform:Sa,...r},o={...i.platform,_c:n};return Jo(t,e,{...i,platform:o})},Ca=t=>{let e={placement:"bottom",strategy:"absolute",middleware:[]},r=Object.keys(t),n=i=>t[i];return r.includes("offset")&&e.middleware.push(Wi(n("offset"))),r.includes("teleport")&&(e.strategy="fixed"),r.includes("placement")&&(e.placement=n("placement")),r.includes("autoPlacement")&&!r.includes("flip")&&e.middleware.push(ii(n("autoPlacement"))),r.includes("flip")&&e.middleware.push(Zi(n("flip"))),r.includes("shift")&&e.middleware.push(Ji(n("shift"))),r.includes("inline")&&e.middleware.push(no(n("inline"))),r.includes("arrow")&&e.middleware.push(to(n("arrow"))),r.includes("hide")&&e.middleware.push(eo(n("hide"))),r.includes("size")&&e.middleware.push(Qi(n("size"))),e},Da=(t,e)=>{let r={component:{trap:!1},float:{placement:"bottom",strategy:"absolute",middleware:[]}},n=i=>t[t.indexOf(i)+1];if(t.includes("trap")&&(r.component.trap=!0),t.includes("teleport")&&(r.float.strategy="fixed"),t.includes("offset")&&r.float.middleware.push(Wi(e.offset||10)),t.includes("placement")&&(r.float.placement=n("placement")),t.includes("autoPlacement")&&!t.includes("flip")&&r.float.middleware.push(ii(e.autoPlacement)),t.includes("flip")&&r.float.middleware.push(Zi(e.flip)),t.includes("shift")&&r.float.middleware.push(Ji(e.shift)),t.includes("inline")&&r.float.middleware.push(no(e.inline)),t.includes("arrow")&&r.float.middleware.push(to(e.arrow)),t.includes("hide")&&r.float.middleware.push(eo(e.hide)),t.includes("size")){let i=e.size?.availableWidth??null,o=e.size?.availableHeight??null;i&&delete e.size.availableWidth,o&&delete e.size.availableHeight,r.float.middleware.push(Qi({...e.size,apply({availableWidth:l,availableHeight:h,elements:u}){Object.assign(u.floating.style,{maxWidth:`${i??l}px`,maxHeight:`${o??h}px`})}}))}return r},Ta=t=>{var e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz".split(""),r="";t||(t=Math.floor(Math.random()*e.length));for(var n=0;n<t;n++)r+=e[Math.floor(Math.random()*e.length)];return r};function _a(t,e=()=>{}){let r=!1;return function(){r?e.apply(this,arguments):(r=!0,t.apply(this,arguments))}}function Pa(t){let e={dismissable:!0,trap:!1};function r(n,i=null){if(n){if(n.hasAttribute("aria-expanded")||n.setAttribute("aria-expanded",!1),i.hasAttribute("id"))n.setAttribute("aria-controls",i.getAttribute("id"));else{let o=`panel-${Ta(8)}`;n.setAttribute("aria-controls",o),i.setAttribute("id",o)}i.setAttribute("aria-modal",!0),i.setAttribute("role","dialog")}}t.magic("float",n=>(i={},o={})=>{let l={...e,...o},h=Object.keys(i).length>0?Ca(i):{middleware:[ii()]},u=n,f=n.parentElement.closest("[x-data]"),y=f.querySelector('[x-ref="panel"]');r(u,y);function b(){return y.style.display=="block"}function A(){y.style.display="none",u.setAttribute("aria-expanded","false"),l.trap&&y.setAttribute("x-trap","false"),Ni(n,y,P)}function E(){y.style.display="block",u.setAttribute("aria-expanded","true"),l.trap&&y.setAttribute("x-trap","true"),P()}function O(){b()?A():E()}async function P(){return await ki(n,y,h).then(({middlewareData:R,placement:$,x:B,y:K})=>{if(R.arrow){let X=R.arrow?.x,ne=R.arrow?.y,J=h.middleware.filter(de=>de.name=="arrow")[0].options.element,V={top:"bottom",right:"left",bottom:"top",left:"right"}[$.split("-")[0]];Object.assign(J.style,{left:X!=null?`${X}px`:"",top:ne!=null?`${ne}px`:"",right:"",bottom:"",[V]:"-4px"})}if(R.hide){let{referenceHidden:X}=R.hide;Object.assign(y.style,{visibility:X?"hidden":"visible"})}Object.assign(y.style,{left:`${B}px`,top:`${K}px`})})}l.dismissable&&(window.addEventListener("click",R=>{!f.contains(R.target)&&b()&&O()}),window.addEventListener("keydown",R=>{R.key==="Escape"&&b()&&O()},!0)),O()}),t.directive("float",(n,{modifiers:i,expression:o},{evaluate:l,effect:h})=>{let u=o?l(o):{},f=i.length>0?Da(i,u):{},y=null;f.float.strategy=="fixed"&&(n.style.position="fixed");let b=V=>n.parentElement&&!n.parentElement.closest("[x-data]").contains(V.target)?n.close():null,A=V=>V.key==="Escape"?n.close():null,E=n.getAttribute("x-ref"),O=n.parentElement.closest("[x-data]"),P=O.querySelectorAll(`[\\@click^="$refs.${E}"]`),R=O.querySelectorAll(`[x-on\\:click^="$refs.${E}"]`);n.style.setProperty("display","none"),r([...P,...R][0],n),n._x_isShown=!1,n.trigger=null,n._x_doHide||(n._x_doHide=()=>{n.style.setProperty("display","none",i.includes("important")?"important":void 0)}),n._x_doShow||(n._x_doShow=()=>{n.style.setProperty("display","block",i.includes("important")?"important":void 0)});let $=()=>{n._x_doHide(),n._x_isShown=!1},B=()=>{n._x_doShow(),n._x_isShown=!0},K=()=>setTimeout(B),X=_a(V=>V?B():$(),V=>{typeof n._x_toggleAndCascadeWithTransitions=="function"?n._x_toggleAndCascadeWithTransitions(n,V,B,$):V?K():$()}),ne,J=!0;h(()=>l(V=>{!J&&V===ne||(i.includes("immediate")&&(V?K():$()),X(V),ne=V,J=!1)})),n.open=async function(V){n.trigger=V.currentTarget?V.currentTarget:V,X(!0),n.trigger.setAttribute("aria-expanded","true"),f.component.trap&&n.setAttribute("x-trap","true"),y=Ni(n.trigger,n,()=>{ki(n.trigger,n,f.float).then(({middlewareData:de,placement:U,x:Z,y:me})=>{if(de.arrow){let s=de.arrow?.x,p=de.arrow?.y,v=f.float.middleware.filter(N=>N.name=="arrow")[0].options.element,d={top:"bottom",right:"left",bottom:"top",left:"right"}[U.split("-")[0]];Object.assign(v.style,{left:s!=null?`${s}px`:"",top:p!=null?`${p}px`:"",right:"",bottom:"",[d]:"-4px"})}if(de.hide){let{referenceHidden:s}=de.hide;Object.assign(n.style,{visibility:s?"hidden":"visible"})}Object.assign(n.style,{left:`${Z}px`,top:`${me}px`})})}),window.addEventListener("click",b),window.addEventListener("keydown",A,!0)},n.close=function(){if(!n._x_isShown)return!1;X(!1),n.trigger.setAttribute("aria-expanded","false"),f.component.trap&&n.setAttribute("x-trap","false"),y(),window.removeEventListener("click",b),window.removeEventListener("keydown",A,!1)},n.toggle=function(V){n._x_isShown?n.close():n.open(V)}})}var ro=Pa;function Ma(t){t.store("lazyLoadedAssets",{loaded:new Set,check(l){return Array.isArray(l)?l.every(h=>this.loaded.has(h)):this.loaded.has(l)},markLoaded(l){Array.isArray(l)?l.forEach(h=>this.loaded.add(h)):this.loaded.add(l)}});function e(l){return new CustomEvent(l,{bubbles:!0,composed:!0,cancelable:!0})}function r(l,h={},u,f){let y=document.createElement(l);for(let[b,A]of Object.entries(h))y[b]=A;return u&&(f?u.insertBefore(y,f):u.appendChild(y)),y}function n(l,h,u={},f=null,y=null){let b=l==="link"?`link[href="${h}"]`:`script[src="${h}"]`;if(document.querySelector(b)||t.store("lazyLoadedAssets").check(h))return Promise.resolve();let A=l==="link"?{...u,href:h}:{...u,src:h},E=r(l,A,f,y);return new Promise((O,P)=>{E.onload=()=>{t.store("lazyLoadedAssets").markLoaded(h),O()},E.onerror=()=>{P(new Error(`Failed to load ${l}: ${h}`))}})}async function i(l,h,u=null,f=null){let y={type:"text/css",rel:"stylesheet"};h&&(y.media=h);let b=document.head,A=null;if(u&&f){let E=document.querySelector(`link[href*="${f}"]`);E?(b=E.parentNode,A=u==="before"?E:E.nextSibling):console.warn(`Target (${f}) not found for ${l}. Appending to head.`)}await n("link",l,y,b,A)}async function o(l,h,u=null,f=null){let y,b;u&&f&&(y=document.querySelector(`script[src*="${f}"]`),y?b=u==="before"?y:y.nextSibling:console.warn(`Target (${f}) not found for ${l}. Appending to body.`));let A=h.has("body-start")?"prepend":"append";await n("script",l,{},y||document[h.has("body-end")?"body":"head"],b)}t.directive("load-css",(l,{expression:h},{evaluate:u})=>{let f=u(h),y=l.media,b=l.getAttribute("data-dispatch"),A=l.getAttribute("data-css-before")?"before":l.getAttribute("data-css-after")?"after":null,E=l.getAttribute("data-css-before")||l.getAttribute("data-css-after")||null;Promise.all(f.map(O=>i(O,y,A,E))).then(()=>{b&&window.dispatchEvent(e(b+"-css"))}).catch(O=>{console.error(O)})}),t.directive("load-js",(l,{expression:h,modifiers:u},{evaluate:f})=>{let y=f(h),b=new Set(u),A=l.getAttribute("data-js-before")?"before":l.getAttribute("data-js-after")?"after":null,E=l.getAttribute("data-js-before")||l.getAttribute("data-js-after")||null,O=l.getAttribute("data-dispatch");Promise.all(y.map(P=>o(P,b,A,E))).then(()=>{O&&window.dispatchEvent(e(O+"-js"))}).catch(P=>{console.error(P)})})}var io=Ma;var ko=zo(so(),1);function lo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Mt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?lo(Object(r),!0).forEach(function(n){Ra(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lo(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Sr(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Sr=function(e){return typeof e}:Sr=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sr(t)}function Ra(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $t(){return $t=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$t.apply(this,arguments)}function Ia(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,o;for(o=0;o<n.length;o++)i=n[o],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}function Fa(t,e){if(t==null)return{};var r=Ia(t,e),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)n=o[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var La="1.15.2";function Ht(t){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(t)}var Wt=Ht(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),er=Ht(/Edge/i),co=Ht(/firefox/i),Gn=Ht(/safari/i)&&!Ht(/chrome/i)&&!Ht(/android/i),bo=Ht(/iP(ad|od|hone)/i),yo=Ht(/chrome/i)&&Ht(/android/i),wo={capture:!1,passive:!1};function Ce(t,e,r){t.addEventListener(e,r,!Wt&&wo)}function Oe(t,e,r){t.removeEventListener(e,r,!Wt&&wo)}function _r(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch{return!1}return!1}}function Na(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function St(t,e,r,n){if(t){r=r||document;do{if(e!=null&&(e[0]===">"?t.parentNode===r&&_r(t,e):_r(t,e))||n&&t===r)return t;if(t===r)break}while(t=Na(t))}return null}var fo=/\s+/g;function ct(t,e,r){if(t&&e)if(t.classList)t.classList[r?"add":"remove"](e);else{var n=(" "+t.className+" ").replace(fo," ").replace(" "+e+" "," ");t.className=(n+(r?" "+e:"")).replace(fo," ")}}function ae(t,e,r){var n=t&&t.style;if(n){if(r===void 0)return document.defaultView&&document.defaultView.getComputedStyle?r=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(r=t.currentStyle),e===void 0?r:r[e];!(e in n)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),n[e]=r+(typeof r=="string"?"":"px")}}function Ln(t,e){var r="";if(typeof t=="string")r=t;else do{var n=ae(t,"transform");n&&n!=="none"&&(r=n+" "+r)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(r)}function xo(t,e,r){if(t){var n=t.getElementsByTagName(e),i=0,o=n.length;if(r)for(;i<o;i++)r(n[i],i);return n}return[]}function Pt(){var t=document.scrollingElement;return t||document.documentElement}function qe(t,e,r,n,i){if(!(!t.getBoundingClientRect&&t!==window)){var o,l,h,u,f,y,b;if(t!==window&&t.parentNode&&t!==Pt()?(o=t.getBoundingClientRect(),l=o.top,h=o.left,u=o.bottom,f=o.right,y=o.height,b=o.width):(l=0,h=0,u=window.innerHeight,f=window.innerWidth,y=window.innerHeight,b=window.innerWidth),(e||r)&&t!==window&&(i=i||t.parentNode,!Wt))do if(i&&i.getBoundingClientRect&&(ae(i,"transform")!=="none"||r&&ae(i,"position")!=="static")){var A=i.getBoundingClientRect();l-=A.top+parseInt(ae(i,"border-top-width")),h-=A.left+parseInt(ae(i,"border-left-width")),u=l+o.height,f=h+o.width;break}while(i=i.parentNode);if(n&&t!==window){var E=Ln(i||t),O=E&&E.a,P=E&&E.d;E&&(l/=P,h/=O,b/=O,y/=P,u=l+y,f=h+b)}return{top:l,left:h,bottom:u,right:f,width:b,height:y}}}function uo(t,e,r){for(var n=sn(t,!0),i=qe(t)[e];n;){var o=qe(n)[r],l=void 0;if(r==="top"||r==="left"?l=i>=o:l=i<=o,!l)return n;if(n===Pt())break;n=sn(n,!1)}return!1}function Nn(t,e,r,n){for(var i=0,o=0,l=t.children;o<l.length;){if(l[o].style.display!=="none"&&l[o]!==se.ghost&&(n||l[o]!==se.dragged)&&St(l[o],r.draggable,t,!1)){if(i===e)return l[o];i++}o++}return null}function bi(t,e){for(var r=t.lastElementChild;r&&(r===se.ghost||ae(r,"display")==="none"||e&&!_r(r,e));)r=r.previousElementSibling;return r||null}function vt(t,e){var r=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==se.clone&&(!e||_r(t,e))&&r++;return r}function po(t){var e=0,r=0,n=Pt();if(t)do{var i=Ln(t),o=i.a,l=i.d;e+=t.scrollLeft*o,r+=t.scrollTop*l}while(t!==n&&(t=t.parentNode));return[e,r]}function ka(t,e){for(var r in t)if(t.hasOwnProperty(r)){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===t[r][n])return Number(r)}return-1}function sn(t,e){if(!t||!t.getBoundingClientRect)return Pt();var r=t,n=!1;do if(r.clientWidth<r.scrollWidth||r.clientHeight<r.scrollHeight){var i=ae(r);if(r.clientWidth<r.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||r.clientHeight<r.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!r.getBoundingClientRect||r===document.body)return Pt();if(n||e)return r;n=!0}}while(r=r.parentNode);return Pt()}function ja(t,e){if(t&&e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}function oi(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Kn;function Eo(t,e){return function(){if(!Kn){var r=arguments,n=this;r.length===1?t.call(n,r[0]):t.apply(n,r),Kn=setTimeout(function(){Kn=void 0},e)}}}function Ba(){clearTimeout(Kn),Kn=void 0}function Oo(t,e,r){t.scrollLeft+=e,t.scrollTop+=r}function So(t){var e=window.Polymer,r=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):r?r(t).clone(!0)[0]:t.cloneNode(!0)}function Ao(t,e,r){var n={};return Array.from(t.children).forEach(function(i){var o,l,h,u;if(!(!St(i,e.draggable,t,!1)||i.animated||i===r)){var f=qe(i);n.left=Math.min((o=n.left)!==null&&o!==void 0?o:1/0,f.left),n.top=Math.min((l=n.top)!==null&&l!==void 0?l:1/0,f.top),n.right=Math.max((h=n.right)!==null&&h!==void 0?h:-1/0,f.right),n.bottom=Math.max((u=n.bottom)!==null&&u!==void 0?u:-1/0,f.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var ut="Sortable"+new Date().getTime();function Ha(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(ae(i,"display")==="none"||i===se.ghost)){t.push({target:i,rect:qe(i)});var o=Mt({},t[t.length-1].rect);if(i.thisAnimationDuration){var l=Ln(i,!0);l&&(o.top-=l.f,o.left-=l.e)}i.fromRect=o}})}},addAnimationState:function(n){t.push(n)},removeAnimationState:function(n){t.splice(ka(t,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(e),typeof n=="function"&&n();return}var o=!1,l=0;t.forEach(function(h){var u=0,f=h.target,y=f.fromRect,b=qe(f),A=f.prevFromRect,E=f.prevToRect,O=h.rect,P=Ln(f,!0);P&&(b.top-=P.f,b.left-=P.e),f.toRect=b,f.thisAnimationDuration&&oi(A,b)&&!oi(y,b)&&(O.top-b.top)/(O.left-b.left)===(y.top-b.top)/(y.left-b.left)&&(u=Wa(O,A,E,i.options)),oi(b,y)||(f.prevFromRect=y,f.prevToRect=b,u||(u=i.options.animation),i.animate(f,O,b,u)),u&&(o=!0,l=Math.max(l,u),clearTimeout(f.animationResetTimer),f.animationResetTimer=setTimeout(function(){f.animationTime=0,f.prevFromRect=null,f.fromRect=null,f.prevToRect=null,f.thisAnimationDuration=null},u),f.thisAnimationDuration=u)}),clearTimeout(e),o?e=setTimeout(function(){typeof n=="function"&&n()},l):typeof n=="function"&&n(),t=[]},animate:function(n,i,o,l){if(l){ae(n,"transition",""),ae(n,"transform","");var h=Ln(this.el),u=h&&h.a,f=h&&h.d,y=(i.left-o.left)/(u||1),b=(i.top-o.top)/(f||1);n.animatingX=!!y,n.animatingY=!!b,ae(n,"transform","translate3d("+y+"px,"+b+"px,0)"),this.forRepaintDummy=$a(n),ae(n,"transition","transform "+l+"ms"+(this.options.easing?" "+this.options.easing:"")),ae(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){ae(n,"transition",""),ae(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},l)}}}}function $a(t){return t.offsetWidth}function Wa(t,e,r,n){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-r.top,2)+Math.pow(e.left-r.left,2))*n.animation}var Mn=[],ai={initializeByDefault:!0},tr={mount:function(e){for(var r in ai)ai.hasOwnProperty(r)&&!(r in e)&&(e[r]=ai[r]);Mn.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Mn.push(e)},pluginEvent:function(e,r,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var o=e+"Global";Mn.forEach(function(l){r[l.pluginName]&&(r[l.pluginName][o]&&r[l.pluginName][o](Mt({sortable:r},n)),r.options[l.pluginName]&&r[l.pluginName][e]&&r[l.pluginName][e](Mt({sortable:r},n)))})},initializePlugins:function(e,r,n,i){Mn.forEach(function(h){var u=h.pluginName;if(!(!e.options[u]&&!h.initializeByDefault)){var f=new h(e,r,e.options);f.sortable=e,f.options=e.options,e[u]=f,$t(n,f.defaults)}});for(var o in e.options)if(e.options.hasOwnProperty(o)){var l=this.modifyOption(e,o,e.options[o]);typeof l<"u"&&(e.options[o]=l)}},getEventProperties:function(e,r){var n={};return Mn.forEach(function(i){typeof i.eventProperties=="function"&&$t(n,i.eventProperties.call(r[i.pluginName],e))}),n},modifyOption:function(e,r,n){var i;return Mn.forEach(function(o){e[o.pluginName]&&o.optionListeners&&typeof o.optionListeners[r]=="function"&&(i=o.optionListeners[r].call(e[o.pluginName],n))}),i}};function Va(t){var e=t.sortable,r=t.rootEl,n=t.name,i=t.targetEl,o=t.cloneEl,l=t.toEl,h=t.fromEl,u=t.oldIndex,f=t.newIndex,y=t.oldDraggableIndex,b=t.newDraggableIndex,A=t.originalEvent,E=t.putSortable,O=t.extraEventProperties;if(e=e||r&&r[ut],!!e){var P,R=e.options,$="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!Wt&&!er?P=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(P=document.createEvent("Event"),P.initEvent(n,!0,!0)),P.to=l||r,P.from=h||r,P.item=i||r,P.clone=o,P.oldIndex=u,P.newIndex=f,P.oldDraggableIndex=y,P.newDraggableIndex=b,P.originalEvent=A,P.pullMode=E?E.lastPutMode:void 0;var B=Mt(Mt({},O),tr.getEventProperties(n,e));for(var K in B)P[K]=B[K];r&&r.dispatchEvent(P),R[$]&&R[$].call(e,P)}}var za=["evt"],at=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,o=Fa(n,za);tr.pluginEvent.bind(se)(e,r,Mt({dragEl:L,parentEl:ze,ghostEl:ue,rootEl:ke,nextEl:bn,lastDownEl:Ar,cloneEl:We,cloneHidden:an,dragStarted:Yn,putSortable:Ze,activeSortable:se.active,originalEvent:i,oldIndex:Fn,oldDraggableIndex:Jn,newIndex:ft,newDraggableIndex:on,hideGhostForTarget:_o,unhideGhostForTarget:Po,cloneNowHidden:function(){an=!0},cloneNowShown:function(){an=!1},dispatchSortableEvent:function(h){it({sortable:r,name:h,originalEvent:i})}},o))};function it(t){Va(Mt({putSortable:Ze,cloneEl:We,targetEl:L,rootEl:ke,oldIndex:Fn,oldDraggableIndex:Jn,newIndex:ft,newDraggableIndex:on},t))}var L,ze,ue,ke,bn,Ar,We,an,Fn,ft,Jn,on,wr,Ze,In=!1,Pr=!1,Mr=[],mn,Ot,si,li,ho,vo,Yn,Rn,Zn,Qn=!1,xr=!1,Cr,nt,ci=[],hi=!1,Rr=[],Fr=typeof document<"u",Er=bo,mo=er||Wt?"cssFloat":"float",Ua=Fr&&!yo&&!bo&&"draggable"in document.createElement("div"),Co=function(){if(Fr){if(Wt)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),Do=function(e,r){var n=ae(e),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),o=Nn(e,0,r),l=Nn(e,1,r),h=o&&ae(o),u=l&&ae(l),f=h&&parseInt(h.marginLeft)+parseInt(h.marginRight)+qe(o).width,y=u&&parseInt(u.marginLeft)+parseInt(u.marginRight)+qe(l).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&h.float&&h.float!=="none"){var b=h.float==="left"?"left":"right";return l&&(u.clear==="both"||u.clear===b)?"vertical":"horizontal"}return o&&(h.display==="block"||h.display==="flex"||h.display==="table"||h.display==="grid"||f>=i&&n[mo]==="none"||l&&n[mo]==="none"&&f+y>i)?"vertical":"horizontal"},Ya=function(e,r,n){var i=n?e.left:e.top,o=n?e.right:e.bottom,l=n?e.width:e.height,h=n?r.left:r.top,u=n?r.right:r.bottom,f=n?r.width:r.height;return i===h||o===u||i+l/2===h+f/2},Xa=function(e,r){var n;return Mr.some(function(i){var o=i[ut].options.emptyInsertThreshold;if(!(!o||bi(i))){var l=qe(i),h=e>=l.left-o&&e<=l.right+o,u=r>=l.top-o&&r<=l.bottom+o;if(h&&u)return n=i}}),n},To=function(e){function r(o,l){return function(h,u,f,y){var b=h.options.group.name&&u.options.group.name&&h.options.group.name===u.options.group.name;if(o==null&&(l||b))return!0;if(o==null||o===!1)return!1;if(l&&o==="clone")return o;if(typeof o=="function")return r(o(h,u,f,y),l)(h,u,f,y);var A=(l?h:u).options.group.name;return o===!0||typeof o=="string"&&o===A||o.join&&o.indexOf(A)>-1}}var n={},i=e.group;(!i||Sr(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=r(i.pull,!0),n.checkPut=r(i.put),n.revertClone=i.revertClone,e.group=n},_o=function(){!Co&&ue&&ae(ue,"display","none")},Po=function(){!Co&&ue&&ae(ue,"display","")};Fr&&!yo&&document.addEventListener("click",function(t){if(Pr)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Pr=!1,!1},!0);var gn=function(e){if(L){e=e.touches?e.touches[0]:e;var r=Xa(e.clientX,e.clientY);if(r){var n={};for(var i in e)e.hasOwnProperty(i)&&(n[i]=e[i]);n.target=n.rootEl=r,n.preventDefault=void 0,n.stopPropagation=void 0,r[ut]._onDragOver(n)}}},qa=function(e){L&&L.parentNode[ut]._isOutsideThisEl(e.target)};function se(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=$t({},e),t[ut]=this;var r={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Do(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(l,h){l.setData("Text",h.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:se.supportPointer!==!1&&"PointerEvent"in window&&!Gn,emptyInsertThreshold:5};tr.initializePlugins(this,t,r);for(var n in r)!(n in e)&&(e[n]=r[n]);To(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:Ua,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?Ce(t,"pointerdown",this._onTapStart):(Ce(t,"mousedown",this._onTapStart),Ce(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(Ce(t,"dragover",this),Ce(t,"dragenter",this)),Mr.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),$t(this,Ha())}se.prototype={constructor:se,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Rn=null)},_getDirection:function(e,r){return typeof this.options.direction=="function"?this.options.direction.call(this,e,r,L):this.options.direction},_onTapStart:function(e){if(e.cancelable){var r=this,n=this.el,i=this.options,o=i.preventOnFilter,l=e.type,h=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,u=(h||e).target,f=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||u,y=i.filter;if(ns(n),!L&&!(/mousedown|pointerdown/.test(l)&&e.button!==0||i.disabled)&&!f.isContentEditable&&!(!this.nativeDraggable&&Gn&&u&&u.tagName.toUpperCase()==="SELECT")&&(u=St(u,i.draggable,n,!1),!(u&&u.animated)&&Ar!==u)){if(Fn=vt(u),Jn=vt(u,i.draggable),typeof y=="function"){if(y.call(this,e,u,this)){it({sortable:r,rootEl:f,name:"filter",targetEl:u,toEl:n,fromEl:n}),at("filter",r,{evt:e}),o&&e.cancelable&&e.preventDefault();return}}else if(y&&(y=y.split(",").some(function(b){if(b=St(f,b.trim(),n,!1),b)return it({sortable:r,rootEl:b,name:"filter",targetEl:u,fromEl:n,toEl:n}),at("filter",r,{evt:e}),!0}),y)){o&&e.cancelable&&e.preventDefault();return}i.handle&&!St(f,i.handle,n,!1)||this._prepareDragStart(e,h,u)}}},_prepareDragStart:function(e,r,n){var i=this,o=i.el,l=i.options,h=o.ownerDocument,u;if(n&&!L&&n.parentNode===o){var f=qe(n);if(ke=o,L=n,ze=L.parentNode,bn=L.nextSibling,Ar=n,wr=l.group,se.dragged=L,mn={target:L,clientX:(r||e).clientX,clientY:(r||e).clientY},ho=mn.clientX-f.left,vo=mn.clientY-f.top,this._lastX=(r||e).clientX,this._lastY=(r||e).clientY,L.style["will-change"]="all",u=function(){if(at("delayEnded",i,{evt:e}),se.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!co&&i.nativeDraggable&&(L.draggable=!0),i._triggerDragStart(e,r),it({sortable:i,name:"choose",originalEvent:e}),ct(L,l.chosenClass,!0)},l.ignore.split(",").forEach(function(y){xo(L,y.trim(),fi)}),Ce(h,"dragover",gn),Ce(h,"mousemove",gn),Ce(h,"touchmove",gn),Ce(h,"mouseup",i._onDrop),Ce(h,"touchend",i._onDrop),Ce(h,"touchcancel",i._onDrop),co&&this.nativeDraggable&&(this.options.touchStartThreshold=4,L.draggable=!0),at("delayStart",this,{evt:e}),l.delay&&(!l.delayOnTouchOnly||r)&&(!this.nativeDraggable||!(er||Wt))){if(se.eventCanceled){this._onDrop();return}Ce(h,"mouseup",i._disableDelayedDrag),Ce(h,"touchend",i._disableDelayedDrag),Ce(h,"touchcancel",i._disableDelayedDrag),Ce(h,"mousemove",i._delayedDragTouchMoveHandler),Ce(h,"touchmove",i._delayedDragTouchMoveHandler),l.supportPointer&&Ce(h,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(u,l.delay)}else u()}},_delayedDragTouchMoveHandler:function(e){var r=e.touches?e.touches[0]:e;Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){L&&fi(L),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;Oe(e,"mouseup",this._disableDelayedDrag),Oe(e,"touchend",this._disableDelayedDrag),Oe(e,"touchcancel",this._disableDelayedDrag),Oe(e,"mousemove",this._delayedDragTouchMoveHandler),Oe(e,"touchmove",this._delayedDragTouchMoveHandler),Oe(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,r){r=r||e.pointerType=="touch"&&e,!this.nativeDraggable||r?this.options.supportPointer?Ce(document,"pointermove",this._onTouchMove):r?Ce(document,"touchmove",this._onTouchMove):Ce(document,"mousemove",this._onTouchMove):(Ce(L,"dragend",this),Ce(ke,"dragstart",this._onDragStart));try{document.selection?Dr(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,r){if(In=!1,ke&&L){at("dragStarted",this,{evt:r}),this.nativeDraggable&&Ce(document,"dragover",qa);var n=this.options;!e&&ct(L,n.dragClass,!1),ct(L,n.ghostClass,!0),se.active=this,e&&this._appendGhost(),it({sortable:this,name:"start",originalEvent:r})}else this._nulling()},_emulateDragOver:function(){if(Ot){this._lastX=Ot.clientX,this._lastY=Ot.clientY,_o();for(var e=document.elementFromPoint(Ot.clientX,Ot.clientY),r=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Ot.clientX,Ot.clientY),e!==r);)r=e;if(L.parentNode[ut]._isOutsideThisEl(e),r)do{if(r[ut]){var n=void 0;if(n=r[ut]._onDragOver({clientX:Ot.clientX,clientY:Ot.clientY,target:e,rootEl:r}),n&&!this.options.dragoverBubble)break}e=r}while(r=r.parentNode);Po()}},_onTouchMove:function(e){if(mn){var r=this.options,n=r.fallbackTolerance,i=r.fallbackOffset,o=e.touches?e.touches[0]:e,l=ue&&Ln(ue,!0),h=ue&&l&&l.a,u=ue&&l&&l.d,f=Er&&nt&&po(nt),y=(o.clientX-mn.clientX+i.x)/(h||1)+(f?f[0]-ci[0]:0)/(h||1),b=(o.clientY-mn.clientY+i.y)/(u||1)+(f?f[1]-ci[1]:0)/(u||1);if(!se.active&&!In){if(n&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(ue){l?(l.e+=y-(si||0),l.f+=b-(li||0)):l={a:1,b:0,c:0,d:1,e:y,f:b};var A="matrix(".concat(l.a,",").concat(l.b,",").concat(l.c,",").concat(l.d,",").concat(l.e,",").concat(l.f,")");ae(ue,"webkitTransform",A),ae(ue,"mozTransform",A),ae(ue,"msTransform",A),ae(ue,"transform",A),si=y,li=b,Ot=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!ue){var e=this.options.fallbackOnBody?document.body:ke,r=qe(L,!0,Er,!0,e),n=this.options;if(Er){for(nt=e;ae(nt,"position")==="static"&&ae(nt,"transform")==="none"&&nt!==document;)nt=nt.parentNode;nt!==document.body&&nt!==document.documentElement?(nt===document&&(nt=Pt()),r.top+=nt.scrollTop,r.left+=nt.scrollLeft):nt=Pt(),ci=po(nt)}ue=L.cloneNode(!0),ct(ue,n.ghostClass,!1),ct(ue,n.fallbackClass,!0),ct(ue,n.dragClass,!0),ae(ue,"transition",""),ae(ue,"transform",""),ae(ue,"box-sizing","border-box"),ae(ue,"margin",0),ae(ue,"top",r.top),ae(ue,"left",r.left),ae(ue,"width",r.width),ae(ue,"height",r.height),ae(ue,"opacity","0.8"),ae(ue,"position",Er?"absolute":"fixed"),ae(ue,"zIndex","100000"),ae(ue,"pointerEvents","none"),se.ghost=ue,e.appendChild(ue),ae(ue,"transform-origin",ho/parseInt(ue.style.width)*100+"% "+vo/parseInt(ue.style.height)*100+"%")}},_onDragStart:function(e,r){var n=this,i=e.dataTransfer,o=n.options;if(at("dragStart",this,{evt:e}),se.eventCanceled){this._onDrop();return}at("setupClone",this),se.eventCanceled||(We=So(L),We.removeAttribute("id"),We.draggable=!1,We.style["will-change"]="",this._hideClone(),ct(We,this.options.chosenClass,!1),se.clone=We),n.cloneId=Dr(function(){at("clone",n),!se.eventCanceled&&(n.options.removeCloneOnHide||ke.insertBefore(We,L),n._hideClone(),it({sortable:n,name:"clone"}))}),!r&&ct(L,o.dragClass,!0),r?(Pr=!0,n._loopId=setInterval(n._emulateDragOver,50)):(Oe(document,"mouseup",n._onDrop),Oe(document,"touchend",n._onDrop),Oe(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",o.setData&&o.setData.call(n,i,L)),Ce(document,"drop",n),ae(L,"transform","translateZ(0)")),In=!0,n._dragStartId=Dr(n._dragStarted.bind(n,r,e)),Ce(document,"selectstart",n),Yn=!0,Gn&&ae(document.body,"user-select","none")},_onDragOver:function(e){var r=this.el,n=e.target,i,o,l,h=this.options,u=h.group,f=se.active,y=wr===u,b=h.sort,A=Ze||f,E,O=this,P=!1;if(hi)return;function R(M,Q){at(M,O,Mt({evt:e,isOwner:y,axis:E?"vertical":"horizontal",revert:l,dragRect:i,targetRect:o,canSort:b,fromSortable:A,target:n,completed:B,onMove:function(Rt,Vt){return Or(ke,r,L,i,Rt,qe(Rt),e,Vt)},changed:K},Q))}function $(){R("dragOverAnimationCapture"),O.captureAnimationState(),O!==A&&A.captureAnimationState()}function B(M){return R("dragOverCompleted",{insertion:M}),M&&(y?f._hideClone():f._showClone(O),O!==A&&(ct(L,Ze?Ze.options.ghostClass:f.options.ghostClass,!1),ct(L,h.ghostClass,!0)),Ze!==O&&O!==se.active?Ze=O:O===se.active&&Ze&&(Ze=null),A===O&&(O._ignoreWhileAnimating=n),O.animateAll(function(){R("dragOverAnimationComplete"),O._ignoreWhileAnimating=null}),O!==A&&(A.animateAll(),A._ignoreWhileAnimating=null)),(n===L&&!L.animated||n===r&&!n.animated)&&(Rn=null),!h.dragoverBubble&&!e.rootEl&&n!==document&&(L.parentNode[ut]._isOutsideThisEl(e.target),!M&&gn(e)),!h.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),P=!0}function K(){ft=vt(L),on=vt(L,h.draggable),it({sortable:O,name:"change",toEl:r,newIndex:ft,newDraggableIndex:on,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=St(n,h.draggable,r,!0),R("dragOver"),se.eventCanceled)return P;if(L.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||O._ignoreWhileAnimating===n)return B(!1);if(Pr=!1,f&&!h.disabled&&(y?b||(l=ze!==ke):Ze===this||(this.lastPutMode=wr.checkPull(this,f,L,e))&&u.checkPut(this,f,L,e))){if(E=this._getDirection(e,n)==="vertical",i=qe(L),R("dragOverValid"),se.eventCanceled)return P;if(l)return ze=ke,$(),this._hideClone(),R("revert"),se.eventCanceled||(bn?ke.insertBefore(L,bn):ke.appendChild(L)),B(!0);var X=bi(r,h.draggable);if(!X||Za(e,E,this)&&!X.animated){if(X===L)return B(!1);if(X&&r===e.target&&(n=X),n&&(o=qe(n)),Or(ke,r,L,i,n,o,e,!!n)!==!1)return $(),X&&X.nextSibling?r.insertBefore(L,X.nextSibling):r.appendChild(L),ze=r,K(),B(!0)}else if(X&&Ja(e,E,this)){var ne=Nn(r,0,h,!0);if(ne===L)return B(!1);if(n=ne,o=qe(n),Or(ke,r,L,i,n,o,e,!1)!==!1)return $(),r.insertBefore(L,ne),ze=r,K(),B(!0)}else if(n.parentNode===r){o=qe(n);var J=0,V,de=L.parentNode!==r,U=!Ya(L.animated&&L.toRect||i,n.animated&&n.toRect||o,E),Z=E?"top":"left",me=uo(n,"top","top")||uo(L,"top","top"),s=me?me.scrollTop:void 0;Rn!==n&&(V=o[Z],Qn=!1,xr=!U&&h.invertSwap||de),J=Qa(e,n,o,E,U?1:h.swapThreshold,h.invertedSwapThreshold==null?h.swapThreshold:h.invertedSwapThreshold,xr,Rn===n);var p;if(J!==0){var v=vt(L);do v-=J,p=ze.children[v];while(p&&(ae(p,"display")==="none"||p===ue))}if(J===0||p===n)return B(!1);Rn=n,Zn=J;var d=n.nextElementSibling,N=!1;N=J===1;var _=Or(ke,r,L,i,n,o,e,N);if(_!==!1)return(_===1||_===-1)&&(N=_===1),hi=!0,setTimeout(Ka,30),$(),N&&!d?r.appendChild(L):n.parentNode.insertBefore(L,N?d:n),me&&Oo(me,0,s-me.scrollTop),ze=L.parentNode,V!==void 0&&!xr&&(Cr=Math.abs(V-qe(n)[Z])),K(),B(!0)}if(r.contains(L))return B(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){Oe(document,"mousemove",this._onTouchMove),Oe(document,"touchmove",this._onTouchMove),Oe(document,"pointermove",this._onTouchMove),Oe(document,"dragover",gn),Oe(document,"mousemove",gn),Oe(document,"touchmove",gn)},_offUpEvents:function(){var e=this.el.ownerDocument;Oe(e,"mouseup",this._onDrop),Oe(e,"touchend",this._onDrop),Oe(e,"pointerup",this._onDrop),Oe(e,"touchcancel",this._onDrop),Oe(document,"selectstart",this)},_onDrop:function(e){var r=this.el,n=this.options;if(ft=vt(L),on=vt(L,n.draggable),at("drop",this,{evt:e}),ze=L&&L.parentNode,ft=vt(L),on=vt(L,n.draggable),se.eventCanceled){this._nulling();return}In=!1,xr=!1,Qn=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),vi(this.cloneId),vi(this._dragStartId),this.nativeDraggable&&(Oe(document,"drop",this),Oe(r,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Gn&&ae(document.body,"user-select",""),ae(L,"transform",""),e&&(Yn&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),ue&&ue.parentNode&&ue.parentNode.removeChild(ue),(ke===ze||Ze&&Ze.lastPutMode!=="clone")&&We&&We.parentNode&&We.parentNode.removeChild(We),L&&(this.nativeDraggable&&Oe(L,"dragend",this),fi(L),L.style["will-change"]="",Yn&&!In&&ct(L,Ze?Ze.options.ghostClass:this.options.ghostClass,!1),ct(L,this.options.chosenClass,!1),it({sortable:this,name:"unchoose",toEl:ze,newIndex:null,newDraggableIndex:null,originalEvent:e}),ke!==ze?(ft>=0&&(it({rootEl:ze,name:"add",toEl:ze,fromEl:ke,originalEvent:e}),it({sortable:this,name:"remove",toEl:ze,originalEvent:e}),it({rootEl:ze,name:"sort",toEl:ze,fromEl:ke,originalEvent:e}),it({sortable:this,name:"sort",toEl:ze,originalEvent:e})),Ze&&Ze.save()):ft!==Fn&&ft>=0&&(it({sortable:this,name:"update",toEl:ze,originalEvent:e}),it({sortable:this,name:"sort",toEl:ze,originalEvent:e})),se.active&&((ft==null||ft===-1)&&(ft=Fn,on=Jn),it({sortable:this,name:"end",toEl:ze,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){at("nulling",this),ke=L=ze=ue=bn=We=Ar=an=mn=Ot=Yn=ft=on=Fn=Jn=Rn=Zn=Ze=wr=se.dragged=se.ghost=se.clone=se.active=null,Rr.forEach(function(e){e.checked=!0}),Rr.length=si=li=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":L&&(this._onDragOver(e),Ga(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],r,n=this.el.children,i=0,o=n.length,l=this.options;i<o;i++)r=n[i],St(r,l.draggable,this.el,!1)&&e.push(r.getAttribute(l.dataIdAttr)||ts(r));return e},sort:function(e,r){var n={},i=this.el;this.toArray().forEach(function(o,l){var h=i.children[l];St(h,this.options.draggable,i,!1)&&(n[o]=h)},this),r&&this.captureAnimationState(),e.forEach(function(o){n[o]&&(i.removeChild(n[o]),i.appendChild(n[o]))}),r&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,r){return St(e,r||this.options.draggable,this.el,!1)},option:function(e,r){var n=this.options;if(r===void 0)return n[e];var i=tr.modifyOption(this,e,r);typeof i<"u"?n[e]=i:n[e]=r,e==="group"&&To(n)},destroy:function(){at("destroy",this);var e=this.el;e[ut]=null,Oe(e,"mousedown",this._onTapStart),Oe(e,"touchstart",this._onTapStart),Oe(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(Oe(e,"dragover",this),Oe(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(r){r.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Mr.splice(Mr.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!an){if(at("hideClone",this),se.eventCanceled)return;ae(We,"display","none"),this.options.removeCloneOnHide&&We.parentNode&&We.parentNode.removeChild(We),an=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(an){if(at("showClone",this),se.eventCanceled)return;L.parentNode==ke&&!this.options.group.revertClone?ke.insertBefore(We,L):bn?ke.insertBefore(We,bn):ke.appendChild(We),this.options.group.revertClone&&this.animate(L,We),ae(We,"display",""),an=!1}}};function Ga(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function Or(t,e,r,n,i,o,l,h){var u,f=t[ut],y=f.options.onMove,b;return window.CustomEvent&&!Wt&&!er?u=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(u=document.createEvent("Event"),u.initEvent("move",!0,!0)),u.to=e,u.from=t,u.dragged=r,u.draggedRect=n,u.related=i||e,u.relatedRect=o||qe(e),u.willInsertAfter=h,u.originalEvent=l,t.dispatchEvent(u),y&&(b=y.call(f,u,l)),b}function fi(t){t.draggable=!1}function Ka(){hi=!1}function Ja(t,e,r){var n=qe(Nn(r.el,0,r.options,!0)),i=Ao(r.el,r.options,ue),o=10;return e?t.clientX<i.left-o||t.clientY<n.top&&t.clientX<n.right:t.clientY<i.top-o||t.clientY<n.bottom&&t.clientX<n.left}function Za(t,e,r){var n=qe(bi(r.el,r.options.draggable)),i=Ao(r.el,r.options,ue),o=10;return e?t.clientX>i.right+o||t.clientY>n.bottom&&t.clientX>n.left:t.clientY>i.bottom+o||t.clientX>n.right&&t.clientY>n.top}function Qa(t,e,r,n,i,o,l,h){var u=n?t.clientY:t.clientX,f=n?r.height:r.width,y=n?r.top:r.left,b=n?r.bottom:r.right,A=!1;if(!l){if(h&&Cr<f*i){if(!Qn&&(Zn===1?u>y+f*o/2:u<b-f*o/2)&&(Qn=!0),Qn)A=!0;else if(Zn===1?u<y+Cr:u>b-Cr)return-Zn}else if(u>y+f*(1-i)/2&&u<b-f*(1-i)/2)return es(e)}return A=A||l,A&&(u<y+f*o/2||u>b-f*o/2)?u>y+f/2?1:-1:0}function es(t){return vt(L)<vt(t)?1:-1}function ts(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,r=e.length,n=0;r--;)n+=e.charCodeAt(r);return n.toString(36)}function ns(t){Rr.length=0;for(var e=t.getElementsByTagName("input"),r=e.length;r--;){var n=e[r];n.checked&&Rr.push(n)}}function Dr(t){return setTimeout(t,0)}function vi(t){return clearTimeout(t)}Fr&&Ce(document,"touchmove",function(t){(se.active||In)&&t.cancelable&&t.preventDefault()});se.utils={on:Ce,off:Oe,css:ae,find:xo,is:function(e,r){return!!St(e,r,e,!1)},extend:ja,throttle:Eo,closest:St,toggleClass:ct,clone:So,index:vt,nextTick:Dr,cancelNextTick:vi,detectDirection:Do,getChild:Nn};se.get=function(t){return t[ut]};se.mount=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];e[0].constructor===Array&&(e=e[0]),e.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(se.utils=Mt(Mt({},se.utils),n.utils)),tr.mount(n)})};se.create=function(t,e){return new se(t,e)};se.version=La;var Xe=[],Xn,mi,gi=!1,ui,di,Ir,qn;function rs(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(r){var n=r.originalEvent;this.sortable.nativeDraggable?Ce(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Ce(document,"pointermove",this._handleFallbackAutoScroll):n.touches?Ce(document,"touchmove",this._handleFallbackAutoScroll):Ce(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(r){var n=r.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?Oe(document,"dragover",this._handleAutoScroll):(Oe(document,"pointermove",this._handleFallbackAutoScroll),Oe(document,"touchmove",this._handleFallbackAutoScroll),Oe(document,"mousemove",this._handleFallbackAutoScroll)),go(),Tr(),Ba()},nulling:function(){Ir=mi=Xn=gi=qn=ui=di=null,Xe.length=0},_handleFallbackAutoScroll:function(r){this._handleAutoScroll(r,!0)},_handleAutoScroll:function(r,n){var i=this,o=(r.touches?r.touches[0]:r).clientX,l=(r.touches?r.touches[0]:r).clientY,h=document.elementFromPoint(o,l);if(Ir=r,n||this.options.forceAutoScrollFallback||er||Wt||Gn){pi(r,this.options,h,n);var u=sn(h,!0);gi&&(!qn||o!==ui||l!==di)&&(qn&&go(),qn=setInterval(function(){var f=sn(document.elementFromPoint(o,l),!0);f!==u&&(u=f,Tr()),pi(r,i.options,f,n)},10),ui=o,di=l)}else{if(!this.options.bubbleScroll||sn(h,!0)===Pt()){Tr();return}pi(r,this.options,sn(h,!1),!1)}}},$t(t,{pluginName:"scroll",initializeByDefault:!0})}function Tr(){Xe.forEach(function(t){clearInterval(t.pid)}),Xe=[]}function go(){clearInterval(qn)}var pi=Eo(function(t,e,r,n){if(e.scroll){var i=(t.touches?t.touches[0]:t).clientX,o=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,h=e.scrollSpeed,u=Pt(),f=!1,y;mi!==r&&(mi=r,Tr(),Xn=e.scroll,y=e.scrollFn,Xn===!0&&(Xn=sn(r,!0)));var b=0,A=Xn;do{var E=A,O=qe(E),P=O.top,R=O.bottom,$=O.left,B=O.right,K=O.width,X=O.height,ne=void 0,J=void 0,V=E.scrollWidth,de=E.scrollHeight,U=ae(E),Z=E.scrollLeft,me=E.scrollTop;E===u?(ne=K<V&&(U.overflowX==="auto"||U.overflowX==="scroll"||U.overflowX==="visible"),J=X<de&&(U.overflowY==="auto"||U.overflowY==="scroll"||U.overflowY==="visible")):(ne=K<V&&(U.overflowX==="auto"||U.overflowX==="scroll"),J=X<de&&(U.overflowY==="auto"||U.overflowY==="scroll"));var s=ne&&(Math.abs(B-i)<=l&&Z+K<V)-(Math.abs($-i)<=l&&!!Z),p=J&&(Math.abs(R-o)<=l&&me+X<de)-(Math.abs(P-o)<=l&&!!me);if(!Xe[b])for(var v=0;v<=b;v++)Xe[v]||(Xe[v]={});(Xe[b].vx!=s||Xe[b].vy!=p||Xe[b].el!==E)&&(Xe[b].el=E,Xe[b].vx=s,Xe[b].vy=p,clearInterval(Xe[b].pid),(s!=0||p!=0)&&(f=!0,Xe[b].pid=setInterval(function(){n&&this.layer===0&&se.active._onTouchMove(Ir);var d=Xe[this.layer].vy?Xe[this.layer].vy*h:0,N=Xe[this.layer].vx?Xe[this.layer].vx*h:0;typeof y=="function"&&y.call(se.dragged.parentNode[ut],N,d,t,Ir,Xe[this.layer].el)!=="continue"||Oo(Xe[this.layer].el,N,d)}.bind({layer:b}),24))),b++}while(e.bubbleScroll&&A!==u&&(A=sn(A,!1)));gi=f}},30),Mo=function(e){var r=e.originalEvent,n=e.putSortable,i=e.dragEl,o=e.activeSortable,l=e.dispatchSortableEvent,h=e.hideGhostForTarget,u=e.unhideGhostForTarget;if(r){var f=n||o;h();var y=r.changedTouches&&r.changedTouches.length?r.changedTouches[0]:r,b=document.elementFromPoint(y.clientX,y.clientY);u(),f&&!f.el.contains(b)&&(l("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function yi(){}yi.prototype={startIndex:null,dragStart:function(e){var r=e.oldDraggableIndex;this.startIndex=r},onSpill:function(e){var r=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=Nn(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(r,i):this.sortable.el.appendChild(r),this.sortable.animateAll(),n&&n.animateAll()},drop:Mo};$t(yi,{pluginName:"revertOnSpill"});function wi(){}wi.prototype={onSpill:function(e){var r=e.dragEl,n=e.putSortable,i=n||this.sortable;i.captureAnimationState(),r.parentNode&&r.parentNode.removeChild(r),i.animateAll()},drop:Mo};$t(wi,{pluginName:"removeOnSpill"});se.mount(new rs);se.mount(wi,yi);var xi=se;window.Sortable=xi;var Ro=t=>{t.directive("sortable",e=>{let r=parseInt(e.dataset?.sortableAnimationDuration);r!==0&&!r&&(r=300),e.sortable=xi.create(e,{group:e.getAttribute("x-sortable-group"),draggable:"[x-sortable-item]",handle:"[x-sortable-handle]",dataIdAttr:"x-sortable-item",animation:r,ghostClass:"fi-sortable-ghost"})})};var is=Object.create,Si=Object.defineProperty,os=Object.getPrototypeOf,as=Object.prototype.hasOwnProperty,ss=Object.getOwnPropertyNames,ls=Object.getOwnPropertyDescriptor,cs=t=>Si(t,"__esModule",{value:!0}),Io=(t,e)=>()=>(e||(e={exports:{}},t(e.exports,e)),e.exports),fs=(t,e,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of ss(e))!as.call(t,n)&&n!=="default"&&Si(t,n,{get:()=>e[n],enumerable:!(r=ls(e,n))||r.enumerable});return t},Fo=t=>fs(cs(Si(t!=null?is(os(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t),us=Io(t=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});function e(c){var a=c.getBoundingClientRect();return{width:a.width,height:a.height,top:a.top,right:a.right,bottom:a.bottom,left:a.left,x:a.left,y:a.top}}function r(c){if(c==null)return window;if(c.toString()!=="[object Window]"){var a=c.ownerDocument;return a&&a.defaultView||window}return c}function n(c){var a=r(c),g=a.pageXOffset,D=a.pageYOffset;return{scrollLeft:g,scrollTop:D}}function i(c){var a=r(c).Element;return c instanceof a||c instanceof Element}function o(c){var a=r(c).HTMLElement;return c instanceof a||c instanceof HTMLElement}function l(c){if(typeof ShadowRoot>"u")return!1;var a=r(c).ShadowRoot;return c instanceof a||c instanceof ShadowRoot}function h(c){return{scrollLeft:c.scrollLeft,scrollTop:c.scrollTop}}function u(c){return c===r(c)||!o(c)?n(c):h(c)}function f(c){return c?(c.nodeName||"").toLowerCase():null}function y(c){return((i(c)?c.ownerDocument:c.document)||window.document).documentElement}function b(c){return e(y(c)).left+n(c).scrollLeft}function A(c){return r(c).getComputedStyle(c)}function E(c){var a=A(c),g=a.overflow,D=a.overflowX,T=a.overflowY;return/auto|scroll|overlay|hidden/.test(g+T+D)}function O(c,a,g){g===void 0&&(g=!1);var D=y(a),T=e(c),F=o(a),W={scrollLeft:0,scrollTop:0},j={x:0,y:0};return(F||!F&&!g)&&((f(a)!=="body"||E(D))&&(W=u(a)),o(a)?(j=e(a),j.x+=a.clientLeft,j.y+=a.clientTop):D&&(j.x=b(D))),{x:T.left+W.scrollLeft-j.x,y:T.top+W.scrollTop-j.y,width:T.width,height:T.height}}function P(c){var a=e(c),g=c.offsetWidth,D=c.offsetHeight;return Math.abs(a.width-g)<=1&&(g=a.width),Math.abs(a.height-D)<=1&&(D=a.height),{x:c.offsetLeft,y:c.offsetTop,width:g,height:D}}function R(c){return f(c)==="html"?c:c.assignedSlot||c.parentNode||(l(c)?c.host:null)||y(c)}function $(c){return["html","body","#document"].indexOf(f(c))>=0?c.ownerDocument.body:o(c)&&E(c)?c:$(R(c))}function B(c,a){var g;a===void 0&&(a=[]);var D=$(c),T=D===((g=c.ownerDocument)==null?void 0:g.body),F=r(D),W=T?[F].concat(F.visualViewport||[],E(D)?D:[]):D,j=a.concat(W);return T?j:j.concat(B(R(W)))}function K(c){return["table","td","th"].indexOf(f(c))>=0}function X(c){return!o(c)||A(c).position==="fixed"?null:c.offsetParent}function ne(c){var a=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,g=navigator.userAgent.indexOf("Trident")!==-1;if(g&&o(c)){var D=A(c);if(D.position==="fixed")return null}for(var T=R(c);o(T)&&["html","body"].indexOf(f(T))<0;){var F=A(T);if(F.transform!=="none"||F.perspective!=="none"||F.contain==="paint"||["transform","perspective"].indexOf(F.willChange)!==-1||a&&F.willChange==="filter"||a&&F.filter&&F.filter!=="none")return T;T=T.parentNode}return null}function J(c){for(var a=r(c),g=X(c);g&&K(g)&&A(g).position==="static";)g=X(g);return g&&(f(g)==="html"||f(g)==="body"&&A(g).position==="static")?a:g||ne(c)||a}var V="top",de="bottom",U="right",Z="left",me="auto",s=[V,de,U,Z],p="start",v="end",d="clippingParents",N="viewport",_="popper",M="reference",Q=s.reduce(function(c,a){return c.concat([a+"-"+p,a+"-"+v])},[]),Ue=[].concat(s,[me]).reduce(function(c,a){return c.concat([a,a+"-"+p,a+"-"+v])},[]),Rt="beforeRead",Vt="read",Lr="afterRead",Nr="beforeMain",kr="main",zt="afterMain",nr="beforeWrite",jr="write",rr="afterWrite",It=[Rt,Vt,Lr,Nr,kr,zt,nr,jr,rr];function Br(c){var a=new Map,g=new Set,D=[];c.forEach(function(F){a.set(F.name,F)});function T(F){g.add(F.name);var W=[].concat(F.requires||[],F.requiresIfExists||[]);W.forEach(function(j){if(!g.has(j)){var q=a.get(j);q&&T(q)}}),D.push(F)}return c.forEach(function(F){g.has(F.name)||T(F)}),D}function mt(c){var a=Br(c);return It.reduce(function(g,D){return g.concat(a.filter(function(T){return T.phase===D}))},[])}function Ut(c){var a;return function(){return a||(a=new Promise(function(g){Promise.resolve().then(function(){a=void 0,g(c())})})),a}}function At(c){for(var a=arguments.length,g=new Array(a>1?a-1:0),D=1;D<a;D++)g[D-1]=arguments[D];return[].concat(g).reduce(function(T,F){return T.replace(/%s/,F)},c)}var Ct='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',Hr='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',Qe=["name","enabled","phase","fn","effect","requires","options"];function $r(c){c.forEach(function(a){Object.keys(a).forEach(function(g){switch(g){case"name":typeof a.name!="string"&&console.error(At(Ct,String(a.name),'"name"','"string"','"'+String(a.name)+'"'));break;case"enabled":typeof a.enabled!="boolean"&&console.error(At(Ct,a.name,'"enabled"','"boolean"','"'+String(a.enabled)+'"'));case"phase":It.indexOf(a.phase)<0&&console.error(At(Ct,a.name,'"phase"',"either "+It.join(", "),'"'+String(a.phase)+'"'));break;case"fn":typeof a.fn!="function"&&console.error(At(Ct,a.name,'"fn"','"function"','"'+String(a.fn)+'"'));break;case"effect":typeof a.effect!="function"&&console.error(At(Ct,a.name,'"effect"','"function"','"'+String(a.fn)+'"'));break;case"requires":Array.isArray(a.requires)||console.error(At(Ct,a.name,'"requires"','"array"','"'+String(a.requires)+'"'));break;case"requiresIfExists":Array.isArray(a.requiresIfExists)||console.error(At(Ct,a.name,'"requiresIfExists"','"array"','"'+String(a.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+a.name+'" modifier, valid properties are '+Qe.map(function(D){return'"'+D+'"'}).join(", ")+'; but "'+g+'" was provided.')}a.requires&&a.requires.forEach(function(D){c.find(function(T){return T.name===D})==null&&console.error(At(Hr,String(a.name),D,D))})})})}function Wr(c,a){var g=new Set;return c.filter(function(D){var T=a(D);if(!g.has(T))return g.add(T),!0})}function ot(c){return c.split("-")[0]}function Vr(c){var a=c.reduce(function(g,D){var T=g[D.name];return g[D.name]=T?Object.assign({},T,D,{options:Object.assign({},T.options,D.options),data:Object.assign({},T.data,D.data)}):D,g},{});return Object.keys(a).map(function(g){return a[g]})}function ir(c){var a=r(c),g=y(c),D=a.visualViewport,T=g.clientWidth,F=g.clientHeight,W=0,j=0;return D&&(T=D.width,F=D.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(W=D.offsetLeft,j=D.offsetTop)),{width:T,height:F,x:W+b(c),y:j}}var gt=Math.max,ln=Math.min,Yt=Math.round;function or(c){var a,g=y(c),D=n(c),T=(a=c.ownerDocument)==null?void 0:a.body,F=gt(g.scrollWidth,g.clientWidth,T?T.scrollWidth:0,T?T.clientWidth:0),W=gt(g.scrollHeight,g.clientHeight,T?T.scrollHeight:0,T?T.clientHeight:0),j=-D.scrollLeft+b(c),q=-D.scrollTop;return A(T||g).direction==="rtl"&&(j+=gt(g.clientWidth,T?T.clientWidth:0)-F),{width:F,height:W,x:j,y:q}}function kn(c,a){var g=a.getRootNode&&a.getRootNode();if(c.contains(a))return!0;if(g&&l(g)){var D=a;do{if(D&&c.isSameNode(D))return!0;D=D.parentNode||D.host}while(D)}return!1}function Xt(c){return Object.assign({},c,{left:c.x,top:c.y,right:c.x+c.width,bottom:c.y+c.height})}function ar(c){var a=e(c);return a.top=a.top+c.clientTop,a.left=a.left+c.clientLeft,a.bottom=a.top+c.clientHeight,a.right=a.left+c.clientWidth,a.width=c.clientWidth,a.height=c.clientHeight,a.x=a.left,a.y=a.top,a}function sr(c,a){return a===N?Xt(ir(c)):o(a)?ar(a):Xt(or(y(c)))}function yn(c){var a=B(R(c)),g=["absolute","fixed"].indexOf(A(c).position)>=0,D=g&&o(c)?J(c):c;return i(D)?a.filter(function(T){return i(T)&&kn(T,D)&&f(T)!=="body"}):[]}function wn(c,a,g){var D=a==="clippingParents"?yn(c):[].concat(a),T=[].concat(D,[g]),F=T[0],W=T.reduce(function(j,q){var oe=sr(c,q);return j.top=gt(oe.top,j.top),j.right=ln(oe.right,j.right),j.bottom=ln(oe.bottom,j.bottom),j.left=gt(oe.left,j.left),j},sr(c,F));return W.width=W.right-W.left,W.height=W.bottom-W.top,W.x=W.left,W.y=W.top,W}function cn(c){return c.split("-")[1]}function dt(c){return["top","bottom"].indexOf(c)>=0?"x":"y"}function lr(c){var a=c.reference,g=c.element,D=c.placement,T=D?ot(D):null,F=D?cn(D):null,W=a.x+a.width/2-g.width/2,j=a.y+a.height/2-g.height/2,q;switch(T){case V:q={x:W,y:a.y-g.height};break;case de:q={x:W,y:a.y+a.height};break;case U:q={x:a.x+a.width,y:j};break;case Z:q={x:a.x-g.width,y:j};break;default:q={x:a.x,y:a.y}}var oe=T?dt(T):null;if(oe!=null){var z=oe==="y"?"height":"width";switch(F){case p:q[oe]=q[oe]-(a[z]/2-g[z]/2);break;case v:q[oe]=q[oe]+(a[z]/2-g[z]/2);break}}return q}function cr(){return{top:0,right:0,bottom:0,left:0}}function fr(c){return Object.assign({},cr(),c)}function ur(c,a){return a.reduce(function(g,D){return g[D]=c,g},{})}function qt(c,a){a===void 0&&(a={});var g=a,D=g.placement,T=D===void 0?c.placement:D,F=g.boundary,W=F===void 0?d:F,j=g.rootBoundary,q=j===void 0?N:j,oe=g.elementContext,z=oe===void 0?_:oe,De=g.altBoundary,Le=De===void 0?!1:De,Ae=g.padding,xe=Ae===void 0?0:Ae,Me=fr(typeof xe!="number"?xe:ur(xe,s)),Ee=z===_?M:_,Be=c.elements.reference,Re=c.rects.popper,He=c.elements[Le?Ee:z],ce=wn(i(He)?He:He.contextElement||y(c.elements.popper),W,q),Pe=e(Be),Te=lr({reference:Pe,element:Re,strategy:"absolute",placement:T}),Ne=Xt(Object.assign({},Re,Te)),Fe=z===_?Ne:Pe,Ye={top:ce.top-Fe.top+Me.top,bottom:Fe.bottom-ce.bottom+Me.bottom,left:ce.left-Fe.left+Me.left,right:Fe.right-ce.right+Me.right},$e=c.modifiersData.offset;if(z===_&&$e){var Ve=$e[T];Object.keys(Ye).forEach(function(wt){var et=[U,de].indexOf(wt)>=0?1:-1,Lt=[V,de].indexOf(wt)>=0?"y":"x";Ye[wt]+=Ve[Lt]*et})}return Ye}var dr="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",zr="Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.",xn={placement:"bottom",modifiers:[],strategy:"absolute"};function fn(){for(var c=arguments.length,a=new Array(c),g=0;g<c;g++)a[g]=arguments[g];return!a.some(function(D){return!(D&&typeof D.getBoundingClientRect=="function")})}function En(c){c===void 0&&(c={});var a=c,g=a.defaultModifiers,D=g===void 0?[]:g,T=a.defaultOptions,F=T===void 0?xn:T;return function(j,q,oe){oe===void 0&&(oe=F);var z={placement:"bottom",orderedModifiers:[],options:Object.assign({},xn,F),modifiersData:{},elements:{reference:j,popper:q},attributes:{},styles:{}},De=[],Le=!1,Ae={state:z,setOptions:function(Be){Me(),z.options=Object.assign({},F,z.options,Be),z.scrollParents={reference:i(j)?B(j):j.contextElement?B(j.contextElement):[],popper:B(q)};var Re=mt(Vr([].concat(D,z.options.modifiers)));z.orderedModifiers=Re.filter(function($e){return $e.enabled});var He=Wr([].concat(Re,z.options.modifiers),function($e){var Ve=$e.name;return Ve});if($r(He),ot(z.options.placement)===me){var ce=z.orderedModifiers.find(function($e){var Ve=$e.name;return Ve==="flip"});ce||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" "))}var Pe=A(q),Te=Pe.marginTop,Ne=Pe.marginRight,Fe=Pe.marginBottom,Ye=Pe.marginLeft;return[Te,Ne,Fe,Ye].some(function($e){return parseFloat($e)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" ")),xe(),Ae.update()},forceUpdate:function(){if(!Le){var Be=z.elements,Re=Be.reference,He=Be.popper;if(!fn(Re,He)){console.error(dr);return}z.rects={reference:O(Re,J(He),z.options.strategy==="fixed"),popper:P(He)},z.reset=!1,z.placement=z.options.placement,z.orderedModifiers.forEach(function(Ve){return z.modifiersData[Ve.name]=Object.assign({},Ve.data)});for(var ce=0,Pe=0;Pe<z.orderedModifiers.length;Pe++){if(ce+=1,ce>100){console.error(zr);break}if(z.reset===!0){z.reset=!1,Pe=-1;continue}var Te=z.orderedModifiers[Pe],Ne=Te.fn,Fe=Te.options,Ye=Fe===void 0?{}:Fe,$e=Te.name;typeof Ne=="function"&&(z=Ne({state:z,options:Ye,name:$e,instance:Ae})||z)}}},update:Ut(function(){return new Promise(function(Ee){Ae.forceUpdate(),Ee(z)})}),destroy:function(){Me(),Le=!0}};if(!fn(j,q))return console.error(dr),Ae;Ae.setOptions(oe).then(function(Ee){!Le&&oe.onFirstUpdate&&oe.onFirstUpdate(Ee)});function xe(){z.orderedModifiers.forEach(function(Ee){var Be=Ee.name,Re=Ee.options,He=Re===void 0?{}:Re,ce=Ee.effect;if(typeof ce=="function"){var Pe=ce({state:z,name:Be,instance:Ae,options:He}),Te=function(){};De.push(Pe||Te)}})}function Me(){De.forEach(function(Ee){return Ee()}),De=[]}return Ae}}var On={passive:!0};function Ur(c){var a=c.state,g=c.instance,D=c.options,T=D.scroll,F=T===void 0?!0:T,W=D.resize,j=W===void 0?!0:W,q=r(a.elements.popper),oe=[].concat(a.scrollParents.reference,a.scrollParents.popper);return F&&oe.forEach(function(z){z.addEventListener("scroll",g.update,On)}),j&&q.addEventListener("resize",g.update,On),function(){F&&oe.forEach(function(z){z.removeEventListener("scroll",g.update,On)}),j&&q.removeEventListener("resize",g.update,On)}}var jn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ur,data:{}};function Yr(c){var a=c.state,g=c.name;a.modifiersData[g]=lr({reference:a.rects.reference,element:a.rects.popper,strategy:"absolute",placement:a.placement})}var Bn={name:"popperOffsets",enabled:!0,phase:"read",fn:Yr,data:{}},Xr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function qr(c){var a=c.x,g=c.y,D=window,T=D.devicePixelRatio||1;return{x:Yt(Yt(a*T)/T)||0,y:Yt(Yt(g*T)/T)||0}}function Hn(c){var a,g=c.popper,D=c.popperRect,T=c.placement,F=c.offsets,W=c.position,j=c.gpuAcceleration,q=c.adaptive,oe=c.roundOffsets,z=oe===!0?qr(F):typeof oe=="function"?oe(F):F,De=z.x,Le=De===void 0?0:De,Ae=z.y,xe=Ae===void 0?0:Ae,Me=F.hasOwnProperty("x"),Ee=F.hasOwnProperty("y"),Be=Z,Re=V,He=window;if(q){var ce=J(g),Pe="clientHeight",Te="clientWidth";ce===r(g)&&(ce=y(g),A(ce).position!=="static"&&(Pe="scrollHeight",Te="scrollWidth")),ce=ce,T===V&&(Re=de,xe-=ce[Pe]-D.height,xe*=j?1:-1),T===Z&&(Be=U,Le-=ce[Te]-D.width,Le*=j?1:-1)}var Ne=Object.assign({position:W},q&&Xr);if(j){var Fe;return Object.assign({},Ne,(Fe={},Fe[Re]=Ee?"0":"",Fe[Be]=Me?"0":"",Fe.transform=(He.devicePixelRatio||1)<2?"translate("+Le+"px, "+xe+"px)":"translate3d("+Le+"px, "+xe+"px, 0)",Fe))}return Object.assign({},Ne,(a={},a[Re]=Ee?xe+"px":"",a[Be]=Me?Le+"px":"",a.transform="",a))}function m(c){var a=c.state,g=c.options,D=g.gpuAcceleration,T=D===void 0?!0:D,F=g.adaptive,W=F===void 0?!0:F,j=g.roundOffsets,q=j===void 0?!0:j,oe=A(a.elements.popper).transitionProperty||"";W&&["transform","top","right","bottom","left"].some(function(De){return oe.indexOf(De)>=0})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',`

`,'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.",`

`,"We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "));var z={placement:ot(a.placement),popper:a.elements.popper,popperRect:a.rects.popper,gpuAcceleration:T};a.modifiersData.popperOffsets!=null&&(a.styles.popper=Object.assign({},a.styles.popper,Hn(Object.assign({},z,{offsets:a.modifiersData.popperOffsets,position:a.options.strategy,adaptive:W,roundOffsets:q})))),a.modifiersData.arrow!=null&&(a.styles.arrow=Object.assign({},a.styles.arrow,Hn(Object.assign({},z,{offsets:a.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:q})))),a.attributes.popper=Object.assign({},a.attributes.popper,{"data-popper-placement":a.placement})}var w={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:m,data:{}};function S(c){var a=c.state;Object.keys(a.elements).forEach(function(g){var D=a.styles[g]||{},T=a.attributes[g]||{},F=a.elements[g];!o(F)||!f(F)||(Object.assign(F.style,D),Object.keys(T).forEach(function(W){var j=T[W];j===!1?F.removeAttribute(W):F.setAttribute(W,j===!0?"":j)}))})}function I(c){var a=c.state,g={popper:{position:a.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(a.elements.popper.style,g.popper),a.styles=g,a.elements.arrow&&Object.assign(a.elements.arrow.style,g.arrow),function(){Object.keys(a.elements).forEach(function(D){var T=a.elements[D],F=a.attributes[D]||{},W=Object.keys(a.styles.hasOwnProperty(D)?a.styles[D]:g[D]),j=W.reduce(function(q,oe){return q[oe]="",q},{});!o(T)||!f(T)||(Object.assign(T.style,j),Object.keys(F).forEach(function(q){T.removeAttribute(q)}))})}}var Y={name:"applyStyles",enabled:!0,phase:"write",fn:S,effect:I,requires:["computeStyles"]};function H(c,a,g){var D=ot(c),T=[Z,V].indexOf(D)>=0?-1:1,F=typeof g=="function"?g(Object.assign({},a,{placement:c})):g,W=F[0],j=F[1];return W=W||0,j=(j||0)*T,[Z,U].indexOf(D)>=0?{x:j,y:W}:{x:W,y:j}}function k(c){var a=c.state,g=c.options,D=c.name,T=g.offset,F=T===void 0?[0,0]:T,W=Ue.reduce(function(z,De){return z[De]=H(De,a.rects,F),z},{}),j=W[a.placement],q=j.x,oe=j.y;a.modifiersData.popperOffsets!=null&&(a.modifiersData.popperOffsets.x+=q,a.modifiersData.popperOffsets.y+=oe),a.modifiersData[D]=W}var be={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:k},le={left:"right",right:"left",bottom:"top",top:"bottom"};function pe(c){return c.replace(/left|right|bottom|top/g,function(a){return le[a]})}var ye={start:"end",end:"start"};function _e(c){return c.replace(/start|end/g,function(a){return ye[a]})}function je(c,a){a===void 0&&(a={});var g=a,D=g.placement,T=g.boundary,F=g.rootBoundary,W=g.padding,j=g.flipVariations,q=g.allowedAutoPlacements,oe=q===void 0?Ue:q,z=cn(D),De=z?j?Q:Q.filter(function(xe){return cn(xe)===z}):s,Le=De.filter(function(xe){return oe.indexOf(xe)>=0});Le.length===0&&(Le=De,console.error(["Popper: The `allowedAutoPlacements` option did not allow any","placements. Ensure the `placement` option matches the variation","of the allowed placements.",'For example, "auto" cannot be used to allow "bottom-start".','Use "auto-start" instead.'].join(" ")));var Ae=Le.reduce(function(xe,Me){return xe[Me]=qt(c,{placement:Me,boundary:T,rootBoundary:F,padding:W})[ot(Me)],xe},{});return Object.keys(Ae).sort(function(xe,Me){return Ae[xe]-Ae[Me]})}function Se(c){if(ot(c)===me)return[];var a=pe(c);return[_e(c),a,_e(a)]}function Ie(c){var a=c.state,g=c.options,D=c.name;if(!a.modifiersData[D]._skip){for(var T=g.mainAxis,F=T===void 0?!0:T,W=g.altAxis,j=W===void 0?!0:W,q=g.fallbackPlacements,oe=g.padding,z=g.boundary,De=g.rootBoundary,Le=g.altBoundary,Ae=g.flipVariations,xe=Ae===void 0?!0:Ae,Me=g.allowedAutoPlacements,Ee=a.options.placement,Be=ot(Ee),Re=Be===Ee,He=q||(Re||!xe?[pe(Ee)]:Se(Ee)),ce=[Ee].concat(He).reduce(function(te,ge){return te.concat(ot(ge)===me?je(a,{placement:ge,boundary:z,rootBoundary:De,padding:oe,flipVariations:xe,allowedAutoPlacements:Me}):ge)},[]),Pe=a.rects.reference,Te=a.rects.popper,Ne=new Map,Fe=!0,Ye=ce[0],$e=0;$e<ce.length;$e++){var Ve=ce[$e],wt=ot(Ve),et=cn(Ve)===p,Lt=[V,de].indexOf(wt)>=0,dn=Lt?"width":"height",Zt=qt(a,{placement:Ve,boundary:z,rootBoundary:De,altBoundary:Le,padding:oe}),Nt=Lt?et?U:Z:et?de:V;Pe[dn]>Te[dn]&&(Nt=pe(Nt));var $n=pe(Nt),Qt=[];if(F&&Qt.push(Zt[wt]<=0),j&&Qt.push(Zt[Nt]<=0,Zt[$n]<=0),Qt.every(function(te){return te})){Ye=Ve,Fe=!1;break}Ne.set(Ve,Qt)}if(Fe)for(var Sn=xe?3:1,Wn=function(ge){var we=ce.find(function(Ke){var Je=Ne.get(Ke);if(Je)return Je.slice(0,ge).every(function(Dt){return Dt})});if(we)return Ye=we,"break"},C=Sn;C>0;C--){var G=Wn(C);if(G==="break")break}a.placement!==Ye&&(a.modifiersData[D]._skip=!0,a.placement=Ye,a.reset=!0)}}var re={name:"flip",enabled:!0,phase:"main",fn:Ie,requiresIfExists:["offset"],data:{_skip:!1}};function he(c){return c==="x"?"y":"x"}function ve(c,a,g){return gt(c,ln(a,g))}function ee(c){var a=c.state,g=c.options,D=c.name,T=g.mainAxis,F=T===void 0?!0:T,W=g.altAxis,j=W===void 0?!1:W,q=g.boundary,oe=g.rootBoundary,z=g.altBoundary,De=g.padding,Le=g.tether,Ae=Le===void 0?!0:Le,xe=g.tetherOffset,Me=xe===void 0?0:xe,Ee=qt(a,{boundary:q,rootBoundary:oe,padding:De,altBoundary:z}),Be=ot(a.placement),Re=cn(a.placement),He=!Re,ce=dt(Be),Pe=he(ce),Te=a.modifiersData.popperOffsets,Ne=a.rects.reference,Fe=a.rects.popper,Ye=typeof Me=="function"?Me(Object.assign({},a.rects,{placement:a.placement})):Me,$e={x:0,y:0};if(Te){if(F||j){var Ve=ce==="y"?V:Z,wt=ce==="y"?de:U,et=ce==="y"?"height":"width",Lt=Te[ce],dn=Te[ce]+Ee[Ve],Zt=Te[ce]-Ee[wt],Nt=Ae?-Fe[et]/2:0,$n=Re===p?Ne[et]:Fe[et],Qt=Re===p?-Fe[et]:-Ne[et],Sn=a.elements.arrow,Wn=Ae&&Sn?P(Sn):{width:0,height:0},C=a.modifiersData["arrow#persistent"]?a.modifiersData["arrow#persistent"].padding:cr(),G=C[Ve],te=C[wt],ge=ve(0,Ne[et],Wn[et]),we=He?Ne[et]/2-Nt-ge-G-Ye:$n-ge-G-Ye,Ke=He?-Ne[et]/2+Nt+ge+te+Ye:Qt+ge+te+Ye,Je=a.elements.arrow&&J(a.elements.arrow),Dt=Je?ce==="y"?Je.clientTop||0:Je.clientLeft||0:0,Vn=a.modifiersData.offset?a.modifiersData.offset[a.placement][ce]:0,Tt=Te[ce]+we-Vn-Dt,An=Te[ce]+Ke-Vn;if(F){var pn=ve(Ae?ln(dn,Tt):dn,Lt,Ae?gt(Zt,An):Zt);Te[ce]=pn,$e[ce]=pn-Lt}if(j){var en=ce==="x"?V:Z,Gr=ce==="x"?de:U,tn=Te[Pe],hn=tn+Ee[en],Ai=tn-Ee[Gr],Ci=ve(Ae?ln(hn,Tt):hn,tn,Ae?gt(Ai,An):Ai);Te[Pe]=Ci,$e[Pe]=Ci-tn}}a.modifiersData[D]=$e}}var ie={name:"preventOverflow",enabled:!0,phase:"main",fn:ee,requiresIfExists:["offset"]},x=function(a,g){return a=typeof a=="function"?a(Object.assign({},g.rects,{placement:g.placement})):a,fr(typeof a!="number"?a:ur(a,s))};function Ge(c){var a,g=c.state,D=c.name,T=c.options,F=g.elements.arrow,W=g.modifiersData.popperOffsets,j=ot(g.placement),q=dt(j),oe=[Z,U].indexOf(j)>=0,z=oe?"height":"width";if(!(!F||!W)){var De=x(T.padding,g),Le=P(F),Ae=q==="y"?V:Z,xe=q==="y"?de:U,Me=g.rects.reference[z]+g.rects.reference[q]-W[q]-g.rects.popper[z],Ee=W[q]-g.rects.reference[q],Be=J(F),Re=Be?q==="y"?Be.clientHeight||0:Be.clientWidth||0:0,He=Me/2-Ee/2,ce=De[Ae],Pe=Re-Le[z]-De[xe],Te=Re/2-Le[z]/2+He,Ne=ve(ce,Te,Pe),Fe=q;g.modifiersData[D]=(a={},a[Fe]=Ne,a.centerOffset=Ne-Te,a)}}function fe(c){var a=c.state,g=c.options,D=g.element,T=D===void 0?"[data-popper-arrow]":D;if(T!=null&&!(typeof T=="string"&&(T=a.elements.popper.querySelector(T),!T))){if(o(T)||console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).',"To use an SVG arrow, wrap it in an HTMLElement that will be used as","the arrow."].join(" ")),!kn(a.elements.popper,T)){console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper',"element."].join(" "));return}a.elements.arrow=T}}var Ft={name:"arrow",enabled:!0,phase:"main",fn:Ge,effect:fe,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function bt(c,a,g){return g===void 0&&(g={x:0,y:0}),{top:c.top-a.height-g.y,right:c.right-a.width+g.x,bottom:c.bottom-a.height+g.y,left:c.left-a.width-g.x}}function Gt(c){return[V,U,de,Z].some(function(a){return c[a]>=0})}function Kt(c){var a=c.state,g=c.name,D=a.rects.reference,T=a.rects.popper,F=a.modifiersData.preventOverflow,W=qt(a,{elementContext:"reference"}),j=qt(a,{altBoundary:!0}),q=bt(W,D),oe=bt(j,T,F),z=Gt(q),De=Gt(oe);a.modifiersData[g]={referenceClippingOffsets:q,popperEscapeOffsets:oe,isReferenceHidden:z,hasPopperEscaped:De},a.attributes.popper=Object.assign({},a.attributes.popper,{"data-popper-reference-hidden":z,"data-popper-escaped":De})}var Jt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Kt},rt=[jn,Bn,w,Y],st=En({defaultModifiers:rt}),yt=[jn,Bn,w,Y,be,re,ie,Ft,Jt],un=En({defaultModifiers:yt});t.applyStyles=Y,t.arrow=Ft,t.computeStyles=w,t.createPopper=un,t.createPopperLite=st,t.defaultModifiers=yt,t.detectOverflow=qt,t.eventListeners=jn,t.flip=re,t.hide=Jt,t.offset=be,t.popperGenerator=En,t.popperOffsets=Bn,t.preventOverflow=ie}),Lo=Io(t=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var e=us(),r='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',n="tippy-box",i="tippy-content",o="tippy-backdrop",l="tippy-arrow",h="tippy-svg-arrow",u={passive:!0,capture:!0};function f(m,w){return{}.hasOwnProperty.call(m,w)}function y(m,w,S){if(Array.isArray(m)){var I=m[w];return I??(Array.isArray(S)?S[w]:S)}return m}function b(m,w){var S={}.toString.call(m);return S.indexOf("[object")===0&&S.indexOf(w+"]")>-1}function A(m,w){return typeof m=="function"?m.apply(void 0,w):m}function E(m,w){if(w===0)return m;var S;return function(I){clearTimeout(S),S=setTimeout(function(){m(I)},w)}}function O(m,w){var S=Object.assign({},m);return w.forEach(function(I){delete S[I]}),S}function P(m){return m.split(/\s+/).filter(Boolean)}function R(m){return[].concat(m)}function $(m,w){m.indexOf(w)===-1&&m.push(w)}function B(m){return m.filter(function(w,S){return m.indexOf(w)===S})}function K(m){return m.split("-")[0]}function X(m){return[].slice.call(m)}function ne(m){return Object.keys(m).reduce(function(w,S){return m[S]!==void 0&&(w[S]=m[S]),w},{})}function J(){return document.createElement("div")}function V(m){return["Element","Fragment"].some(function(w){return b(m,w)})}function de(m){return b(m,"NodeList")}function U(m){return b(m,"MouseEvent")}function Z(m){return!!(m&&m._tippy&&m._tippy.reference===m)}function me(m){return V(m)?[m]:de(m)?X(m):Array.isArray(m)?m:X(document.querySelectorAll(m))}function s(m,w){m.forEach(function(S){S&&(S.style.transitionDuration=w+"ms")})}function p(m,w){m.forEach(function(S){S&&S.setAttribute("data-state",w)})}function v(m){var w,S=R(m),I=S[0];return!(I==null||(w=I.ownerDocument)==null)&&w.body?I.ownerDocument:document}function d(m,w){var S=w.clientX,I=w.clientY;return m.every(function(Y){var H=Y.popperRect,k=Y.popperState,be=Y.props,le=be.interactiveBorder,pe=K(k.placement),ye=k.modifiersData.offset;if(!ye)return!0;var _e=pe==="bottom"?ye.top.y:0,je=pe==="top"?ye.bottom.y:0,Se=pe==="right"?ye.left.x:0,Ie=pe==="left"?ye.right.x:0,re=H.top-I+_e>le,he=I-H.bottom-je>le,ve=H.left-S+Se>le,ee=S-H.right-Ie>le;return re||he||ve||ee})}function N(m,w,S){var I=w+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(Y){m[I](Y,S)})}var _={isTouch:!1},M=0;function Q(){_.isTouch||(_.isTouch=!0,window.performance&&document.addEventListener("mousemove",Ue))}function Ue(){var m=performance.now();m-M<20&&(_.isTouch=!1,document.removeEventListener("mousemove",Ue)),M=m}function Rt(){var m=document.activeElement;if(Z(m)){var w=m._tippy;m.blur&&!w.state.isVisible&&m.blur()}}function Vt(){document.addEventListener("touchstart",Q,u),window.addEventListener("blur",Rt)}var Lr=typeof window<"u"&&typeof document<"u",Nr=Lr?navigator.userAgent:"",kr=/MSIE |Trident\//.test(Nr);function zt(m){var w=m==="destroy"?"n already-":" ";return[m+"() was called on a"+w+"destroyed instance. This is a no-op but","indicates a potential memory leak."].join(" ")}function nr(m){var w=/[ \t]{2,}/g,S=/^[ \t]*/gm;return m.replace(w," ").replace(S,"").trim()}function jr(m){return nr(`
  %ctippy.js

  %c`+nr(m)+`

  %c\u{1F477}\u200D This is a development-only message. It will be removed in production.
  `)}function rr(m){return[jr(m),"color: #00C584; font-size: 1.3em; font-weight: bold;","line-height: 1.5","color: #a6a095;"]}var It;Br();function Br(){It=new Set}function mt(m,w){if(m&&!It.has(w)){var S;It.add(w),(S=console).warn.apply(S,rr(w))}}function Ut(m,w){if(m&&!It.has(w)){var S;It.add(w),(S=console).error.apply(S,rr(w))}}function At(m){var w=!m,S=Object.prototype.toString.call(m)==="[object Object]"&&!m.addEventListener;Ut(w,["tippy() was passed","`"+String(m)+"`","as its targets (first) argument. Valid types are: String, Element,","Element[], or NodeList."].join(" ")),Ut(S,["tippy() was passed a plain object which is not supported as an argument","for virtual positioning. Use props.getReferenceClientRect instead."].join(" "))}var Ct={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Hr={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Qe=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Ct,{},Hr),$r=Object.keys(Qe),Wr=function(w){gt(w,[]);var S=Object.keys(w);S.forEach(function(I){Qe[I]=w[I]})};function ot(m){var w=m.plugins||[],S=w.reduce(function(I,Y){var H=Y.name,k=Y.defaultValue;return H&&(I[H]=m[H]!==void 0?m[H]:k),I},{});return Object.assign({},m,{},S)}function Vr(m,w){var S=w?Object.keys(ot(Object.assign({},Qe,{plugins:w}))):$r,I=S.reduce(function(Y,H){var k=(m.getAttribute("data-tippy-"+H)||"").trim();if(!k)return Y;if(H==="content")Y[H]=k;else try{Y[H]=JSON.parse(k)}catch{Y[H]=k}return Y},{});return I}function ir(m,w){var S=Object.assign({},w,{content:A(w.content,[m])},w.ignoreAttributes?{}:Vr(m,w.plugins));return S.aria=Object.assign({},Qe.aria,{},S.aria),S.aria={expanded:S.aria.expanded==="auto"?w.interactive:S.aria.expanded,content:S.aria.content==="auto"?w.interactive?null:"describedby":S.aria.content},S}function gt(m,w){m===void 0&&(m={}),w===void 0&&(w=[]);var S=Object.keys(m);S.forEach(function(I){var Y=O(Qe,Object.keys(Ct)),H=!f(Y,I);H&&(H=w.filter(function(k){return k.name===I}).length===0),mt(H,["`"+I+"`","is not a valid prop. You may have spelled it incorrectly, or if it's","a plugin, forgot to pass it in an array as props.plugins.",`

`,`All props: https://atomiks.github.io/tippyjs/v6/all-props/
`,"Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "))})}var ln=function(){return"innerHTML"};function Yt(m,w){m[ln()]=w}function or(m){var w=J();return m===!0?w.className=l:(w.className=h,V(m)?w.appendChild(m):Yt(w,m)),w}function kn(m,w){V(w.content)?(Yt(m,""),m.appendChild(w.content)):typeof w.content!="function"&&(w.allowHTML?Yt(m,w.content):m.textContent=w.content)}function Xt(m){var w=m.firstElementChild,S=X(w.children);return{box:w,content:S.find(function(I){return I.classList.contains(i)}),arrow:S.find(function(I){return I.classList.contains(l)||I.classList.contains(h)}),backdrop:S.find(function(I){return I.classList.contains(o)})}}function ar(m){var w=J(),S=J();S.className=n,S.setAttribute("data-state","hidden"),S.setAttribute("tabindex","-1");var I=J();I.className=i,I.setAttribute("data-state","hidden"),kn(I,m.props),w.appendChild(S),S.appendChild(I),Y(m.props,m.props);function Y(H,k){var be=Xt(w),le=be.box,pe=be.content,ye=be.arrow;k.theme?le.setAttribute("data-theme",k.theme):le.removeAttribute("data-theme"),typeof k.animation=="string"?le.setAttribute("data-animation",k.animation):le.removeAttribute("data-animation"),k.inertia?le.setAttribute("data-inertia",""):le.removeAttribute("data-inertia"),le.style.maxWidth=typeof k.maxWidth=="number"?k.maxWidth+"px":k.maxWidth,k.role?le.setAttribute("role",k.role):le.removeAttribute("role"),(H.content!==k.content||H.allowHTML!==k.allowHTML)&&kn(pe,m.props),k.arrow?ye?H.arrow!==k.arrow&&(le.removeChild(ye),le.appendChild(or(k.arrow))):le.appendChild(or(k.arrow)):ye&&le.removeChild(ye)}return{popper:w,onUpdate:Y}}ar.$$tippy=!0;var sr=1,yn=[],wn=[];function cn(m,w){var S=ir(m,Object.assign({},Qe,{},ot(ne(w)))),I,Y,H,k=!1,be=!1,le=!1,pe=!1,ye,_e,je,Se=[],Ie=E(Re,S.interactiveDebounce),re,he=sr++,ve=null,ee=B(S.plugins),ie={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},x={id:he,reference:m,popper:J(),popperInstance:ve,props:S,state:ie,plugins:ee,clearDelayTimeouts:Lt,setProps:dn,setContent:Zt,show:Nt,hide:$n,hideWithInteractivity:Qt,enable:wt,disable:et,unmount:Sn,destroy:Wn};if(!S.render)return Ut(!0,"render() function has not been supplied."),x;var Ge=S.render(x),fe=Ge.popper,Ft=Ge.onUpdate;fe.setAttribute("data-tippy-root",""),fe.id="tippy-"+x.id,x.popper=fe,m._tippy=x,fe._tippy=x;var bt=ee.map(function(C){return C.fn(x)}),Gt=m.hasAttribute("aria-expanded");return Me(),T(),a(),g("onCreate",[x]),S.showOnCreate&&$e(),fe.addEventListener("mouseenter",function(){x.props.interactive&&x.state.isVisible&&x.clearDelayTimeouts()}),fe.addEventListener("mouseleave",function(C){x.props.interactive&&x.props.trigger.indexOf("mouseenter")>=0&&(yt().addEventListener("mousemove",Ie),Ie(C))}),x;function Kt(){var C=x.props.touch;return Array.isArray(C)?C:[C,0]}function Jt(){return Kt()[0]==="hold"}function rt(){var C;return!!((C=x.props.render)!=null&&C.$$tippy)}function st(){return re||m}function yt(){var C=st().parentNode;return C?v(C):document}function un(){return Xt(fe)}function c(C){return x.state.isMounted&&!x.state.isVisible||_.isTouch||ye&&ye.type==="focus"?0:y(x.props.delay,C?0:1,Qe.delay)}function a(){fe.style.pointerEvents=x.props.interactive&&x.state.isVisible?"":"none",fe.style.zIndex=""+x.props.zIndex}function g(C,G,te){if(te===void 0&&(te=!0),bt.forEach(function(we){we[C]&&we[C].apply(void 0,G)}),te){var ge;(ge=x.props)[C].apply(ge,G)}}function D(){var C=x.props.aria;if(C.content){var G="aria-"+C.content,te=fe.id,ge=R(x.props.triggerTarget||m);ge.forEach(function(we){var Ke=we.getAttribute(G);if(x.state.isVisible)we.setAttribute(G,Ke?Ke+" "+te:te);else{var Je=Ke&&Ke.replace(te,"").trim();Je?we.setAttribute(G,Je):we.removeAttribute(G)}})}}function T(){if(!(Gt||!x.props.aria.expanded)){var C=R(x.props.triggerTarget||m);C.forEach(function(G){x.props.interactive?G.setAttribute("aria-expanded",x.state.isVisible&&G===st()?"true":"false"):G.removeAttribute("aria-expanded")})}}function F(){yt().removeEventListener("mousemove",Ie),yn=yn.filter(function(C){return C!==Ie})}function W(C){if(!(_.isTouch&&(le||C.type==="mousedown"))&&!(x.props.interactive&&fe.contains(C.target))){if(st().contains(C.target)){if(_.isTouch||x.state.isVisible&&x.props.trigger.indexOf("click")>=0)return}else g("onClickOutside",[x,C]);x.props.hideOnClick===!0&&(x.clearDelayTimeouts(),x.hide(),be=!0,setTimeout(function(){be=!1}),x.state.isMounted||z())}}function j(){le=!0}function q(){le=!1}function oe(){var C=yt();C.addEventListener("mousedown",W,!0),C.addEventListener("touchend",W,u),C.addEventListener("touchstart",q,u),C.addEventListener("touchmove",j,u)}function z(){var C=yt();C.removeEventListener("mousedown",W,!0),C.removeEventListener("touchend",W,u),C.removeEventListener("touchstart",q,u),C.removeEventListener("touchmove",j,u)}function De(C,G){Ae(C,function(){!x.state.isVisible&&fe.parentNode&&fe.parentNode.contains(fe)&&G()})}function Le(C,G){Ae(C,G)}function Ae(C,G){var te=un().box;function ge(we){we.target===te&&(N(te,"remove",ge),G())}if(C===0)return G();N(te,"remove",_e),N(te,"add",ge),_e=ge}function xe(C,G,te){te===void 0&&(te=!1);var ge=R(x.props.triggerTarget||m);ge.forEach(function(we){we.addEventListener(C,G,te),Se.push({node:we,eventType:C,handler:G,options:te})})}function Me(){Jt()&&(xe("touchstart",Be,{passive:!0}),xe("touchend",He,{passive:!0})),P(x.props.trigger).forEach(function(C){if(C!=="manual")switch(xe(C,Be),C){case"mouseenter":xe("mouseleave",He);break;case"focus":xe(kr?"focusout":"blur",ce);break;case"focusin":xe("focusout",ce);break}})}function Ee(){Se.forEach(function(C){var G=C.node,te=C.eventType,ge=C.handler,we=C.options;G.removeEventListener(te,ge,we)}),Se=[]}function Be(C){var G,te=!1;if(!(!x.state.isEnabled||Pe(C)||be)){var ge=((G=ye)==null?void 0:G.type)==="focus";ye=C,re=C.currentTarget,T(),!x.state.isVisible&&U(C)&&yn.forEach(function(we){return we(C)}),C.type==="click"&&(x.props.trigger.indexOf("mouseenter")<0||k)&&x.props.hideOnClick!==!1&&x.state.isVisible?te=!0:$e(C),C.type==="click"&&(k=!te),te&&!ge&&Ve(C)}}function Re(C){var G=C.target,te=st().contains(G)||fe.contains(G);if(!(C.type==="mousemove"&&te)){var ge=Ye().concat(fe).map(function(we){var Ke,Je=we._tippy,Dt=(Ke=Je.popperInstance)==null?void 0:Ke.state;return Dt?{popperRect:we.getBoundingClientRect(),popperState:Dt,props:S}:null}).filter(Boolean);d(ge,C)&&(F(),Ve(C))}}function He(C){var G=Pe(C)||x.props.trigger.indexOf("click")>=0&&k;if(!G){if(x.props.interactive){x.hideWithInteractivity(C);return}Ve(C)}}function ce(C){x.props.trigger.indexOf("focusin")<0&&C.target!==st()||x.props.interactive&&C.relatedTarget&&fe.contains(C.relatedTarget)||Ve(C)}function Pe(C){return _.isTouch?Jt()!==C.type.indexOf("touch")>=0:!1}function Te(){Ne();var C=x.props,G=C.popperOptions,te=C.placement,ge=C.offset,we=C.getReferenceClientRect,Ke=C.moveTransition,Je=rt()?Xt(fe).arrow:null,Dt=we?{getBoundingClientRect:we,contextElement:we.contextElement||st()}:m,Vn={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(pn){var en=pn.state;if(rt()){var Gr=un(),tn=Gr.box;["placement","reference-hidden","escaped"].forEach(function(hn){hn==="placement"?tn.setAttribute("data-placement",en.placement):en.attributes.popper["data-popper-"+hn]?tn.setAttribute("data-"+hn,""):tn.removeAttribute("data-"+hn)}),en.attributes.popper={}}}},Tt=[{name:"offset",options:{offset:ge}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!Ke}},Vn];rt()&&Je&&Tt.push({name:"arrow",options:{element:Je,padding:3}}),Tt.push.apply(Tt,G?.modifiers||[]),x.popperInstance=e.createPopper(Dt,fe,Object.assign({},G,{placement:te,onFirstUpdate:je,modifiers:Tt}))}function Ne(){x.popperInstance&&(x.popperInstance.destroy(),x.popperInstance=null)}function Fe(){var C=x.props.appendTo,G,te=st();x.props.interactive&&C===Qe.appendTo||C==="parent"?G=te.parentNode:G=A(C,[te]),G.contains(fe)||G.appendChild(fe),Te(),mt(x.props.interactive&&C===Qe.appendTo&&te.nextElementSibling!==fe,["Interactive tippy element may not be accessible via keyboard","navigation because it is not directly after the reference element","in the DOM source order.",`

`,"Using a wrapper <div> or <span> tag around the reference element","solves this by creating a new parentNode context.",`

`,"Specifying `appendTo: document.body` silences this warning, but it","assumes you are using a focus management solution to handle","keyboard navigation.",`

`,"See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" "))}function Ye(){return X(fe.querySelectorAll("[data-tippy-root]"))}function $e(C){x.clearDelayTimeouts(),C&&g("onTrigger",[x,C]),oe();var G=c(!0),te=Kt(),ge=te[0],we=te[1];_.isTouch&&ge==="hold"&&we&&(G=we),G?I=setTimeout(function(){x.show()},G):x.show()}function Ve(C){if(x.clearDelayTimeouts(),g("onUntrigger",[x,C]),!x.state.isVisible){z();return}if(!(x.props.trigger.indexOf("mouseenter")>=0&&x.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(C.type)>=0&&k)){var G=c(!1);G?Y=setTimeout(function(){x.state.isVisible&&x.hide()},G):H=requestAnimationFrame(function(){x.hide()})}}function wt(){x.state.isEnabled=!0}function et(){x.hide(),x.state.isEnabled=!1}function Lt(){clearTimeout(I),clearTimeout(Y),cancelAnimationFrame(H)}function dn(C){if(mt(x.state.isDestroyed,zt("setProps")),!x.state.isDestroyed){g("onBeforeUpdate",[x,C]),Ee();var G=x.props,te=ir(m,Object.assign({},x.props,{},C,{ignoreAttributes:!0}));x.props=te,Me(),G.interactiveDebounce!==te.interactiveDebounce&&(F(),Ie=E(Re,te.interactiveDebounce)),G.triggerTarget&&!te.triggerTarget?R(G.triggerTarget).forEach(function(ge){ge.removeAttribute("aria-expanded")}):te.triggerTarget&&m.removeAttribute("aria-expanded"),T(),a(),Ft&&Ft(G,te),x.popperInstance&&(Te(),Ye().forEach(function(ge){requestAnimationFrame(ge._tippy.popperInstance.forceUpdate)})),g("onAfterUpdate",[x,C])}}function Zt(C){x.setProps({content:C})}function Nt(){mt(x.state.isDestroyed,zt("show"));var C=x.state.isVisible,G=x.state.isDestroyed,te=!x.state.isEnabled,ge=_.isTouch&&!x.props.touch,we=y(x.props.duration,0,Qe.duration);if(!(C||G||te||ge)&&!st().hasAttribute("disabled")&&(g("onShow",[x],!1),x.props.onShow(x)!==!1)){if(x.state.isVisible=!0,rt()&&(fe.style.visibility="visible"),a(),oe(),x.state.isMounted||(fe.style.transition="none"),rt()){var Ke=un(),Je=Ke.box,Dt=Ke.content;s([Je,Dt],0)}je=function(){var Tt;if(!(!x.state.isVisible||pe)){if(pe=!0,fe.offsetHeight,fe.style.transition=x.props.moveTransition,rt()&&x.props.animation){var An=un(),pn=An.box,en=An.content;s([pn,en],we),p([pn,en],"visible")}D(),T(),$(wn,x),(Tt=x.popperInstance)==null||Tt.forceUpdate(),x.state.isMounted=!0,g("onMount",[x]),x.props.animation&&rt()&&Le(we,function(){x.state.isShown=!0,g("onShown",[x])})}},Fe()}}function $n(){mt(x.state.isDestroyed,zt("hide"));var C=!x.state.isVisible,G=x.state.isDestroyed,te=!x.state.isEnabled,ge=y(x.props.duration,1,Qe.duration);if(!(C||G||te)&&(g("onHide",[x],!1),x.props.onHide(x)!==!1)){if(x.state.isVisible=!1,x.state.isShown=!1,pe=!1,k=!1,rt()&&(fe.style.visibility="hidden"),F(),z(),a(),rt()){var we=un(),Ke=we.box,Je=we.content;x.props.animation&&(s([Ke,Je],ge),p([Ke,Je],"hidden"))}D(),T(),x.props.animation?rt()&&De(ge,x.unmount):x.unmount()}}function Qt(C){mt(x.state.isDestroyed,zt("hideWithInteractivity")),yt().addEventListener("mousemove",Ie),$(yn,Ie),Ie(C)}function Sn(){mt(x.state.isDestroyed,zt("unmount")),x.state.isVisible&&x.hide(),x.state.isMounted&&(Ne(),Ye().forEach(function(C){C._tippy.unmount()}),fe.parentNode&&fe.parentNode.removeChild(fe),wn=wn.filter(function(C){return C!==x}),x.state.isMounted=!1,g("onHidden",[x]))}function Wn(){mt(x.state.isDestroyed,zt("destroy")),!x.state.isDestroyed&&(x.clearDelayTimeouts(),x.unmount(),Ee(),delete m._tippy,x.state.isDestroyed=!0,g("onDestroy",[x]))}}function dt(m,w){w===void 0&&(w={});var S=Qe.plugins.concat(w.plugins||[]);At(m),gt(w,S),Vt();var I=Object.assign({},w,{plugins:S}),Y=me(m),H=V(I.content),k=Y.length>1;mt(H&&k,["tippy() was passed an Element as the `content` prop, but more than","one tippy instance was created by this invocation. This means the","content element will only be appended to the last tippy instance.",`

`,"Instead, pass the .innerHTML of the element, or use a function that","returns a cloned version of the element instead.",`

`,`1) content: element.innerHTML
`,"2) content: () => element.cloneNode(true)"].join(" "));var be=Y.reduce(function(le,pe){var ye=pe&&cn(pe,I);return ye&&le.push(ye),le},[]);return V(m)?be[0]:be}dt.defaultProps=Qe,dt.setDefaultProps=Wr,dt.currentInput=_;var lr=function(w){var S=w===void 0?{}:w,I=S.exclude,Y=S.duration;wn.forEach(function(H){var k=!1;if(I&&(k=Z(I)?H.reference===I:H.popper===I.popper),!k){var be=H.props.duration;H.setProps({duration:Y}),H.hide(),H.state.isDestroyed||H.setProps({duration:be})}})},cr=Object.assign({},e.applyStyles,{effect:function(w){var S=w.state,I={popper:{position:S.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(S.elements.popper.style,I.popper),S.styles=I,S.elements.arrow&&Object.assign(S.elements.arrow.style,I.arrow)}}),fr=function(w,S){var I;S===void 0&&(S={}),Ut(!Array.isArray(w),["The first argument passed to createSingleton() must be an array of","tippy instances. The passed value was",String(w)].join(" "));var Y=w,H=[],k,be=S.overrides,le=[],pe=!1;function ye(){H=Y.map(function(ee){return ee.reference})}function _e(ee){Y.forEach(function(ie){ee?ie.enable():ie.disable()})}function je(ee){return Y.map(function(ie){var x=ie.setProps;return ie.setProps=function(Ge){x(Ge),ie.reference===k&&ee.setProps(Ge)},function(){ie.setProps=x}})}function Se(ee,ie){var x=H.indexOf(ie);if(ie!==k){k=ie;var Ge=(be||[]).concat("content").reduce(function(fe,Ft){return fe[Ft]=Y[x].props[Ft],fe},{});ee.setProps(Object.assign({},Ge,{getReferenceClientRect:typeof Ge.getReferenceClientRect=="function"?Ge.getReferenceClientRect:function(){return ie.getBoundingClientRect()}}))}}_e(!1),ye();var Ie={fn:function(){return{onDestroy:function(){_e(!0)},onHidden:function(){k=null},onClickOutside:function(x){x.props.showOnCreate&&!pe&&(pe=!0,k=null)},onShow:function(x){x.props.showOnCreate&&!pe&&(pe=!0,Se(x,H[0]))},onTrigger:function(x,Ge){Se(x,Ge.currentTarget)}}}},re=dt(J(),Object.assign({},O(S,["overrides"]),{plugins:[Ie].concat(S.plugins||[]),triggerTarget:H,popperOptions:Object.assign({},S.popperOptions,{modifiers:[].concat(((I=S.popperOptions)==null?void 0:I.modifiers)||[],[cr])})})),he=re.show;re.show=function(ee){if(he(),!k&&ee==null)return Se(re,H[0]);if(!(k&&ee==null)){if(typeof ee=="number")return H[ee]&&Se(re,H[ee]);if(Y.includes(ee)){var ie=ee.reference;return Se(re,ie)}if(H.includes(ee))return Se(re,ee)}},re.showNext=function(){var ee=H[0];if(!k)return re.show(0);var ie=H.indexOf(k);re.show(H[ie+1]||ee)},re.showPrevious=function(){var ee=H[H.length-1];if(!k)return re.show(ee);var ie=H.indexOf(k),x=H[ie-1]||ee;re.show(x)};var ve=re.setProps;return re.setProps=function(ee){be=ee.overrides||be,ve(ee)},re.setInstances=function(ee){_e(!0),le.forEach(function(ie){return ie()}),Y=ee,_e(!1),ye(),je(re),re.setProps({triggerTarget:H})},le=je(re),re},ur={mouseover:"mouseenter",focusin:"focus",click:"click"};function qt(m,w){Ut(!(w&&w.target),["You must specity a `target` prop indicating a CSS selector string matching","the target elements that should receive a tippy."].join(" "));var S=[],I=[],Y=!1,H=w.target,k=O(w,["target"]),be=Object.assign({},k,{trigger:"manual",touch:!1}),le=Object.assign({},k,{showOnCreate:!0}),pe=dt(m,be),ye=R(pe);function _e(he){if(!(!he.target||Y)){var ve=he.target.closest(H);if(ve){var ee=ve.getAttribute("data-tippy-trigger")||w.trigger||Qe.trigger;if(!ve._tippy&&!(he.type==="touchstart"&&typeof le.touch=="boolean")&&!(he.type!=="touchstart"&&ee.indexOf(ur[he.type])<0)){var ie=dt(ve,le);ie&&(I=I.concat(ie))}}}}function je(he,ve,ee,ie){ie===void 0&&(ie=!1),he.addEventListener(ve,ee,ie),S.push({node:he,eventType:ve,handler:ee,options:ie})}function Se(he){var ve=he.reference;je(ve,"touchstart",_e,u),je(ve,"mouseover",_e),je(ve,"focusin",_e),je(ve,"click",_e)}function Ie(){S.forEach(function(he){var ve=he.node,ee=he.eventType,ie=he.handler,x=he.options;ve.removeEventListener(ee,ie,x)}),S=[]}function re(he){var ve=he.destroy,ee=he.enable,ie=he.disable;he.destroy=function(x){x===void 0&&(x=!0),x&&I.forEach(function(Ge){Ge.destroy()}),I=[],Ie(),ve()},he.enable=function(){ee(),I.forEach(function(x){return x.enable()}),Y=!1},he.disable=function(){ie(),I.forEach(function(x){return x.disable()}),Y=!0},Se(he)}return ye.forEach(re),pe}var dr={name:"animateFill",defaultValue:!1,fn:function(w){var S;if(!((S=w.props.render)!=null&&S.$$tippy))return Ut(w.props.animateFill,"The `animateFill` plugin requires the default render function."),{};var I=Xt(w.popper),Y=I.box,H=I.content,k=w.props.animateFill?zr():null;return{onCreate:function(){k&&(Y.insertBefore(k,Y.firstElementChild),Y.setAttribute("data-animatefill",""),Y.style.overflow="hidden",w.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(k){var le=Y.style.transitionDuration,pe=Number(le.replace("ms",""));H.style.transitionDelay=Math.round(pe/10)+"ms",k.style.transitionDuration=le,p([k],"visible")}},onShow:function(){k&&(k.style.transitionDuration="0ms")},onHide:function(){k&&p([k],"hidden")}}}};function zr(){var m=J();return m.className=o,p([m],"hidden"),m}var xn={clientX:0,clientY:0},fn=[];function En(m){var w=m.clientX,S=m.clientY;xn={clientX:w,clientY:S}}function On(m){m.addEventListener("mousemove",En)}function Ur(m){m.removeEventListener("mousemove",En)}var jn={name:"followCursor",defaultValue:!1,fn:function(w){var S=w.reference,I=v(w.props.triggerTarget||S),Y=!1,H=!1,k=!0,be=w.props;function le(){return w.props.followCursor==="initial"&&w.state.isVisible}function pe(){I.addEventListener("mousemove",je)}function ye(){I.removeEventListener("mousemove",je)}function _e(){Y=!0,w.setProps({getReferenceClientRect:null}),Y=!1}function je(re){var he=re.target?S.contains(re.target):!0,ve=w.props.followCursor,ee=re.clientX,ie=re.clientY,x=S.getBoundingClientRect(),Ge=ee-x.left,fe=ie-x.top;(he||!w.props.interactive)&&w.setProps({getReferenceClientRect:function(){var bt=S.getBoundingClientRect(),Gt=ee,Kt=ie;ve==="initial"&&(Gt=bt.left+Ge,Kt=bt.top+fe);var Jt=ve==="horizontal"?bt.top:Kt,rt=ve==="vertical"?bt.right:Gt,st=ve==="horizontal"?bt.bottom:Kt,yt=ve==="vertical"?bt.left:Gt;return{width:rt-yt,height:st-Jt,top:Jt,right:rt,bottom:st,left:yt}}})}function Se(){w.props.followCursor&&(fn.push({instance:w,doc:I}),On(I))}function Ie(){fn=fn.filter(function(re){return re.instance!==w}),fn.filter(function(re){return re.doc===I}).length===0&&Ur(I)}return{onCreate:Se,onDestroy:Ie,onBeforeUpdate:function(){be=w.props},onAfterUpdate:function(he,ve){var ee=ve.followCursor;Y||ee!==void 0&&be.followCursor!==ee&&(Ie(),ee?(Se(),w.state.isMounted&&!H&&!le()&&pe()):(ye(),_e()))},onMount:function(){w.props.followCursor&&!H&&(k&&(je(xn),k=!1),le()||pe())},onTrigger:function(he,ve){U(ve)&&(xn={clientX:ve.clientX,clientY:ve.clientY}),H=ve.type==="focus"},onHidden:function(){w.props.followCursor&&(_e(),ye(),k=!0)}}}};function Yr(m,w){var S;return{popperOptions:Object.assign({},m.popperOptions,{modifiers:[].concat((((S=m.popperOptions)==null?void 0:S.modifiers)||[]).filter(function(I){var Y=I.name;return Y!==w.name}),[w])})}}var Bn={name:"inlinePositioning",defaultValue:!1,fn:function(w){var S=w.reference;function I(){return!!w.props.inlinePositioning}var Y,H=-1,k=!1,be={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(je){var Se=je.state;I()&&(Y!==Se.placement&&w.setProps({getReferenceClientRect:function(){return le(Se.placement)}}),Y=Se.placement)}};function le(_e){return Xr(K(_e),S.getBoundingClientRect(),X(S.getClientRects()),H)}function pe(_e){k=!0,w.setProps(_e),k=!1}function ye(){k||pe(Yr(w.props,be))}return{onCreate:ye,onAfterUpdate:ye,onTrigger:function(je,Se){if(U(Se)){var Ie=X(w.reference.getClientRects()),re=Ie.find(function(he){return he.left-2<=Se.clientX&&he.right+2>=Se.clientX&&he.top-2<=Se.clientY&&he.bottom+2>=Se.clientY});H=Ie.indexOf(re)}},onUntrigger:function(){H=-1}}}};function Xr(m,w,S,I){if(S.length<2||m===null)return w;if(S.length===2&&I>=0&&S[0].left>S[1].right)return S[I]||w;switch(m){case"top":case"bottom":{var Y=S[0],H=S[S.length-1],k=m==="top",be=Y.top,le=H.bottom,pe=k?Y.left:H.left,ye=k?Y.right:H.right,_e=ye-pe,je=le-be;return{top:be,bottom:le,left:pe,right:ye,width:_e,height:je}}case"left":case"right":{var Se=Math.min.apply(Math,S.map(function(fe){return fe.left})),Ie=Math.max.apply(Math,S.map(function(fe){return fe.right})),re=S.filter(function(fe){return m==="left"?fe.left===Se:fe.right===Ie}),he=re[0].top,ve=re[re.length-1].bottom,ee=Se,ie=Ie,x=ie-ee,Ge=ve-he;return{top:he,bottom:ve,left:ee,right:ie,width:x,height:Ge}}default:return w}}var qr={name:"sticky",defaultValue:!1,fn:function(w){var S=w.reference,I=w.popper;function Y(){return w.popperInstance?w.popperInstance.state.elements.reference:S}function H(pe){return w.props.sticky===!0||w.props.sticky===pe}var k=null,be=null;function le(){var pe=H("reference")?Y().getBoundingClientRect():null,ye=H("popper")?I.getBoundingClientRect():null;(pe&&Hn(k,pe)||ye&&Hn(be,ye))&&w.popperInstance&&w.popperInstance.update(),k=pe,be=ye,w.state.isMounted&&requestAnimationFrame(le)}return{onMount:function(){w.props.sticky&&le()}}}};function Hn(m,w){return m&&w?m.top!==w.top||m.right!==w.right||m.bottom!==w.bottom||m.left!==w.left:!0}dt.setDefaultProps({render:ar}),t.animateFill=dr,t.createSingleton=fr,t.default=dt,t.delegate=qt,t.followCursor=jn,t.hideAll=lr,t.inlinePositioning=Bn,t.roundArrow=r,t.sticky=qr}),Ei=Fo(Lo()),ds=Fo(Lo()),ps=t=>{let e={plugins:[]},r=i=>t[t.indexOf(i)+1];if(t.includes("animation")&&(e.animation=r("animation")),t.includes("duration")&&(e.duration=parseInt(r("duration"))),t.includes("delay")){let i=r("delay");e.delay=i.includes("-")?i.split("-").map(o=>parseInt(o)):parseInt(i)}if(t.includes("cursor")){e.plugins.push(ds.followCursor);let i=r("cursor");["x","initial"].includes(i)?e.followCursor=i==="x"?"horizontal":"initial":e.followCursor=!0}t.includes("on")&&(e.trigger=r("on")),t.includes("arrowless")&&(e.arrow=!1),t.includes("html")&&(e.allowHTML=!0),t.includes("interactive")&&(e.interactive=!0),t.includes("border")&&e.interactive&&(e.interactiveBorder=parseInt(r("border"))),t.includes("debounce")&&e.interactive&&(e.interactiveDebounce=parseInt(r("debounce"))),t.includes("max-width")&&(e.maxWidth=parseInt(r("max-width"))),t.includes("theme")&&(e.theme=r("theme")),t.includes("placement")&&(e.placement=r("placement"));let n={};return t.includes("no-flip")&&(n.modifiers||(n.modifiers=[]),n.modifiers.push({name:"flip",enabled:!1})),e.popperOptions=n,e};function Oi(t){t.magic("tooltip",e=>(r,n={})=>{let i=n.timeout;delete n.timeout;let o=(0,Ei.default)(e,{content:r,trigger:"manual",...n});o.show(),setTimeout(()=>{o.hide(),setTimeout(()=>o.destroy(),n.duration||300)},i||2e3)}),t.directive("tooltip",(e,{modifiers:r,expression:n},{evaluateLater:i,effect:o})=>{let l=r.length>0?ps(r):{};e.__x_tippy||(e.__x_tippy=(0,Ei.default)(e,l));let h=()=>e.__x_tippy.enable(),u=()=>e.__x_tippy.disable(),f=y=>{y?(h(),e.__x_tippy.setContent(y)):u()};if(r.includes("raw"))f(n);else{let y=i(n);o(()=>{y(b=>{typeof b=="object"?(e.__x_tippy.setProps(b),h()):f(b)})})}})}Oi.defaultProps=t=>(Ei.default.setDefaultProps(t),Oi);var hs=Oi,No=hs;document.addEventListener("alpine:init",()=>{window.Alpine.plugin(ro),window.Alpine.plugin(io),window.Alpine.plugin(Ro),window.Alpine.plugin(No)});var vs=function(t,e,r){function n(y,b){for(let A of y){let E=i(A,b);if(E!==null)return E}}function i(y,b){let A=y.match(/^[\{\[]([^\[\]\{\}]*)[\}\]](.*)/s);if(A===null||A.length!==3)return null;let E=A[1],O=A[2];if(E.includes(",")){let[P,R]=E.split(",",2);if(R==="*"&&b>=P)return O;if(P==="*"&&b<=R)return O;if(b>=P&&b<=R)return O}return E==b?O:null}function o(y){return y.toString().charAt(0).toUpperCase()+y.toString().slice(1)}function l(y,b){if(b.length===0)return y;let A={};for(let[E,O]of Object.entries(b))A[":"+o(E??"")]=o(O??""),A[":"+E.toUpperCase()]=O.toString().toUpperCase(),A[":"+E]=O;return Object.entries(A).forEach(([E,O])=>{y=y.replaceAll(E,O)}),y}function h(y){return y.map(b=>b.replace(/^[\{\[]([^\[\]\{\}]*)[\}\]]/,""))}let u=t.split("|"),f=n(u,e);return f!=null?l(f.trim(),r):(u=h(u),l(u.length>1&&e>1?u[1]:u[0],r))};window.jsMd5=ko.md5;window.pluralize=vs;})();
/*! Bundled license information:

js-md5/src/md5.js:
  (**
   * [js-md5]{@link https://github.com/emn178/js-md5}
   *
   * @namespace md5
   * @version 0.8.3
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2014-2023
   * @license MIT
   *)

sortablejs/modular/sortable.esm.js:
  (**!
   * Sortable 1.15.2
   * <AUTHOR>   <<EMAIL>>
   * <AUTHOR>    <<EMAIL>>
   * @license MIT
   *)
*/
