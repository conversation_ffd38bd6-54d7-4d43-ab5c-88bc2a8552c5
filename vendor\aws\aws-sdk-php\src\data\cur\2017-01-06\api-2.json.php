<?php
// This file was auto-generated from sdk-root/src/data/cur/2017-01-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-01-06', 'endpointPrefix' => 'cur', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Cost and Usage Report Service', 'serviceId' => 'Cost and Usage Report Service', 'signatureVersion' => 'v4', 'signingName' => 'cur', 'targetPrefix' => 'AWSOrigamiServiceGatewayService', 'uid' => 'cur-2017-01-06', ], 'operations' => [ 'DeleteReportDefinition' => [ 'name' => 'DeleteReportDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReportDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteReportDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeReportDefinitions' => [ 'name' => 'DescribeReportDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReportDefinitionsRequest', ], 'output' => [ 'shape' => 'DescribeReportDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'InternalErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], ], ], 'ModifyReportDefinition' => [ 'name' => 'ModifyReportDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReportDefinitionRequest', ], 'output' => [ 'shape' => 'ModifyReportDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutReportDefinition' => [ 'name' => 'PutReportDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutReportDefinitionRequest', ], 'output' => [ 'shape' => 'PutReportDefinitionResponse', ], 'errors' => [ [ 'shape' => 'DuplicateReportNameException', ], [ 'shape' => 'ReportLimitReachedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AWSRegion' => [ 'type' => 'string', 'enum' => [ 'af-south-1', 'ap-east-1', 'ap-south-1', 'ap-south-2', 'ap-southeast-1', 'ap-southeast-2', 'ap-southeast-3', 'ap-northeast-1', 'ap-northeast-2', 'ap-northeast-3', 'ca-central-1', 'eu-central-1', 'eu-central-2', 'eu-west-1', 'eu-west-2', 'eu-west-3', 'eu-north-1', 'eu-south-1', 'eu-south-2', 'me-central-1', 'me-south-1', 'sa-east-1', 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2', 'cn-north-1', 'cn-northwest-1', ], ], 'AdditionalArtifact' => [ 'type' => 'string', 'enum' => [ 'REDSHIFT', 'QUICKSIGHT', 'ATHENA', ], ], 'AdditionalArtifactList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalArtifact', ], ], 'BillingViewArn' => [ 'type' => 'string', 'max' => 128, 'pattern' => '(arn:aws(-cn)?:billing::[0-9]{12}:billingview/)?[a-zA-Z0-9_\\+=\\.\\-@].{1,30}', ], 'CompressionFormat' => [ 'type' => 'string', 'enum' => [ 'ZIP', 'GZIP', 'Parquet', ], ], 'DeleteReportDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'ReportName', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], ], ], 'DeleteReportDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'ResponseMessage' => [ 'shape' => 'DeleteResponseMessage', ], ], ], 'DeleteResponseMessage' => [ 'type' => 'string', ], 'DescribeReportDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'DescribeReportDefinitionsResponse' => [ 'type' => 'structure', 'members' => [ 'ReportDefinitions' => [ 'shape' => 'ReportDefinitionList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'DuplicateReportNameException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ErrorMessage' => [ 'type' => 'string', ], 'GenericString' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[A-Za-z0-9_\\.\\-=]*', ], 'InternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'LastDelivery' => [ 'type' => 'string', 'max' => 20, 'min' => 16, 'pattern' => '[0-9]{8}[T][0-9]{6}([Z]|[+-][0-9]{4})', ], 'LastStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'ERROR_PERMISSIONS', 'ERROR_NO_BUCKET', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ReportName', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 5, ], 'ModifyReportDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'ReportName', 'ReportDefinition', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], 'ReportDefinition' => [ 'shape' => 'ReportDefinition', ], ], ], 'ModifyReportDefinitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutReportDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'ReportDefinition', ], 'members' => [ 'ReportDefinition' => [ 'shape' => 'ReportDefinition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PutReportDefinitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'RefreshClosedReports' => [ 'type' => 'boolean', 'box' => true, ], 'ReportDefinition' => [ 'type' => 'structure', 'required' => [ 'ReportName', 'TimeUnit', 'Format', 'Compression', 'AdditionalSchemaElements', 'S3Bucket', 'S3Prefix', 'S3Region', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], 'TimeUnit' => [ 'shape' => 'TimeUnit', ], 'Format' => [ 'shape' => 'ReportFormat', ], 'Compression' => [ 'shape' => 'CompressionFormat', ], 'AdditionalSchemaElements' => [ 'shape' => 'SchemaElementList', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Prefix' => [ 'shape' => 'S3Prefix', ], 'S3Region' => [ 'shape' => 'AWSRegion', ], 'AdditionalArtifacts' => [ 'shape' => 'AdditionalArtifactList', ], 'RefreshClosedReports' => [ 'shape' => 'RefreshClosedReports', ], 'ReportVersioning' => [ 'shape' => 'ReportVersioning', ], 'BillingViewArn' => [ 'shape' => 'BillingViewArn', ], 'ReportStatus' => [ 'shape' => 'ReportStatus', ], ], ], 'ReportDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportDefinition', ], ], 'ReportFormat' => [ 'type' => 'string', 'enum' => [ 'textORcsv', 'Parquet', ], ], 'ReportLimitReachedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReportName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[0-9A-Za-z!\\-_.*\\\'()]+', ], 'ReportStatus' => [ 'type' => 'structure', 'members' => [ 'lastDelivery' => [ 'shape' => 'LastDelivery', ], 'lastStatus' => [ 'shape' => 'LastStatus', ], ], ], 'ReportVersioning' => [ 'type' => 'string', 'enum' => [ 'CREATE_NEW_REPORT', 'OVERWRITE_REPORT', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'S3Bucket' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[A-Za-z0-9_\\.\\-]+', ], 'S3Prefix' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[0-9A-Za-z!\\-_.*\\\'()/]*', ], 'SchemaElement' => [ 'type' => 'string', 'enum' => [ 'RESOURCES', 'SPLIT_COST_ALLOCATION_DATA', 'MANUAL_DISCOUNT_COMPATIBILITY', ], ], 'SchemaElementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaElement', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ReportName', 'Tags', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'TimeUnit' => [ 'type' => 'string', 'enum' => [ 'HOURLY', 'DAILY', 'MONTHLY', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ReportName', 'TagKeys', ], 'members' => [ 'ReportName' => [ 'shape' => 'ReportName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], ],];
