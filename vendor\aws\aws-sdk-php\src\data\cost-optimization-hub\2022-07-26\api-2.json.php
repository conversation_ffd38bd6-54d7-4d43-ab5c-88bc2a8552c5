<?php
// This file was auto-generated from sdk-root/src/data/cost-optimization-hub/2022-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-07-26', 'endpointPrefix' => 'cost-optimization-hub', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceFullName' => 'Cost Optimization Hub', 'serviceId' => 'Cost Optimization Hub', 'signatureVersion' => 'v4', 'signingName' => 'cost-optimization-hub', 'targetPrefix' => 'CostOptimizationHubService', 'uid' => 'cost-optimization-hub-2022-07-26', ], 'operations' => [ 'GetPreferences' => [ 'name' => 'GetPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPreferencesRequest', ], 'output' => [ 'shape' => 'GetPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRecommendation' => [ 'name' => 'GetRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRecommendationRequest', ], 'output' => [ 'shape' => 'GetRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEnrollmentStatuses' => [ 'name' => 'ListEnrollmentStatuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnrollmentStatusesRequest', ], 'output' => [ 'shape' => 'ListEnrollmentStatusesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRecommendationSummaries' => [ 'name' => 'ListRecommendationSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecommendationSummariesRequest', ], 'output' => [ 'shape' => 'ListRecommendationSummariesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRecommendations' => [ 'name' => 'ListRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecommendationsRequest', ], 'output' => [ 'shape' => 'ListRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateEnrollmentStatus' => [ 'name' => 'UpdateEnrollmentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnrollmentStatusRequest', ], 'output' => [ 'shape' => 'UpdateEnrollmentStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdatePreferences' => [ 'name' => 'UpdatePreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePreferencesRequest', ], 'output' => [ 'shape' => 'UpdatePreferencesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AccountEnrollmentStatus' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'EnrollmentStatus', ], ], ], 'AccountEnrollmentStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountEnrollmentStatus', ], ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 100, 'min' => 1, ], 'ActionType' => [ 'type' => 'string', 'enum' => [ 'Rightsize', 'Stop', 'Upgrade', 'PurchaseSavingsPlans', 'PurchaseReservedInstances', 'MigrateToGraviton', ], ], 'ActionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionType', ], 'max' => 100, 'min' => 1, ], 'BlockStoragePerformanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'iops' => [ 'shape' => 'Double', ], 'throughput' => [ 'shape' => 'Double', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ComputeConfiguration' => [ 'type' => 'structure', 'members' => [ 'architecture' => [ 'shape' => 'String', ], 'memorySizeInMB' => [ 'shape' => 'Integer', ], 'platform' => [ 'shape' => 'String', ], 'vCpu' => [ 'shape' => 'Double', ], ], ], 'ComputeSavingsPlans' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ComputeSavingsPlansConfiguration', ], 'costCalculation' => [ 'shape' => 'SavingsPlansCostCalculation', ], ], ], 'ComputeSavingsPlansConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'hourlyCommitment' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'term' => [ 'shape' => 'String', ], ], ], 'Datetime' => [ 'type' => 'timestamp', ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EbsVolume' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'EbsVolumeConfiguration', ], 'costCalculation' => [ 'shape' => 'ResourceCostCalculation', ], ], ], 'EbsVolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'attachmentState' => [ 'shape' => 'String', ], 'performance' => [ 'shape' => 'BlockStoragePerformanceConfiguration', ], 'storage' => [ 'shape' => 'StorageConfiguration', ], ], ], 'Ec2AutoScalingGroup' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'Ec2AutoScalingGroupConfiguration', ], 'costCalculation' => [ 'shape' => 'ResourceCostCalculation', ], ], ], 'Ec2AutoScalingGroupConfiguration' => [ 'type' => 'structure', 'members' => [ 'instance' => [ 'shape' => 'InstanceConfiguration', ], ], ], 'Ec2Instance' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'Ec2InstanceConfiguration', ], 'costCalculation' => [ 'shape' => 'ResourceCostCalculation', ], ], ], 'Ec2InstanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'instance' => [ 'shape' => 'InstanceConfiguration', ], ], ], 'Ec2InstanceSavingsPlans' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'Ec2InstanceSavingsPlansConfiguration', ], 'costCalculation' => [ 'shape' => 'SavingsPlansCostCalculation', ], ], ], 'Ec2InstanceSavingsPlansConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'hourlyCommitment' => [ 'shape' => 'String', ], 'instanceFamily' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'savingsPlansRegion' => [ 'shape' => 'String', ], 'term' => [ 'shape' => 'String', ], ], ], 'Ec2ReservedInstances' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'Ec2ReservedInstancesConfiguration', ], 'costCalculation' => [ 'shape' => 'ReservedInstancesCostCalculation', ], ], ], 'Ec2ReservedInstancesConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'currentGeneration' => [ 'shape' => 'String', ], 'instanceFamily' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'monthlyRecurringCost' => [ 'shape' => 'String', ], 'normalizedUnitsToPurchase' => [ 'shape' => 'String', ], 'numberOfInstancesToPurchase' => [ 'shape' => 'String', ], 'offeringClass' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'platform' => [ 'shape' => 'String', ], 'reservedInstancesRegion' => [ 'shape' => 'String', ], 'service' => [ 'shape' => 'String', ], 'sizeFlexEligible' => [ 'shape' => 'Boolean', ], 'tenancy' => [ 'shape' => 'String', ], 'term' => [ 'shape' => 'String', ], 'upfrontCost' => [ 'shape' => 'String', ], ], ], 'EcsService' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'EcsServiceConfiguration', ], 'costCalculation' => [ 'shape' => 'ResourceCostCalculation', ], ], ], 'EcsServiceConfiguration' => [ 'type' => 'structure', 'members' => [ 'compute' => [ 'shape' => 'ComputeConfiguration', ], ], ], 'ElastiCacheReservedInstances' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ElastiCacheReservedInstancesConfiguration', ], 'costCalculation' => [ 'shape' => 'ReservedInstancesCostCalculation', ], ], ], 'ElastiCacheReservedInstancesConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'currentGeneration' => [ 'shape' => 'String', ], 'instanceFamily' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'monthlyRecurringCost' => [ 'shape' => 'String', ], 'normalizedUnitsToPurchase' => [ 'shape' => 'String', ], 'numberOfInstancesToPurchase' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'reservedInstancesRegion' => [ 'shape' => 'String', ], 'service' => [ 'shape' => 'String', ], 'sizeFlexEligible' => [ 'shape' => 'Boolean', ], 'term' => [ 'shape' => 'String', ], 'upfrontCost' => [ 'shape' => 'String', ], ], ], 'EnrollmentStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'EstimatedDiscounts' => [ 'type' => 'structure', 'members' => [ 'otherDiscount' => [ 'shape' => 'Double', ], 'reservedInstancesDiscount' => [ 'shape' => 'Double', ], 'savingsPlansDiscount' => [ 'shape' => 'Double', ], ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdList', ], 'actionTypes' => [ 'shape' => 'ActionTypeList', ], 'implementationEfforts' => [ 'shape' => 'ImplementationEffortList', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'regions' => [ 'shape' => 'RegionList', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceIds' => [ 'shape' => 'ResourceIdList', ], 'resourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'restartNeeded' => [ 'shape' => 'Boolean', ], 'rollbackPossible' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'GetPreferencesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'memberAccountDiscountVisibility' => [ 'shape' => 'MemberAccountDiscountVisibility', ], 'savingsEstimationMode' => [ 'shape' => 'SavingsEstimationMode', ], ], ], 'GetRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'recommendationId', ], 'members' => [ 'recommendationId' => [ 'shape' => 'String', ], ], ], 'GetRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'String', ], 'actionType' => [ 'shape' => 'ActionType', ], 'costCalculationLookbackPeriodInDays' => [ 'shape' => 'Integer', ], 'currencyCode' => [ 'shape' => 'String', ], 'currentResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'currentResourceType' => [ 'shape' => 'ResourceType', ], 'estimatedMonthlyCost' => [ 'shape' => 'Double', ], 'estimatedMonthlySavings' => [ 'shape' => 'Double', ], 'estimatedSavingsOverCostCalculationLookbackPeriod' => [ 'shape' => 'Double', ], 'estimatedSavingsPercentage' => [ 'shape' => 'Double', ], 'implementationEffort' => [ 'shape' => 'ImplementationEffort', ], 'lastRefreshTimestamp' => [ 'shape' => 'Datetime', ], 'recommendationId' => [ 'shape' => 'String', ], 'recommendationLookbackPeriodInDays' => [ 'shape' => 'Integer', ], 'recommendedResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'recommendedResourceType' => [ 'shape' => 'ResourceType', ], 'region' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'restartNeeded' => [ 'shape' => 'Boolean', ], 'rollbackPossible' => [ 'shape' => 'Boolean', ], 'source' => [ 'shape' => 'Source', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'ImplementationEffort' => [ 'type' => 'string', 'enum' => [ 'VeryLow', 'Low', 'Medium', 'High', 'VeryHigh', ], ], 'ImplementationEffortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImplementationEffort', ], 'max' => 100, 'min' => 1, ], 'InstanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'String', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'LambdaFunction' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'LambdaFunctionConfiguration', ], 'costCalculation' => [ 'shape' => 'ResourceCostCalculation', ], ], ], 'LambdaFunctionConfiguration' => [ 'type' => 'structure', 'members' => [ 'compute' => [ 'shape' => 'ComputeConfiguration', ], ], ], 'ListEnrollmentStatusesRequest' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'includeOrganizationInfo' => [ 'shape' => 'PrimitiveBoolean', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListEnrollmentStatusesResponse' => [ 'type' => 'structure', 'members' => [ 'includeMemberAccounts' => [ 'shape' => 'Boolean', ], 'items' => [ 'shape' => 'AccountEnrollmentStatuses', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListRecommendationSummariesRequest' => [ 'type' => 'structure', 'required' => [ 'groupBy', ], 'members' => [ 'filter' => [ 'shape' => 'Filter', ], 'groupBy' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'ListRecommendationSummariesRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListRecommendationSummariesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'ListRecommendationSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'currencyCode' => [ 'shape' => 'String', ], 'estimatedTotalDedupedSavings' => [ 'shape' => 'Double', ], 'groupBy' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'RecommendationSummariesList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'Filter', ], 'includeAllRecommendations' => [ 'shape' => 'PrimitiveBoolean', ], 'maxResults' => [ 'shape' => 'ListRecommendationsRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'String', ], 'orderBy' => [ 'shape' => 'OrderBy', ], ], ], 'ListRecommendationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'ListRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'RecommendationList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'MemberAccountDiscountVisibility' => [ 'type' => 'string', 'enum' => [ 'All', 'None', ], ], 'OpenSearchReservedInstances' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'OpenSearchReservedInstancesConfiguration', ], 'costCalculation' => [ 'shape' => 'ReservedInstancesCostCalculation', ], ], ], 'OpenSearchReservedInstancesConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'currentGeneration' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'monthlyRecurringCost' => [ 'shape' => 'String', ], 'normalizedUnitsToPurchase' => [ 'shape' => 'String', ], 'numberOfInstancesToPurchase' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'reservedInstancesRegion' => [ 'shape' => 'String', ], 'service' => [ 'shape' => 'String', ], 'sizeFlexEligible' => [ 'shape' => 'Boolean', ], 'term' => [ 'shape' => 'String', ], 'upfrontCost' => [ 'shape' => 'String', ], ], ], 'Order' => [ 'type' => 'string', 'enum' => [ 'Asc', 'Desc', ], ], 'OrderBy' => [ 'type' => 'structure', 'members' => [ 'dimension' => [ 'shape' => 'String', ], 'order' => [ 'shape' => 'Order', ], ], ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'RdsReservedInstances' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RdsReservedInstancesConfiguration', ], 'costCalculation' => [ 'shape' => 'ReservedInstancesCostCalculation', ], ], ], 'RdsReservedInstancesConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'currentGeneration' => [ 'shape' => 'String', ], 'databaseEdition' => [ 'shape' => 'String', ], 'databaseEngine' => [ 'shape' => 'String', ], 'deploymentOption' => [ 'shape' => 'String', ], 'instanceFamily' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'licenseModel' => [ 'shape' => 'String', ], 'monthlyRecurringCost' => [ 'shape' => 'String', ], 'normalizedUnitsToPurchase' => [ 'shape' => 'String', ], 'numberOfInstancesToPurchase' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'reservedInstancesRegion' => [ 'shape' => 'String', ], 'service' => [ 'shape' => 'String', ], 'sizeFlexEligible' => [ 'shape' => 'Boolean', ], 'term' => [ 'shape' => 'String', ], 'upfrontCost' => [ 'shape' => 'String', ], ], ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'String', ], 'actionType' => [ 'shape' => 'String', ], 'currencyCode' => [ 'shape' => 'String', ], 'currentResourceSummary' => [ 'shape' => 'String', ], 'currentResourceType' => [ 'shape' => 'String', ], 'estimatedMonthlyCost' => [ 'shape' => 'Double', ], 'estimatedMonthlySavings' => [ 'shape' => 'Double', ], 'estimatedSavingsPercentage' => [ 'shape' => 'Double', ], 'implementationEffort' => [ 'shape' => 'String', ], 'lastRefreshTimestamp' => [ 'shape' => 'Datetime', ], 'recommendationId' => [ 'shape' => 'String', ], 'recommendationLookbackPeriodInDays' => [ 'shape' => 'Integer', ], 'recommendedResourceSummary' => [ 'shape' => 'String', ], 'recommendedResourceType' => [ 'shape' => 'String', ], 'region' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'restartNeeded' => [ 'shape' => 'Boolean', ], 'rollbackPossible' => [ 'shape' => 'Boolean', ], 'source' => [ 'shape' => 'Source', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'RecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], ], 'RecommendationSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationSummary', ], ], 'RecommendationSummary' => [ 'type' => 'structure', 'members' => [ 'estimatedMonthlySavings' => [ 'shape' => 'Double', ], 'group' => [ 'shape' => 'String', ], 'recommendationCount' => [ 'shape' => 'Integer', ], ], ], 'RedshiftReservedInstances' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RedshiftReservedInstancesConfiguration', ], 'costCalculation' => [ 'shape' => 'ReservedInstancesCostCalculation', ], ], ], 'RedshiftReservedInstancesConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'currentGeneration' => [ 'shape' => 'String', ], 'instanceFamily' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'monthlyRecurringCost' => [ 'shape' => 'String', ], 'normalizedUnitsToPurchase' => [ 'shape' => 'String', ], 'numberOfInstancesToPurchase' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'reservedInstancesRegion' => [ 'shape' => 'String', ], 'service' => [ 'shape' => 'String', ], 'sizeFlexEligible' => [ 'shape' => 'Boolean', ], 'term' => [ 'shape' => 'String', ], 'upfrontCost' => [ 'shape' => 'String', ], ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'ReservedInstancesCostCalculation' => [ 'type' => 'structure', 'members' => [ 'pricing' => [ 'shape' => 'ReservedInstancesPricing', ], ], ], 'ReservedInstancesPricing' => [ 'type' => 'structure', 'members' => [ 'estimatedMonthlyAmortizedReservationCost' => [ 'shape' => 'Double', ], 'estimatedOnDemandCost' => [ 'shape' => 'Double', ], 'monthlyReservationEligibleCost' => [ 'shape' => 'Double', ], 'savingsPercentage' => [ 'shape' => 'Double', ], ], ], 'ResourceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'ResourceCostCalculation' => [ 'type' => 'structure', 'members' => [ 'pricing' => [ 'shape' => 'ResourcePricing', ], 'usages' => [ 'shape' => 'UsageList', ], ], ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'computeSavingsPlans' => [ 'shape' => 'ComputeSavingsPlans', ], 'ebsVolume' => [ 'shape' => 'EbsVolume', ], 'ec2AutoScalingGroup' => [ 'shape' => 'Ec2AutoScalingGroup', ], 'ec2Instance' => [ 'shape' => 'Ec2Instance', ], 'ec2InstanceSavingsPlans' => [ 'shape' => 'Ec2InstanceSavingsPlans', ], 'ec2ReservedInstances' => [ 'shape' => 'Ec2ReservedInstances', ], 'ecsService' => [ 'shape' => 'EcsService', ], 'elastiCacheReservedInstances' => [ 'shape' => 'ElastiCacheReservedInstances', ], 'lambdaFunction' => [ 'shape' => 'LambdaFunction', ], 'openSearchReservedInstances' => [ 'shape' => 'OpenSearchReservedInstances', ], 'rdsReservedInstances' => [ 'shape' => 'RdsReservedInstances', ], 'redshiftReservedInstances' => [ 'shape' => 'RedshiftReservedInstances', ], 'sageMakerSavingsPlans' => [ 'shape' => 'SageMakerSavingsPlans', ], ], 'union' => true, ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePricing' => [ 'type' => 'structure', 'members' => [ 'estimatedCostAfterDiscounts' => [ 'shape' => 'Double', ], 'estimatedCostBeforeDiscounts' => [ 'shape' => 'Double', ], 'estimatedDiscounts' => [ 'shape' => 'EstimatedDiscounts', ], 'estimatedNetUnusedAmortizedCommitments' => [ 'shape' => 'Double', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Ec2Instance', 'LambdaFunction', 'EbsVolume', 'EcsService', 'Ec2AutoScalingGroup', 'Ec2InstanceSavingsPlans', 'ComputeSavingsPlans', 'SageMakerSavingsPlans', 'Ec2ReservedInstances', 'RdsReservedInstances', 'OpenSearchReservedInstances', 'RedshiftReservedInstances', 'ElastiCacheReservedInstances', ], ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], 'max' => 100, 'min' => 1, ], 'SageMakerSavingsPlans' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'SageMakerSavingsPlansConfiguration', ], 'costCalculation' => [ 'shape' => 'SavingsPlansCostCalculation', ], ], ], 'SageMakerSavingsPlansConfiguration' => [ 'type' => 'structure', 'members' => [ 'accountScope' => [ 'shape' => 'String', ], 'hourlyCommitment' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'term' => [ 'shape' => 'String', ], ], ], 'SavingsEstimationMode' => [ 'type' => 'string', 'enum' => [ 'BeforeDiscounts', 'AfterDiscounts', ], ], 'SavingsPlansCostCalculation' => [ 'type' => 'structure', 'members' => [ 'pricing' => [ 'shape' => 'SavingsPlansPricing', ], ], ], 'SavingsPlansPricing' => [ 'type' => 'structure', 'members' => [ 'estimatedMonthlyCommitment' => [ 'shape' => 'Double', ], 'estimatedOnDemandCost' => [ 'shape' => 'Double', ], 'monthlySavingsPlansEligibleCost' => [ 'shape' => 'Double', ], 'savingsPercentage' => [ 'shape' => 'Double', ], ], ], 'Source' => [ 'type' => 'string', 'enum' => [ 'ComputeOptimizer', 'CostExplorer', ], ], 'StorageConfiguration' => [ 'type' => 'structure', 'members' => [ 'sizeInGb' => [ 'shape' => 'Double', ], 'type' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 100, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UpdateEnrollmentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'includeMemberAccounts' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'EnrollmentStatus', ], ], ], 'UpdateEnrollmentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'UpdatePreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'memberAccountDiscountVisibility' => [ 'shape' => 'MemberAccountDiscountVisibility', ], 'savingsEstimationMode' => [ 'shape' => 'SavingsEstimationMode', ], ], ], 'UpdatePreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'memberAccountDiscountVisibility' => [ 'shape' => 'MemberAccountDiscountVisibility', ], 'savingsEstimationMode' => [ 'shape' => 'SavingsEstimationMode', ], ], ], 'Usage' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'String', ], 'productCode' => [ 'shape' => 'String', ], 'unit' => [ 'shape' => 'String', ], 'usageAmount' => [ 'shape' => 'Double', ], 'usageType' => [ 'shape' => 'String', ], ], ], 'UsageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Usage', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'fields' => [ 'shape' => 'ValidationExceptionDetails', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'exception' => true, ], 'ValidationExceptionDetail' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'message', ], 'members' => [ 'fieldName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionDetail', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'FieldValidationFailed', 'Other', ], ], ],];
