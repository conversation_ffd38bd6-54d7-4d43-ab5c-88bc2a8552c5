<?php

namespace {{ namespace }};

use App\Models\{{ model }};
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class {{ class }} extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
{{ schema }}
            ])
            ->statePath('data')
            ->model({{ modelClass }}::class);
    }

    public function create(): void
    {
        $data = $this->form->getState();

        $record = {{ modelClass }}::create($data);

        $this->form->model($record)->saveRelationships();
    }

    public function render(): View
    {
        return view('{{ view }}');
    }
}