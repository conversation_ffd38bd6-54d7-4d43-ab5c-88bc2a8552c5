<?php

namespace {{ namespace }};

use App\Models\{{ model }};
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class {{ exporterClass }} extends Exporter
{
    protected static ?string $model = {{ modelClass }}::class;

    public static function getColumns(): array
    {
        return [
{{ columns }}
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your {{ modelLabel }} export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
