<?php
namespace Aws\GroundStation;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Ground Station** service.
 * @method \Aws\Result cancelContact(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelContactAsync(array $args = [])
 * @method \Aws\Result createConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfigAsync(array $args = [])
 * @method \Aws\Result createDataflowEndpointGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataflowEndpointGroupAsync(array $args = [])
 * @method \Aws\Result createEphemeris(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEphemerisAsync(array $args = [])
 * @method \Aws\Result createMissionProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMissionProfileAsync(array $args = [])
 * @method \Aws\Result deleteConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfigAsync(array $args = [])
 * @method \Aws\Result deleteDataflowEndpointGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataflowEndpointGroupAsync(array $args = [])
 * @method \Aws\Result deleteEphemeris(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEphemerisAsync(array $args = [])
 * @method \Aws\Result deleteMissionProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMissionProfileAsync(array $args = [])
 * @method \Aws\Result describeContact(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeContactAsync(array $args = [])
 * @method \Aws\Result describeEphemeris(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeEphemerisAsync(array $args = [])
 * @method \Aws\Result getAgentConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentConfigurationAsync(array $args = [])
 * @method \Aws\Result getMissionProfileConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMissionProfileConfigAsync(array $args = [])
 * @method \Aws\Result getDataflowEndpointGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataflowEndpointGroupAsync(array $args = [])
 * @method \Aws\Result getMinuteUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMinuteUsageAsync(array $args = [])
 * @method \Aws\Result getMissionProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMissionProfileAsync(array $args = [])
 * @method \Aws\Result getSatellite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSatelliteAsync(array $args = [])
 * @method \Aws\Result listConfigs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfigsAsync(array $args = [])
 * @method \Aws\Result listContacts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listContactsAsync(array $args = [])
 * @method \Aws\Result listDataflowEndpointGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataflowEndpointGroupsAsync(array $args = [])
 * @method \Aws\Result listEphemerides(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEphemeridesAsync(array $args = [])
 * @method \Aws\Result listGroundStations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGroundStationsAsync(array $args = [])
 * @method \Aws\Result listMissionProfiles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMissionProfilesAsync(array $args = [])
 * @method \Aws\Result listSatellites(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSatellitesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result registerAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerAgentAsync(array $args = [])
 * @method \Aws\Result reserveContact(array $args = [])
 * @method \GuzzleHttp\Promise\Promise reserveContactAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAgentStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentStatusAsync(array $args = [])
 * @method \Aws\Result updateConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfigAsync(array $args = [])
 * @method \Aws\Result updateEphemeris(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEphemerisAsync(array $args = [])
 * @method \Aws\Result updateMissionProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMissionProfileAsync(array $args = [])
 */
class GroundStationClient extends AwsClient {}
