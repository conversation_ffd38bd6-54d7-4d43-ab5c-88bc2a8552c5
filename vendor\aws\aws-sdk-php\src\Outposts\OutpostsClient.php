<?php
namespace Aws\Outposts;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Outposts** service.
 * @method \Aws\Result cancelOrder(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelOrderAsync(array $args = [])
 * @method \Aws\Result createOrder(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOrderAsync(array $args = [])
 * @method \Aws\Result createOutpost(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOutpostAsync(array $args = [])
 * @method \Aws\Result createSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSiteAsync(array $args = [])
 * @method \Aws\Result deleteOutpost(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOutpostAsync(array $args = [])
 * @method \Aws\Result deleteSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSiteAsync(array $args = [])
 * @method \Aws\Result getCatalogItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCatalogItemAsync(array $args = [])
 * @method \Aws\Result getConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectionAsync(array $args = [])
 * @method \Aws\Result getOrder(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOrderAsync(array $args = [])
 * @method \Aws\Result getOutpost(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOutpostAsync(array $args = [])
 * @method \Aws\Result getOutpostInstanceTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOutpostInstanceTypesAsync(array $args = [])
 * @method \Aws\Result getSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSiteAsync(array $args = [])
 * @method \Aws\Result getSiteAddress(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSiteAddressAsync(array $args = [])
 * @method \Aws\Result listAssets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAssetsAsync(array $args = [])
 * @method \Aws\Result listCatalogItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCatalogItemsAsync(array $args = [])
 * @method \Aws\Result listOrders(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOrdersAsync(array $args = [])
 * @method \Aws\Result listOutposts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOutpostsAsync(array $args = [])
 * @method \Aws\Result listSites(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSitesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startConnectionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateOutpost(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateOutpostAsync(array $args = [])
 * @method \Aws\Result updateSite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSiteAsync(array $args = [])
 * @method \Aws\Result updateSiteAddress(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSiteAddressAsync(array $args = [])
 * @method \Aws\Result updateSiteRackPhysicalProperties(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSiteRackPhysicalPropertiesAsync(array $args = [])
 */
class OutpostsClient extends AwsClient {}
