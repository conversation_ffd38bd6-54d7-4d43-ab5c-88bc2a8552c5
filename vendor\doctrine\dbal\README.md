# Doctrine DBAL

|                   [5.0-dev][5.0]                    |                   [4.1-dev][4.1]                    |                     [4.0][4.0]                      |                   [3.9-dev][3.9]                    |                     [3.8][3.8]                      |
|:---------------------------------------------------:|:---------------------------------------------------:|:---------------------------------------------------:|:---------------------------------------------------:|:---------------------------------------------------:|
|      [![GitHub Actions][GA 5.0 image]][GA 5.0]      |      [![GitHub Actions][GA 4.1 image]][GA 4.1]      |      [![GitHub Actions][GA 4.0 image]][GA 4.0]      |      [![GitHub Actions][GA 3.9 image]][GA 3.9]      |      [![GitHub Actions][GA 3.8 image]][GA 3.8]      |
|   [![AppVeyor][AppVeyor 5.0 image]][AppVeyor 5.0]   |   [![AppVeyor][AppVeyor 4.1 image]][AppVeyor 4.1]   |   [![AppVeyor][AppVeyor 4.0 image]][AppVeyor 4.0]   |   [![AppVeyor][AppVeyor 3.9 image]][AppVeyor 3.9]   |   [![AppVeyor][AppVeyor 3.8 image]][AppVeyor 3.8]   |
| [![Code Coverage][Coverage 5.0 image]][CodeCov 5.0] | [![Code Coverage][Coverage 4.1 image]][CodeCov 4.1] | [![Code Coverage][Coverage 4.0 image]][CodeCov 4.0] | [![Code Coverage][Coverage 3.9 image]][CodeCov 3.9] | [![Code Coverage][Coverage 3.8 image]][CodeCov 3.8] |
|                         N/A                         |                         N/A                         |  [![Type Coverage][TypeCov image]][TypeCov]         |                         N/A                         |                                                     |

Powerful ***D***ata***B***ase ***A***bstraction ***L***ayer with many features for database schema introspection and schema management.

## More resources:

* [Website](http://www.doctrine-project.org/projects/dbal.html)
* [Documentation](http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/)
* [Issue Tracker](https://github.com/doctrine/dbal/issues)

  [Coverage 5.0 image]: https://codecov.io/gh/doctrine/dbal/branch/5.0.x/graph/badge.svg
  [5.0]: https://github.com/doctrine/dbal/tree/5.0.x
  [CodeCov 5.0]: https://codecov.io/gh/doctrine/dbal/branch/5.0.x
  [AppVeyor 5.0]: https://ci.appveyor.com/project/doctrine/dbal/branch/5.0.x
  [AppVeyor 5.0 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/5.0.x?svg=true
  [GA 5.0]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A5.0.x
  [GA 5.0 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=5.0.x

  [Coverage 4.1 image]: https://codecov.io/gh/doctrine/dbal/branch/4.1.x/graph/badge.svg
  [4.1]: https://github.com/doctrine/dbal/tree/4.1.x
  [CodeCov 4.1]: https://codecov.io/gh/doctrine/dbal/branch/4.1.x
  [AppVeyor 4.1]: https://ci.appveyor.com/project/doctrine/dbal/branch/4.1.x
  [AppVeyor 4.1 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/4.1.x?svg=true
  [GA 4.1]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A4.1.x
  [GA 4.1 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=4.1.x

  [Coverage 4.0 image]: https://codecov.io/gh/doctrine/dbal/branch/4.0.x/graph/badge.svg
  [4.0]: https://github.com/doctrine/dbal/tree/4.0.x
  [CodeCov 4.0]: https://codecov.io/gh/doctrine/dbal/branch/4.0.x
  [AppVeyor 4.0]: https://ci.appveyor.com/project/doctrine/dbal/branch/4.0.x
  [AppVeyor 4.0 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/4.0.x?svg=true
  [GA 4.0]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A4.0.x
  [GA 4.0 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=4.0.x
  [TypeCov]: https://shepherd.dev/github/doctrine/dbal
  [TypeCov image]: https://shepherd.dev/github/doctrine/dbal/coverage.svg

  [Coverage 3.9 image]: https://codecov.io/gh/doctrine/dbal/branch/3.9.x/graph/badge.svg
  [3.9]: https://github.com/doctrine/dbal/tree/3.9.x
  [CodeCov 3.9]: https://codecov.io/gh/doctrine/dbal/branch/3.9.x
  [AppVeyor 3.9]: https://ci.appveyor.com/project/doctrine/dbal/branch/3.9.x
  [AppVeyor 3.9 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/3.9.x?svg=true
  [GA 3.9]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.9.x
  [GA 3.9 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=3.9.x

  [Coverage 3.8 image]: https://codecov.io/gh/doctrine/dbal/branch/3.8.x/graph/badge.svg
  [3.8]: https://github.com/doctrine/dbal/tree/3.8.x
  [CodeCov 3.8]: https://codecov.io/gh/doctrine/dbal/branch/3.8.x
  [AppVeyor 3.8]: https://ci.appveyor.com/project/doctrine/dbal/branch/3.8.x
  [AppVeyor 3.8 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/3.8.x?svg=true
  [GA 3.8]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.8.x
  [GA 3.8 image]: https://github.com/doctrine/dbal/workflows/Continuous%20Integration/badge.svg?branch=3.8.x
