<?php
// This file was auto-generated from sdk-root/src/data/appflow/2020-08-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-08-23', 'endpointPrefix' => 'appflow', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Appflow', 'serviceId' => 'Appflow', 'signatureVersion' => 'v4', 'signingName' => 'appflow', 'uid' => 'appflow-2020-08-23', ], 'operations' => [ 'CancelFlowExecutions' => [ 'name' => 'CancelFlowExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancel-flow-executions', ], 'input' => [ 'shape' => 'CancelFlowExecutionsRequest', ], 'output' => [ 'shape' => 'CancelFlowExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConnectorProfile' => [ 'name' => 'CreateConnectorProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-connector-profile', ], 'input' => [ 'shape' => 'CreateConnectorProfileRequest', ], 'output' => [ 'shape' => 'CreateConnectorProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFlow' => [ 'name' => 'CreateFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-flow', ], 'input' => [ 'shape' => 'CreateFlowRequest', ], 'output' => [ 'shape' => 'CreateFlowResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteConnectorProfile' => [ 'name' => 'DeleteConnectorProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-connector-profile', ], 'input' => [ 'shape' => 'DeleteConnectorProfileRequest', ], 'output' => [ 'shape' => 'DeleteConnectorProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFlow' => [ 'name' => 'DeleteFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-flow', ], 'input' => [ 'shape' => 'DeleteFlowRequest', ], 'output' => [ 'shape' => 'DeleteFlowResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeConnector' => [ 'name' => 'DescribeConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-connector', ], 'input' => [ 'shape' => 'DescribeConnectorRequest', ], 'output' => [ 'shape' => 'DescribeConnectorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeConnectorEntity' => [ 'name' => 'DescribeConnectorEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-connector-entity', ], 'input' => [ 'shape' => 'DescribeConnectorEntityRequest', ], 'output' => [ 'shape' => 'DescribeConnectorEntityResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeConnectorProfiles' => [ 'name' => 'DescribeConnectorProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-connector-profiles', ], 'input' => [ 'shape' => 'DescribeConnectorProfilesRequest', ], 'output' => [ 'shape' => 'DescribeConnectorProfilesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeConnectors' => [ 'name' => 'DescribeConnectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-connectors', ], 'input' => [ 'shape' => 'DescribeConnectorsRequest', ], 'output' => [ 'shape' => 'DescribeConnectorsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFlow' => [ 'name' => 'DescribeFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-flow', ], 'input' => [ 'shape' => 'DescribeFlowRequest', ], 'output' => [ 'shape' => 'DescribeFlowResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFlowExecutionRecords' => [ 'name' => 'DescribeFlowExecutionRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-flow-execution-records', ], 'input' => [ 'shape' => 'DescribeFlowExecutionRecordsRequest', ], 'output' => [ 'shape' => 'DescribeFlowExecutionRecordsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListConnectorEntities' => [ 'name' => 'ListConnectorEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-connector-entities', ], 'input' => [ 'shape' => 'ListConnectorEntitiesRequest', ], 'output' => [ 'shape' => 'ListConnectorEntitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListConnectors' => [ 'name' => 'ListConnectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-connectors', ], 'input' => [ 'shape' => 'ListConnectorsRequest', ], 'output' => [ 'shape' => 'ListConnectorsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFlows' => [ 'name' => 'ListFlows', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-flows', ], 'input' => [ 'shape' => 'ListFlowsRequest', ], 'output' => [ 'shape' => 'ListFlowsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterConnector' => [ 'name' => 'RegisterConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/register-connector', ], 'input' => [ 'shape' => 'RegisterConnectorRequest', ], 'output' => [ 'shape' => 'RegisterConnectorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'ConnectorAuthenticationException', ], ], ], 'ResetConnectorMetadataCache' => [ 'name' => 'ResetConnectorMetadataCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/reset-connector-metadata-cache', ], 'input' => [ 'shape' => 'ResetConnectorMetadataCacheRequest', ], 'output' => [ 'shape' => 'ResetConnectorMetadataCacheResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartFlow' => [ 'name' => 'StartFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-flow', ], 'input' => [ 'shape' => 'StartFlowRequest', ], 'output' => [ 'shape' => 'StartFlowResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopFlow' => [ 'name' => 'StopFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/stop-flow', ], 'input' => [ 'shape' => 'StopFlowRequest', ], 'output' => [ 'shape' => 'StopFlowResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UnregisterConnector' => [ 'name' => 'UnregisterConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/unregister-connector', ], 'input' => [ 'shape' => 'UnregisterConnectorRequest', ], 'output' => [ 'shape' => 'UnregisterConnectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateConnectorProfile' => [ 'name' => 'UpdateConnectorProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-connector-profile', ], 'input' => [ 'shape' => 'UpdateConnectorProfileRequest', ], 'output' => [ 'shape' => 'UpdateConnectorProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateConnectorRegistration' => [ 'name' => 'UpdateConnectorRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-connector-registration', ], 'input' => [ 'shape' => 'UpdateConnectorRegistrationRequest', ], 'output' => [ 'shape' => 'UpdateConnectorRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'ConnectorAuthenticationException', ], ], ], 'UpdateFlow' => [ 'name' => 'UpdateFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-flow', ], 'input' => [ 'shape' => 'UpdateFlowRequest', ], 'output' => [ 'shape' => 'UpdateFlowResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ConnectorAuthenticationException', ], [ 'shape' => 'ConnectorServerException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:.*:.*:[0-9]+:.*', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessKeyId' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', 'sensitive' => true, ], 'AccessToken' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '\\S+', 'sensitive' => true, ], 'AccountName' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'AggregationConfig' => [ 'type' => 'structure', 'members' => [ 'aggregationType' => [ 'shape' => 'AggregationType', ], 'targetFileSize' => [ 'shape' => 'Long', ], ], ], 'AggregationType' => [ 'type' => 'string', 'enum' => [ 'None', 'SingleFile', ], ], 'AmplitudeConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'BETWEEN', ], ], 'AmplitudeConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'apiKey', 'secretKey', ], 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], 'secretKey' => [ 'shape' => 'SecretKey', ], ], ], 'AmplitudeConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [], ], 'AmplitudeMetadata' => [ 'type' => 'structure', 'members' => [], ], 'AmplitudeSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'ApiKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', 'sensitive' => true, ], 'ApiKeyCredentials' => [ 'type' => 'structure', 'required' => [ 'apiKey', ], 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], 'apiSecretKey' => [ 'shape' => 'ApiSecretKey', ], ], ], 'ApiSecretKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', 'sensitive' => true, ], 'ApiToken' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ApiVersion' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ApplicationHostUrl' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'ApplicationKey' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ApplicationServicePath' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ApplicationType' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'AuthCode' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '\\S+', ], 'AuthCodeUrl' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'AuthCodeUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthCodeUrl', ], ], 'AuthParameter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'isRequired' => [ 'shape' => 'Boolean', ], 'label' => [ 'shape' => 'Label', ], 'description' => [ 'shape' => 'Description', ], 'isSensitiveField' => [ 'shape' => 'Boolean', ], 'connectorSuppliedValues' => [ 'shape' => 'ConnectorSuppliedValueList', ], ], ], 'AuthParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthParameter', ], ], 'AuthenticationConfig' => [ 'type' => 'structure', 'members' => [ 'isBasicAuthSupported' => [ 'shape' => 'Boolean', ], 'isApiKeyAuthSupported' => [ 'shape' => 'Boolean', ], 'isOAuth2Supported' => [ 'shape' => 'Boolean', ], 'isCustomAuthSupported' => [ 'shape' => 'Boolean', ], 'oAuth2Defaults' => [ 'shape' => 'OAuth2Defaults', ], 'customAuthConfigs' => [ 'shape' => 'CustomAuthConfigList', ], ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'OAUTH2', 'APIKEY', 'BASIC', 'CUSTOM', ], ], 'BasicAuthCredentials' => [ 'type' => 'structure', 'required' => [ 'username', 'password', ], 'members' => [ 'username' => [ 'shape' => 'Username', ], 'password' => [ 'shape' => 'Password', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '\\S+', ], 'BucketPrefix' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'BusinessUnitId' => [ 'type' => 'string', 'max' => 18, 'pattern' => '\\S+', ], 'CancelFlowExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'executionIds' => [ 'shape' => 'ExecutionIds', ], ], ], 'CancelFlowExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'invalidExecutions' => [ 'shape' => 'ExecutionIds', ], ], ], 'CatalogType' => [ 'type' => 'string', 'enum' => [ 'GLUE', ], ], 'ClientCredentialsArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:secretsmanager:.*:[0-9]+:.*', 'sensitive' => true, ], 'ClientId' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ClientNumber' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '^\\d{3}$', ], 'ClientSecret' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', 'sensitive' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[ -~]+', ], 'ClusterIdentifier' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionMode' => [ 'type' => 'string', 'enum' => [ 'Public', 'Private', ], ], 'ConnectorAuthenticationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'ConnectorConfiguration' => [ 'type' => 'structure', 'members' => [ 'canUseAsSource' => [ 'shape' => 'Boolean', ], 'canUseAsDestination' => [ 'shape' => 'Boolean', ], 'supportedDestinationConnectors' => [ 'shape' => 'ConnectorTypeList', ], 'supportedSchedulingFrequencies' => [ 'shape' => 'SchedulingFrequencyTypeList', ], 'isPrivateLinkEnabled' => [ 'shape' => 'Boolean', ], 'isPrivateLinkEndpointUrlRequired' => [ 'shape' => 'Boolean', ], 'supportedTriggerTypes' => [ 'shape' => 'TriggerTypeList', ], 'connectorMetadata' => [ 'shape' => 'ConnectorMetadata', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'connectorDescription' => [ 'shape' => 'ConnectorDescription', ], 'connectorOwner' => [ 'shape' => 'ConnectorOwner', ], 'connectorName' => [ 'shape' => 'ConnectorName', ], 'connectorVersion' => [ 'shape' => 'ConnectorVersion', ], 'connectorArn' => [ 'shape' => 'ARN', ], 'connectorModes' => [ 'shape' => 'ConnectorModeList', ], 'authenticationConfig' => [ 'shape' => 'AuthenticationConfig', ], 'connectorRuntimeSettings' => [ 'shape' => 'ConnectorRuntimeSettingList', ], 'supportedApiVersions' => [ 'shape' => 'SupportedApiVersionList', ], 'supportedOperators' => [ 'shape' => 'SupportedOperatorList', ], 'supportedWriteOperations' => [ 'shape' => 'SupportedWriteOperationList', ], 'connectorProvisioningType' => [ 'shape' => 'ConnectorProvisioningType', ], 'connectorProvisioningConfig' => [ 'shape' => 'ConnectorProvisioningConfig', ], 'logoURL' => [ 'shape' => 'LogoURL', ], 'registeredAt' => [ 'shape' => 'Date', ], 'registeredBy' => [ 'shape' => 'RegisteredBy', ], 'supportedDataTransferTypes' => [ 'shape' => 'SupportedDataTransferTypeList', ], 'supportedDataTransferApis' => [ 'shape' => 'SupportedDataTransferApis', ], ], ], 'ConnectorConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConnectorType', ], 'value' => [ 'shape' => 'ConnectorConfiguration', ], ], 'ConnectorDescription' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\w!@#\\-.?,\\s]*', ], 'ConnectorDetail' => [ 'type' => 'structure', 'members' => [ 'connectorDescription' => [ 'shape' => 'ConnectorDescription', ], 'connectorName' => [ 'shape' => 'ConnectorName', ], 'connectorOwner' => [ 'shape' => 'ConnectorOwner', ], 'connectorVersion' => [ 'shape' => 'ConnectorVersion', ], 'applicationType' => [ 'shape' => 'ApplicationType', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'registeredAt' => [ 'shape' => 'Date', ], 'registeredBy' => [ 'shape' => 'RegisteredBy', ], 'connectorProvisioningType' => [ 'shape' => 'ConnectorProvisioningType', ], 'connectorModes' => [ 'shape' => 'ConnectorModeList', ], 'supportedDataTransferTypes' => [ 'shape' => 'SupportedDataTransferTypeList', ], ], ], 'ConnectorEntity' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'label' => [ 'shape' => 'Label', ], 'hasNestedEntities' => [ 'shape' => 'Boolean', ], ], ], 'ConnectorEntityField' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'Identifier', ], 'parentIdentifier' => [ 'shape' => 'Identifier', ], 'label' => [ 'shape' => 'Label', ], 'isPrimaryKey' => [ 'shape' => 'Boolean', ], 'defaultValue' => [ 'shape' => 'String', ], 'isDeprecated' => [ 'shape' => 'Boolean', ], 'supportedFieldTypeDetails' => [ 'shape' => 'SupportedFieldTypeDetails', ], 'description' => [ 'shape' => 'Description', ], 'sourceProperties' => [ 'shape' => 'SourceFieldProperties', ], 'destinationProperties' => [ 'shape' => 'DestinationFieldProperties', ], 'customProperties' => [ 'shape' => 'CustomProperties', ], ], ], 'ConnectorEntityFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorEntityField', ], ], 'ConnectorEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorEntity', ], ], 'ConnectorEntityMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Group', ], 'value' => [ 'shape' => 'ConnectorEntityList', ], ], 'ConnectorLabel' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[a-zA-Z0-9][\\w!@#.-]+', ], 'ConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorDetail', ], ], 'ConnectorMetadata' => [ 'type' => 'structure', 'members' => [ 'Amplitude' => [ 'shape' => 'AmplitudeMetadata', ], 'Datadog' => [ 'shape' => 'DatadogMetadata', ], 'Dynatrace' => [ 'shape' => 'DynatraceMetadata', ], 'GoogleAnalytics' => [ 'shape' => 'GoogleAnalyticsMetadata', ], 'InforNexus' => [ 'shape' => 'InforNexusMetadata', ], 'Marketo' => [ 'shape' => 'MarketoMetadata', ], 'Redshift' => [ 'shape' => 'RedshiftMetadata', ], 'S3' => [ 'shape' => 'S3Metadata', ], 'Salesforce' => [ 'shape' => 'SalesforceMetadata', ], 'ServiceNow' => [ 'shape' => 'ServiceNowMetadata', ], 'Singular' => [ 'shape' => 'SingularMetadata', ], 'Slack' => [ 'shape' => 'SlackMetadata', ], 'Snowflake' => [ 'shape' => 'SnowflakeMetadata', ], 'Trendmicro' => [ 'shape' => 'TrendmicroMetadata', ], 'Veeva' => [ 'shape' => 'VeevaMetadata', ], 'Zendesk' => [ 'shape' => 'ZendeskMetadata', ], 'EventBridge' => [ 'shape' => 'EventBridgeMetadata', ], 'Upsolver' => [ 'shape' => 'UpsolverMetadata', ], 'CustomerProfiles' => [ 'shape' => 'CustomerProfilesMetadata', ], 'Honeycode' => [ 'shape' => 'HoneycodeMetadata', ], 'SAPOData' => [ 'shape' => 'SAPODataMetadata', ], 'Pardot' => [ 'shape' => 'PardotMetadata', ], ], ], 'ConnectorMode' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ConnectorModeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorMode', ], ], 'ConnectorName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ConnectorOAuthRequest' => [ 'type' => 'structure', 'members' => [ 'authCode' => [ 'shape' => 'AuthCode', ], 'redirectUri' => [ 'shape' => 'RedirectUri', ], ], ], 'ConnectorOperator' => [ 'type' => 'structure', 'members' => [ 'Amplitude' => [ 'shape' => 'AmplitudeConnectorOperator', ], 'Datadog' => [ 'shape' => 'DatadogConnectorOperator', ], 'Dynatrace' => [ 'shape' => 'DynatraceConnectorOperator', ], 'GoogleAnalytics' => [ 'shape' => 'GoogleAnalyticsConnectorOperator', ], 'InforNexus' => [ 'shape' => 'InforNexusConnectorOperator', ], 'Marketo' => [ 'shape' => 'MarketoConnectorOperator', ], 'S3' => [ 'shape' => 'S3ConnectorOperator', ], 'Salesforce' => [ 'shape' => 'SalesforceConnectorOperator', ], 'ServiceNow' => [ 'shape' => 'ServiceNowConnectorOperator', ], 'Singular' => [ 'shape' => 'SingularConnectorOperator', ], 'Slack' => [ 'shape' => 'SlackConnectorOperator', ], 'Trendmicro' => [ 'shape' => 'TrendmicroConnectorOperator', ], 'Veeva' => [ 'shape' => 'VeevaConnectorOperator', ], 'Zendesk' => [ 'shape' => 'ZendeskConnectorOperator', ], 'SAPOData' => [ 'shape' => 'SAPODataConnectorOperator', ], 'CustomConnector' => [ 'shape' => 'Operator', ], 'Pardot' => [ 'shape' => 'PardotConnectorOperator', ], ], ], 'ConnectorOwner' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ConnectorProfile' => [ 'type' => 'structure', 'members' => [ 'connectorProfileArn' => [ 'shape' => 'ConnectorProfileArn', ], 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'connectionMode' => [ 'shape' => 'ConnectionMode', ], 'credentialsArn' => [ 'shape' => 'ARN', ], 'connectorProfileProperties' => [ 'shape' => 'ConnectorProfileProperties', ], 'createdAt' => [ 'shape' => 'Date', ], 'lastUpdatedAt' => [ 'shape' => 'Date', ], 'privateConnectionProvisioningState' => [ 'shape' => 'PrivateConnectionProvisioningState', ], ], ], 'ConnectorProfileArn' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:appflow:.*:[0-9]+:.*', ], 'ConnectorProfileConfig' => [ 'type' => 'structure', 'required' => [ 'connectorProfileProperties', ], 'members' => [ 'connectorProfileProperties' => [ 'shape' => 'ConnectorProfileProperties', ], 'connectorProfileCredentials' => [ 'shape' => 'ConnectorProfileCredentials', ], ], ], 'ConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'Amplitude' => [ 'shape' => 'AmplitudeConnectorProfileCredentials', ], 'Datadog' => [ 'shape' => 'DatadogConnectorProfileCredentials', ], 'Dynatrace' => [ 'shape' => 'DynatraceConnectorProfileCredentials', ], 'GoogleAnalytics' => [ 'shape' => 'GoogleAnalyticsConnectorProfileCredentials', ], 'Honeycode' => [ 'shape' => 'HoneycodeConnectorProfileCredentials', ], 'InforNexus' => [ 'shape' => 'InforNexusConnectorProfileCredentials', ], 'Marketo' => [ 'shape' => 'MarketoConnectorProfileCredentials', ], 'Redshift' => [ 'shape' => 'RedshiftConnectorProfileCredentials', ], 'Salesforce' => [ 'shape' => 'SalesforceConnectorProfileCredentials', ], 'ServiceNow' => [ 'shape' => 'ServiceNowConnectorProfileCredentials', ], 'Singular' => [ 'shape' => 'SingularConnectorProfileCredentials', ], 'Slack' => [ 'shape' => 'SlackConnectorProfileCredentials', ], 'Snowflake' => [ 'shape' => 'SnowflakeConnectorProfileCredentials', ], 'Trendmicro' => [ 'shape' => 'TrendmicroConnectorProfileCredentials', ], 'Veeva' => [ 'shape' => 'VeevaConnectorProfileCredentials', ], 'Zendesk' => [ 'shape' => 'ZendeskConnectorProfileCredentials', ], 'SAPOData' => [ 'shape' => 'SAPODataConnectorProfileCredentials', ], 'CustomConnector' => [ 'shape' => 'CustomConnectorProfileCredentials', ], 'Pardot' => [ 'shape' => 'PardotConnectorProfileCredentials', ], ], ], 'ConnectorProfileDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorProfile', ], ], 'ConnectorProfileName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\w/!@#+=.-]+', ], 'ConnectorProfileNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorProfileName', ], 'max' => 100, 'min' => 0, ], 'ConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [ 'Amplitude' => [ 'shape' => 'AmplitudeConnectorProfileProperties', ], 'Datadog' => [ 'shape' => 'DatadogConnectorProfileProperties', ], 'Dynatrace' => [ 'shape' => 'DynatraceConnectorProfileProperties', ], 'GoogleAnalytics' => [ 'shape' => 'GoogleAnalyticsConnectorProfileProperties', ], 'Honeycode' => [ 'shape' => 'HoneycodeConnectorProfileProperties', ], 'InforNexus' => [ 'shape' => 'InforNexusConnectorProfileProperties', ], 'Marketo' => [ 'shape' => 'MarketoConnectorProfileProperties', ], 'Redshift' => [ 'shape' => 'RedshiftConnectorProfileProperties', ], 'Salesforce' => [ 'shape' => 'SalesforceConnectorProfileProperties', ], 'ServiceNow' => [ 'shape' => 'ServiceNowConnectorProfileProperties', ], 'Singular' => [ 'shape' => 'SingularConnectorProfileProperties', ], 'Slack' => [ 'shape' => 'SlackConnectorProfileProperties', ], 'Snowflake' => [ 'shape' => 'SnowflakeConnectorProfileProperties', ], 'Trendmicro' => [ 'shape' => 'TrendmicroConnectorProfileProperties', ], 'Veeva' => [ 'shape' => 'VeevaConnectorProfileProperties', ], 'Zendesk' => [ 'shape' => 'ZendeskConnectorProfileProperties', ], 'SAPOData' => [ 'shape' => 'SAPODataConnectorProfileProperties', ], 'CustomConnector' => [ 'shape' => 'CustomConnectorProfileProperties', ], 'Pardot' => [ 'shape' => 'PardotConnectorProfileProperties', ], ], ], 'ConnectorProvisioningConfig' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaConnectorProvisioningConfig', ], ], ], 'ConnectorProvisioningType' => [ 'type' => 'string', 'enum' => [ 'LAMBDA', ], ], 'ConnectorRuntimeSetting' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'dataType' => [ 'shape' => 'ConnectorRuntimeSettingDataType', ], 'isRequired' => [ 'shape' => 'Boolean', ], 'label' => [ 'shape' => 'Label', ], 'description' => [ 'shape' => 'Description', ], 'scope' => [ 'shape' => 'ConnectorRuntimeSettingScope', ], 'connectorSuppliedValueOptions' => [ 'shape' => 'ConnectorSuppliedValueOptionList', ], ], ], 'ConnectorRuntimeSettingDataType' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ConnectorRuntimeSettingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorRuntimeSetting', ], ], 'ConnectorRuntimeSettingScope' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ConnectorServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ConnectorSuppliedValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ConnectorSuppliedValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorSuppliedValue', ], ], 'ConnectorSuppliedValueOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorSuppliedValue', ], ], 'ConnectorType' => [ 'type' => 'string', 'enum' => [ 'Salesforce', 'Singular', 'Slack', 'Redshift', 'S3', 'Marketo', 'Googleanalytics', 'Zendesk', 'Servicenow', 'Datadog', 'Trendmicro', 'Snowflake', 'Dynatrace', 'Infornexus', 'Amplitude', 'Veeva', 'EventBridge', 'LookoutMetrics', 'Upsolver', 'Honeycode', 'CustomerProfiles', 'SAPOData', 'CustomConnector', 'Pardot', ], ], 'ConnectorTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorType', ], 'max' => 100, 'min' => 0, ], 'ConnectorVersion' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'CreateConnectorProfileRequest' => [ 'type' => 'structure', 'required' => [ 'connectorProfileName', 'connectorType', 'connectionMode', 'connectorProfileConfig', ], 'members' => [ 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'kmsArn' => [ 'shape' => 'KMSArn', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'connectionMode' => [ 'shape' => 'ConnectionMode', ], 'connectorProfileConfig' => [ 'shape' => 'ConnectorProfileConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateConnectorProfileResponse' => [ 'type' => 'structure', 'members' => [ 'connectorProfileArn' => [ 'shape' => 'ConnectorProfileArn', ], ], ], 'CreateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', 'triggerConfig', 'sourceFlowConfig', 'destinationFlowConfigList', 'tasks', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'description' => [ 'shape' => 'FlowDescription', ], 'kmsArn' => [ 'shape' => 'KMSArn', ], 'triggerConfig' => [ 'shape' => 'TriggerConfig', ], 'sourceFlowConfig' => [ 'shape' => 'SourceFlowConfig', ], 'destinationFlowConfigList' => [ 'shape' => 'DestinationFlowConfigList', ], 'tasks' => [ 'shape' => 'Tasks', ], 'tags' => [ 'shape' => 'TagMap', ], 'metadataCatalogConfig' => [ 'shape' => 'MetadataCatalogConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateFlowResponse' => [ 'type' => 'structure', 'members' => [ 'flowArn' => [ 'shape' => 'FlowArn', ], 'flowStatus' => [ 'shape' => 'FlowStatus', ], ], ], 'CreatedBy' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'CredentialsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CredentialsMapKey', ], 'value' => [ 'shape' => 'CredentialsMapValue', ], 'max' => 50, 'min' => 0, ], 'CredentialsMapKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w]+', 'sensitive' => true, ], 'CredentialsMapValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', 'sensitive' => true, ], 'CustomAuthConfig' => [ 'type' => 'structure', 'members' => [ 'customAuthenticationType' => [ 'shape' => 'CustomAuthenticationType', ], 'authParameters' => [ 'shape' => 'AuthParameterList', ], ], ], 'CustomAuthConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomAuthConfig', ], ], 'CustomAuthCredentials' => [ 'type' => 'structure', 'required' => [ 'customAuthenticationType', ], 'members' => [ 'customAuthenticationType' => [ 'shape' => 'CustomAuthenticationType', ], 'credentialsMap' => [ 'shape' => 'CredentialsMap', ], ], ], 'CustomAuthenticationType' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'CustomConnectorDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'entityName', ], 'members' => [ 'entityName' => [ 'shape' => 'EntityName', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], 'writeOperationType' => [ 'shape' => 'WriteOperationType', ], 'idFieldNames' => [ 'shape' => 'IdFieldNameList', ], 'customProperties' => [ 'shape' => 'CustomProperties', ], ], ], 'CustomConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'authenticationType', ], 'members' => [ 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'basic' => [ 'shape' => 'BasicAuthCredentials', ], 'oauth2' => [ 'shape' => 'OAuth2Credentials', ], 'apiKey' => [ 'shape' => 'ApiKeyCredentials', ], 'custom' => [ 'shape' => 'CustomAuthCredentials', ], ], ], 'CustomConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [ 'profileProperties' => [ 'shape' => 'ProfilePropertiesMap', ], 'oAuth2Properties' => [ 'shape' => 'OAuth2Properties', ], ], ], 'CustomConnectorSourceProperties' => [ 'type' => 'structure', 'required' => [ 'entityName', ], 'members' => [ 'entityName' => [ 'shape' => 'EntityName', ], 'customProperties' => [ 'shape' => 'CustomProperties', ], 'dataTransferApi' => [ 'shape' => 'DataTransferApi', ], ], ], 'CustomProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomPropertyKey', ], 'value' => [ 'shape' => 'CustomPropertyValue', ], 'max' => 50, 'min' => 0, ], 'CustomPropertyKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w]+', ], 'CustomPropertyValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'CustomerProfilesDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'objectTypeName' => [ 'shape' => 'ObjectTypeName', ], ], ], 'CustomerProfilesMetadata' => [ 'type' => 'structure', 'members' => [], ], 'DataApiRoleArn' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:iam:.*:[0-9]+:.*', ], 'DataPullMode' => [ 'type' => 'string', 'enum' => [ 'Incremental', 'Complete', ], ], 'DataTransferApi' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DataTransferApiTypeName', ], 'Type' => [ 'shape' => 'DataTransferApiType', ], ], ], 'DataTransferApiType' => [ 'type' => 'string', 'enum' => [ 'SYNC', 'ASYNC', 'AUTOMATIC', ], ], 'DataTransferApiTypeName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\w/-]+', ], 'DatabaseName' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'DatabaseUrl' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'DatadogConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'BETWEEN', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'DatadogConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'apiKey', 'applicationKey', ], 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], 'applicationKey' => [ 'shape' => 'ApplicationKey', ], ], ], 'DatadogConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'DatadogMetadata' => [ 'type' => 'structure', 'members' => [], ], 'DatadogSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'Date' => [ 'type' => 'timestamp', ], 'DatetimeTypeFieldName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'DeleteConnectorProfileRequest' => [ 'type' => 'structure', 'required' => [ 'connectorProfileName', ], 'members' => [ 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'forceDelete' => [ 'shape' => 'Boolean', ], ], ], 'DeleteConnectorProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'forceDelete' => [ 'shape' => 'Boolean', ], ], ], 'DeleteFlowResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeConnectorEntityRequest' => [ 'type' => 'structure', 'required' => [ 'connectorEntityName', ], 'members' => [ 'connectorEntityName' => [ 'shape' => 'EntityName', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'apiVersion' => [ 'shape' => 'ApiVersion', ], ], ], 'DescribeConnectorEntityResponse' => [ 'type' => 'structure', 'required' => [ 'connectorEntityFields', ], 'members' => [ 'connectorEntityFields' => [ 'shape' => 'ConnectorEntityFieldList', ], ], ], 'DescribeConnectorProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'connectorProfileNames' => [ 'shape' => 'ConnectorProfileNameList', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConnectorProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'connectorProfileDetails' => [ 'shape' => 'ConnectorProfileDetailList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'connectorType', ], 'members' => [ 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], ], ], 'DescribeConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'connectorConfiguration' => [ 'shape' => 'ConnectorConfiguration', ], ], ], 'DescribeConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'connectorTypes' => [ 'shape' => 'ConnectorTypeList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'connectorConfigurations' => [ 'shape' => 'ConnectorConfigurationsMap', ], 'connectors' => [ 'shape' => 'ConnectorList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFlowExecutionRecordsRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFlowExecutionRecordsResponse' => [ 'type' => 'structure', 'members' => [ 'flowExecutions' => [ 'shape' => 'FlowExecutionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], ], ], 'DescribeFlowResponse' => [ 'type' => 'structure', 'members' => [ 'flowArn' => [ 'shape' => 'FlowArn', ], 'description' => [ 'shape' => 'FlowDescription', ], 'flowName' => [ 'shape' => 'FlowName', ], 'kmsArn' => [ 'shape' => 'KMSArn', ], 'flowStatus' => [ 'shape' => 'FlowStatus', ], 'flowStatusMessage' => [ 'shape' => 'FlowStatusMessage', ], 'sourceFlowConfig' => [ 'shape' => 'SourceFlowConfig', ], 'destinationFlowConfigList' => [ 'shape' => 'DestinationFlowConfigList', ], 'lastRunExecutionDetails' => [ 'shape' => 'ExecutionDetails', ], 'triggerConfig' => [ 'shape' => 'TriggerConfig', ], 'tasks' => [ 'shape' => 'Tasks', ], 'createdAt' => [ 'shape' => 'Date', ], 'lastUpdatedAt' => [ 'shape' => 'Date', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'lastUpdatedBy' => [ 'shape' => 'UpdatedBy', ], 'tags' => [ 'shape' => 'TagMap', ], 'metadataCatalogConfig' => [ 'shape' => 'MetadataCatalogConfig', ], 'lastRunMetadataCatalogDetails' => [ 'shape' => 'MetadataCatalogDetails', ], 'schemaVersion' => [ 'shape' => 'Long', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'DestinationConnectorProperties' => [ 'type' => 'structure', 'members' => [ 'Redshift' => [ 'shape' => 'RedshiftDestinationProperties', ], 'S3' => [ 'shape' => 'S3DestinationProperties', ], 'Salesforce' => [ 'shape' => 'SalesforceDestinationProperties', ], 'Snowflake' => [ 'shape' => 'SnowflakeDestinationProperties', ], 'EventBridge' => [ 'shape' => 'EventBridgeDestinationProperties', ], 'LookoutMetrics' => [ 'shape' => 'LookoutMetricsDestinationProperties', ], 'Upsolver' => [ 'shape' => 'UpsolverDestinationProperties', ], 'Honeycode' => [ 'shape' => 'HoneycodeDestinationProperties', ], 'CustomerProfiles' => [ 'shape' => 'CustomerProfilesDestinationProperties', ], 'Zendesk' => [ 'shape' => 'ZendeskDestinationProperties', ], 'Marketo' => [ 'shape' => 'MarketoDestinationProperties', ], 'CustomConnector' => [ 'shape' => 'CustomConnectorDestinationProperties', ], 'SAPOData' => [ 'shape' => 'SAPODataDestinationProperties', ], ], ], 'DestinationField' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'DestinationFieldProperties' => [ 'type' => 'structure', 'members' => [ 'isCreatable' => [ 'shape' => 'Boolean', ], 'isNullable' => [ 'shape' => 'Boolean', ], 'isUpsertable' => [ 'shape' => 'Boolean', ], 'isUpdatable' => [ 'shape' => 'Boolean', ], 'isDefaultedOnCreate' => [ 'shape' => 'Boolean', ], 'supportedWriteOperations' => [ 'shape' => 'SupportedWriteOperationList', ], ], ], 'DestinationFlowConfig' => [ 'type' => 'structure', 'required' => [ 'connectorType', 'destinationConnectorProperties', ], 'members' => [ 'connectorType' => [ 'shape' => 'ConnectorType', ], 'apiVersion' => [ 'shape' => 'ApiVersion', ], 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'destinationConnectorProperties' => [ 'shape' => 'DestinationConnectorProperties', ], ], ], 'DestinationFlowConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationFlowConfig', ], ], 'DocumentType' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\w_-]+', ], 'DomainName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '\\S+', ], 'Double' => [ 'type' => 'double', ], 'DynatraceConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'BETWEEN', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'DynatraceConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'apiToken', ], 'members' => [ 'apiToken' => [ 'shape' => 'ApiToken', ], ], ], 'DynatraceConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'DynatraceMetadata' => [ 'type' => 'structure', 'members' => [], ], 'DynatraceSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'EntitiesPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\s\\w/!@#+=,.-]*', ], 'EntityName' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '\\S+', ], 'ErrorHandlingConfig' => [ 'type' => 'structure', 'members' => [ 'failOnFirstDestinationError' => [ 'shape' => 'Boolean', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'bucketName' => [ 'shape' => 'BucketName', ], ], ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'putFailuresCount' => [ 'shape' => 'Long', ], 'executionMessage' => [ 'shape' => 'ExecutionMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'EventBridgeDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], ], ], 'EventBridgeMetadata' => [ 'type' => 'structure', 'members' => [], ], 'ExecutionDetails' => [ 'type' => 'structure', 'members' => [ 'mostRecentExecutionMessage' => [ 'shape' => 'MostRecentExecutionMessage', ], 'mostRecentExecutionTime' => [ 'shape' => 'Date', ], 'mostRecentExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], ], ], 'ExecutionId' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'ExecutionIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecutionId', ], 'max' => 100, 'min' => 0, ], 'ExecutionMessage' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'ExecutionRecord' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'ExecutionId', ], 'executionStatus' => [ 'shape' => 'ExecutionStatus', ], 'executionResult' => [ 'shape' => 'ExecutionResult', ], 'startedAt' => [ 'shape' => 'Date', ], 'lastUpdatedAt' => [ 'shape' => 'Date', ], 'dataPullStartTime' => [ 'shape' => 'Date', ], 'dataPullEndTime' => [ 'shape' => 'Date', ], 'metadataCatalogDetails' => [ 'shape' => 'MetadataCatalogDetails', ], ], ], 'ExecutionResult' => [ 'type' => 'structure', 'members' => [ 'errorInfo' => [ 'shape' => 'ErrorInfo', ], 'bytesProcessed' => [ 'shape' => 'Long', ], 'bytesWritten' => [ 'shape' => 'Long', ], 'recordsProcessed' => [ 'shape' => 'Long', ], 'numParallelProcesses' => [ 'shape' => 'Long', ], 'maxPageSize' => [ 'shape' => 'Long', ], ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Successful', 'Error', 'CancelStarted', 'Canceled', ], ], 'FieldType' => [ 'type' => 'string', ], 'FieldTypeDetails' => [ 'type' => 'structure', 'required' => [ 'fieldType', 'filterOperators', ], 'members' => [ 'fieldType' => [ 'shape' => 'FieldType', ], 'filterOperators' => [ 'shape' => 'FilterOperatorList', ], 'supportedValues' => [ 'shape' => 'SupportedValueList', ], 'valueRegexPattern' => [ 'shape' => 'String', ], 'supportedDateFormat' => [ 'shape' => 'String', ], 'fieldValueRange' => [ 'shape' => 'Range', ], 'fieldLengthRange' => [ 'shape' => 'Range', ], ], ], 'FileType' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', 'PARQUET', ], ], 'FilterOperatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operator', ], ], 'FlowArn' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:appflow:.*:[0-9]+:.*', ], 'FlowDefinition' => [ 'type' => 'structure', 'members' => [ 'flowArn' => [ 'shape' => 'FlowArn', ], 'description' => [ 'shape' => 'FlowDescription', ], 'flowName' => [ 'shape' => 'FlowName', ], 'flowStatus' => [ 'shape' => 'FlowStatus', ], 'sourceConnectorType' => [ 'shape' => 'ConnectorType', ], 'sourceConnectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'destinationConnectorType' => [ 'shape' => 'ConnectorType', ], 'destinationConnectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'triggerType' => [ 'shape' => 'TriggerType', ], 'createdAt' => [ 'shape' => 'Date', ], 'lastUpdatedAt' => [ 'shape' => 'Date', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'lastUpdatedBy' => [ 'shape' => 'UpdatedBy', ], 'tags' => [ 'shape' => 'TagMap', ], 'lastRunExecutionDetails' => [ 'shape' => 'ExecutionDetails', ], ], ], 'FlowDescription' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\w!@#\\-.?,\\s]*', ], 'FlowErrorDeactivationThreshold' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'FlowExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecutionRecord', ], ], 'FlowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowDefinition', ], ], 'FlowName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[a-zA-Z0-9][\\w!@#.-]+', ], 'FlowStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deprecated', 'Deleted', 'Draft', 'Errored', 'Suspended', ], ], 'FlowStatusMessage' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'GlueDataCatalogConfig' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'databaseName', 'tablePrefix', ], 'members' => [ 'roleArn' => [ 'shape' => 'GlueDataCatalogIAMRole', ], 'databaseName' => [ 'shape' => 'GlueDataCatalogDatabaseName', ], 'tablePrefix' => [ 'shape' => 'GlueDataCatalogTablePrefix', ], ], ], 'GlueDataCatalogDatabaseName' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'GlueDataCatalogIAMRole' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:iam:.*:[0-9]+:.*', ], 'GlueDataCatalogTablePrefix' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'GoogleAnalyticsConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'BETWEEN', ], ], 'GoogleAnalyticsConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'GoogleAnalyticsConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [], ], 'GoogleAnalyticsMetadata' => [ 'type' => 'structure', 'members' => [ 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], ], ], 'GoogleAnalyticsSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'Group' => [ 'type' => 'string', 'max' => 128, 'pattern' => '\\S+', ], 'HoneycodeConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'HoneycodeConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [], ], 'HoneycodeDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], ], ], 'HoneycodeMetadata' => [ 'type' => 'structure', 'members' => [ 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], ], ], 'IdFieldNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], 'min' => 0, ], 'Identifier' => [ 'type' => 'string', 'max' => 128, 'pattern' => '\\S+', ], 'IncrementalPullConfig' => [ 'type' => 'structure', 'members' => [ 'datetimeTypeFieldName' => [ 'shape' => 'DatetimeTypeFieldName', ], ], ], 'InforNexusConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'BETWEEN', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'InforNexusConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'accessKeyId', 'userId', 'secretAccessKey', 'datakey', ], 'members' => [ 'accessKeyId' => [ 'shape' => 'AccessKeyId', ], 'userId' => [ 'shape' => 'Username', ], 'secretAccessKey' => [ 'shape' => 'Key', ], 'datakey' => [ 'shape' => 'Key', ], ], ], 'InforNexusConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'InforNexusMetadata' => [ 'type' => 'structure', 'members' => [], ], 'InforNexusSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'InstanceUrl' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'JavaBoolean' => [ 'type' => 'boolean', ], 'JwtToken' => [ 'type' => 'string', 'max' => 8000, 'pattern' => '^([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_\\-\\+\\/=]*)', 'sensitive' => true, ], 'KMSArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:kms:.*:[0-9]+:.*', ], 'Key' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'Label' => [ 'type' => 'string', 'max' => 128, 'pattern' => '.*', ], 'LambdaConnectorProvisioningConfig' => [ 'type' => 'structure', 'required' => [ 'lambdaArn', ], 'members' => [ 'lambdaArn' => [ 'shape' => 'ARN', ], ], ], 'ListConnectorEntitiesRequest' => [ 'type' => 'structure', 'members' => [ 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'entitiesPath' => [ 'shape' => 'EntitiesPath', ], 'apiVersion' => [ 'shape' => 'ApiVersion', ], 'maxResults' => [ 'shape' => 'ListEntitiesMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorEntitiesResponse' => [ 'type' => 'structure', 'required' => [ 'connectorEntityMap', ], 'members' => [ 'connectorEntityMap' => [ 'shape' => 'ConnectorEntityMap', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'connectors' => [ 'shape' => 'ConnectorList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEntitiesMaxResults' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'ListFlowsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'flows' => [ 'shape' => 'FlowList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LogoURL' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'LogonLanguage' => [ 'type' => 'string', 'max' => 2, 'pattern' => '^[a-zA-Z0-9_]*$', ], 'Long' => [ 'type' => 'long', ], 'LookoutMetricsDestinationProperties' => [ 'type' => 'structure', 'members' => [], ], 'MarketoConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'MarketoConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'MarketoConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'MarketoDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], ], ], 'MarketoMetadata' => [ 'type' => 'structure', 'members' => [], ], 'MarketoSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MetadataCatalogConfig' => [ 'type' => 'structure', 'members' => [ 'glueDataCatalog' => [ 'shape' => 'GlueDataCatalogConfig', ], ], ], 'MetadataCatalogDetail' => [ 'type' => 'structure', 'members' => [ 'catalogType' => [ 'shape' => 'CatalogType', ], 'tableName' => [ 'shape' => 'String', ], 'tableRegistrationOutput' => [ 'shape' => 'RegistrationOutput', ], 'partitionRegistrationOutput' => [ 'shape' => 'RegistrationOutput', ], ], ], 'MetadataCatalogDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataCatalogDetail', ], ], 'MostRecentExecutionMessage' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\w!@#\\-.?,\\s]*', ], 'Name' => [ 'type' => 'string', 'max' => 128, 'pattern' => '\\S+', ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'OAuth2Credentials' => [ 'type' => 'structure', 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'OAuth2CustomParameter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'isRequired' => [ 'shape' => 'Boolean', ], 'label' => [ 'shape' => 'Label', ], 'description' => [ 'shape' => 'Description', ], 'isSensitiveField' => [ 'shape' => 'Boolean', ], 'connectorSuppliedValues' => [ 'shape' => 'ConnectorSuppliedValueList', ], 'type' => [ 'shape' => 'OAuth2CustomPropType', ], ], ], 'OAuth2CustomPropType' => [ 'type' => 'string', 'enum' => [ 'TOKEN_URL', 'AUTH_URL', ], ], 'OAuth2CustomPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OAuth2CustomParameter', ], ], 'OAuth2Defaults' => [ 'type' => 'structure', 'members' => [ 'oauthScopes' => [ 'shape' => 'OAuthScopeList', ], 'tokenUrls' => [ 'shape' => 'TokenUrlList', ], 'authCodeUrls' => [ 'shape' => 'AuthCodeUrlList', ], 'oauth2GrantTypesSupported' => [ 'shape' => 'OAuth2GrantTypeSupportedList', ], 'oauth2CustomProperties' => [ 'shape' => 'OAuth2CustomPropertiesList', ], ], ], 'OAuth2GrantType' => [ 'type' => 'string', 'enum' => [ 'CLIENT_CREDENTIALS', 'AUTHORIZATION_CODE', 'JWT_BEARER', ], ], 'OAuth2GrantTypeSupportedList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OAuth2GrantType', ], ], 'OAuth2Properties' => [ 'type' => 'structure', 'required' => [ 'tokenUrl', 'oAuth2GrantType', ], 'members' => [ 'tokenUrl' => [ 'shape' => 'TokenUrl', ], 'oAuth2GrantType' => [ 'shape' => 'OAuth2GrantType', ], 'tokenUrlCustomProperties' => [ 'shape' => 'TokenUrlCustomProperties', ], ], ], 'OAuthCredentials' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'OAuthProperties' => [ 'type' => 'structure', 'required' => [ 'tokenUrl', 'authCodeUrl', 'oAuthScopes', ], 'members' => [ 'tokenUrl' => [ 'shape' => 'TokenUrl', ], 'authCodeUrl' => [ 'shape' => 'AuthCodeUrl', ], 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], ], ], 'OAuthScope' => [ 'type' => 'string', 'max' => 128, 'pattern' => '\\S+', ], 'OAuthScopeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OAuthScope', ], ], 'Object' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ObjectTypeName' => [ 'type' => 'string', 'max' => 255, 'pattern' => '\\S+', ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'CONTAINS', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'OperatorPropertiesKeys' => [ 'type' => 'string', 'enum' => [ 'VALUE', 'VALUES', 'DATA_TYPE', 'UPPER_BOUND', 'LOWER_BOUND', 'SOURCE_DATA_TYPE', 'DESTINATION_DATA_TYPE', 'VALIDATION_ACTION', 'MASK_VALUE', 'MASK_LENGTH', 'TRUNCATE_LENGTH', 'MATH_OPERATION_FIELDS_ORDER', 'CONCAT_FORMAT', 'SUBFIELD_CATEGORY_MAP', 'EXCLUDE_SOURCE_FIELDS_LIST', 'INCLUDE_NEW_FIELDS', 'ORDERED_PARTITION_KEYS_LIST', ], ], 'Operators' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'CONTAINS', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'PardotConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'EQUAL_TO', 'NO_OP', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', ], ], 'PardotConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], 'clientCredentialsArn' => [ 'shape' => 'ClientCredentialsArn', ], ], ], 'PardotConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], 'isSandboxEnvironment' => [ 'shape' => 'Boolean', ], 'businessUnitId' => [ 'shape' => 'BusinessUnitId', ], ], ], 'PardotMetadata' => [ 'type' => 'structure', 'members' => [], ], 'PardotSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'Password' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', 'sensitive' => true, ], 'PathPrefix' => [ 'type' => 'string', 'enum' => [ 'EXECUTION_ID', 'SCHEMA_VERSION', ], ], 'PathPrefixHierarchy' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathPrefix', ], ], 'PortNumber' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'PrefixConfig' => [ 'type' => 'structure', 'members' => [ 'prefixType' => [ 'shape' => 'PrefixType', ], 'prefixFormat' => [ 'shape' => 'PrefixFormat', ], 'pathPrefixHierarchy' => [ 'shape' => 'PathPrefixHierarchy', ], ], ], 'PrefixFormat' => [ 'type' => 'string', 'enum' => [ 'YEAR', 'MONTH', 'DAY', 'HOUR', 'MINUTE', ], ], 'PrefixType' => [ 'type' => 'string', 'enum' => [ 'FILENAME', 'PATH', 'PATH_AND_FILENAME', ], ], 'PrivateConnectionProvisioningFailureCause' => [ 'type' => 'string', 'enum' => [ 'CONNECTOR_AUTHENTICATION', 'CONNECTOR_SERVER', 'INTERNAL_SERVER', 'ACCESS_DENIED', 'VALIDATION', ], ], 'PrivateConnectionProvisioningFailureMessage' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'PrivateConnectionProvisioningState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'PrivateConnectionProvisioningStatus', ], 'failureMessage' => [ 'shape' => 'PrivateConnectionProvisioningFailureMessage', ], 'failureCause' => [ 'shape' => 'PrivateConnectionProvisioningFailureCause', ], ], ], 'PrivateConnectionProvisioningStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'PENDING', 'CREATED', ], ], 'PrivateLinkServiceName' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^$|com.amazonaws.vpce.[\\w/!:@#.\\-]+', ], 'ProfilePropertiesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ProfilePropertyKey', ], 'value' => [ 'shape' => 'ProfilePropertyValue', ], 'max' => 50, 'min' => 0, ], 'ProfilePropertyKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w]+', ], 'ProfilePropertyValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'Property' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.+', ], 'Range' => [ 'type' => 'structure', 'members' => [ 'maximum' => [ 'shape' => 'Double', ], 'minimum' => [ 'shape' => 'Double', ], ], ], 'RedirectUri' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'RedshiftConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'username' => [ 'shape' => 'String', ], 'password' => [ 'shape' => 'Password', ], ], ], 'RedshiftConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'roleArn', ], 'members' => [ 'databaseUrl' => [ 'shape' => 'DatabaseUrl', ], 'bucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataApiRoleArn' => [ 'shape' => 'DataApiRoleArn', ], 'isRedshiftServerless' => [ 'shape' => 'Boolean', ], 'clusterIdentifier' => [ 'shape' => 'ClusterIdentifier', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], ], ], 'RedshiftDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', 'intermediateBucketName', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'intermediateBucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], ], ], 'RedshiftMetadata' => [ 'type' => 'structure', 'members' => [], ], 'RefreshToken' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '\\S+', ], 'Region' => [ 'type' => 'string', 'max' => 64, 'pattern' => '\\S+', ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegisterConnectorRequest' => [ 'type' => 'structure', 'members' => [ 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'description' => [ 'shape' => 'Description', ], 'connectorProvisioningType' => [ 'shape' => 'ConnectorProvisioningType', ], 'connectorProvisioningConfig' => [ 'shape' => 'ConnectorProvisioningConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'RegisterConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'connectorArn' => [ 'shape' => 'ARN', ], ], ], 'RegisteredBy' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'RegistrationOutput' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'result' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'ExecutionStatus', ], ], ], 'ResetConnectorMetadataCacheRequest' => [ 'type' => 'structure', 'members' => [ 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'connectorType' => [ 'shape' => 'ConnectorType', ], 'connectorEntityName' => [ 'shape' => 'EntityName', ], 'entitiesPath' => [ 'shape' => 'EntitiesPath', ], 'apiVersion' => [ 'shape' => 'ApiVersion', ], ], ], 'ResetConnectorMetadataCacheResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:iam:.*:[0-9]+:.*', ], 'S3ConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'S3DestinationProperties' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 's3OutputFormatConfig' => [ 'shape' => 'S3OutputFormatConfig', ], ], ], 'S3InputFileType' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'S3InputFormatConfig' => [ 'type' => 'structure', 'members' => [ 's3InputFileType' => [ 'shape' => 'S3InputFileType', ], ], ], 'S3Metadata' => [ 'type' => 'structure', 'members' => [], ], 'S3OutputFormatConfig' => [ 'type' => 'structure', 'members' => [ 'fileType' => [ 'shape' => 'FileType', ], 'prefixConfig' => [ 'shape' => 'PrefixConfig', ], 'aggregationConfig' => [ 'shape' => 'AggregationConfig', ], 'preserveSourceDataTyping' => [ 'shape' => 'JavaBoolean', ], ], ], 'S3SourceProperties' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 's3InputFormatConfig' => [ 'shape' => 'S3InputFormatConfig', ], ], ], 'SAPODataConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'CONTAINS', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'SAPODataConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'oAuthCredentials' => [ 'shape' => 'OAuthCredentials', ], ], ], 'SAPODataConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'applicationHostUrl', 'applicationServicePath', 'portNumber', 'clientNumber', ], 'members' => [ 'applicationHostUrl' => [ 'shape' => 'ApplicationHostUrl', ], 'applicationServicePath' => [ 'shape' => 'ApplicationServicePath', ], 'portNumber' => [ 'shape' => 'PortNumber', 'box' => true, ], 'clientNumber' => [ 'shape' => 'ClientNumber', ], 'logonLanguage' => [ 'shape' => 'LogonLanguage', ], 'privateLinkServiceName' => [ 'shape' => 'PrivateLinkServiceName', ], 'oAuthProperties' => [ 'shape' => 'OAuthProperties', ], 'disableSSO' => [ 'shape' => 'Boolean', ], ], ], 'SAPODataDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'objectPath', ], 'members' => [ 'objectPath' => [ 'shape' => 'Object', ], 'successResponseHandlingConfig' => [ 'shape' => 'SuccessResponseHandlingConfig', ], 'idFieldNames' => [ 'shape' => 'IdFieldNameList', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], 'writeOperationType' => [ 'shape' => 'WriteOperationType', ], ], ], 'SAPODataMaxPageSize' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'SAPODataMaxParallelism' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'SAPODataMetadata' => [ 'type' => 'structure', 'members' => [], ], 'SAPODataPaginationConfig' => [ 'type' => 'structure', 'required' => [ 'maxPageSize', ], 'members' => [ 'maxPageSize' => [ 'shape' => 'SAPODataMaxPageSize', 'box' => true, ], ], ], 'SAPODataParallelismConfig' => [ 'type' => 'structure', 'required' => [ 'maxParallelism', ], 'members' => [ 'maxParallelism' => [ 'shape' => 'SAPODataMaxParallelism', 'box' => true, ], ], ], 'SAPODataSourceProperties' => [ 'type' => 'structure', 'members' => [ 'objectPath' => [ 'shape' => 'Object', ], 'parallelismConfig' => [ 'shape' => 'SAPODataParallelismConfig', ], 'paginationConfig' => [ 'shape' => 'SAPODataPaginationConfig', ], ], ], 'SalesforceConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'CONTAINS', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'SalesforceConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'accessToken' => [ 'shape' => 'AccessToken', ], 'refreshToken' => [ 'shape' => 'RefreshToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], 'clientCredentialsArn' => [ 'shape' => 'ClientCredentialsArn', ], 'oAuth2GrantType' => [ 'shape' => 'OAuth2GrantType', ], 'jwtToken' => [ 'shape' => 'JwtToken', ], ], ], 'SalesforceConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], 'isSandboxEnvironment' => [ 'shape' => 'Boolean', ], 'usePrivateLinkForMetadataAndAuthorization' => [ 'shape' => 'Boolean', ], ], ], 'SalesforceDataTransferApi' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'BULKV2', 'REST_SYNC', ], ], 'SalesforceDataTransferApiList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesforceDataTransferApi', ], ], 'SalesforceDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'idFieldNames' => [ 'shape' => 'IdFieldNameList', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], 'writeOperationType' => [ 'shape' => 'WriteOperationType', ], 'dataTransferApi' => [ 'shape' => 'SalesforceDataTransferApi', ], ], ], 'SalesforceMetadata' => [ 'type' => 'structure', 'members' => [ 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], 'dataTransferApis' => [ 'shape' => 'SalesforceDataTransferApiList', ], 'oauth2GrantTypesSupported' => [ 'shape' => 'OAuth2GrantTypeSupportedList', ], ], ], 'SalesforceSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'enableDynamicFieldUpdate' => [ 'shape' => 'Boolean', ], 'includeDeletedRecords' => [ 'shape' => 'Boolean', ], 'dataTransferApi' => [ 'shape' => 'SalesforceDataTransferApi', ], ], ], 'ScheduleExpression' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ScheduleFrequencyType' => [ 'type' => 'string', 'enum' => [ 'BYMINUTE', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'ONCE', ], ], 'ScheduleOffset' => [ 'type' => 'long', 'max' => 36000, 'min' => 0, ], 'ScheduledTriggerProperties' => [ 'type' => 'structure', 'required' => [ 'scheduleExpression', ], 'members' => [ 'scheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'dataPullMode' => [ 'shape' => 'DataPullMode', ], 'scheduleStartTime' => [ 'shape' => 'Date', ], 'scheduleEndTime' => [ 'shape' => 'Date', ], 'timezone' => [ 'shape' => 'Timezone', ], 'scheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'firstExecutionFrom' => [ 'shape' => 'Date', ], 'flowErrorDeactivationThreshold' => [ 'shape' => 'FlowErrorDeactivationThreshold', 'box' => true, ], ], ], 'SchedulingFrequencyTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleFrequencyType', ], ], 'SecretKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', 'sensitive' => true, ], 'ServiceNowConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'CONTAINS', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'ServiceNowConnectorProfileCredentials' => [ 'type' => 'structure', 'members' => [ 'username' => [ 'shape' => 'Username', ], 'password' => [ 'shape' => 'Password', ], 'oAuth2Credentials' => [ 'shape' => 'OAuth2Credentials', ], ], ], 'ServiceNowConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'ServiceNowMetadata' => [ 'type' => 'structure', 'members' => [], ], 'ServiceNowSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SingularConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'SingularConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'apiKey', ], 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], ], ], 'SingularConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [], ], 'SingularMetadata' => [ 'type' => 'structure', 'members' => [], ], 'SingularSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'SlackConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'SlackConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'SlackConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'SlackMetadata' => [ 'type' => 'structure', 'members' => [ 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], ], ], 'SlackSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'SnowflakeConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'username', 'password', ], 'members' => [ 'username' => [ 'shape' => 'Username', ], 'password' => [ 'shape' => 'Password', ], ], ], 'SnowflakeConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'warehouse', 'stage', 'bucketName', ], 'members' => [ 'warehouse' => [ 'shape' => 'Warehouse', ], 'stage' => [ 'shape' => 'Stage', ], 'bucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'privateLinkServiceName' => [ 'shape' => 'PrivateLinkServiceName', ], 'accountName' => [ 'shape' => 'AccountName', ], 'region' => [ 'shape' => 'Region', ], ], ], 'SnowflakeDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', 'intermediateBucketName', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'intermediateBucketName' => [ 'shape' => 'BucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], ], ], 'SnowflakeMetadata' => [ 'type' => 'structure', 'members' => [ 'supportedRegions' => [ 'shape' => 'RegionList', ], ], ], 'SourceConnectorProperties' => [ 'type' => 'structure', 'members' => [ 'Amplitude' => [ 'shape' => 'AmplitudeSourceProperties', ], 'Datadog' => [ 'shape' => 'DatadogSourceProperties', ], 'Dynatrace' => [ 'shape' => 'DynatraceSourceProperties', ], 'GoogleAnalytics' => [ 'shape' => 'GoogleAnalyticsSourceProperties', ], 'InforNexus' => [ 'shape' => 'InforNexusSourceProperties', ], 'Marketo' => [ 'shape' => 'MarketoSourceProperties', ], 'S3' => [ 'shape' => 'S3SourceProperties', ], 'Salesforce' => [ 'shape' => 'SalesforceSourceProperties', ], 'ServiceNow' => [ 'shape' => 'ServiceNowSourceProperties', ], 'Singular' => [ 'shape' => 'SingularSourceProperties', ], 'Slack' => [ 'shape' => 'SlackSourceProperties', ], 'Trendmicro' => [ 'shape' => 'TrendmicroSourceProperties', ], 'Veeva' => [ 'shape' => 'VeevaSourceProperties', ], 'Zendesk' => [ 'shape' => 'ZendeskSourceProperties', ], 'SAPOData' => [ 'shape' => 'SAPODataSourceProperties', ], 'CustomConnector' => [ 'shape' => 'CustomConnectorSourceProperties', ], 'Pardot' => [ 'shape' => 'PardotSourceProperties', ], ], ], 'SourceFieldProperties' => [ 'type' => 'structure', 'members' => [ 'isRetrievable' => [ 'shape' => 'Boolean', ], 'isQueryable' => [ 'shape' => 'Boolean', ], 'isTimestampFieldForIncrementalQueries' => [ 'shape' => 'Boolean', ], ], ], 'SourceFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SourceFlowConfig' => [ 'type' => 'structure', 'required' => [ 'connectorType', 'sourceConnectorProperties', ], 'members' => [ 'connectorType' => [ 'shape' => 'ConnectorType', ], 'apiVersion' => [ 'shape' => 'ApiVersion', ], 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'sourceConnectorProperties' => [ 'shape' => 'SourceConnectorProperties', ], 'incrementalPullConfig' => [ 'shape' => 'IncrementalPullConfig', ], ], ], 'Stage' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'StartFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartFlowResponse' => [ 'type' => 'structure', 'members' => [ 'flowArn' => [ 'shape' => 'FlowArn', ], 'flowStatus' => [ 'shape' => 'FlowStatus', ], 'executionId' => [ 'shape' => 'ExecutionId', ], ], ], 'StopFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], ], ], 'StopFlowResponse' => [ 'type' => 'structure', 'members' => [ 'flowArn' => [ 'shape' => 'FlowArn', ], 'flowStatus' => [ 'shape' => 'FlowStatus', ], ], ], 'String' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.*', ], 'SuccessResponseHandlingConfig' => [ 'type' => 'structure', 'members' => [ 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 'bucketName' => [ 'shape' => 'BucketName', ], ], ], 'SupportedApiVersion' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'SupportedApiVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedApiVersion', ], ], 'SupportedDataTransferApis' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataTransferApi', ], ], 'SupportedDataTransferType' => [ 'type' => 'string', 'enum' => [ 'RECORD', 'FILE', ], ], 'SupportedDataTransferTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedDataTransferType', ], ], 'SupportedFieldTypeDetails' => [ 'type' => 'structure', 'required' => [ 'v1', ], 'members' => [ 'v1' => [ 'shape' => 'FieldTypeDetails', ], ], ], 'SupportedOperatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operators', ], ], 'SupportedValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], ], 'SupportedWriteOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WriteOperationType', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\s\\w+-=\\.:/@]*', ], 'Task' => [ 'type' => 'structure', 'required' => [ 'sourceFields', 'taskType', ], 'members' => [ 'sourceFields' => [ 'shape' => 'SourceFields', ], 'connectorOperator' => [ 'shape' => 'ConnectorOperator', ], 'destinationField' => [ 'shape' => 'DestinationField', ], 'taskType' => [ 'shape' => 'TaskType', ], 'taskProperties' => [ 'shape' => 'TaskPropertiesMap', ], ], ], 'TaskPropertiesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OperatorPropertiesKeys', ], 'value' => [ 'shape' => 'Property', ], ], 'TaskType' => [ 'type' => 'string', 'enum' => [ 'Arithmetic', 'Filter', 'Map', 'Map_all', 'Mask', 'Merge', 'Passthrough', 'Truncate', 'Validate', 'Partition', ], ], 'Tasks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Task', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timezone' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'TokenUrl' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'TokenUrlCustomProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomPropertyKey', ], 'value' => [ 'shape' => 'CustomPropertyValue', ], 'max' => 50, 'min' => 0, ], 'TokenUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TokenUrl', ], ], 'TrendmicroConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'TrendmicroConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'apiSecretKey', ], 'members' => [ 'apiSecretKey' => [ 'shape' => 'ApiSecretKey', ], ], ], 'TrendmicroConnectorProfileProperties' => [ 'type' => 'structure', 'members' => [], ], 'TrendmicroMetadata' => [ 'type' => 'structure', 'members' => [], ], 'TrendmicroSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], 'TriggerConfig' => [ 'type' => 'structure', 'required' => [ 'triggerType', ], 'members' => [ 'triggerType' => [ 'shape' => 'TriggerType', ], 'triggerProperties' => [ 'shape' => 'TriggerProperties', ], ], ], 'TriggerProperties' => [ 'type' => 'structure', 'members' => [ 'Scheduled' => [ 'shape' => 'ScheduledTriggerProperties', ], ], ], 'TriggerType' => [ 'type' => 'string', 'enum' => [ 'Scheduled', 'Event', 'OnDemand', ], ], 'TriggerTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TriggerType', ], ], 'UnregisterConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'connectorLabel', ], 'members' => [ 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'forceDelete' => [ 'shape' => 'Boolean', ], ], ], 'UnregisterConnectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectorProfileRequest' => [ 'type' => 'structure', 'required' => [ 'connectorProfileName', 'connectionMode', 'connectorProfileConfig', ], 'members' => [ 'connectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'connectionMode' => [ 'shape' => 'ConnectionMode', ], 'connectorProfileConfig' => [ 'shape' => 'ConnectorProfileConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateConnectorProfileResponse' => [ 'type' => 'structure', 'members' => [ 'connectorProfileArn' => [ 'shape' => 'ConnectorProfileArn', ], ], ], 'UpdateConnectorRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'connectorLabel', ], 'members' => [ 'connectorLabel' => [ 'shape' => 'ConnectorLabel', ], 'description' => [ 'shape' => 'Description', ], 'connectorProvisioningConfig' => [ 'shape' => 'ConnectorProvisioningConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateConnectorRegistrationResponse' => [ 'type' => 'structure', 'members' => [ 'connectorArn' => [ 'shape' => 'ARN', ], ], ], 'UpdateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowName', 'triggerConfig', 'sourceFlowConfig', 'destinationFlowConfigList', 'tasks', ], 'members' => [ 'flowName' => [ 'shape' => 'FlowName', ], 'description' => [ 'shape' => 'FlowDescription', ], 'triggerConfig' => [ 'shape' => 'TriggerConfig', ], 'sourceFlowConfig' => [ 'shape' => 'SourceFlowConfig', ], 'destinationFlowConfigList' => [ 'shape' => 'DestinationFlowConfigList', ], 'tasks' => [ 'shape' => 'Tasks', ], 'metadataCatalogConfig' => [ 'shape' => 'MetadataCatalogConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateFlowResponse' => [ 'type' => 'structure', 'members' => [ 'flowStatus' => [ 'shape' => 'FlowStatus', ], ], ], 'UpdatedBy' => [ 'type' => 'string', 'max' => 256, 'pattern' => '\\S+', ], 'UpsolverBucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 16, 'pattern' => '^(upsolver-appflow)\\S*', ], 'UpsolverDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'bucketName', 's3OutputFormatConfig', ], 'members' => [ 'bucketName' => [ 'shape' => 'UpsolverBucketName', ], 'bucketPrefix' => [ 'shape' => 'BucketPrefix', ], 's3OutputFormatConfig' => [ 'shape' => 'UpsolverS3OutputFormatConfig', ], ], ], 'UpsolverMetadata' => [ 'type' => 'structure', 'members' => [], ], 'UpsolverS3OutputFormatConfig' => [ 'type' => 'structure', 'required' => [ 'prefixConfig', ], 'members' => [ 'fileType' => [ 'shape' => 'FileType', ], 'prefixConfig' => [ 'shape' => 'PrefixConfig', ], 'aggregationConfig' => [ 'shape' => 'AggregationConfig', ], ], ], 'Username' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 128, 'pattern' => '\\S+', ], 'VeevaConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'CONTAINS', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'VeevaConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'username', 'password', ], 'members' => [ 'username' => [ 'shape' => 'Username', ], 'password' => [ 'shape' => 'Password', ], ], ], 'VeevaConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'VeevaMetadata' => [ 'type' => 'structure', 'members' => [], ], 'VeevaSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'documentType' => [ 'shape' => 'DocumentType', ], 'includeSourceFiles' => [ 'shape' => 'Boolean', ], 'includeRenditions' => [ 'shape' => 'Boolean', ], 'includeAllVersions' => [ 'shape' => 'Boolean', ], ], ], 'Warehouse' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\w/!@#+=.-]*', ], 'WorkgroupName' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'WriteOperationType' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'UPSERT', 'UPDATE', 'DELETE', ], ], 'ZendeskConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'GREATER_THAN', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'ZendeskConnectorProfileCredentials' => [ 'type' => 'structure', 'required' => [ 'clientId', 'clientSecret', ], 'members' => [ 'clientId' => [ 'shape' => 'ClientId', ], 'clientSecret' => [ 'shape' => 'ClientSecret', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'oAuthRequest' => [ 'shape' => 'ConnectorOAuthRequest', ], ], ], 'ZendeskConnectorProfileProperties' => [ 'type' => 'structure', 'required' => [ 'instanceUrl', ], 'members' => [ 'instanceUrl' => [ 'shape' => 'InstanceUrl', ], ], ], 'ZendeskDestinationProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], 'idFieldNames' => [ 'shape' => 'IdFieldNameList', ], 'errorHandlingConfig' => [ 'shape' => 'ErrorHandlingConfig', ], 'writeOperationType' => [ 'shape' => 'WriteOperationType', ], ], ], 'ZendeskMetadata' => [ 'type' => 'structure', 'members' => [ 'oAuthScopes' => [ 'shape' => 'OAuthScopeList', ], ], ], 'ZendeskSourceProperties' => [ 'type' => 'structure', 'required' => [ 'object', ], 'members' => [ 'object' => [ 'shape' => 'Object', ], ], ], ],];
