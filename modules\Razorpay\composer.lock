{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "6122aba153572de4a11715202939c541", "packages": [{"name": "razorpay/razorpay", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/razorpay/razorpay-php.git", "reference": "6dad785301f6391fad108dc0abccea662ad7f93b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/razorpay/razorpay-php/zipball/6dad785301f6391fad108dc0abccea662ad7f93b", "reference": "6dad785301f6391fad108dc0abccea662ad7f93b", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0", "rmccue/requests": "v1.7.0"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0", "raveren/kint": "1.*"}, "type": "library", "autoload": {"psr-4": {"Razorpay\\Api\\": "src/", "Razorpay\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://captnemo.in", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Razorpay PHP Client Library", "homepage": "https://docs.razorpay.com", "keywords": ["api", "client", "php", "razorpay"], "support": {"email": "<EMAIL>", "issues": "https://github.com/Razorpay/razorpay-php/issues", "source": "https://github.com/Razorpay/razorpay-php"}, "time": "2021-04-05T05:10:01+00:00"}, {"name": "rmccue/requests", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/rmccue/Requests.git", "reference": "87932f52ffad70504d93f04f15690cf16a089546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmccue/Requests/zipball/87932f52ffad70504d93f04f15690cf16a089546", "reference": "87932f52ffad70504d93f04f15690cf16a089546", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"requests/test-server": "dev-master"}, "type": "library", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/rmccue/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"issues": "https://github.com/rmccue/Requests/issues", "source": "https://github.com/rmccue/Requests/tree/master"}, "time": "2016-10-13T00:11:37+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.0.0"}