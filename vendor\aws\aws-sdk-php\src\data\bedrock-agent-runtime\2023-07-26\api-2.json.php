<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent-runtime/2023-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-07-26', 'endpointPrefix' => 'bedrock-agent-runtime', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Agents for Amazon Bedrock Runtime', 'serviceId' => 'Bedrock Agent Runtime', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-agent-runtime-2023-07-26', ], 'operations' => [ 'InvokeAgent' => [ 'name' => 'InvokeAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/sessions/{sessionId}/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeAgentRequest', ], 'output' => [ 'shape' => 'InvokeAgentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'Retrieve' => [ 'name' => 'Retrieve', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/retrieve', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveRequest', ], 'output' => [ 'shape' => 'RetrieveResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RetrieveAndGenerate' => [ 'name' => 'RetrieveAndGenerate', 'http' => [ 'method' => 'POST', 'requestUri' => '/retrieveAndGenerate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveAndGenerateRequest', ], 'output' => [ 'shape' => 'RetrieveAndGenerateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionGroupInvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupName' => [ 'shape' => 'ActionGroupName', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'parameters' => [ 'shape' => 'Parameters', ], 'requestBody' => [ 'shape' => 'RequestBody', ], 'verb' => [ 'shape' => 'Verb', ], ], ], 'ActionGroupInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'ActionGroupOutputString', ], ], ], 'ActionGroupName' => [ 'type' => 'string', 'sensitive' => true, ], 'ActionGroupOutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'AgentAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'AgentId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'ApiPath' => [ 'type' => 'string', 'sensitive' => true, ], 'Attribution' => [ 'type' => 'structure', 'members' => [ 'citations' => [ 'shape' => 'Citations', ], ], ], 'BadGatewayException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, 'fault' => true, ], 'BedrockModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}))$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Citation' => [ 'type' => 'structure', 'members' => [ 'generatedResponsePart' => [ 'shape' => 'GeneratedResponsePart', ], 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'Citations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Citation', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Parameters', ], ], 'CreationMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'OVERRIDDEN', ], ], 'DependencyFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'FailureReasonString' => [ 'type' => 'string', 'sensitive' => true, ], 'FailureTrace' => [ 'type' => 'structure', 'members' => [ 'failureReason' => [ 'shape' => 'FailureReasonString', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'FilterAttribute' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'FilterKey', ], 'value' => [ 'shape' => 'FilterValue', ], ], ], 'FilterKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'FilterValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'FinalResponse' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'FinalResponseString', ], ], ], 'FinalResponseString' => [ 'type' => 'string', 'sensitive' => true, ], 'GeneratedResponsePart' => [ 'type' => 'structure', 'members' => [ 'textResponsePart' => [ 'shape' => 'TextResponsePart', ], ], ], 'GenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], ], ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maximumLength' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topK' => [ 'shape' => 'TopK', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'InputText' => [ 'type' => 'string', 'max' => 25000000, 'min' => 0, 'sensitive' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationInput' => [ 'shape' => 'ActionGroupInvocationInput', ], 'invocationType' => [ 'shape' => 'InvocationType', ], 'knowledgeBaseLookupInput' => [ 'shape' => 'KnowledgeBaseLookupInput', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'InvocationType' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'FINISH', ], ], 'InvokeAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', 'inputText', 'sessionId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'enableTrace' => [ 'shape' => 'Boolean', ], 'endSession' => [ 'shape' => 'Boolean', ], 'inputText' => [ 'shape' => 'InputText', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'sessionState' => [ 'shape' => 'SessionState', ], ], ], 'InvokeAgentResponse' => [ 'type' => 'structure', 'required' => [ 'completion', 'contentType', 'sessionId', ], 'members' => [ 'completion' => [ 'shape' => 'ResponseStream', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'x-amzn-bedrock-agent-content-type', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-session-id', ], ], 'payload' => 'completion', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'KnowledgeBaseId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'KnowledgeBaseLookupInput' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'TraceKnowledgeBaseId', ], 'text' => [ 'shape' => 'KnowledgeBaseLookupInputString', ], ], ], 'KnowledgeBaseLookupInputString' => [ 'type' => 'string', 'sensitive' => true, ], 'KnowledgeBaseLookupOutput' => [ 'type' => 'structure', 'members' => [ 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'KnowledgeBaseQuery' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'KnowledgeBaseQueryTextString', ], ], 'sensitive' => true, ], 'KnowledgeBaseQueryTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'KnowledgeBaseRetrievalConfiguration' => [ 'type' => 'structure', 'required' => [ 'vectorSearchConfiguration', ], 'members' => [ 'vectorSearchConfiguration' => [ 'shape' => 'KnowledgeBaseVectorSearchConfiguration', ], ], ], 'KnowledgeBaseRetrievalResult' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], 'score' => [ 'shape' => 'Double', ], ], ], 'KnowledgeBaseRetrievalResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseRetrievalResult', ], 'sensitive' => true, ], 'KnowledgeBaseRetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'modelArn', ], 'members' => [ 'generationConfiguration' => [ 'shape' => 'GenerationConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseVectorSearchConfiguration' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'RetrievalFilter', ], 'numberOfResults' => [ 'shape' => 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger', 'box' => true, ], 'overrideSearchType' => [ 'shape' => 'SearchType', ], ], ], 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'LambdaArn' => [ 'type' => 'string', ], 'MaximumLength' => [ 'type' => 'integer', 'box' => true, 'max' => 4096, 'min' => 0, ], 'MimeType' => [ 'type' => 'string', ], 'ModelInvocationInput' => [ 'type' => 'structure', 'members' => [ 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'overrideLambda' => [ 'shape' => 'LambdaArn', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'text' => [ 'shape' => 'PromptText', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'PromptType', ], ], 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S*$', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'Observation' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationOutput' => [ 'shape' => 'ActionGroupInvocationOutput', ], 'finalResponse' => [ 'shape' => 'FinalResponse', ], 'knowledgeBaseLookupOutput' => [ 'shape' => 'KnowledgeBaseLookupOutput', ], 'repromptResponse' => [ 'shape' => 'RepromptResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'Type', ], ], 'sensitive' => true, ], 'OrchestrationTrace' => [ 'type' => 'structure', 'members' => [ 'invocationInput' => [ 'shape' => 'InvocationInput', ], 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'observation' => [ 'shape' => 'Observation', ], 'rationale' => [ 'shape' => 'Rationale', ], ], 'sensitive' => true, 'union' => true, ], 'OutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PartBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'PayloadPart' => [ 'type' => 'structure', 'members' => [ 'attribution' => [ 'shape' => 'Attribution', ], 'bytes' => [ 'shape' => 'PartBody', ], ], 'event' => true, 'sensitive' => true, ], 'PostProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'parsedResponse' => [ 'shape' => 'PostProcessingParsedResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PostProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'OutputString', ], ], 'sensitive' => true, ], 'PostProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PostProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PreProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'parsedResponse' => [ 'shape' => 'PreProcessingParsedResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PreProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'isValid' => [ 'shape' => 'Boolean', ], 'rationale' => [ 'shape' => 'RationaleString', ], ], 'sensitive' => true, ], 'PreProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PreProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PromptSessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'PromptTemplate' => [ 'type' => 'structure', 'members' => [ 'textPromptTemplate' => [ 'shape' => 'TextPromptTemplate', ], ], ], 'PromptText' => [ 'type' => 'string', 'sensitive' => true, ], 'PromptType' => [ 'type' => 'string', 'enum' => [ 'PRE_PROCESSING', 'ORCHESTRATION', 'KNOWLEDGE_BASE_RESPONSE_GENERATION', 'POST_PROCESSING', ], ], 'Rationale' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'RationaleString', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'RationaleString' => [ 'type' => 'string', 'sensitive' => true, ], 'RepromptResponse' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'Source', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RequestBody' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentMap', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'chunk' => [ 'shape' => 'PayloadPart', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'trace' => [ 'shape' => 'TracePart', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'RetrievalFilter' => [ 'type' => 'structure', 'members' => [ 'andAll' => [ 'shape' => 'RetrievalFilterList', ], 'equals' => [ 'shape' => 'FilterAttribute', ], 'greaterThan' => [ 'shape' => 'FilterAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'in' => [ 'shape' => 'FilterAttribute', ], 'lessThan' => [ 'shape' => 'FilterAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'notEquals' => [ 'shape' => 'FilterAttribute', ], 'notIn' => [ 'shape' => 'FilterAttribute', ], 'orAll' => [ 'shape' => 'RetrievalFilterList', ], 'startsWith' => [ 'shape' => 'FilterAttribute', ], ], 'sensitive' => true, 'union' => true, ], 'RetrievalFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievalFilter', ], 'max' => 5, 'min' => 2, ], 'RetrievalResultContent' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RetrievalResultLocation' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 's3Location' => [ 'shape' => 'RetrievalResultS3Location', ], 'type' => [ 'shape' => 'RetrievalResultLocationType', ], ], 'sensitive' => true, ], 'RetrievalResultLocationType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'RetrievalResultMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'RetrievalResultMetadataKey', ], 'value' => [ 'shape' => 'RetrievalResultMetadataValue', ], 'min' => 1, 'sensitive' => true, ], 'RetrievalResultMetadataKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'RetrievalResultMetadataValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'RetrievalResultS3Location' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'String', ], ], ], 'RetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseRetrieveAndGenerateConfiguration', ], 'type' => [ 'shape' => 'RetrieveAndGenerateType', ], ], ], 'RetrieveAndGenerateInput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'RetrieveAndGenerateInputTextString', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateInputTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'RetrieveAndGenerateOutput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateRequest' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'RetrieveAndGenerateInput', ], 'retrieveAndGenerateConfiguration' => [ 'shape' => 'RetrieveAndGenerateConfiguration', ], 'sessionConfiguration' => [ 'shape' => 'RetrieveAndGenerateSessionConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateResponse' => [ 'type' => 'structure', 'required' => [ 'output', 'sessionId', ], 'members' => [ 'citations' => [ 'shape' => 'Citations', ], 'output' => [ 'shape' => 'RetrieveAndGenerateOutput', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateSessionConfiguration' => [ 'type' => 'structure', 'required' => [ 'kmsKeyArn', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'RetrieveAndGenerateType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'RetrieveRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'retrievalQuery', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], 'retrievalQuery' => [ 'shape' => 'KnowledgeBaseQuery', ], ], ], 'RetrieveResponse' => [ 'type' => 'structure', 'required' => [ 'retrievalResults', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalResults' => [ 'shape' => 'KnowledgeBaseRetrievalResults', ], ], ], 'RetrievedReference' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], ], ], 'RetrievedReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievedReference', ], ], 'SearchType' => [ 'type' => 'string', 'enum' => [ 'HYBRID', 'SEMANTIC', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SessionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'SessionState' => [ 'type' => 'structure', 'members' => [ 'promptSessionAttributes' => [ 'shape' => 'PromptSessionAttributesMap', ], 'sessionAttributes' => [ 'shape' => 'SessionAttributesMap', ], ], ], 'Source' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'PARSER', ], 'sensitive' => true, ], 'Span' => [ 'type' => 'structure', 'members' => [ 'end' => [ 'shape' => 'SpanEndInteger', ], 'start' => [ 'shape' => 'SpanStartInteger', ], ], ], 'SpanEndInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'SpanStartInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 4, 'min' => 0, ], 'String' => [ 'type' => 'string', ], 'Temperature' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'TextPromptTemplate' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'TextResponsePart' => [ 'type' => 'structure', 'members' => [ 'span' => [ 'shape' => 'Span', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TopK' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'TopP' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'Trace' => [ 'type' => 'structure', 'members' => [ 'failureTrace' => [ 'shape' => 'FailureTrace', ], 'orchestrationTrace' => [ 'shape' => 'OrchestrationTrace', ], 'postProcessingTrace' => [ 'shape' => 'PostProcessingTrace', ], 'preProcessingTrace' => [ 'shape' => 'PreProcessingTrace', ], ], 'sensitive' => true, 'union' => true, ], 'TraceId' => [ 'type' => 'string', 'max' => 16, 'min' => 2, ], 'TraceKnowledgeBaseId' => [ 'type' => 'string', 'sensitive' => true, ], 'TracePart' => [ 'type' => 'structure', 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'trace' => [ 'shape' => 'Trace', ], ], 'event' => true, 'sensitive' => true, ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'FINISH', 'ASK_USER', 'REPROMPT', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Verb' => [ 'type' => 'string', 'sensitive' => true, ], ],];
