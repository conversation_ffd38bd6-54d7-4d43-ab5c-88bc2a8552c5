<?php
// This file was auto-generated from sdk-root/src/data/s3control/2018-08-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-08-20', 'endpointPrefix' => 's3-control', 'protocol' => 'rest-xml', 'serviceFullName' => 'AWS S3 Control', 'serviceId' => 'S3 Control', 'signatureVersion' => 's3v4', 'signingName' => 's3', 'uid' => 's3control-2018-08-20', ], 'operations' => [ 'AssociateAccessGrantsIdentityCenter' => [ 'name' => 'AssociateAccessGrantsIdentityCenter', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/accessgrantsinstance/identitycenter', ], 'input' => [ 'shape' => 'AssociateAccessGrantsIdentityCenterRequest', 'locationName' => 'AssociateAccessGrantsIdentityCenterRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateAccessGrant' => [ 'name' => 'CreateAccessGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/accessgrantsinstance/grant', ], 'input' => [ 'shape' => 'CreateAccessGrantRequest', 'locationName' => 'CreateAccessGrantRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateAccessGrantResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateAccessGrantsInstance' => [ 'name' => 'CreateAccessGrantsInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/accessgrantsinstance', ], 'input' => [ 'shape' => 'CreateAccessGrantsInstanceRequest', 'locationName' => 'CreateAccessGrantsInstanceRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateAccessGrantsInstanceResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateAccessGrantsLocation' => [ 'name' => 'CreateAccessGrantsLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/accessgrantsinstance/location', ], 'input' => [ 'shape' => 'CreateAccessGrantsLocationRequest', 'locationName' => 'CreateAccessGrantsLocationRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateAccessGrantsLocationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateAccessPoint' => [ 'name' => 'CreateAccessPoint', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accesspoint/{name}', ], 'input' => [ 'shape' => 'CreateAccessPointRequest', 'locationName' => 'CreateAccessPointRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateAccessPointResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateAccessPointForObjectLambda' => [ 'name' => 'CreateAccessPointForObjectLambda', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}', ], 'input' => [ 'shape' => 'CreateAccessPointForObjectLambdaRequest', 'locationName' => 'CreateAccessPointForObjectLambdaRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateAccessPointForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateBucket' => [ 'name' => 'CreateBucket', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}', ], 'input' => [ 'shape' => 'CreateBucketRequest', ], 'output' => [ 'shape' => 'CreateBucketResult', ], 'errors' => [ [ 'shape' => 'BucketAlreadyExists', ], [ 'shape' => 'BucketAlreadyOwnedByYou', ], ], 'httpChecksumRequired' => true, ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/jobs', ], 'input' => [ 'shape' => 'CreateJobRequest', 'locationName' => 'CreateJobRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateJobResult', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'InternalServiceException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateMultiRegionAccessPoint' => [ 'name' => 'CreateMultiRegionAccessPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/async-requests/mrap/create', ], 'input' => [ 'shape' => 'CreateMultiRegionAccessPointRequest', 'locationName' => 'CreateMultiRegionAccessPointRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'CreateMultiRegionAccessPointResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'CreateStorageLensGroup' => [ 'name' => 'CreateStorageLensGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/storagelensgroup', 'responseCode' => 204, ], 'input' => [ 'shape' => 'CreateStorageLensGroupRequest', 'locationName' => 'CreateStorageLensGroupRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessGrant' => [ 'name' => 'DeleteAccessGrant', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accessgrantsinstance/grant/{id}', ], 'input' => [ 'shape' => 'DeleteAccessGrantRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessGrantsInstance' => [ 'name' => 'DeleteAccessGrantsInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accessgrantsinstance', ], 'input' => [ 'shape' => 'DeleteAccessGrantsInstanceRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessGrantsInstanceResourcePolicy' => [ 'name' => 'DeleteAccessGrantsInstanceResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accessgrantsinstance/resourcepolicy', ], 'input' => [ 'shape' => 'DeleteAccessGrantsInstanceResourcePolicyRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessGrantsLocation' => [ 'name' => 'DeleteAccessGrantsLocation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accessgrantsinstance/location/{id}', ], 'input' => [ 'shape' => 'DeleteAccessGrantsLocationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessPoint' => [ 'name' => 'DeleteAccessPoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accesspoint/{name}', ], 'input' => [ 'shape' => 'DeleteAccessPointRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessPointForObjectLambda' => [ 'name' => 'DeleteAccessPointForObjectLambda', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}', ], 'input' => [ 'shape' => 'DeleteAccessPointForObjectLambdaRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessPointPolicy' => [ 'name' => 'DeleteAccessPointPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accesspoint/{name}/policy', ], 'input' => [ 'shape' => 'DeleteAccessPointPolicyRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteAccessPointPolicyForObjectLambda' => [ 'name' => 'DeleteAccessPointPolicyForObjectLambda', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/policy', ], 'input' => [ 'shape' => 'DeleteAccessPointPolicyForObjectLambdaRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteBucket' => [ 'name' => 'DeleteBucket', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/bucket/{name}', ], 'input' => [ 'shape' => 'DeleteBucketRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteBucketLifecycleConfiguration' => [ 'name' => 'DeleteBucketLifecycleConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/bucket/{name}/lifecycleconfiguration', ], 'input' => [ 'shape' => 'DeleteBucketLifecycleConfigurationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteBucketPolicy' => [ 'name' => 'DeleteBucketPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/bucket/{name}/policy', ], 'input' => [ 'shape' => 'DeleteBucketPolicyRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteBucketReplication' => [ 'name' => 'DeleteBucketReplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/bucket/{name}/replication', ], 'input' => [ 'shape' => 'DeleteBucketReplicationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteBucketTagging' => [ 'name' => 'DeleteBucketTagging', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/bucket/{name}/tagging', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteBucketTaggingRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteJobTagging' => [ 'name' => 'DeleteJobTagging', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/jobs/{id}/tagging', ], 'input' => [ 'shape' => 'DeleteJobTaggingRequest', ], 'output' => [ 'shape' => 'DeleteJobTaggingResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteMultiRegionAccessPoint' => [ 'name' => 'DeleteMultiRegionAccessPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/async-requests/mrap/delete', ], 'input' => [ 'shape' => 'DeleteMultiRegionAccessPointRequest', 'locationName' => 'DeleteMultiRegionAccessPointRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'DeleteMultiRegionAccessPointResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeletePublicAccessBlock' => [ 'name' => 'DeletePublicAccessBlock', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/configuration/publicAccessBlock', ], 'input' => [ 'shape' => 'DeletePublicAccessBlockRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteStorageLensConfiguration' => [ 'name' => 'DeleteStorageLensConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/storagelens/{storagelensid}', ], 'input' => [ 'shape' => 'DeleteStorageLensConfigurationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteStorageLensConfigurationTagging' => [ 'name' => 'DeleteStorageLensConfigurationTagging', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/storagelens/{storagelensid}/tagging', ], 'input' => [ 'shape' => 'DeleteStorageLensConfigurationTaggingRequest', ], 'output' => [ 'shape' => 'DeleteStorageLensConfigurationTaggingResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DeleteStorageLensGroup' => [ 'name' => 'DeleteStorageLensGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/storagelensgroup/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteStorageLensGroupRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DescribeJob' => [ 'name' => 'DescribeJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/jobs/{id}', ], 'input' => [ 'shape' => 'DescribeJobRequest', ], 'output' => [ 'shape' => 'DescribeJobResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DescribeMultiRegionAccessPointOperation' => [ 'name' => 'DescribeMultiRegionAccessPointOperation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/async-requests/mrap/{request_token+}', ], 'input' => [ 'shape' => 'DescribeMultiRegionAccessPointOperationRequest', ], 'output' => [ 'shape' => 'DescribeMultiRegionAccessPointOperationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'DissociateAccessGrantsIdentityCenter' => [ 'name' => 'DissociateAccessGrantsIdentityCenter', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/accessgrantsinstance/identitycenter', ], 'input' => [ 'shape' => 'DissociateAccessGrantsIdentityCenterRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessGrant' => [ 'name' => 'GetAccessGrant', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/grant/{id}', ], 'input' => [ 'shape' => 'GetAccessGrantRequest', ], 'output' => [ 'shape' => 'GetAccessGrantResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessGrantsInstance' => [ 'name' => 'GetAccessGrantsInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance', ], 'input' => [ 'shape' => 'GetAccessGrantsInstanceRequest', ], 'output' => [ 'shape' => 'GetAccessGrantsInstanceResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessGrantsInstanceForPrefix' => [ 'name' => 'GetAccessGrantsInstanceForPrefix', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/prefix', ], 'input' => [ 'shape' => 'GetAccessGrantsInstanceForPrefixRequest', ], 'output' => [ 'shape' => 'GetAccessGrantsInstanceForPrefixResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessGrantsInstanceResourcePolicy' => [ 'name' => 'GetAccessGrantsInstanceResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/resourcepolicy', ], 'input' => [ 'shape' => 'GetAccessGrantsInstanceResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetAccessGrantsInstanceResourcePolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessGrantsLocation' => [ 'name' => 'GetAccessGrantsLocation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/location/{id}', ], 'input' => [ 'shape' => 'GetAccessGrantsLocationRequest', ], 'output' => [ 'shape' => 'GetAccessGrantsLocationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPoint' => [ 'name' => 'GetAccessPoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspoint/{name}', ], 'input' => [ 'shape' => 'GetAccessPointRequest', ], 'output' => [ 'shape' => 'GetAccessPointResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointConfigurationForObjectLambda' => [ 'name' => 'GetAccessPointConfigurationForObjectLambda', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/configuration', ], 'input' => [ 'shape' => 'GetAccessPointConfigurationForObjectLambdaRequest', ], 'output' => [ 'shape' => 'GetAccessPointConfigurationForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointForObjectLambda' => [ 'name' => 'GetAccessPointForObjectLambda', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}', ], 'input' => [ 'shape' => 'GetAccessPointForObjectLambdaRequest', ], 'output' => [ 'shape' => 'GetAccessPointForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointPolicy' => [ 'name' => 'GetAccessPointPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspoint/{name}/policy', ], 'input' => [ 'shape' => 'GetAccessPointPolicyRequest', ], 'output' => [ 'shape' => 'GetAccessPointPolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointPolicyForObjectLambda' => [ 'name' => 'GetAccessPointPolicyForObjectLambda', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/policy', ], 'input' => [ 'shape' => 'GetAccessPointPolicyForObjectLambdaRequest', ], 'output' => [ 'shape' => 'GetAccessPointPolicyForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointPolicyStatus' => [ 'name' => 'GetAccessPointPolicyStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspoint/{name}/policyStatus', ], 'input' => [ 'shape' => 'GetAccessPointPolicyStatusRequest', ], 'output' => [ 'shape' => 'GetAccessPointPolicyStatusResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetAccessPointPolicyStatusForObjectLambda' => [ 'name' => 'GetAccessPointPolicyStatusForObjectLambda', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/policyStatus', ], 'input' => [ 'shape' => 'GetAccessPointPolicyStatusForObjectLambdaRequest', ], 'output' => [ 'shape' => 'GetAccessPointPolicyStatusForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucket' => [ 'name' => 'GetBucket', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}', ], 'input' => [ 'shape' => 'GetBucketRequest', ], 'output' => [ 'shape' => 'GetBucketResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucketLifecycleConfiguration' => [ 'name' => 'GetBucketLifecycleConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}/lifecycleconfiguration', ], 'input' => [ 'shape' => 'GetBucketLifecycleConfigurationRequest', ], 'output' => [ 'shape' => 'GetBucketLifecycleConfigurationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucketPolicy' => [ 'name' => 'GetBucketPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}/policy', ], 'input' => [ 'shape' => 'GetBucketPolicyRequest', ], 'output' => [ 'shape' => 'GetBucketPolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucketReplication' => [ 'name' => 'GetBucketReplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}/replication', ], 'input' => [ 'shape' => 'GetBucketReplicationRequest', ], 'output' => [ 'shape' => 'GetBucketReplicationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucketTagging' => [ 'name' => 'GetBucketTagging', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}/tagging', ], 'input' => [ 'shape' => 'GetBucketTaggingRequest', ], 'output' => [ 'shape' => 'GetBucketTaggingResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetBucketVersioning' => [ 'name' => 'GetBucketVersioning', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket/{name}/versioning', ], 'input' => [ 'shape' => 'GetBucketVersioningRequest', ], 'output' => [ 'shape' => 'GetBucketVersioningResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetDataAccess' => [ 'name' => 'GetDataAccess', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/dataaccess', ], 'input' => [ 'shape' => 'GetDataAccessRequest', ], 'output' => [ 'shape' => 'GetDataAccessResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetJobTagging' => [ 'name' => 'GetJobTagging', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/jobs/{id}/tagging', ], 'input' => [ 'shape' => 'GetJobTaggingRequest', ], 'output' => [ 'shape' => 'GetJobTaggingResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetMultiRegionAccessPoint' => [ 'name' => 'GetMultiRegionAccessPoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/mrap/instances/{name+}', ], 'input' => [ 'shape' => 'GetMultiRegionAccessPointRequest', ], 'output' => [ 'shape' => 'GetMultiRegionAccessPointResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetMultiRegionAccessPointPolicy' => [ 'name' => 'GetMultiRegionAccessPointPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/mrap/instances/{name+}/policy', ], 'input' => [ 'shape' => 'GetMultiRegionAccessPointPolicyRequest', ], 'output' => [ 'shape' => 'GetMultiRegionAccessPointPolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetMultiRegionAccessPointPolicyStatus' => [ 'name' => 'GetMultiRegionAccessPointPolicyStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/mrap/instances/{name+}/policystatus', ], 'input' => [ 'shape' => 'GetMultiRegionAccessPointPolicyStatusRequest', ], 'output' => [ 'shape' => 'GetMultiRegionAccessPointPolicyStatusResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetMultiRegionAccessPointRoutes' => [ 'name' => 'GetMultiRegionAccessPointRoutes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/mrap/instances/{mrap+}/routes', ], 'input' => [ 'shape' => 'GetMultiRegionAccessPointRoutesRequest', ], 'output' => [ 'shape' => 'GetMultiRegionAccessPointRoutesResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetPublicAccessBlock' => [ 'name' => 'GetPublicAccessBlock', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/configuration/publicAccessBlock', ], 'input' => [ 'shape' => 'GetPublicAccessBlockRequest', ], 'output' => [ 'shape' => 'GetPublicAccessBlockOutput', ], 'errors' => [ [ 'shape' => 'NoSuchPublicAccessBlockConfiguration', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetStorageLensConfiguration' => [ 'name' => 'GetStorageLensConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/storagelens/{storagelensid}', ], 'input' => [ 'shape' => 'GetStorageLensConfigurationRequest', ], 'output' => [ 'shape' => 'GetStorageLensConfigurationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetStorageLensConfigurationTagging' => [ 'name' => 'GetStorageLensConfigurationTagging', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/storagelens/{storagelensid}/tagging', ], 'input' => [ 'shape' => 'GetStorageLensConfigurationTaggingRequest', ], 'output' => [ 'shape' => 'GetStorageLensConfigurationTaggingResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'GetStorageLensGroup' => [ 'name' => 'GetStorageLensGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/storagelensgroup/{name}', ], 'input' => [ 'shape' => 'GetStorageLensGroupRequest', ], 'output' => [ 'shape' => 'GetStorageLensGroupResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListAccessGrants' => [ 'name' => 'ListAccessGrants', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/grants', ], 'input' => [ 'shape' => 'ListAccessGrantsRequest', ], 'output' => [ 'shape' => 'ListAccessGrantsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListAccessGrantsInstances' => [ 'name' => 'ListAccessGrantsInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstances', ], 'input' => [ 'shape' => 'ListAccessGrantsInstancesRequest', ], 'output' => [ 'shape' => 'ListAccessGrantsInstancesResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListAccessGrantsLocations' => [ 'name' => 'ListAccessGrantsLocations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accessgrantsinstance/locations', ], 'input' => [ 'shape' => 'ListAccessGrantsLocationsRequest', ], 'output' => [ 'shape' => 'ListAccessGrantsLocationsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListAccessPoints' => [ 'name' => 'ListAccessPoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspoint', ], 'input' => [ 'shape' => 'ListAccessPointsRequest', ], 'output' => [ 'shape' => 'ListAccessPointsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListAccessPointsForObjectLambda' => [ 'name' => 'ListAccessPointsForObjectLambda', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/accesspointforobjectlambda', ], 'input' => [ 'shape' => 'ListAccessPointsForObjectLambdaRequest', ], 'output' => [ 'shape' => 'ListAccessPointsForObjectLambdaResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/jobs', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListMultiRegionAccessPoints' => [ 'name' => 'ListMultiRegionAccessPoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/mrap/instances', ], 'input' => [ 'shape' => 'ListMultiRegionAccessPointsRequest', ], 'output' => [ 'shape' => 'ListMultiRegionAccessPointsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListRegionalBuckets' => [ 'name' => 'ListRegionalBuckets', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/bucket', ], 'input' => [ 'shape' => 'ListRegionalBucketsRequest', ], 'output' => [ 'shape' => 'ListRegionalBucketsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListStorageLensConfigurations' => [ 'name' => 'ListStorageLensConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/storagelens', ], 'input' => [ 'shape' => 'ListStorageLensConfigurationsRequest', ], 'output' => [ 'shape' => 'ListStorageLensConfigurationsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListStorageLensGroups' => [ 'name' => 'ListStorageLensGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/storagelensgroup', ], 'input' => [ 'shape' => 'ListStorageLensGroupsRequest', ], 'output' => [ 'shape' => 'ListStorageLensGroupsResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20180820/tags/{resourceArn+}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutAccessGrantsInstanceResourcePolicy' => [ 'name' => 'PutAccessGrantsInstanceResourcePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accessgrantsinstance/resourcepolicy', ], 'input' => [ 'shape' => 'PutAccessGrantsInstanceResourcePolicyRequest', 'locationName' => 'PutAccessGrantsInstanceResourcePolicyRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'PutAccessGrantsInstanceResourcePolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutAccessPointConfigurationForObjectLambda' => [ 'name' => 'PutAccessPointConfigurationForObjectLambda', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/configuration', ], 'input' => [ 'shape' => 'PutAccessPointConfigurationForObjectLambdaRequest', 'locationName' => 'PutAccessPointConfigurationForObjectLambdaRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutAccessPointPolicy' => [ 'name' => 'PutAccessPointPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accesspoint/{name}/policy', ], 'input' => [ 'shape' => 'PutAccessPointPolicyRequest', 'locationName' => 'PutAccessPointPolicyRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutAccessPointPolicyForObjectLambda' => [ 'name' => 'PutAccessPointPolicyForObjectLambda', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accesspointforobjectlambda/{name}/policy', ], 'input' => [ 'shape' => 'PutAccessPointPolicyForObjectLambdaRequest', 'locationName' => 'PutAccessPointPolicyForObjectLambdaRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutBucketLifecycleConfiguration' => [ 'name' => 'PutBucketLifecycleConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}/lifecycleconfiguration', ], 'input' => [ 'shape' => 'PutBucketLifecycleConfigurationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutBucketPolicy' => [ 'name' => 'PutBucketPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}/policy', ], 'input' => [ 'shape' => 'PutBucketPolicyRequest', 'locationName' => 'PutBucketPolicyRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutBucketReplication' => [ 'name' => 'PutBucketReplication', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}/replication', ], 'input' => [ 'shape' => 'PutBucketReplicationRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutBucketTagging' => [ 'name' => 'PutBucketTagging', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}/tagging', ], 'input' => [ 'shape' => 'PutBucketTaggingRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutBucketVersioning' => [ 'name' => 'PutBucketVersioning', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/bucket/{name}/versioning', ], 'input' => [ 'shape' => 'PutBucketVersioningRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutJobTagging' => [ 'name' => 'PutJobTagging', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/jobs/{id}/tagging', ], 'input' => [ 'shape' => 'PutJobTaggingRequest', 'locationName' => 'PutJobTaggingRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'PutJobTaggingResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyTagsException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutMultiRegionAccessPointPolicy' => [ 'name' => 'PutMultiRegionAccessPointPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/async-requests/mrap/put-policy', ], 'input' => [ 'shape' => 'PutMultiRegionAccessPointPolicyRequest', 'locationName' => 'PutMultiRegionAccessPointPolicyRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'PutMultiRegionAccessPointPolicyResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutPublicAccessBlock' => [ 'name' => 'PutPublicAccessBlock', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/configuration/publicAccessBlock', ], 'input' => [ 'shape' => 'PutPublicAccessBlockRequest', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutStorageLensConfiguration' => [ 'name' => 'PutStorageLensConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/storagelens/{storagelensid}', ], 'input' => [ 'shape' => 'PutStorageLensConfigurationRequest', 'locationName' => 'PutStorageLensConfigurationRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'PutStorageLensConfigurationTagging' => [ 'name' => 'PutStorageLensConfigurationTagging', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/storagelens/{storagelensid}/tagging', ], 'input' => [ 'shape' => 'PutStorageLensConfigurationTaggingRequest', 'locationName' => 'PutStorageLensConfigurationTaggingRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'PutStorageLensConfigurationTaggingResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'SubmitMultiRegionAccessPointRoutes' => [ 'name' => 'SubmitMultiRegionAccessPointRoutes', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/v20180820/mrap/instances/{mrap+}/routes', ], 'input' => [ 'shape' => 'SubmitMultiRegionAccessPointRoutesRequest', 'locationName' => 'SubmitMultiRegionAccessPointRoutesRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'SubmitMultiRegionAccessPointRoutesResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/tags/{resourceArn+}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', 'locationName' => 'TagResourceRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'TagResourceResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20180820/tags/{resourceArn+}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'UpdateAccessGrantsLocation' => [ 'name' => 'UpdateAccessGrantsLocation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/accessgrantsinstance/location/{id}', ], 'input' => [ 'shape' => 'UpdateAccessGrantsLocationRequest', 'locationName' => 'UpdateAccessGrantsLocationRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'output' => [ 'shape' => 'UpdateAccessGrantsLocationResult', ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'httpChecksumRequired' => true, 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'UpdateJobPriority' => [ 'name' => 'UpdateJobPriority', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/jobs/{id}/priority', ], 'input' => [ 'shape' => 'UpdateJobPriorityRequest', ], 'output' => [ 'shape' => 'UpdateJobPriorityResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'UpdateJobStatus' => [ 'name' => 'UpdateJobStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/v20180820/jobs/{id}/status', ], 'input' => [ 'shape' => 'UpdateJobStatusRequest', ], 'output' => [ 'shape' => 'UpdateJobStatusResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'JobStatusException', ], [ 'shape' => 'InternalServiceException', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], 'UpdateStorageLensGroup' => [ 'name' => 'UpdateStorageLensGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20180820/storagelensgroup/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateStorageLensGroupRequest', 'locationName' => 'UpdateStorageLensGroupRequest', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'endpoint' => [ 'hostPrefix' => '{AccountId}.', ], 'staticContextParams' => [ 'RequiresAccountId' => [ 'value' => true, ], ], ], ], 'shapes' => [ 'AbortIncompleteMultipartUpload' => [ 'type' => 'structure', 'members' => [ 'DaysAfterInitiation' => [ 'shape' => 'DaysAfterInitiation', ], ], ], 'AccessControlTranslation' => [ 'type' => 'structure', 'required' => [ 'Owner', ], 'members' => [ 'Owner' => [ 'shape' => 'OwnerOverride', ], ], ], 'AccessGrantArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:access\\-grants\\/grant/[a-zA-Z0-9\\-]+', ], 'AccessGrantId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'AccessGrantsInstanceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:access\\-grants\\/[a-zA-Z0-9\\-]+', ], 'AccessGrantsInstanceId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'AccessGrantsInstancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListAccessGrantsInstanceEntry', 'locationName' => 'AccessGrantsInstance', ], ], 'AccessGrantsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListAccessGrantEntry', 'locationName' => 'AccessGrant', ], ], 'AccessGrantsLocationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:access\\-grants\\/location/[a-zA-Z0-9\\-]+', ], 'AccessGrantsLocationConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3SubPrefix' => [ 'shape' => 'S3Prefix', ], ], ], 'AccessGrantsLocationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'AccessGrantsLocationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListAccessGrantsLocationsEntry', 'locationName' => 'AccessGrantsLocation', ], ], 'AccessKeyId' => [ 'type' => 'string', 'sensitive' => true, ], 'AccessPoint' => [ 'type' => 'structure', 'required' => [ 'Name', 'NetworkOrigin', 'Bucket', ], 'members' => [ 'Name' => [ 'shape' => 'AccessPointName', ], 'NetworkOrigin' => [ 'shape' => 'NetworkOrigin', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'Bucket' => [ 'shape' => 'BucketName', ], 'AccessPointArn' => [ 'shape' => 'S3AccessPointArn', ], 'Alias' => [ 'shape' => 'Alias', ], 'BucketAccountId' => [ 'shape' => 'AccountId', ], ], ], 'AccessPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPoint', 'locationName' => 'AccessPoint', ], ], 'AccessPointName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, ], 'AccountId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '^\\d{12}$', ], 'AccountLevel' => [ 'type' => 'structure', 'required' => [ 'BucketLevel', ], 'members' => [ 'ActivityMetrics' => [ 'shape' => 'ActivityMetrics', ], 'BucketLevel' => [ 'shape' => 'BucketLevel', ], 'AdvancedCostOptimizationMetrics' => [ 'shape' => 'AdvancedCostOptimizationMetrics', ], 'AdvancedDataProtectionMetrics' => [ 'shape' => 'AdvancedDataProtectionMetrics', ], 'DetailedStatusCodesMetrics' => [ 'shape' => 'DetailedStatusCodesMetrics', ], 'StorageLensGroupLevel' => [ 'shape' => 'StorageLensGroupLevel', ], ], ], 'ActivityMetrics' => [ 'type' => 'structure', 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'AdvancedCostOptimizationMetrics' => [ 'type' => 'structure', 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'AdvancedDataProtectionMetrics' => [ 'type' => 'structure', 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'Alias' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[0-9a-z\\\\-]{63}', ], 'AssociateAccessGrantsIdentityCenterRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'IdentityCenterArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'IdentityCenterArn' => [ 'shape' => 'IdentityCenterArn', ], ], ], 'AsyncCreationTimestamp' => [ 'type' => 'timestamp', ], 'AsyncErrorDetails' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'MaxLength1024String', ], 'Message' => [ 'shape' => 'MaxLength1024String', ], 'Resource' => [ 'shape' => 'MaxLength1024String', ], 'RequestId' => [ 'shape' => 'MaxLength1024String', ], ], ], 'AsyncOperation' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'AsyncCreationTimestamp', ], 'Operation' => [ 'shape' => 'AsyncOperationName', ], 'RequestTokenARN' => [ 'shape' => 'AsyncRequestTokenARN', ], 'RequestParameters' => [ 'shape' => 'AsyncRequestParameters', ], 'RequestStatus' => [ 'shape' => 'AsyncRequestStatus', ], 'ResponseDetails' => [ 'shape' => 'AsyncResponseDetails', ], ], ], 'AsyncOperationName' => [ 'type' => 'string', 'enum' => [ 'CreateMultiRegionAccessPoint', 'DeleteMultiRegionAccessPoint', 'PutMultiRegionAccessPointPolicy', ], ], 'AsyncRequestParameters' => [ 'type' => 'structure', 'members' => [ 'CreateMultiRegionAccessPointRequest' => [ 'shape' => 'CreateMultiRegionAccessPointInput', ], 'DeleteMultiRegionAccessPointRequest' => [ 'shape' => 'DeleteMultiRegionAccessPointInput', ], 'PutMultiRegionAccessPointPolicyRequest' => [ 'shape' => 'PutMultiRegionAccessPointPolicyInput', ], ], ], 'AsyncRequestStatus' => [ 'type' => 'string', ], 'AsyncRequestTokenARN' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:.+', ], 'AsyncResponseDetails' => [ 'type' => 'structure', 'members' => [ 'MultiRegionAccessPointDetails' => [ 'shape' => 'MultiRegionAccessPointsAsyncResponse', ], 'ErrorDetails' => [ 'shape' => 'AsyncErrorDetails', ], ], ], 'AwsLambdaTransformation' => [ 'type' => 'structure', 'required' => [ 'FunctionArn', ], 'members' => [ 'FunctionArn' => [ 'shape' => 'FunctionArnString', ], 'FunctionPayload' => [ 'shape' => 'AwsLambdaTransformationPayload', ], ], ], 'AwsLambdaTransformationPayload' => [ 'type' => 'string', ], 'AwsOrgArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[a-z\\-]+:organizations::\\d{12}:organization\\/o-[a-z0-9]{10,32}', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BucketAlreadyExists' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BucketAlreadyOwnedByYou' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BucketCannedACL' => [ 'type' => 'string', 'enum' => [ 'private', 'public-read', 'public-read-write', 'authenticated-read', ], ], 'BucketIdentifierString' => [ 'type' => 'string', ], 'BucketLevel' => [ 'type' => 'structure', 'members' => [ 'ActivityMetrics' => [ 'shape' => 'ActivityMetrics', ], 'PrefixLevel' => [ 'shape' => 'PrefixLevel', ], 'AdvancedCostOptimizationMetrics' => [ 'shape' => 'AdvancedCostOptimizationMetrics', ], 'AdvancedDataProtectionMetrics' => [ 'shape' => 'AdvancedDataProtectionMetrics', ], 'DetailedStatusCodesMetrics' => [ 'shape' => 'DetailedStatusCodesMetrics', ], ], ], 'BucketLocationConstraint' => [ 'type' => 'string', 'enum' => [ 'EU', 'eu-west-1', 'us-west-1', 'us-west-2', 'ap-south-1', 'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1', 'sa-east-1', 'cn-north-1', 'eu-central-1', ], ], 'BucketName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, ], 'BucketVersioningStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Suspended', ], ], 'Buckets' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketArnString', 'locationName' => 'Arn', ], ], 'CloudWatchMetrics' => [ 'type' => 'structure', 'required' => [ 'IsEnabled', ], 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'ConfigId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-\\_\\.]+', ], 'ConfirmRemoveSelfBucketAccess' => [ 'type' => 'boolean', ], 'ConfirmationRequired' => [ 'type' => 'boolean', ], 'ContinuationToken' => [ 'type' => 'string', ], 'CreateAccessGrantRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantsLocationId', 'Grantee', 'Permission', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationConfiguration' => [ 'shape' => 'AccessGrantsLocationConfiguration', ], 'Grantee' => [ 'shape' => 'Grantee', ], 'Permission' => [ 'shape' => 'Permission', ], 'ApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'S3PrefixType' => [ 'shape' => 'S3PrefixType', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAccessGrantResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantId' => [ 'shape' => 'AccessGrantId', ], 'AccessGrantArn' => [ 'shape' => 'AccessGrantArn', ], 'Grantee' => [ 'shape' => 'Grantee', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationConfiguration' => [ 'shape' => 'AccessGrantsLocationConfiguration', ], 'Permission' => [ 'shape' => 'Permission', ], 'ApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'GrantScope' => [ 'shape' => 'S3Prefix', ], ], ], 'CreateAccessGrantsInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'IdentityCenterArn' => [ 'shape' => 'IdentityCenterArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAccessGrantsInstanceResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantsInstanceId' => [ 'shape' => 'AccessGrantsInstanceId', ], 'AccessGrantsInstanceArn' => [ 'shape' => 'AccessGrantsInstanceArn', ], 'IdentityCenterArn' => [ 'shape' => 'IdentityCenterArn', ], ], ], 'CreateAccessGrantsLocationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'LocationScope', 'IAMRoleArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'LocationScope' => [ 'shape' => 'S3Prefix', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAccessGrantsLocationResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationArn' => [ 'shape' => 'AccessGrantsLocationArn', ], 'LocationScope' => [ 'shape' => 'S3Prefix', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'CreateAccessPointForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', 'Configuration', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], 'Configuration' => [ 'shape' => 'ObjectLambdaConfiguration', ], ], ], 'CreateAccessPointForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'ObjectLambdaAccessPointArn' => [ 'shape' => 'ObjectLambdaAccessPointArn', ], 'Alias' => [ 'shape' => 'ObjectLambdaAccessPointAlias', ], ], ], 'CreateAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'location' => 'uri', 'locationName' => 'name', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'PublicAccessBlockConfiguration', ], 'BucketAccountId' => [ 'shape' => 'AccountId', ], ], ], 'CreateAccessPointResult' => [ 'type' => 'structure', 'members' => [ 'AccessPointArn' => [ 'shape' => 'S3AccessPointArn', ], 'Alias' => [ 'shape' => 'Alias', ], ], ], 'CreateBucketConfiguration' => [ 'type' => 'structure', 'members' => [ 'LocationConstraint' => [ 'shape' => 'BucketLocationConstraint', ], ], ], 'CreateBucketRequest' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'ACL' => [ 'shape' => 'BucketCannedACL', 'location' => 'header', 'locationName' => 'x-amz-acl', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'CreateBucketConfiguration' => [ 'shape' => 'CreateBucketConfiguration', 'locationName' => 'CreateBucketConfiguration', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'GrantFullControl' => [ 'shape' => 'GrantFullControl', 'location' => 'header', 'locationName' => 'x-amz-grant-full-control', ], 'GrantRead' => [ 'shape' => 'GrantRead', 'location' => 'header', 'locationName' => 'x-amz-grant-read', ], 'GrantReadACP' => [ 'shape' => 'GrantReadACP', 'location' => 'header', 'locationName' => 'x-amz-grant-read-acp', ], 'GrantWrite' => [ 'shape' => 'GrantWrite', 'location' => 'header', 'locationName' => 'x-amz-grant-write', ], 'GrantWriteACP' => [ 'shape' => 'GrantWriteACP', 'location' => 'header', 'locationName' => 'x-amz-grant-write-acp', ], 'ObjectLockEnabledForBucket' => [ 'shape' => 'ObjectLockEnabledForBucket', 'location' => 'header', 'locationName' => 'x-amz-bucket-object-lock-enabled', ], 'OutpostId' => [ 'shape' => 'NonEmptyMaxLength64String', 'contextParam' => [ 'name' => 'OutpostId', ], 'location' => 'header', 'locationName' => 'x-amz-outpost-id', ], ], 'payload' => 'CreateBucketConfiguration', ], 'CreateBucketResult' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'Location', 'location' => 'header', 'locationName' => 'Location', ], 'BucketArn' => [ 'shape' => 'S3RegionalBucketArn', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Operation', 'Report', 'ClientRequestToken', 'Priority', 'RoleArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ConfirmationRequired' => [ 'shape' => 'ConfirmationRequired', 'box' => true, ], 'Operation' => [ 'shape' => 'JobOperation', ], 'Report' => [ 'shape' => 'JobReport', ], 'ClientRequestToken' => [ 'shape' => 'NonEmptyMaxLength64String', 'idempotencyToken' => true, ], 'Manifest' => [ 'shape' => 'JobManifest', ], 'Description' => [ 'shape' => 'NonEmptyMaxLength256String', ], 'Priority' => [ 'shape' => 'JobPriority', 'box' => true, ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'Tags' => [ 'shape' => 'S3TagSet', ], 'ManifestGenerator' => [ 'shape' => 'JobManifestGenerator', ], ], ], 'CreateJobResult' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreateMultiRegionAccessPointInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Regions', ], 'members' => [ 'Name' => [ 'shape' => 'MultiRegionAccessPointName', ], 'PublicAccessBlock' => [ 'shape' => 'PublicAccessBlockConfiguration', ], 'Regions' => [ 'shape' => 'RegionCreationList', ], ], ], 'CreateMultiRegionAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ClientToken', 'Details', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ClientToken' => [ 'shape' => 'MultiRegionAccessPointClientToken', 'idempotencyToken' => true, ], 'Details' => [ 'shape' => 'CreateMultiRegionAccessPointInput', ], ], ], 'CreateMultiRegionAccessPointResult' => [ 'type' => 'structure', 'members' => [ 'RequestTokenARN' => [ 'shape' => 'AsyncRequestTokenARN', ], ], ], 'CreateStorageLensGroupRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'StorageLensGroup', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'StorageLensGroup' => [ 'shape' => 'StorageLensGroup', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreationDate' => [ 'type' => 'timestamp', ], 'CreationTimestamp' => [ 'type' => 'timestamp', ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'AccessKeyId', ], 'SecretAccessKey' => [ 'shape' => 'SecretAccessKey', ], 'SessionToken' => [ 'shape' => 'SessionToken', ], 'Expiration' => [ 'shape' => 'Expiration', ], ], 'sensitive' => true, ], 'Date' => [ 'type' => 'timestamp', ], 'Days' => [ 'type' => 'integer', ], 'DaysAfterInitiation' => [ 'type' => 'integer', ], 'DeleteAccessGrantRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantId' => [ 'shape' => 'AccessGrantId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteAccessGrantsInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DeleteAccessGrantsInstanceResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DeleteAccessGrantsLocationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantsLocationId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteAccessPointForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteAccessPointPolicyForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteAccessPointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBucketLifecycleConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBucketReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBucketRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBucketTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteJobTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteJobTaggingResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMarkerReplication' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DeleteMarkerReplicationStatus', ], ], ], 'DeleteMarkerReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'DeleteMultiRegionAccessPointInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MultiRegionAccessPointName', ], ], ], 'DeleteMultiRegionAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ClientToken', 'Details', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ClientToken' => [ 'shape' => 'MultiRegionAccessPointClientToken', 'idempotencyToken' => true, ], 'Details' => [ 'shape' => 'DeleteMultiRegionAccessPointInput', ], ], ], 'DeleteMultiRegionAccessPointResult' => [ 'type' => 'structure', 'members' => [ 'RequestTokenARN' => [ 'shape' => 'AsyncRequestTokenARN', ], ], ], 'DeletePublicAccessBlockRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DeleteStorageLensConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DeleteStorageLensConfigurationTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DeleteStorageLensConfigurationTaggingResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStorageLensGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'AccountId', ], 'members' => [ 'Name' => [ 'shape' => 'StorageLensGroupName', 'location' => 'uri', 'locationName' => 'name', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DescribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DescribeJobResult' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'JobDescriptor', ], ], ], 'DescribeMultiRegionAccessPointOperationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RequestTokenARN', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'RequestTokenARN' => [ 'shape' => 'AsyncRequestTokenARN', 'location' => 'uri', 'locationName' => 'request_token', ], ], ], 'DescribeMultiRegionAccessPointOperationResult' => [ 'type' => 'structure', 'members' => [ 'AsyncOperation' => [ 'shape' => 'AsyncOperation', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Account' => [ 'shape' => 'AccountId', ], 'Bucket' => [ 'shape' => 'BucketIdentifierString', ], 'ReplicationTime' => [ 'shape' => 'ReplicationTime', ], 'AccessControlTranslation' => [ 'shape' => 'AccessControlTranslation', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'Metrics' => [ 'shape' => 'Metrics', ], 'StorageClass' => [ 'shape' => 'ReplicationStorageClass', ], ], ], 'DetailedStatusCodesMetrics' => [ 'type' => 'structure', 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'DissociateAccessGrantsIdentityCenterRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'DurationSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 43200, 'min' => 900, ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'ReplicaKmsKeyID' => [ 'shape' => 'ReplicaKmsKeyID', 'box' => true, ], ], ], 'Endpoints' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyMaxLength64String', ], 'value' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], 'EstablishedMultiRegionAccessPointPolicy' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Exclude' => [ 'type' => 'structure', 'members' => [ 'Buckets' => [ 'shape' => 'Buckets', ], 'Regions' => [ 'shape' => 'Regions', ], ], ], 'ExistingObjectReplication' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ExistingObjectReplicationStatus', ], ], ], 'ExistingObjectReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'Expiration' => [ 'type' => 'timestamp', ], 'ExpirationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ExpiredObjectDeleteMarker' => [ 'type' => 'boolean', ], 'Format' => [ 'type' => 'string', 'enum' => [ 'CSV', 'Parquet', ], ], 'FunctionArnString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(arn:(aws[a-zA-Z-]*)?:lambda:)?([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:)?(\\d{12}:)?(function:)?([a-zA-Z0-9-_]+)(:(\\$LATEST|[a-zA-Z0-9-_]+))?', ], 'GeneratedManifestEncryption' => [ 'type' => 'structure', 'members' => [ 'SSES3' => [ 'shape' => 'SSES3Encryption', 'locationName' => 'SSE-S3', ], 'SSEKMS' => [ 'shape' => 'SSEKMSEncryption', 'locationName' => 'SSE-KMS', ], ], ], 'GeneratedManifestFormat' => [ 'type' => 'string', 'enum' => [ 'S3InventoryReport_CSV_20211130', ], ], 'GetAccessGrantRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantId' => [ 'shape' => 'AccessGrantId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetAccessGrantResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantId' => [ 'shape' => 'AccessGrantId', ], 'AccessGrantArn' => [ 'shape' => 'AccessGrantArn', ], 'Grantee' => [ 'shape' => 'Grantee', ], 'Permission' => [ 'shape' => 'Permission', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationConfiguration' => [ 'shape' => 'AccessGrantsLocationConfiguration', ], 'GrantScope' => [ 'shape' => 'S3Prefix', ], 'ApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], ], ], 'GetAccessGrantsInstanceForPrefixRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'S3Prefix', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'S3Prefix' => [ 'shape' => 'S3Prefix', 'location' => 'querystring', 'locationName' => 's3prefix', ], ], ], 'GetAccessGrantsInstanceForPrefixResult' => [ 'type' => 'structure', 'members' => [ 'AccessGrantsInstanceArn' => [ 'shape' => 'AccessGrantsInstanceArn', ], 'AccessGrantsInstanceId' => [ 'shape' => 'AccessGrantsInstanceId', ], ], ], 'GetAccessGrantsInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetAccessGrantsInstanceResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetAccessGrantsInstanceResourcePolicyResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyDocument', ], 'Organization' => [ 'shape' => 'Organization', ], 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], ], ], 'GetAccessGrantsInstanceResult' => [ 'type' => 'structure', 'members' => [ 'AccessGrantsInstanceArn' => [ 'shape' => 'AccessGrantsInstanceArn', ], 'AccessGrantsInstanceId' => [ 'shape' => 'AccessGrantsInstanceId', ], 'IdentityCenterArn' => [ 'shape' => 'IdentityCenterArn', ], 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], ], ], 'GetAccessGrantsLocationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantsLocationId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetAccessGrantsLocationResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationArn' => [ 'shape' => 'AccessGrantsLocationArn', ], 'LocationScope' => [ 'shape' => 'S3Prefix', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'GetAccessPointConfigurationForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointConfigurationForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'Configuration' => [ 'shape' => 'ObjectLambdaConfiguration', ], ], ], 'GetAccessPointForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'PublicAccessBlockConfiguration', ], 'CreationDate' => [ 'shape' => 'CreationDate', ], 'Alias' => [ 'shape' => 'ObjectLambdaAccessPointAlias', ], ], ], 'GetAccessPointPolicyForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointPolicyForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'ObjectLambdaPolicy', ], ], ], 'GetAccessPointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointPolicyResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'GetAccessPointPolicyStatusForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointPolicyStatusForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'PolicyStatus' => [ 'shape' => 'PolicyStatus', ], ], ], 'GetAccessPointPolicyStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointPolicyStatusResult' => [ 'type' => 'structure', 'members' => [ 'PolicyStatus' => [ 'shape' => 'PolicyStatus', ], ], ], 'GetAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetAccessPointResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AccessPointName', ], 'Bucket' => [ 'shape' => 'BucketName', ], 'NetworkOrigin' => [ 'shape' => 'NetworkOrigin', ], 'VpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'PublicAccessBlockConfiguration', ], 'CreationDate' => [ 'shape' => 'CreationDate', ], 'Alias' => [ 'shape' => 'Alias', ], 'AccessPointArn' => [ 'shape' => 'S3AccessPointArn', ], 'Endpoints' => [ 'shape' => 'Endpoints', ], 'BucketAccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetBucketLifecycleConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketLifecycleConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'LifecycleRules', ], ], ], 'GetBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketPolicyResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'GetBucketReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketReplicationResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', ], ], ], 'GetBucketRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketResult' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'BucketName', ], 'PublicAccessBlockEnabled' => [ 'shape' => 'PublicAccessBlockEnabled', ], 'CreationDate' => [ 'shape' => 'CreationDate', ], ], ], 'GetBucketTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketTaggingResult' => [ 'type' => 'structure', 'required' => [ 'TagSet', ], 'members' => [ 'TagSet' => [ 'shape' => 'S3TagSet', ], ], ], 'GetBucketVersioningRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetBucketVersioningResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'BucketVersioningStatus', ], 'MFADelete' => [ 'shape' => 'MFADeleteStatus', 'locationName' => 'MfaDelete', ], ], ], 'GetDataAccessRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Target', 'Permission', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Target' => [ 'shape' => 'S3Prefix', 'location' => 'querystring', 'locationName' => 'target', ], 'Permission' => [ 'shape' => 'Permission', 'location' => 'querystring', 'locationName' => 'permission', ], 'DurationSeconds' => [ 'shape' => 'DurationSeconds', 'location' => 'querystring', 'locationName' => 'durationSeconds', ], 'Privilege' => [ 'shape' => 'Privilege', 'location' => 'querystring', 'locationName' => 'privilege', ], 'TargetType' => [ 'shape' => 'S3PrefixType', 'location' => 'querystring', 'locationName' => 'targetType', ], ], ], 'GetDataAccessResult' => [ 'type' => 'structure', 'members' => [ 'Credentials' => [ 'shape' => 'Credentials', ], 'MatchedGrantTarget' => [ 'shape' => 'S3Prefix', ], ], ], 'GetJobTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetJobTaggingResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'S3TagSet', ], ], ], 'GetMultiRegionAccessPointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'MultiRegionAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetMultiRegionAccessPointPolicyResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'MultiRegionAccessPointPolicyDocument', ], ], ], 'GetMultiRegionAccessPointPolicyStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'MultiRegionAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetMultiRegionAccessPointPolicyStatusResult' => [ 'type' => 'structure', 'members' => [ 'Established' => [ 'shape' => 'PolicyStatus', ], ], ], 'GetMultiRegionAccessPointRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'MultiRegionAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetMultiRegionAccessPointResult' => [ 'type' => 'structure', 'members' => [ 'AccessPoint' => [ 'shape' => 'MultiRegionAccessPointReport', ], ], ], 'GetMultiRegionAccessPointRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Mrap', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Mrap' => [ 'shape' => 'MultiRegionAccessPointId', 'location' => 'uri', 'locationName' => 'mrap', ], ], ], 'GetMultiRegionAccessPointRoutesResult' => [ 'type' => 'structure', 'members' => [ 'Mrap' => [ 'shape' => 'MultiRegionAccessPointId', ], 'Routes' => [ 'shape' => 'RouteList', ], ], ], 'GetPublicAccessBlockOutput' => [ 'type' => 'structure', 'members' => [ 'PublicAccessBlockConfiguration' => [ 'shape' => 'PublicAccessBlockConfiguration', ], ], 'payload' => 'PublicAccessBlockConfiguration', ], 'GetPublicAccessBlockRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetStorageLensConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetStorageLensConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'StorageLensConfiguration' => [ 'shape' => 'StorageLensConfiguration', ], ], 'payload' => 'StorageLensConfiguration', ], 'GetStorageLensConfigurationTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetStorageLensConfigurationTaggingResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'StorageLensTags', ], ], ], 'GetStorageLensGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'AccountId', ], 'members' => [ 'Name' => [ 'shape' => 'StorageLensGroupName', 'location' => 'uri', 'locationName' => 'name', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], ], 'GetStorageLensGroupResult' => [ 'type' => 'structure', 'members' => [ 'StorageLensGroup' => [ 'shape' => 'StorageLensGroup', ], ], 'payload' => 'StorageLensGroup', ], 'GrantFullControl' => [ 'type' => 'string', ], 'GrantRead' => [ 'type' => 'string', ], 'GrantReadACP' => [ 'type' => 'string', ], 'GrantWrite' => [ 'type' => 'string', ], 'GrantWriteACP' => [ 'type' => 'string', ], 'Grantee' => [ 'type' => 'structure', 'members' => [ 'GranteeType' => [ 'shape' => 'GranteeType', ], 'GranteeIdentifier' => [ 'shape' => 'GranteeIdentifier', ], ], ], 'GranteeIdentifier' => [ 'type' => 'string', ], 'GranteeType' => [ 'type' => 'string', 'enum' => [ 'DIRECTORY_USER', 'DIRECTORY_GROUP', 'IAM', ], ], 'IAMRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[^:]+:iam::\\d{12}:role/.*', ], 'ID' => [ 'type' => 'string', ], 'IdempotencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'IdentityCenterApplicationArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:[^:]+:sso:.*$', ], 'IdentityCenterArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:[^:]+:sso::(\\d{12}){0,1}:instance/.*$', ], 'Include' => [ 'type' => 'structure', 'members' => [ 'Buckets' => [ 'shape' => 'Buckets', ], 'Regions' => [ 'shape' => 'Regions', ], ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'IsEnabled' => [ 'type' => 'boolean', ], 'IsPublic' => [ 'type' => 'boolean', ], 'JobArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[^:]+:s3:[a-zA-Z0-9\\-]+:\\d{12}:job\\/.*', ], 'JobCreationTime' => [ 'type' => 'timestamp', ], 'JobDescriptor' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ConfirmationRequired' => [ 'shape' => 'ConfirmationRequired', 'box' => true, ], 'Description' => [ 'shape' => 'NonEmptyMaxLength256String', 'box' => true, ], 'JobArn' => [ 'shape' => 'JobArn', 'box' => true, ], 'Status' => [ 'shape' => 'JobStatus', ], 'Manifest' => [ 'shape' => 'JobManifest', 'box' => true, ], 'Operation' => [ 'shape' => 'JobOperation', 'box' => true, ], 'Priority' => [ 'shape' => 'JobPriority', ], 'ProgressSummary' => [ 'shape' => 'JobProgressSummary', 'box' => true, ], 'StatusUpdateReason' => [ 'shape' => 'JobStatusUpdateReason', 'box' => true, ], 'FailureReasons' => [ 'shape' => 'JobFailureList', 'box' => true, ], 'Report' => [ 'shape' => 'JobReport', 'box' => true, ], 'CreationTime' => [ 'shape' => 'JobCreationTime', ], 'TerminationDate' => [ 'shape' => 'JobTerminationDate', 'box' => true, ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', 'box' => true, ], 'SuspendedDate' => [ 'shape' => 'SuspendedDate', 'box' => true, ], 'SuspendedCause' => [ 'shape' => 'SuspendedCause', 'box' => true, ], 'ManifestGenerator' => [ 'shape' => 'JobManifestGenerator', ], 'GeneratedManifestDescriptor' => [ 'shape' => 'S3GeneratedManifestDescriptor', ], ], ], 'JobFailure' => [ 'type' => 'structure', 'members' => [ 'FailureCode' => [ 'shape' => 'JobFailureCode', ], 'FailureReason' => [ 'shape' => 'JobFailureReason', ], ], ], 'JobFailureCode' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'JobFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobFailure', ], ], 'JobFailureReason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'JobId' => [ 'type' => 'string', 'max' => 36, 'min' => 5, 'pattern' => '[a-zA-Z0-9\\-\\_]+', ], 'JobListDescriptor' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'Description' => [ 'shape' => 'NonEmptyMaxLength256String', ], 'Operation' => [ 'shape' => 'OperationName', ], 'Priority' => [ 'shape' => 'JobPriority', ], 'Status' => [ 'shape' => 'JobStatus', ], 'CreationTime' => [ 'shape' => 'JobCreationTime', ], 'TerminationDate' => [ 'shape' => 'JobTerminationDate', ], 'ProgressSummary' => [ 'shape' => 'JobProgressSummary', ], ], ], 'JobListDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobListDescriptor', ], ], 'JobManifest' => [ 'type' => 'structure', 'required' => [ 'Spec', 'Location', ], 'members' => [ 'Spec' => [ 'shape' => 'JobManifestSpec', ], 'Location' => [ 'shape' => 'JobManifestLocation', ], ], ], 'JobManifestFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobManifestFieldName', ], ], 'JobManifestFieldName' => [ 'type' => 'string', 'enum' => [ 'Ignore', 'Bucket', 'Key', 'VersionId', ], ], 'JobManifestFormat' => [ 'type' => 'string', 'enum' => [ 'S3BatchOperations_CSV_20180820', 'S3InventoryReport_CSV_20161130', ], ], 'JobManifestGenerator' => [ 'type' => 'structure', 'members' => [ 'S3JobManifestGenerator' => [ 'shape' => 'S3JobManifestGenerator', ], ], 'union' => true, ], 'JobManifestGeneratorFilter' => [ 'type' => 'structure', 'members' => [ 'EligibleForReplication' => [ 'shape' => 'Boolean', 'box' => true, ], 'CreatedAfter' => [ 'shape' => 'ObjectCreationTime', ], 'CreatedBefore' => [ 'shape' => 'ObjectCreationTime', ], 'ObjectReplicationStatuses' => [ 'shape' => 'ReplicationStatusFilterList', ], 'KeyNameConstraint' => [ 'shape' => 'KeyNameConstraint', ], 'ObjectSizeGreaterThanBytes' => [ 'shape' => 'ObjectSizeGreaterThanBytes', 'box' => true, ], 'ObjectSizeLessThanBytes' => [ 'shape' => 'ObjectSizeLessThanBytes', 'box' => true, ], 'MatchAnyStorageClass' => [ 'shape' => 'StorageClassList', ], ], ], 'JobManifestLocation' => [ 'type' => 'structure', 'required' => [ 'ObjectArn', 'ETag', ], 'members' => [ 'ObjectArn' => [ 'shape' => 'S3KeyArnString', ], 'ObjectVersionId' => [ 'shape' => 'S3ObjectVersionId', 'box' => true, ], 'ETag' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'JobManifestSpec' => [ 'type' => 'structure', 'required' => [ 'Format', ], 'members' => [ 'Format' => [ 'shape' => 'JobManifestFormat', ], 'Fields' => [ 'shape' => 'JobManifestFieldList', 'box' => true, ], ], ], 'JobNumberOfTasksFailed' => [ 'type' => 'long', 'min' => 0, ], 'JobNumberOfTasksSucceeded' => [ 'type' => 'long', 'min' => 0, ], 'JobOperation' => [ 'type' => 'structure', 'members' => [ 'LambdaInvoke' => [ 'shape' => 'LambdaInvokeOperation', 'box' => true, ], 'S3PutObjectCopy' => [ 'shape' => 'S3CopyObjectOperation', 'box' => true, ], 'S3PutObjectAcl' => [ 'shape' => 'S3SetObjectAclOperation', 'box' => true, ], 'S3PutObjectTagging' => [ 'shape' => 'S3SetObjectTaggingOperation', 'box' => true, ], 'S3DeleteObjectTagging' => [ 'shape' => 'S3DeleteObjectTaggingOperation', 'box' => true, ], 'S3InitiateRestoreObject' => [ 'shape' => 'S3InitiateRestoreObjectOperation', 'box' => true, ], 'S3PutObjectLegalHold' => [ 'shape' => 'S3SetObjectLegalHoldOperation', 'box' => true, ], 'S3PutObjectRetention' => [ 'shape' => 'S3SetObjectRetentionOperation', 'box' => true, ], 'S3ReplicateObject' => [ 'shape' => 'S3ReplicateObjectOperation', 'box' => true, ], ], ], 'JobPriority' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'JobProgressSummary' => [ 'type' => 'structure', 'members' => [ 'TotalNumberOfTasks' => [ 'shape' => 'JobTotalNumberOfTasks', 'box' => true, ], 'NumberOfTasksSucceeded' => [ 'shape' => 'JobNumberOfTasksSucceeded', 'box' => true, ], 'NumberOfTasksFailed' => [ 'shape' => 'JobNumberOfTasksFailed', 'box' => true, ], 'Timers' => [ 'shape' => 'JobTimers', ], ], ], 'JobReport' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3BucketArnString', 'box' => true, ], 'Format' => [ 'shape' => 'JobReportFormat', 'box' => true, ], 'Enabled' => [ 'shape' => 'Boolean', ], 'Prefix' => [ 'shape' => 'ReportPrefixString', 'box' => true, ], 'ReportScope' => [ 'shape' => 'JobReportScope', 'box' => true, ], ], ], 'JobReportFormat' => [ 'type' => 'string', 'enum' => [ 'Report_CSV_20180820', ], ], 'JobReportScope' => [ 'type' => 'string', 'enum' => [ 'AllTasks', 'FailedTasksOnly', ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Cancelled', 'Cancelling', 'Complete', 'Completing', 'Failed', 'Failing', 'New', 'Paused', 'Pausing', 'Preparing', 'Ready', 'Suspended', ], ], 'JobStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'JobStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobStatus', ], ], 'JobStatusUpdateReason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'JobTerminationDate' => [ 'type' => 'timestamp', ], 'JobTimeInStateSeconds' => [ 'type' => 'long', 'min' => 0, ], 'JobTimers' => [ 'type' => 'structure', 'members' => [ 'ElapsedTimeInActiveSeconds' => [ 'shape' => 'JobTimeInStateSeconds', 'box' => true, ], ], ], 'JobTotalNumberOfTasks' => [ 'type' => 'long', 'min' => 0, ], 'KeyNameConstraint' => [ 'type' => 'structure', 'members' => [ 'MatchAnyPrefix' => [ 'shape' => 'NonEmptyMaxLength1024StringList', ], 'MatchAnySuffix' => [ 'shape' => 'NonEmptyMaxLength1024StringList', ], 'MatchAnySubstring' => [ 'shape' => 'NonEmptyMaxLength1024StringList', ], ], ], 'KmsKeyArnString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'LambdaInvokeOperation' => [ 'type' => 'structure', 'members' => [ 'FunctionArn' => [ 'shape' => 'FunctionArnString', ], 'InvocationSchemaVersion' => [ 'shape' => 'NonEmptyMaxLength64String', ], 'UserArguments' => [ 'shape' => 'UserArguments', ], ], ], 'LifecycleConfiguration' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'LifecycleRules', ], ], ], 'LifecycleExpiration' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'Date', ], 'Days' => [ 'shape' => 'Days', ], 'ExpiredObjectDeleteMarker' => [ 'shape' => 'ExpiredObjectDeleteMarker', ], ], ], 'LifecycleRule' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Expiration' => [ 'shape' => 'LifecycleExpiration', ], 'ID' => [ 'shape' => 'ID', ], 'Filter' => [ 'shape' => 'LifecycleRuleFilter', ], 'Status' => [ 'shape' => 'ExpirationStatus', ], 'Transitions' => [ 'shape' => 'TransitionList', ], 'NoncurrentVersionTransitions' => [ 'shape' => 'NoncurrentVersionTransitionList', ], 'NoncurrentVersionExpiration' => [ 'shape' => 'NoncurrentVersionExpiration', ], 'AbortIncompleteMultipartUpload' => [ 'shape' => 'AbortIncompleteMultipartUpload', ], ], ], 'LifecycleRuleAndOperator' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'Tags' => [ 'shape' => 'S3TagSet', ], 'ObjectSizeGreaterThan' => [ 'shape' => 'ObjectSizeGreaterThanBytes', 'box' => true, ], 'ObjectSizeLessThan' => [ 'shape' => 'ObjectSizeLessThanBytes', 'box' => true, ], ], ], 'LifecycleRuleFilter' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'Tag' => [ 'shape' => 'S3Tag', ], 'And' => [ 'shape' => 'LifecycleRuleAndOperator', ], 'ObjectSizeGreaterThan' => [ 'shape' => 'ObjectSizeGreaterThanBytes', 'box' => true, ], 'ObjectSizeLessThan' => [ 'shape' => 'ObjectSizeLessThanBytes', 'box' => true, ], ], ], 'LifecycleRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleRule', 'locationName' => 'Rule', ], ], 'ListAccessGrantEntry' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantId' => [ 'shape' => 'AccessGrantId', ], 'AccessGrantArn' => [ 'shape' => 'AccessGrantArn', ], 'Grantee' => [ 'shape' => 'Grantee', ], 'Permission' => [ 'shape' => 'Permission', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationConfiguration' => [ 'shape' => 'AccessGrantsLocationConfiguration', ], 'GrantScope' => [ 'shape' => 'S3Prefix', ], 'ApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], ], ], 'ListAccessGrantsInstanceEntry' => [ 'type' => 'structure', 'members' => [ 'AccessGrantsInstanceId' => [ 'shape' => 'AccessGrantsInstanceId', ], 'AccessGrantsInstanceArn' => [ 'shape' => 'AccessGrantsInstanceArn', ], 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'IdentityCenterArn' => [ 'shape' => 'IdentityCenterArn', ], ], ], 'ListAccessGrantsInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'ContinuationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessGrantsInstancesResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ContinuationToken', ], 'AccessGrantsInstancesList' => [ 'shape' => 'AccessGrantsInstancesList', ], ], ], 'ListAccessGrantsLocationsEntry' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationArn' => [ 'shape' => 'AccessGrantsLocationArn', ], 'LocationScope' => [ 'shape' => 'S3Prefix', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'ListAccessGrantsLocationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'ContinuationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'LocationScope' => [ 'shape' => 'S3Prefix', 'location' => 'querystring', 'locationName' => 'locationscope', ], ], ], 'ListAccessGrantsLocationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ContinuationToken', ], 'AccessGrantsLocationsList' => [ 'shape' => 'AccessGrantsLocationsList', ], ], ], 'ListAccessGrantsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'ContinuationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'GranteeType' => [ 'shape' => 'GranteeType', 'location' => 'querystring', 'locationName' => 'granteetype', ], 'GranteeIdentifier' => [ 'shape' => 'GranteeIdentifier', 'location' => 'querystring', 'locationName' => 'granteeidentifier', ], 'Permission' => [ 'shape' => 'Permission', 'location' => 'querystring', 'locationName' => 'permission', ], 'GrantScope' => [ 'shape' => 'S3Prefix', 'location' => 'querystring', 'locationName' => 'grantscope', ], 'ApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', 'location' => 'querystring', 'locationName' => 'application_arn', ], ], ], 'ListAccessGrantsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ContinuationToken', ], 'AccessGrantsList' => [ 'shape' => 'AccessGrantsList', ], ], ], 'ListAccessPointsForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessPointsForObjectLambdaResult' => [ 'type' => 'structure', 'members' => [ 'ObjectLambdaAccessPointList' => [ 'shape' => 'ObjectLambdaAccessPointList', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'ListAccessPointsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'querystring', 'locationName' => 'bucket', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessPointsResult' => [ 'type' => 'structure', 'members' => [ 'AccessPointList' => [ 'shape' => 'AccessPointList', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobStatuses' => [ 'shape' => 'JobStatusList', 'location' => 'querystring', 'locationName' => 'jobStatuses', ], 'NextToken' => [ 'shape' => 'StringForNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListJobsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'StringForNextToken', ], 'Jobs' => [ 'shape' => 'JobListDescriptorList', ], ], ], 'ListMultiRegionAccessPointsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMultiRegionAccessPointsResult' => [ 'type' => 'structure', 'members' => [ 'AccessPoints' => [ 'shape' => 'MultiRegionAccessPointReportList', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'ListRegionalBucketsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'OutpostId' => [ 'shape' => 'NonEmptyMaxLength64String', 'contextParam' => [ 'name' => 'OutpostId', ], 'location' => 'header', 'locationName' => 'x-amz-outpost-id', ], ], ], 'ListRegionalBucketsResult' => [ 'type' => 'structure', 'members' => [ 'RegionalBucketList' => [ 'shape' => 'RegionalBucketList', ], 'NextToken' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'ListStorageLensConfigurationEntry' => [ 'type' => 'structure', 'required' => [ 'Id', 'StorageLensArn', 'HomeRegion', ], 'members' => [ 'Id' => [ 'shape' => 'ConfigId', ], 'StorageLensArn' => [ 'shape' => 'StorageLensArn', ], 'HomeRegion' => [ 'shape' => 'S3AWSRegion', ], 'IsEnabled' => [ 'shape' => 'IsEnabled', ], ], ], 'ListStorageLensConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'ContinuationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListStorageLensConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ContinuationToken', ], 'StorageLensConfigurationList' => [ 'shape' => 'StorageLensConfigurationList', ], ], ], 'ListStorageLensGroupEntry' => [ 'type' => 'structure', 'required' => [ 'Name', 'StorageLensGroupArn', 'HomeRegion', ], 'members' => [ 'Name' => [ 'shape' => 'StorageLensGroupName', ], 'StorageLensGroupArn' => [ 'shape' => 'StorageLensGroupArn', ], 'HomeRegion' => [ 'shape' => 'S3AWSRegion', ], ], ], 'ListStorageLensGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'NextToken' => [ 'shape' => 'ContinuationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListStorageLensGroupsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ContinuationToken', ], 'StorageLensGroupList' => [ 'shape' => 'StorageLensGroupList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ResourceArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ResourceArn' => [ 'shape' => 'S3ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'Location' => [ 'type' => 'string', ], 'MFA' => [ 'type' => 'string', ], 'MFADelete' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'MFADeleteStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ManifestPrefixString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'MatchAnyPrefix' => [ 'type' => 'list', 'member' => [ 'shape' => 'Prefix', 'locationName' => 'Prefix', ], ], 'MatchAnySuffix' => [ 'type' => 'list', 'member' => [ 'shape' => 'Suffix', 'locationName' => 'Suffix', ], ], 'MatchAnyTag' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Tag', 'locationName' => 'Tag', ], ], 'MatchObjectAge' => [ 'type' => 'structure', 'members' => [ 'DaysGreaterThan' => [ 'shape' => 'ObjectAgeValue', ], 'DaysLessThan' => [ 'shape' => 'ObjectAgeValue', ], ], ], 'MatchObjectSize' => [ 'type' => 'structure', 'members' => [ 'BytesGreaterThan' => [ 'shape' => 'ObjectSizeValue', ], 'BytesLessThan' => [ 'shape' => 'ObjectSizeValue', ], ], ], 'MaxLength1024String' => [ 'type' => 'string', 'max' => 1024, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'Metrics' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'MetricsStatus', ], 'EventThreshold' => [ 'shape' => 'ReplicationTimeValue', ], ], ], 'MetricsStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'MinStorageBytesPercentage' => [ 'type' => 'double', 'max' => 100, 'min' => 0.1, ], 'Minutes' => [ 'type' => 'integer', ], 'MultiRegionAccessPointAlias' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-z][a-z0-9]*[.]mrap$', ], 'MultiRegionAccessPointClientToken' => [ 'type' => 'string', 'max' => 64, 'pattern' => '\\S+', ], 'MultiRegionAccessPointId' => [ 'type' => 'string', 'max' => 200, 'pattern' => '^[a-zA-Z0-9\\:.-]{3,200}$', ], 'MultiRegionAccessPointName' => [ 'type' => 'string', 'max' => 50, 'pattern' => '^[a-z0-9][-a-z0-9]{1,48}[a-z0-9]$', ], 'MultiRegionAccessPointPolicyDocument' => [ 'type' => 'structure', 'members' => [ 'Established' => [ 'shape' => 'EstablishedMultiRegionAccessPointPolicy', ], 'Proposed' => [ 'shape' => 'ProposedMultiRegionAccessPointPolicy', ], ], ], 'MultiRegionAccessPointRegionalResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RegionName', ], 'RequestStatus' => [ 'shape' => 'AsyncRequestStatus', ], ], ], 'MultiRegionAccessPointRegionalResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiRegionAccessPointRegionalResponse', 'locationName' => 'Region', ], ], 'MultiRegionAccessPointReport' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'MultiRegionAccessPointName', ], 'Alias' => [ 'shape' => 'MultiRegionAccessPointAlias', ], 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'PublicAccessBlock' => [ 'shape' => 'PublicAccessBlockConfiguration', ], 'Status' => [ 'shape' => 'MultiRegionAccessPointStatus', ], 'Regions' => [ 'shape' => 'RegionReportList', ], ], ], 'MultiRegionAccessPointReportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiRegionAccessPointReport', 'locationName' => 'AccessPoint', ], ], 'MultiRegionAccessPointRoute' => [ 'type' => 'structure', 'required' => [ 'TrafficDialPercentage', ], 'members' => [ 'Bucket' => [ 'shape' => 'BucketName', ], 'Region' => [ 'shape' => 'RegionName', ], 'TrafficDialPercentage' => [ 'shape' => 'TrafficDialPercentage', ], ], ], 'MultiRegionAccessPointStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'INCONSISTENT_ACROSS_REGIONS', 'CREATING', 'PARTIALLY_CREATED', 'PARTIALLY_DELETED', 'DELETING', ], ], 'MultiRegionAccessPointsAsyncResponse' => [ 'type' => 'structure', 'members' => [ 'Regions' => [ 'shape' => 'MultiRegionAccessPointRegionalResponseList', ], ], ], 'NetworkOrigin' => [ 'type' => 'string', 'enum' => [ 'Internet', 'VPC', ], ], 'NoSuchPublicAccessBlockConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NoSuchPublicAccessBlockConfigurationMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NoSuchPublicAccessBlockConfigurationMessage' => [ 'type' => 'string', ], 'NonEmptyMaxLength1024String' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'NonEmptyMaxLength1024StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], 'NonEmptyMaxLength2048String' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NonEmptyMaxLength256String' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'NonEmptyMaxLength64String' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'NoncurrentVersionCount' => [ 'type' => 'integer', ], 'NoncurrentVersionExpiration' => [ 'type' => 'structure', 'members' => [ 'NoncurrentDays' => [ 'shape' => 'Days', ], 'NewerNoncurrentVersions' => [ 'shape' => 'NoncurrentVersionCount', 'box' => true, ], ], ], 'NoncurrentVersionTransition' => [ 'type' => 'structure', 'members' => [ 'NoncurrentDays' => [ 'shape' => 'Days', ], 'StorageClass' => [ 'shape' => 'TransitionStorageClass', ], ], ], 'NoncurrentVersionTransitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NoncurrentVersionTransition', 'locationName' => 'NoncurrentVersionTransition', ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ObjectAgeValue' => [ 'type' => 'integer', ], 'ObjectCreationTime' => [ 'type' => 'timestamp', ], 'ObjectLambdaAccessPoint' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', ], 'ObjectLambdaAccessPointArn' => [ 'shape' => 'ObjectLambdaAccessPointArn', ], 'Alias' => [ 'shape' => 'ObjectLambdaAccessPointAlias', ], ], ], 'ObjectLambdaAccessPointAlias' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'ObjectLambdaAccessPointAliasValue', ], 'Status' => [ 'shape' => 'ObjectLambdaAccessPointAliasStatus', ], ], ], 'ObjectLambdaAccessPointAliasStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'READY', ], 'max' => 16, 'min' => 2, ], 'ObjectLambdaAccessPointAliasValue' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[0-9a-z\\\\-]{3,63}', ], 'ObjectLambdaAccessPointArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[^:]+:s3-object-lambda:[^:]*:\\d{12}:accesspoint/.*', ], 'ObjectLambdaAccessPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectLambdaAccessPoint', 'locationName' => 'ObjectLambdaAccessPoint', ], ], 'ObjectLambdaAccessPointName' => [ 'type' => 'string', 'max' => 45, 'min' => 3, 'pattern' => '^[a-z0-9]([a-z0-9\\-]*[a-z0-9])?$', ], 'ObjectLambdaAllowedFeature' => [ 'type' => 'string', 'enum' => [ 'GetObject-Range', 'GetObject-PartNumber', 'HeadObject-Range', 'HeadObject-PartNumber', ], ], 'ObjectLambdaAllowedFeaturesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectLambdaAllowedFeature', 'locationName' => 'AllowedFeature', ], ], 'ObjectLambdaConfiguration' => [ 'type' => 'structure', 'required' => [ 'SupportingAccessPoint', 'TransformationConfigurations', ], 'members' => [ 'SupportingAccessPoint' => [ 'shape' => 'ObjectLambdaSupportingAccessPointArn', ], 'CloudWatchMetricsEnabled' => [ 'shape' => 'Boolean', ], 'AllowedFeatures' => [ 'shape' => 'ObjectLambdaAllowedFeaturesList', ], 'TransformationConfigurations' => [ 'shape' => 'ObjectLambdaTransformationConfigurationsList', ], ], ], 'ObjectLambdaContentTransformation' => [ 'type' => 'structure', 'members' => [ 'AwsLambda' => [ 'shape' => 'AwsLambdaTransformation', ], ], 'union' => true, ], 'ObjectLambdaPolicy' => [ 'type' => 'string', ], 'ObjectLambdaSupportingAccessPointArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[^:]+:s3:[^:]*:\\d{12}:accesspoint/.*', ], 'ObjectLambdaTransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'Actions', 'ContentTransformation', ], 'members' => [ 'Actions' => [ 'shape' => 'ObjectLambdaTransformationConfigurationActionsList', ], 'ContentTransformation' => [ 'shape' => 'ObjectLambdaContentTransformation', ], ], ], 'ObjectLambdaTransformationConfigurationAction' => [ 'type' => 'string', 'enum' => [ 'GetObject', 'HeadObject', 'ListObjects', 'ListObjectsV2', ], ], 'ObjectLambdaTransformationConfigurationActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectLambdaTransformationConfigurationAction', 'locationName' => 'Action', ], ], 'ObjectLambdaTransformationConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectLambdaTransformationConfiguration', 'locationName' => 'TransformationConfiguration', ], ], 'ObjectLockEnabledForBucket' => [ 'type' => 'boolean', ], 'ObjectSizeGreaterThanBytes' => [ 'type' => 'long', ], 'ObjectSizeLessThanBytes' => [ 'type' => 'long', ], 'ObjectSizeValue' => [ 'type' => 'long', ], 'OperationName' => [ 'type' => 'string', 'enum' => [ 'LambdaInvoke', 'S3PutObjectCopy', 'S3PutObjectAcl', 'S3PutObjectTagging', 'S3DeleteObjectTagging', 'S3InitiateRestoreObject', 'S3PutObjectLegalHold', 'S3PutObjectRetention', 'S3ReplicateObject', ], ], 'Organization' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => '^o-[a-z0-9]{10,32}$', ], 'OutputSchemaVersion' => [ 'type' => 'string', 'enum' => [ 'V_1', ], ], 'OwnerOverride' => [ 'type' => 'string', 'enum' => [ 'Destination', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'READ', 'WRITE', 'READWRITE', ], ], 'Policy' => [ 'type' => 'string', ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 350000, 'min' => 1, ], 'PolicyStatus' => [ 'type' => 'structure', 'members' => [ 'IsPublic' => [ 'shape' => 'IsPublic', 'locationName' => 'IsPublic', ], ], ], 'Prefix' => [ 'type' => 'string', ], 'PrefixLevel' => [ 'type' => 'structure', 'required' => [ 'StorageMetrics', ], 'members' => [ 'StorageMetrics' => [ 'shape' => 'PrefixLevelStorageMetrics', ], ], ], 'PrefixLevelStorageMetrics' => [ 'type' => 'structure', 'members' => [ 'IsEnabled' => [ 'shape' => 'IsEnabled', ], 'SelectionCriteria' => [ 'shape' => 'SelectionCriteria', ], ], ], 'Priority' => [ 'type' => 'integer', ], 'Privilege' => [ 'type' => 'string', 'enum' => [ 'Minimal', 'Default', ], ], 'ProposedMultiRegionAccessPointPolicy' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'PublicAccessBlockConfiguration' => [ 'type' => 'structure', 'members' => [ 'BlockPublicAcls' => [ 'shape' => 'Setting', 'locationName' => 'BlockPublicAcls', ], 'IgnorePublicAcls' => [ 'shape' => 'Setting', 'locationName' => 'IgnorePublicAcls', ], 'BlockPublicPolicy' => [ 'shape' => 'Setting', 'locationName' => 'BlockPublicPolicy', ], 'RestrictPublicBuckets' => [ 'shape' => 'Setting', 'locationName' => 'RestrictPublicBuckets', ], ], ], 'PublicAccessBlockEnabled' => [ 'type' => 'boolean', ], 'PutAccessGrantsInstanceResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Policy', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Policy' => [ 'shape' => 'PolicyDocument', ], 'Organization' => [ 'shape' => 'Organization', ], ], ], 'PutAccessGrantsInstanceResourcePolicyResult' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyDocument', ], 'Organization' => [ 'shape' => 'Organization', ], 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], ], ], 'PutAccessPointConfigurationForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', 'Configuration', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], 'Configuration' => [ 'shape' => 'ObjectLambdaConfiguration', ], ], ], 'PutAccessPointPolicyForObjectLambdaRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', 'Policy', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'ObjectLambdaAccessPointName', 'location' => 'uri', 'locationName' => 'name', ], 'Policy' => [ 'shape' => 'ObjectLambdaPolicy', ], ], ], 'PutAccessPointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', 'Policy', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Name' => [ 'shape' => 'AccessPointName', 'contextParam' => [ 'name' => 'AccessPointName', ], 'location' => 'uri', 'locationName' => 'name', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'PutBucketLifecycleConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'LifecycleConfiguration' => [ 'shape' => 'LifecycleConfiguration', 'locationName' => 'LifecycleConfiguration', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], ], 'payload' => 'LifecycleConfiguration', ], 'PutBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', 'Policy', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'ConfirmRemoveSelfBucketAccess' => [ 'shape' => 'ConfirmRemoveSelfBucketAccess', 'location' => 'header', 'locationName' => 'x-amz-confirm-remove-self-bucket-access', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'PutBucketReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', 'ReplicationConfiguration', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'ReplicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', 'locationName' => 'ReplicationConfiguration', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], ], 'payload' => 'ReplicationConfiguration', ], 'PutBucketTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', 'Tagging', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'Tagging' => [ 'shape' => 'Tagging', 'locationName' => 'Tagging', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], ], 'payload' => 'Tagging', ], 'PutBucketVersioningRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Bucket', 'VersioningConfiguration', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Bucket' => [ 'shape' => 'BucketName', 'contextParam' => [ 'name' => 'Bucket', ], 'location' => 'uri', 'locationName' => 'name', ], 'MFA' => [ 'shape' => 'MFA', 'location' => 'header', 'locationName' => 'x-amz-mfa', ], 'VersioningConfiguration' => [ 'shape' => 'VersioningConfiguration', 'locationName' => 'VersioningConfiguration', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], ], 'payload' => 'VersioningConfiguration', ], 'PutJobTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', 'Tags', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], 'Tags' => [ 'shape' => 'S3TagSet', ], ], ], 'PutJobTaggingResult' => [ 'type' => 'structure', 'members' => [], ], 'PutMultiRegionAccessPointPolicyInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Policy', ], 'members' => [ 'Name' => [ 'shape' => 'MultiRegionAccessPointName', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'PutMultiRegionAccessPointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ClientToken', 'Details', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ClientToken' => [ 'shape' => 'MultiRegionAccessPointClientToken', 'idempotencyToken' => true, ], 'Details' => [ 'shape' => 'PutMultiRegionAccessPointPolicyInput', ], ], ], 'PutMultiRegionAccessPointPolicyResult' => [ 'type' => 'structure', 'members' => [ 'RequestTokenARN' => [ 'shape' => 'AsyncRequestTokenARN', ], ], ], 'PutPublicAccessBlockRequest' => [ 'type' => 'structure', 'required' => [ 'PublicAccessBlockConfiguration', 'AccountId', ], 'members' => [ 'PublicAccessBlockConfiguration' => [ 'shape' => 'PublicAccessBlockConfiguration', 'locationName' => 'PublicAccessBlockConfiguration', 'xmlNamespace' => [ 'uri' => 'http://awss3control.amazonaws.com/doc/2018-08-20/', ], ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], ], 'payload' => 'PublicAccessBlockConfiguration', ], 'PutStorageLensConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', 'StorageLensConfiguration', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'StorageLensConfiguration' => [ 'shape' => 'StorageLensConfiguration', ], 'Tags' => [ 'shape' => 'StorageLensTags', ], ], ], 'PutStorageLensConfigurationTaggingRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigId', 'AccountId', 'Tags', ], 'members' => [ 'ConfigId' => [ 'shape' => 'ConfigId', 'location' => 'uri', 'locationName' => 'storagelensid', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Tags' => [ 'shape' => 'StorageLensTags', ], ], ], 'PutStorageLensConfigurationTaggingResult' => [ 'type' => 'structure', 'members' => [], ], 'Region' => [ 'type' => 'structure', 'required' => [ 'Bucket', ], 'members' => [ 'Bucket' => [ 'shape' => 'BucketName', ], 'BucketAccountId' => [ 'shape' => 'AccountId', ], ], ], 'RegionCreationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', 'locationName' => 'Region', ], ], 'RegionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RegionReport' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'BucketName', ], 'Region' => [ 'shape' => 'RegionName', ], 'BucketAccountId' => [ 'shape' => 'AccountId', ], ], ], 'RegionReportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionReport', 'locationName' => 'Region', ], ], 'RegionalBucket' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'PublicAccessBlockEnabled', 'CreationDate', ], 'members' => [ 'Bucket' => [ 'shape' => 'BucketName', ], 'BucketArn' => [ 'shape' => 'S3RegionalBucketArn', ], 'PublicAccessBlockEnabled' => [ 'shape' => 'PublicAccessBlockEnabled', ], 'CreationDate' => [ 'shape' => 'CreationDate', ], 'OutpostId' => [ 'shape' => 'NonEmptyMaxLength64String', ], ], ], 'RegionalBucketList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionalBucket', 'locationName' => 'RegionalBucket', ], ], 'Regions' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3AWSRegion', 'locationName' => 'Region', ], ], 'ReplicaKmsKeyID' => [ 'type' => 'string', ], 'ReplicaModifications' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ReplicaModificationsStatus', ], ], ], 'ReplicaModificationsStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'required' => [ 'Role', 'Rules', ], 'members' => [ 'Role' => [ 'shape' => 'Role', ], 'Rules' => [ 'shape' => 'ReplicationRules', ], ], ], 'ReplicationRule' => [ 'type' => 'structure', 'required' => [ 'Status', 'Destination', 'Bucket', ], 'members' => [ 'ID' => [ 'shape' => 'ID', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Prefix' => [ 'shape' => 'Prefix', 'deprecated' => true, 'deprecatedMessage' => 'Prefix has been deprecated', ], 'Filter' => [ 'shape' => 'ReplicationRuleFilter', ], 'Status' => [ 'shape' => 'ReplicationRuleStatus', ], 'SourceSelectionCriteria' => [ 'shape' => 'SourceSelectionCriteria', ], 'ExistingObjectReplication' => [ 'shape' => 'ExistingObjectReplication', ], 'Destination' => [ 'shape' => 'Destination', ], 'DeleteMarkerReplication' => [ 'shape' => 'DeleteMarkerReplication', ], 'Bucket' => [ 'shape' => 'BucketIdentifierString', ], ], ], 'ReplicationRuleAndOperator' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'Tags' => [ 'shape' => 'S3TagSet', ], ], ], 'ReplicationRuleFilter' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'Tag' => [ 'shape' => 'S3Tag', ], 'And' => [ 'shape' => 'ReplicationRuleAndOperator', ], ], ], 'ReplicationRuleStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ReplicationRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationRule', 'locationName' => 'Rule', ], ], 'ReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'FAILED', 'REPLICA', 'NONE', ], ], 'ReplicationStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationStatus', ], ], 'ReplicationStorageClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'REDUCED_REDUNDANCY', 'STANDARD_IA', 'ONEZONE_IA', 'INTELLIGENT_TIERING', 'GLACIER', 'DEEP_ARCHIVE', 'OUTPOSTS', 'GLACIER_IR', ], ], 'ReplicationTime' => [ 'type' => 'structure', 'required' => [ 'Status', 'Time', ], 'members' => [ 'Status' => [ 'shape' => 'ReplicationTimeStatus', ], 'Time' => [ 'shape' => 'ReplicationTimeValue', ], ], ], 'ReplicationTimeStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ReplicationTimeValue' => [ 'type' => 'structure', 'members' => [ 'Minutes' => [ 'shape' => 'Minutes', 'box' => true, ], ], ], 'ReportPrefixString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'RequestedJobStatus' => [ 'type' => 'string', 'enum' => [ 'Cancelled', 'Ready', ], ], 'Role' => [ 'type' => 'string', ], 'RouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiRegionAccessPointRoute', 'locationName' => 'Route', ], ], 'S3AWSRegion' => [ 'type' => 'string', 'max' => 30, 'min' => 5, 'pattern' => '[a-z0-9\\-]+', ], 'S3AccessControlList' => [ 'type' => 'structure', 'required' => [ 'Owner', ], 'members' => [ 'Owner' => [ 'shape' => 'S3ObjectOwner', ], 'Grants' => [ 'shape' => 'S3GrantList', ], ], ], 'S3AccessControlPolicy' => [ 'type' => 'structure', 'members' => [ 'AccessControlList' => [ 'shape' => 'S3AccessControlList', 'box' => true, ], 'CannedAccessControlList' => [ 'shape' => 'S3CannedAccessControlList', 'box' => true, ], ], ], 'S3AccessPointArn' => [ 'type' => 'string', 'max' => 128, 'min' => 4, ], 'S3BucketArnString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:[^:]+:s3:.*', ], 'S3BucketDestination' => [ 'type' => 'structure', 'required' => [ 'Format', 'OutputSchemaVersion', 'AccountId', 'Arn', ], 'members' => [ 'Format' => [ 'shape' => 'Format', ], 'OutputSchemaVersion' => [ 'shape' => 'OutputSchemaVersion', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'Arn' => [ 'shape' => 'S3BucketArnString', ], 'Prefix' => [ 'shape' => 'Prefix', ], 'Encryption' => [ 'shape' => 'StorageLensDataExportEncryption', ], ], ], 'S3CannedAccessControlList' => [ 'type' => 'string', 'enum' => [ 'private', 'public-read', 'public-read-write', 'aws-exec-read', 'authenticated-read', 'bucket-owner-read', 'bucket-owner-full-control', ], ], 'S3ChecksumAlgorithm' => [ 'type' => 'string', 'enum' => [ 'CRC32', 'CRC32C', 'SHA1', 'SHA256', ], ], 'S3ContentLength' => [ 'type' => 'long', 'min' => 0, ], 'S3CopyObjectOperation' => [ 'type' => 'structure', 'members' => [ 'TargetResource' => [ 'shape' => 'S3RegionalOrS3ExpressBucketArnString', ], 'CannedAccessControlList' => [ 'shape' => 'S3CannedAccessControlList', 'box' => true, ], 'AccessControlGrants' => [ 'shape' => 'S3GrantList', 'box' => true, ], 'MetadataDirective' => [ 'shape' => 'S3MetadataDirective', ], 'ModifiedSinceConstraint' => [ 'shape' => 'TimeStamp', ], 'NewObjectMetadata' => [ 'shape' => 'S3ObjectMetadata', ], 'NewObjectTagging' => [ 'shape' => 'S3TagSet', ], 'RedirectLocation' => [ 'shape' => 'NonEmptyMaxLength2048String', ], 'RequesterPays' => [ 'shape' => 'Boolean', ], 'StorageClass' => [ 'shape' => 'S3StorageClass', ], 'UnModifiedSinceConstraint' => [ 'shape' => 'TimeStamp', ], 'SSEAwsKmsKeyId' => [ 'shape' => 'KmsKeyArnString', ], 'TargetKeyPrefix' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'ObjectLockLegalHoldStatus' => [ 'shape' => 'S3ObjectLockLegalHoldStatus', ], 'ObjectLockMode' => [ 'shape' => 'S3ObjectLockMode', ], 'ObjectLockRetainUntilDate' => [ 'shape' => 'TimeStamp', ], 'BucketKeyEnabled' => [ 'shape' => 'Boolean', ], 'ChecksumAlgorithm' => [ 'shape' => 'S3ChecksumAlgorithm', ], ], ], 'S3DeleteObjectTaggingOperation' => [ 'type' => 'structure', 'members' => [], ], 'S3ExpirationInDays' => [ 'type' => 'integer', 'min' => 0, ], 'S3GeneratedManifestDescriptor' => [ 'type' => 'structure', 'members' => [ 'Format' => [ 'shape' => 'GeneratedManifestFormat', ], 'Location' => [ 'shape' => 'JobManifestLocation', ], ], ], 'S3GlacierJobTier' => [ 'type' => 'string', 'enum' => [ 'BULK', 'STANDARD', ], ], 'S3Grant' => [ 'type' => 'structure', 'members' => [ 'Grantee' => [ 'shape' => 'S3Grantee', ], 'Permission' => [ 'shape' => 'S3Permission', ], ], ], 'S3GrantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Grant', ], ], 'S3Grantee' => [ 'type' => 'structure', 'members' => [ 'TypeIdentifier' => [ 'shape' => 'S3GranteeTypeIdentifier', ], 'Identifier' => [ 'shape' => 'NonEmptyMaxLength1024String', 'box' => true, ], 'DisplayName' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'S3GranteeTypeIdentifier' => [ 'type' => 'string', 'enum' => [ 'id', 'emailAddress', 'uri', ], ], 'S3InitiateRestoreObjectOperation' => [ 'type' => 'structure', 'members' => [ 'ExpirationInDays' => [ 'shape' => 'S3ExpirationInDays', 'box' => true, ], 'GlacierJobTier' => [ 'shape' => 'S3GlacierJobTier', ], ], ], 'S3JobManifestGenerator' => [ 'type' => 'structure', 'required' => [ 'SourceBucket', 'EnableManifestOutput', ], 'members' => [ 'ExpectedBucketOwner' => [ 'shape' => 'AccountId', ], 'SourceBucket' => [ 'shape' => 'S3BucketArnString', ], 'ManifestOutputLocation' => [ 'shape' => 'S3ManifestOutputLocation', ], 'Filter' => [ 'shape' => 'JobManifestGeneratorFilter', ], 'EnableManifestOutput' => [ 'shape' => 'Boolean', ], ], ], 'S3KeyArnString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => 'arn:[^:]+:s3:.*', ], 'S3ManifestOutputLocation' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'ManifestFormat', ], 'members' => [ 'ExpectedManifestBucketOwner' => [ 'shape' => 'AccountId', ], 'Bucket' => [ 'shape' => 'S3BucketArnString', ], 'ManifestPrefix' => [ 'shape' => 'ManifestPrefixString', ], 'ManifestEncryption' => [ 'shape' => 'GeneratedManifestEncryption', ], 'ManifestFormat' => [ 'shape' => 'GeneratedManifestFormat', ], ], ], 'S3MetadataDirective' => [ 'type' => 'string', 'enum' => [ 'COPY', 'REPLACE', ], ], 'S3ObjectLockLegalHold' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'S3ObjectLockLegalHoldStatus', ], ], ], 'S3ObjectLockLegalHoldStatus' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ON', ], ], 'S3ObjectLockMode' => [ 'type' => 'string', 'enum' => [ 'COMPLIANCE', 'GOVERNANCE', ], ], 'S3ObjectLockRetentionMode' => [ 'type' => 'string', 'enum' => [ 'COMPLIANCE', 'GOVERNANCE', ], ], 'S3ObjectMetadata' => [ 'type' => 'structure', 'members' => [ 'CacheControl' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'ContentDisposition' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'ContentEncoding' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'ContentLanguage' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'UserMetadata' => [ 'shape' => 'S3UserMetadata', ], 'ContentLength' => [ 'shape' => 'S3ContentLength', 'box' => true, ], 'ContentMD5' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'ContentType' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'HttpExpiresDate' => [ 'shape' => 'TimeStamp', ], 'RequesterCharged' => [ 'shape' => 'Boolean', ], 'SSEAlgorithm' => [ 'shape' => 'S3SSEAlgorithm', ], ], ], 'S3ObjectOwner' => [ 'type' => 'structure', 'members' => [ 'ID' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'DisplayName' => [ 'shape' => 'NonEmptyMaxLength1024String', ], ], ], 'S3ObjectVersionId' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'S3Permission' => [ 'type' => 'string', 'enum' => [ 'FULL_CONTROL', 'READ', 'WRITE', 'READ_ACP', 'WRITE_ACP', ], ], 'S3Prefix' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '^.+$', ], 'S3PrefixType' => [ 'type' => 'string', 'enum' => [ 'Object', ], ], 'S3RegionalBucketArn' => [ 'type' => 'string', 'max' => 128, 'min' => 4, ], 'S3RegionalOrS3ExpressBucketArnString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:[^:]+:(s3|s3express):.*', ], 'S3ReplicateObjectOperation' => [ 'type' => 'structure', 'members' => [], ], 'S3ResourceArn' => [ 'type' => 'string', 'max' => 1011, 'pattern' => 'arn:[^:]+:s3:[^:].*', ], 'S3Retention' => [ 'type' => 'structure', 'members' => [ 'RetainUntilDate' => [ 'shape' => 'TimeStamp', ], 'Mode' => [ 'shape' => 'S3ObjectLockRetentionMode', ], ], ], 'S3SSEAlgorithm' => [ 'type' => 'string', 'enum' => [ 'AES256', 'KMS', ], ], 'S3SetObjectAclOperation' => [ 'type' => 'structure', 'members' => [ 'AccessControlPolicy' => [ 'shape' => 'S3AccessControlPolicy', ], ], ], 'S3SetObjectLegalHoldOperation' => [ 'type' => 'structure', 'required' => [ 'LegalHold', ], 'members' => [ 'LegalHold' => [ 'shape' => 'S3ObjectLockLegalHold', ], ], ], 'S3SetObjectRetentionOperation' => [ 'type' => 'structure', 'required' => [ 'Retention', ], 'members' => [ 'BypassGovernanceRetention' => [ 'shape' => 'Boolean', 'box' => true, ], 'Retention' => [ 'shape' => 'S3Retention', ], ], ], 'S3SetObjectTaggingOperation' => [ 'type' => 'structure', 'members' => [ 'TagSet' => [ 'shape' => 'S3TagSet', ], ], ], 'S3StorageClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'STANDARD_IA', 'ONEZONE_IA', 'GLACIER', 'INTELLIGENT_TIERING', 'DEEP_ARCHIVE', 'GLACIER_IR', ], ], 'S3Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKeyString', ], 'Value' => [ 'shape' => 'TagValueString', ], ], ], 'S3TagSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Tag', ], ], 'S3UserMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyMaxLength1024String', ], 'value' => [ 'shape' => 'MaxLength1024String', ], 'max' => 8192, ], 'SSEKMS' => [ 'type' => 'structure', 'required' => [ 'KeyId', ], 'members' => [ 'KeyId' => [ 'shape' => 'SSEKMSKeyId', ], ], 'locationName' => 'SSE-KMS', ], 'SSEKMSEncryption' => [ 'type' => 'structure', 'required' => [ 'KeyId', ], 'members' => [ 'KeyId' => [ 'shape' => 'KmsKeyArnString', ], ], 'locationName' => 'SSE-KMS', ], 'SSEKMSKeyId' => [ 'type' => 'string', ], 'SSES3' => [ 'type' => 'structure', 'members' => [], 'locationName' => 'SSE-S3', ], 'SSES3Encryption' => [ 'type' => 'structure', 'members' => [], 'locationName' => 'SSE-S3', ], 'SecretAccessKey' => [ 'type' => 'string', 'sensitive' => true, ], 'SelectionCriteria' => [ 'type' => 'structure', 'members' => [ 'Delimiter' => [ 'shape' => 'StorageLensPrefixLevelDelimiter', ], 'MaxDepth' => [ 'shape' => 'StorageLensPrefixLevelMaxDepth', ], 'MinStorageBytesPercentage' => [ 'shape' => 'MinStorageBytesPercentage', ], ], ], 'SessionToken' => [ 'type' => 'string', 'sensitive' => true, ], 'Setting' => [ 'type' => 'boolean', ], 'SourceSelectionCriteria' => [ 'type' => 'structure', 'members' => [ 'SseKmsEncryptedObjects' => [ 'shape' => 'SseKmsEncryptedObjects', ], 'ReplicaModifications' => [ 'shape' => 'ReplicaModifications', ], ], ], 'SseKmsEncryptedObjects' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'SseKmsEncryptedObjectsStatus', ], ], ], 'SseKmsEncryptedObjectsStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'StorageClassList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3StorageClass', ], ], 'StorageLensArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:storage\\-lens\\/.*', ], 'StorageLensAwsOrg' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'AwsOrgArn', ], ], ], 'StorageLensConfiguration' => [ 'type' => 'structure', 'required' => [ 'Id', 'AccountLevel', 'IsEnabled', ], 'members' => [ 'Id' => [ 'shape' => 'ConfigId', ], 'AccountLevel' => [ 'shape' => 'AccountLevel', ], 'Include' => [ 'shape' => 'Include', ], 'Exclude' => [ 'shape' => 'Exclude', ], 'DataExport' => [ 'shape' => 'StorageLensDataExport', ], 'IsEnabled' => [ 'shape' => 'IsEnabled', ], 'AwsOrg' => [ 'shape' => 'StorageLensAwsOrg', ], 'StorageLensArn' => [ 'shape' => 'StorageLensArn', ], ], ], 'StorageLensConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListStorageLensConfigurationEntry', 'locationName' => 'StorageLensConfiguration', ], 'flattened' => true, ], 'StorageLensDataExport' => [ 'type' => 'structure', 'members' => [ 'S3BucketDestination' => [ 'shape' => 'S3BucketDestination', ], 'CloudWatchMetrics' => [ 'shape' => 'CloudWatchMetrics', ], ], ], 'StorageLensDataExportEncryption' => [ 'type' => 'structure', 'members' => [ 'SSES3' => [ 'shape' => 'SSES3', 'locationName' => 'SSE-S3', ], 'SSEKMS' => [ 'shape' => 'SSEKMS', 'locationName' => 'SSE-KMS', ], ], ], 'StorageLensGroup' => [ 'type' => 'structure', 'required' => [ 'Name', 'Filter', ], 'members' => [ 'Name' => [ 'shape' => 'StorageLensGroupName', ], 'Filter' => [ 'shape' => 'StorageLensGroupFilter', ], 'StorageLensGroupArn' => [ 'shape' => 'StorageLensGroupArn', ], ], ], 'StorageLensGroupAndOperator' => [ 'type' => 'structure', 'members' => [ 'MatchAnyPrefix' => [ 'shape' => 'MatchAnyPrefix', ], 'MatchAnySuffix' => [ 'shape' => 'MatchAnySuffix', ], 'MatchAnyTag' => [ 'shape' => 'MatchAnyTag', ], 'MatchObjectAge' => [ 'shape' => 'MatchObjectAge', ], 'MatchObjectSize' => [ 'shape' => 'MatchObjectSize', ], ], ], 'StorageLensGroupArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 4, 'pattern' => 'arn:[a-z\\-]+:s3:[a-z0-9\\-]+:\\d{12}:storage\\-lens\\-group\\/.*', ], 'StorageLensGroupFilter' => [ 'type' => 'structure', 'members' => [ 'MatchAnyPrefix' => [ 'shape' => 'MatchAnyPrefix', ], 'MatchAnySuffix' => [ 'shape' => 'MatchAnySuffix', ], 'MatchAnyTag' => [ 'shape' => 'MatchAnyTag', ], 'MatchObjectAge' => [ 'shape' => 'MatchObjectAge', ], 'MatchObjectSize' => [ 'shape' => 'MatchObjectSize', ], 'And' => [ 'shape' => 'StorageLensGroupAndOperator', ], 'Or' => [ 'shape' => 'StorageLensGroupOrOperator', ], ], ], 'StorageLensGroupLevel' => [ 'type' => 'structure', 'members' => [ 'SelectionCriteria' => [ 'shape' => 'StorageLensGroupLevelSelectionCriteria', ], ], ], 'StorageLensGroupLevelExclude' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageLensGroupArn', 'locationName' => 'Arn', ], ], 'StorageLensGroupLevelInclude' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageLensGroupArn', 'locationName' => 'Arn', ], ], 'StorageLensGroupLevelSelectionCriteria' => [ 'type' => 'structure', 'members' => [ 'Include' => [ 'shape' => 'StorageLensGroupLevelInclude', ], 'Exclude' => [ 'shape' => 'StorageLensGroupLevelExclude', ], ], ], 'StorageLensGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListStorageLensGroupEntry', 'locationName' => 'StorageLensGroup', ], 'flattened' => true, ], 'StorageLensGroupName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-\\_]+', ], 'StorageLensGroupOrOperator' => [ 'type' => 'structure', 'members' => [ 'MatchAnyPrefix' => [ 'shape' => 'MatchAnyPrefix', ], 'MatchAnySuffix' => [ 'shape' => 'MatchAnySuffix', ], 'MatchAnyTag' => [ 'shape' => 'MatchAnyTag', ], 'MatchObjectAge' => [ 'shape' => 'MatchObjectAge', ], 'MatchObjectSize' => [ 'shape' => 'MatchObjectSize', ], ], ], 'StorageLensPrefixLevelDelimiter' => [ 'type' => 'string', 'max' => 1, ], 'StorageLensPrefixLevelMaxDepth' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'StorageLensTag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKeyString', ], 'Value' => [ 'shape' => 'TagValueString', ], ], ], 'StorageLensTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageLensTag', 'locationName' => 'Tag', ], ], 'StringForNextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[A-Za-z0-9\\+\\:\\/\\=\\?\\#-_]+$', ], 'SubmitMultiRegionAccessPointRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Mrap', 'RouteUpdates', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'Mrap' => [ 'shape' => 'MultiRegionAccessPointId', 'location' => 'uri', 'locationName' => 'mrap', ], 'RouteUpdates' => [ 'shape' => 'RouteList', ], ], ], 'SubmitMultiRegionAccessPointRoutesResult' => [ 'type' => 'structure', 'members' => [], ], 'Suffix' => [ 'type' => 'string', ], 'SuspendedCause' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SuspendedDate' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKeyString', ], 'Value' => [ 'shape' => 'TagValueString', ], ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKeyString', ], 'max' => 50, 'min' => 0, ], 'TagKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ResourceArn', 'Tags', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ResourceArn' => [ 'shape' => 'S3ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'TagValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tagging' => [ 'type' => 'structure', 'required' => [ 'TagSet', ], 'members' => [ 'TagSet' => [ 'shape' => 'S3TagSet', ], ], ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'TrafficDialPercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Transition' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'Date', ], 'Days' => [ 'shape' => 'Days', ], 'StorageClass' => [ 'shape' => 'TransitionStorageClass', ], ], ], 'TransitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Transition', 'locationName' => 'Transition', ], ], 'TransitionStorageClass' => [ 'type' => 'string', 'enum' => [ 'GLACIER', 'STANDARD_IA', 'ONEZONE_IA', 'INTELLIGENT_TIERING', 'DEEP_ARCHIVE', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ResourceArn', 'TagKeys', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'ResourceArn' => [ 'shape' => 'S3ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccessGrantsLocationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccessGrantsLocationId', 'IAMRoleArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', 'location' => 'uri', 'locationName' => 'id', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'UpdateAccessGrantsLocationResult' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'CreationTimestamp', ], 'AccessGrantsLocationId' => [ 'shape' => 'AccessGrantsLocationId', ], 'AccessGrantsLocationArn' => [ 'shape' => 'AccessGrantsLocationArn', ], 'LocationScope' => [ 'shape' => 'S3Prefix', ], 'IAMRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'UpdateJobPriorityRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', 'Priority', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], 'Priority' => [ 'shape' => 'JobPriority', 'location' => 'querystring', 'locationName' => 'priority', ], ], ], 'UpdateJobPriorityResult' => [ 'type' => 'structure', 'required' => [ 'JobId', 'Priority', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'Priority' => [ 'shape' => 'JobPriority', ], ], ], 'UpdateJobStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'JobId', 'RequestedJobStatus', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'id', ], 'RequestedJobStatus' => [ 'shape' => 'RequestedJobStatus', 'location' => 'querystring', 'locationName' => 'requestedJobStatus', ], 'StatusUpdateReason' => [ 'shape' => 'JobStatusUpdateReason', 'location' => 'querystring', 'locationName' => 'statusUpdateReason', ], ], ], 'UpdateJobStatusResult' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'Status' => [ 'shape' => 'JobStatus', ], 'StatusUpdateReason' => [ 'shape' => 'JobStatusUpdateReason', ], ], ], 'UpdateStorageLensGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'AccountId', 'StorageLensGroup', ], 'members' => [ 'Name' => [ 'shape' => 'StorageLensGroupName', 'location' => 'uri', 'locationName' => 'name', ], 'AccountId' => [ 'shape' => 'AccountId', 'contextParam' => [ 'name' => 'AccountId', ], 'hostLabel' => true, 'location' => 'header', 'locationName' => 'x-amz-account-id', ], 'StorageLensGroup' => [ 'shape' => 'StorageLensGroup', ], ], ], 'UserArguments' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyMaxLength64String', ], 'value' => [ 'shape' => 'MaxLength1024String', ], 'max' => 10, 'min' => 1, ], 'VersioningConfiguration' => [ 'type' => 'structure', 'members' => [ 'MFADelete' => [ 'shape' => 'MFADelete', 'locationName' => 'MfaDelete', ], 'Status' => [ 'shape' => 'BucketVersioningStatus', ], ], ], 'VpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'VpcId', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], ], ], 'VpcId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], ], 'clientContextParams' => [ 'UseArnRegion' => [ 'documentation' => 'Enables this client to use an ARN\'s region when constructing an endpoint instead of the client\'s configured region.', 'type' => 'boolean', ], ],];
