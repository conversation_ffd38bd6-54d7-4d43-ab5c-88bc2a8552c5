<?php
// This file was auto-generated from sdk-root/src/data/appstream/2016-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-12-01', 'endpointPrefix' => 'appstream2', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon AppStream', 'serviceId' => 'AppStream', 'signatureVersion' => 'v4', 'signingName' => 'appstream', 'targetPrefix' => 'PhotonAdminProxyService', 'uid' => 'appstream-2016-12-01', ], 'operations' => [ 'AssociateAppBlockBuilderAppBlock' => [ 'name' => 'AssociateAppBlockBuilderAppBlock', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateAppBlockBuilderAppBlockRequest', ], 'output' => [ 'shape' => 'AssociateAppBlockBuilderAppBlockResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'AssociateApplicationFleet' => [ 'name' => 'AssociateApplicationFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateApplicationFleetRequest', ], 'output' => [ 'shape' => 'AssociateApplicationFleetResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'AssociateApplicationToEntitlement' => [ 'name' => 'AssociateApplicationToEntitlement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateApplicationToEntitlementRequest', ], 'output' => [ 'shape' => 'AssociateApplicationToEntitlementResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'AssociateFleet' => [ 'name' => 'AssociateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateFleetRequest', ], 'output' => [ 'shape' => 'AssociateFleetResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'IncompatibleImageException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'BatchAssociateUserStack' => [ 'name' => 'BatchAssociateUserStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchAssociateUserStackRequest', ], 'output' => [ 'shape' => 'BatchAssociateUserStackResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'BatchDisassociateUserStack' => [ 'name' => 'BatchDisassociateUserStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDisassociateUserStackRequest', ], 'output' => [ 'shape' => 'BatchDisassociateUserStackResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CopyImage' => [ 'name' => 'CopyImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyImageRequest', ], 'output' => [ 'shape' => 'CopyImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'IncompatibleImageException', ], ], ], 'CreateAppBlock' => [ 'name' => 'CreateAppBlock', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppBlockRequest', ], 'output' => [ 'shape' => 'CreateAppBlockResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateAppBlockBuilder' => [ 'name' => 'CreateAppBlockBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppBlockBuilderRequest', ], 'output' => [ 'shape' => 'CreateAppBlockBuilderResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateAppBlockBuilderStreamingURL' => [ 'name' => 'CreateAppBlockBuilderStreamingURL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppBlockBuilderStreamingURLRequest', ], 'output' => [ 'shape' => 'CreateAppBlockBuilderStreamingURLResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateDirectoryConfig' => [ 'name' => 'CreateDirectoryConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDirectoryConfigRequest', ], 'output' => [ 'shape' => 'CreateDirectoryConfigResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidRoleException', ], ], ], 'CreateEntitlement' => [ 'name' => 'CreateEntitlement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEntitlementRequest', ], 'output' => [ 'shape' => 'CreateEntitlementResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'EntitlementAlreadyExistsException', ], ], ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetRequest', ], 'output' => [ 'shape' => 'CreateFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'IncompatibleImageException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateImageBuilder' => [ 'name' => 'CreateImageBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateImageBuilderRequest', ], 'output' => [ 'shape' => 'CreateImageBuilderResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'IncompatibleImageException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateImageBuilderStreamingURL' => [ 'name' => 'CreateImageBuilderStreamingURL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateImageBuilderStreamingURLRequest', ], 'output' => [ 'shape' => 'CreateImageBuilderStreamingURLResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateStack' => [ 'name' => 'CreateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackRequest', ], 'output' => [ 'shape' => 'CreateStackResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateStreamingURL' => [ 'name' => 'CreateStreamingURL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStreamingURLRequest', ], 'output' => [ 'shape' => 'CreateStreamingURLResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateUpdatedImage' => [ 'name' => 'CreateUpdatedImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUpdatedImageRequest', ], 'output' => [ 'shape' => 'CreateUpdatedImageResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'IncompatibleImageException', ], ], ], 'CreateUsageReportSubscription' => [ 'name' => 'CreateUsageReportSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUsageReportSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateUsageReportSubscriptionResult', ], 'errors' => [ [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResult', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DeleteAppBlock' => [ 'name' => 'DeleteAppBlock', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppBlockRequest', ], 'output' => [ 'shape' => 'DeleteAppBlockResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteAppBlockBuilder' => [ 'name' => 'DeleteAppBlockBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppBlockBuilderRequest', ], 'output' => [ 'shape' => 'DeleteAppBlockBuilderResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteDirectoryConfig' => [ 'name' => 'DeleteDirectoryConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDirectoryConfigRequest', ], 'output' => [ 'shape' => 'DeleteDirectoryConfigResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteEntitlement' => [ 'name' => 'DeleteEntitlement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEntitlementRequest', ], 'output' => [ 'shape' => 'DeleteEntitlementResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetRequest', ], 'output' => [ 'shape' => 'DeleteFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteImage' => [ 'name' => 'DeleteImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImageRequest', ], 'output' => [ 'shape' => 'DeleteImageResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteImageBuilder' => [ 'name' => 'DeleteImageBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImageBuilderRequest', ], 'output' => [ 'shape' => 'DeleteImageBuilderResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteImagePermissions' => [ 'name' => 'DeleteImagePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImagePermissionsRequest', ], 'output' => [ 'shape' => 'DeleteImagePermissionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteStack' => [ 'name' => 'DeleteStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackRequest', ], 'output' => [ 'shape' => 'DeleteStackResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteUsageReportSubscription' => [ 'name' => 'DeleteUsageReportSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUsageReportSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteUsageReportSubscriptionResult', ], 'errors' => [ [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeAppBlockBuilderAppBlockAssociations' => [ 'name' => 'DescribeAppBlockBuilderAppBlockAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppBlockBuilderAppBlockAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeAppBlockBuilderAppBlockAssociationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DescribeAppBlockBuilders' => [ 'name' => 'DescribeAppBlockBuilders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppBlockBuildersRequest', ], 'output' => [ 'shape' => 'DescribeAppBlockBuildersResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeAppBlocks' => [ 'name' => 'DescribeAppBlocks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppBlocksRequest', ], 'output' => [ 'shape' => 'DescribeAppBlocksResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApplicationFleetAssociations' => [ 'name' => 'DescribeApplicationFleetAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationFleetAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationFleetAssociationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DescribeApplications' => [ 'name' => 'DescribeApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationsResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDirectoryConfigs' => [ 'name' => 'DescribeDirectoryConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDirectoryConfigsRequest', ], 'output' => [ 'shape' => 'DescribeDirectoryConfigsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeEntitlements' => [ 'name' => 'DescribeEntitlements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEntitlementsRequest', ], 'output' => [ 'shape' => 'DescribeEntitlementsResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], ], ], 'DescribeFleets' => [ 'name' => 'DescribeFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetsRequest', ], 'output' => [ 'shape' => 'DescribeFleetsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeImageBuilders' => [ 'name' => 'DescribeImageBuilders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageBuildersRequest', ], 'output' => [ 'shape' => 'DescribeImageBuildersResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeImagePermissions' => [ 'name' => 'DescribeImagePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImagePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeImagePermissionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeImages' => [ 'name' => 'DescribeImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImagesRequest', ], 'output' => [ 'shape' => 'DescribeImagesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeSessions' => [ 'name' => 'DescribeSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSessionsRequest', ], 'output' => [ 'shape' => 'DescribeSessionsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeStacks' => [ 'name' => 'DescribeStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStacksRequest', ], 'output' => [ 'shape' => 'DescribeStacksResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeUsageReportSubscriptions' => [ 'name' => 'DescribeUsageReportSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUsageReportSubscriptionsRequest', ], 'output' => [ 'shape' => 'DescribeUsageReportSubscriptionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccountStatusException', ], ], ], 'DescribeUserStackAssociations' => [ 'name' => 'DescribeUserStackAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserStackAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeUserStackAssociationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DescribeUsers' => [ 'name' => 'DescribeUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUsersRequest', ], 'output' => [ 'shape' => 'DescribeUsersResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DisableUser' => [ 'name' => 'DisableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableUserRequest', ], 'output' => [ 'shape' => 'DisableUserResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateAppBlockBuilderAppBlock' => [ 'name' => 'DisassociateAppBlockBuilderAppBlock', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateAppBlockBuilderAppBlockRequest', ], 'output' => [ 'shape' => 'DisassociateAppBlockBuilderAppBlockResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateApplicationFleet' => [ 'name' => 'DisassociateApplicationFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateApplicationFleetRequest', ], 'output' => [ 'shape' => 'DisassociateApplicationFleetResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DisassociateApplicationFromEntitlement' => [ 'name' => 'DisassociateApplicationFromEntitlement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateApplicationFromEntitlementRequest', ], 'output' => [ 'shape' => 'DisassociateApplicationFromEntitlementResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DisassociateFleet' => [ 'name' => 'DisassociateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateFleetRequest', ], 'output' => [ 'shape' => 'DisassociateFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'EnableUser' => [ 'name' => 'EnableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableUserRequest', ], 'output' => [ 'shape' => 'EnableUserResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccountStatusException', ], ], ], 'ExpireSession' => [ 'name' => 'ExpireSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExpireSessionRequest', ], 'output' => [ 'shape' => 'ExpireSessionResult', ], ], 'ListAssociatedFleets' => [ 'name' => 'ListAssociatedFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociatedFleetsRequest', ], 'output' => [ 'shape' => 'ListAssociatedFleetsResult', ], ], 'ListAssociatedStacks' => [ 'name' => 'ListAssociatedStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociatedStacksRequest', ], 'output' => [ 'shape' => 'ListAssociatedStacksResult', ], ], 'ListEntitledApplications' => [ 'name' => 'ListEntitledApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntitledApplicationsRequest', ], 'output' => [ 'shape' => 'ListEntitledApplicationsResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartAppBlockBuilder' => [ 'name' => 'StartAppBlockBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAppBlockBuilderRequest', ], 'output' => [ 'shape' => 'StartAppBlockBuilderResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartFleet' => [ 'name' => 'StartFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFleetRequest', ], 'output' => [ 'shape' => 'StartFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'InvalidRoleException', ], ], ], 'StartImageBuilder' => [ 'name' => 'StartImageBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartImageBuilderRequest', ], 'output' => [ 'shape' => 'StartImageBuilderResult', ], 'errors' => [ [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'IncompatibleImageException', ], ], ], 'StopAppBlockBuilder' => [ 'name' => 'StopAppBlockBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAppBlockBuilderRequest', ], 'output' => [ 'shape' => 'StopAppBlockBuilderResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopFleet' => [ 'name' => 'StopFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopFleetRequest', ], 'output' => [ 'shape' => 'StopFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'StopImageBuilder' => [ 'name' => 'StopImageBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopImageBuilderRequest', ], 'output' => [ 'shape' => 'StopImageBuilderResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAppBlockBuilder' => [ 'name' => 'UpdateAppBlockBuilder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAppBlockBuilderRequest', ], 'output' => [ 'shape' => 'UpdateAppBlockBuilderResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateDirectoryConfig' => [ 'name' => 'UpdateDirectoryConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDirectoryConfigRequest', ], 'output' => [ 'shape' => 'UpdateDirectoryConfigResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'InvalidRoleException', ], ], ], 'UpdateEntitlement' => [ 'name' => 'UpdateEntitlement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEntitlementRequest', ], 'output' => [ 'shape' => 'UpdateEntitlementResult', ], 'errors' => [ [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntitlementNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateFleet' => [ 'name' => 'UpdateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetRequest', ], 'output' => [ 'shape' => 'UpdateFleetResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RequestLimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'IncompatibleImageException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'UpdateImagePermissions' => [ 'name' => 'UpdateImagePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateImagePermissionsRequest', ], 'output' => [ 'shape' => 'UpdateImagePermissionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceNotAvailableException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateStack' => [ 'name' => 'UpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackRequest', ], 'output' => [ 'shape' => 'UpdateStackResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccountStatusException', ], [ 'shape' => 'IncompatibleImageException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], ], 'shapes' => [ 'AccessEndpoint' => [ 'type' => 'structure', 'required' => [ 'EndpointType', ], 'members' => [ 'EndpointType' => [ 'shape' => 'AccessEndpointType', ], 'VpceId' => [ 'shape' => 'String', ], ], ], 'AccessEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessEndpoint', ], 'max' => 4, 'min' => 1, ], 'AccessEndpointType' => [ 'type' => 'string', 'enum' => [ 'STREAMING', ], ], 'AccountName' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'AccountPassword' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'sensitive' => true, ], 'Action' => [ 'type' => 'string', 'enum' => [ 'CLIPBOARD_COPY_FROM_LOCAL_DEVICE', 'CLIPBOARD_COPY_TO_LOCAL_DEVICE', 'FILE_UPLOAD', 'FILE_DOWNLOAD', 'PRINTING_TO_LOCAL_DEVICE', 'DOMAIN_PASSWORD_SIGNIN', 'DOMAIN_SMART_CARD_SIGNIN', ], ], 'AppBlock' => [ 'type' => 'structure', 'required' => [ 'Name', 'Arn', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'SourceS3Location' => [ 'shape' => 'S3Location', ], 'SetupScriptDetails' => [ 'shape' => 'ScriptDetails', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'PostSetupScriptDetails' => [ 'shape' => 'ScriptDetails', ], 'PackagingType' => [ 'shape' => 'PackagingType', ], 'State' => [ 'shape' => 'AppBlockState', ], 'AppBlockErrors' => [ 'shape' => 'ErrorDetailsList', ], ], ], 'AppBlockBuilder' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Name', 'Platform', 'InstanceType', 'VpcConfig', 'State', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'AppBlockBuilderPlatformType', ], 'InstanceType' => [ 'shape' => 'String', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'State' => [ 'shape' => 'AppBlockBuilderState', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'AppBlockBuilderErrors' => [ 'shape' => 'ResourceErrors', ], 'StateChangeReason' => [ 'shape' => 'AppBlockBuilderStateChangeReason', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], ], ], 'AppBlockBuilderAppBlockAssociation' => [ 'type' => 'structure', 'required' => [ 'AppBlockArn', 'AppBlockBuilderName', ], 'members' => [ 'AppBlockArn' => [ 'shape' => 'Arn', ], 'AppBlockBuilderName' => [ 'shape' => 'Name', ], ], ], 'AppBlockBuilderAppBlockAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppBlockBuilderAppBlockAssociation', ], 'max' => 25, 'min' => 1, ], 'AppBlockBuilderAttribute' => [ 'type' => 'string', 'enum' => [ 'IAM_ROLE_ARN', 'ACCESS_ENDPOINTS', 'VPC_CONFIGURATION_SECURITY_GROUP_IDS', ], ], 'AppBlockBuilderAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppBlockBuilderAttribute', ], ], 'AppBlockBuilderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppBlockBuilder', ], ], 'AppBlockBuilderPlatformType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_SERVER_2019', ], ], 'AppBlockBuilderState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', ], ], 'AppBlockBuilderStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'AppBlockBuilderStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'AppBlockBuilderStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', ], ], 'AppBlockState' => [ 'type' => 'string', 'enum' => [ 'INACTIVE', 'ACTIVE', ], ], 'AppBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppBlock', ], ], 'AppVisibility' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ASSOCIATED', ], ], 'Application' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'IconURL' => [ 'shape' => 'String', ], 'LaunchPath' => [ 'shape' => 'String', ], 'LaunchParameters' => [ 'shape' => 'String', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'WorkingDirectory' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'Arn', ], 'AppBlockArn' => [ 'shape' => 'Arn', ], 'IconS3Location' => [ 'shape' => 'S3Location', ], 'Platforms' => [ 'shape' => 'Platforms', ], 'InstanceFamilies' => [ 'shape' => 'StringList', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ApplicationAttribute' => [ 'type' => 'string', 'enum' => [ 'LAUNCH_PARAMETERS', 'WORKING_DIRECTORY', ], ], 'ApplicationAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationAttribute', ], 'max' => 2, ], 'ApplicationFleetAssociation' => [ 'type' => 'structure', 'required' => [ 'FleetName', 'ApplicationArn', ], 'members' => [ 'FleetName' => [ 'shape' => 'String', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], ], ], 'ApplicationFleetAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationFleetAssociation', ], 'max' => 25, 'min' => 1, ], 'ApplicationSettings' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'SettingsGroup' => [ 'shape' => 'SettingsGroup', ], ], ], 'ApplicationSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'SettingsGroup' => [ 'shape' => 'SettingsGroup', ], 'S3BucketName' => [ 'shape' => 'String', ], ], ], 'Applications' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], ], 'AppstreamAgentVersion' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:aws(?:\\-cn|\\-iso\\-b|\\-iso|\\-us\\-gov)?:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.\\\\-]{0,1023}$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssociateAppBlockBuilderAppBlockRequest' => [ 'type' => 'structure', 'required' => [ 'AppBlockArn', 'AppBlockBuilderName', ], 'members' => [ 'AppBlockArn' => [ 'shape' => 'Arn', ], 'AppBlockBuilderName' => [ 'shape' => 'Name', ], ], ], 'AssociateAppBlockBuilderAppBlockResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilderAppBlockAssociation' => [ 'shape' => 'AppBlockBuilderAppBlockAssociation', ], ], ], 'AssociateApplicationFleetRequest' => [ 'type' => 'structure', 'required' => [ 'FleetName', 'ApplicationArn', ], 'members' => [ 'FleetName' => [ 'shape' => 'Name', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateApplicationFleetResult' => [ 'type' => 'structure', 'members' => [ 'ApplicationFleetAssociation' => [ 'shape' => 'ApplicationFleetAssociation', ], ], ], 'AssociateApplicationToEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', 'EntitlementName', 'ApplicationIdentifier', ], 'members' => [ 'StackName' => [ 'shape' => 'Name', ], 'EntitlementName' => [ 'shape' => 'Name', ], 'ApplicationIdentifier' => [ 'shape' => 'String', ], ], ], 'AssociateApplicationToEntitlementResult' => [ 'type' => 'structure', 'members' => [], ], 'AssociateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'FleetName', 'StackName', ], 'members' => [ 'FleetName' => [ 'shape' => 'String', ], 'StackName' => [ 'shape' => 'String', ], ], ], 'AssociateFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'API', 'SAML', 'USERPOOL', 'AWS_AD', ], ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '^\\d+$', ], 'AwsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], 'max' => 5, 'min' => 1, ], 'BatchAssociateUserStackRequest' => [ 'type' => 'structure', 'required' => [ 'UserStackAssociations', ], 'members' => [ 'UserStackAssociations' => [ 'shape' => 'UserStackAssociationList', ], ], ], 'BatchAssociateUserStackResult' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'UserStackAssociationErrorList', ], ], ], 'BatchDisassociateUserStackRequest' => [ 'type' => 'structure', 'required' => [ 'UserStackAssociations', ], 'members' => [ 'UserStackAssociations' => [ 'shape' => 'UserStackAssociationList', ], ], ], 'BatchDisassociateUserStackResult' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'UserStackAssociationErrorList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanObject' => [ 'type' => 'boolean', ], 'CertificateBasedAuthProperties' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'CertificateBasedAuthStatus', ], 'CertificateAuthorityArn' => [ 'shape' => 'Arn', ], ], ], 'CertificateBasedAuthStatus' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'ENABLED_NO_DIRECTORY_LOGIN_FALLBACK', ], ], 'ComputeCapacity' => [ 'type' => 'structure', 'members' => [ 'DesiredInstances' => [ 'shape' => 'Integer', ], 'DesiredSessions' => [ 'shape' => 'Integer', ], ], ], 'ComputeCapacityStatus' => [ 'type' => 'structure', 'required' => [ 'Desired', ], 'members' => [ 'Desired' => [ 'shape' => 'Integer', ], 'Running' => [ 'shape' => 'Integer', ], 'InUse' => [ 'shape' => 'Integer', ], 'Available' => [ 'shape' => 'Integer', ], 'DesiredUserSessions' => [ 'shape' => 'Integer', ], 'AvailableUserSessions' => [ 'shape' => 'Integer', ], 'ActiveUserSessions' => [ 'shape' => 'Integer', ], 'ActualUserSessions' => [ 'shape' => 'Integer', ], ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CopyImageRequest' => [ 'type' => 'structure', 'required' => [ 'SourceImageName', 'DestinationImageName', 'DestinationRegion', ], 'members' => [ 'SourceImageName' => [ 'shape' => 'Name', ], 'DestinationImageName' => [ 'shape' => 'Name', ], 'DestinationRegion' => [ 'shape' => 'RegionName', ], 'DestinationImageDescription' => [ 'shape' => 'Description', ], ], ], 'CopyImageResponse' => [ 'type' => 'structure', 'members' => [ 'DestinationImageName' => [ 'shape' => 'Name', ], ], ], 'CreateAppBlockBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Platform', 'InstanceType', 'VpcConfig', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Tags' => [ 'shape' => 'Tags', ], 'Platform' => [ 'shape' => 'AppBlockBuilderPlatformType', ], 'InstanceType' => [ 'shape' => 'String', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], ], ], 'CreateAppBlockBuilderResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilder' => [ 'shape' => 'AppBlockBuilder', ], ], ], 'CreateAppBlockBuilderStreamingURLRequest' => [ 'type' => 'structure', 'required' => [ 'AppBlockBuilderName', ], 'members' => [ 'AppBlockBuilderName' => [ 'shape' => 'Name', ], 'Validity' => [ 'shape' => 'Long', ], ], ], 'CreateAppBlockBuilderStreamingURLResult' => [ 'type' => 'structure', 'members' => [ 'StreamingURL' => [ 'shape' => 'String', ], 'Expires' => [ 'shape' => 'Timestamp', ], ], ], 'CreateAppBlockRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SourceS3Location', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'SourceS3Location' => [ 'shape' => 'S3Location', ], 'SetupScriptDetails' => [ 'shape' => 'ScriptDetails', ], 'Tags' => [ 'shape' => 'Tags', ], 'PostSetupScriptDetails' => [ 'shape' => 'ScriptDetails', ], 'PackagingType' => [ 'shape' => 'PackagingType', ], ], ], 'CreateAppBlockResult' => [ 'type' => 'structure', 'members' => [ 'AppBlock' => [ 'shape' => 'AppBlock', ], ], ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IconS3Location', 'LaunchPath', 'Platforms', 'InstanceFamilies', 'AppBlockArn', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'IconS3Location' => [ 'shape' => 'S3Location', ], 'LaunchPath' => [ 'shape' => 'String', ], 'WorkingDirectory' => [ 'shape' => 'String', ], 'LaunchParameters' => [ 'shape' => 'String', ], 'Platforms' => [ 'shape' => 'Platforms', ], 'InstanceFamilies' => [ 'shape' => 'StringList', ], 'AppBlockArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateApplicationResult' => [ 'type' => 'structure', 'members' => [ 'Application' => [ 'shape' => 'Application', ], ], ], 'CreateDirectoryConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryName', 'OrganizationalUnitDistinguishedNames', ], 'members' => [ 'DirectoryName' => [ 'shape' => 'DirectoryName', ], 'OrganizationalUnitDistinguishedNames' => [ 'shape' => 'OrganizationalUnitDistinguishedNamesList', ], 'ServiceAccountCredentials' => [ 'shape' => 'ServiceAccountCredentials', ], 'CertificateBasedAuthProperties' => [ 'shape' => 'CertificateBasedAuthProperties', ], ], ], 'CreateDirectoryConfigResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryConfig' => [ 'shape' => 'DirectoryConfig', ], ], ], 'CreateEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'StackName', 'AppVisibility', 'Attributes', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'StackName' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'AppVisibility' => [ 'shape' => 'AppVisibility', ], 'Attributes' => [ 'shape' => 'EntitlementAttributeList', ], ], ], 'CreateEntitlementResult' => [ 'type' => 'structure', 'members' => [ 'Entitlement' => [ 'shape' => 'Entitlement', ], ], ], 'CreateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceType', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'ImageName' => [ 'shape' => 'Name', ], 'ImageArn' => [ 'shape' => 'Arn', ], 'InstanceType' => [ 'shape' => 'String', ], 'FleetType' => [ 'shape' => 'FleetType', ], 'ComputeCapacity' => [ 'shape' => 'ComputeCapacity', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'MaxUserDurationInSeconds' => [ 'shape' => 'Integer', ], 'DisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DomainJoinInfo' => [ 'shape' => 'DomainJoinInfo', ], 'Tags' => [ 'shape' => 'Tags', ], 'IdleDisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'StreamView' => [ 'shape' => 'StreamView', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'MaxConcurrentSessions' => [ 'shape' => 'Integer', ], 'UsbDeviceFilterStrings' => [ 'shape' => 'UsbDeviceFilterStrings', ], 'SessionScriptS3Location' => [ 'shape' => 'S3Location', ], 'MaxSessionsPerInstance' => [ 'shape' => 'Integer', ], ], ], 'CreateFleetResult' => [ 'type' => 'structure', 'members' => [ 'Fleet' => [ 'shape' => 'Fleet', ], ], ], 'CreateImageBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceType', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'ImageName' => [ 'shape' => 'String', ], 'ImageArn' => [ 'shape' => 'Arn', ], 'InstanceType' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DomainJoinInfo' => [ 'shape' => 'DomainJoinInfo', ], 'AppstreamAgentVersion' => [ 'shape' => 'AppstreamAgentVersion', ], 'Tags' => [ 'shape' => 'Tags', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], ], ], 'CreateImageBuilderResult' => [ 'type' => 'structure', 'members' => [ 'ImageBuilder' => [ 'shape' => 'ImageBuilder', ], ], ], 'CreateImageBuilderStreamingURLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Validity' => [ 'shape' => 'Long', ], ], ], 'CreateImageBuilderStreamingURLResult' => [ 'type' => 'structure', 'members' => [ 'StreamingURL' => [ 'shape' => 'String', ], 'Expires' => [ 'shape' => 'Timestamp', ], ], ], 'CreateStackRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'StorageConnectors' => [ 'shape' => 'StorageConnectorList', ], 'RedirectURL' => [ 'shape' => 'RedirectURL', ], 'FeedbackURL' => [ 'shape' => 'FeedbackURL', ], 'UserSettings' => [ 'shape' => 'UserSettingList', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettings', ], 'Tags' => [ 'shape' => 'Tags', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], 'EmbedHostDomains' => [ 'shape' => 'EmbedHostDomains', ], 'StreamingExperienceSettings' => [ 'shape' => 'StreamingExperienceSettings', ], ], ], 'CreateStackResult' => [ 'type' => 'structure', 'members' => [ 'Stack' => [ 'shape' => 'Stack', ], ], ], 'CreateStreamingURLRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', 'FleetName', 'UserId', ], 'members' => [ 'StackName' => [ 'shape' => 'String', ], 'FleetName' => [ 'shape' => 'String', ], 'UserId' => [ 'shape' => 'StreamingUrlUserId', ], 'ApplicationId' => [ 'shape' => 'String', ], 'Validity' => [ 'shape' => 'Long', ], 'SessionContext' => [ 'shape' => 'String', ], ], ], 'CreateStreamingURLResult' => [ 'type' => 'structure', 'members' => [ 'StreamingURL' => [ 'shape' => 'String', ], 'Expires' => [ 'shape' => 'Timestamp', ], ], ], 'CreateUpdatedImageRequest' => [ 'type' => 'structure', 'required' => [ 'existingImageName', 'newImageName', ], 'members' => [ 'existingImageName' => [ 'shape' => 'Name', ], 'newImageName' => [ 'shape' => 'Name', ], 'newImageDescription' => [ 'shape' => 'Description', ], 'newImageDisplayName' => [ 'shape' => 'DisplayName', ], 'newImageTags' => [ 'shape' => 'Tags', ], 'dryRun' => [ 'shape' => 'Boolean', ], ], ], 'CreateUpdatedImageResult' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'Image', ], 'canUpdateImage' => [ 'shape' => 'Boolean', ], ], ], 'CreateUsageReportSubscriptionRequest' => [ 'type' => 'structure', 'members' => [], ], 'CreateUsageReportSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 'String', ], 'Schedule' => [ 'shape' => 'UsageReportSchedule', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AuthenticationType', ], 'members' => [ 'UserName' => [ 'shape' => 'Username', ], 'MessageAction' => [ 'shape' => 'MessageAction', ], 'FirstName' => [ 'shape' => 'UserAttributeValue', ], 'LastName' => [ 'shape' => 'UserAttributeValue', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], ], ], 'CreateUserResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppBlockBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'DeleteAppBlockBuilderResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppBlockRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'DeleteAppBlockResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'DeleteApplicationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDirectoryConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryName', ], 'members' => [ 'DirectoryName' => [ 'shape' => 'DirectoryName', ], ], ], 'DeleteDirectoryConfigResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'StackName', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'StackName' => [ 'shape' => 'Name', ], ], ], 'DeleteEntitlementResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFleetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'DeleteFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImageBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'DeleteImageBuilderResult' => [ 'type' => 'structure', 'members' => [ 'ImageBuilder' => [ 'shape' => 'ImageBuilder', ], ], ], 'DeleteImagePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SharedAccountId', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'SharedAccountId' => [ 'shape' => 'AwsAccountId', ], ], ], 'DeleteImagePermissionsResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImageRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'DeleteImageResult' => [ 'type' => 'structure', 'members' => [ 'Image' => [ 'shape' => 'Image', ], ], ], 'DeleteStackRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'DeleteStackResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUsageReportSubscriptionRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUsageReportSubscriptionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AuthenticationType', ], 'members' => [ 'UserName' => [ 'shape' => 'Username', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], ], ], 'DeleteUserResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAppBlockBuilderAppBlockAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'AppBlockArn' => [ 'shape' => 'Arn', ], 'AppBlockBuilderName' => [ 'shape' => 'Name', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeAppBlockBuilderAppBlockAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilderAppBlockAssociations' => [ 'shape' => 'AppBlockBuilderAppBlockAssociationsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeAppBlockBuildersRequest' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'DescribeAppBlockBuildersResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilders' => [ 'shape' => 'AppBlockBuilderList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeAppBlocksRequest' => [ 'type' => 'structure', 'members' => [ 'Arns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'DescribeAppBlocksResult' => [ 'type' => 'structure', 'members' => [ 'AppBlocks' => [ 'shape' => 'AppBlocks', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeApplicationFleetAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'FleetName' => [ 'shape' => 'Name', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeApplicationFleetAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'ApplicationFleetAssociations' => [ 'shape' => 'ApplicationFleetAssociationList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'Arns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'DescribeApplicationsResult' => [ 'type' => 'structure', 'members' => [ 'Applications' => [ 'shape' => 'Applications', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeDirectoryConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryNames' => [ 'shape' => 'DirectoryNameList', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeDirectoryConfigsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryConfigs' => [ 'shape' => 'DirectoryConfigList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeEntitlementsRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'StackName' => [ 'shape' => 'Name', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'DescribeEntitlementsResult' => [ 'type' => 'structure', 'members' => [ 'Entitlements' => [ 'shape' => 'EntitlementList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetsRequest' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeFleetsResult' => [ 'type' => 'structure', 'members' => [ 'Fleets' => [ 'shape' => 'FleetList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeImageBuildersRequest' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeImageBuildersResult' => [ 'type' => 'structure', 'members' => [ 'ImageBuilders' => [ 'shape' => 'ImageBuilderList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeImagePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SharedAwsAccountIds' => [ 'shape' => 'AwsAccountIdList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeImagePermissionsResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'SharedImagePermissionsList' => [ 'shape' => 'SharedImagePermissionsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeImagesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 0, ], 'DescribeImagesRequest' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'Arns' => [ 'shape' => 'ArnList', ], 'Type' => [ 'shape' => 'VisibilityType', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'DescribeImagesMaxResults', ], ], ], 'DescribeImagesResult' => [ 'type' => 'structure', 'members' => [ 'Images' => [ 'shape' => 'ImageList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', 'FleetName', ], 'members' => [ 'StackName' => [ 'shape' => 'Name', ], 'FleetName' => [ 'shape' => 'Name', ], 'UserId' => [ 'shape' => 'UserId', ], 'NextToken' => [ 'shape' => 'String', ], 'Limit' => [ 'shape' => 'Integer', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'DescribeSessionsResult' => [ 'type' => 'structure', 'members' => [ 'Sessions' => [ 'shape' => 'SessionList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeStacksRequest' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeStacksResult' => [ 'type' => 'structure', 'members' => [ 'Stacks' => [ 'shape' => 'StackList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUsageReportSubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUsageReportSubscriptionsResult' => [ 'type' => 'structure', 'members' => [ 'UsageReportSubscriptions' => [ 'shape' => 'UsageReportSubscriptionList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUserStackAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'String', ], 'UserName' => [ 'shape' => 'Username', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUserStackAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'UserStackAssociations' => [ 'shape' => 'UserStackAssociationList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AuthenticationType', ], 'members' => [ 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUsersResult' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 256, ], 'DirectoryConfig' => [ 'type' => 'structure', 'required' => [ 'DirectoryName', ], 'members' => [ 'DirectoryName' => [ 'shape' => 'DirectoryName', ], 'OrganizationalUnitDistinguishedNames' => [ 'shape' => 'OrganizationalUnitDistinguishedNamesList', ], 'ServiceAccountCredentials' => [ 'shape' => 'ServiceAccountCredentials', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'CertificateBasedAuthProperties' => [ 'shape' => 'CertificateBasedAuthProperties', ], ], ], 'DirectoryConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryConfig', ], ], 'DirectoryName' => [ 'type' => 'string', ], 'DirectoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryName', ], ], 'DisableUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AuthenticationType', ], 'members' => [ 'UserName' => [ 'shape' => 'Username', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], ], ], 'DisableUserResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateAppBlockBuilderAppBlockRequest' => [ 'type' => 'structure', 'required' => [ 'AppBlockArn', 'AppBlockBuilderName', ], 'members' => [ 'AppBlockArn' => [ 'shape' => 'Arn', ], 'AppBlockBuilderName' => [ 'shape' => 'Name', ], ], ], 'DisassociateAppBlockBuilderAppBlockResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateApplicationFleetRequest' => [ 'type' => 'structure', 'required' => [ 'FleetName', 'ApplicationArn', ], 'members' => [ 'FleetName' => [ 'shape' => 'Name', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateApplicationFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateApplicationFromEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', 'EntitlementName', 'ApplicationIdentifier', ], 'members' => [ 'StackName' => [ 'shape' => 'Name', ], 'EntitlementName' => [ 'shape' => 'Name', ], 'ApplicationIdentifier' => [ 'shape' => 'String', ], ], ], 'DisassociateApplicationFromEntitlementResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'FleetName', 'StackName', ], 'members' => [ 'FleetName' => [ 'shape' => 'String', ], 'StackName' => [ 'shape' => 'String', ], ], ], 'DisassociateFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'DisplayName' => [ 'type' => 'string', 'max' => 100, ], 'Domain' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DomainJoinInfo' => [ 'type' => 'structure', 'members' => [ 'DirectoryName' => [ 'shape' => 'DirectoryName', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDistinguishedName', ], ], ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Domain', ], 'max' => 50, ], 'EmbedHostDomain' => [ 'type' => 'string', 'max' => 128, 'pattern' => '(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]', ], 'EmbedHostDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmbedHostDomain', ], 'max' => 20, 'min' => 1, ], 'EnableUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AuthenticationType', ], 'members' => [ 'UserName' => [ 'shape' => 'Username', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], ], ], 'EnableUserResult' => [ 'type' => 'structure', 'members' => [], ], 'EntitledApplication' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', ], 'members' => [ 'ApplicationIdentifier' => [ 'shape' => 'String', ], ], ], 'EntitledApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitledApplication', ], ], 'Entitlement' => [ 'type' => 'structure', 'required' => [ 'Name', 'StackName', 'AppVisibility', 'Attributes', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'StackName' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'AppVisibility' => [ 'shape' => 'AppVisibility', ], 'Attributes' => [ 'shape' => 'EntitlementAttributeList', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'EntitlementAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EntitlementAttribute' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'EntitlementAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitlementAttribute', ], 'min' => 1, ], 'EntitlementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entitlement', ], ], 'EntitlementNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'ErrorDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetails', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExpireSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', ], 'members' => [ 'SessionId' => [ 'shape' => 'String', ], ], ], 'ExpireSessionResult' => [ 'type' => 'structure', 'members' => [], ], 'FeedbackURL' => [ 'type' => 'string', 'max' => 1000, ], 'Fleet' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Name', 'InstanceType', 'ComputeCapacityStatus', 'State', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ImageName' => [ 'shape' => 'String', ], 'ImageArn' => [ 'shape' => 'Arn', ], 'InstanceType' => [ 'shape' => 'String', ], 'FleetType' => [ 'shape' => 'FleetType', ], 'ComputeCapacityStatus' => [ 'shape' => 'ComputeCapacityStatus', ], 'MaxUserDurationInSeconds' => [ 'shape' => 'Integer', ], 'DisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'State' => [ 'shape' => 'FleetState', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'FleetErrors' => [ 'shape' => 'FleetErrors', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DomainJoinInfo' => [ 'shape' => 'DomainJoinInfo', ], 'IdleDisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'StreamView' => [ 'shape' => 'StreamView', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'MaxConcurrentSessions' => [ 'shape' => 'Integer', ], 'UsbDeviceFilterStrings' => [ 'shape' => 'UsbDeviceFilterStrings', ], 'SessionScriptS3Location' => [ 'shape' => 'S3Location', ], 'MaxSessionsPerInstance' => [ 'shape' => 'Integer', ], ], ], 'FleetAttribute' => [ 'type' => 'string', 'enum' => [ 'VPC_CONFIGURATION', 'VPC_CONFIGURATION_SECURITY_GROUP_IDS', 'DOMAIN_JOIN_INFO', 'IAM_ROLE_ARN', 'USB_DEVICE_FILTER_STRINGS', 'SESSION_SCRIPT_S3_LOCATION', 'MAX_SESSIONS_PER_INSTANCE', ], ], 'FleetAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttribute', ], ], 'FleetError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'FleetErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'FleetErrorCode' => [ 'type' => 'string', 'enum' => [ 'IAM_SERVICE_ROLE_MISSING_ENI_DESCRIBE_ACTION', 'IAM_SERVICE_ROLE_MISSING_ENI_CREATE_ACTION', 'IAM_SERVICE_ROLE_MISSING_ENI_DELETE_ACTION', 'NETWORK_INTERFACE_LIMIT_EXCEEDED', 'INTERNAL_SERVICE_ERROR', 'IAM_SERVICE_ROLE_IS_MISSING', 'MACHINE_ROLE_IS_MISSING', 'STS_DISABLED_IN_REGION', 'SUBNET_HAS_INSUFFICIENT_IP_ADDRESSES', 'IAM_SERVICE_ROLE_MISSING_DESCRIBE_SUBNET_ACTION', 'SUBNET_NOT_FOUND', 'IMAGE_NOT_FOUND', 'INVALID_SUBNET_CONFIGURATION', 'SECURITY_GROUPS_NOT_FOUND', 'IGW_NOT_ATTACHED', 'IAM_SERVICE_ROLE_MISSING_DESCRIBE_SECURITY_GROUPS_ACTION', 'FLEET_STOPPED', 'FLEET_INSTANCE_PROVISIONING_FAILURE', 'DOMAIN_JOIN_ERROR_FILE_NOT_FOUND', 'DOMAIN_JOIN_ERROR_ACCESS_DENIED', 'DOMAIN_JOIN_ERROR_LOGON_FAILURE', 'DOMAIN_JOIN_ERROR_INVALID_PARAMETER', 'DOMAIN_JOIN_ERROR_MORE_DATA', 'DOMAIN_JOIN_ERROR_NO_SUCH_DOMAIN', 'DOMAIN_JOIN_ERROR_NOT_SUPPORTED', 'DOMAIN_JOIN_NERR_INVALID_WORKGROUP_NAME', 'DOMAIN_JOIN_NERR_WORKSTATION_NOT_STARTED', 'DOMAIN_JOIN_ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED', 'DOMAIN_JOIN_NERR_PASSWORD_EXPIRED', 'DOMAIN_JOIN_INTERNAL_SERVICE_ERROR', ], ], 'FleetErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetError', ], ], 'FleetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Fleet', ], ], 'FleetState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', ], ], 'FleetType' => [ 'type' => 'string', 'enum' => [ 'ALWAYS_ON', 'ON_DEMAND', 'ELASTIC', ], ], 'Image' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'Arn', ], 'BaseImageArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'ImageState', ], 'Visibility' => [ 'shape' => 'VisibilityType', ], 'ImageBuilderSupported' => [ 'shape' => 'Boolean', ], 'ImageBuilderName' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'Description' => [ 'shape' => 'String', ], 'StateChangeReason' => [ 'shape' => 'ImageStateChangeReason', ], 'Applications' => [ 'shape' => 'Applications', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'PublicBaseImageReleasedDate' => [ 'shape' => 'Timestamp', ], 'AppstreamAgentVersion' => [ 'shape' => 'AppstreamAgentVersion', ], 'ImagePermissions' => [ 'shape' => 'ImagePermissions', ], 'ImageErrors' => [ 'shape' => 'ResourceErrors', ], ], ], 'ImageBuilder' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'Arn', ], 'ImageArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'InstanceType' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'State' => [ 'shape' => 'ImageBuilderState', ], 'StateChangeReason' => [ 'shape' => 'ImageBuilderStateChangeReason', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DomainJoinInfo' => [ 'shape' => 'DomainJoinInfo', ], 'NetworkAccessConfiguration' => [ 'shape' => 'NetworkAccessConfiguration', ], 'ImageBuilderErrors' => [ 'shape' => 'ResourceErrors', ], 'AppstreamAgentVersion' => [ 'shape' => 'AppstreamAgentVersion', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], ], ], 'ImageBuilderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageBuilder', ], ], 'ImageBuilderState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'UPDATING_AGENT', 'RUNNING', 'STOPPING', 'STOPPED', 'REBOOTING', 'SNAPSHOTTING', 'DELETING', 'FAILED', 'UPDATING', 'PENDING_QUALIFICATION', ], ], 'ImageBuilderStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ImageBuilderStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ImageBuilderStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'IMAGE_UNAVAILABLE', ], ], 'ImageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Image', ], ], 'ImagePermissions' => [ 'type' => 'structure', 'members' => [ 'allowFleet' => [ 'shape' => 'BooleanObject', ], 'allowImageBuilder' => [ 'shape' => 'BooleanObject', ], ], ], 'ImageState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'FAILED', 'COPYING', 'DELETING', 'CREATING', 'IMPORTING', ], ], 'ImageStateChangeReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ImageStateChangeReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ImageStateChangeReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'IMAGE_BUILDER_NOT_AVAILABLE', 'IMAGE_COPY_FAILURE', ], ], 'IncompatibleImageException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'InvalidAccountStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidRoleException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'LastReportGenerationExecutionError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'UsageReportExecutionErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'LastReportGenerationExecutionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'LastReportGenerationExecutionError', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListAssociatedFleetsRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListAssociatedFleetsResult' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListAssociatedStacksRequest' => [ 'type' => 'structure', 'required' => [ 'FleetName', ], 'members' => [ 'FleetName' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListAssociatedStacksResult' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'StringList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEntitledApplicationsRequest' => [ 'type' => 'structure', 'required' => [ 'StackName', 'EntitlementName', ], 'members' => [ 'StackName' => [ 'shape' => 'Name', ], 'EntitlementName' => [ 'shape' => 'Name', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'ListEntitledApplicationsResult' => [ 'type' => 'structure', 'members' => [ 'EntitledApplications' => [ 'shape' => 'EntitledApplicationList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Long' => [ 'type' => 'long', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'MessageAction' => [ 'type' => 'string', 'enum' => [ 'SUPPRESS', 'RESEND', ], ], 'Metadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Name' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_.-]{0,100}$', ], 'NetworkAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'EniPrivateIpAddress' => [ 'shape' => 'String', ], 'EniId' => [ 'shape' => 'String', ], ], ], 'OperationNotPermittedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'OrganizationalUnitDistinguishedName' => [ 'type' => 'string', 'max' => 2000, ], 'OrganizationalUnitDistinguishedNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitDistinguishedName', ], ], 'PackagingType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM', 'APPSTREAM2', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PlatformType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'WINDOWS_SERVER_2016', 'WINDOWS_SERVER_2019', 'WINDOWS_SERVER_2022', 'AMAZON_LINUX2', ], ], 'Platforms' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatformType', ], 'max' => 4, ], 'PreferredProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'RedirectURL' => [ 'type' => 'string', 'max' => 1000, ], 'RegionName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'RequestLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'FleetErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'ErrorTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ResourceErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceError', ], ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotAvailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[0-9a-z\\.\\-]*(?<!\\.)$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'S3Bucket', ], 'members' => [ 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Key' => [ 'shape' => 'S3Key', ], ], ], 'ScriptDetails' => [ 'type' => 'structure', 'required' => [ 'ScriptS3Location', 'ExecutablePath', 'TimeoutInSeconds', ], 'members' => [ 'ScriptS3Location' => [ 'shape' => 'S3Location', ], 'ExecutablePath' => [ 'shape' => 'String', ], 'ExecutableParameters' => [ 'shape' => 'String', ], 'TimeoutInSeconds' => [ 'shape' => 'Integer', ], ], ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 5, ], 'ServiceAccountCredentials' => [ 'type' => 'structure', 'required' => [ 'AccountName', 'AccountPassword', ], 'members' => [ 'AccountName' => [ 'shape' => 'AccountName', ], 'AccountPassword' => [ 'shape' => 'AccountPassword', ], ], ], 'Session' => [ 'type' => 'structure', 'required' => [ 'Id', 'UserId', 'StackName', 'FleetName', 'State', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'UserId' => [ 'shape' => 'UserId', ], 'StackName' => [ 'shape' => 'String', ], 'FleetName' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'SessionState', ], 'ConnectionState' => [ 'shape' => 'SessionConnectionState', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'MaxExpirationTime' => [ 'shape' => 'Timestamp', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'NetworkAccessConfiguration' => [ 'shape' => 'NetworkAccessConfiguration', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'SessionConnectionState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'NOT_CONNECTED', ], ], 'SessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Session', ], ], 'SessionState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PENDING', 'EXPIRED', ], ], 'SettingsGroup' => [ 'type' => 'string', 'max' => 100, ], 'SharedImagePermissions' => [ 'type' => 'structure', 'required' => [ 'sharedAccountId', 'imagePermissions', ], 'members' => [ 'sharedAccountId' => [ 'shape' => 'AwsAccountId', ], 'imagePermissions' => [ 'shape' => 'ImagePermissions', ], ], ], 'SharedImagePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedImagePermissions', ], ], 'Stack' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'StorageConnectors' => [ 'shape' => 'StorageConnectorList', ], 'RedirectURL' => [ 'shape' => 'RedirectURL', ], 'FeedbackURL' => [ 'shape' => 'FeedbackURL', ], 'StackErrors' => [ 'shape' => 'StackErrors', ], 'UserSettings' => [ 'shape' => 'UserSettingList', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettingsResponse', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], 'EmbedHostDomains' => [ 'shape' => 'EmbedHostDomains', ], 'StreamingExperienceSettings' => [ 'shape' => 'StreamingExperienceSettings', ], ], ], 'StackAttribute' => [ 'type' => 'string', 'enum' => [ 'STORAGE_CONNECTORS', 'STORAGE_CONNECTOR_HOMEFOLDERS', 'STORAGE_CONNECTOR_GOOGLE_DRIVE', 'STORAGE_CONNECTOR_ONE_DRIVE', 'REDIRECT_URL', 'FEEDBACK_URL', 'THEME_NAME', 'USER_SETTINGS', 'EMBED_HOST_DOMAINS', 'IAM_ROLE_ARN', 'ACCESS_ENDPOINTS', 'STREAMING_EXPERIENCE_SETTINGS', ], ], 'StackAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackAttribute', ], ], 'StackError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'StackErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'StackErrorCode' => [ 'type' => 'string', 'enum' => [ 'STORAGE_CONNECTOR_ERROR', 'INTERNAL_SERVICE_ERROR', ], ], 'StackErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackError', ], ], 'StackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stack', ], ], 'StartAppBlockBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'StartAppBlockBuilderResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilder' => [ 'shape' => 'AppBlockBuilder', ], ], ], 'StartFleetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'StartFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'StartImageBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'AppstreamAgentVersion' => [ 'shape' => 'AppstreamAgentVersion', ], ], ], 'StartImageBuilderResult' => [ 'type' => 'structure', 'members' => [ 'ImageBuilder' => [ 'shape' => 'ImageBuilder', ], ], ], 'StopAppBlockBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], ], ], 'StopAppBlockBuilderResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilder' => [ 'shape' => 'AppBlockBuilder', ], ], ], 'StopFleetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'StopFleetResult' => [ 'type' => 'structure', 'members' => [], ], 'StopImageBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'StopImageBuilderResult' => [ 'type' => 'structure', 'members' => [ 'ImageBuilder' => [ 'shape' => 'ImageBuilder', ], ], ], 'StorageConnector' => [ 'type' => 'structure', 'required' => [ 'ConnectorType', ], 'members' => [ 'ConnectorType' => [ 'shape' => 'StorageConnectorType', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'Domains' => [ 'shape' => 'DomainList', ], ], ], 'StorageConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageConnector', ], ], 'StorageConnectorType' => [ 'type' => 'string', 'enum' => [ 'HOMEFOLDERS', 'GOOGLE_DRIVE', 'ONE_DRIVE', ], ], 'StreamView' => [ 'type' => 'string', 'enum' => [ 'APP', 'DESKTOP', ], ], 'StreamingExperienceSettings' => [ 'type' => 'structure', 'members' => [ 'PreferredProtocol' => [ 'shape' => 'PreferredProtocol', ], ], ], 'StreamingUrlUserId' => [ 'type' => 'string', 'max' => 32, 'min' => 2, 'pattern' => '[\\w+=,.@-]*', ], 'String' => [ 'type' => 'string', 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(^(?!aws:).[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppBlockBuilderRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'InstanceType' => [ 'shape' => 'String', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], 'AttributesToDelete' => [ 'shape' => 'AppBlockBuilderAttributes', ], ], ], 'UpdateAppBlockBuilderResult' => [ 'type' => 'structure', 'members' => [ 'AppBlockBuilder' => [ 'shape' => 'AppBlockBuilder', ], ], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'IconS3Location' => [ 'shape' => 'S3Location', ], 'LaunchPath' => [ 'shape' => 'String', ], 'WorkingDirectory' => [ 'shape' => 'String', ], 'LaunchParameters' => [ 'shape' => 'String', ], 'AppBlockArn' => [ 'shape' => 'Arn', ], 'AttributesToDelete' => [ 'shape' => 'ApplicationAttributes', ], ], ], 'UpdateApplicationResult' => [ 'type' => 'structure', 'members' => [ 'Application' => [ 'shape' => 'Application', ], ], ], 'UpdateDirectoryConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryName', ], 'members' => [ 'DirectoryName' => [ 'shape' => 'DirectoryName', ], 'OrganizationalUnitDistinguishedNames' => [ 'shape' => 'OrganizationalUnitDistinguishedNamesList', ], 'ServiceAccountCredentials' => [ 'shape' => 'ServiceAccountCredentials', ], 'CertificateBasedAuthProperties' => [ 'shape' => 'CertificateBasedAuthProperties', ], ], ], 'UpdateDirectoryConfigResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryConfig' => [ 'shape' => 'DirectoryConfig', ], ], ], 'UpdateEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'StackName', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'StackName' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'AppVisibility' => [ 'shape' => 'AppVisibility', ], 'Attributes' => [ 'shape' => 'EntitlementAttributeList', ], ], ], 'UpdateEntitlementResult' => [ 'type' => 'structure', 'members' => [ 'Entitlement' => [ 'shape' => 'Entitlement', ], ], ], 'UpdateFleetRequest' => [ 'type' => 'structure', 'members' => [ 'ImageName' => [ 'shape' => 'String', ], 'ImageArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'InstanceType' => [ 'shape' => 'String', ], 'ComputeCapacity' => [ 'shape' => 'ComputeCapacity', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'MaxUserDurationInSeconds' => [ 'shape' => 'Integer', ], 'DisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'DeleteVpcConfig' => [ 'shape' => 'Boolean', 'deprecated' => true, ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'EnableDefaultInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DomainJoinInfo' => [ 'shape' => 'DomainJoinInfo', ], 'IdleDisconnectTimeoutInSeconds' => [ 'shape' => 'Integer', ], 'AttributesToDelete' => [ 'shape' => 'FleetAttributes', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'StreamView' => [ 'shape' => 'StreamView', ], 'Platform' => [ 'shape' => 'PlatformType', ], 'MaxConcurrentSessions' => [ 'shape' => 'Integer', ], 'UsbDeviceFilterStrings' => [ 'shape' => 'UsbDeviceFilterStrings', ], 'SessionScriptS3Location' => [ 'shape' => 'S3Location', ], 'MaxSessionsPerInstance' => [ 'shape' => 'Integer', ], ], ], 'UpdateFleetResult' => [ 'type' => 'structure', 'members' => [ 'Fleet' => [ 'shape' => 'Fleet', ], ], ], 'UpdateImagePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SharedAccountId', 'ImagePermissions', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'SharedAccountId' => [ 'shape' => 'AwsAccountId', ], 'ImagePermissions' => [ 'shape' => 'ImagePermissions', ], ], ], 'UpdateImagePermissionsResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStackRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'String', ], 'StorageConnectors' => [ 'shape' => 'StorageConnectorList', ], 'DeleteStorageConnectors' => [ 'shape' => 'Boolean', 'deprecated' => true, ], 'RedirectURL' => [ 'shape' => 'RedirectURL', ], 'FeedbackURL' => [ 'shape' => 'FeedbackURL', ], 'AttributesToDelete' => [ 'shape' => 'StackAttributes', ], 'UserSettings' => [ 'shape' => 'UserSettingList', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettings', ], 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], 'EmbedHostDomains' => [ 'shape' => 'EmbedHostDomains', ], 'StreamingExperienceSettings' => [ 'shape' => 'StreamingExperienceSettings', ], ], ], 'UpdateStackResult' => [ 'type' => 'structure', 'members' => [ 'Stack' => [ 'shape' => 'Stack', ], ], ], 'UsageReportExecutionErrorCode' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_NOT_FOUND', 'ACCESS_DENIED', 'INTERNAL_SERVICE_ERROR', ], ], 'UsageReportSchedule' => [ 'type' => 'string', 'enum' => [ 'DAILY', ], ], 'UsageReportSubscription' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 'String', ], 'Schedule' => [ 'shape' => 'UsageReportSchedule', ], 'LastGeneratedReportDate' => [ 'shape' => 'Timestamp', ], 'SubscriptionErrors' => [ 'shape' => 'LastReportGenerationExecutionErrors', ], ], ], 'UsageReportSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageReportSubscription', ], ], 'UsbDeviceFilterString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '^((\\w*)\\s*(\\w*)\\s*\\,\\s*(\\w*)\\s*\\,\\s*\\*?(\\w*)\\s*\\,\\s*\\*?(\\w*)\\s*\\,\\s*\\*?\\d*\\s*\\,\\s*\\*?\\d*\\s*\\,\\s*[0-1]\\s*\\,\\s*[0-1]\\s*)$', ], 'UsbDeviceFilterStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsbDeviceFilterString', ], ], 'User' => [ 'type' => 'structure', 'required' => [ 'AuthenticationType', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'UserName' => [ 'shape' => 'Username', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'String', ], 'FirstName' => [ 'shape' => 'UserAttributeValue', ], 'LastName' => [ 'shape' => 'UserAttributeValue', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], ], ], 'UserAttributeValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^[A-Za-z0-9_\\-\\s]+$', 'sensitive' => true, ], 'UserId' => [ 'type' => 'string', 'max' => 128, 'min' => 2, ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserSetting' => [ 'type' => 'structure', 'required' => [ 'Action', 'Permission', ], 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Permission' => [ 'shape' => 'Permission', ], 'MaximumLength' => [ 'shape' => 'Integer', ], ], ], 'UserSettingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSetting', ], 'min' => 1, ], 'UserStackAssociation' => [ 'type' => 'structure', 'required' => [ 'StackName', 'UserName', 'AuthenticationType', ], 'members' => [ 'StackName' => [ 'shape' => 'String', ], 'UserName' => [ 'shape' => 'Username', ], 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'SendEmailNotification' => [ 'shape' => 'Boolean', ], ], ], 'UserStackAssociationError' => [ 'type' => 'structure', 'members' => [ 'UserStackAssociation' => [ 'shape' => 'UserStackAssociation', ], 'ErrorCode' => [ 'shape' => 'UserStackAssociationErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'UserStackAssociationErrorCode' => [ 'type' => 'string', 'enum' => [ 'STACK_NOT_FOUND', 'USER_NAME_NOT_FOUND', 'DIRECTORY_NOT_FOUND', 'INTERNAL_ERROR', ], ], 'UserStackAssociationErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserStackAssociationError', ], ], 'UserStackAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserStackAssociation', ], 'max' => 25, 'min' => 1, ], 'Username' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', 'sensitive' => true, ], 'VisibilityType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', 'SHARED', ], ], 'VpcConfig' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], ], ], ],];
