<?php
// This file was auto-generated from sdk-root/src/data/organizations/2016-11-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-11-28', 'endpointPrefix' => 'organizations', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Organizations', 'serviceFullName' => 'AWS Organizations', 'serviceId' => 'Organizations', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSOrganizationsV20161128', 'uid' => 'organizations-2016-11-28', ], 'operations' => [ 'AcceptHandshake' => [ 'name' => 'AcceptHandshake', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptHandshakeRequest', ], 'output' => [ 'shape' => 'AcceptHandshakeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'HandshakeConstraintViolationException', ], [ 'shape' => 'HandshakeNotFoundException', ], [ 'shape' => 'InvalidHandshakeTransitionException', ], [ 'shape' => 'HandshakeAlreadyInStateException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AccessDeniedForDependencyException', ], ], ], 'AttachPolicy' => [ 'name' => 'AttachPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachPolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'DuplicatePolicyAttachmentException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'PolicyTypeNotEnabledException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'PolicyChangesInProgressException', ], ], ], 'CancelHandshake' => [ 'name' => 'CancelHandshake', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelHandshakeRequest', ], 'output' => [ 'shape' => 'CancelHandshakeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'HandshakeNotFoundException', ], [ 'shape' => 'InvalidHandshakeTransitionException', ], [ 'shape' => 'HandshakeAlreadyInStateException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CloseAccount' => [ 'name' => 'CloseAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CloseAccountRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountAlreadyClosedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'CreateAccount' => [ 'name' => 'CreateAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccountRequest', ], 'output' => [ 'shape' => 'CreateAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'FinalizingOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'CreateGovCloudAccount' => [ 'name' => 'CreateGovCloudAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGovCloudAccountRequest', ], 'output' => [ 'shape' => 'CreateGovCloudAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'FinalizingOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'CreateOrganization' => [ 'name' => 'CreateOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOrganizationRequest', ], 'output' => [ 'shape' => 'CreateOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyInOrganizationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AccessDeniedForDependencyException', ], ], ], 'CreateOrganizationalUnit' => [ 'name' => 'CreateOrganizationalUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOrganizationalUnitRequest', ], 'output' => [ 'shape' => 'CreateOrganizationalUnitResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'DuplicateOrganizationalUnitException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ParentNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreatePolicy' => [ 'name' => 'CreatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePolicyRequest', ], 'output' => [ 'shape' => 'CreatePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'DuplicatePolicyException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'MalformedPolicyDocumentException', ], [ 'shape' => 'PolicyTypeNotAvailableForOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DeclineHandshake' => [ 'name' => 'DeclineHandshake', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeclineHandshakeRequest', ], 'output' => [ 'shape' => 'DeclineHandshakeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'HandshakeNotFoundException', ], [ 'shape' => 'InvalidHandshakeTransitionException', ], [ 'shape' => 'HandshakeAlreadyInStateException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteOrganization' => [ 'name' => 'DeleteOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OrganizationNotEmptyException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteOrganizationalUnit' => [ 'name' => 'DeleteOrganizationalUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOrganizationalUnitRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OrganizationalUnitNotEmptyException', ], [ 'shape' => 'OrganizationalUnitNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyInUseException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], ], ], 'DeregisterDelegatedAdministrator' => [ 'name' => 'DeregisterDelegatedAdministrator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterDelegatedAdministratorRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AccountNotRegisteredException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DescribeAccount' => [ 'name' => 'DescribeAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountRequest', ], 'output' => [ 'shape' => 'DescribeAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeCreateAccountStatus' => [ 'name' => 'DescribeCreateAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCreateAccountStatusRequest', ], 'output' => [ 'shape' => 'DescribeCreateAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'CreateAccountStatusNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DescribeEffectivePolicy' => [ 'name' => 'DescribeEffectivePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEffectivePolicyRequest', ], 'output' => [ 'shape' => 'DescribeEffectivePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'EffectivePolicyNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DescribeHandshake' => [ 'name' => 'DescribeHandshake', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeHandshakeRequest', ], 'output' => [ 'shape' => 'DescribeHandshakeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'HandshakeNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeOrganization' => [ 'name' => 'DescribeOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeOrganizationalUnit' => [ 'name' => 'DescribeOrganizationalUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationalUnitRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationalUnitResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OrganizationalUnitNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribePolicy' => [ 'name' => 'DescribePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePolicyRequest', ], 'output' => [ 'shape' => 'DescribePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], [ 'shape' => 'ConstraintViolationException', ], ], ], 'DetachPolicy' => [ 'name' => 'DetachPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachPolicyRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyNotAttachedException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'PolicyChangesInProgressException', ], ], ], 'DisableAWSServiceAccess' => [ 'name' => 'DisableAWSServiceAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableAWSServiceAccessRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'DisablePolicyType' => [ 'name' => 'DisablePolicyType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisablePolicyTypeRequest', ], 'output' => [ 'shape' => 'DisablePolicyTypeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyTypeNotEnabledException', ], [ 'shape' => 'RootNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'PolicyChangesInProgressException', ], ], ], 'EnableAWSServiceAccess' => [ 'name' => 'EnableAWSServiceAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableAWSServiceAccessRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'EnableAllFeatures' => [ 'name' => 'EnableAllFeatures', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableAllFeaturesRequest', ], 'output' => [ 'shape' => 'EnableAllFeaturesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'HandshakeConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'EnablePolicyType' => [ 'name' => 'EnablePolicyType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnablePolicyTypeRequest', ], 'output' => [ 'shape' => 'EnablePolicyTypeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyTypeAlreadyEnabledException', ], [ 'shape' => 'RootNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PolicyTypeNotAvailableForOrganizationException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'PolicyChangesInProgressException', ], ], ], 'InviteAccountToOrganization' => [ 'name' => 'InviteAccountToOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InviteAccountToOrganizationRequest', ], 'output' => [ 'shape' => 'InviteAccountToOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'AccountOwnerNotVerifiedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'HandshakeConstraintViolationException', ], [ 'shape' => 'DuplicateHandshakeException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'FinalizingOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'LeaveOrganization' => [ 'name' => 'LeaveOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'MasterCannotLeaveOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListAWSServiceAccessForOrganization' => [ 'name' => 'ListAWSServiceAccessForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAWSServiceAccessForOrganizationRequest', ], 'output' => [ 'shape' => 'ListAWSServiceAccessForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListAccounts' => [ 'name' => 'ListAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountsRequest', ], 'output' => [ 'shape' => 'ListAccountsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListAccountsForParent' => [ 'name' => 'ListAccountsForParent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountsForParentRequest', ], 'output' => [ 'shape' => 'ListAccountsForParentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ParentNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListChildren' => [ 'name' => 'ListChildren', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListChildrenRequest', ], 'output' => [ 'shape' => 'ListChildrenResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ParentNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListCreateAccountStatus' => [ 'name' => 'ListCreateAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCreateAccountStatusRequest', ], 'output' => [ 'shape' => 'ListCreateAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListDelegatedAdministrators' => [ 'name' => 'ListDelegatedAdministrators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDelegatedAdministratorsRequest', ], 'output' => [ 'shape' => 'ListDelegatedAdministratorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListDelegatedServicesForAccount' => [ 'name' => 'ListDelegatedServicesForAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDelegatedServicesForAccountRequest', ], 'output' => [ 'shape' => 'ListDelegatedServicesForAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AccountNotRegisteredException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListHandshakesForAccount' => [ 'name' => 'ListHandshakesForAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHandshakesForAccountRequest', ], 'output' => [ 'shape' => 'ListHandshakesForAccountResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListHandshakesForOrganization' => [ 'name' => 'ListHandshakesForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHandshakesForOrganizationRequest', ], 'output' => [ 'shape' => 'ListHandshakesForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListOrganizationalUnitsForParent' => [ 'name' => 'ListOrganizationalUnitsForParent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOrganizationalUnitsForParentRequest', ], 'output' => [ 'shape' => 'ListOrganizationalUnitsForParentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ParentNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListParents' => [ 'name' => 'ListParents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListParentsRequest', ], 'output' => [ 'shape' => 'ListParentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ChildNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPoliciesRequest', ], 'output' => [ 'shape' => 'ListPoliciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListPoliciesForTarget' => [ 'name' => 'ListPoliciesForTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPoliciesForTargetRequest', ], 'output' => [ 'shape' => 'ListPoliciesForTargetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'ListRoots' => [ 'name' => 'ListRoots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRootsRequest', ], 'output' => [ 'shape' => 'ListRootsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTargetsForPolicy' => [ 'name' => 'ListTargetsForPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTargetsForPolicyRequest', ], 'output' => [ 'shape' => 'ListTargetsForPolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'MoveAccount' => [ 'name' => 'MoveAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MoveAccountRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'SourceParentNotFoundException', ], [ 'shape' => 'DestinationParentNotFoundException', ], [ 'shape' => 'DuplicateAccountException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ServiceException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], ], ], 'RegisterDelegatedAdministrator' => [ 'name' => 'RegisterDelegatedAdministrator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterDelegatedAdministratorRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountAlreadyRegisteredException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], ], ], 'RemoveAccountFromOrganization' => [ 'name' => 'RemoveAccountFromOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveAccountFromOrganizationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountNotFoundException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'MasterCannotLeaveOrganizationException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'TargetNotFoundException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateOrganizationalUnit' => [ 'name' => 'UpdateOrganizationalUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateOrganizationalUnitRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationalUnitResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'DuplicateOrganizationalUnitException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OrganizationalUnitNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdatePolicy' => [ 'name' => 'UpdatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePolicyRequest', ], 'output' => [ 'shape' => 'UpdatePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AWSOrganizationsNotInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'DuplicatePolicyException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'MalformedPolicyDocumentException', ], [ 'shape' => 'PolicyNotFoundException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedAPIEndpointException', ], [ 'shape' => 'PolicyChangesInProgressException', ], ], ], ], 'shapes' => [ 'AWSOrganizationsNotInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AcceptHandshakeRequest' => [ 'type' => 'structure', 'required' => [ 'HandshakeId', ], 'members' => [ 'HandshakeId' => [ 'shape' => 'HandshakeId', ], ], ], 'AcceptHandshakeResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccessDeniedForDependencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'Reason' => [ 'shape' => 'AccessDeniedForDependencyExceptionReason', ], ], 'exception' => true, ], 'AccessDeniedForDependencyExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED_DURING_CREATE_SERVICE_LINKED_ROLE', ], ], 'Account' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AccountId', ], 'Arn' => [ 'shape' => 'AccountArn', ], 'Email' => [ 'shape' => 'Email', ], 'Name' => [ 'shape' => 'AccountName', ], 'Status' => [ 'shape' => 'AccountStatus', ], 'JoinedMethod' => [ 'shape' => 'AccountJoinedMethod', ], 'JoinedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AccountAlreadyClosedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountAlreadyRegisteredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::\\d{12}:account\\/o-[a-z0-9]{10,32}\\/\\d{12}', ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'pattern' => '^\\d{12}$', ], 'AccountJoinedMethod' => [ 'type' => 'string', 'enum' => [ 'INVITED', 'CREATED', ], ], 'AccountName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'AccountNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountNotRegisteredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountOwnerNotVerifiedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'SUSPENDED', 'PENDING_CLOSURE', ], ], 'Accounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'ActionType' => [ 'type' => 'string', 'enum' => [ 'INVITE', 'ENABLE_ALL_FEATURES', 'APPROVE_ALL_FEATURES', 'ADD_ORGANIZATIONS_SERVICE_LINKED_ROLE', ], ], 'AlreadyInOrganizationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AttachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', 'TargetId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'TargetId' => [ 'shape' => 'PolicyTargetId', ], ], ], 'AwsManagedPolicy' => [ 'type' => 'boolean', ], 'CancelHandshakeRequest' => [ 'type' => 'structure', 'required' => [ 'HandshakeId', ], 'members' => [ 'HandshakeId' => [ 'shape' => 'HandshakeId', ], ], ], 'CancelHandshakeResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'Child' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ChildId', ], 'Type' => [ 'shape' => 'ChildType', ], ], ], 'ChildId' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$', ], 'ChildNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ChildType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATIONAL_UNIT', ], ], 'Children' => [ 'type' => 'list', 'member' => [ 'shape' => 'Child', ], ], 'CloseAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ConstraintViolationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'Reason' => [ 'shape' => 'ConstraintViolationExceptionReason', ], ], 'exception' => true, ], 'ConstraintViolationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_NUMBER_LIMIT_EXCEEDED', 'HANDSHAKE_RATE_LIMIT_EXCEEDED', 'OU_NUMBER_LIMIT_EXCEEDED', 'OU_DEPTH_LIMIT_EXCEEDED', 'POLICY_NUMBER_LIMIT_EXCEEDED', 'POLICY_CONTENT_LIMIT_EXCEEDED', 'MAX_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED', 'MIN_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED', 'ACCOUNT_CANNOT_LEAVE_ORGANIZATION', 'ACCOUNT_CANNOT_LEAVE_WITHOUT_EULA', 'ACCOUNT_CANNOT_LEAVE_WITHOUT_PHONE_VERIFICATION', 'MASTER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED', 'MEMBER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED', 'ACCOUNT_CREATION_RATE_LIMIT_EXCEEDED', 'MASTER_ACCOUNT_ADDRESS_DOES_NOT_MATCH_MARKETPLACE', 'MASTER_ACCOUNT_MISSING_CONTACT_INFO', 'MASTER_ACCOUNT_NOT_GOVCLOUD_ENABLED', 'ORGANIZATION_NOT_IN_ALL_FEATURES_MODE', 'CREATE_ORGANIZATION_IN_BILLING_MODE_UNSUPPORTED_REGION', 'EMAIL_VERIFICATION_CODE_EXPIRED', 'WAIT_PERIOD_ACTIVE', 'MAX_TAG_LIMIT_EXCEEDED', 'TAG_POLICY_VIOLATION', 'MAX_DELEGATED_ADMINISTRATORS_FOR_SERVICE_LIMIT_EXCEEDED', 'CANNOT_REGISTER_MASTER_AS_DELEGATED_ADMINISTRATOR', 'CANNOT_REMOVE_DELEGATED_ADMINISTRATOR_FROM_ORG', 'DELEGATED_ADMINISTRATOR_EXISTS_FOR_THIS_SERVICE', 'MASTER_ACCOUNT_MISSING_BUSINESS_LICENSE', 'CANNOT_CLOSE_MANAGEMENT_ACCOUNT', 'CLOSE_ACCOUNT_QUOTA_EXCEEDED', 'CLOSE_ACCOUNT_REQUESTS_LIMIT_EXCEEDED', 'SERVICE_ACCESS_NOT_ENABLED', 'INVALID_PAYMENT_INSTRUMENT', 'ACCOUNT_CREATION_NOT_COMPLETE', ], ], 'CreateAccountFailureReason' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_LIMIT_EXCEEDED', 'EMAIL_ALREADY_EXISTS', 'INVALID_ADDRESS', 'INVALID_EMAIL', 'CONCURRENT_ACCOUNT_MODIFICATION', 'INTERNAL_FAILURE', 'GOVCLOUD_ACCOUNT_ALREADY_EXISTS', 'MISSING_BUSINESS_VALIDATION', 'FAILED_BUSINESS_VALIDATION', 'PENDING_BUSINESS_VALIDATION', 'INVALID_IDENTITY_FOR_BUSINESS_VALIDATION', 'UNKNOWN_BUSINESS_VALIDATION', 'MISSING_PAYMENT_INSTRUMENT', 'INVALID_PAYMENT_INSTRUMENT', 'UPDATE_EXISTING_RESOURCE_POLICY_WITH_TAGS_NOT_SUPPORTED', ], ], 'CreateAccountName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[\\u0020-\\u007E]+', 'sensitive' => true, ], 'CreateAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Email', 'AccountName', ], 'members' => [ 'Email' => [ 'shape' => 'Email', ], 'AccountName' => [ 'shape' => 'CreateAccountName', ], 'RoleName' => [ 'shape' => 'RoleName', ], 'IamUserAccessToBilling' => [ 'shape' => 'IAMUserAccessToBilling', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAccountRequestId' => [ 'type' => 'string', 'max' => 36, 'pattern' => '^car-[a-z0-9]{8,32}$', ], 'CreateAccountResponse' => [ 'type' => 'structure', 'members' => [ 'CreateAccountStatus' => [ 'shape' => 'CreateAccountStatus', ], ], ], 'CreateAccountState' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'CreateAccountStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAccountState', ], ], 'CreateAccountStatus' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CreateAccountRequestId', ], 'AccountName' => [ 'shape' => 'CreateAccountName', ], 'State' => [ 'shape' => 'CreateAccountState', ], 'RequestedTimestamp' => [ 'shape' => 'Timestamp', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'GovCloudAccountId' => [ 'shape' => 'AccountId', ], 'FailureReason' => [ 'shape' => 'CreateAccountFailureReason', ], ], ], 'CreateAccountStatusNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'CreateAccountStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAccountStatus', ], ], 'CreateGovCloudAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Email', 'AccountName', ], 'members' => [ 'Email' => [ 'shape' => 'Email', ], 'AccountName' => [ 'shape' => 'CreateAccountName', ], 'RoleName' => [ 'shape' => 'RoleName', ], 'IamUserAccessToBilling' => [ 'shape' => 'IAMUserAccessToBilling', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateGovCloudAccountResponse' => [ 'type' => 'structure', 'members' => [ 'CreateAccountStatus' => [ 'shape' => 'CreateAccountStatus', ], ], ], 'CreateOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'FeatureSet' => [ 'shape' => 'OrganizationFeatureSet', ], ], ], 'CreateOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Organization' => [ 'shape' => 'Organization', ], ], ], 'CreateOrganizationalUnitRequest' => [ 'type' => 'structure', 'required' => [ 'ParentId', 'Name', ], 'members' => [ 'ParentId' => [ 'shape' => 'ParentId', ], 'Name' => [ 'shape' => 'OrganizationalUnitName', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateOrganizationalUnitResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnit' => [ 'shape' => 'OrganizationalUnit', ], ], ], 'CreatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Content', 'Description', 'Name', 'Type', ], 'members' => [ 'Content' => [ 'shape' => 'PolicyContent', ], 'Description' => [ 'shape' => 'PolicyDescription', ], 'Name' => [ 'shape' => 'PolicyName', ], 'Type' => [ 'shape' => 'PolicyType', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreatePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'DeclineHandshakeRequest' => [ 'type' => 'structure', 'required' => [ 'HandshakeId', ], 'members' => [ 'HandshakeId' => [ 'shape' => 'HandshakeId', ], ], ], 'DeclineHandshakeResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'DelegatedAdministrator' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AccountId', ], 'Arn' => [ 'shape' => 'AccountArn', ], 'Email' => [ 'shape' => 'Email', ], 'Name' => [ 'shape' => 'AccountName', ], 'Status' => [ 'shape' => 'AccountStatus', ], 'JoinedMethod' => [ 'shape' => 'AccountJoinedMethod', ], 'JoinedTimestamp' => [ 'shape' => 'Timestamp', ], 'DelegationEnabledDate' => [ 'shape' => 'Timestamp', ], ], ], 'DelegatedAdministrators' => [ 'type' => 'list', 'member' => [ 'shape' => 'DelegatedAdministrator', ], ], 'DelegatedService' => [ 'type' => 'structure', 'members' => [ 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], 'DelegationEnabledDate' => [ 'shape' => 'Timestamp', ], ], ], 'DelegatedServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'DelegatedService', ], ], 'DeleteOrganizationalUnitRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationalUnitId', ], 'members' => [ 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], ], ], 'DeletePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], ], ], 'DeregisterDelegatedAdministratorRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ServicePrincipal', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], ], ], 'DescribeAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'DescribeAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'DescribeCreateAccountStatusRequest' => [ 'type' => 'structure', 'required' => [ 'CreateAccountRequestId', ], 'members' => [ 'CreateAccountRequestId' => [ 'shape' => 'CreateAccountRequestId', ], ], ], 'DescribeCreateAccountStatusResponse' => [ 'type' => 'structure', 'members' => [ 'CreateAccountStatus' => [ 'shape' => 'CreateAccountStatus', ], ], ], 'DescribeEffectivePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyType', ], 'members' => [ 'PolicyType' => [ 'shape' => 'EffectivePolicyType', ], 'TargetId' => [ 'shape' => 'PolicyTargetId', ], ], ], 'DescribeEffectivePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'EffectivePolicy' => [ 'shape' => 'EffectivePolicy', ], ], ], 'DescribeHandshakeRequest' => [ 'type' => 'structure', 'required' => [ 'HandshakeId', ], 'members' => [ 'HandshakeId' => [ 'shape' => 'HandshakeId', ], ], ], 'DescribeHandshakeResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'DescribeOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Organization' => [ 'shape' => 'Organization', ], ], ], 'DescribeOrganizationalUnitRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationalUnitId', ], 'members' => [ 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], ], ], 'DescribeOrganizationalUnitResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnit' => [ 'shape' => 'OrganizationalUnit', ], ], ], 'DescribePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], ], ], 'DescribePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'DestinationParentNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'DetachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', 'TargetId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'TargetId' => [ 'shape' => 'PolicyTargetId', ], ], ], 'DisableAWSServiceAccessRequest' => [ 'type' => 'structure', 'required' => [ 'ServicePrincipal', ], 'members' => [ 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], ], ], 'DisablePolicyTypeRequest' => [ 'type' => 'structure', 'required' => [ 'RootId', 'PolicyType', ], 'members' => [ 'RootId' => [ 'shape' => 'RootId', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], ], ], 'DisablePolicyTypeResponse' => [ 'type' => 'structure', 'members' => [ 'Root' => [ 'shape' => 'Root', ], ], ], 'DuplicateAccountException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'DuplicateHandshakeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'DuplicateOrganizationalUnitException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'DuplicatePolicyAttachmentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'DuplicatePolicyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'EffectivePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyContent' => [ 'shape' => 'PolicyContent', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'TargetId' => [ 'shape' => 'PolicyTargetId', ], 'PolicyType' => [ 'shape' => 'EffectivePolicyType', ], ], ], 'EffectivePolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'EffectivePolicyType' => [ 'type' => 'string', 'enum' => [ 'TAG_POLICY', 'BACKUP_POLICY', 'AISERVICES_OPT_OUT_POLICY', ], ], 'Email' => [ 'type' => 'string', 'max' => 64, 'min' => 6, 'pattern' => '[^\\s@]+@[^\\s@]+\\.[^\\s@]+', 'sensitive' => true, ], 'EnableAWSServiceAccessRequest' => [ 'type' => 'structure', 'required' => [ 'ServicePrincipal', ], 'members' => [ 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], ], ], 'EnableAllFeaturesRequest' => [ 'type' => 'structure', 'members' => [], ], 'EnableAllFeaturesResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'EnablePolicyTypeRequest' => [ 'type' => 'structure', 'required' => [ 'RootId', 'PolicyType', ], 'members' => [ 'RootId' => [ 'shape' => 'RootId', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], ], ], 'EnablePolicyTypeResponse' => [ 'type' => 'structure', 'members' => [ 'Root' => [ 'shape' => 'Root', ], ], ], 'EnabledServicePrincipal' => [ 'type' => 'structure', 'members' => [ 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], 'DateEnabled' => [ 'shape' => 'Timestamp', ], ], ], 'EnabledServicePrincipals' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnabledServicePrincipal', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExceptionType' => [ 'type' => 'string', ], 'FinalizingOrganizationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'GenericArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::.+:.+', ], 'Handshake' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HandshakeId', ], 'Arn' => [ 'shape' => 'HandshakeArn', ], 'Parties' => [ 'shape' => 'HandshakeParties', ], 'State' => [ 'shape' => 'HandshakeState', ], 'RequestedTimestamp' => [ 'shape' => 'Timestamp', ], 'ExpirationTimestamp' => [ 'shape' => 'Timestamp', ], 'Action' => [ 'shape' => 'ActionType', ], 'Resources' => [ 'shape' => 'HandshakeResources', ], ], ], 'HandshakeAlreadyInStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'HandshakeArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::\\d{12}:handshake\\/o-[a-z0-9]{10,32}\\/[a-z_]{1,32}\\/h-[0-9a-z]{8,32}', ], 'HandshakeConstraintViolationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'Reason' => [ 'shape' => 'HandshakeConstraintViolationExceptionReason', ], ], 'exception' => true, ], 'HandshakeConstraintViolationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_NUMBER_LIMIT_EXCEEDED', 'HANDSHAKE_RATE_LIMIT_EXCEEDED', 'ALREADY_IN_AN_ORGANIZATION', 'ORGANIZATION_ALREADY_HAS_ALL_FEATURES', 'ORGANIZATION_IS_ALREADY_PENDING_ALL_FEATURES_MIGRATION', 'INVITE_DISABLED_DURING_ENABLE_ALL_FEATURES', 'PAYMENT_INSTRUMENT_REQUIRED', 'ORGANIZATION_FROM_DIFFERENT_SELLER_OF_RECORD', 'ORGANIZATION_MEMBERSHIP_CHANGE_RATE_LIMIT_EXCEEDED', 'MANAGEMENT_ACCOUNT_EMAIL_NOT_VERIFIED', ], ], 'HandshakeFilter' => [ 'type' => 'structure', 'members' => [ 'ActionType' => [ 'shape' => 'ActionType', ], 'ParentHandshakeId' => [ 'shape' => 'HandshakeId', ], ], ], 'HandshakeId' => [ 'type' => 'string', 'max' => 34, 'pattern' => '^h-[0-9a-z]{8,32}$', ], 'HandshakeNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'HandshakeNotes' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'HandshakeParties' => [ 'type' => 'list', 'member' => [ 'shape' => 'HandshakeParty', ], ], 'HandshakeParty' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', ], 'members' => [ 'Id' => [ 'shape' => 'HandshakePartyId', ], 'Type' => [ 'shape' => 'HandshakePartyType', ], ], ], 'HandshakePartyId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'HandshakePartyType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATION', 'EMAIL', ], ], 'HandshakeResource' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'HandshakeResourceValue', ], 'Type' => [ 'shape' => 'HandshakeResourceType', ], 'Resources' => [ 'shape' => 'HandshakeResources', ], ], ], 'HandshakeResourceType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATION', 'ORGANIZATION_FEATURE_SET', 'EMAIL', 'MASTER_EMAIL', 'MASTER_NAME', 'NOTES', 'PARENT_HANDSHAKE', ], ], 'HandshakeResourceValue' => [ 'type' => 'string', 'sensitive' => true, ], 'HandshakeResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'HandshakeResource', ], ], 'HandshakeState' => [ 'type' => 'string', 'enum' => [ 'REQUESTED', 'OPEN', 'CANCELED', 'ACCEPTED', 'DECLINED', 'EXPIRED', ], ], 'Handshakes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Handshake', ], ], 'IAMUserAccessToBilling' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'InvalidHandshakeTransitionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'Reason' => [ 'shape' => 'InvalidInputExceptionReason', ], ], 'exception' => true, ], 'InvalidInputExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_PARTY_TYPE_TARGET', 'INVALID_SYNTAX_ORGANIZATION_ARN', 'INVALID_SYNTAX_POLICY_ID', 'INVALID_ENUM', 'INVALID_ENUM_POLICY_TYPE', 'INVALID_LIST_MEMBER', 'MAX_LENGTH_EXCEEDED', 'MAX_VALUE_EXCEEDED', 'MIN_LENGTH_EXCEEDED', 'MIN_VALUE_EXCEEDED', 'IMMUTABLE_POLICY', 'INVALID_PATTERN', 'INVALID_PATTERN_TARGET_ID', 'INPUT_REQUIRED', 'INVALID_NEXT_TOKEN', 'MAX_LIMIT_EXCEEDED_FILTER', 'MOVING_ACCOUNT_BETWEEN_DIFFERENT_ROOTS', 'INVALID_FULL_NAME_TARGET', 'UNRECOGNIZED_SERVICE_PRINCIPAL', 'INVALID_ROLE_NAME', 'INVALID_SYSTEM_TAGS_PARAMETER', 'DUPLICATE_TAG_KEY', 'TARGET_NOT_SUPPORTED', 'INVALID_EMAIL_ADDRESS_TARGET', 'INVALID_RESOURCE_POLICY_JSON', 'UNSUPPORTED_ACTION_IN_RESOURCE_POLICY', 'UNSUPPORTED_POLICY_TYPE_IN_RESOURCE_POLICY', 'UNSUPPORTED_RESOURCE_IN_RESOURCE_POLICY', ], ], 'InviteAccountToOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'Target', ], 'members' => [ 'Target' => [ 'shape' => 'HandshakeParty', ], 'Notes' => [ 'shape' => 'HandshakeNotes', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'InviteAccountToOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Handshake' => [ 'shape' => 'Handshake', ], ], ], 'ListAWSServiceAccessForOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAWSServiceAccessForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'EnabledServicePrincipals' => [ 'shape' => 'EnabledServicePrincipals', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAccountsForParentRequest' => [ 'type' => 'structure', 'required' => [ 'ParentId', ], 'members' => [ 'ParentId' => [ 'shape' => 'ParentId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAccountsForParentResponse' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'Accounts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'Accounts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChildrenRequest' => [ 'type' => 'structure', 'required' => [ 'ParentId', 'ChildType', ], 'members' => [ 'ParentId' => [ 'shape' => 'ParentId', ], 'ChildType' => [ 'shape' => 'ChildType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListChildrenResponse' => [ 'type' => 'structure', 'members' => [ 'Children' => [ 'shape' => 'Children', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCreateAccountStatusRequest' => [ 'type' => 'structure', 'members' => [ 'States' => [ 'shape' => 'CreateAccountStates', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCreateAccountStatusResponse' => [ 'type' => 'structure', 'members' => [ 'CreateAccountStatuses' => [ 'shape' => 'CreateAccountStatuses', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedAdministratorsRequest' => [ 'type' => 'structure', 'members' => [ 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDelegatedAdministratorsResponse' => [ 'type' => 'structure', 'members' => [ 'DelegatedAdministrators' => [ 'shape' => 'DelegatedAdministrators', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDelegatedServicesForAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDelegatedServicesForAccountResponse' => [ 'type' => 'structure', 'members' => [ 'DelegatedServices' => [ 'shape' => 'DelegatedServices', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHandshakesForAccountRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'HandshakeFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListHandshakesForAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Handshakes' => [ 'shape' => 'Handshakes', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHandshakesForOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'HandshakeFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListHandshakesForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Handshakes' => [ 'shape' => 'Handshakes', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOrganizationalUnitsForParentRequest' => [ 'type' => 'structure', 'required' => [ 'ParentId', ], 'members' => [ 'ParentId' => [ 'shape' => 'ParentId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListOrganizationalUnitsForParentResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnits' => [ 'shape' => 'OrganizationalUnits', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListParentsRequest' => [ 'type' => 'structure', 'required' => [ 'ChildId', ], 'members' => [ 'ChildId' => [ 'shape' => 'ChildId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListParentsResponse' => [ 'type' => 'structure', 'members' => [ 'Parents' => [ 'shape' => 'Parents', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPoliciesForTargetRequest' => [ 'type' => 'structure', 'required' => [ 'TargetId', 'Filter', ], 'members' => [ 'TargetId' => [ 'shape' => 'PolicyTargetId', ], 'Filter' => [ 'shape' => 'PolicyType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPoliciesForTargetResponse' => [ 'type' => 'structure', 'members' => [ 'Policies' => [ 'shape' => 'Policies', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'Filter', ], 'members' => [ 'Filter' => [ 'shape' => 'PolicyType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'Policies' => [ 'shape' => 'Policies', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRootsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRootsResponse' => [ 'type' => 'structure', 'members' => [ 'Roots' => [ 'shape' => 'Roots', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'TaggableResourceId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTargetsForPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTargetsForPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Targets' => [ 'shape' => 'PolicyTargets', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MalformedPolicyDocumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'MasterCannotLeaveOrganizationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'MoveAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'SourceParentId', 'DestinationParentId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'SourceParentId' => [ 'shape' => 'ParentId', ], 'DestinationParentId' => [ 'shape' => 'ParentId', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 100000, 'pattern' => '[\\s\\S]*', ], 'Organization' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'OrganizationId', ], 'Arn' => [ 'shape' => 'OrganizationArn', ], 'FeatureSet' => [ 'shape' => 'OrganizationFeatureSet', ], 'MasterAccountArn' => [ 'shape' => 'AccountArn', ], 'MasterAccountId' => [ 'shape' => 'AccountId', ], 'MasterAccountEmail' => [ 'shape' => 'Email', ], 'AvailablePolicyTypes' => [ 'shape' => 'PolicyTypes', ], ], ], 'OrganizationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::\\d{12}:organization\\/o-[a-z0-9]{10,32}', ], 'OrganizationFeatureSet' => [ 'type' => 'string', 'enum' => [ 'ALL', 'CONSOLIDATED_BILLING', ], ], 'OrganizationId' => [ 'type' => 'string', 'pattern' => '^o-[a-z0-9]{10,32}$', ], 'OrganizationNotEmptyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'OrganizationalUnit' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'OrganizationalUnitId', ], 'Arn' => [ 'shape' => 'OrganizationalUnitArn', ], 'Name' => [ 'shape' => 'OrganizationalUnitName', ], ], ], 'OrganizationalUnitArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::\\d{12}:ou\\/o-[a-z0-9]{10,32}\\/ou-[0-9a-z]{4,32}-[0-9a-z]{8,32}', ], 'OrganizationalUnitId' => [ 'type' => 'string', 'max' => 68, 'pattern' => '^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$', ], 'OrganizationalUnitName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'OrganizationalUnitNotEmptyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'OrganizationalUnitNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'OrganizationalUnits' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnit', ], ], 'Parent' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ParentId', ], 'Type' => [ 'shape' => 'ParentType', ], ], ], 'ParentId' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^(r-[0-9a-z]{4,32})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$', ], 'ParentNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ParentType' => [ 'type' => 'string', 'enum' => [ 'ROOT', 'ORGANIZATIONAL_UNIT', ], ], 'Parents' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parent', ], ], 'Policies' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicySummary', ], ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'PolicySummary' => [ 'shape' => 'PolicySummary', ], 'Content' => [ 'shape' => 'PolicyContent', ], ], ], 'PolicyArn' => [ 'type' => 'string', 'pattern' => '^(arn:aws:organizations::\\d{12}:policy\\/o-[a-z0-9]{10,32}\\/[0-9a-z_]+\\/p-[0-9a-z]{10,32})|(arn:aws:organizations::aws:policy\\/[0-9a-z_]+\\/p-[0-9a-zA-Z_]{10,128})', ], 'PolicyChangesInProgressException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyContent' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'PolicyDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]*', ], 'PolicyId' => [ 'type' => 'string', 'max' => 130, 'pattern' => '^p-[0-9a-zA-Z_]{8,128}$', ], 'PolicyInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'PolicyNotAttachedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicySummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PolicyId', ], 'Arn' => [ 'shape' => 'PolicyArn', ], 'Name' => [ 'shape' => 'PolicyName', ], 'Description' => [ 'shape' => 'PolicyDescription', ], 'Type' => [ 'shape' => 'PolicyType', ], 'AwsManaged' => [ 'shape' => 'AwsManagedPolicy', ], ], ], 'PolicyTargetId' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^(r-[0-9a-z]{4,32})|(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$', ], 'PolicyTargetSummary' => [ 'type' => 'structure', 'members' => [ 'TargetId' => [ 'shape' => 'PolicyTargetId', ], 'Arn' => [ 'shape' => 'GenericArn', ], 'Name' => [ 'shape' => 'TargetName', ], 'Type' => [ 'shape' => 'TargetType', ], ], ], 'PolicyTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyTargetSummary', ], ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_CONTROL_POLICY', 'TAG_POLICY', 'BACKUP_POLICY', 'AISERVICES_OPT_OUT_POLICY', ], ], 'PolicyTypeAlreadyEnabledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyTypeNotAvailableForOrganizationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyTypeNotEnabledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PolicyTypeStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'PENDING_ENABLE', 'PENDING_DISABLE', ], ], 'PolicyTypeSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'PolicyType', ], 'Status' => [ 'shape' => 'PolicyTypeStatus', ], ], ], 'PolicyTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyTypeSummary', ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Content', ], 'members' => [ 'Content' => [ 'shape' => 'ResourcePolicyContent', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'RegisterDelegatedAdministratorRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ServicePrincipal', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], ], ], 'RemoveAccountFromOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'ResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicySummary' => [ 'shape' => 'ResourcePolicySummary', ], 'Content' => [ 'shape' => 'ResourcePolicyContent', ], ], ], 'ResourcePolicyArn' => [ 'type' => 'string', 'pattern' => '^arn:[a-z0-9][a-z0-9-.]{0,62}:organizations::\\d{12}:resourcepolicy\\/o-[a-z0-9]{10,32}\\/rp-[0-9a-zA-Z_]{4,128}', ], 'ResourcePolicyContent' => [ 'type' => 'string', 'max' => 40000, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'ResourcePolicyId' => [ 'type' => 'string', 'max' => 131, 'pattern' => '^rp-[0-9a-zA-Z_]{4,128}$', ], 'ResourcePolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourcePolicySummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourcePolicyId', ], 'Arn' => [ 'shape' => 'ResourcePolicyArn', ], ], ], 'RoleName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\w+=,.@-]{1,64}', ], 'Root' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'RootId', ], 'Arn' => [ 'shape' => 'RootArn', ], 'Name' => [ 'shape' => 'RootName', ], 'PolicyTypes' => [ 'shape' => 'PolicyTypes', ], ], ], 'RootArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:organizations::\\d{12}:root\\/o-[a-z0-9]{10,32}\\/r-[0-9a-z]{4,32}', ], 'RootId' => [ 'type' => 'string', 'max' => 34, 'pattern' => '^r-[0-9a-z]{4,32}$', ], 'RootName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RootNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Roots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Root', ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, 'fault' => true, ], 'ServicePrincipal' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+=,.@-]*', ], 'SourceParentNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Tags', ], 'members' => [ 'ResourceId' => [ 'shape' => 'TaggableResourceId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TaggableResourceId' => [ 'type' => 'string', 'max' => 130, 'pattern' => '^(r-[0-9a-z]{4,32})|(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})|(^p-[0-9a-zA-Z_]{8,128})|(^rp-[0-9a-zA-Z_]{4,128})$', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TargetName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TargetNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATIONAL_UNIT', 'ROOT', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ExceptionType', ], 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UnsupportedAPIEndpointException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceId' => [ 'shape' => 'TaggableResourceId', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UpdateOrganizationalUnitRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationalUnitId', ], 'members' => [ 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'Name' => [ 'shape' => 'OrganizationalUnitName', ], ], ], 'UpdateOrganizationalUnitResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnit' => [ 'shape' => 'OrganizationalUnit', ], ], ], 'UpdatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'Name' => [ 'shape' => 'PolicyName', ], 'Description' => [ 'shape' => 'PolicyDescription', ], 'Content' => [ 'shape' => 'PolicyContent', ], ], ], 'UpdatePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], ],];
