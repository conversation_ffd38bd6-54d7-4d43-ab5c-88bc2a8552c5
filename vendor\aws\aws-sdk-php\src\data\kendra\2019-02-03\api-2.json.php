<?php
// This file was auto-generated from sdk-root/src/data/kendra/2019-02-03/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-02-03', 'endpointPrefix' => 'kendra', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'kendra', 'serviceFullName' => 'AWSKendraFrontendService', 'serviceId' => 'kendra', 'signatureVersion' => 'v4', 'signingName' => 'kendra', 'targetPrefix' => 'AWSKendraFrontendService', 'uid' => 'kendra-2019-02-03', ], 'operations' => [ 'AssociateEntitiesToExperience' => [ 'name' => 'AssociateEntitiesToExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateEntitiesToExperienceRequest', ], 'output' => [ 'shape' => 'AssociateEntitiesToExperienceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociatePersonasToEntities' => [ 'name' => 'AssociatePersonasToEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociatePersonasToEntitiesRequest', ], 'output' => [ 'shape' => 'AssociatePersonasToEntitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDeleteDocument' => [ 'name' => 'BatchDeleteDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteDocumentRequest', ], 'output' => [ 'shape' => 'BatchDeleteDocumentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDeleteFeaturedResultsSet' => [ 'name' => 'BatchDeleteFeaturedResultsSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteFeaturedResultsSetRequest', ], 'output' => [ 'shape' => 'BatchDeleteFeaturedResultsSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchGetDocumentStatus' => [ 'name' => 'BatchGetDocumentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDocumentStatusRequest', ], 'output' => [ 'shape' => 'BatchGetDocumentStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchPutDocument' => [ 'name' => 'BatchPutDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchPutDocumentRequest', ], 'output' => [ 'shape' => 'BatchPutDocumentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ClearQuerySuggestions' => [ 'name' => 'ClearQuerySuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ClearQuerySuggestionsRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateAccessControlConfiguration' => [ 'name' => 'CreateAccessControlConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccessControlConfigurationRequest', ], 'output' => [ 'shape' => 'CreateAccessControlConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateExperience' => [ 'name' => 'CreateExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExperienceRequest', ], 'output' => [ 'shape' => 'CreateExperienceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFaq' => [ 'name' => 'CreateFaq', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFaqRequest', ], 'output' => [ 'shape' => 'CreateFaqResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateFeaturedResultsSet' => [ 'name' => 'CreateFeaturedResultsSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFeaturedResultsSetRequest', ], 'output' => [ 'shape' => 'CreateFeaturedResultsSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'FeaturedResultsConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateIndex' => [ 'name' => 'CreateIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIndexRequest', ], 'output' => [ 'shape' => 'CreateIndexResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceAlreadyExistException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateQuerySuggestionsBlockList' => [ 'name' => 'CreateQuerySuggestionsBlockList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateQuerySuggestionsBlockListRequest', ], 'output' => [ 'shape' => 'CreateQuerySuggestionsBlockListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateThesaurus' => [ 'name' => 'CreateThesaurus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateThesaurusRequest', ], 'output' => [ 'shape' => 'CreateThesaurusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAccessControlConfiguration' => [ 'name' => 'DeleteAccessControlConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccessControlConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAccessControlConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteExperience' => [ 'name' => 'DeleteExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExperienceRequest', ], 'output' => [ 'shape' => 'DeleteExperienceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteFaq' => [ 'name' => 'DeleteFaq', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFaqRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteIndex' => [ 'name' => 'DeleteIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIndexRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeletePrincipalMapping' => [ 'name' => 'DeletePrincipalMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePrincipalMappingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteQuerySuggestionsBlockList' => [ 'name' => 'DeleteQuerySuggestionsBlockList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteQuerySuggestionsBlockListRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteThesaurus' => [ 'name' => 'DeleteThesaurus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteThesaurusRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeAccessControlConfiguration' => [ 'name' => 'DescribeAccessControlConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccessControlConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeAccessControlConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDataSource' => [ 'name' => 'DescribeDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataSourceRequest', ], 'output' => [ 'shape' => 'DescribeDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeExperience' => [ 'name' => 'DescribeExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExperienceRequest', ], 'output' => [ 'shape' => 'DescribeExperienceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFaq' => [ 'name' => 'DescribeFaq', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFaqRequest', ], 'output' => [ 'shape' => 'DescribeFaqResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeFeaturedResultsSet' => [ 'name' => 'DescribeFeaturedResultsSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFeaturedResultsSetRequest', ], 'output' => [ 'shape' => 'DescribeFeaturedResultsSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeIndex' => [ 'name' => 'DescribeIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIndexRequest', ], 'output' => [ 'shape' => 'DescribeIndexResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePrincipalMapping' => [ 'name' => 'DescribePrincipalMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePrincipalMappingRequest', ], 'output' => [ 'shape' => 'DescribePrincipalMappingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeQuerySuggestionsBlockList' => [ 'name' => 'DescribeQuerySuggestionsBlockList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeQuerySuggestionsBlockListRequest', ], 'output' => [ 'shape' => 'DescribeQuerySuggestionsBlockListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeQuerySuggestionsConfig' => [ 'name' => 'DescribeQuerySuggestionsConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeQuerySuggestionsConfigRequest', ], 'output' => [ 'shape' => 'DescribeQuerySuggestionsConfigResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeThesaurus' => [ 'name' => 'DescribeThesaurus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeThesaurusRequest', ], 'output' => [ 'shape' => 'DescribeThesaurusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateEntitiesFromExperience' => [ 'name' => 'DisassociateEntitiesFromExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateEntitiesFromExperienceRequest', ], 'output' => [ 'shape' => 'DisassociateEntitiesFromExperienceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociatePersonasFromEntities' => [ 'name' => 'DisassociatePersonasFromEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociatePersonasFromEntitiesRequest', ], 'output' => [ 'shape' => 'DisassociatePersonasFromEntitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQuerySuggestions' => [ 'name' => 'GetQuerySuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetQuerySuggestionsRequest', ], 'output' => [ 'shape' => 'GetQuerySuggestionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSnapshots' => [ 'name' => 'GetSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSnapshotsRequest', ], 'output' => [ 'shape' => 'GetSnapshotsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAccessControlConfigurations' => [ 'name' => 'ListAccessControlConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccessControlConfigurationsRequest', ], 'output' => [ 'shape' => 'ListAccessControlConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSourceSyncJobs' => [ 'name' => 'ListDataSourceSyncJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataSourceSyncJobsRequest', ], 'output' => [ 'shape' => 'ListDataSourceSyncJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEntityPersonas' => [ 'name' => 'ListEntityPersonas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntityPersonasRequest', ], 'output' => [ 'shape' => 'ListEntityPersonasResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListExperienceEntities' => [ 'name' => 'ListExperienceEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExperienceEntitiesRequest', ], 'output' => [ 'shape' => 'ListExperienceEntitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListExperiences' => [ 'name' => 'ListExperiences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExperiencesRequest', ], 'output' => [ 'shape' => 'ListExperiencesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFaqs' => [ 'name' => 'ListFaqs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFaqsRequest', ], 'output' => [ 'shape' => 'ListFaqsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFeaturedResultsSets' => [ 'name' => 'ListFeaturedResultsSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFeaturedResultsSetsRequest', ], 'output' => [ 'shape' => 'ListFeaturedResultsSetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListGroupsOlderThanOrderingId' => [ 'name' => 'ListGroupsOlderThanOrderingId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGroupsOlderThanOrderingIdRequest', ], 'output' => [ 'shape' => 'ListGroupsOlderThanOrderingIdResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIndices' => [ 'name' => 'ListIndices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIndicesRequest', ], 'output' => [ 'shape' => 'ListIndicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListQuerySuggestionsBlockLists' => [ 'name' => 'ListQuerySuggestionsBlockLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListQuerySuggestionsBlockListsRequest', ], 'output' => [ 'shape' => 'ListQuerySuggestionsBlockListsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListThesauri' => [ 'name' => 'ListThesauri', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListThesauriRequest', ], 'output' => [ 'shape' => 'ListThesauriResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutPrincipalMapping' => [ 'name' => 'PutPrincipalMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPrincipalMappingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Query' => [ 'name' => 'Query', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'QueryRequest', ], 'output' => [ 'shape' => 'QueryResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'Retrieve' => [ 'name' => 'Retrieve', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RetrieveRequest', ], 'output' => [ 'shape' => 'RetrieveResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartDataSourceSyncJob' => [ 'name' => 'StartDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDataSourceSyncJobRequest', ], 'output' => [ 'shape' => 'StartDataSourceSyncJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopDataSourceSyncJob' => [ 'name' => 'StopDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDataSourceSyncJobRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SubmitFeedback' => [ 'name' => 'SubmitFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SubmitFeedbackRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateAccessControlConfiguration' => [ 'name' => 'UpdateAccessControlConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAccessControlConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateAccessControlConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateExperience' => [ 'name' => 'UpdateExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateExperienceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateFeaturedResultsSet' => [ 'name' => 'UpdateFeaturedResultsSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFeaturedResultsSetRequest', ], 'output' => [ 'shape' => 'UpdateFeaturedResultsSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'FeaturedResultsConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateIndex' => [ 'name' => 'UpdateIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIndexRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateQuerySuggestionsBlockList' => [ 'name' => 'UpdateQuerySuggestionsBlockList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateQuerySuggestionsBlockListRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateQuerySuggestionsConfig' => [ 'name' => 'UpdateQuerySuggestionsConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateQuerySuggestionsConfigRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateThesaurus' => [ 'name' => 'UpdateThesaurus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateThesaurusRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessControlConfigurationId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'AccessControlConfigurationName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\S\\s]*', ], 'AccessControlConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'AccessControlConfigurationId', ], ], ], 'AccessControlConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlConfigurationSummary', ], ], 'AccessControlListConfiguration' => [ 'type' => 'structure', 'members' => [ 'KeyPath' => [ 'shape' => 'S3ObjectKey', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AclConfiguration' => [ 'type' => 'structure', 'required' => [ 'AllowedGroupsColumnName', ], 'members' => [ 'AllowedGroupsColumnName' => [ 'shape' => 'ColumnName', ], ], ], 'AdditionalResultAttribute' => [ 'type' => 'structure', 'required' => [ 'Key', 'ValueType', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'ValueType' => [ 'shape' => 'AdditionalResultAttributeValueType', ], 'Value' => [ 'shape' => 'AdditionalResultAttributeValue', ], ], ], 'AdditionalResultAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalResultAttribute', ], ], 'AdditionalResultAttributeValue' => [ 'type' => 'structure', 'members' => [ 'TextWithHighlightsValue' => [ 'shape' => 'TextWithHighlights', ], ], ], 'AdditionalResultAttributeValueType' => [ 'type' => 'string', 'enum' => [ 'TEXT_WITH_HIGHLIGHTS_VALUE', ], ], 'AlfrescoConfiguration' => [ 'type' => 'structure', 'required' => [ 'SiteUrl', 'SiteId', 'SecretArn', 'SslCertificateS3Path', ], 'members' => [ 'SiteUrl' => [ 'shape' => 'SiteUrl', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SslCertificateS3Path' => [ 'shape' => 'S3Path', ], 'CrawlSystemFolders' => [ 'shape' => 'Boolean', ], 'CrawlComments' => [ 'shape' => 'Boolean', ], 'EntityFilter' => [ 'shape' => 'EntityFilter', ], 'DocumentLibraryFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'BlogFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'WikiFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'AlfrescoEntity' => [ 'type' => 'string', 'enum' => [ 'wiki', 'blog', 'documentLibrary', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssociateEntitiesToExperienceFailedEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedEntity', ], 'max' => 20, 'min' => 1, ], 'AssociateEntitiesToExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', 'EntityList', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'EntityList' => [ 'shape' => 'AssociateEntityList', ], ], ], 'AssociateEntitiesToExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntityList' => [ 'shape' => 'AssociateEntitiesToExperienceFailedEntityList', ], ], ], 'AssociateEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityConfiguration', ], 'max' => 20, 'min' => 1, ], 'AssociatePersonasToEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', 'Personas', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Personas' => [ 'shape' => 'EntityPersonaConfigurationList', ], ], ], 'AssociatePersonasToEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntityList' => [ 'shape' => 'FailedEntityList', ], ], ], 'AttributeFilter' => [ 'type' => 'structure', 'members' => [ 'AndAllFilters' => [ 'shape' => 'AttributeFilterList', ], 'OrAllFilters' => [ 'shape' => 'AttributeFilterList', ], 'NotFilter' => [ 'shape' => 'AttributeFilter', ], 'EqualsTo' => [ 'shape' => 'DocumentAttribute', ], 'ContainsAll' => [ 'shape' => 'DocumentAttribute', ], 'ContainsAny' => [ 'shape' => 'DocumentAttribute', ], 'GreaterThan' => [ 'shape' => 'DocumentAttribute', ], 'GreaterThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], 'LessThan' => [ 'shape' => 'DocumentAttribute', ], 'LessThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], ], ], 'AttributeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeFilter', ], ], 'AttributeSuggestionsDescribeConfig' => [ 'type' => 'structure', 'members' => [ 'SuggestableConfigList' => [ 'shape' => 'SuggestableConfigList', ], 'AttributeSuggestionsMode' => [ 'shape' => 'AttributeSuggestionsMode', ], ], ], 'AttributeSuggestionsGetConfig' => [ 'type' => 'structure', 'members' => [ 'SuggestionAttributes' => [ 'shape' => 'DocumentAttributeKeyList', ], 'AdditionalResponseAttributes' => [ 'shape' => 'DocumentAttributeKeyList', ], 'AttributeFilter' => [ 'shape' => 'AttributeFilter', ], 'UserContext' => [ 'shape' => 'UserContext', ], ], ], 'AttributeSuggestionsMode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'AttributeSuggestionsUpdateConfig' => [ 'type' => 'structure', 'members' => [ 'SuggestableConfigList' => [ 'shape' => 'SuggestableConfigList', ], 'AttributeSuggestionsMode' => [ 'shape' => 'AttributeSuggestionsMode', ], ], ], 'AuthenticationConfiguration' => [ 'type' => 'structure', 'members' => [ 'BasicAuthentication' => [ 'shape' => 'BasicAuthenticationConfigurationList', ], ], ], 'BasicAuthenticationConfiguration' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Credentials', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Credentials' => [ 'shape' => 'SecretArn', ], ], ], 'BasicAuthenticationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BasicAuthenticationConfiguration', ], 'max' => 10, 'min' => 0, ], 'BatchDeleteDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'DocumentIdList', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DocumentIdList' => [ 'shape' => 'DocumentIdList', ], 'DataSourceSyncJobMetricTarget' => [ 'shape' => 'DataSourceSyncJobMetricTarget', ], ], ], 'BatchDeleteDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'FailedDocuments' => [ 'shape' => 'BatchDeleteDocumentResponseFailedDocuments', ], ], ], 'BatchDeleteDocumentResponseFailedDocument' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchDeleteDocumentResponseFailedDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteDocumentResponseFailedDocument', ], ], 'BatchDeleteFeaturedResultsSetError' => [ 'type' => 'structure', 'required' => [ 'Id', 'ErrorCode', 'ErrorMessage', ], 'members' => [ 'Id' => [ 'shape' => 'FeaturedResultsSetId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchDeleteFeaturedResultsSetErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteFeaturedResultsSetError', ], ], 'BatchDeleteFeaturedResultsSetRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'FeaturedResultsSetIds', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'FeaturedResultsSetIds' => [ 'shape' => 'FeaturedResultsSetIdList', ], ], ], 'BatchDeleteFeaturedResultsSetResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteFeaturedResultsSetErrors', ], ], ], 'BatchGetDocumentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'DocumentInfoList', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DocumentInfoList' => [ 'shape' => 'DocumentInfoList', ], ], ], 'BatchGetDocumentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'BatchGetDocumentStatusResponseErrors', ], 'DocumentStatusList' => [ 'shape' => 'DocumentStatusList', ], ], ], 'BatchGetDocumentStatusResponseError' => [ 'type' => 'structure', 'members' => [ 'DocumentId' => [ 'shape' => 'DocumentId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchGetDocumentStatusResponseErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetDocumentStatusResponseError', ], ], 'BatchPutDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Documents', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Documents' => [ 'shape' => 'DocumentList', ], 'CustomDocumentEnrichmentConfiguration' => [ 'shape' => 'CustomDocumentEnrichmentConfiguration', ], ], ], 'BatchPutDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'FailedDocuments' => [ 'shape' => 'BatchPutDocumentResponseFailedDocuments', ], ], ], 'BatchPutDocumentResponseFailedDocument' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchPutDocumentResponseFailedDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutDocumentResponseFailedDocument', ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', ], 'BoxConfiguration' => [ 'type' => 'structure', 'required' => [ 'EnterpriseId', 'SecretArn', ], 'members' => [ 'EnterpriseId' => [ 'shape' => 'EnterpriseId', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'CrawlComments' => [ 'shape' => 'Boolean', ], 'CrawlTasks' => [ 'shape' => 'Boolean', ], 'CrawlWebLinks' => [ 'shape' => 'Boolean', ], 'FileFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'TaskFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'CommentFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'WebLinkFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'CapacityUnitsConfiguration' => [ 'type' => 'structure', 'required' => [ 'StorageCapacityUnits', 'QueryCapacityUnits', ], 'members' => [ 'StorageCapacityUnits' => [ 'shape' => 'StorageCapacityUnit', ], 'QueryCapacityUnits' => [ 'shape' => 'QueryCapacityUnit', ], ], ], 'ChangeDetectingColumns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'max' => 5, 'min' => 1, ], 'ClaimRegex' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'ClearQuerySuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'ClickFeedback' => [ 'type' => 'structure', 'required' => [ 'ResultId', 'ClickTime', ], 'members' => [ 'ResultId' => [ 'shape' => 'ResultId', ], 'ClickTime' => [ 'shape' => 'Timestamp', ], ], ], 'ClickFeedbackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClickFeedback', ], ], 'ClientTokenName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CollapseConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentAttributeKey', ], 'members' => [ 'DocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'SortingConfigurations' => [ 'shape' => 'SortingConfigurationList', ], 'MissingAttributeKeyStrategy' => [ 'shape' => 'MissingAttributeKeyStrategy', ], 'Expand' => [ 'shape' => 'Boolean', ], 'ExpandConfiguration' => [ 'shape' => 'ExpandConfiguration', ], ], ], 'CollapsedResultDetail' => [ 'type' => 'structure', 'required' => [ 'DocumentAttribute', ], 'members' => [ 'DocumentAttribute' => [ 'shape' => 'DocumentAttribute', ], 'ExpandedResults' => [ 'shape' => 'ExpandedResultList', ], ], ], 'ColumnConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentIdColumnName', 'DocumentDataColumnName', 'ChangeDetectingColumns', ], 'members' => [ 'DocumentIdColumnName' => [ 'shape' => 'ColumnName', ], 'DocumentDataColumnName' => [ 'shape' => 'ColumnName', ], 'DocumentTitleColumnName' => [ 'shape' => 'ColumnName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'ChangeDetectingColumns' => [ 'shape' => 'ChangeDetectingColumns', ], ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'ConditionOperator' => [ 'type' => 'string', 'enum' => [ 'GreaterThan', 'GreaterThanOrEquals', 'LessThan', 'LessThanOrEquals', 'Equals', 'NotEquals', 'Contains', 'NotContains', 'Exists', 'NotExists', 'BeginsWith', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ConflictingItem' => [ 'type' => 'structure', 'members' => [ 'QueryText' => [ 'shape' => 'QueryText', ], 'SetName' => [ 'shape' => 'String', ], 'SetId' => [ 'shape' => 'String', ], ], ], 'ConflictingItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictingItem', ], ], 'ConfluenceAttachmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'AttachmentFieldMappings' => [ 'shape' => 'ConfluenceAttachmentFieldMappingsList', ], ], ], 'ConfluenceAttachmentFieldMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfluenceAttachmentToIndexFieldMapping', ], 'max' => 11, 'min' => 1, ], 'ConfluenceAttachmentFieldName' => [ 'type' => 'string', 'enum' => [ 'AUTHOR', 'CONTENT_TYPE', 'CREATED_DATE', 'DISPLAY_URL', 'FILE_SIZE', 'ITEM_TYPE', 'PARENT_ID', 'SPACE_KEY', 'SPACE_NAME', 'URL', 'VERSION', ], ], 'ConfluenceAttachmentToIndexFieldMapping' => [ 'type' => 'structure', 'members' => [ 'DataSourceFieldName' => [ 'shape' => 'ConfluenceAttachmentFieldName', ], 'DateFieldFormat' => [ 'shape' => 'DataSourceDateFieldFormat', ], 'IndexFieldName' => [ 'shape' => 'IndexFieldName', ], ], ], 'ConfluenceAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'HTTP_BASIC', 'PAT', ], ], 'ConfluenceBlogConfiguration' => [ 'type' => 'structure', 'members' => [ 'BlogFieldMappings' => [ 'shape' => 'ConfluenceBlogFieldMappingsList', ], ], ], 'ConfluenceBlogFieldMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfluenceBlogToIndexFieldMapping', ], 'max' => 9, 'min' => 1, ], 'ConfluenceBlogFieldName' => [ 'type' => 'string', 'enum' => [ 'AUTHOR', 'DISPLAY_URL', 'ITEM_TYPE', 'LABELS', 'PUBLISH_DATE', 'SPACE_KEY', 'SPACE_NAME', 'URL', 'VERSION', ], ], 'ConfluenceBlogToIndexFieldMapping' => [ 'type' => 'structure', 'members' => [ 'DataSourceFieldName' => [ 'shape' => 'ConfluenceBlogFieldName', ], 'DateFieldFormat' => [ 'shape' => 'DataSourceDateFieldFormat', ], 'IndexFieldName' => [ 'shape' => 'IndexFieldName', ], ], ], 'ConfluenceConfiguration' => [ 'type' => 'structure', 'required' => [ 'ServerUrl', 'SecretArn', 'Version', ], 'members' => [ 'ServerUrl' => [ 'shape' => 'Url', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'Version' => [ 'shape' => 'ConfluenceVersion', ], 'SpaceConfiguration' => [ 'shape' => 'ConfluenceSpaceConfiguration', ], 'PageConfiguration' => [ 'shape' => 'ConfluencePageConfiguration', ], 'BlogConfiguration' => [ 'shape' => 'ConfluenceBlogConfiguration', ], 'AttachmentConfiguration' => [ 'shape' => 'ConfluenceAttachmentConfiguration', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ProxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], 'AuthenticationType' => [ 'shape' => 'ConfluenceAuthenticationType', ], ], ], 'ConfluencePageConfiguration' => [ 'type' => 'structure', 'members' => [ 'PageFieldMappings' => [ 'shape' => 'ConfluencePageFieldMappingsList', ], ], ], 'ConfluencePageFieldMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfluencePageToIndexFieldMapping', ], 'max' => 12, 'min' => 1, ], 'ConfluencePageFieldName' => [ 'type' => 'string', 'enum' => [ 'AUTHOR', 'CONTENT_STATUS', 'CREATED_DATE', 'DISPLAY_URL', 'ITEM_TYPE', 'LABELS', 'MODIFIED_DATE', 'PARENT_ID', 'SPACE_KEY', 'SPACE_NAME', 'URL', 'VERSION', ], ], 'ConfluencePageToIndexFieldMapping' => [ 'type' => 'structure', 'members' => [ 'DataSourceFieldName' => [ 'shape' => 'ConfluencePageFieldName', ], 'DateFieldFormat' => [ 'shape' => 'DataSourceDateFieldFormat', ], 'IndexFieldName' => [ 'shape' => 'IndexFieldName', ], ], ], 'ConfluenceSpaceConfiguration' => [ 'type' => 'structure', 'members' => [ 'CrawlPersonalSpaces' => [ 'shape' => 'Boolean', ], 'CrawlArchivedSpaces' => [ 'shape' => 'Boolean', ], 'IncludeSpaces' => [ 'shape' => 'ConfluenceSpaceList', ], 'ExcludeSpaces' => [ 'shape' => 'ConfluenceSpaceList', ], 'SpaceFieldMappings' => [ 'shape' => 'ConfluenceSpaceFieldMappingsList', ], ], ], 'ConfluenceSpaceFieldMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfluenceSpaceToIndexFieldMapping', ], 'max' => 4, 'min' => 1, ], 'ConfluenceSpaceFieldName' => [ 'type' => 'string', 'enum' => [ 'DISPLAY_URL', 'ITEM_TYPE', 'SPACE_KEY', 'URL', ], ], 'ConfluenceSpaceIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'ConfluenceSpaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfluenceSpaceIdentifier', ], 'min' => 1, ], 'ConfluenceSpaceToIndexFieldMapping' => [ 'type' => 'structure', 'members' => [ 'DataSourceFieldName' => [ 'shape' => 'ConfluenceSpaceFieldName', ], 'DateFieldFormat' => [ 'shape' => 'DataSourceDateFieldFormat', ], 'IndexFieldName' => [ 'shape' => 'IndexFieldName', ], ], ], 'ConfluenceVersion' => [ 'type' => 'string', 'enum' => [ 'CLOUD', 'SERVER', ], ], 'ConnectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'DatabaseHost', 'DatabasePort', 'DatabaseName', 'TableName', 'SecretArn', ], 'members' => [ 'DatabaseHost' => [ 'shape' => 'DatabaseHost', ], 'DatabasePort' => [ 'shape' => 'DatabasePort', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'Content' => [ 'type' => 'string', ], 'ContentSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'DataSourceIds' => [ 'shape' => 'DataSourceIdList', ], 'FaqIds' => [ 'shape' => 'FaqIdsList', ], 'DirectPutContent' => [ 'shape' => 'Boolean', ], ], ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'PDF', 'HTML', 'MS_WORD', 'PLAIN_TEXT', 'PPT', 'RTF', 'XML', 'XSLT', 'MS_EXCEL', 'CSV', 'JSON', 'MD', ], ], 'Correction' => [ 'type' => 'structure', 'members' => [ 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], 'Term' => [ 'shape' => 'String', ], 'CorrectedTerm' => [ 'shape' => 'String', ], ], ], 'CorrectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Correction', ], ], 'CrawlDepth' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'CreateAccessControlConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Name', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'AccessControlConfigurationName', ], 'Description' => [ 'shape' => 'Description', ], 'AccessControlList' => [ 'shape' => 'PrincipalList', ], 'HierarchicalAccessControlList' => [ 'shape' => 'HierarchicalPrincipalList', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], ], ], 'CreateAccessControlConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'AccessControlConfigurationId', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IndexId', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'DataSourceName', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Type' => [ 'shape' => 'DataSourceType', ], 'Configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'Description' => [ 'shape' => 'Description', ], 'Schedule' => [ 'shape' => 'ScanSchedule', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'CustomDocumentEnrichmentConfiguration' => [ 'shape' => 'CustomDocumentEnrichmentConfiguration', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], ], ], 'CreateExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IndexId', ], 'members' => [ 'Name' => [ 'shape' => 'ExperienceName', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Configuration' => [ 'shape' => 'ExperienceConfiguration', ], 'Description' => [ 'shape' => 'Description', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], ], ], 'CreateExperienceResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], ], ], 'CreateFaqRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Name', 'S3Path', 'RoleArn', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'FaqName', ], 'Description' => [ 'shape' => 'Description', ], 'S3Path' => [ 'shape' => 'S3Path', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'FileFormat' => [ 'shape' => 'FaqFileFormat', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'CreateFaqResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'FaqId', ], ], ], 'CreateFeaturedResultsSetRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'FeaturedResultsSetName', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'FeaturedResultsSetName' => [ 'shape' => 'FeaturedResultsSetName', ], 'Description' => [ 'shape' => 'FeaturedResultsSetDescription', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', ], 'Status' => [ 'shape' => 'FeaturedResultsSetStatus', ], 'QueryTexts' => [ 'shape' => 'QueryTextList', ], 'FeaturedDocuments' => [ 'shape' => 'FeaturedDocumentList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFeaturedResultsSetResponse' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSet' => [ 'shape' => 'FeaturedResultsSet', ], ], ], 'CreateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', ], 'members' => [ 'Name' => [ 'shape' => 'IndexName', ], 'Edition' => [ 'shape' => 'IndexEdition', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ServerSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'Description' => [ 'shape' => 'Description', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'UserTokenConfigurations' => [ 'shape' => 'UserTokenConfigurationList', ], 'UserContextPolicy' => [ 'shape' => 'UserContextPolicy', ], 'UserGroupResolutionConfiguration' => [ 'shape' => 'UserGroupResolutionConfiguration', ], ], ], 'CreateIndexResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IndexId', ], ], ], 'CreateQuerySuggestionsBlockListRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Name', 'SourceS3Path', 'RoleArn', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'QuerySuggestionsBlockListName', ], 'Description' => [ 'shape' => 'Description', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateQuerySuggestionsBlockListResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], ], ], 'CreateThesaurusRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Name', 'RoleArn', 'SourceS3Path', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'ThesaurusName', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], 'ClientToken' => [ 'shape' => 'ClientTokenName', 'idempotencyToken' => true, ], ], ], 'CreateThesaurusResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], ], ], 'CustomDocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'InlineConfigurations' => [ 'shape' => 'InlineCustomDocumentEnrichmentConfigurationList', ], 'PreExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], 'PostExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3Configuration' => [ 'shape' => 'S3DataSourceConfiguration', ], 'SharePointConfiguration' => [ 'shape' => 'SharePointConfiguration', ], 'DatabaseConfiguration' => [ 'shape' => 'DatabaseConfiguration', ], 'SalesforceConfiguration' => [ 'shape' => 'SalesforceConfiguration', ], 'OneDriveConfiguration' => [ 'shape' => 'OneDriveConfiguration', ], 'ServiceNowConfiguration' => [ 'shape' => 'ServiceNowConfiguration', ], 'ConfluenceConfiguration' => [ 'shape' => 'ConfluenceConfiguration', ], 'GoogleDriveConfiguration' => [ 'shape' => 'GoogleDriveConfiguration', ], 'WebCrawlerConfiguration' => [ 'shape' => 'WebCrawlerConfiguration', ], 'WorkDocsConfiguration' => [ 'shape' => 'WorkDocsConfiguration', ], 'FsxConfiguration' => [ 'shape' => 'FsxConfiguration', ], 'SlackConfiguration' => [ 'shape' => 'SlackConfiguration', ], 'BoxConfiguration' => [ 'shape' => 'BoxConfiguration', ], 'QuipConfiguration' => [ 'shape' => 'QuipConfiguration', ], 'JiraConfiguration' => [ 'shape' => 'JiraConfiguration', ], 'GitHubConfiguration' => [ 'shape' => 'GitHubConfiguration', ], 'AlfrescoConfiguration' => [ 'shape' => 'AlfrescoConfiguration', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated AlfrescoConfiguration in favor of TemplateConfiguration', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], ], ], 'DataSourceDateFieldFormat' => [ 'type' => 'string', 'max' => 40, 'min' => 4, 'pattern' => '^(?!\\s).*(?<!\\s)$', ], 'DataSourceFieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_.]*$', ], 'DataSourceGroup' => [ 'type' => 'structure', 'required' => [ 'GroupId', 'DataSourceId', ], 'members' => [ 'GroupId' => [ 'shape' => 'PrincipalName', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'DataSourceGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceGroup', ], 'max' => 2048, 'min' => 1, ], 'DataSourceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'DataSourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceId', ], 'max' => 100, 'min' => 1, ], 'DataSourceInclusionsExclusionsStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceInclusionsExclusionsStringsMember', ], 'max' => 250, 'min' => 0, ], 'DataSourceInclusionsExclusionsStringsMember' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'DataSourceName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'FAILED', 'UPDATING', 'ACTIVE', ], ], 'DataSourceSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DataSourceName', ], 'Id' => [ 'shape' => 'DataSourceId', ], 'Type' => [ 'shape' => 'DataSourceType', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'DataSourceStatus', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DataSourceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSummary', ], ], 'DataSourceSyncJob' => [ 'type' => 'structure', 'members' => [ 'ExecutionId' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'DataSourceSyncJobStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'DataSourceErrorCode' => [ 'shape' => 'String', ], 'Metrics' => [ 'shape' => 'DataSourceSyncJobMetrics', ], ], ], 'DataSourceSyncJobHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSyncJob', ], ], 'DataSourceSyncJobId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'DataSourceSyncJobMetricTarget' => [ 'type' => 'structure', 'required' => [ 'DataSourceId', ], 'members' => [ 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'DataSourceSyncJobId' => [ 'shape' => 'DataSourceSyncJobId', ], ], ], 'DataSourceSyncJobMetrics' => [ 'type' => 'structure', 'members' => [ 'DocumentsAdded' => [ 'shape' => 'MetricValue', ], 'DocumentsModified' => [ 'shape' => 'MetricValue', ], 'DocumentsDeleted' => [ 'shape' => 'MetricValue', ], 'DocumentsFailed' => [ 'shape' => 'MetricValue', ], 'DocumentsScanned' => [ 'shape' => 'MetricValue', ], ], ], 'DataSourceSyncJobStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'SYNCING', 'INCOMPLETE', 'STOPPING', 'ABORTED', 'SYNCING_INDEXING', ], ], 'DataSourceToIndexFieldMapping' => [ 'type' => 'structure', 'required' => [ 'DataSourceFieldName', 'IndexFieldName', ], 'members' => [ 'DataSourceFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DateFieldFormat' => [ 'shape' => 'DataSourceDateFieldFormat', ], 'IndexFieldName' => [ 'shape' => 'IndexFieldName', ], ], ], 'DataSourceToIndexFieldMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceToIndexFieldMapping', ], 'max' => 100, 'min' => 1, ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'SHAREPOINT', 'DATABASE', 'SALESFORCE', 'ONEDRIVE', 'SERVICENOW', 'CUSTOM', 'CONFLUENCE', 'GOOGLEDRIVE', 'WEBCRAWLER', 'WORKDOCS', 'FSX', 'SLACK', 'BOX', 'QUIP', 'JIRA', 'GITHUB', 'ALFRESCO', 'TEMPLATE', ], ], 'DataSourceVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'SubnetIds', 'SecurityGroupIds', ], 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIdList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], ], ], 'DatabaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'DatabaseEngineType', 'ConnectionConfiguration', 'ColumnConfiguration', ], 'members' => [ 'DatabaseEngineType' => [ 'shape' => 'DatabaseEngineType', ], 'ConnectionConfiguration' => [ 'shape' => 'ConnectionConfiguration', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'ColumnConfiguration' => [ 'shape' => 'ColumnConfiguration', ], 'AclConfiguration' => [ 'shape' => 'AclConfiguration', ], 'SqlConfiguration' => [ 'shape' => 'SqlConfiguration', ], ], ], 'DatabaseEngineType' => [ 'type' => 'string', 'enum' => [ 'RDS_AURORA_MYSQL', 'RDS_AURORA_POSTGRESQL', 'RDS_MYSQL', 'RDS_POSTGRESQL', ], ], 'DatabaseHost' => [ 'type' => 'string', 'max' => 253, 'min' => 1, ], 'DatabaseName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'DatabasePort' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'DeleteAccessControlConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'AccessControlConfigurationId', ], ], ], 'DeleteAccessControlConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DeleteExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DeleteExperienceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFaqRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'FaqId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DeleteIndexRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'IndexId', ], ], ], 'DeletePrincipalMappingRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'GroupId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'GroupId' => [ 'shape' => 'GroupId', ], 'OrderingId' => [ 'shape' => 'PrincipalOrderingId', ], ], ], 'DeleteQuerySuggestionsBlockListRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], ], ], 'DeleteThesaurusRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeAccessControlConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'AccessControlConfigurationId', ], ], ], 'DescribeAccessControlConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AccessControlConfigurationName', ], 'Description' => [ 'shape' => 'Description', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'AccessControlList' => [ 'shape' => 'PrincipalList', ], 'HierarchicalAccessControlList' => [ 'shape' => 'HierarchicalPrincipalList', ], ], ], 'DescribeDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'DataSourceName', ], 'Type' => [ 'shape' => 'DataSourceType', ], 'Configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'DataSourceStatus', ], 'Schedule' => [ 'shape' => 'ScanSchedule', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'CustomDocumentEnrichmentConfiguration' => [ 'shape' => 'CustomDocumentEnrichmentConfiguration', ], ], ], 'DescribeExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'ExperienceName', ], 'Endpoints' => [ 'shape' => 'ExperienceEndpoints', ], 'Configuration' => [ 'shape' => 'ExperienceConfiguration', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'ExperienceStatus', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'DescribeFaqRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'FaqId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeFaqResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'FaqId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'FaqName', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'S3Path' => [ 'shape' => 'S3Path', ], 'Status' => [ 'shape' => 'FaqStatus', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'FileFormat' => [ 'shape' => 'FaqFileFormat', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'DescribeFeaturedResultsSetRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'FeaturedResultsSetId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'FeaturedResultsSetId' => [ 'shape' => 'FeaturedResultsSetId', ], ], ], 'DescribeFeaturedResultsSetResponse' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSetId' => [ 'shape' => 'FeaturedResultsSetId', ], 'FeaturedResultsSetName' => [ 'shape' => 'FeaturedResultsSetName', ], 'Description' => [ 'shape' => 'FeaturedResultsSetDescription', ], 'Status' => [ 'shape' => 'FeaturedResultsSetStatus', ], 'QueryTexts' => [ 'shape' => 'QueryTextList', ], 'FeaturedDocumentsWithMetadata' => [ 'shape' => 'FeaturedDocumentWithMetadataList', ], 'FeaturedDocumentsMissing' => [ 'shape' => 'FeaturedDocumentMissingList', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Long', ], 'CreationTimestamp' => [ 'shape' => 'Long', ], ], ], 'DescribeIndexRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'IndexId', ], ], ], 'DescribeIndexResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'IndexName', ], 'Id' => [ 'shape' => 'IndexId', ], 'Edition' => [ 'shape' => 'IndexEdition', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ServerSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'Status' => [ 'shape' => 'IndexStatus', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'DocumentMetadataConfigurations' => [ 'shape' => 'DocumentMetadataConfigurationList', ], 'IndexStatistics' => [ 'shape' => 'IndexStatistics', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'CapacityUnits' => [ 'shape' => 'CapacityUnitsConfiguration', ], 'UserTokenConfigurations' => [ 'shape' => 'UserTokenConfigurationList', ], 'UserContextPolicy' => [ 'shape' => 'UserContextPolicy', ], 'UserGroupResolutionConfiguration' => [ 'shape' => 'UserGroupResolutionConfiguration', ], ], ], 'DescribePrincipalMappingRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'GroupId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'GroupId' => [ 'shape' => 'GroupId', ], ], ], 'DescribePrincipalMappingResponse' => [ 'type' => 'structure', 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'GroupId' => [ 'shape' => 'GroupId', ], 'GroupOrderingIdSummaries' => [ 'shape' => 'GroupOrderingIdSummaries', ], ], ], 'DescribeQuerySuggestionsBlockListRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], ], ], 'DescribeQuerySuggestionsBlockListResponse' => [ 'type' => 'structure', 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], 'Name' => [ 'shape' => 'QuerySuggestionsBlockListName', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'QuerySuggestionsBlockListStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'FileSizeBytes' => [ 'shape' => 'Long', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'DescribeQuerySuggestionsConfigRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeQuerySuggestionsConfigResponse' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'Mode', ], 'Status' => [ 'shape' => 'QuerySuggestionsStatus', ], 'QueryLogLookBackWindowInDays' => [ 'shape' => 'Integer', ], 'IncludeQueriesWithoutUserInformation' => [ 'shape' => 'ObjectBoolean', ], 'MinimumNumberOfQueryingUsers' => [ 'shape' => 'MinimumNumberOfQueryingUsers', ], 'MinimumQueryCount' => [ 'shape' => 'MinimumQueryCount', ], 'LastSuggestionsBuildTime' => [ 'shape' => 'Timestamp', ], 'LastClearTime' => [ 'shape' => 'Timestamp', ], 'TotalSuggestionsCount' => [ 'shape' => 'Integer', ], 'AttributeSuggestionsConfig' => [ 'shape' => 'AttributeSuggestionsDescribeConfig', ], ], ], 'DescribeThesaurusRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'DescribeThesaurusResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'ThesaurusName', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'ThesaurusStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], 'FileSizeBytes' => [ 'shape' => 'Long', ], 'TermCount' => [ 'shape' => 'Long', ], 'SynonymRuleCount' => [ 'shape' => 'Long', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'DisassociateEntitiesFromExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', 'EntityList', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'EntityList' => [ 'shape' => 'DisassociateEntityList', ], ], ], 'DisassociateEntitiesFromExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntityList' => [ 'shape' => 'FailedEntityList', ], ], ], 'DisassociateEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityConfiguration', ], 'max' => 40, 'min' => 1, ], 'DisassociatePersonasFromEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', 'EntityIds', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'EntityIds' => [ 'shape' => 'EntityIdsList', ], ], ], 'DisassociatePersonasFromEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntityList' => [ 'shape' => 'FailedEntityList', ], ], ], 'Document' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], 'Title' => [ 'shape' => 'Title', ], 'Blob' => [ 'shape' => 'Blob', ], 'S3Path' => [ 'shape' => 'S3Path', ], 'Attributes' => [ 'shape' => 'DocumentAttributeList', ], 'AccessControlList' => [ 'shape' => 'PrincipalList', ], 'HierarchicalAccessControlList' => [ 'shape' => 'HierarchicalPrincipalList', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'AccessControlConfigurationId' => [ 'shape' => 'AccessControlConfigurationId', ], ], ], 'DocumentAttribute' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'DocumentAttributeKey', ], 'Value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeCondition' => [ 'type' => 'structure', 'required' => [ 'ConditionDocumentAttributeKey', 'Operator', ], 'members' => [ 'ConditionDocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'Operator' => [ 'shape' => 'ConditionOperator', ], 'ConditionOnValue' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9_][a-zA-Z0-9_-]*', ], 'DocumentAttributeKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttributeKey', ], 'max' => 100, 'min' => 1, ], 'DocumentAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttribute', ], ], 'DocumentAttributeStringListValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DocumentAttributeStringValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DocumentAttributeTarget' => [ 'type' => 'structure', 'members' => [ 'TargetDocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'TargetDocumentAttributeValueDeletion' => [ 'shape' => 'Boolean', ], 'TargetDocumentAttributeValue' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'StringValue' => [ 'shape' => 'DocumentAttributeStringValue', ], 'StringListValue' => [ 'shape' => 'DocumentAttributeStringListValue', ], 'LongValue' => [ 'shape' => 'Long', ], 'DateValue' => [ 'shape' => 'Timestamp', ], ], ], 'DocumentAttributeValueCountPair' => [ 'type' => 'structure', 'members' => [ 'DocumentAttributeValue' => [ 'shape' => 'DocumentAttributeValue', ], 'Count' => [ 'shape' => 'Integer', ], 'FacetResults' => [ 'shape' => 'FacetResultList', ], ], ], 'DocumentAttributeValueCountPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttributeValueCountPair', ], ], 'DocumentAttributeValueType' => [ 'type' => 'string', 'enum' => [ 'STRING_VALUE', 'STRING_LIST_VALUE', 'LONG_VALUE', 'DATE_VALUE', ], ], 'DocumentId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DocumentIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentId', ], 'max' => 10, 'min' => 1, ], 'DocumentInfo' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'DocumentId' => [ 'shape' => 'DocumentId', ], 'Attributes' => [ 'shape' => 'DocumentAttributeList', ], ], ], 'DocumentInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentInfo', ], 'max' => 10, 'min' => 1, ], 'DocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Document', ], 'max' => 10, 'min' => 1, ], 'DocumentMetadataBoolean' => [ 'type' => 'boolean', ], 'DocumentMetadataConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentMetadataConfigurationName', ], 'Type' => [ 'shape' => 'DocumentAttributeValueType', ], 'Relevance' => [ 'shape' => 'Relevance', ], 'Search' => [ 'shape' => 'Search', ], ], ], 'DocumentMetadataConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentMetadataConfiguration', ], 'max' => 500, 'min' => 0, ], 'DocumentMetadataConfigurationName' => [ 'type' => 'string', 'max' => 30, 'min' => 1, ], 'DocumentRelevanceConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'Relevance', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentMetadataConfigurationName', ], 'Relevance' => [ 'shape' => 'Relevance', ], ], ], 'DocumentRelevanceOverrideConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentRelevanceConfiguration', ], 'max' => 500, 'min' => 0, ], 'DocumentStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_FOUND', 'PROCESSING', 'INDEXED', 'UPDATED', 'FAILED', 'UPDATE_FAILED', ], ], 'DocumentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Status', ], ], 'DocumentTitle' => [ 'type' => 'string', ], 'DocumentsMetadataConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3Prefix' => [ 'shape' => 'S3ObjectKey', ], ], ], 'Domain' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^(?!-)[A-Za-z0-9-].*(?<!-)$', ], 'Duration' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '[0-9]+[s]', ], 'Endpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'EndpointType' => [ 'type' => 'string', 'enum' => [ 'HOME', ], ], 'EnterpriseId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Z0-9]*$', ], 'EntityConfiguration' => [ 'type' => 'structure', 'required' => [ 'EntityId', 'EntityType', ], 'members' => [ 'EntityId' => [ 'shape' => 'EntityId', ], 'EntityType' => [ 'shape' => 'EntityType', ], ], ], 'EntityDisplayData' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'NameType', ], 'GroupName' => [ 'shape' => 'NameType', ], 'IdentifiedUserName' => [ 'shape' => 'NameType', ], 'FirstName' => [ 'shape' => 'NameType', ], 'LastName' => [ 'shape' => 'NameType', ], ], ], 'EntityFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlfrescoEntity', ], 'max' => 3, 'min' => 1, ], 'EntityId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'EntityIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityId', ], 'max' => 25, 'min' => 1, ], 'EntityPersonaConfiguration' => [ 'type' => 'structure', 'required' => [ 'EntityId', 'Persona', ], 'members' => [ 'EntityId' => [ 'shape' => 'EntityId', ], 'Persona' => [ 'shape' => 'Persona', ], ], ], 'EntityPersonaConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityPersonaConfiguration', ], 'max' => 25, 'min' => 1, ], 'EntityType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalError', 'InvalidRequest', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'ExcludeMimeTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MimeType', ], 'max' => 30, 'min' => 0, ], 'ExcludeSharedDrivesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedDriveId', ], 'max' => 100, 'min' => 0, ], 'ExcludeUserAccountsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAccount', ], 'max' => 100, 'min' => 0, ], 'ExpandConfiguration' => [ 'type' => 'structure', 'members' => [ 'MaxResultItemsToExpand' => [ 'shape' => 'Integer', ], 'MaxExpandedResultsPerItem' => [ 'shape' => 'Integer', ], ], ], 'ExpandedResultItem' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResultId', ], 'DocumentId' => [ 'shape' => 'DocumentId', ], 'DocumentTitle' => [ 'shape' => 'TextWithHighlights', ], 'DocumentExcerpt' => [ 'shape' => 'TextWithHighlights', ], 'DocumentURI' => [ 'shape' => 'Url', ], 'DocumentAttributes' => [ 'shape' => 'DocumentAttributeList', ], ], ], 'ExpandedResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExpandedResultItem', ], ], 'ExperienceConfiguration' => [ 'type' => 'structure', 'members' => [ 'ContentSourceConfiguration' => [ 'shape' => 'ContentSourceConfiguration', ], 'UserIdentityConfiguration' => [ 'shape' => 'UserIdentityConfiguration', ], ], ], 'ExperienceEndpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointType' => [ 'shape' => 'EndpointType', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'ExperienceEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperienceEndpoint', ], 'max' => 2, 'min' => 1, ], 'ExperienceEntitiesSummary' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'EntityId', ], 'EntityType' => [ 'shape' => 'EntityType', ], 'DisplayData' => [ 'shape' => 'EntityDisplayData', ], ], ], 'ExperienceEntitiesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperienceEntitiesSummary', ], ], 'ExperienceId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ExperienceName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ExperienceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'ExperiencesSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ExperienceName', ], 'Id' => [ 'shape' => 'ExperienceId', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ExperienceStatus', ], 'Endpoints' => [ 'shape' => 'ExperienceEndpoints', ], ], ], 'ExperiencesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperiencesSummary', ], ], 'Facet' => [ 'type' => 'structure', 'members' => [ 'DocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'Facets' => [ 'shape' => 'FacetList', ], 'MaxResults' => [ 'shape' => 'TopDocumentAttributeValueCountPairsSize', ], ], ], 'FacetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Facet', ], ], 'FacetResult' => [ 'type' => 'structure', 'members' => [ 'DocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'DocumentAttributeValueType' => [ 'shape' => 'DocumentAttributeValueType', ], 'DocumentAttributeValueCountPairs' => [ 'shape' => 'DocumentAttributeValueCountPairList', ], ], ], 'FacetResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FacetResult', ], ], 'FailedEntity' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'EntityId', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'FailedEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedEntity', ], 'max' => 25, 'min' => 1, ], 'FailureReason' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'FaqFileFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'CSV_WITH_HEADER', 'JSON', ], ], 'FaqId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'FaqIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaqId', ], 'max' => 100, 'min' => 1, ], 'FaqName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'FaqStatistics' => [ 'type' => 'structure', 'required' => [ 'IndexedQuestionAnswersCount', ], 'members' => [ 'IndexedQuestionAnswersCount' => [ 'shape' => 'IndexedQuestionAnswersCount', ], ], ], 'FaqStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'FaqSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'FaqId', ], 'Name' => [ 'shape' => 'FaqName', ], 'Status' => [ 'shape' => 'FaqStatus', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'FileFormat' => [ 'shape' => 'FaqFileFormat', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], ], ], 'FaqSummaryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaqSummary', ], ], 'FeaturedDocument' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], ], ], 'FeaturedDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedDocument', ], ], 'FeaturedDocumentMissing' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], ], ], 'FeaturedDocumentMissingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedDocumentMissing', ], ], 'FeaturedDocumentWithMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentId', ], 'Title' => [ 'shape' => 'String', ], 'URI' => [ 'shape' => 'Url', ], ], ], 'FeaturedDocumentWithMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedDocumentWithMetadata', ], ], 'FeaturedResultsConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ConflictingItems' => [ 'shape' => 'ConflictingItems', ], ], 'exception' => true, ], 'FeaturedResultsItem' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResultId', ], 'Type' => [ 'shape' => 'QueryResultType', ], 'AdditionalAttributes' => [ 'shape' => 'AdditionalResultAttributeList', ], 'DocumentId' => [ 'shape' => 'DocumentId', ], 'DocumentTitle' => [ 'shape' => 'TextWithHighlights', ], 'DocumentExcerpt' => [ 'shape' => 'TextWithHighlights', ], 'DocumentURI' => [ 'shape' => 'Url', ], 'DocumentAttributes' => [ 'shape' => 'DocumentAttributeList', ], 'FeedbackToken' => [ 'shape' => 'FeedbackToken', ], ], ], 'FeaturedResultsItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedResultsItem', ], ], 'FeaturedResultsSet' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSetId' => [ 'shape' => 'FeaturedResultsSetId', ], 'FeaturedResultsSetName' => [ 'shape' => 'FeaturedResultsSetName', ], 'Description' => [ 'shape' => 'FeaturedResultsSetDescription', ], 'Status' => [ 'shape' => 'FeaturedResultsSetStatus', ], 'QueryTexts' => [ 'shape' => 'QueryTextList', ], 'FeaturedDocuments' => [ 'shape' => 'FeaturedDocumentList', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Long', ], 'CreationTimestamp' => [ 'shape' => 'Long', ], ], ], 'FeaturedResultsSetDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'FeaturedResultsSetId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z-0-9]*', ], 'FeaturedResultsSetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedResultsSetId', ], 'max' => 50, 'min' => 1, ], 'FeaturedResultsSetName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][ a-zA-Z0-9_-]*', ], 'FeaturedResultsSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'FeaturedResultsSetSummary' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSetId' => [ 'shape' => 'FeaturedResultsSetId', ], 'FeaturedResultsSetName' => [ 'shape' => 'FeaturedResultsSetName', ], 'Status' => [ 'shape' => 'FeaturedResultsSetStatus', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Long', ], 'CreationTimestamp' => [ 'shape' => 'Long', ], ], ], 'FeaturedResultsSetSummaryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturedResultsSetSummary', ], ], 'FeedbackToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*.\\P{C}*$', ], 'FileSystemId' => [ 'type' => 'string', 'max' => 21, 'min' => 11, 'pattern' => '^(fs-[0-9a-f]{8,})$', ], 'FolderId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FolderIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FolderId', ], ], 'FsxConfiguration' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'FileSystemType', 'VpcConfiguration', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'FileSystemType' => [ 'shape' => 'FsxFileSystemType', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'FsxFileSystemType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', ], ], 'GetQuerySuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'QueryText', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'QueryText' => [ 'shape' => 'SuggestionQueryText', ], 'MaxSuggestionsCount' => [ 'shape' => 'Integer', ], 'SuggestionTypes' => [ 'shape' => 'SuggestionTypes', ], 'AttributeSuggestionsConfig' => [ 'shape' => 'AttributeSuggestionsGetConfig', ], ], ], 'GetQuerySuggestionsResponse' => [ 'type' => 'structure', 'members' => [ 'QuerySuggestionsId' => [ 'shape' => 'QuerySuggestionsId', ], 'Suggestions' => [ 'shape' => 'SuggestionList', ], ], ], 'GetSnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Interval', 'MetricType', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Interval' => [ 'shape' => 'Interval', ], 'MetricType' => [ 'shape' => 'MetricType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'GetSnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'SnapShotTimeFilter' => [ 'shape' => 'TimeRange', ], 'SnapshotsDataHeader' => [ 'shape' => 'SnapshotsDataHeaderFields', ], 'SnapshotsData' => [ 'shape' => 'SnapshotsDataRecords', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GitHubConfiguration' => [ 'type' => 'structure', 'required' => [ 'SecretArn', ], 'members' => [ 'SaaSConfiguration' => [ 'shape' => 'SaaSConfiguration', ], 'OnPremiseConfiguration' => [ 'shape' => 'OnPremiseConfiguration', ], 'Type' => [ 'shape' => 'Type', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'GitHubDocumentCrawlProperties' => [ 'shape' => 'GitHubDocumentCrawlProperties', ], 'RepositoryFilter' => [ 'shape' => 'RepositoryNames', ], 'InclusionFolderNamePatterns' => [ 'shape' => 'StringList', ], 'InclusionFileTypePatterns' => [ 'shape' => 'StringList', ], 'InclusionFileNamePatterns' => [ 'shape' => 'StringList', ], 'ExclusionFolderNamePatterns' => [ 'shape' => 'StringList', ], 'ExclusionFileTypePatterns' => [ 'shape' => 'StringList', ], 'ExclusionFileNamePatterns' => [ 'shape' => 'StringList', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'GitHubRepositoryConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubCommitConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubIssueDocumentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubIssueCommentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubIssueAttachmentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubPullRequestCommentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubPullRequestDocumentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'GitHubPullRequestDocumentAttachmentConfigurationFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'GitHubDocumentCrawlProperties' => [ 'type' => 'structure', 'members' => [ 'CrawlRepositoryDocuments' => [ 'shape' => 'Boolean', ], 'CrawlIssue' => [ 'shape' => 'Boolean', ], 'CrawlIssueComment' => [ 'shape' => 'Boolean', ], 'CrawlIssueCommentAttachment' => [ 'shape' => 'Boolean', ], 'CrawlPullRequest' => [ 'shape' => 'Boolean', ], 'CrawlPullRequestComment' => [ 'shape' => 'Boolean', ], 'CrawlPullRequestCommentAttachment' => [ 'shape' => 'Boolean', ], ], ], 'GoogleDriveConfiguration' => [ 'type' => 'structure', 'required' => [ 'SecretArn', ], 'members' => [ 'SecretArn' => [ 'shape' => 'SecretArn', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'ExcludeMimeTypes' => [ 'shape' => 'ExcludeMimeTypesList', ], 'ExcludeUserAccounts' => [ 'shape' => 'ExcludeUserAccountsList', ], 'ExcludeSharedDrives' => [ 'shape' => 'ExcludeSharedDrivesList', ], ], ], 'GroupAttributeField' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'GroupId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'GroupMembers' => [ 'type' => 'structure', 'members' => [ 'MemberGroups' => [ 'shape' => 'MemberGroups', ], 'MemberUsers' => [ 'shape' => 'MemberUsers', ], 'S3PathforGroupMembers' => [ 'shape' => 'S3Path', ], ], ], 'GroupOrderingIdSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupOrderingIdSummary', ], 'max' => 10, ], 'GroupOrderingIdSummary' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PrincipalMappingStatus', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ReceivedAt' => [ 'shape' => 'Timestamp', ], 'OrderingId' => [ 'shape' => 'PrincipalOrderingId', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'GroupSummary' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'GroupId', ], 'OrderingId' => [ 'shape' => 'PrincipalOrderingId', ], ], ], 'Groups' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalName', ], 'max' => 2048, 'min' => 1, ], 'HierarchicalPrincipal' => [ 'type' => 'structure', 'required' => [ 'PrincipalList', ], 'members' => [ 'PrincipalList' => [ 'shape' => 'PrincipalList', ], ], ], 'HierarchicalPrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchicalPrincipal', ], 'max' => 30, 'min' => 1, ], 'Highlight' => [ 'type' => 'structure', 'required' => [ 'BeginOffset', 'EndOffset', ], 'members' => [ 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], 'TopAnswer' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'HighlightType', ], ], ], 'HighlightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Highlight', ], ], 'HighlightType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'THESAURUS_SYNONYM', ], ], 'HookConfiguration' => [ 'type' => 'structure', 'required' => [ 'LambdaArn', 'S3Bucket', ], 'members' => [ 'InvocationCondition' => [ 'shape' => 'DocumentAttributeCondition', ], 'LambdaArn' => [ 'shape' => 'LambdaArn', ], 'S3Bucket' => [ 'shape' => 'S3BucketName', ], ], ], 'Host' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '([^\\s]*)', ], 'IdentityAttributeName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'Importance' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'IndexConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'CreatedAt', 'UpdatedAt', 'Status', ], 'members' => [ 'Name' => [ 'shape' => 'IndexName', ], 'Id' => [ 'shape' => 'IndexId', ], 'Edition' => [ 'shape' => 'IndexEdition', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'IndexStatus', ], ], ], 'IndexConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IndexConfigurationSummary', ], ], 'IndexEdition' => [ 'type' => 'string', 'enum' => [ 'DEVELOPER_EDITION', 'ENTERPRISE_EDITION', ], ], 'IndexFieldName' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'IndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]*', ], 'IndexName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'IndexStatistics' => [ 'type' => 'structure', 'required' => [ 'FaqStatistics', 'TextDocumentStatistics', ], 'members' => [ 'FaqStatistics' => [ 'shape' => 'FaqStatistics', ], 'TextDocumentStatistics' => [ 'shape' => 'TextDocumentStatistics', ], ], ], 'IndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', 'SYSTEM_UPDATING', ], ], 'IndexedQuestionAnswersCount' => [ 'type' => 'integer', 'min' => 0, ], 'IndexedTextBytes' => [ 'type' => 'long', 'min' => 0, ], 'IndexedTextDocumentsCount' => [ 'type' => 'integer', 'min' => 0, ], 'InlineCustomDocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'DocumentAttributeCondition', ], 'Target' => [ 'shape' => 'DocumentAttributeTarget', ], 'DocumentContentDeletion' => [ 'shape' => 'Boolean', ], ], ], 'InlineCustomDocumentEnrichmentConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InlineCustomDocumentEnrichmentConfiguration', ], 'max' => 100, 'min' => 0, ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'Interval' => [ 'type' => 'string', 'enum' => [ 'THIS_MONTH', 'THIS_WEEK', 'ONE_WEEK_AGO', 'TWO_WEEKS_AGO', 'ONE_MONTH_AGO', 'TWO_MONTHS_AGO', ], ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IssueSubEntity' => [ 'type' => 'string', 'enum' => [ 'COMMENTS', 'ATTACHMENTS', 'WORKLOGS', ], ], 'IssueSubEntityFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'IssueSubEntity', ], 'max' => 3, 'min' => 0, ], 'IssueType' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Issuer' => [ 'type' => 'string', 'max' => 65, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'JiraAccountUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^https:\\/\\/[a-zA-Z0-9_\\-\\.]+(\\.atlassian\\.net\\/)$', ], 'JiraConfiguration' => [ 'type' => 'structure', 'required' => [ 'JiraAccountUrl', 'SecretArn', ], 'members' => [ 'JiraAccountUrl' => [ 'shape' => 'JiraAccountUrl', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'Project' => [ 'shape' => 'Project', ], 'IssueType' => [ 'shape' => 'IssueType', ], 'Status' => [ 'shape' => 'JiraStatus', ], 'IssueSubEntityFilter' => [ 'shape' => 'IssueSubEntityFilter', ], 'AttachmentFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'CommentFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'IssueFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'ProjectFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'WorkLogFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'JiraStatus' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'JsonTokenTypeConfiguration' => [ 'type' => 'structure', 'required' => [ 'UserNameAttributeField', 'GroupAttributeField', ], 'members' => [ 'UserNameAttributeField' => [ 'shape' => 'String', ], 'GroupAttributeField' => [ 'shape' => 'String', ], ], ], 'JwtTokenTypeConfiguration' => [ 'type' => 'structure', 'required' => [ 'KeyLocation', ], 'members' => [ 'KeyLocation' => [ 'shape' => 'KeyLocation', ], 'URL' => [ 'shape' => 'Url', ], 'SecretManagerArn' => [ 'shape' => 'RoleArn', ], 'UserNameAttributeField' => [ 'shape' => 'UserNameAttributeField', ], 'GroupAttributeField' => [ 'shape' => 'GroupAttributeField', ], 'Issuer' => [ 'shape' => 'Issuer', ], 'ClaimRegex' => [ 'shape' => 'ClaimRegex', ], ], ], 'KeyLocation' => [ 'type' => 'string', 'enum' => [ 'URL', 'SECRET_MANAGER', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '/arn:aws[a-zA-Z-]*:lambda:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(\\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?/', ], 'LanguageCode' => [ 'type' => 'string', 'max' => 10, 'min' => 2, 'pattern' => '[a-zA-Z-]*', ], 'ListAccessControlConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListAccessControlConfigurationsRequest', ], ], ], 'ListAccessControlConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'AccessControlConfigurations', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'AccessControlConfigurations' => [ 'shape' => 'AccessControlConfigurationSummaryList', ], ], ], 'ListDataSourceSyncJobsRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSourceSyncJobsRequest', ], 'StartTimeFilter' => [ 'shape' => 'TimeRange', ], 'StatusFilter' => [ 'shape' => 'DataSourceSyncJobStatus', ], ], ], 'ListDataSourceSyncJobsResponse' => [ 'type' => 'structure', 'members' => [ 'History' => [ 'shape' => 'DataSourceSyncJobHistoryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSourcesRequest', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryItems' => [ 'shape' => 'DataSourceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEntityPersonasRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListEntityPersonasRequest', ], ], ], 'ListEntityPersonasResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryItems' => [ 'shape' => 'PersonasSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperienceEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperienceEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryItems' => [ 'shape' => 'ExperienceEntitiesSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperiencesRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListExperiencesRequest', ], ], ], 'ListExperiencesResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryItems' => [ 'shape' => 'ExperiencesSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFaqsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListFaqsRequest', ], ], ], 'ListFaqsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FaqSummaryItems' => [ 'shape' => 'FaqSummaryItems', ], ], ], 'ListFeaturedResultsSetsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListFeaturedResultsSetsRequest', ], ], ], 'ListFeaturedResultsSetsResponse' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSetSummaryItems' => [ 'shape' => 'FeaturedResultsSetSummaryItems', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsOlderThanOrderingIdRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'OrderingId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'OrderingId' => [ 'shape' => 'PrincipalOrderingId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListPrincipalsRequest', ], ], ], 'ListGroupsOlderThanOrderingIdResponse' => [ 'type' => 'structure', 'members' => [ 'GroupsSummaries' => [ 'shape' => 'ListOfGroupSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIndicesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListIndicesRequest', ], ], ], 'ListIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'IndexConfigurationSummaryItems' => [ 'shape' => 'IndexConfigurationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOfGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupSummary', ], ], 'ListQuerySuggestionsBlockListsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListQuerySuggestionsBlockLists', ], ], ], 'ListQuerySuggestionsBlockListsResponse' => [ 'type' => 'structure', 'members' => [ 'BlockListSummaryItems' => [ 'shape' => 'QuerySuggestionsBlockListSummaryItems', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListThesauriRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResultsIntegerForListThesauriRequest', ], ], ], 'ListThesauriResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ThesaurusSummaryItems' => [ 'shape' => 'ThesaurusSummaryItems', ], ], ], 'Long' => [ 'type' => 'long', ], 'LookBackPeriod' => [ 'type' => 'integer', 'max' => 168, 'min' => 0, ], 'MaxContentSizePerPageInMegaBytes' => [ 'type' => 'float', 'max' => 50, 'min' => 1.0E-6, ], 'MaxLinksPerPage' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxResultsIntegerForListAccessControlConfigurationsRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListDataSourceSyncJobsRequest' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListDataSourcesRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListEntityPersonasRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListExperiencesRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListFaqsRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListFeaturedResultsSetsRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListIndicesRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListPrincipalsRequest' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListQuerySuggestionsBlockLists' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListThesauriRequest' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxUrlsPerMinuteCrawlRate' => [ 'type' => 'integer', 'max' => 300, 'min' => 1, ], 'MemberGroup' => [ 'type' => 'structure', 'required' => [ 'GroupId', ], 'members' => [ 'GroupId' => [ 'shape' => 'GroupId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'MemberGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberGroup', ], 'max' => 1000, 'min' => 1, ], 'MemberUser' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], ], ], 'MemberUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberUser', ], 'max' => 1000, 'min' => 1, ], 'MetricType' => [ 'type' => 'string', 'enum' => [ 'QUERIES_BY_COUNT', 'QUERIES_BY_ZERO_CLICK_RATE', 'QUERIES_BY_ZERO_RESULT_RATE', 'DOCS_BY_CLICK_COUNT', 'AGG_QUERY_DOC_METRICS', 'TREND_QUERY_DOC_METRICS', ], ], 'MetricValue' => [ 'type' => 'string', 'pattern' => '(([1-9][0-9]*)|0)', ], 'MimeType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'MinimumNumberOfQueryingUsers' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'MinimumQueryCount' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'MissingAttributeKeyStrategy' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'COLLAPSE', 'EXPAND', ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'LEARN_ONLY', ], ], 'NameType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\S\\s]*$', 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 800, 'min' => 1, ], 'ObjectBoolean' => [ 'type' => 'boolean', ], 'OnPremiseConfiguration' => [ 'type' => 'structure', 'required' => [ 'HostUrl', 'OrganizationName', 'SslCertificateS3Path', ], 'members' => [ 'HostUrl' => [ 'shape' => 'Url', ], 'OrganizationName' => [ 'shape' => 'OrganizationName', ], 'SslCertificateS3Path' => [ 'shape' => 'S3Path', ], ], ], 'OneDriveConfiguration' => [ 'type' => 'structure', 'required' => [ 'TenantDomain', 'SecretArn', 'OneDriveUsers', ], 'members' => [ 'TenantDomain' => [ 'shape' => 'TenantDomain', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'OneDriveUsers' => [ 'shape' => 'OneDriveUsers', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'DisableLocalGroups' => [ 'shape' => 'Boolean', ], ], ], 'OneDriveUser' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(?!\\s).+@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5})$', ], 'OneDriveUserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OneDriveUser', ], 'max' => 100, 'min' => 1, ], 'OneDriveUsers' => [ 'type' => 'structure', 'members' => [ 'OneDriveUserList' => [ 'shape' => 'OneDriveUserList', ], 'OneDriveUserS3Path' => [ 'shape' => 'S3Path', ], ], ], 'Order' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'OrganizationId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => 'd-[0-9a-fA-F]{10}', ], 'OrganizationName' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '^[A-Za-z0-9_.-]+$', ], 'Persona' => [ 'type' => 'string', 'enum' => [ 'OWNER', 'VIEWER', ], ], 'PersonasSummary' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'EntityId', ], 'Persona' => [ 'shape' => 'Persona', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'PersonasSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PersonasSummary', ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'Principal' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'Access', ], 'members' => [ 'Name' => [ 'shape' => 'PrincipalName', ], 'Type' => [ 'shape' => 'PrincipalType', ], 'Access' => [ 'shape' => 'ReadAccessType', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'PrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PrincipalMappingStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'PROCESSING', 'DELETING', 'DELETED', ], ], 'PrincipalName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'PrincipalOrderingId' => [ 'type' => 'long', 'max' => 32535158400000, 'min' => 0, ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'PrivateChannelFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Project' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ProxyConfiguration' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Credentials' => [ 'shape' => 'SecretArn', ], ], ], 'PublicChannelFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'PutPrincipalMappingRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'GroupId', 'GroupMembers', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'DataSourceId' => [ 'shape' => 'DataSourceId', ], 'GroupId' => [ 'shape' => 'GroupId', ], 'GroupMembers' => [ 'shape' => 'GroupMembers', ], 'OrderingId' => [ 'shape' => 'PrincipalOrderingId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'QueryCapacityUnit' => [ 'type' => 'integer', 'min' => 0, ], 'QueryId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]*', ], 'QueryIdentifiersEnclosingOption' => [ 'type' => 'string', 'enum' => [ 'DOUBLE_QUOTES', 'NONE', ], ], 'QueryRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'QueryText' => [ 'shape' => 'QueryText', ], 'AttributeFilter' => [ 'shape' => 'AttributeFilter', ], 'Facets' => [ 'shape' => 'FacetList', ], 'RequestedDocumentAttributes' => [ 'shape' => 'DocumentAttributeKeyList', ], 'QueryResultTypeFilter' => [ 'shape' => 'QueryResultType', ], 'DocumentRelevanceOverrideConfigurations' => [ 'shape' => 'DocumentRelevanceOverrideConfigurationList', ], 'PageNumber' => [ 'shape' => 'Integer', ], 'PageSize' => [ 'shape' => 'Integer', ], 'SortingConfiguration' => [ 'shape' => 'SortingConfiguration', ], 'SortingConfigurations' => [ 'shape' => 'SortingConfigurationList', ], 'UserContext' => [ 'shape' => 'UserContext', ], 'VisitorId' => [ 'shape' => 'VisitorId', ], 'SpellCorrectionConfiguration' => [ 'shape' => 'SpellCorrectionConfiguration', ], 'CollapseConfiguration' => [ 'shape' => 'CollapseConfiguration', ], ], ], 'QueryResult' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'QueryId', ], 'ResultItems' => [ 'shape' => 'QueryResultItemList', ], 'FacetResults' => [ 'shape' => 'FacetResultList', ], 'TotalNumberOfResults' => [ 'shape' => 'Integer', ], 'Warnings' => [ 'shape' => 'WarningList', ], 'SpellCorrectedQueries' => [ 'shape' => 'SpellCorrectedQueryList', ], 'FeaturedResultsItems' => [ 'shape' => 'FeaturedResultsItemList', ], ], ], 'QueryResultFormat' => [ 'type' => 'string', 'enum' => [ 'TABLE', 'TEXT', ], ], 'QueryResultItem' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResultId', ], 'Type' => [ 'shape' => 'QueryResultType', ], 'Format' => [ 'shape' => 'QueryResultFormat', ], 'AdditionalAttributes' => [ 'shape' => 'AdditionalResultAttributeList', ], 'DocumentId' => [ 'shape' => 'DocumentId', ], 'DocumentTitle' => [ 'shape' => 'TextWithHighlights', ], 'DocumentExcerpt' => [ 'shape' => 'TextWithHighlights', ], 'DocumentURI' => [ 'shape' => 'Url', ], 'DocumentAttributes' => [ 'shape' => 'DocumentAttributeList', ], 'ScoreAttributes' => [ 'shape' => 'ScoreAttributes', ], 'FeedbackToken' => [ 'shape' => 'FeedbackToken', ], 'TableExcerpt' => [ 'shape' => 'TableExcerpt', ], 'CollapsedResultDetail' => [ 'shape' => 'CollapsedResultDetail', ], ], ], 'QueryResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryResultItem', ], ], 'QueryResultType' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT', 'QUESTION_ANSWER', 'ANSWER', ], ], 'QuerySuggestionsBlockListId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-]*', ], 'QuerySuggestionsBlockListName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'QuerySuggestionsBlockListStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'DELETING', 'UPDATING', 'ACTIVE_BUT_UPDATE_FAILED', 'FAILED', ], ], 'QuerySuggestionsBlockListSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], 'Name' => [ 'shape' => 'QuerySuggestionsBlockListName', ], 'Status' => [ 'shape' => 'QuerySuggestionsBlockListStatus', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'ItemCount' => [ 'shape' => 'Integer', ], ], ], 'QuerySuggestionsBlockListSummaryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuerySuggestionsBlockListSummary', ], ], 'QuerySuggestionsId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'QuerySuggestionsStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'UPDATING', ], ], 'QueryText' => [ 'type' => 'string', ], 'QueryTextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryText', ], 'max' => 49, 'min' => 0, ], 'QuipConfiguration' => [ 'type' => 'structure', 'required' => [ 'Domain', 'SecretArn', ], 'members' => [ 'Domain' => [ 'shape' => 'Domain', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'CrawlFileComments' => [ 'shape' => 'Boolean', ], 'CrawlChatRooms' => [ 'shape' => 'Boolean', ], 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'FolderIds' => [ 'shape' => 'FolderIdList', ], 'ThreadFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'MessageFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'AttachmentFieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'ReadAccessType' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'Relevance' => [ 'type' => 'structure', 'members' => [ 'Freshness' => [ 'shape' => 'DocumentMetadataBoolean', ], 'Importance' => [ 'shape' => 'Importance', ], 'Duration' => [ 'shape' => 'Duration', ], 'RankOrder' => [ 'shape' => 'Order', ], 'ValueImportanceMap' => [ 'shape' => 'ValueImportanceMap', ], ], ], 'RelevanceFeedback' => [ 'type' => 'structure', 'required' => [ 'ResultId', 'RelevanceValue', ], 'members' => [ 'ResultId' => [ 'shape' => 'ResultId', ], 'RelevanceValue' => [ 'shape' => 'RelevanceType', ], ], ], 'RelevanceFeedbackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelevanceFeedback', ], ], 'RelevanceType' => [ 'type' => 'string', 'enum' => [ 'RELEVANT', 'NOT_RELEVANT', ], ], 'RepositoryName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Za-z0-9_.-]+$', ], 'RepositoryNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'ResourceAlreadyExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResultId' => [ 'type' => 'string', 'max' => 73, 'min' => 1, ], 'RetrieveRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'QueryText', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'QueryText' => [ 'shape' => 'QueryText', ], 'AttributeFilter' => [ 'shape' => 'AttributeFilter', ], 'RequestedDocumentAttributes' => [ 'shape' => 'DocumentAttributeKeyList', ], 'DocumentRelevanceOverrideConfigurations' => [ 'shape' => 'DocumentRelevanceOverrideConfigurationList', ], 'PageNumber' => [ 'shape' => 'Integer', ], 'PageSize' => [ 'shape' => 'Integer', ], 'UserContext' => [ 'shape' => 'UserContext', ], ], ], 'RetrieveResult' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'QueryId', ], 'ResultItems' => [ 'shape' => 'RetrieveResultItemList', ], ], ], 'RetrieveResultItem' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResultId', ], 'DocumentId' => [ 'shape' => 'DocumentId', ], 'DocumentTitle' => [ 'shape' => 'DocumentTitle', ], 'Content' => [ 'shape' => 'Content', ], 'DocumentURI' => [ 'shape' => 'Url', ], 'DocumentAttributes' => [ 'shape' => 'DocumentAttributeList', ], 'ScoreAttributes' => [ 'shape' => 'ScoreAttributes', ], ], ], 'RetrieveResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrieveResultItem', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]', ], 'S3DataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'BucketName', ], 'members' => [ 'BucketName' => [ 'shape' => 'S3BucketName', ], 'InclusionPrefixes' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'DocumentsMetadataConfiguration' => [ 'shape' => 'DocumentsMetadataConfiguration', ], 'AccessControlListConfiguration' => [ 'shape' => 'AccessControlListConfiguration', ], ], ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Path' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'Key', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3BucketName', ], 'Key' => [ 'shape' => 'S3ObjectKey', ], ], ], 'SaaSConfiguration' => [ 'type' => 'structure', 'required' => [ 'OrganizationName', 'HostUrl', ], 'members' => [ 'OrganizationName' => [ 'shape' => 'OrganizationName', ], 'HostUrl' => [ 'shape' => 'Url', ], ], ], 'SalesforceChatterFeedConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentDataFieldName', ], 'members' => [ 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'IncludeFilterTypes' => [ 'shape' => 'SalesforceChatterFeedIncludeFilterTypes', ], ], ], 'SalesforceChatterFeedIncludeFilterType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE_USER', 'STANDARD_USER', ], ], 'SalesforceChatterFeedIncludeFilterTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesforceChatterFeedIncludeFilterType', ], 'max' => 2, 'min' => 1, ], 'SalesforceConfiguration' => [ 'type' => 'structure', 'required' => [ 'ServerUrl', 'SecretArn', ], 'members' => [ 'ServerUrl' => [ 'shape' => 'Url', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'StandardObjectConfigurations' => [ 'shape' => 'SalesforceStandardObjectConfigurationList', ], 'KnowledgeArticleConfiguration' => [ 'shape' => 'SalesforceKnowledgeArticleConfiguration', ], 'ChatterFeedConfiguration' => [ 'shape' => 'SalesforceChatterFeedConfiguration', ], 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'StandardObjectAttachmentConfiguration' => [ 'shape' => 'SalesforceStandardObjectAttachmentConfiguration', ], 'IncludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExcludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], ], ], 'SalesforceCustomKnowledgeArticleTypeConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'DocumentDataFieldName', ], 'members' => [ 'Name' => [ 'shape' => 'SalesforceCustomKnowledgeArticleTypeName', ], 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'SalesforceCustomKnowledgeArticleTypeConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesforceCustomKnowledgeArticleTypeConfiguration', ], 'max' => 10, 'min' => 1, ], 'SalesforceCustomKnowledgeArticleTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'SalesforceKnowledgeArticleConfiguration' => [ 'type' => 'structure', 'required' => [ 'IncludedStates', ], 'members' => [ 'IncludedStates' => [ 'shape' => 'SalesforceKnowledgeArticleStateList', ], 'StandardKnowledgeArticleTypeConfiguration' => [ 'shape' => 'SalesforceStandardKnowledgeArticleTypeConfiguration', ], 'CustomKnowledgeArticleTypeConfigurations' => [ 'shape' => 'SalesforceCustomKnowledgeArticleTypeConfigurationList', ], ], ], 'SalesforceKnowledgeArticleState' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'PUBLISHED', 'ARCHIVED', ], ], 'SalesforceKnowledgeArticleStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesforceKnowledgeArticleState', ], 'max' => 3, 'min' => 1, ], 'SalesforceStandardKnowledgeArticleTypeConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentDataFieldName', ], 'members' => [ 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'SalesforceStandardObjectAttachmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'SalesforceStandardObjectConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'DocumentDataFieldName', ], 'members' => [ 'Name' => [ 'shape' => 'SalesforceStandardObjectName', ], 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'SalesforceStandardObjectConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesforceStandardObjectConfiguration', ], 'max' => 17, 'min' => 1, ], 'SalesforceStandardObjectName' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'CAMPAIGN', 'CASE', 'CONTACT', 'CONTRACT', 'DOCUMENT', 'GROUP', 'IDEA', 'LEAD', 'OPPORTUNITY', 'PARTNER', 'PRICEBOOK', 'PRODUCT', 'PROFILE', 'SOLUTION', 'TASK', 'USER', ], ], 'ScanSchedule' => [ 'type' => 'string', ], 'ScoreAttributes' => [ 'type' => 'structure', 'members' => [ 'ScoreConfidence' => [ 'shape' => 'ScoreConfidence', ], ], ], 'ScoreConfidence' => [ 'type' => 'string', 'enum' => [ 'VERY_HIGH', 'HIGH', 'MEDIUM', 'LOW', 'NOT_AVAILABLE', ], ], 'Search' => [ 'type' => 'structure', 'members' => [ 'Facetable' => [ 'shape' => 'Boolean', ], 'Searchable' => [ 'shape' => 'Boolean', ], 'Displayable' => [ 'shape' => 'Boolean', ], 'Sortable' => [ 'shape' => 'Boolean', ], ], ], 'SecretArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 1, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'SeedUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(https?):\\/\\/([^\\s]*)', ], 'SeedUrlConfiguration' => [ 'type' => 'structure', 'required' => [ 'SeedUrls', ], 'members' => [ 'SeedUrls' => [ 'shape' => 'SeedUrlList', ], 'WebCrawlerMode' => [ 'shape' => 'WebCrawlerMode', ], ], ], 'SeedUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SeedUrl', ], 'max' => 100, 'min' => 0, ], 'ServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ServiceNowAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'HTTP_BASIC', 'OAUTH2', ], ], 'ServiceNowBuildVersionType' => [ 'type' => 'string', 'enum' => [ 'LONDON', 'OTHERS', ], ], 'ServiceNowConfiguration' => [ 'type' => 'structure', 'required' => [ 'HostUrl', 'SecretArn', 'ServiceNowBuildVersion', ], 'members' => [ 'HostUrl' => [ 'shape' => 'ServiceNowHostUrl', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'ServiceNowBuildVersion' => [ 'shape' => 'ServiceNowBuildVersionType', ], 'KnowledgeArticleConfiguration' => [ 'shape' => 'ServiceNowKnowledgeArticleConfiguration', ], 'ServiceCatalogConfiguration' => [ 'shape' => 'ServiceNowServiceCatalogConfiguration', ], 'AuthenticationType' => [ 'shape' => 'ServiceNowAuthenticationType', ], ], ], 'ServiceNowHostUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(?!(^(https?|ftp|file):\\/\\/))[a-z0-9-]+(\\.service-now\\.com)$', ], 'ServiceNowKnowledgeArticleConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentDataFieldName', ], 'members' => [ 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'IncludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExcludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'FilterQuery' => [ 'shape' => 'ServiceNowKnowledgeArticleFilterQuery', ], ], ], 'ServiceNowKnowledgeArticleFilterQuery' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'ServiceNowServiceCatalogConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentDataFieldName', ], 'members' => [ 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'IncludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExcludeAttachmentFilePatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'DocumentDataFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'SharePointConfiguration' => [ 'type' => 'structure', 'required' => [ 'SharePointVersion', 'Urls', 'SecretArn', ], 'members' => [ 'SharePointVersion' => [ 'shape' => 'SharePointVersion', ], 'Urls' => [ 'shape' => 'SharePointUrlList', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'CrawlAttachments' => [ 'shape' => 'Boolean', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], 'DocumentTitleFieldName' => [ 'shape' => 'DataSourceFieldName', ], 'DisableLocalGroups' => [ 'shape' => 'Boolean', ], 'SslCertificateS3Path' => [ 'shape' => 'S3Path', ], 'AuthenticationType' => [ 'shape' => 'SharePointOnlineAuthenticationType', ], 'ProxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], ], ], 'SharePointOnlineAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'HTTP_BASIC', 'OAUTH2', ], ], 'SharePointUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Url', ], 'max' => 100, 'min' => 1, ], 'SharePointVersion' => [ 'type' => 'string', 'enum' => [ 'SHAREPOINT_2013', 'SHAREPOINT_2016', 'SHAREPOINT_ONLINE', 'SHAREPOINT_2019', ], ], 'SharedDriveId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'SinceCrawlDate' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '(20\\d{2})-(0?[1-9]|1[0-2])-(0?[1-9]|1\\d|2\\d|3[01])', ], 'SiteId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9-]+$', ], 'SiteMap' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(https?):\\/\\/([^\\s]*)', ], 'SiteMapsConfiguration' => [ 'type' => 'structure', 'required' => [ 'SiteMaps', ], 'members' => [ 'SiteMaps' => [ 'shape' => 'SiteMapsList', ], ], ], 'SiteMapsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SiteMap', ], 'max' => 3, 'min' => 0, ], 'SiteUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^https:\\/\\/[a-zA-Z0-9_\\-\\.]+$', ], 'SlackConfiguration' => [ 'type' => 'structure', 'required' => [ 'TeamId', 'SecretArn', 'SlackEntityList', 'SinceCrawlDate', ], 'members' => [ 'TeamId' => [ 'shape' => 'TeamId', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'SlackEntityList' => [ 'shape' => 'SlackEntityList', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'CrawlBotMessage' => [ 'shape' => 'Boolean', ], 'ExcludeArchived' => [ 'shape' => 'Boolean', ], 'SinceCrawlDate' => [ 'shape' => 'SinceCrawlDate', ], 'LookBackPeriod' => [ 'shape' => 'LookBackPeriod', ], 'PrivateChannelFilter' => [ 'shape' => 'PrivateChannelFilter', ], 'PublicChannelFilter' => [ 'shape' => 'PublicChannelFilter', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], 'SlackEntity' => [ 'type' => 'string', 'enum' => [ 'PUBLIC_CHANNEL', 'PRIVATE_CHANNEL', 'GROUP_MESSAGE', 'DIRECT_MESSAGE', ], ], 'SlackEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackEntity', ], 'max' => 4, 'min' => 1, ], 'SnapshotsDataHeaderFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SnapshotsDataRecord' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SnapshotsDataRecords' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotsDataRecord', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'DESC', 'ASC', ], ], 'SortingConfiguration' => [ 'type' => 'structure', 'required' => [ 'DocumentAttributeKey', 'SortOrder', ], 'members' => [ 'DocumentAttributeKey' => [ 'shape' => 'DocumentAttributeKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortingConfiguration', ], 'min' => 1, ], 'SourceDocument' => [ 'type' => 'structure', 'members' => [ 'DocumentId' => [ 'shape' => 'String', ], 'SuggestionAttributes' => [ 'shape' => 'DocumentAttributeKeyList', ], 'AdditionalAttributes' => [ 'shape' => 'DocumentAttributeList', ], ], ], 'SourceDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceDocument', ], ], 'SpellCorrectedQuery' => [ 'type' => 'structure', 'members' => [ 'SuggestedQueryText' => [ 'shape' => 'SuggestedQueryText', ], 'Corrections' => [ 'shape' => 'CorrectionList', ], ], ], 'SpellCorrectedQueryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SpellCorrectedQuery', ], ], 'SpellCorrectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'IncludeQuerySpellCheckSuggestions', ], 'members' => [ 'IncludeQuerySpellCheckSuggestions' => [ 'shape' => 'Boolean', ], ], ], 'SqlConfiguration' => [ 'type' => 'structure', 'members' => [ 'QueryIdentifiersEnclosingOption' => [ 'shape' => 'QueryIdentifiersEnclosingOption', ], ], ], 'StartDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'StartDataSourceSyncJobResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionId' => [ 'shape' => 'String', ], ], ], 'Status' => [ 'type' => 'structure', 'members' => [ 'DocumentId' => [ 'shape' => 'DocumentId', ], 'DocumentStatus' => [ 'shape' => 'DocumentStatus', ], 'FailureCode' => [ 'shape' => 'String', ], 'FailureReason' => [ 'shape' => 'String', ], ], ], 'StopDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'IndexId' => [ 'shape' => 'IndexId', ], ], ], 'StorageCapacityUnit' => [ 'type' => 'integer', 'min' => 0, ], 'String' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubmitFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'QueryId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'QueryId' => [ 'shape' => 'QueryId', ], 'ClickFeedbackItems' => [ 'shape' => 'ClickFeedbackList', ], 'RelevanceFeedbackItems' => [ 'shape' => 'RelevanceFeedbackList', ], ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\-0-9a-zA-Z]+', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 6, 'min' => 1, ], 'SuggestableConfig' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'DocumentAttributeKey', ], 'Suggestable' => [ 'shape' => 'ObjectBoolean', ], ], ], 'SuggestableConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestableConfig', ], ], 'SuggestedQueryText' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Suggestion' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResultId', ], 'Value' => [ 'shape' => 'SuggestionValue', ], 'SourceDocuments' => [ 'shape' => 'SourceDocuments', ], ], ], 'SuggestionHighlight' => [ 'type' => 'structure', 'members' => [ 'BeginOffset' => [ 'shape' => 'Integer', ], 'EndOffset' => [ 'shape' => 'Integer', ], ], ], 'SuggestionHighlightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestionHighlight', ], ], 'SuggestionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Suggestion', ], ], 'SuggestionQueryText' => [ 'type' => 'string', 'pattern' => '^\\P{C}*$', ], 'SuggestionTextWithHighlights' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'String', ], 'Highlights' => [ 'shape' => 'SuggestionHighlightList', ], ], ], 'SuggestionType' => [ 'type' => 'string', 'enum' => [ 'QUERY', 'DOCUMENT_ATTRIBUTES', ], ], 'SuggestionTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestionType', ], ], 'SuggestionValue' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'SuggestionTextWithHighlights', ], ], ], 'TableCell' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], 'TopAnswer' => [ 'shape' => 'Boolean', ], 'Highlighted' => [ 'shape' => 'Boolean', ], 'Header' => [ 'shape' => 'Boolean', ], ], ], 'TableCellList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableCell', ], ], 'TableExcerpt' => [ 'type' => 'structure', 'members' => [ 'Rows' => [ 'shape' => 'TableRowList', ], 'TotalNumberOfRows' => [ 'shape' => 'Integer', ], ], ], 'TableName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'TableRow' => [ 'type' => 'structure', 'members' => [ 'Cells' => [ 'shape' => 'TableCellList', ], ], ], 'TableRowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableRow', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TeamId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Z0-9]*', ], 'Template' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'TemplateConfiguration' => [ 'type' => 'structure', 'members' => [ 'Template' => [ 'shape' => 'Template', ], ], ], 'TenantDomain' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\\.)+[a-z]{2,}$', ], 'TextDocumentStatistics' => [ 'type' => 'structure', 'required' => [ 'IndexedTextDocumentsCount', 'IndexedTextBytes', ], 'members' => [ 'IndexedTextDocumentsCount' => [ 'shape' => 'IndexedTextDocumentsCount', ], 'IndexedTextBytes' => [ 'shape' => 'IndexedTextBytes', ], ], ], 'TextWithHighlights' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'String', ], 'Highlights' => [ 'shape' => 'HighlightList', ], ], ], 'ThesaurusId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ThesaurusName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ThesaurusStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'UPDATING', 'ACTIVE_BUT_UPDATE_FAILED', 'FAILED', ], ], 'ThesaurusSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], 'Name' => [ 'shape' => 'ThesaurusName', ], 'Status' => [ 'shape' => 'ThesaurusStatus', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ThesaurusSummaryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThesaurusSummary', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TimeRange' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Title' => [ 'type' => 'string', ], 'Token' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'TopDocumentAttributeValueCountPairsSize' => [ 'type' => 'integer', 'max' => 5000, 'min' => 0, ], 'Type' => [ 'type' => 'string', 'enum' => [ 'SAAS', 'ON_PREMISE', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccessControlConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'AccessControlConfigurationId', ], 'Name' => [ 'shape' => 'AccessControlConfigurationName', ], 'Description' => [ 'shape' => 'Description', ], 'AccessControlList' => [ 'shape' => 'PrincipalList', ], 'HierarchicalAccessControlList' => [ 'shape' => 'HierarchicalPrincipalList', ], ], ], 'UpdateAccessControlConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'DataSourceId', ], 'Name' => [ 'shape' => 'DataSourceName', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'VpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], 'Description' => [ 'shape' => 'Description', ], 'Schedule' => [ 'shape' => 'ScanSchedule', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'CustomDocumentEnrichmentConfiguration' => [ 'shape' => 'CustomDocumentEnrichmentConfiguration', ], ], ], 'UpdateExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ExperienceId', ], 'Name' => [ 'shape' => 'ExperienceName', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Configuration' => [ 'shape' => 'ExperienceConfiguration', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateFeaturedResultsSetRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'FeaturedResultsSetId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'FeaturedResultsSetId' => [ 'shape' => 'FeaturedResultsSetId', ], 'FeaturedResultsSetName' => [ 'shape' => 'FeaturedResultsSetName', ], 'Description' => [ 'shape' => 'FeaturedResultsSetDescription', ], 'Status' => [ 'shape' => 'FeaturedResultsSetStatus', ], 'QueryTexts' => [ 'shape' => 'QueryTextList', ], 'FeaturedDocuments' => [ 'shape' => 'FeaturedDocumentList', ], ], ], 'UpdateFeaturedResultsSetResponse' => [ 'type' => 'structure', 'members' => [ 'FeaturedResultsSet' => [ 'shape' => 'FeaturedResultsSet', ], ], ], 'UpdateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'IndexId', ], 'Name' => [ 'shape' => 'IndexName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Description' => [ 'shape' => 'Description', ], 'DocumentMetadataConfigurationUpdates' => [ 'shape' => 'DocumentMetadataConfigurationList', ], 'CapacityUnits' => [ 'shape' => 'CapacityUnitsConfiguration', ], 'UserTokenConfigurations' => [ 'shape' => 'UserTokenConfigurationList', ], 'UserContextPolicy' => [ 'shape' => 'UserContextPolicy', ], 'UserGroupResolutionConfiguration' => [ 'shape' => 'UserGroupResolutionConfiguration', ], ], ], 'UpdateQuerySuggestionsBlockListRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', 'Id', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Id' => [ 'shape' => 'QuerySuggestionsBlockListId', ], 'Name' => [ 'shape' => 'QuerySuggestionsBlockListName', ], 'Description' => [ 'shape' => 'Description', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateQuerySuggestionsConfigRequest' => [ 'type' => 'structure', 'required' => [ 'IndexId', ], 'members' => [ 'IndexId' => [ 'shape' => 'IndexId', ], 'Mode' => [ 'shape' => 'Mode', ], 'QueryLogLookBackWindowInDays' => [ 'shape' => 'Integer', ], 'IncludeQueriesWithoutUserInformation' => [ 'shape' => 'ObjectBoolean', ], 'MinimumNumberOfQueryingUsers' => [ 'shape' => 'MinimumNumberOfQueryingUsers', ], 'MinimumQueryCount' => [ 'shape' => 'MinimumQueryCount', ], 'AttributeSuggestionsConfig' => [ 'shape' => 'AttributeSuggestionsUpdateConfig', ], ], ], 'UpdateThesaurusRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IndexId', ], 'members' => [ 'Id' => [ 'shape' => 'ThesaurusId', ], 'Name' => [ 'shape' => 'ThesaurusName', ], 'IndexId' => [ 'shape' => 'IndexId', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SourceS3Path' => [ 'shape' => 'S3Path', ], ], ], 'Url' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(https?|ftp|file):\\/\\/([^\\s]*)', ], 'Urls' => [ 'type' => 'structure', 'members' => [ 'SeedUrlConfiguration' => [ 'shape' => 'SeedUrlConfiguration', ], 'SiteMapsConfiguration' => [ 'shape' => 'SiteMapsConfiguration', ], ], ], 'UserAccount' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'UserContext' => [ 'type' => 'structure', 'members' => [ 'Token' => [ 'shape' => 'Token', ], 'UserId' => [ 'shape' => 'PrincipalName', ], 'Groups' => [ 'shape' => 'Groups', ], 'DataSourceGroups' => [ 'shape' => 'DataSourceGroups', ], ], ], 'UserContextPolicy' => [ 'type' => 'string', 'enum' => [ 'ATTRIBUTE_FILTER', 'USER_TOKEN', ], ], 'UserGroupResolutionConfiguration' => [ 'type' => 'structure', 'required' => [ 'UserGroupResolutionMode', ], 'members' => [ 'UserGroupResolutionMode' => [ 'shape' => 'UserGroupResolutionMode', ], ], ], 'UserGroupResolutionMode' => [ 'type' => 'string', 'enum' => [ 'AWS_SSO', 'NONE', ], ], 'UserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'UserIdentityConfiguration' => [ 'type' => 'structure', 'members' => [ 'IdentityAttributeName' => [ 'shape' => 'IdentityAttributeName', ], ], ], 'UserNameAttributeField' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'UserTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'JwtTokenTypeConfiguration' => [ 'shape' => 'JwtTokenTypeConfiguration', ], 'JsonTokenTypeConfiguration' => [ 'shape' => 'JsonTokenTypeConfiguration', ], ], ], 'UserTokenConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserTokenConfiguration', ], 'max' => 1, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ValueImportanceMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ValueImportanceMapKey', ], 'value' => [ 'shape' => 'Importance', ], ], 'ValueImportanceMapKey' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'VisitorId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'VpcSecurityGroupId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[-0-9a-zA-Z]+', ], 'Warning' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'WarningMessage', ], 'Code' => [ 'shape' => 'WarningCode', ], ], ], 'WarningCode' => [ 'type' => 'string', 'enum' => [ 'QUERY_LANGUAGE_INVALID_SYNTAX', ], ], 'WarningList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Warning', ], 'max' => 1, 'min' => 1, ], 'WarningMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'WebCrawlerConfiguration' => [ 'type' => 'structure', 'required' => [ 'Urls', ], 'members' => [ 'Urls' => [ 'shape' => 'Urls', ], 'CrawlDepth' => [ 'shape' => 'CrawlDepth', ], 'MaxLinksPerPage' => [ 'shape' => 'MaxLinksPerPage', ], 'MaxContentSizePerPageInMegaBytes' => [ 'shape' => 'MaxContentSizePerPageInMegaBytes', ], 'MaxUrlsPerMinuteCrawlRate' => [ 'shape' => 'MaxUrlsPerMinuteCrawlRate', ], 'UrlInclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'UrlExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ProxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfiguration', ], ], ], 'WebCrawlerMode' => [ 'type' => 'string', 'enum' => [ 'HOST_ONLY', 'SUBDOMAINS', 'EVERYTHING', ], ], 'WorkDocsConfiguration' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'CrawlComments' => [ 'shape' => 'Boolean', ], 'UseChangeLog' => [ 'shape' => 'Boolean', ], 'InclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'ExclusionPatterns' => [ 'shape' => 'DataSourceInclusionsExclusionsStrings', ], 'FieldMappings' => [ 'shape' => 'DataSourceToIndexFieldMappingList', ], ], ], ],];
