<?php
// This file was auto-generated from sdk-root/src/data/chime/2018-05-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-01', 'endpointPrefix' => 'chime', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Chime', 'serviceId' => 'Chime', 'signatureVersion' => 'v4', 'uid' => 'chime-2018-05-01', ], 'operations' => [ 'AssociatePhoneNumberWithUser' => [ 'name' => 'AssociatePhoneNumberWithUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=associate-phone-number', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociatePhoneNumberWithUserRequest', ], 'output' => [ 'shape' => 'AssociatePhoneNumberWithUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'AssociatePhoneNumbersWithVoiceConnector' => [ 'name' => 'AssociatePhoneNumbersWithVoiceConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}?operation=associate-phone-numbers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociatePhoneNumbersWithVoiceConnectorRequest', ], 'output' => [ 'shape' => 'AssociatePhoneNumbersWithVoiceConnectorResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by AssociatePhoneNumbersWithVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'AssociatePhoneNumbersWithVoiceConnectorGroup' => [ 'name' => 'AssociatePhoneNumbersWithVoiceConnectorGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connector-groups/{voiceConnectorGroupId}?operation=associate-phone-numbers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociatePhoneNumbersWithVoiceConnectorGroupRequest', ], 'output' => [ 'shape' => 'AssociatePhoneNumbersWithVoiceConnectorGroupResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by AssociatePhoneNumbersWithVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'AssociateSigninDelegateGroupsWithAccount' => [ 'name' => 'AssociateSigninDelegateGroupsWithAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}?operation=associate-signin-delegate-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateSigninDelegateGroupsWithAccountRequest', ], 'output' => [ 'shape' => 'AssociateSigninDelegateGroupsWithAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchCreateAttendee' => [ 'name' => 'BatchCreateAttendee', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/attendees?operation=batch-create', 'responseCode' => 201, ], 'input' => [ 'shape' => 'BatchCreateAttendeeRequest', ], 'output' => [ 'shape' => 'BatchCreateAttendeeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by BatchCreateAttendee in the Amazon Chime SDK Meetings Namespace', ], 'BatchCreateChannelMembership' => [ 'name' => 'BatchCreateChannelMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/memberships?operation=batch-create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchCreateChannelMembershipRequest', ], 'output' => [ 'shape' => 'BatchCreateChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by BatchCreateChannelMembership in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'BatchCreateRoomMembership' => [ 'name' => 'BatchCreateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships?operation=batch-create', 'responseCode' => 201, ], 'input' => [ 'shape' => 'BatchCreateRoomMembershipRequest', ], 'output' => [ 'shape' => 'BatchCreateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchDeletePhoneNumber' => [ 'name' => 'BatchDeletePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers?operation=batch-delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeletePhoneNumberRequest', ], 'output' => [ 'shape' => 'BatchDeletePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchSuspendUser' => [ 'name' => 'BatchSuspendUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=suspend', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchSuspendUserRequest', ], 'output' => [ 'shape' => 'BatchSuspendUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUnsuspendUser' => [ 'name' => 'BatchUnsuspendUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=unsuspend', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUnsuspendUserRequest', ], 'output' => [ 'shape' => 'BatchUnsuspendUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUpdatePhoneNumber' => [ 'name' => 'BatchUpdatePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers?operation=batch-update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'BatchUpdatePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'BatchUpdateUser' => [ 'name' => 'BatchUpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateUserRequest', ], 'output' => [ 'shape' => 'BatchUpdateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateAccount' => [ 'name' => 'CreateAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccountRequest', ], 'output' => [ 'shape' => 'CreateAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateAppInstance' => [ 'name' => 'CreateAppInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/app-instances', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppInstanceRequest', ], 'output' => [ 'shape' => 'CreateAppInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateAppInstance in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'CreateAppInstanceAdmin' => [ 'name' => 'CreateAppInstanceAdmin', 'http' => [ 'method' => 'POST', 'requestUri' => '/app-instances/{appInstanceArn}/admins', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppInstanceAdminRequest', ], 'output' => [ 'shape' => 'CreateAppInstanceAdminResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateAppInstanceAdmin in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'CreateAppInstanceUser' => [ 'name' => 'CreateAppInstanceUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/app-instance-users', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppInstanceUserRequest', ], 'output' => [ 'shape' => 'CreateAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateAppInstanceUser in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'CreateAttendee' => [ 'name' => 'CreateAttendee', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/attendees', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAttendeeRequest', ], 'output' => [ 'shape' => 'CreateAttendeeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateAttendee in the Amazon Chime SDK Meetings Namespace', ], 'CreateBot' => [ 'name' => 'CreateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBotRequest', ], 'output' => [ 'shape' => 'CreateBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateChannel in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'CreateChannelBan' => [ 'name' => 'CreateChannelBan', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/bans', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelBanRequest', ], 'output' => [ 'shape' => 'CreateChannelBanResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateChannelBan in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'CreateChannelMembership' => [ 'name' => 'CreateChannelMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/memberships', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelMembershipRequest', ], 'output' => [ 'shape' => 'CreateChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateChannelMembership in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'CreateChannelModerator' => [ 'name' => 'CreateChannelModerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/moderators', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelModeratorRequest', ], 'output' => [ 'shape' => 'CreateChannelModeratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateChannelModerator in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'CreateMediaCapturePipeline' => [ 'name' => 'CreateMediaCapturePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-capture-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaCapturePipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaCapturePipelineResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateMediaCapturePipeline in the Amazon Chime SDK Media Pipelines Namespace', ], 'CreateMeeting' => [ 'name' => 'CreateMeeting', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMeetingRequest', ], 'output' => [ 'shape' => 'CreateMeetingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateMeeting in the Amazon Chime SDK Meetings Namespace', ], 'CreateMeetingDialOut' => [ 'name' => 'CreateMeetingDialOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/dial-outs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMeetingDialOutRequest', ], 'output' => [ 'shape' => 'CreateMeetingDialOutResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMeetingWithAttendees' => [ 'name' => 'CreateMeetingWithAttendees', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings?operation=create-attendees', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMeetingWithAttendeesRequest', ], 'output' => [ 'shape' => 'CreateMeetingWithAttendeesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateMeetingWithAttendees in the Amazon Chime SDK Meetings Namespace', ], 'CreatePhoneNumberOrder' => [ 'name' => 'CreatePhoneNumberOrder', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number-orders', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePhoneNumberOrderRequest', ], 'output' => [ 'shape' => 'CreatePhoneNumberOrderResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateProxySession' => [ 'name' => 'CreateProxySession', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}/proxy-sessions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProxySessionRequest', ], 'output' => [ 'shape' => 'CreateProxySessionResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateProxySession in the Amazon Chime SDK Voice Namespace', ], 'CreateRoom' => [ 'name' => 'CreateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRoomRequest', ], 'output' => [ 'shape' => 'CreateRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateRoomMembership' => [ 'name' => 'CreateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRoomMembershipRequest', ], 'output' => [ 'shape' => 'CreateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateSipMediaApplication' => [ 'name' => 'CreateSipMediaApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/sip-media-applications', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSipMediaApplicationRequest', ], 'output' => [ 'shape' => 'CreateSipMediaApplicationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateSipMediaApplication in the Amazon Chime SDK Voice Namespace', ], 'CreateSipMediaApplicationCall' => [ 'name' => 'CreateSipMediaApplicationCall', 'http' => [ 'method' => 'POST', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}/calls', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSipMediaApplicationCallRequest', ], 'output' => [ 'shape' => 'CreateSipMediaApplicationCallResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateSipMediaApplicationCall in the Amazon Chime SDK Voice Namespace', ], 'CreateSipRule' => [ 'name' => 'CreateSipRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/sip-rules', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSipRuleRequest', ], 'output' => [ 'shape' => 'CreateSipRuleResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateSipRule in the Amazon Chime SDK Voice Namespace', ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=create', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateVoiceConnector' => [ 'name' => 'CreateVoiceConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateVoiceConnectorRequest', ], 'output' => [ 'shape' => 'CreateVoiceConnectorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'CreateVoiceConnectorGroup' => [ 'name' => 'CreateVoiceConnectorGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connector-groups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateVoiceConnectorGroupRequest', ], 'output' => [ 'shape' => 'CreateVoiceConnectorGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by CreateVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'DeleteAccount' => [ 'name' => 'DeleteAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAccountRequest', ], 'output' => [ 'shape' => 'DeleteAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteAppInstance' => [ 'name' => 'DeleteAppInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app-instances/{appInstanceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppInstanceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteAppInstance in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DeleteAppInstanceAdmin' => [ 'name' => 'DeleteAppInstanceAdmin', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app-instances/{appInstanceArn}/admins/{appInstanceAdminArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppInstanceAdminRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteAppInstanceAdmin in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DeleteAppInstanceStreamingConfigurations' => [ 'name' => 'DeleteAppInstanceStreamingConfigurations', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppInstanceStreamingConfigurationsRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteAppInstanceStreamingConfigurations in the Amazon Chime SDK Messaging Namespace', ], 'DeleteAppInstanceUser' => [ 'name' => 'DeleteAppInstanceUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app-instance-users/{appInstanceUserArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAppInstanceUserRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteAppInstanceUser in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DeleteAttendee' => [ 'name' => 'DeleteAttendee', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/meetings/{meetingId}/attendees/{attendeeId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAttendeeRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteAttendee in the Amazon Chime SDK Meetings Namespace', ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteChannel in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DeleteChannelBan' => [ 'name' => 'DeleteChannelBan', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/bans/{memberArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelBanRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteChannelBan in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DeleteChannelMembership' => [ 'name' => 'DeleteChannelMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelMembershipRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteChannelMembership in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DeleteChannelMessage' => [ 'name' => 'DeleteChannelMessage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelMessageRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteChannelMessage in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DeleteChannelModerator' => [ 'name' => 'DeleteChannelModerator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channels/{channelArn}/moderators/{channelModeratorArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelModeratorRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteChannelModerator in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DeleteEventsConfiguration' => [ 'name' => 'DeleteEventsConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEventsConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'DeleteMediaCapturePipeline' => [ 'name' => 'DeleteMediaCapturePipeline', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/media-capture-pipelines/{mediaPipelineId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMediaCapturePipelineRequest', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteMediaCapturePipeline in the Amazon Chime SDK Media Pipelines Namespace', ], 'DeleteMeeting' => [ 'name' => 'DeleteMeeting', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/meetings/{meetingId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMeetingRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteMeeting in the Amazon Chime SDK Meetings Namespace', ], 'DeletePhoneNumber' => [ 'name' => 'DeletePhoneNumber', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/phone-numbers/{phoneNumberId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePhoneNumberRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteProxySession' => [ 'name' => 'DeleteProxySession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProxySessionRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteProxySession in the Amazon Chime SDK Voice Namespace', ], 'DeleteRoom' => [ 'name' => 'DeleteRoom', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRoomRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteRoomMembership' => [ 'name' => 'DeleteRoomMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships/{memberId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRoomMembershipRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteSipMediaApplication' => [ 'name' => 'DeleteSipMediaApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSipMediaApplicationRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteSipMediaApplication in the Amazon Chime SDK Voice Namespace', ], 'DeleteSipRule' => [ 'name' => 'DeleteSipRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sip-rules/{sipRuleId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSipRuleRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteSipRule in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnector' => [ 'name' => 'DeleteVoiceConnector', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorEmergencyCallingConfiguration' => [ 'name' => 'DeleteVoiceConnectorEmergencyCallingConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/emergency-calling-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorEmergencyCallingConfigurationRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorEmergencyCallingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorGroup' => [ 'name' => 'DeleteVoiceConnectorGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connector-groups/{voiceConnectorGroupId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorGroupRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorOrigination' => [ 'name' => 'DeleteVoiceConnectorOrigination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/origination', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorOriginationRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorOrigination in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorProxy' => [ 'name' => 'DeleteVoiceConnectorProxy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorProxyRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorProxy in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorStreamingConfiguration' => [ 'name' => 'DeleteVoiceConnectorStreamingConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/streaming-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorStreamingConfigurationRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorStreamingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorTermination' => [ 'name' => 'DeleteVoiceConnectorTermination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorTerminationRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorTermination in the Amazon Chime SDK Voice Namespace', ], 'DeleteVoiceConnectorTerminationCredentials' => [ 'name' => 'DeleteVoiceConnectorTerminationCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination/credentials?operation=delete', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVoiceConnectorTerminationCredentialsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DeleteVoiceConnectorTerminationCredentials in the Amazon Chime SDK Voice Namespace', ], 'DescribeAppInstance' => [ 'name' => 'DescribeAppInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppInstanceRequest', ], 'output' => [ 'shape' => 'DescribeAppInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeAppInstance in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DescribeAppInstanceAdmin' => [ 'name' => 'DescribeAppInstanceAdmin', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}/admins/{appInstanceAdminArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppInstanceAdminRequest', ], 'output' => [ 'shape' => 'DescribeAppInstanceAdminResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeAppInstanceAdmin in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DescribeAppInstanceUser' => [ 'name' => 'DescribeAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instance-users/{appInstanceUserArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppInstanceUserRequest', ], 'output' => [ 'shape' => 'DescribeAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeAppInstanceUser in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'DescribeChannel' => [ 'name' => 'DescribeChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelRequest', ], 'output' => [ 'shape' => 'DescribeChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannel in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DescribeChannelBan' => [ 'name' => 'DescribeChannelBan', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/bans/{memberArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelBanRequest', ], 'output' => [ 'shape' => 'DescribeChannelBanResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannelBan in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DescribeChannelMembership' => [ 'name' => 'DescribeChannelMembership', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/memberships/{memberArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelMembershipRequest', ], 'output' => [ 'shape' => 'DescribeChannelMembershipResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannelMembership in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DescribeChannelMembershipForAppInstanceUser' => [ 'name' => 'DescribeChannelMembershipForAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}?scope=app-instance-user-membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelMembershipForAppInstanceUserRequest', ], 'output' => [ 'shape' => 'DescribeChannelMembershipForAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannelMembershipForAppInstanceUser in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DescribeChannelModeratedByAppInstanceUser' => [ 'name' => 'DescribeChannelModeratedByAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}?scope=app-instance-user-moderated-channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelModeratedByAppInstanceUserRequest', ], 'output' => [ 'shape' => 'DescribeChannelModeratedByAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannelModeratedByAppInstanceUser in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DescribeChannelModerator' => [ 'name' => 'DescribeChannelModerator', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/moderators/{channelModeratorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelModeratorRequest', ], 'output' => [ 'shape' => 'DescribeChannelModeratorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DescribeChannelModerator in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'DisassociatePhoneNumberFromUser' => [ 'name' => 'DisassociatePhoneNumberFromUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=disassociate-phone-number', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociatePhoneNumberFromUserRequest', ], 'output' => [ 'shape' => 'DisassociatePhoneNumberFromUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DisassociatePhoneNumbersFromVoiceConnector' => [ 'name' => 'DisassociatePhoneNumbersFromVoiceConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}?operation=disassociate-phone-numbers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociatePhoneNumbersFromVoiceConnectorRequest', ], 'output' => [ 'shape' => 'DisassociatePhoneNumbersFromVoiceConnectorResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DisassociatePhoneNumbersFromVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'DisassociatePhoneNumbersFromVoiceConnectorGroup' => [ 'name' => 'DisassociatePhoneNumbersFromVoiceConnectorGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connector-groups/{voiceConnectorGroupId}?operation=disassociate-phone-numbers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociatePhoneNumbersFromVoiceConnectorGroupRequest', ], 'output' => [ 'shape' => 'DisassociatePhoneNumbersFromVoiceConnectorGroupResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by DisassociatePhoneNumbersFromVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'DisassociateSigninDelegateGroupsFromAccount' => [ 'name' => 'DisassociateSigninDelegateGroupsFromAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}?operation=disassociate-signin-delegate-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateSigninDelegateGroupsFromAccountRequest', ], 'output' => [ 'shape' => 'DisassociateSigninDelegateGroupsFromAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetAccount' => [ 'name' => 'GetAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}', ], 'input' => [ 'shape' => 'GetAccountRequest', ], 'output' => [ 'shape' => 'GetAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/settings', ], 'input' => [ 'shape' => 'GetAccountSettingsRequest', ], 'output' => [ 'shape' => 'GetAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetAppInstanceRetentionSettings' => [ 'name' => 'GetAppInstanceRetentionSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}/retention-settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppInstanceRetentionSettingsRequest', ], 'output' => [ 'shape' => 'GetAppInstanceRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetAppInstanceRetentionSettings in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'GetAppInstanceStreamingConfigurations' => [ 'name' => 'GetAppInstanceStreamingConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppInstanceStreamingConfigurationsRequest', ], 'output' => [ 'shape' => 'GetAppInstanceStreamingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetAppInstanceStreamingConfigurations in the Amazon Chime SDK Messaging Namespace', ], 'GetAttendee' => [ 'name' => 'GetAttendee', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings/{meetingId}/attendees/{attendeeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAttendeeRequest', ], 'output' => [ 'shape' => 'GetAttendeeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetAttendee in the Amazon Chime SDK Meetings Namespace', ], 'GetBot' => [ 'name' => 'GetBot', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots/{botId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotRequest', ], 'output' => [ 'shape' => 'GetBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'GetChannelMessage' => [ 'name' => 'GetChannelMessage', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelMessageRequest', ], 'output' => [ 'shape' => 'GetChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetChannelMessage in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'GetEventsConfiguration' => [ 'name' => 'GetEventsConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventsConfigurationRequest', ], 'output' => [ 'shape' => 'GetEventsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetGlobalSettings' => [ 'name' => 'GetGlobalSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/settings', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetGlobalSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMediaCapturePipeline' => [ 'name' => 'GetMediaCapturePipeline', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-capture-pipelines/{mediaPipelineId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMediaCapturePipelineRequest', ], 'output' => [ 'shape' => 'GetMediaCapturePipelineResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetMediaCapturePipeline in the Amazon Chime SDK Media Pipelines Namespace', ], 'GetMeeting' => [ 'name' => 'GetMeeting', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings/{meetingId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMeetingRequest', ], 'output' => [ 'shape' => 'GetMeetingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetMeeting in the Amazon Chime SDK Meetings Namespace', ], 'GetMessagingSessionEndpoint' => [ 'name' => 'GetMessagingSessionEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/endpoints/messaging-session', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMessagingSessionEndpointRequest', ], 'output' => [ 'shape' => 'GetMessagingSessionEndpointResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetMessagingSessionEndpoint in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'GetPhoneNumber' => [ 'name' => 'GetPhoneNumber', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers/{phoneNumberId}', ], 'input' => [ 'shape' => 'GetPhoneNumberRequest', ], 'output' => [ 'shape' => 'GetPhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetPhoneNumberOrder' => [ 'name' => 'GetPhoneNumberOrder', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-orders/{phoneNumberOrderId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPhoneNumberOrderRequest', ], 'output' => [ 'shape' => 'GetPhoneNumberOrderResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetPhoneNumberSettings' => [ 'name' => 'GetPhoneNumberSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/settings/phone-number', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetPhoneNumberSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetProxySession' => [ 'name' => 'GetProxySession', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProxySessionRequest', ], 'output' => [ 'shape' => 'GetProxySessionResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetProxySession in the Amazon Chime SDK Voice Namespace', ], 'GetRetentionSettings' => [ 'name' => 'GetRetentionSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/retention-settings', ], 'input' => [ 'shape' => 'GetRetentionSettingsRequest', ], 'output' => [ 'shape' => 'GetRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetRoom' => [ 'name' => 'GetRoom', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRoomRequest', ], 'output' => [ 'shape' => 'GetRoomResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetSipMediaApplication' => [ 'name' => 'GetSipMediaApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSipMediaApplicationRequest', ], 'output' => [ 'shape' => 'GetSipMediaApplicationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetSipMediaApplication in the Amazon Chime SDK Voice Namespace', ], 'GetSipMediaApplicationLoggingConfiguration' => [ 'name' => 'GetSipMediaApplicationLoggingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}/logging-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSipMediaApplicationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetSipMediaApplicationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetSipMediaApplicationLoggingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'GetSipRule' => [ 'name' => 'GetSipRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/sip-rules/{sipRuleId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSipRuleRequest', ], 'output' => [ 'shape' => 'GetSipRuleResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetSipRule in the Amazon Chime SDK Voice Namespace', ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetUserSettings' => [ 'name' => 'GetUserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users/{userId}/settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserSettingsRequest', ], 'output' => [ 'shape' => 'GetUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetVoiceConnector' => [ 'name' => 'GetVoiceConnector', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorEmergencyCallingConfiguration' => [ 'name' => 'GetVoiceConnectorEmergencyCallingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/emergency-calling-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorEmergencyCallingConfigurationRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorEmergencyCallingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorEmergencyCallingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorGroup' => [ 'name' => 'GetVoiceConnectorGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connector-groups/{voiceConnectorGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorGroupRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorGroupResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorLoggingConfiguration' => [ 'name' => 'GetVoiceConnectorLoggingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/logging-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorLoggingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorOrigination' => [ 'name' => 'GetVoiceConnectorOrigination', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/origination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorOriginationRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorOriginationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorOrigination in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorProxy' => [ 'name' => 'GetVoiceConnectorProxy', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorProxyRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorProxyResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorProxy in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorStreamingConfiguration' => [ 'name' => 'GetVoiceConnectorStreamingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/streaming-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorStreamingConfigurationRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorStreamingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorStreamingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorTermination' => [ 'name' => 'GetVoiceConnectorTermination', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorTerminationRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorTerminationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorTermination in the Amazon Chime SDK Voice Namespace', ], 'GetVoiceConnectorTerminationHealth' => [ 'name' => 'GetVoiceConnectorTerminationHealth', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination/health', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceConnectorTerminationHealthRequest', ], 'output' => [ 'shape' => 'GetVoiceConnectorTerminationHealthResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by GetVoiceConnectorTerminationHealth in the Amazon Chime SDK Voice Namespace', ], 'InviteUsers' => [ 'name' => 'InviteUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users?operation=add', 'responseCode' => 201, ], 'input' => [ 'shape' => 'InviteUsersRequest', ], 'output' => [ 'shape' => 'InviteUsersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListAccounts' => [ 'name' => 'ListAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'ListAccountsRequest', ], 'output' => [ 'shape' => 'ListAccountsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListAppInstanceAdmins' => [ 'name' => 'ListAppInstanceAdmins', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances/{appInstanceArn}/admins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppInstanceAdminsRequest', ], 'output' => [ 'shape' => 'ListAppInstanceAdminsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListAppInstanceAdmins in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'ListAppInstanceUsers' => [ 'name' => 'ListAppInstanceUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instance-users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppInstanceUsersRequest', ], 'output' => [ 'shape' => 'ListAppInstanceUsersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListAppInstanceUsers in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'ListAppInstances' => [ 'name' => 'ListAppInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/app-instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppInstancesRequest', ], 'output' => [ 'shape' => 'ListAppInstancesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListAppInstances in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'ListAttendeeTags' => [ 'name' => 'ListAttendeeTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings/{meetingId}/attendees/{attendeeId}/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAttendeeTagsRequest', ], 'output' => [ 'shape' => 'ListAttendeeTagsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Attendee Tags are not supported in the Amazon Chime SDK Meetings Namespace. Update your application to remove calls to this API.', ], 'ListAttendees' => [ 'name' => 'ListAttendees', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings/{meetingId}/attendees', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAttendeesRequest', ], 'output' => [ 'shape' => 'ListAttendeesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListAttendees in the Amazon Chime SDK Meetings Namespace', ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/bots', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'ListChannelBans' => [ 'name' => 'ListChannelBans', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/bans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelBansRequest', ], 'output' => [ 'shape' => 'ListChannelBansResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelBans in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannelMemberships' => [ 'name' => 'ListChannelMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMembershipsRequest', ], 'output' => [ 'shape' => 'ListChannelMembershipsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelMemberships in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannelMembershipsForAppInstanceUser' => [ 'name' => 'ListChannelMembershipsForAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels?scope=app-instance-user-memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMembershipsForAppInstanceUserRequest', ], 'output' => [ 'shape' => 'ListChannelMembershipsForAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelMembershipsForAppInstanceUser in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannelMessages' => [ 'name' => 'ListChannelMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/messages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelMessagesRequest', ], 'output' => [ 'shape' => 'ListChannelMessagesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelMessages in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannelModerators' => [ 'name' => 'ListChannelModerators', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/{channelArn}/moderators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelModeratorsRequest', ], 'output' => [ 'shape' => 'ListChannelModeratorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelModerators in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannels in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListChannelsModeratedByAppInstanceUser' => [ 'name' => 'ListChannelsModeratedByAppInstanceUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels?scope=app-instance-user-moderated-channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsModeratedByAppInstanceUserRequest', ], 'output' => [ 'shape' => 'ListChannelsModeratedByAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListChannelsModeratedByAppInstanceUser in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'ListMediaCapturePipelines' => [ 'name' => 'ListMediaCapturePipelines', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-capture-pipelines', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMediaCapturePipelinesRequest', ], 'output' => [ 'shape' => 'ListMediaCapturePipelinesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListMediaCapturePipelines in the Amazon Chime SDK Media Pipelines Namespace', ], 'ListMeetingTags' => [ 'name' => 'ListMeetingTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings/{meetingId}/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMeetingTagsRequest', ], 'output' => [ 'shape' => 'ListMeetingTagsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Use ListTagsForResource in the Amazon Chime SDK Meetings Namespace.', ], 'ListMeetings' => [ 'name' => 'ListMeetings', 'http' => [ 'method' => 'GET', 'requestUri' => '/meetings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMeetingsRequest', ], 'output' => [ 'shape' => 'ListMeetingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'ListMeetings is not supported in the Amazon Chime SDK Meetings Namespace. Update your application to remove calls to this API.', ], 'ListPhoneNumberOrders' => [ 'name' => 'ListPhoneNumberOrders', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-orders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPhoneNumberOrdersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumberOrdersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListPhoneNumbers' => [ 'name' => 'ListPhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers', ], 'input' => [ 'shape' => 'ListPhoneNumbersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListProxySessions' => [ 'name' => 'ListProxySessions', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/proxy-sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProxySessionsRequest', ], 'output' => [ 'shape' => 'ListProxySessionsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListProxySessions in the Amazon Chime SDK Voice Namespace', ], 'ListRoomMemberships' => [ 'name' => 'ListRoomMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoomMembershipsRequest', ], 'output' => [ 'shape' => 'ListRoomMembershipsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListRooms' => [ 'name' => 'ListRooms', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/rooms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoomsRequest', ], 'output' => [ 'shape' => 'ListRoomsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListSipMediaApplications' => [ 'name' => 'ListSipMediaApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/sip-media-applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSipMediaApplicationsRequest', ], 'output' => [ 'shape' => 'ListSipMediaApplicationsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListSipMediaApplications in the Amazon Chime SDK Voice Namespace', ], 'ListSipRules' => [ 'name' => 'ListSipRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/sip-rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSipRulesRequest', ], 'output' => [ 'shape' => 'ListSipRulesResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListSipRules in the Amazon Chime SDK Voice Namespace', ], 'ListSupportedPhoneNumberCountries' => [ 'name' => 'ListSupportedPhoneNumberCountries', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number-countries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSupportedPhoneNumberCountriesRequest', ], 'output' => [ 'shape' => 'ListSupportedPhoneNumberCountriesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListTagsForResource in the Amazon Chime SDK Voice, Amazon Chime SDK Meetings, Amazon Chime SDK Identity, Amazon Chime SDK Messaging, and Amazon Chime SDK Media Pipelines Namespaces', ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{accountId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListVoiceConnectorGroups' => [ 'name' => 'ListVoiceConnectorGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connector-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVoiceConnectorGroupsRequest', ], 'output' => [ 'shape' => 'ListVoiceConnectorGroupsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListVoiceConnectorGroups in the Amazon Chime SDK Voice Namespace', ], 'ListVoiceConnectorTerminationCredentials' => [ 'name' => 'ListVoiceConnectorTerminationCredentials', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination/credentials', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVoiceConnectorTerminationCredentialsRequest', ], 'output' => [ 'shape' => 'ListVoiceConnectorTerminationCredentialsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListVoiceConnectorTerminationCredentials in the Amazon Chime SDK Voice Namespace', ], 'ListVoiceConnectors' => [ 'name' => 'ListVoiceConnectors', 'http' => [ 'method' => 'GET', 'requestUri' => '/voice-connectors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVoiceConnectorsRequest', ], 'output' => [ 'shape' => 'ListVoiceConnectorsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ListVoiceConnectors in the Amazon Chime SDK Voice Namespace', ], 'LogoutUser' => [ 'name' => 'LogoutUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=logout', 'responseCode' => 204, ], 'input' => [ 'shape' => 'LogoutUserRequest', ], 'output' => [ 'shape' => 'LogoutUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutAppInstanceRetentionSettings' => [ 'name' => 'PutAppInstanceRetentionSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app-instances/{appInstanceArn}/retention-settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAppInstanceRetentionSettingsRequest', ], 'output' => [ 'shape' => 'PutAppInstanceRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutAppInstanceRetentionSettings in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'PutAppInstanceStreamingConfigurations' => [ 'name' => 'PutAppInstanceStreamingConfigurations', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app-instances/{appInstanceArn}/streaming-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAppInstanceStreamingConfigurationsRequest', ], 'output' => [ 'shape' => 'PutAppInstanceStreamingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutAppInstanceStreamingConfigurations in the Amazon Chime SDK Messaging Namespace', ], 'PutEventsConfiguration' => [ 'name' => 'PutEventsConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/bots/{botId}/events-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PutEventsConfigurationRequest', ], 'output' => [ 'shape' => 'PutEventsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], ], ], 'PutRetentionSettings' => [ 'name' => 'PutRetentionSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/retention-settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutRetentionSettingsRequest', ], 'output' => [ 'shape' => 'PutRetentionSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'PutSipMediaApplicationLoggingConfiguration' => [ 'name' => 'PutSipMediaApplicationLoggingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}/logging-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutSipMediaApplicationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutSipMediaApplicationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutSipMediaApplicationLoggingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorEmergencyCallingConfiguration' => [ 'name' => 'PutVoiceConnectorEmergencyCallingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/emergency-calling-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutVoiceConnectorEmergencyCallingConfigurationRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorEmergencyCallingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorEmergencyCallingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorLoggingConfiguration' => [ 'name' => 'PutVoiceConnectorLoggingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/logging-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutVoiceConnectorLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorLoggingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorOrigination' => [ 'name' => 'PutVoiceConnectorOrigination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/origination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutVoiceConnectorOriginationRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorOriginationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorOrigination in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorProxy' => [ 'name' => 'PutVoiceConnectorProxy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy', ], 'input' => [ 'shape' => 'PutVoiceConnectorProxyRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorProxyResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorProxy in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorStreamingConfiguration' => [ 'name' => 'PutVoiceConnectorStreamingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/streaming-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutVoiceConnectorStreamingConfigurationRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorStreamingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorStreamingConfiguration in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorTermination' => [ 'name' => 'PutVoiceConnectorTermination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutVoiceConnectorTerminationRequest', ], 'output' => [ 'shape' => 'PutVoiceConnectorTerminationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorTermination in the Amazon Chime SDK Voice Namespace', ], 'PutVoiceConnectorTerminationCredentials' => [ 'name' => 'PutVoiceConnectorTerminationCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}/termination/credentials?operation=put', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutVoiceConnectorTerminationCredentialsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by PutVoiceConnectorTerminationCredentials in the Amazon Chime SDK Voice Namespace', ], 'RedactChannelMessage' => [ 'name' => 'RedactChannelMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactChannelMessageRequest', ], 'output' => [ 'shape' => 'RedactChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by RedactChannelMessage in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'RedactConversationMessage' => [ 'name' => 'RedactConversationMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/conversations/{conversationId}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactConversationMessageRequest', ], 'output' => [ 'shape' => 'RedactConversationMessageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RedactRoomMessage' => [ 'name' => 'RedactRoomMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/messages/{messageId}?operation=redact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RedactRoomMessageRequest', ], 'output' => [ 'shape' => 'RedactRoomMessageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RegenerateSecurityToken' => [ 'name' => 'RegenerateSecurityToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots/{botId}?operation=regenerate-security-token', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegenerateSecurityTokenRequest', ], 'output' => [ 'shape' => 'RegenerateSecurityTokenResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'ResetPersonalPIN' => [ 'name' => 'ResetPersonalPIN', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}?operation=reset-personal-pin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResetPersonalPINRequest', ], 'output' => [ 'shape' => 'ResetPersonalPINResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'RestorePhoneNumber' => [ 'name' => 'RestorePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers/{phoneNumberId}?operation=restore', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RestorePhoneNumberRequest', ], 'output' => [ 'shape' => 'RestorePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'SearchAvailablePhoneNumbers' => [ 'name' => 'SearchAvailablePhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/search?type=phone-numbers', ], 'input' => [ 'shape' => 'SearchAvailablePhoneNumbersRequest', ], 'output' => [ 'shape' => 'SearchAvailablePhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'SendChannelMessage' => [ 'name' => 'SendChannelMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/{channelArn}/messages', 'responseCode' => 201, ], 'input' => [ 'shape' => 'SendChannelMessageRequest', ], 'output' => [ 'shape' => 'SendChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by SendChannelMessage in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'StartMeetingTranscription' => [ 'name' => 'StartMeetingTranscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/transcription?operation=start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMeetingTranscriptionRequest', ], 'output' => [ 'shape' => 'StartMeetingTranscriptionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by StartMeetingTranscription in the Amazon Chime SDK Meetings Namespace', ], 'StopMeetingTranscription' => [ 'name' => 'StopMeetingTranscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/transcription?operation=stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopMeetingTranscriptionRequest', ], 'output' => [ 'shape' => 'StopMeetingTranscriptionResponse', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by StopMeetingTranscription in the Amazon Chime SDK Meetings Namespace', ], 'TagAttendee' => [ 'name' => 'TagAttendee', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/attendees/{attendeeId}/tags?operation=add', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagAttendeeRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Attendee Tags are not supported in the Amazon Chime SDK Meetings Namespace. Update your application to remove calls to this API.', ], 'TagMeeting' => [ 'name' => 'TagMeeting', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/tags?operation=add', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagMeetingRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Use TagResource in the Amazon Chime SDK Meetings Namespace.', ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=tag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by TagResource in the Amazon Chime SDK Voice, Amazon Chime SDK Meetings, Amazon Chime SDK Identity, Amazon Chime SDK Messaging, and Amazon Chime SDK Media Pipelines Namespaces', ], 'UntagAttendee' => [ 'name' => 'UntagAttendee', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/attendees/{attendeeId}/tags?operation=delete', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagAttendeeRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Attendee Tags are not supported in the Amazon Chime SDK Meetings Namespace. Update your application to remove calls to this API.', ], 'UntagMeeting' => [ 'name' => 'UntagMeeting', 'http' => [ 'method' => 'POST', 'requestUri' => '/meetings/{meetingId}/tags?operation=delete', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagMeetingRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Use UntagResource in the Amazon Chime SDK Meetings Namespace.', ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=untag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UntagResource in the Amazon Chime SDK Voice, Amazon Chime SDK Meetings, Amazon Chime SDK Identity, Amazon Chime SDK Messaging, and Amazon Chime SDK Media Pipelines Namespaces', ], 'UpdateAccount' => [ 'name' => 'UpdateAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccountRequest', ], 'output' => [ 'shape' => 'UpdateAccountResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateAccountSettings' => [ 'name' => 'UpdateAccountSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateAccountSettingsRequest', ], 'output' => [ 'shape' => 'UpdateAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateAppInstance' => [ 'name' => 'UpdateAppInstance', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app-instances/{appInstanceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppInstanceRequest', ], 'output' => [ 'shape' => 'UpdateAppInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateAppInstance in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'UpdateAppInstanceUser' => [ 'name' => 'UpdateAppInstanceUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app-instance-users/{appInstanceUserArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppInstanceUserRequest', ], 'output' => [ 'shape' => 'UpdateAppInstanceUserResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateAppInstanceUser in the Amazon Chime SDK Identity Namespace', 'endpoint' => [ 'hostPrefix' => 'identity-', ], ], 'UpdateBot' => [ 'name' => 'UpdateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/bots/{botId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBotRequest', ], 'output' => [ 'shape' => 'UpdateBotResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ThrottledClientException', ], ], ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateChannel in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'UpdateChannelMessage' => [ 'name' => 'UpdateChannelMessage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/messages/{messageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelMessageRequest', ], 'output' => [ 'shape' => 'UpdateChannelMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateChannelMessage in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'UpdateChannelReadMarker' => [ 'name' => 'UpdateChannelReadMarker', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/{channelArn}/readMarker', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelReadMarkerRequest', ], 'output' => [ 'shape' => 'UpdateChannelReadMarkerResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateChannelReadMarker in the Amazon Chime SDK Messaging Namespace', 'endpoint' => [ 'hostPrefix' => 'messaging-', ], ], 'UpdateGlobalSettings' => [ 'name' => 'UpdateGlobalSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateGlobalSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdatePhoneNumber' => [ 'name' => 'UpdatePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-numbers/{phoneNumberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'UpdatePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdatePhoneNumberSettings' => [ 'name' => 'UpdatePhoneNumberSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/settings/phone-number', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdatePhoneNumberSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateProxySession' => [ 'name' => 'UpdateProxySession', 'http' => [ 'method' => 'POST', 'requestUri' => '/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateProxySessionRequest', ], 'output' => [ 'shape' => 'UpdateProxySessionResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateProxySession in the Amazon Chime SDK Voice Namespace', ], 'UpdateRoom' => [ 'name' => 'UpdateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRoomRequest', ], 'output' => [ 'shape' => 'UpdateRoomResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateRoomMembership' => [ 'name' => 'UpdateRoomMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/rooms/{roomId}/memberships/{memberId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRoomMembershipRequest', ], 'output' => [ 'shape' => 'UpdateRoomMembershipResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateSipMediaApplication' => [ 'name' => 'UpdateSipMediaApplication', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSipMediaApplicationRequest', ], 'output' => [ 'shape' => 'UpdateSipMediaApplicationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateSipMediaApplication in the Amazon Chime SDK Voice Namespace', ], 'UpdateSipMediaApplicationCall' => [ 'name' => 'UpdateSipMediaApplicationCall', 'http' => [ 'method' => 'POST', 'requestUri' => '/sip-media-applications/{sipMediaApplicationId}/calls/{transactionId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateSipMediaApplicationCallRequest', ], 'output' => [ 'shape' => 'UpdateSipMediaApplicationCallResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateSipMediaApplicationCall in the Amazon Chime SDK Voice Namespace', ], 'UpdateSipRule' => [ 'name' => 'UpdateSipRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sip-rules/{sipRuleId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateSipRuleRequest', ], 'output' => [ 'shape' => 'UpdateSipRuleResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateSipRule in the Amazon Chime SDK Voice Namespace', ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{accountId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateUserSettings' => [ 'name' => 'UpdateUserSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{accountId}/users/{userId}/settings', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateUserSettingsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateVoiceConnector' => [ 'name' => 'UpdateVoiceConnector', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connectors/{voiceConnectorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVoiceConnectorRequest', ], 'output' => [ 'shape' => 'UpdateVoiceConnectorResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateVoiceConnector in the Amazon Chime SDK Voice Namespace', ], 'UpdateVoiceConnectorGroup' => [ 'name' => 'UpdateVoiceConnectorGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/voice-connector-groups/{voiceConnectorGroupId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateVoiceConnectorGroupRequest', ], 'output' => [ 'shape' => 'UpdateVoiceConnectorGroupResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by UpdateVoiceConnectorGroup in the Amazon Chime SDK Voice Namespace', ], 'ValidateE911Address' => [ 'name' => 'ValidateE911Address', 'http' => [ 'method' => 'POST', 'requestUri' => '/emergency-calling/address', 'responseCode' => 202, ], 'input' => [ 'shape' => 'ValidateE911AddressRequest', ], 'output' => [ 'shape' => 'ValidateE911AddressResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Replaced by ValidateE911Address in the Amazon Chime SDK Voice Namespace', ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AccountId', 'Name', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'AccountType' => [ 'shape' => 'AccountType', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'DefaultLicense' => [ 'shape' => 'License', ], 'SupportedLicenses' => [ 'shape' => 'LicenseList', ], 'AccountStatus' => [ 'shape' => 'AccountStatus', ], 'SigninDelegateGroups' => [ 'shape' => 'SigninDelegateGroupList', ], ], ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AccountName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*\\S.*', ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'DisableRemoteControl' => [ 'shape' => 'Boolean', ], 'EnableDialOut' => [ 'shape' => 'Boolean', ], ], ], 'AccountStatus' => [ 'type' => 'string', 'enum' => [ 'Suspended', 'Active', ], ], 'AccountType' => [ 'type' => 'string', 'enum' => [ 'Team', 'EnterpriseDirectory', 'EnterpriseLWA', 'EnterpriseOIDC', ], ], 'Address' => [ 'type' => 'structure', 'members' => [ 'streetName' => [ 'shape' => 'SensitiveNonEmptyString', ], 'streetSuffix' => [ 'shape' => 'SensitiveNonEmptyString', ], 'postDirectional' => [ 'shape' => 'SensitiveNonEmptyString', ], 'preDirectional' => [ 'shape' => 'SensitiveNonEmptyString', ], 'streetNumber' => [ 'shape' => 'SensitiveNonEmptyString', ], 'city' => [ 'shape' => 'SensitiveNonEmptyString', ], 'state' => [ 'shape' => 'SensitiveNonEmptyString', ], 'postalCode' => [ 'shape' => 'SensitiveNonEmptyString', ], 'postalCodePlus4' => [ 'shape' => 'SensitiveNonEmptyString', ], 'country' => [ 'shape' => 'SensitiveNonEmptyString', ], ], ], 'AlexaForBusinessMetadata' => [ 'type' => 'structure', 'members' => [ 'IsAlexaForBusinessEnabled' => [ 'shape' => 'Boolean', ], 'AlexaForBusinessRoomArn' => [ 'shape' => 'SensitiveString', ], ], ], 'Alpha2CountryCode' => [ 'type' => 'string', 'pattern' => '[A-Z]{2}', ], 'AppInstance' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AppInstanceAdmin' => [ 'type' => 'structure', 'members' => [ 'Admin' => [ 'shape' => 'Identity', ], 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AppInstanceAdminList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppInstanceAdminSummary', ], ], 'AppInstanceAdminSummary' => [ 'type' => 'structure', 'members' => [ 'Admin' => [ 'shape' => 'Identity', ], ], ], 'AppInstanceDataType' => [ 'type' => 'string', 'enum' => [ 'Channel', 'ChannelMessage', ], ], 'AppInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppInstanceSummary', ], ], 'AppInstanceRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelRetentionSettings' => [ 'shape' => 'ChannelRetentionSettings', ], ], ], 'AppInstanceStreamingConfiguration' => [ 'type' => 'structure', 'required' => [ 'AppInstanceDataType', 'ResourceArn', ], 'members' => [ 'AppInstanceDataType' => [ 'shape' => 'AppInstanceDataType', ], 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'AppInstanceStreamingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppInstanceStreamingConfiguration', ], 'max' => 2, 'min' => 1, ], 'AppInstanceSummary' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'AppInstanceUser' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'UserName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AppInstanceUserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppInstanceUserSummary', ], ], 'AppInstanceUserMembershipSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'ReadMarkerTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AppInstanceUserSummary' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'UserName', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'AreaCode' => [ 'type' => 'string', 'pattern' => '^$|^[0-9]{3,3}$', ], 'Arn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^arn[\\/\\:\\-\\_\\.a-zA-Z0-9]+$', 'sensitive' => true, ], 'ArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Audio', 'Video', 'Content', ], 'members' => [ 'Audio' => [ 'shape' => 'AudioArtifactsConfiguration', ], 'Video' => [ 'shape' => 'VideoArtifactsConfiguration', ], 'Content' => [ 'shape' => 'ContentArtifactsConfiguration', ], ], ], 'ArtifactsState' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'AssociatePhoneNumberWithUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', 'E164PhoneNumber', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'AssociatePhoneNumberWithUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociatePhoneNumbersWithVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorGroupId', 'E164PhoneNumbers', ], 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorGroupId', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], 'ForceAssociate' => [ 'shape' => 'NullableBoolean', ], ], ], 'AssociatePhoneNumbersWithVoiceConnectorGroupResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'AssociatePhoneNumbersWithVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'E164PhoneNumbers', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], 'ForceAssociate' => [ 'shape' => 'NullableBoolean', ], ], ], 'AssociatePhoneNumbersWithVoiceConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'AssociateSigninDelegateGroupsWithAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'SigninDelegateGroups', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'SigninDelegateGroups' => [ 'shape' => 'SigninDelegateGroupList', ], ], ], 'AssociateSigninDelegateGroupsWithAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'Attendee' => [ 'type' => 'structure', 'members' => [ 'ExternalUserId' => [ 'shape' => 'ExternalUserIdType', ], 'AttendeeId' => [ 'shape' => 'GuidString', ], 'JoinToken' => [ 'shape' => 'JoinTokenString', ], ], ], 'AttendeeIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuidString', ], 'min' => 1, ], 'AttendeeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attendee', ], ], 'AttendeeTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 10, 'min' => 1, ], 'AttendeeTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 10, 'min' => 1, ], 'AudioArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'MuxType', ], 'members' => [ 'MuxType' => [ 'shape' => 'AudioMuxType', ], ], ], 'AudioMuxType' => [ 'type' => 'string', 'enum' => [ 'AudioOnly', 'AudioWithActiveSpeakerVideo', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BatchChannelMemberships' => [ 'type' => 'structure', 'members' => [ 'InvitedBy' => [ 'shape' => 'Identity', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'Members' => [ 'shape' => 'Members', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'BatchCreateAttendeeErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAttendeeError', ], ], 'BatchCreateAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'Attendees', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'Attendees' => [ 'shape' => 'CreateAttendeeRequestItemList', ], ], ], 'BatchCreateAttendeeResponse' => [ 'type' => 'structure', 'members' => [ 'Attendees' => [ 'shape' => 'AttendeeList', ], 'Errors' => [ 'shape' => 'BatchCreateAttendeeErrorList', ], ], ], 'BatchCreateChannelMembershipError' => [ 'type' => 'structure', 'members' => [ 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'BatchCreateChannelMembershipErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateChannelMembershipError', ], ], 'BatchCreateChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArns', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'MemberArns' => [ 'shape' => 'MemberArns', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'BatchCreateChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'BatchChannelMemberships' => [ 'shape' => 'BatchChannelMemberships', ], 'Errors' => [ 'shape' => 'BatchCreateChannelMembershipErrors', ], ], ], 'BatchCreateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MembershipItemList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MembershipItemList' => [ 'shape' => 'MembershipItemList', ], ], ], 'BatchCreateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'MemberErrorList', ], ], ], 'BatchDeletePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberIds', ], 'members' => [ 'PhoneNumberIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'BatchDeletePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'BatchSuspendUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserIdList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserIdList' => [ 'shape' => 'UserIdList', ], ], ], 'BatchSuspendUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'BatchUnsuspendUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserIdList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserIdList' => [ 'shape' => 'UserIdList', ], ], ], 'BatchUnsuspendUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'BatchUpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'UpdatePhoneNumberRequestItems', ], 'members' => [ 'UpdatePhoneNumberRequestItems' => [ 'shape' => 'UpdatePhoneNumberRequestItemList', ], ], ], 'BatchUpdatePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'BatchUpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UpdateUserRequestItems', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UpdateUserRequestItems' => [ 'shape' => 'UpdateUserRequestItemList', ], ], ], 'BatchUpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserErrors' => [ 'shape' => 'UserErrorList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'Bot' => [ 'type' => 'structure', 'members' => [ 'BotId' => [ 'shape' => 'String', ], 'UserId' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'BotType' => [ 'shape' => 'BotType', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'BotEmail' => [ 'shape' => 'SensitiveString', ], 'SecurityToken' => [ 'shape' => 'SensitiveString', ], ], ], 'BotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bot', ], ], 'BotType' => [ 'type' => 'string', 'enum' => [ 'ChatBot', ], ], 'BusinessCallingSettings' => [ 'type' => 'structure', 'members' => [ 'CdrBucket' => [ 'shape' => 'String', 'box' => true, ], ], ], 'CallingName' => [ 'type' => 'string', 'pattern' => '^$|^[a-zA-Z0-9 ]{2,15}$', 'sensitive' => true, ], 'CallingNameStatus' => [ 'type' => 'string', 'enum' => [ 'Unassigned', 'UpdateInProgress', 'UpdateSucceeded', 'UpdateFailed', ], ], 'CallingRegion' => [ 'type' => 'string', ], 'CallingRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CallingRegion', ], ], 'CandidateAddress' => [ 'type' => 'structure', 'members' => [ 'streetInfo' => [ 'shape' => 'SensitiveNonEmptyString', ], 'streetNumber' => [ 'shape' => 'SensitiveNonEmptyString', ], 'city' => [ 'shape' => 'SensitiveNonEmptyString', ], 'state' => [ 'shape' => 'SensitiveNonEmptyString', ], 'postalCode' => [ 'shape' => 'SensitiveNonEmptyString', ], 'postalCodePlus4' => [ 'shape' => 'SensitiveNonEmptyString', ], 'country' => [ 'shape' => 'SensitiveNonEmptyString', ], ], ], 'CandidateAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CandidateAddress', ], ], 'Capability' => [ 'type' => 'string', 'enum' => [ 'Voice', 'SMS', ], ], 'CapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Capability', ], ], 'Channel' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'CreatedBy' => [ 'shape' => 'Identity', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastMessageTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ChannelBan' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'Identity', ], ], ], 'ChannelBanSummary' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], ], ], 'ChannelBanSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelBanSummary', ], ], 'ChannelMembership' => [ 'type' => 'structure', 'members' => [ 'InvitedBy' => [ 'shape' => 'Identity', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'Member' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ChannelMembershipForAppInstanceUserSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelSummary' => [ 'shape' => 'ChannelSummary', ], 'AppInstanceUserMembershipSummary' => [ 'shape' => 'AppInstanceUserMembershipSummary', ], ], ], 'ChannelMembershipForAppInstanceUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummary', ], ], 'ChannelMembershipSummary' => [ 'type' => 'structure', 'members' => [ 'Member' => [ 'shape' => 'Identity', ], ], ], 'ChannelMembershipSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMembershipSummary', ], ], 'ChannelMembershipType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'HIDDEN', ], ], 'ChannelMessage' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], 'Content' => [ 'shape' => 'Content', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastEditedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Sender' => [ 'shape' => 'Identity', ], 'Redacted' => [ 'shape' => 'NonNullableBoolean', ], 'Persistence' => [ 'shape' => 'ChannelMessagePersistenceType', ], ], ], 'ChannelMessagePersistenceType' => [ 'type' => 'string', 'enum' => [ 'PERSISTENT', 'NON_PERSISTENT', ], ], 'ChannelMessageSummary' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'MessageId', ], 'Content' => [ 'shape' => 'Content', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastEditedTimestamp' => [ 'shape' => 'Timestamp', ], 'Sender' => [ 'shape' => 'Identity', ], 'Redacted' => [ 'shape' => 'NonNullableBoolean', ], ], ], 'ChannelMessageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelMessageSummary', ], ], 'ChannelMessageType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'CONTROL', ], ], 'ChannelMode' => [ 'type' => 'string', 'enum' => [ 'UNRESTRICTED', 'RESTRICTED', ], ], 'ChannelModeratedByAppInstanceUserSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelSummary' => [ 'shape' => 'ChannelSummary', ], ], ], 'ChannelModeratedByAppInstanceUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummary', ], ], 'ChannelModerator' => [ 'type' => 'structure', 'members' => [ 'Moderator' => [ 'shape' => 'Identity', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'Identity', ], ], ], 'ChannelModeratorSummary' => [ 'type' => 'structure', 'members' => [ 'Moderator' => [ 'shape' => 'Identity', ], ], ], 'ChannelModeratorSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelModeratorSummary', ], ], 'ChannelPrivacy' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'ChannelRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'ChannelSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'LastMessageTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ChannelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSummary', ], ], 'ChimeArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 5, 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'ChimeSdkMeetingConfiguration' => [ 'type' => 'structure', 'members' => [ 'SourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'ArtifactsConfiguration' => [ 'shape' => 'ArtifactsConfiguration', ], ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '[-_a-zA-Z0-9]*', 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Content' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'ContentArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsState', ], 'MuxType' => [ 'shape' => 'ContentMuxType', ], ], ], 'ContentMuxType' => [ 'type' => 'string', 'enum' => [ 'ContentOnly', ], ], 'ConversationRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'Country' => [ 'type' => 'string', 'pattern' => '^$|^[A-Z]{2,2}$', ], 'CountryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Country', ], 'max' => 100, 'min' => 1, ], 'CpsLimit' => [ 'type' => 'integer', 'min' => 1, ], 'CreateAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AccountName', ], ], ], 'CreateAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'CreateAppInstanceAdminRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceAdminArn', 'AppInstanceArn', ], 'members' => [ 'AppInstanceAdminArn' => [ 'shape' => 'ChimeArn', ], 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'CreateAppInstanceAdminResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceAdmin' => [ 'shape' => 'Identity', ], 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], ], ], 'CreateAppInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ClientRequestToken', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAppInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], ], ], 'CreateAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'AppInstanceUserId', 'Name', 'ClientRequestToken', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'AppInstanceUserId' => [ 'shape' => 'UserId', ], 'Name' => [ 'shape' => 'UserName', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', ], ], ], 'CreateAttendeeError' => [ 'type' => 'structure', 'members' => [ 'ExternalUserId' => [ 'shape' => 'ExternalUserIdType', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'CreateAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'ExternalUserId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'ExternalUserId' => [ 'shape' => 'ExternalUserIdType', ], 'Tags' => [ 'shape' => 'AttendeeTagList', ], ], ], 'CreateAttendeeRequestItem' => [ 'type' => 'structure', 'required' => [ 'ExternalUserId', ], 'members' => [ 'ExternalUserId' => [ 'shape' => 'ExternalUserIdType', ], 'Tags' => [ 'shape' => 'AttendeeTagList', ], ], ], 'CreateAttendeeRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAttendeeRequestItem', ], ], 'CreateAttendeeResponse' => [ 'type' => 'structure', 'members' => [ 'Attendee' => [ 'shape' => 'Attendee', ], ], ], 'CreateBotRequest' => [ 'type' => 'structure', 'required' => [ 'DisplayName', 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'CreateChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelBanResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], ], ], 'CreateChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', 'Type', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'Member' => [ 'shape' => 'Identity', ], ], ], 'CreateChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelModeratorResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'ChannelModerator' => [ 'shape' => 'Identity', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'Name', 'ClientRequestToken', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'CreateMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceArn', 'SinkType', 'SinkArn', ], 'members' => [ 'SourceType' => [ 'shape' => 'MediaPipelineSourceType', ], 'SourceArn' => [ 'shape' => 'Arn', ], 'SinkType' => [ 'shape' => 'MediaPipelineSinkType', ], 'SinkArn' => [ 'shape' => 'Arn', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ChimeSdkMeetingConfiguration' => [ 'shape' => 'ChimeSdkMeetingConfiguration', ], ], ], 'CreateMediaCapturePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipeline' => [ 'shape' => 'MediaCapturePipeline', ], ], ], 'CreateMeetingDialOutRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'FromPhoneNumber', 'ToPhoneNumber', 'JoinToken', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'FromPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'ToPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'JoinToken' => [ 'shape' => 'JoinTokenString', ], ], ], 'CreateMeetingDialOutResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'GuidString', ], ], ], 'CreateMeetingRequest' => [ 'type' => 'structure', 'required' => [ 'ClientRequestToken', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ExternalMeetingId' => [ 'shape' => 'ExternalMeetingIdType', ], 'MeetingHostId' => [ 'shape' => 'ExternalUserIdType', ], 'MediaRegion' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'MeetingTagList', ], 'NotificationsConfiguration' => [ 'shape' => 'MeetingNotificationConfiguration', ], ], ], 'CreateMeetingResponse' => [ 'type' => 'structure', 'members' => [ 'Meeting' => [ 'shape' => 'Meeting', ], ], ], 'CreateMeetingWithAttendeesRequest' => [ 'type' => 'structure', 'required' => [ 'ClientRequestToken', ], 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ExternalMeetingId' => [ 'shape' => 'ExternalMeetingIdType', ], 'MeetingHostId' => [ 'shape' => 'ExternalUserIdType', ], 'MediaRegion' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'MeetingTagList', ], 'NotificationsConfiguration' => [ 'shape' => 'MeetingNotificationConfiguration', ], 'Attendees' => [ 'shape' => 'CreateMeetingWithAttendeesRequestItemList', ], ], ], 'CreateMeetingWithAttendeesRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAttendeeRequestItem', ], 'max' => 10, 'min' => 1, ], 'CreateMeetingWithAttendeesResponse' => [ 'type' => 'structure', 'members' => [ 'Meeting' => [ 'shape' => 'Meeting', ], 'Attendees' => [ 'shape' => 'AttendeeList', ], 'Errors' => [ 'shape' => 'BatchCreateAttendeeErrorList', ], ], ], 'CreatePhoneNumberOrderRequest' => [ 'type' => 'structure', 'required' => [ 'ProductType', 'E164PhoneNumbers', ], 'members' => [ 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], ], ], 'CreatePhoneNumberOrderResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrder' => [ 'shape' => 'PhoneNumberOrder', ], ], ], 'CreateProxySessionRequest' => [ 'type' => 'structure', 'required' => [ 'ParticipantPhoneNumbers', 'Capabilities', 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'ParticipantPhoneNumbers' => [ 'shape' => 'ParticipantPhoneNumberList', ], 'Name' => [ 'shape' => 'ProxySessionNameString', ], 'ExpiryMinutes' => [ 'shape' => 'PositiveInteger', ], 'Capabilities' => [ 'shape' => 'CapabilityList', ], 'NumberSelectionBehavior' => [ 'shape' => 'NumberSelectionBehavior', ], 'GeoMatchLevel' => [ 'shape' => 'GeoMatchLevel', ], 'GeoMatchParams' => [ 'shape' => 'GeoMatchParams', ], ], ], 'CreateProxySessionResponse' => [ 'type' => 'structure', 'members' => [ 'ProxySession' => [ 'shape' => 'ProxySession', ], ], ], 'CreateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'CreateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMembership' => [ 'shape' => 'RoomMembership', ], ], ], 'CreateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Name', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Name' => [ 'shape' => 'SensitiveString', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'CreateSipMediaApplicationCallRequest' => [ 'type' => 'structure', 'required' => [ 'FromPhoneNumber', 'ToPhoneNumber', 'SipMediaApplicationId', ], 'members' => [ 'FromPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'ToPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], 'SipHeaders' => [ 'shape' => 'SipHeadersMap', ], ], ], 'CreateSipMediaApplicationCallResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationCall' => [ 'shape' => 'SipMediaApplicationCall', ], ], ], 'CreateSipMediaApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'AwsRegion', 'Name', 'Endpoints', ], 'members' => [ 'AwsRegion' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'SipMediaApplicationName', ], 'Endpoints' => [ 'shape' => 'SipMediaApplicationEndpointList', ], ], ], 'CreateSipMediaApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplication' => [ 'shape' => 'SipMediaApplication', ], ], ], 'CreateSipRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'TriggerType', 'TriggerValue', 'TargetApplications', ], 'members' => [ 'Name' => [ 'shape' => 'SipRuleName', ], 'TriggerType' => [ 'shape' => 'SipRuleTriggerType', ], 'TriggerValue' => [ 'shape' => 'NonEmptyString', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], 'TargetApplications' => [ 'shape' => 'SipRuleTargetApplicationList', ], ], ], 'CreateSipRuleResponse' => [ 'type' => 'structure', 'members' => [ 'SipRule' => [ 'shape' => 'SipRule', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Username' => [ 'shape' => 'String', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'UserType' => [ 'shape' => 'UserType', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'CreateVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'VoiceConnectorGroupName', ], 'VoiceConnectorItems' => [ 'shape' => 'VoiceConnectorItemList', ], ], ], 'CreateVoiceConnectorGroupResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorGroup' => [ 'shape' => 'VoiceConnectorGroup', ], ], ], 'CreateVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RequireEncryption', ], 'members' => [ 'Name' => [ 'shape' => 'VoiceConnectorName', ], 'AwsRegion' => [ 'shape' => 'VoiceConnectorAwsRegion', ], 'RequireEncryption' => [ 'shape' => 'Boolean', ], ], ], 'CreateVoiceConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnector' => [ 'shape' => 'VoiceConnector', ], ], ], 'Credential' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'SensitiveString', ], 'Password' => [ 'shape' => 'SensitiveString', ], ], ], 'CredentialList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Credential', ], ], 'DNISEmergencyCallingConfiguration' => [ 'type' => 'structure', 'required' => [ 'EmergencyPhoneNumber', 'CallingCountry', ], 'members' => [ 'EmergencyPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'TestPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'CallingCountry' => [ 'shape' => 'Alpha2CountryCode', ], ], ], 'DNISEmergencyCallingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DNISEmergencyCallingConfiguration', ], ], 'DataRetentionInHours' => [ 'type' => 'integer', 'min' => 0, ], 'DeleteAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'DeleteAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAppInstanceAdminRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceAdminArn', 'AppInstanceArn', ], 'members' => [ 'AppInstanceAdminArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceAdminArn', ], 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DeleteAppInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DeleteAppInstanceStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DeleteAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceUserArn', ], 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceUserArn', ], ], ], 'DeleteAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'AttendeeId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'AttendeeId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'attendeeId', ], ], ], 'DeleteChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelModeratorArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DeleteEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DeleteMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'DeleteMeetingRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], ], ], 'DeletePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'DeleteProxySessionRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'ProxySessionId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'ProxySessionId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'proxySessionId', ], ], ], 'DeleteRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'memberId', ], ], ], 'DeleteRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], ], ], 'DeleteSipMediaApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], ], ], 'DeleteSipRuleRequest' => [ 'type' => 'structure', 'required' => [ 'SipRuleId', ], 'members' => [ 'SipRuleId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipRuleId', ], ], ], 'DeleteVoiceConnectorEmergencyCallingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DeleteVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorGroupId', ], 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorGroupId', ], ], ], 'DeleteVoiceConnectorOriginationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DeleteVoiceConnectorProxyRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DeleteVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DeleteVoiceConnectorStreamingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DeleteVoiceConnectorTerminationCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'Usernames', 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Usernames' => [ 'shape' => 'SensitiveStringList', ], ], ], 'DeleteVoiceConnectorTerminationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'DescribeAppInstanceAdminRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceAdminArn', 'AppInstanceArn', ], 'members' => [ 'AppInstanceAdminArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceAdminArn', ], 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DescribeAppInstanceAdminResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceAdmin' => [ 'shape' => 'AppInstanceAdmin', ], ], ], 'DescribeAppInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'DescribeAppInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstance' => [ 'shape' => 'AppInstance', ], ], ], 'DescribeAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceUserArn', ], 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceUserArn', ], ], ], 'DescribeAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUser' => [ 'shape' => 'AppInstanceUser', ], ], ], 'DescribeChannelBanRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelBanResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelBan' => [ 'shape' => 'ChannelBan', ], ], ], 'DescribeChannelMembershipForAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'AppInstanceUserArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelMembershipForAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMembership' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummary', ], ], ], 'DescribeChannelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MemberArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MemberArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'memberArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMembership' => [ 'shape' => 'ChannelMembership', ], ], ], 'DescribeChannelModeratedByAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'AppInstanceUserArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelModeratedByAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummary', ], ], ], 'DescribeChannelModeratorRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'ChannelModeratorArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChannelModeratorArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelModeratorArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelModeratorResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelModerator' => [ 'shape' => 'ChannelModerator', ], ], ], 'DescribeChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'DescribeChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', ], ], ], 'DisassociatePhoneNumberFromUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'DisassociatePhoneNumberFromUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociatePhoneNumbersFromVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorGroupId', 'E164PhoneNumbers', ], 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorGroupId', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], ], ], 'DisassociatePhoneNumbersFromVoiceConnectorGroupResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'DisassociatePhoneNumbersFromVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'E164PhoneNumbers', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], ], ], 'DisassociatePhoneNumbersFromVoiceConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberErrors' => [ 'shape' => 'PhoneNumberErrorList', ], ], ], 'DisassociateSigninDelegateGroupsFromAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'GroupNames', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'GroupNames' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'DisassociateSigninDelegateGroupsFromAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'E164PhoneNumber' => [ 'type' => 'string', 'pattern' => '^\\+?[1-9]\\d{1,14}$', 'sensitive' => true, ], 'E164PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'E164PhoneNumber', ], ], 'EmailAddress' => [ 'type' => 'string', 'pattern' => '.+@.+\\..+', 'sensitive' => true, ], 'EmailStatus' => [ 'type' => 'string', 'enum' => [ 'NotSent', 'Sent', 'Failed', ], ], 'EmergencyCallingConfiguration' => [ 'type' => 'structure', 'members' => [ 'DNIS' => [ 'shape' => 'DNISEmergencyCallingConfigurationList', ], ], ], 'EngineTranscribeMedicalSettings' => [ 'type' => 'structure', 'required' => [ 'LanguageCode', 'Specialty', 'Type', ], 'members' => [ 'LanguageCode' => [ 'shape' => 'TranscribeMedicalLanguageCode', ], 'Specialty' => [ 'shape' => 'TranscribeMedicalSpecialty', ], 'Type' => [ 'shape' => 'TranscribeMedicalType', ], 'VocabularyName' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'TranscribeMedicalRegion', ], 'ContentIdentificationType' => [ 'shape' => 'TranscribeMedicalContentIdentificationType', ], ], ], 'EngineTranscribeSettings' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => 'TranscribeLanguageCode', ], 'VocabularyFilterMethod' => [ 'shape' => 'TranscribeVocabularyFilterMethod', ], 'VocabularyFilterName' => [ 'shape' => 'String', ], 'VocabularyName' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'TranscribeRegion', ], 'EnablePartialResultsStabilization' => [ 'shape' => 'Boolean', ], 'PartialResultsStability' => [ 'shape' => 'TranscribePartialResultsStability', ], 'ContentIdentificationType' => [ 'shape' => 'TranscribeContentIdentificationType', ], 'ContentRedactionType' => [ 'shape' => 'TranscribeContentRedactionType', ], 'PiiEntityTypes' => [ 'shape' => 'TranscribePiiEntityTypes', ], 'LanguageModelName' => [ 'shape' => 'TranscribeLanguageModelName', ], 'IdentifyLanguage' => [ 'shape' => 'Boolean', ], 'LanguageOptions' => [ 'shape' => 'TranscribeLanguageOptions', ], 'PreferredLanguage' => [ 'shape' => 'TranscribeLanguageCode', ], 'VocabularyNames' => [ 'shape' => 'TranscribeVocabularyNamesOrFilterNamesString', ], 'VocabularyFilterNames' => [ 'shape' => 'TranscribeVocabularyNamesOrFilterNamesString', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'BadRequest', 'Conflict', 'Forbidden', 'NotFound', 'PreconditionFailed', 'ResourceLimitExceeded', 'ServiceFailure', 'AccessDenied', 'ServiceUnavailable', 'Throttled', 'Throttling', 'Unauthorized', 'Unprocessable', 'VoiceConnectorGroupAssociationsExist', 'PhoneNumberAssociationsExist', ], ], 'EventsConfiguration' => [ 'type' => 'structure', 'members' => [ 'BotId' => [ 'shape' => 'String', ], 'OutboundEventsHTTPSEndpoint' => [ 'shape' => 'SensitiveString', ], 'LambdaFunctionArn' => [ 'shape' => 'SensitiveString', ], ], ], 'ExternalMeetingIdType' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'sensitive' => true, ], 'ExternalUserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalUserIdType', ], 'min' => 1, ], 'ExternalUserIdType' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'sensitive' => true, ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'FunctionArn' => [ 'type' => 'string', 'max' => 10000, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?', 'sensitive' => true, ], 'GeoMatchLevel' => [ 'type' => 'string', 'enum' => [ 'Country', 'AreaCode', ], ], 'GeoMatchParams' => [ 'type' => 'structure', 'required' => [ 'Country', 'AreaCode', ], 'members' => [ 'Country' => [ 'shape' => 'Country', ], 'AreaCode' => [ 'shape' => 'AreaCode', ], ], ], 'GetAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'GetAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetAppInstanceRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'GetAppInstanceRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceRetentionSettings' => [ 'shape' => 'AppInstanceRetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetAppInstanceStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], ], ], 'GetAppInstanceStreamingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceStreamingConfigurations' => [ 'shape' => 'AppInstanceStreamingConfigurationList', ], ], ], 'GetAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'AttendeeId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'AttendeeId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'attendeeId', ], ], ], 'GetAttendeeResponse' => [ 'type' => 'structure', 'members' => [ 'Attendee' => [ 'shape' => 'Attendee', ], ], ], 'GetBotRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'GetBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'GetChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'GetChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMessage' => [ 'shape' => 'ChannelMessage', ], ], ], 'GetEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'GetEventsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventsConfiguration' => [ 'shape' => 'EventsConfiguration', ], ], ], 'GetGlobalSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'BusinessCalling' => [ 'shape' => 'BusinessCallingSettings', ], 'VoiceConnector' => [ 'shape' => 'VoiceConnectorSettings', ], ], ], 'GetMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'GetMediaCapturePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipeline' => [ 'shape' => 'MediaCapturePipeline', ], ], ], 'GetMeetingRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], ], ], 'GetMeetingResponse' => [ 'type' => 'structure', 'members' => [ 'Meeting' => [ 'shape' => 'Meeting', ], ], ], 'GetMessagingSessionEndpointRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMessagingSessionEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'MessagingSessionEndpoint', ], ], ], 'GetPhoneNumberOrderRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberOrderId', ], 'members' => [ 'PhoneNumberOrderId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'phoneNumberOrderId', ], ], ], 'GetPhoneNumberOrderResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrder' => [ 'shape' => 'PhoneNumberOrder', ], ], ], 'GetPhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'GetPhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'GetPhoneNumberSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'CallingName' => [ 'shape' => 'CallingName', ], 'CallingNameUpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'GetProxySessionRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'ProxySessionId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'ProxySessionId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'proxySessionId', ], ], ], 'GetProxySessionResponse' => [ 'type' => 'structure', 'members' => [ 'ProxySession' => [ 'shape' => 'ProxySession', ], ], ], 'GetRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], ], ], 'GetRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'GetRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], ], ], 'GetRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'GetSipMediaApplicationLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], ], ], 'GetSipMediaApplicationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationLoggingConfiguration' => [ 'shape' => 'SipMediaApplicationLoggingConfiguration', ], ], ], 'GetSipMediaApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], ], ], 'GetSipMediaApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplication' => [ 'shape' => 'SipMediaApplication', ], ], ], 'GetSipRuleRequest' => [ 'type' => 'structure', 'required' => [ 'SipRuleId', ], 'members' => [ 'SipRuleId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipRuleId', ], ], ], 'GetSipRuleResponse' => [ 'type' => 'structure', 'members' => [ 'SipRule' => [ 'shape' => 'SipRule', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'GetUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'GetVoiceConnectorEmergencyCallingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorEmergencyCallingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EmergencyCallingConfiguration' => [ 'shape' => 'EmergencyCallingConfiguration', ], ], ], 'GetVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorGroupId', ], 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorGroupId', ], ], ], 'GetVoiceConnectorGroupResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorGroup' => [ 'shape' => 'VoiceConnectorGroup', ], ], ], 'GetVoiceConnectorLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'GetVoiceConnectorOriginationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorOriginationResponse' => [ 'type' => 'structure', 'members' => [ 'Origination' => [ 'shape' => 'Origination', ], ], ], 'GetVoiceConnectorProxyRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorProxyResponse' => [ 'type' => 'structure', 'members' => [ 'Proxy' => [ 'shape' => 'Proxy', ], ], ], 'GetVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnector' => [ 'shape' => 'VoiceConnector', ], ], ], 'GetVoiceConnectorStreamingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorStreamingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'StreamingConfiguration' => [ 'shape' => 'StreamingConfiguration', ], ], ], 'GetVoiceConnectorTerminationHealthRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorTerminationHealthResponse' => [ 'type' => 'structure', 'members' => [ 'TerminationHealth' => [ 'shape' => 'TerminationHealth', ], ], ], 'GetVoiceConnectorTerminationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'GetVoiceConnectorTerminationResponse' => [ 'type' => 'structure', 'members' => [ 'Termination' => [ 'shape' => 'Termination', ], ], ], 'GuidString' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}', ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ChimeArn', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'Invite' => [ 'type' => 'structure', 'members' => [ 'InviteId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'InviteStatus', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'EmailStatus' => [ 'shape' => 'EmailStatus', ], ], ], 'InviteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invite', ], ], 'InviteStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Accepted', 'Failed', ], ], 'InviteUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserEmailList', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserEmailList' => [ 'shape' => 'UserEmailList', ], 'UserType' => [ 'shape' => 'UserType', ], ], ], 'InviteUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Invites' => [ 'shape' => 'InviteList', ], ], ], 'Iso8601Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'JoinTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 2, 'pattern' => '^[a-zA-Z0-9+/]+$', 'sensitive' => true, ], 'License' => [ 'type' => 'string', 'enum' => [ 'Basic', 'Plus', 'Pro', 'ProTrial', ], ], 'LicenseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'License', ], ], 'ListAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AccountName', 'location' => 'querystring', 'locationName' => 'name', ], 'UserEmail' => [ 'shape' => 'EmailAddress', 'location' => 'querystring', 'locationName' => 'user-email', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ProfileServiceMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'AccountList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListAppInstanceAdminsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListAppInstanceAdminsResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'AppInstanceAdmins' => [ 'shape' => 'AppInstanceAdminList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppInstanceUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListAppInstanceUsersResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], 'AppInstanceUsers' => [ 'shape' => 'AppInstanceUserList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListAppInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstances' => [ 'shape' => 'AppInstanceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAttendeeTagsRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'AttendeeId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'AttendeeId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'attendeeId', ], ], ], 'ListAttendeeTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListAttendeesRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListAttendeesResponse' => [ 'type' => 'structure', 'members' => [ 'Attendees' => [ 'shape' => 'AttendeeList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'Bots' => [ 'shape' => 'BotList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListChannelBansRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelBansResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelBans' => [ 'shape' => 'ChannelBanSummaryList', ], ], ], 'ListChannelMembershipsForAppInstanceUserRequest' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelMembershipsForAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelMemberships' => [ 'shape' => 'ChannelMembershipForAppInstanceUserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelMembershipsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Type' => [ 'shape' => 'ChannelMembershipType', 'location' => 'querystring', 'locationName' => 'type', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'ChannelMemberships' => [ 'shape' => 'ChannelMembershipSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'SortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sort-order', ], 'NotBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'not-before', ], 'NotAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'not-after', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelMessages' => [ 'shape' => 'ChannelMessageSummaryList', ], ], ], 'ListChannelModeratorsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelModeratorsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ChannelModerators' => [ 'shape' => 'ChannelModeratorSummaryList', ], ], ], 'ListChannelsModeratedByAppInstanceUserRequest' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-user-arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelsModeratedByAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelModeratedByAppInstanceUserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'querystring', 'locationName' => 'app-instance-arn', ], 'Privacy' => [ 'shape' => 'ChannelPrivacy', 'location' => 'querystring', 'locationName' => 'privacy', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'ChannelSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMediaCapturePipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMediaCapturePipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipelines' => [ 'shape' => 'MediaCapturePipelineList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListMeetingTagsRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], ], ], 'ListMeetingTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListMeetingsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMeetingsResponse' => [ 'type' => 'structure', 'members' => [ 'Meetings' => [ 'shape' => 'MeetingList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPhoneNumberOrdersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListPhoneNumberOrdersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrders' => [ 'shape' => 'PhoneNumberOrderList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListPhoneNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PhoneNumberStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', 'location' => 'querystring', 'locationName' => 'product-type', ], 'FilterName' => [ 'shape' => 'PhoneNumberAssociationName', 'location' => 'querystring', 'locationName' => 'filter-name', ], 'FilterValue' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'filter-value', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListPhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListProxySessionsRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Status' => [ 'shape' => 'ProxySessionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'NextToken' => [ 'shape' => 'NextTokenString', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListProxySessionsResponse' => [ 'type' => 'structure', 'members' => [ 'ProxySessions' => [ 'shape' => 'ProxySessions', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListRoomMembershipsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRoomMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMemberships' => [ 'shape' => 'RoomMembershipList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListRoomsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'MemberId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'member-id', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListRoomsResponse' => [ 'type' => 'structure', 'members' => [ 'Rooms' => [ 'shape' => 'RoomList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSipMediaApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextTokenString', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListSipMediaApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplications' => [ 'shape' => 'SipMediaApplicationList', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListSipRulesRequest' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'sip-media-application', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'NextTokenString', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListSipRulesResponse' => [ 'type' => 'structure', 'members' => [ 'SipRules' => [ 'shape' => 'SipRuleList', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListSupportedPhoneNumberCountriesRequest' => [ 'type' => 'structure', 'required' => [ 'ProductType', ], 'members' => [ 'ProductType' => [ 'shape' => 'PhoneNumberProductType', 'location' => 'querystring', 'locationName' => 'product-type', ], ], ], 'ListSupportedPhoneNumberCountriesResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberCountries' => [ 'shape' => 'PhoneNumberCountriesList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserEmail' => [ 'shape' => 'EmailAddress', 'location' => 'querystring', 'locationName' => 'user-email', ], 'UserType' => [ 'shape' => 'UserType', 'location' => 'querystring', 'locationName' => 'user-type', ], 'MaxResults' => [ 'shape' => 'ProfileServiceMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListVoiceConnectorGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListVoiceConnectorGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorGroups' => [ 'shape' => 'VoiceConnectorGroupList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListVoiceConnectorTerminationCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], ], ], 'ListVoiceConnectorTerminationCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'Usernames' => [ 'shape' => 'SensitiveStringList', ], ], ], 'ListVoiceConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListVoiceConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectors' => [ 'shape' => 'VoiceConnectorList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'LoggingConfiguration' => [ 'type' => 'structure', 'members' => [ 'EnableSIPLogs' => [ 'shape' => 'Boolean', ], 'EnableMediaMetricLogs' => [ 'shape' => 'Boolean', ], ], ], 'LogoutUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'LogoutUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MediaCapturePipeline' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'SourceType' => [ 'shape' => 'MediaPipelineSourceType', ], 'SourceArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'SinkType' => [ 'shape' => 'MediaPipelineSinkType', ], 'SinkArn' => [ 'shape' => 'Arn', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'ChimeSdkMeetingConfiguration' => [ 'shape' => 'ChimeSdkMeetingConfiguration', ], ], ], 'MediaCapturePipelineList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaCapturePipeline', ], ], 'MediaPipelineSinkType' => [ 'type' => 'string', 'enum' => [ 'S3Bucket', ], ], 'MediaPipelineSourceType' => [ 'type' => 'string', 'enum' => [ 'ChimeSdkMeeting', ], ], 'MediaPipelineStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'InProgress', 'Failed', 'Stopping', 'Stopped', ], ], 'MediaPlacement' => [ 'type' => 'structure', 'members' => [ 'AudioHostUrl' => [ 'shape' => 'UriType', ], 'AudioFallbackUrl' => [ 'shape' => 'UriType', ], 'ScreenDataUrl' => [ 'shape' => 'UriType', ], 'ScreenSharingUrl' => [ 'shape' => 'UriType', ], 'ScreenViewingUrl' => [ 'shape' => 'UriType', ], 'SignalingUrl' => [ 'shape' => 'UriType', ], 'TurnControlUrl' => [ 'shape' => 'UriType', ], 'EventIngestionUrl' => [ 'shape' => 'UriType', ], ], ], 'Meeting' => [ 'type' => 'structure', 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', ], 'ExternalMeetingId' => [ 'shape' => 'ExternalMeetingIdType', ], 'MediaPlacement' => [ 'shape' => 'MediaPlacement', ], 'MediaRegion' => [ 'shape' => 'String', ], ], ], 'MeetingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Meeting', ], ], 'MeetingNotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'SnsTopicArn' => [ 'shape' => 'Arn', ], 'SqsQueueArn' => [ 'shape' => 'Arn', ], ], ], 'MeetingTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'MeetingTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'MemberType' => [ 'shape' => 'MemberType', ], 'Email' => [ 'shape' => 'SensitiveString', ], 'FullName' => [ 'shape' => 'SensitiveString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], ], ], 'MemberArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeArn', ], 'max' => 100, 'min' => 1, ], 'MemberError' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'MemberErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberError', ], ], 'MemberType' => [ 'type' => 'string', 'enum' => [ 'User', 'Bot', 'Webhook', ], ], 'Members' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identity', ], ], 'MembershipItem' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'MembershipItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MembershipItem', ], 'max' => 50, ], 'MessageId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-_a-zA-Z0-9]*', ], 'MessagingSessionEndpoint' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'UrlType', ], ], ], 'Metadata' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'NextTokenString' => [ 'type' => 'string', 'max' => 65535, ], 'NonEmptyContent' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'NonEmptyResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', 'sensitive' => true, ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NonEmptyString128' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'NonNullableBoolean' => [ 'type' => 'boolean', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NotificationTarget' => [ 'type' => 'string', 'enum' => [ 'EventBridge', 'SNS', 'SQS', ], ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'NumberSelectionBehavior' => [ 'type' => 'string', 'enum' => [ 'PreferSticky', 'AvoidSticky', ], ], 'OrderedPhoneNumber' => [ 'type' => 'structure', 'members' => [ 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'Status' => [ 'shape' => 'OrderedPhoneNumberStatus', ], ], ], 'OrderedPhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderedPhoneNumber', ], ], 'OrderedPhoneNumberStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Acquired', 'Failed', ], ], 'Origination' => [ 'type' => 'structure', 'members' => [ 'Routes' => [ 'shape' => 'OriginationRouteList', ], 'Disabled' => [ 'shape' => 'Boolean', ], ], ], 'OriginationRoute' => [ 'type' => 'structure', 'members' => [ 'Host' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Port', ], 'Protocol' => [ 'shape' => 'OriginationRouteProtocol', ], 'Priority' => [ 'shape' => 'OriginationRoutePriority', ], 'Weight' => [ 'shape' => 'OriginationRouteWeight', ], ], ], 'OriginationRouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginationRoute', ], ], 'OriginationRoutePriority' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'OriginationRouteProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'OriginationRouteWeight' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Participant' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'ProxyPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'ParticipantPhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'E164PhoneNumber', ], 'max' => 2, 'min' => 2, ], 'Participants' => [ 'type' => 'list', 'member' => [ 'shape' => 'Participant', ], ], 'PhoneNumber' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', ], 'E164PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'Country' => [ 'shape' => 'Alpha2CountryCode', ], 'Type' => [ 'shape' => 'PhoneNumberType', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'Status' => [ 'shape' => 'PhoneNumberStatus', ], 'Capabilities' => [ 'shape' => 'PhoneNumberCapabilities', ], 'Associations' => [ 'shape' => 'PhoneNumberAssociationList', ], 'CallingName' => [ 'shape' => 'CallingName', ], 'CallingNameStatus' => [ 'shape' => 'CallingNameStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'DeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberAssociation' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'PhoneNumberAssociationName', ], 'AssociatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberAssociation', ], ], 'PhoneNumberAssociationName' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'UserId', 'VoiceConnectorId', 'VoiceConnectorGroupId', 'SipRuleId', ], ], 'PhoneNumberCapabilities' => [ 'type' => 'structure', 'members' => [ 'InboundCall' => [ 'shape' => 'NullableBoolean', ], 'OutboundCall' => [ 'shape' => 'NullableBoolean', ], 'InboundSMS' => [ 'shape' => 'NullableBoolean', ], 'OutboundSMS' => [ 'shape' => 'NullableBoolean', ], 'InboundMMS' => [ 'shape' => 'NullableBoolean', ], 'OutboundMMS' => [ 'shape' => 'NullableBoolean', ], ], ], 'PhoneNumberCountriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberCountry', ], ], 'PhoneNumberCountry' => [ 'type' => 'structure', 'members' => [ 'CountryCode' => [ 'shape' => 'Alpha2CountryCode', ], 'SupportedPhoneNumberTypes' => [ 'shape' => 'PhoneNumberTypeList', ], ], ], 'PhoneNumberError' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'PhoneNumberErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberError', ], ], 'PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], ], 'PhoneNumberMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'PhoneNumberOrder' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberOrderId' => [ 'shape' => 'GuidString', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'Status' => [ 'shape' => 'PhoneNumberOrderStatus', ], 'OrderedPhoneNumbers' => [ 'shape' => 'OrderedPhoneNumberList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PhoneNumberOrderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberOrder', ], ], 'PhoneNumberOrderStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Successful', 'Failed', 'Partial', ], ], 'PhoneNumberProductType' => [ 'type' => 'string', 'enum' => [ 'BusinessCalling', 'VoiceConnector', 'SipMediaApplicationDialIn', ], ], 'PhoneNumberStatus' => [ 'type' => 'string', 'enum' => [ 'AcquireInProgress', 'AcquireFailed', 'Unassigned', 'Assigned', 'ReleaseInProgress', 'DeleteInProgress', 'ReleaseFailed', 'DeleteFailed', ], ], 'PhoneNumberType' => [ 'type' => 'string', 'enum' => [ 'Local', 'TollFree', ], ], 'PhoneNumberTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberType', ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'PositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'ProfileServiceMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'Proxy' => [ 'type' => 'structure', 'members' => [ 'DefaultSessionExpiryMinutes' => [ 'shape' => 'Integer', ], 'Disabled' => [ 'shape' => 'Boolean', ], 'FallBackPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'PhoneNumberCountries' => [ 'shape' => 'StringList', ], ], ], 'ProxySession' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', ], 'ProxySessionId' => [ 'shape' => 'NonEmptyString128', ], 'Name' => [ 'shape' => 'String128', ], 'Status' => [ 'shape' => 'ProxySessionStatus', ], 'ExpiryMinutes' => [ 'shape' => 'PositiveInteger', ], 'Capabilities' => [ 'shape' => 'CapabilityList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'EndedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'Participants' => [ 'shape' => 'Participants', ], 'NumberSelectionBehavior' => [ 'shape' => 'NumberSelectionBehavior', ], 'GeoMatchLevel' => [ 'shape' => 'GeoMatchLevel', ], 'GeoMatchParams' => [ 'shape' => 'GeoMatchParams', ], ], ], 'ProxySessionNameString' => [ 'type' => 'string', 'pattern' => '^$|^[a-zA-Z0-9 ]{0,30}$', 'sensitive' => true, ], 'ProxySessionStatus' => [ 'type' => 'string', 'enum' => [ 'Open', 'InProgress', 'Closed', ], ], 'ProxySessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProxySession', ], ], 'PutAppInstanceRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'AppInstanceRetentionSettings', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], 'AppInstanceRetentionSettings' => [ 'shape' => 'AppInstanceRetentionSettings', ], ], ], 'PutAppInstanceRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceRetentionSettings' => [ 'shape' => 'AppInstanceRetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'PutAppInstanceStreamingConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'AppInstanceStreamingConfigurations', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], 'AppInstanceStreamingConfigurations' => [ 'shape' => 'AppInstanceStreamingConfigurationList', ], ], ], 'PutAppInstanceStreamingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceStreamingConfigurations' => [ 'shape' => 'AppInstanceStreamingConfigurationList', ], ], ], 'PutEventsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], 'OutboundEventsHTTPSEndpoint' => [ 'shape' => 'SensitiveString', ], 'LambdaFunctionArn' => [ 'shape' => 'SensitiveString', ], ], ], 'PutEventsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventsConfiguration' => [ 'shape' => 'EventsConfiguration', ], ], ], 'PutRetentionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RetentionSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], ], ], 'PutRetentionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionSettings' => [ 'shape' => 'RetentionSettings', ], 'InitiateDeletionTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'PutSipMediaApplicationLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], 'SipMediaApplicationLoggingConfiguration' => [ 'shape' => 'SipMediaApplicationLoggingConfiguration', ], ], ], 'PutSipMediaApplicationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationLoggingConfiguration' => [ 'shape' => 'SipMediaApplicationLoggingConfiguration', ], ], ], 'PutVoiceConnectorEmergencyCallingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'EmergencyCallingConfiguration', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'EmergencyCallingConfiguration' => [ 'shape' => 'EmergencyCallingConfiguration', ], ], ], 'PutVoiceConnectorEmergencyCallingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EmergencyCallingConfiguration' => [ 'shape' => 'EmergencyCallingConfiguration', ], ], ], 'PutVoiceConnectorLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'LoggingConfiguration', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutVoiceConnectorLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutVoiceConnectorOriginationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'Origination', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Origination' => [ 'shape' => 'Origination', ], ], ], 'PutVoiceConnectorOriginationResponse' => [ 'type' => 'structure', 'members' => [ 'Origination' => [ 'shape' => 'Origination', ], ], ], 'PutVoiceConnectorProxyRequest' => [ 'type' => 'structure', 'required' => [ 'DefaultSessionExpiryMinutes', 'PhoneNumberPoolCountries', 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'DefaultSessionExpiryMinutes' => [ 'shape' => 'Integer', ], 'PhoneNumberPoolCountries' => [ 'shape' => 'CountryList', ], 'FallBackPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'Disabled' => [ 'shape' => 'Boolean', ], ], ], 'PutVoiceConnectorProxyResponse' => [ 'type' => 'structure', 'members' => [ 'Proxy' => [ 'shape' => 'Proxy', ], ], ], 'PutVoiceConnectorStreamingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'StreamingConfiguration', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'StreamingConfiguration' => [ 'shape' => 'StreamingConfiguration', ], ], ], 'PutVoiceConnectorStreamingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'StreamingConfiguration' => [ 'shape' => 'StreamingConfiguration', ], ], ], 'PutVoiceConnectorTerminationCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Credentials' => [ 'shape' => 'CredentialList', ], ], ], 'PutVoiceConnectorTerminationRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'Termination', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Termination' => [ 'shape' => 'Termination', ], ], ], 'PutVoiceConnectorTerminationResponse' => [ 'type' => 'structure', 'members' => [ 'Termination' => [ 'shape' => 'Termination', ], ], ], 'RedactChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'RedactChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], ], ], 'RedactConversationMessageRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ConversationId', 'MessageId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'ConversationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'conversationId', ], 'MessageId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'messageId', ], ], ], 'RedactConversationMessageResponse' => [ 'type' => 'structure', 'members' => [], ], 'RedactRoomMessageRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MessageId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MessageId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'messageId', ], ], ], 'RedactRoomMessageResponse' => [ 'type' => 'structure', 'members' => [], ], 'RegenerateSecurityTokenRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'RegenerateSecurityTokenResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'Unregistered', 'Registered', 'Suspended', ], ], 'ResetPersonalPINRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'ResetPersonalPINResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', 'sensitive' => true, ], 'RestorePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], ], ], 'RestorePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'ResultMax' => [ 'type' => 'integer', 'max' => 99, 'min' => 1, ], 'RetentionDays' => [ 'type' => 'integer', 'max' => 5475, 'min' => 1, ], 'RetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RoomRetentionSettings' => [ 'shape' => 'RoomRetentionSettings', ], 'ConversationRetentionSettings' => [ 'shape' => 'ConversationRetentionSettings', ], ], ], 'Room' => [ 'type' => 'structure', 'members' => [ 'RoomId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'SensitiveString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'CreatedBy' => [ 'shape' => 'NonEmptyString', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'RoomList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Room', ], ], 'RoomMembership' => [ 'type' => 'structure', 'members' => [ 'RoomId' => [ 'shape' => 'NonEmptyString', ], 'Member' => [ 'shape' => 'Member', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], 'InvitedBy' => [ 'shape' => 'NonEmptyString', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'RoomMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomMembership', ], ], 'RoomMembershipRole' => [ 'type' => 'string', 'enum' => [ 'Administrator', 'Member', ], ], 'RoomRetentionSettings' => [ 'type' => 'structure', 'members' => [ 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'SMAUpdateCallArgumentsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'SensitiveString', ], 'value' => [ 'shape' => 'SensitiveString', ], 'max' => 20, 'min' => 0, ], 'SearchAvailablePhoneNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'AreaCode' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'area-code', ], 'City' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'city', ], 'Country' => [ 'shape' => 'Alpha2CountryCode', 'location' => 'querystring', 'locationName' => 'country', ], 'State' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'state', ], 'TollFreePrefix' => [ 'shape' => 'TollFreePrefix', 'location' => 'querystring', 'locationName' => 'toll-free-prefix', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', 'location' => 'querystring', 'locationName' => 'phone-number-type', ], 'MaxResults' => [ 'shape' => 'PhoneNumberMaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'SearchAvailablePhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'E164PhoneNumbers' => [ 'shape' => 'E164PhoneNumberList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'SelectedVideoStreams' => [ 'type' => 'structure', 'members' => [ 'AttendeeIds' => [ 'shape' => 'AttendeeIdList', ], 'ExternalUserIds' => [ 'shape' => 'ExternalUserIdList', ], ], ], 'SendChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'Content', 'Type', 'Persistence', 'ClientRequestToken', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Content' => [ 'shape' => 'NonEmptyContent', ], 'Type' => [ 'shape' => 'ChannelMessageType', ], 'Persistence' => [ 'shape' => 'ChannelMessagePersistenceType', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'SendChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], ], ], 'SensitiveNonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'SensitiveStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveString', ], ], 'ServiceFailureException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SigninDelegateGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], ], ], 'SigninDelegateGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SigninDelegateGroup', ], ], 'SipApplicationPriority' => [ 'type' => 'integer', 'min' => 1, ], 'SipHeadersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'SensitiveString', ], 'value' => [ 'shape' => 'SensitiveString', ], 'max' => 20, 'min' => 0, ], 'SipMediaApplication' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', ], 'AwsRegion' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'SipMediaApplicationName', ], 'Endpoints' => [ 'shape' => 'SipMediaApplicationEndpointList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'SipMediaApplicationCall' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'GuidString', ], ], ], 'SipMediaApplicationEndpoint' => [ 'type' => 'structure', 'members' => [ 'LambdaArn' => [ 'shape' => 'FunctionArn', ], ], ], 'SipMediaApplicationEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SipMediaApplicationEndpoint', ], 'max' => 1, 'min' => 1, ], 'SipMediaApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SipMediaApplication', ], ], 'SipMediaApplicationLoggingConfiguration' => [ 'type' => 'structure', 'members' => [ 'EnableSipMediaApplicationMessageLogs' => [ 'shape' => 'Boolean', ], ], ], 'SipMediaApplicationName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SipRule' => [ 'type' => 'structure', 'members' => [ 'SipRuleId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'SipRuleName', ], 'Disabled' => [ 'shape' => 'Boolean', ], 'TriggerType' => [ 'shape' => 'SipRuleTriggerType', ], 'TriggerValue' => [ 'shape' => 'NonEmptyString', ], 'TargetApplications' => [ 'shape' => 'SipRuleTargetApplicationList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'SipRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SipRule', ], ], 'SipRuleName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SipRuleTargetApplication' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', ], 'Priority' => [ 'shape' => 'SipApplicationPriority', ], 'AwsRegion' => [ 'shape' => 'String', ], ], ], 'SipRuleTargetApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SipRuleTargetApplication', ], 'max' => 25, 'min' => 1, ], 'SipRuleTriggerType' => [ 'type' => 'string', 'enum' => [ 'ToPhoneNumber', 'RequestUriHostname', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'SelectedVideoStreams' => [ 'shape' => 'SelectedVideoStreams', ], ], ], 'StartMeetingTranscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'TranscriptionConfiguration', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'TranscriptionConfiguration' => [ 'shape' => 'TranscriptionConfiguration', ], ], ], 'StartMeetingTranscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopMeetingTranscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], ], ], 'StopMeetingTranscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StreamingConfiguration' => [ 'type' => 'structure', 'required' => [ 'DataRetentionInHours', ], 'members' => [ 'DataRetentionInHours' => [ 'shape' => 'DataRetentionInHours', ], 'Disabled' => [ 'shape' => 'Boolean', ], 'StreamingNotificationTargets' => [ 'shape' => 'StreamingNotificationTargetList', ], ], ], 'StreamingNotificationTarget' => [ 'type' => 'structure', 'required' => [ 'NotificationTarget', ], 'members' => [ 'NotificationTarget' => [ 'shape' => 'NotificationTarget', ], ], ], 'StreamingNotificationTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingNotificationTarget', ], 'max' => 3, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'String128' => [ 'type' => 'string', 'max' => 128, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'AttendeeId', 'Tags', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'AttendeeId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'attendeeId', ], 'Tags' => [ 'shape' => 'AttendeeTagList', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TagMeetingRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'Tags', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'Tags' => [ 'shape' => 'MeetingTagList', ], ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'TelephonySettings' => [ 'type' => 'structure', 'required' => [ 'InboundCalling', 'OutboundCalling', 'SMS', ], 'members' => [ 'InboundCalling' => [ 'shape' => 'Boolean', ], 'OutboundCalling' => [ 'shape' => 'Boolean', ], 'SMS' => [ 'shape' => 'Boolean', ], ], ], 'Termination' => [ 'type' => 'structure', 'members' => [ 'CpsLimit' => [ 'shape' => 'CpsLimit', ], 'DefaultPhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'CallingRegions' => [ 'shape' => 'CallingRegionList', ], 'CidrAllowedList' => [ 'shape' => 'StringList', ], 'Disabled' => [ 'shape' => 'Boolean', ], ], ], 'TerminationHealth' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'Source' => [ 'shape' => 'String', ], ], ], 'ThrottledClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TollFreePrefix' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '^8(00|33|44|55|66|77|88)$', ], 'TranscribeContentIdentificationType' => [ 'type' => 'string', 'enum' => [ 'PII', ], ], 'TranscribeContentRedactionType' => [ 'type' => 'string', 'enum' => [ 'PII', ], ], 'TranscribeLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', 'en-GB', 'es-US', 'fr-CA', 'fr-FR', 'en-AU', 'it-IT', 'de-DE', 'pt-BR', 'ja-JP', 'ko-KR', 'zh-CN', 'th-TH', 'hi-IN', ], ], 'TranscribeLanguageModelName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'TranscribeLanguageOptions' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[a-zA-Z-,]+', ], 'TranscribeMedicalContentIdentificationType' => [ 'type' => 'string', 'enum' => [ 'PHI', ], ], 'TranscribeMedicalLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', ], ], 'TranscribeMedicalRegion' => [ 'type' => 'string', 'enum' => [ 'us-east-1', 'us-east-2', 'us-west-2', 'ap-southeast-2', 'ca-central-1', 'eu-west-1', 'auto', ], ], 'TranscribeMedicalSpecialty' => [ 'type' => 'string', 'enum' => [ 'PRIMARYCARE', 'CARDIOLOGY', 'NEUROLOGY', 'ONCOLOGY', 'RADIOLOGY', 'UROLOGY', ], ], 'TranscribeMedicalType' => [ 'type' => 'string', 'enum' => [ 'CONVERSATION', 'DICTATION', ], ], 'TranscribePartialResultsStability' => [ 'type' => 'string', 'enum' => [ 'low', 'medium', 'high', ], ], 'TranscribePiiEntityTypes' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[A-Z_, ]+', ], 'TranscribeRegion' => [ 'type' => 'string', 'enum' => [ 'us-east-2', 'us-east-1', 'us-west-2', 'ap-northeast-2', 'ap-southeast-2', 'ap-northeast-1', 'ca-central-1', 'eu-central-1', 'eu-west-1', 'eu-west-2', 'sa-east-1', 'auto', ], ], 'TranscribeVocabularyFilterMethod' => [ 'type' => 'string', 'enum' => [ 'remove', 'mask', 'tag', ], ], 'TranscribeVocabularyNamesOrFilterNamesString' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9,-._]+', ], 'TranscriptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'EngineTranscribeSettings' => [ 'shape' => 'EngineTranscribeSettings', ], 'EngineTranscribeMedicalSettings' => [ 'shape' => 'EngineTranscribeMedicalSettings', ], ], ], 'UnauthorizedClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UnprocessableEntityException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 422, ], 'exception' => true, ], 'UntagAttendeeRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'TagKeys', 'AttendeeId', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'AttendeeId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'attendeeId', ], 'TagKeys' => [ 'shape' => 'AttendeeTagKeyList', ], ], ], 'UntagMeetingRequest' => [ 'type' => 'structure', 'required' => [ 'MeetingId', 'TagKeys', ], 'members' => [ 'MeetingId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'meetingId', ], 'TagKeys' => [ 'shape' => 'MeetingTagKeyList', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'Name' => [ 'shape' => 'AccountName', ], 'DefaultLicense' => [ 'shape' => 'License', ], ], ], 'UpdateAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], ], ], 'UpdateAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'AccountSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'UpdateAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceArn', 'Name', ], 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'UpdateAppInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateAppInstanceUserRequest' => [ 'type' => 'structure', 'required' => [ 'AppInstanceUserArn', 'Name', ], 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'appInstanceUserArn', ], 'Name' => [ 'shape' => 'UserName', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'UpdateAppInstanceUserResponse' => [ 'type' => 'structure', 'members' => [ 'AppInstanceUserArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateBotRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'BotId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'BotId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'botId', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'UpdateBotResponse' => [ 'type' => 'structure', 'members' => [ 'Bot' => [ 'shape' => 'Bot', ], ], ], 'UpdateChannelMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'MessageId', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'Content' => [ 'shape' => 'Content', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'UpdateChannelMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], 'MessageId' => [ 'shape' => 'MessageId', ], ], ], 'UpdateChannelReadMarkerRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'UpdateChannelReadMarkerResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelArn', 'Name', 'Mode', ], 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'Name' => [ 'shape' => 'NonEmptyResourceName', ], 'Mode' => [ 'shape' => 'ChannelMode', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'ChimeBearer' => [ 'shape' => 'ChimeArn', 'location' => 'header', 'locationName' => 'x-amz-chime-bearer', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelArn' => [ 'shape' => 'ChimeArn', ], ], ], 'UpdateGlobalSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'BusinessCalling' => [ 'shape' => 'BusinessCallingSettings', ], 'VoiceConnector' => [ 'shape' => 'VoiceConnectorSettings', ], ], ], 'UpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'phoneNumberId', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdatePhoneNumberRequestItem' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'NonEmptyString', ], 'ProductType' => [ 'shape' => 'PhoneNumberProductType', ], 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdatePhoneNumberRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdatePhoneNumberRequestItem', ], ], 'UpdatePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'UpdatePhoneNumberSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'CallingName', ], 'members' => [ 'CallingName' => [ 'shape' => 'CallingName', ], ], ], 'UpdateProxySessionRequest' => [ 'type' => 'structure', 'required' => [ 'Capabilities', 'VoiceConnectorId', 'ProxySessionId', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'ProxySessionId' => [ 'shape' => 'NonEmptyString128', 'location' => 'uri', 'locationName' => 'proxySessionId', ], 'Capabilities' => [ 'shape' => 'CapabilityList', ], 'ExpiryMinutes' => [ 'shape' => 'PositiveInteger', ], ], ], 'UpdateProxySessionResponse' => [ 'type' => 'structure', 'members' => [ 'ProxySession' => [ 'shape' => 'ProxySession', ], ], ], 'UpdateRoomMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', 'MemberId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'MemberId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'memberId', ], 'Role' => [ 'shape' => 'RoomMembershipRole', ], ], ], 'UpdateRoomMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'RoomMembership' => [ 'shape' => 'RoomMembership', ], ], ], 'UpdateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'RoomId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'RoomId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'roomId', ], 'Name' => [ 'shape' => 'SensitiveString', ], ], ], 'UpdateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'UpdateSipMediaApplicationCallRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', 'TransactionId', 'Arguments', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], 'TransactionId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'transactionId', ], 'Arguments' => [ 'shape' => 'SMAUpdateCallArgumentsMap', ], ], ], 'UpdateSipMediaApplicationCallResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplicationCall' => [ 'shape' => 'SipMediaApplicationCall', ], ], ], 'UpdateSipMediaApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'SipMediaApplicationId', ], 'members' => [ 'SipMediaApplicationId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipMediaApplicationId', ], 'Name' => [ 'shape' => 'SipMediaApplicationName', ], 'Endpoints' => [ 'shape' => 'SipMediaApplicationEndpointList', ], ], ], 'UpdateSipMediaApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'SipMediaApplication' => [ 'shape' => 'SipMediaApplication', ], ], ], 'UpdateSipRuleRequest' => [ 'type' => 'structure', 'required' => [ 'SipRuleId', 'Name', ], 'members' => [ 'SipRuleId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'sipRuleId', ], 'Name' => [ 'shape' => 'SipRuleName', ], 'Disabled' => [ 'shape' => 'NullableBoolean', ], 'TargetApplications' => [ 'shape' => 'SipRuleTargetApplicationList', ], ], ], 'UpdateSipRuleResponse' => [ 'type' => 'structure', 'members' => [ 'SipRule' => [ 'shape' => 'SipRule', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', ], 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'userId', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], ], ], 'UpdateUserRequestItem' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'NonEmptyString', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], ], ], 'UpdateUserRequestItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateUserRequestItem', ], 'max' => 20, ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'UpdateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'UserId', 'UserSettings', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'accountId', ], 'UserId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'UpdateVoiceConnectorGroupRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorGroupId', 'Name', 'VoiceConnectorItems', ], 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorGroupId', ], 'Name' => [ 'shape' => 'VoiceConnectorGroupName', ], 'VoiceConnectorItems' => [ 'shape' => 'VoiceConnectorItemList', ], ], ], 'UpdateVoiceConnectorGroupResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorGroup' => [ 'shape' => 'VoiceConnectorGroup', ], ], ], 'UpdateVoiceConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'Name', 'RequireEncryption', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'voiceConnectorId', ], 'Name' => [ 'shape' => 'VoiceConnectorName', ], 'RequireEncryption' => [ 'shape' => 'Boolean', ], ], ], 'UpdateVoiceConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceConnector' => [ 'shape' => 'VoiceConnector', ], ], ], 'UriType' => [ 'type' => 'string', 'max' => 4096, ], 'UrlType' => [ 'type' => 'string', 'max' => 4096, ], 'User' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'PrimaryEmail' => [ 'shape' => 'EmailAddress', ], 'PrimaryProvisionedNumber' => [ 'shape' => 'SensitiveString', ], 'DisplayName' => [ 'shape' => 'SensitiveString', ], 'LicenseType' => [ 'shape' => 'License', ], 'UserType' => [ 'shape' => 'UserType', ], 'UserRegistrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'UserInvitationStatus' => [ 'shape' => 'InviteStatus', ], 'RegisteredOn' => [ 'shape' => 'Iso8601Timestamp', ], 'InvitedOn' => [ 'shape' => 'Iso8601Timestamp', ], 'AlexaForBusinessMetadata' => [ 'shape' => 'AlexaForBusinessMetadata', ], 'PersonalPIN' => [ 'shape' => 'String', ], ], ], 'UserEmailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddress', ], 'max' => 50, ], 'UserError' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'UserErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserError', ], ], 'UserId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9]([A-Za-z0-9\\:\\-\\_\\.\\@]{0,62}[A-Za-z0-9])?', 'sensitive' => true, ], 'UserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 50, ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'UserSettings' => [ 'type' => 'structure', 'required' => [ 'Telephony', ], 'members' => [ 'Telephony' => [ 'shape' => 'TelephonySettings', ], ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'PrivateUser', 'SharedDevice', ], ], 'ValidateE911AddressRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'StreetNumber', 'StreetInfo', 'City', 'State', 'Country', 'PostalCode', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'NonEmptyString', ], 'StreetNumber' => [ 'shape' => 'SensitiveNonEmptyString', ], 'StreetInfo' => [ 'shape' => 'SensitiveNonEmptyString', ], 'City' => [ 'shape' => 'SensitiveNonEmptyString', ], 'State' => [ 'shape' => 'SensitiveNonEmptyString', ], 'Country' => [ 'shape' => 'SensitiveNonEmptyString', ], 'PostalCode' => [ 'shape' => 'SensitiveNonEmptyString', ], ], ], 'ValidateE911AddressResponse' => [ 'type' => 'structure', 'members' => [ 'ValidationResult' => [ 'shape' => 'ValidationResult', ], 'AddressExternalId' => [ 'shape' => 'String', ], 'Address' => [ 'shape' => 'Address', ], 'CandidateAddressList' => [ 'shape' => 'CandidateAddressList', ], ], ], 'ValidationResult' => [ 'type' => 'integer', 'max' => 2, 'min' => 0, ], 'VideoArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsState', ], 'MuxType' => [ 'shape' => 'VideoMuxType', ], ], ], 'VideoMuxType' => [ 'type' => 'string', 'enum' => [ 'VideoOnly', ], ], 'VoiceConnector' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', ], 'AwsRegion' => [ 'shape' => 'VoiceConnectorAwsRegion', ], 'Name' => [ 'shape' => 'VoiceConnectorName', ], 'OutboundHostName' => [ 'shape' => 'String', ], 'RequireEncryption' => [ 'shape' => 'Boolean', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'VoiceConnectorArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'VoiceConnectorAwsRegion' => [ 'type' => 'string', 'enum' => [ 'us-east-1', 'us-west-2', ], ], 'VoiceConnectorGroup' => [ 'type' => 'structure', 'members' => [ 'VoiceConnectorGroupId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'VoiceConnectorGroupName', ], 'VoiceConnectorItems' => [ 'shape' => 'VoiceConnectorItemList', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'VoiceConnectorGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'VoiceConnectorGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VoiceConnectorGroup', ], ], 'VoiceConnectorGroupName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'VoiceConnectorItem' => [ 'type' => 'structure', 'required' => [ 'VoiceConnectorId', 'Priority', ], 'members' => [ 'VoiceConnectorId' => [ 'shape' => 'NonEmptyString', ], 'Priority' => [ 'shape' => 'VoiceConnectorItemPriority', ], ], ], 'VoiceConnectorItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VoiceConnectorItem', ], ], 'VoiceConnectorItemPriority' => [ 'type' => 'integer', 'max' => 99, 'min' => 1, ], 'VoiceConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VoiceConnector', ], ], 'VoiceConnectorName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'VoiceConnectorSettings' => [ 'type' => 'structure', 'members' => [ 'CdrBucket' => [ 'shape' => 'String', 'box' => true, ], ], ], ], 'deprecated' => true, 'deprecatedMessage' => 'This namespace has been deprecated',];
