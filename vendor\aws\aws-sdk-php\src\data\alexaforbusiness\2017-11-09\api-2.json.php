<?php
// This file was auto-generated from sdk-root/src/data/alexaforbusiness/2017-11-09/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-09', 'endpointPrefix' => 'a4b', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Alexa For Business', 'serviceId' => 'Alexa For Business', 'signatureVersion' => 'v4', 'targetPrefix' => 'AlexaForBusiness', 'uid' => 'alexaforbusiness-2017-11-09', ], 'operations' => [ 'ApproveSkill' => [ 'name' => 'ApproveSkill', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ApproveSkillRequest', ], 'output' => [ 'shape' => 'ApproveSkillResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateContactWithAddressBook' => [ 'name' => 'AssociateContactWithAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateContactWithAddressBookRequest', ], 'output' => [ 'shape' => 'AssociateContactWithAddressBookResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateDeviceWithNetworkProfile' => [ 'name' => 'AssociateDeviceWithNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateDeviceWithNetworkProfileRequest', ], 'output' => [ 'shape' => 'AssociateDeviceWithNetworkProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateDeviceWithRoom' => [ 'name' => 'AssociateDeviceWithRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateDeviceWithRoomRequest', ], 'output' => [ 'shape' => 'AssociateDeviceWithRoomResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateSkillGroupWithRoom' => [ 'name' => 'AssociateSkillGroupWithRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSkillGroupWithRoomRequest', ], 'output' => [ 'shape' => 'AssociateSkillGroupWithRoomResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateSkillWithSkillGroup' => [ 'name' => 'AssociateSkillWithSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSkillWithSkillGroupRequest', ], 'output' => [ 'shape' => 'AssociateSkillWithSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'SkillNotLinkedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'AssociateSkillWithUsers' => [ 'name' => 'AssociateSkillWithUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSkillWithUsersRequest', ], 'output' => [ 'shape' => 'AssociateSkillWithUsersResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateAddressBook' => [ 'name' => 'CreateAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddressBookRequest', ], 'output' => [ 'shape' => 'CreateAddressBookResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateBusinessReportSchedule' => [ 'name' => 'CreateBusinessReportSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBusinessReportScheduleRequest', ], 'output' => [ 'shape' => 'CreateBusinessReportScheduleResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateConferenceProvider' => [ 'name' => 'CreateConferenceProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConferenceProviderRequest', ], 'output' => [ 'shape' => 'CreateConferenceProviderResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateContact' => [ 'name' => 'CreateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContactRequest', ], 'output' => [ 'shape' => 'CreateContactResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateGatewayGroup' => [ 'name' => 'CreateGatewayGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGatewayGroupRequest', ], 'output' => [ 'shape' => 'CreateGatewayGroupResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateNetworkProfile' => [ 'name' => 'CreateNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNetworkProfileRequest', ], 'output' => [ 'shape' => 'CreateNetworkProfileResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidCertificateAuthorityException', ], [ 'shape' => 'InvalidServiceLinkedRoleStateException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateRoom' => [ 'name' => 'CreateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRoomRequest', ], 'output' => [ 'shape' => 'CreateRoomResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateSkillGroup' => [ 'name' => 'CreateSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSkillGroupRequest', ], 'output' => [ 'shape' => 'CreateSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteAddressBook' => [ 'name' => 'DeleteAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAddressBookRequest', ], 'output' => [ 'shape' => 'DeleteAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteBusinessReportSchedule' => [ 'name' => 'DeleteBusinessReportSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBusinessReportScheduleRequest', ], 'output' => [ 'shape' => 'DeleteBusinessReportScheduleResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteConferenceProvider' => [ 'name' => 'DeleteConferenceProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConferenceProviderRequest', ], 'output' => [ 'shape' => 'DeleteConferenceProviderResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteContact' => [ 'name' => 'DeleteContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContactRequest', ], 'output' => [ 'shape' => 'DeleteContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteDevice' => [ 'name' => 'DeleteDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeviceRequest', ], 'output' => [ 'shape' => 'DeleteDeviceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidCertificateAuthorityException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteDeviceUsageData' => [ 'name' => 'DeleteDeviceUsageData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeviceUsageDataRequest', ], 'output' => [ 'shape' => 'DeleteDeviceUsageDataResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'DeviceNotRegisteredException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteGatewayGroup' => [ 'name' => 'DeleteGatewayGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGatewayGroupRequest', ], 'output' => [ 'shape' => 'DeleteGatewayGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceAssociatedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteNetworkProfile' => [ 'name' => 'DeleteNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNetworkProfileRequest', ], 'output' => [ 'shape' => 'DeleteNetworkProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'output' => [ 'shape' => 'DeleteProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteRoom' => [ 'name' => 'DeleteRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRoomRequest', ], 'output' => [ 'shape' => 'DeleteRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteRoomSkillParameter' => [ 'name' => 'DeleteRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'DeleteRoomSkillParameterResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteSkillAuthorization' => [ 'name' => 'DeleteSkillAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSkillAuthorizationRequest', ], 'output' => [ 'shape' => 'DeleteSkillAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteSkillGroup' => [ 'name' => 'DeleteSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSkillGroupRequest', ], 'output' => [ 'shape' => 'DeleteSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DisassociateContactFromAddressBook' => [ 'name' => 'DisassociateContactFromAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateContactFromAddressBookRequest', ], 'output' => [ 'shape' => 'DisassociateContactFromAddressBookResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DisassociateDeviceFromRoom' => [ 'name' => 'DisassociateDeviceFromRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateDeviceFromRoomRequest', ], 'output' => [ 'shape' => 'DisassociateDeviceFromRoomResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DisassociateSkillFromSkillGroup' => [ 'name' => 'DisassociateSkillFromSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateSkillFromSkillGroupRequest', ], 'output' => [ 'shape' => 'DisassociateSkillFromSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DisassociateSkillFromUsers' => [ 'name' => 'DisassociateSkillFromUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateSkillFromUsersRequest', ], 'output' => [ 'shape' => 'DisassociateSkillFromUsersResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'DisassociateSkillGroupFromRoom' => [ 'name' => 'DisassociateSkillGroupFromRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateSkillGroupFromRoomRequest', ], 'output' => [ 'shape' => 'DisassociateSkillGroupFromRoomResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ForgetSmartHomeAppliances' => [ 'name' => 'ForgetSmartHomeAppliances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ForgetSmartHomeAppliancesRequest', ], 'output' => [ 'shape' => 'ForgetSmartHomeAppliancesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetAddressBook' => [ 'name' => 'GetAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddressBookRequest', ], 'output' => [ 'shape' => 'GetAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetConferencePreference' => [ 'name' => 'GetConferencePreference', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConferencePreferenceRequest', ], 'output' => [ 'shape' => 'GetConferencePreferenceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetConferenceProvider' => [ 'name' => 'GetConferenceProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConferenceProviderRequest', ], 'output' => [ 'shape' => 'GetConferenceProviderResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetContact' => [ 'name' => 'GetContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContactRequest', ], 'output' => [ 'shape' => 'GetContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetDevice' => [ 'name' => 'GetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceRequest', ], 'output' => [ 'shape' => 'GetDeviceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetGateway' => [ 'name' => 'GetGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGatewayRequest', ], 'output' => [ 'shape' => 'GetGatewayResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetGatewayGroup' => [ 'name' => 'GetGatewayGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGatewayGroupRequest', ], 'output' => [ 'shape' => 'GetGatewayGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetInvitationConfiguration' => [ 'name' => 'GetInvitationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInvitationConfigurationRequest', ], 'output' => [ 'shape' => 'GetInvitationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetNetworkProfile' => [ 'name' => 'GetNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetNetworkProfileRequest', ], 'output' => [ 'shape' => 'GetNetworkProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidSecretsManagerResourceException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetProfile' => [ 'name' => 'GetProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProfileRequest', ], 'output' => [ 'shape' => 'GetProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetRoom' => [ 'name' => 'GetRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRoomRequest', ], 'output' => [ 'shape' => 'GetRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetRoomSkillParameter' => [ 'name' => 'GetRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'GetRoomSkillParameterResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'GetSkillGroup' => [ 'name' => 'GetSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSkillGroupRequest', ], 'output' => [ 'shape' => 'GetSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListBusinessReportSchedules' => [ 'name' => 'ListBusinessReportSchedules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBusinessReportSchedulesRequest', ], 'output' => [ 'shape' => 'ListBusinessReportSchedulesResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListConferenceProviders' => [ 'name' => 'ListConferenceProviders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListConferenceProvidersRequest', ], 'output' => [ 'shape' => 'ListConferenceProvidersResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListDeviceEvents' => [ 'name' => 'ListDeviceEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeviceEventsRequest', ], 'output' => [ 'shape' => 'ListDeviceEventsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListGatewayGroups' => [ 'name' => 'ListGatewayGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGatewayGroupsRequest', ], 'output' => [ 'shape' => 'ListGatewayGroupsResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListGateways' => [ 'name' => 'ListGateways', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGatewaysRequest', ], 'output' => [ 'shape' => 'ListGatewaysResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListSkills' => [ 'name' => 'ListSkills', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSkillsRequest', ], 'output' => [ 'shape' => 'ListSkillsResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListSkillsStoreCategories' => [ 'name' => 'ListSkillsStoreCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSkillsStoreCategoriesRequest', ], 'output' => [ 'shape' => 'ListSkillsStoreCategoriesResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListSkillsStoreSkillsByCategory' => [ 'name' => 'ListSkillsStoreSkillsByCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSkillsStoreSkillsByCategoryRequest', ], 'output' => [ 'shape' => 'ListSkillsStoreSkillsByCategoryResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListSmartHomeAppliances' => [ 'name' => 'ListSmartHomeAppliances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSmartHomeAppliancesRequest', ], 'output' => [ 'shape' => 'ListSmartHomeAppliancesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'PutConferencePreference' => [ 'name' => 'PutConferencePreference', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutConferencePreferenceRequest', ], 'output' => [ 'shape' => 'PutConferencePreferenceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'PutInvitationConfiguration' => [ 'name' => 'PutInvitationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInvitationConfigurationRequest', ], 'output' => [ 'shape' => 'PutInvitationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'PutRoomSkillParameter' => [ 'name' => 'PutRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'PutRoomSkillParameterResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'PutSkillAuthorization' => [ 'name' => 'PutSkillAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSkillAuthorizationRequest', ], 'output' => [ 'shape' => 'PutSkillAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'RegisterAVSDevice' => [ 'name' => 'RegisterAVSDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterAVSDeviceRequest', ], 'output' => [ 'shape' => 'RegisterAVSDeviceResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidDeviceException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'RejectSkill' => [ 'name' => 'RejectSkill', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectSkillRequest', ], 'output' => [ 'shape' => 'RejectSkillResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'ResolveRoom' => [ 'name' => 'ResolveRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResolveRoomRequest', ], 'output' => [ 'shape' => 'ResolveRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'RevokeInvitation' => [ 'name' => 'RevokeInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeInvitationRequest', ], 'output' => [ 'shape' => 'RevokeInvitationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchAddressBooks' => [ 'name' => 'SearchAddressBooks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchAddressBooksRequest', ], 'output' => [ 'shape' => 'SearchAddressBooksResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchContacts' => [ 'name' => 'SearchContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchContactsRequest', ], 'output' => [ 'shape' => 'SearchContactsResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchDevices' => [ 'name' => 'SearchDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchDevicesRequest', ], 'output' => [ 'shape' => 'SearchDevicesResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchNetworkProfiles' => [ 'name' => 'SearchNetworkProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchNetworkProfilesRequest', ], 'output' => [ 'shape' => 'SearchNetworkProfilesResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchProfiles' => [ 'name' => 'SearchProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchProfilesRequest', ], 'output' => [ 'shape' => 'SearchProfilesResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchRooms' => [ 'name' => 'SearchRooms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchRoomsRequest', ], 'output' => [ 'shape' => 'SearchRoomsResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchSkillGroups' => [ 'name' => 'SearchSkillGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchSkillGroupsRequest', ], 'output' => [ 'shape' => 'SearchSkillGroupsResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SearchUsers' => [ 'name' => 'SearchUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchUsersRequest', ], 'output' => [ 'shape' => 'SearchUsersResponse', ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'SendAnnouncement' => [ 'name' => 'SendAnnouncement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendAnnouncementRequest', ], 'output' => [ 'shape' => 'SendAnnouncementResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], ], ], 'SendInvitation' => [ 'name' => 'SendInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendInvitationRequest', ], 'output' => [ 'shape' => 'SendInvitationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidUserStatusException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'StartDeviceSync' => [ 'name' => 'StartDeviceSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDeviceSyncRequest', ], 'output' => [ 'shape' => 'StartDeviceSyncResponse', ], 'errors' => [ [ 'shape' => 'DeviceNotRegisteredException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'StartSmartHomeApplianceDiscovery' => [ 'name' => 'StartSmartHomeApplianceDiscovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSmartHomeApplianceDiscoveryRequest', ], 'output' => [ 'shape' => 'StartSmartHomeApplianceDiscoveryResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateAddressBook' => [ 'name' => 'UpdateAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAddressBookRequest', ], 'output' => [ 'shape' => 'UpdateAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateBusinessReportSchedule' => [ 'name' => 'UpdateBusinessReportSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBusinessReportScheduleRequest', ], 'output' => [ 'shape' => 'UpdateBusinessReportScheduleResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateConferenceProvider' => [ 'name' => 'UpdateConferenceProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConferenceProviderRequest', ], 'output' => [ 'shape' => 'UpdateConferenceProviderResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateContact' => [ 'name' => 'UpdateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContactRequest', ], 'output' => [ 'shape' => 'UpdateContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateDevice' => [ 'name' => 'UpdateDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeviceRequest', ], 'output' => [ 'shape' => 'UpdateDeviceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateGateway' => [ 'name' => 'UpdateGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGatewayRequest', ], 'output' => [ 'shape' => 'UpdateGatewayResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateGatewayGroup' => [ 'name' => 'UpdateGatewayGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGatewayGroupRequest', ], 'output' => [ 'shape' => 'UpdateGatewayGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateNetworkProfile' => [ 'name' => 'UpdateNetworkProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNetworkProfileRequest', ], 'output' => [ 'shape' => 'UpdateNetworkProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidCertificateAuthorityException', ], [ 'shape' => 'InvalidSecretsManagerResourceException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateRoom' => [ 'name' => 'UpdateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRoomRequest', ], 'output' => [ 'shape' => 'UpdateRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], 'UpdateSkillGroup' => [ 'name' => 'UpdateSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSkillGroupRequest', ], 'output' => [ 'shape' => 'UpdateSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], [ 'shape' => 'ConcurrentModificationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported', ], ], 'shapes' => [ 'Address' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'AddressBook' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'AddressBookData' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'AddressBookDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressBookData', ], ], 'AddressBookDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'AddressBookName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AmazonId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{1,18}', ], 'ApplianceDescription' => [ 'type' => 'string', ], 'ApplianceFriendlyName' => [ 'type' => 'string', ], 'ApplianceManufacturerName' => [ 'type' => 'string', ], 'ApproveSkillRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'ApproveSkillResponse' => [ 'type' => 'structure', 'members' => [], ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'AssociateContactWithAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', 'AddressBookArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateContactWithAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateDeviceWithNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceArn', 'NetworkProfileArn', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'NetworkProfileArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateDeviceWithNetworkProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateDeviceWithRoomRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateDeviceWithRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateSkillGroupWithRoomRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateSkillGroupWithRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateSkillWithSkillGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'AssociateSkillWithSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateSkillWithUsersRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'AssociateSkillWithUsersResponse' => [ 'type' => 'structure', 'members' => [], ], 'Audio' => [ 'type' => 'structure', 'required' => [ 'Locale', 'Location', ], 'members' => [ 'Locale' => [ 'shape' => 'Locale', ], 'Location' => [ 'shape' => 'AudioLocation', ], ], ], 'AudioList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Audio', ], 'max' => 1, ], 'AudioLocation' => [ 'type' => 'string', 'max' => 1200, 'min' => 0, 'pattern' => 'https://([A-Za-z0-9_.-]+)?(s3-[A-Za-z0-9-]+|s3\\.([A-Za-z0-9-])+|s3|s3.dualstack\\.([A-Za-z0-9-])+)+.amazonaws.com/.*', ], 'AuthorizationResult' => [ 'type' => 'map', 'key' => [ 'shape' => 'Key', ], 'value' => [ 'shape' => 'Value', ], 'sensitive' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BulletPoint' => [ 'type' => 'string', ], 'BulletPoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'BulletPoint', ], ], 'BusinessReport' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'BusinessReportStatus', ], 'FailureCode' => [ 'shape' => 'BusinessReportFailureCode', ], 'S3Location' => [ 'shape' => 'BusinessReportS3Location', ], 'DeliveryTime' => [ 'shape' => 'BusinessReportDeliveryTime', ], 'DownloadUrl' => [ 'shape' => 'BusinessReportDownloadUrl', ], ], ], 'BusinessReportContentRange' => [ 'type' => 'structure', 'required' => [ 'Interval', ], 'members' => [ 'Interval' => [ 'shape' => 'BusinessReportInterval', ], ], ], 'BusinessReportDeliveryTime' => [ 'type' => 'timestamp', ], 'BusinessReportDownloadUrl' => [ 'type' => 'string', ], 'BusinessReportFailureCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'NO_SUCH_BUCKET', 'INTERNAL_FAILURE', ], ], 'BusinessReportFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'CSV_ZIP', ], ], 'BusinessReportInterval' => [ 'type' => 'string', 'enum' => [ 'ONE_DAY', 'ONE_WEEK', 'THIRTY_DAYS', ], ], 'BusinessReportRecurrence' => [ 'type' => 'structure', 'members' => [ 'StartDate' => [ 'shape' => 'Date', ], ], ], 'BusinessReportS3Location' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'BusinessReportS3Path', ], 'BucketName' => [ 'shape' => 'CustomerS3BucketName', ], ], ], 'BusinessReportS3Path' => [ 'type' => 'string', ], 'BusinessReportSchedule' => [ 'type' => 'structure', 'members' => [ 'ScheduleArn' => [ 'shape' => 'Arn', ], 'ScheduleName' => [ 'shape' => 'BusinessReportScheduleName', ], 'S3BucketName' => [ 'shape' => 'CustomerS3BucketName', ], 'S3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'Format' => [ 'shape' => 'BusinessReportFormat', ], 'ContentRange' => [ 'shape' => 'BusinessReportContentRange', ], 'Recurrence' => [ 'shape' => 'BusinessReportRecurrence', ], 'LastBusinessReport' => [ 'shape' => 'BusinessReport', ], ], ], 'BusinessReportScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BusinessReportSchedule', ], ], 'BusinessReportScheduleName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'BusinessReportStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', ], ], 'Category' => [ 'type' => 'structure', 'members' => [ 'CategoryId' => [ 'shape' => 'CategoryId', ], 'CategoryName' => [ 'shape' => 'CategoryName', ], ], ], 'CategoryId' => [ 'type' => 'long', 'min' => 1, ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], ], 'CategoryName' => [ 'type' => 'string', ], 'CertificateTime' => [ 'type' => 'timestamp', ], 'ClientId' => [ 'type' => 'string', 'pattern' => '^\\S+{1,256}$', ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 150, 'min' => 10, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'CommsProtocol' => [ 'type' => 'string', 'enum' => [ 'SIP', 'SIPS', 'H323', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ConferencePreference' => [ 'type' => 'structure', 'members' => [ 'DefaultConferenceProviderArn' => [ 'shape' => 'Arn', ], ], ], 'ConferenceProvider' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ConferenceProviderName', ], 'Type' => [ 'shape' => 'ConferenceProviderType', ], 'IPDialIn' => [ 'shape' => 'IPDialIn', ], 'PSTNDialIn' => [ 'shape' => 'PSTNDialIn', ], 'MeetingSetting' => [ 'shape' => 'MeetingSetting', ], ], ], 'ConferenceProviderName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'ConferenceProviderType' => [ 'type' => 'string', 'enum' => [ 'CHIME', 'BLUEJEANS', 'FUZE', 'GOOGLE_HANGOUTS', 'POLYCOM', 'RINGCENTRAL', 'SKYPE_FOR_BUSINESS', 'WEBEX', 'ZOOM', 'CUSTOM', ], ], 'ConferenceProvidersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConferenceProvider', ], ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'ONLINE', 'OFFLINE', ], ], 'ConnectionStatusUpdatedTime' => [ 'type' => 'timestamp', ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'RawPhoneNumber', ], 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'SipAddresses' => [ 'shape' => 'SipAddressList', ], ], ], 'ContactData' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'RawPhoneNumber', ], 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'SipAddresses' => [ 'shape' => 'SipAddressList', ], ], ], 'ContactDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactData', ], ], 'ContactName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'Content' => [ 'type' => 'structure', 'members' => [ 'TextList' => [ 'shape' => 'TextList', ], 'SsmlList' => [ 'shape' => 'SsmlList', ], 'AudioList' => [ 'shape' => 'AudioList', ], ], ], 'CountryCode' => [ 'type' => 'string', 'pattern' => '\\d{1,3}', ], 'CreateAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAddressBookResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'CreateBusinessReportScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Format', 'ContentRange', ], 'members' => [ 'ScheduleName' => [ 'shape' => 'BusinessReportScheduleName', ], 'S3BucketName' => [ 'shape' => 'CustomerS3BucketName', ], 'S3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'Format' => [ 'shape' => 'BusinessReportFormat', ], 'ContentRange' => [ 'shape' => 'BusinessReportContentRange', ], 'Recurrence' => [ 'shape' => 'BusinessReportRecurrence', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateBusinessReportScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'ScheduleArn' => [ 'shape' => 'Arn', ], ], ], 'CreateConferenceProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ConferenceProviderName', 'ConferenceProviderType', 'MeetingSetting', ], 'members' => [ 'ConferenceProviderName' => [ 'shape' => 'ConferenceProviderName', ], 'ConferenceProviderType' => [ 'shape' => 'ConferenceProviderType', ], 'IPDialIn' => [ 'shape' => 'IPDialIn', ], 'PSTNDialIn' => [ 'shape' => 'PSTNDialIn', ], 'MeetingSetting' => [ 'shape' => 'MeetingSetting', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateConferenceProviderResponse' => [ 'type' => 'structure', 'members' => [ 'ConferenceProviderArn' => [ 'shape' => 'Arn', ], ], ], 'CreateContactRequest' => [ 'type' => 'structure', 'required' => [ 'FirstName', ], 'members' => [ 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'RawPhoneNumber', ], 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'SipAddresses' => [ 'shape' => 'SipAddressList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'CreateEndOfMeetingReminder' => [ 'type' => 'structure', 'required' => [ 'ReminderAtMinutes', 'ReminderType', 'Enabled', ], 'members' => [ 'ReminderAtMinutes' => [ 'shape' => 'EndOfMeetingReminderMinutesList', ], 'ReminderType' => [ 'shape' => 'EndOfMeetingReminderType', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'CreateGatewayGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ClientRequestToken', ], 'members' => [ 'Name' => [ 'shape' => 'GatewayGroupName', ], 'Description' => [ 'shape' => 'GatewayGroupDescription', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGatewayGroupResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateInstantBooking' => [ 'type' => 'structure', 'required' => [ 'DurationInMinutes', 'Enabled', ], 'members' => [ 'DurationInMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'CreateMeetingRoomConfiguration' => [ 'type' => 'structure', 'members' => [ 'RoomUtilizationMetricsEnabled' => [ 'shape' => 'Boolean', ], 'EndOfMeetingReminder' => [ 'shape' => 'CreateEndOfMeetingReminder', ], 'InstantBooking' => [ 'shape' => 'CreateInstantBooking', ], 'RequireCheckIn' => [ 'shape' => 'CreateRequireCheckIn', ], 'ProactiveJoin' => [ 'shape' => 'CreateProactiveJoin', ], ], ], 'CreateNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'NetworkProfileName', 'Ssid', 'SecurityType', 'ClientRequestToken', ], 'members' => [ 'NetworkProfileName' => [ 'shape' => 'NetworkProfileName', ], 'Description' => [ 'shape' => 'NetworkProfileDescription', ], 'Ssid' => [ 'shape' => 'NetworkSsid', ], 'SecurityType' => [ 'shape' => 'NetworkSecurityType', ], 'EapMethod' => [ 'shape' => 'NetworkEapMethod', ], 'CurrentPassword' => [ 'shape' => 'CurrentWiFiPassword', ], 'NextPassword' => [ 'shape' => 'NextWiFiPassword', ], 'CertificateAuthorityArn' => [ 'shape' => 'Arn', ], 'TrustAnchors' => [ 'shape' => 'TrustAnchorList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateNetworkProfileResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], ], ], 'CreateProactiveJoin' => [ 'type' => 'structure', 'required' => [ 'EnabledByMotion', ], 'members' => [ 'EnabledByMotion' => [ 'shape' => 'Boolean', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileName', 'Timezone', 'Address', 'DistanceUnit', 'TemperatureUnit', 'WakeWord', ], 'members' => [ 'ProfileName' => [ 'shape' => 'ProfileName', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'Address' => [ 'shape' => 'Address', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'Locale' => [ 'shape' => 'DeviceLocale', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], 'DataRetentionOptIn' => [ 'shape' => 'Boolean', ], 'MeetingRoomConfiguration' => [ 'shape' => 'CreateMeetingRoomConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'CreateRequireCheckIn' => [ 'type' => 'structure', 'required' => [ 'ReleaseAfterMinutes', 'Enabled', ], 'members' => [ 'ReleaseAfterMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'CreateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'RoomName', ], 'members' => [ 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSkillGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SkillGroupName', ], 'members' => [ 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSkillGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'user_UserId', ], 'FirstName' => [ 'shape' => 'user_FirstName', ], 'LastName' => [ 'shape' => 'user_LastName', ], 'Email' => [ 'shape' => 'Email', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], ], ], 'CurrentWiFiPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 5, 'pattern' => '[\\x00-\\x7F]*', 'sensitive' => true, ], 'CustomerS3BucketName' => [ 'type' => 'string', 'pattern' => '[a-z0-9-\\.]{3,63}', ], 'Date' => [ 'type' => 'string', 'pattern' => '^\\d{4}\\-(0?[1-9]|1[012])\\-(0?[1-9]|[12][0-9]|3[01])$', ], 'DeleteAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteBusinessReportScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'ScheduleArn', ], 'members' => [ 'ScheduleArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteBusinessReportScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConferenceProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ConferenceProviderArn', ], 'members' => [ 'ConferenceProviderArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteConferenceProviderResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceArn', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDeviceUsageDataRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceArn', 'DeviceUsageType', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceUsageType' => [ 'shape' => 'DeviceUsageType', ], ], ], 'DeleteDeviceUsageDataResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGatewayGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayGroupArn', ], 'members' => [ 'GatewayGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteGatewayGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'NetworkProfileArn', ], 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteNetworkProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'ParameterKey', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], ], ], 'DeleteRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSkillAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSkillAuthorizationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'EnrollmentId', ], 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeveloperInfo' => [ 'type' => 'structure', 'members' => [ 'DeveloperName' => [ 'shape' => 'DeveloperName', ], 'PrivacyPolicy' => [ 'shape' => 'PrivacyPolicy', ], 'Email' => [ 'shape' => 'Email', ], 'Url' => [ 'shape' => 'Url', ], ], ], 'DeveloperName' => [ 'type' => 'string', ], 'Device' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'SoftwareVersion' => [ 'shape' => 'SoftwareVersion', ], 'MacAddress' => [ 'shape' => 'MacAddress', ], 'RoomArn' => [ 'shape' => 'Arn', ], 'DeviceStatus' => [ 'shape' => 'DeviceStatus', ], 'DeviceStatusInfo' => [ 'shape' => 'DeviceStatusInfo', ], 'NetworkProfileInfo' => [ 'shape' => 'DeviceNetworkProfileInfo', ], ], ], 'DeviceData' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'SoftwareVersion' => [ 'shape' => 'SoftwareVersion', ], 'MacAddress' => [ 'shape' => 'MacAddress', ], 'DeviceStatus' => [ 'shape' => 'DeviceStatus', ], 'NetworkProfileArn' => [ 'shape' => 'Arn', ], 'NetworkProfileName' => [ 'shape' => 'NetworkProfileName', ], 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'DeviceRoomName', ], 'DeviceStatusInfo' => [ 'shape' => 'DeviceStatusInfo', ], 'CreatedTime' => [ 'shape' => 'DeviceDataCreatedTime', ], ], ], 'DeviceDataCreatedTime' => [ 'type' => 'timestamp', ], 'DeviceDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceData', ], ], 'DeviceEvent' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DeviceEventType', ], 'Value' => [ 'shape' => 'DeviceEventValue', ], 'Timestamp' => [ 'shape' => 'DeviceEventTime', ], ], ], 'DeviceEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceEvent', ], ], 'DeviceEventTime' => [ 'type' => 'timestamp', ], 'DeviceEventType' => [ 'type' => 'string', 'enum' => [ 'CONNECTION_STATUS', 'DEVICE_STATUS', ], ], 'DeviceEventValue' => [ 'type' => 'string', ], 'DeviceLocale' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DeviceName' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'DeviceNetworkProfileInfo' => [ 'type' => 'structure', 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], 'CertificateArn' => [ 'shape' => 'Arn', ], 'CertificateExpirationTime' => [ 'shape' => 'CertificateTime', ], ], ], 'DeviceNotRegisteredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DeviceRoomName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'DeviceSerialNumber' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{1,200}', ], 'DeviceSerialNumberForAVS' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9]{1,50}$', ], 'DeviceStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'PENDING', 'WAS_OFFLINE', 'DEREGISTERED', 'FAILED', ], ], 'DeviceStatusDetail' => [ 'type' => 'structure', 'members' => [ 'Feature' => [ 'shape' => 'Feature', ], 'Code' => [ 'shape' => 'DeviceStatusDetailCode', ], ], ], 'DeviceStatusDetailCode' => [ 'type' => 'string', 'enum' => [ 'DEVICE_SOFTWARE_UPDATE_NEEDED', 'DEVICE_WAS_OFFLINE', 'CREDENTIALS_ACCESS_FAILURE', 'TLS_VERSION_MISMATCH', 'ASSOCIATION_REJECTION', 'AUTHENTICATION_FAILURE', 'DHCP_FAILURE', 'INTERNET_UNAVAILABLE', 'DNS_FAILURE', 'UNKNOWN_FAILURE', 'CERTIFICATE_ISSUING_LIMIT_EXCEEDED', 'INVALID_CERTIFICATE_AUTHORITY', 'NETWORK_PROFILE_NOT_FOUND', 'INVALID_PASSWORD_STATE', 'PASSWORD_NOT_FOUND', 'PASSWORD_MANAGER_ACCESS_DENIED', 'CERTIFICATE_AUTHORITY_ACCESS_DENIED', ], ], 'DeviceStatusDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceStatusDetail', ], ], 'DeviceStatusInfo' => [ 'type' => 'structure', 'members' => [ 'DeviceStatusDetails' => [ 'shape' => 'DeviceStatusDetails', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatus', ], 'ConnectionStatusUpdatedTime' => [ 'shape' => 'ConnectionStatusUpdatedTime', ], ], ], 'DeviceType' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{1,200}', ], 'DeviceUsageType' => [ 'type' => 'string', 'enum' => [ 'VOICE', ], ], 'DisassociateContactFromAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', 'AddressBookArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateContactFromAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateDeviceFromRoomRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateDeviceFromRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateSkillFromSkillGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'DisassociateSkillFromSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateSkillFromUsersRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'DisassociateSkillFromUsersResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateSkillGroupFromRoomRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateSkillGroupFromRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistanceUnit' => [ 'type' => 'string', 'enum' => [ 'METRIC', 'IMPERIAL', ], ], 'Email' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '\\w[+-.\\w]*@\\w[\\w\\.\\-]+\\.[0-9a-zA-Z]{2,24}', ], 'EnablementType' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'PENDING', ], ], 'EnablementTypeFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'PENDING', ], ], 'EndOfMeetingReminder' => [ 'type' => 'structure', 'members' => [ 'ReminderAtMinutes' => [ 'shape' => 'EndOfMeetingReminderMinutesList', ], 'ReminderType' => [ 'shape' => 'EndOfMeetingReminderType', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'EndOfMeetingReminderMinutesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Minutes', ], 'max' => 1, 'min' => 1, ], 'EndOfMeetingReminderType' => [ 'type' => 'string', 'enum' => [ 'ANNOUNCEMENT_TIME_CHECK', 'ANNOUNCEMENT_VARIABLE_TIME_LEFT', 'CHIME', 'KNOCK', ], ], 'EndUserLicenseAgreement' => [ 'type' => 'string', ], 'Endpoint' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'EnrollmentId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'EnrollmentStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PENDING', 'REGISTERED', 'DISASSOCIATING', 'DEREGISTERING', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'Feature' => [ 'type' => 'string', 'enum' => [ 'BLUETOOTH', 'VOLUME', 'NOTIFICATIONS', 'LISTS', 'SKILLS', 'NETWORK_PROFILE', 'SETTINGS', 'ALL', ], ], 'Features' => [ 'type' => 'list', 'member' => [ 'shape' => 'Feature', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'FilterKey', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterKey' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 25, ], 'FilterValue' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 50, ], 'ForgetSmartHomeAppliancesRequest' => [ 'type' => 'structure', 'required' => [ 'RoomArn', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'ForgetSmartHomeAppliancesResponse' => [ 'type' => 'structure', 'members' => [], ], 'Gateway' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayName', ], 'Description' => [ 'shape' => 'GatewayDescription', ], 'GatewayGroupArn' => [ 'shape' => 'Arn', ], 'SoftwareVersion' => [ 'shape' => 'GatewayVersion', ], ], ], 'GatewayDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'GatewayGroup' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayGroupName', ], 'Description' => [ 'shape' => 'GatewayGroupDescription', ], ], ], 'GatewayGroupDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'GatewayGroupName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'GatewayGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayGroupSummary', ], ], 'GatewayGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayGroupName', ], 'Description' => [ 'shape' => 'GatewayGroupDescription', ], ], ], 'GatewayName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'GatewaySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewaySummary', ], ], 'GatewaySummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayName', ], 'Description' => [ 'shape' => 'GatewayDescription', ], 'GatewayGroupArn' => [ 'shape' => 'Arn', ], 'SoftwareVersion' => [ 'shape' => 'GatewayVersion', ], ], ], 'GatewayVersion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'GenericKeyword' => [ 'type' => 'string', ], 'GenericKeywords' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericKeyword', ], ], 'GetAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'GetAddressBookResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBook' => [ 'shape' => 'AddressBook', ], ], ], 'GetConferencePreferenceRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetConferencePreferenceResponse' => [ 'type' => 'structure', 'members' => [ 'Preference' => [ 'shape' => 'ConferencePreference', ], ], ], 'GetConferenceProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ConferenceProviderArn', ], 'members' => [ 'ConferenceProviderArn' => [ 'shape' => 'Arn', ], ], ], 'GetConferenceProviderResponse' => [ 'type' => 'structure', 'members' => [ 'ConferenceProvider' => [ 'shape' => 'ConferenceProvider', ], ], ], 'GetContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'GetContactResponse' => [ 'type' => 'structure', 'members' => [ 'Contact' => [ 'shape' => 'Contact', ], ], ], 'GetDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'GetDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'Device', ], ], ], 'GetGatewayGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayGroupArn', ], 'members' => [ 'GatewayGroupArn' => [ 'shape' => 'Arn', ], ], ], 'GetGatewayGroupResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayGroup' => [ 'shape' => 'GatewayGroup', ], ], ], 'GetGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'Arn', ], ], ], 'GetGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'Gateway' => [ 'shape' => 'Gateway', ], ], ], 'GetInvitationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInvitationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationName' => [ 'shape' => 'OrganizationName', ], 'ContactEmail' => [ 'shape' => 'Email', ], 'PrivateSkillIds' => [ 'shape' => 'ShortSkillIdList', ], ], ], 'GetNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'NetworkProfileArn', ], 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], ], ], 'GetNetworkProfileResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkProfile' => [ 'shape' => 'NetworkProfile', ], ], ], 'GetProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'GetProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Profile' => [ 'shape' => 'Profile', ], ], ], 'GetRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'GetRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'GetRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'ParameterKey', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], ], ], 'GetRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [ 'RoomSkillParameter' => [ 'shape' => 'RoomSkillParameter', ], ], ], 'GetSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'GetSkillGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroup' => [ 'shape' => 'SkillGroup', ], ], ], 'IPDialIn' => [ 'type' => 'structure', 'required' => [ 'Endpoint', 'CommsProtocol', ], 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], 'CommsProtocol' => [ 'shape' => 'CommsProtocol', ], ], ], 'IconUrl' => [ 'type' => 'string', ], 'InstantBooking' => [ 'type' => 'structure', 'members' => [ 'DurationInMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'InvalidCertificateAuthorityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidDeviceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidSecretsManagerResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidServiceLinkedRoleStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidUserStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvocationPhrase' => [ 'type' => 'string', ], 'Key' => [ 'type' => 'string', 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListBusinessReportSchedulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBusinessReportSchedulesResponse' => [ 'type' => 'structure', 'members' => [ 'BusinessReportSchedules' => [ 'shape' => 'BusinessReportScheduleList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConferenceProvidersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListConferenceProvidersResponse' => [ 'type' => 'structure', 'members' => [ 'ConferenceProviders' => [ 'shape' => 'ConferenceProvidersList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeviceEventsRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceArn', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'EventType' => [ 'shape' => 'DeviceEventType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDeviceEventsResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceEvents' => [ 'shape' => 'DeviceEventList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGatewayGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListGatewayGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayGroups' => [ 'shape' => 'GatewayGroupSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'GatewayGroupArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListGatewaysResponse' => [ 'type' => 'structure', 'members' => [ 'Gateways' => [ 'shape' => 'GatewaySummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSkillsRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'EnablementType' => [ 'shape' => 'EnablementTypeFilter', ], 'SkillType' => [ 'shape' => 'SkillTypeFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'SkillListMaxResults', ], ], ], 'ListSkillsResponse' => [ 'type' => 'structure', 'members' => [ 'SkillSummaries' => [ 'shape' => 'SkillSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSkillsStoreCategoriesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSkillsStoreCategoriesResponse' => [ 'type' => 'structure', 'members' => [ 'CategoryList' => [ 'shape' => 'CategoryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSkillsStoreSkillsByCategoryRequest' => [ 'type' => 'structure', 'required' => [ 'CategoryId', ], 'members' => [ 'CategoryId' => [ 'shape' => 'CategoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'SkillListMaxResults', ], ], ], 'ListSkillsStoreSkillsByCategoryResponse' => [ 'type' => 'structure', 'members' => [ 'SkillsStoreSkills' => [ 'shape' => 'SkillsStoreSkillList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSmartHomeAppliancesRequest' => [ 'type' => 'structure', 'required' => [ 'RoomArn', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSmartHomeAppliancesResponse' => [ 'type' => 'structure', 'members' => [ 'SmartHomeAppliances' => [ 'shape' => 'SmartHomeApplianceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'en-US', ], ], 'MacAddress' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxVolumeLimit' => [ 'type' => 'integer', ], 'MeetingRoomConfiguration' => [ 'type' => 'structure', 'members' => [ 'RoomUtilizationMetricsEnabled' => [ 'shape' => 'Boolean', ], 'EndOfMeetingReminder' => [ 'shape' => 'EndOfMeetingReminder', ], 'InstantBooking' => [ 'shape' => 'InstantBooking', ], 'RequireCheckIn' => [ 'shape' => 'RequireCheckIn', ], 'ProactiveJoin' => [ 'shape' => 'ProactiveJoin', ], ], ], 'MeetingSetting' => [ 'type' => 'structure', 'required' => [ 'RequirePin', ], 'members' => [ 'RequirePin' => [ 'shape' => 'RequirePin', ], ], ], 'Minutes' => [ 'type' => 'integer', ], 'NameInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NetworkEapMethod' => [ 'type' => 'string', 'enum' => [ 'EAP_TLS', ], ], 'NetworkProfile' => [ 'type' => 'structure', 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], 'NetworkProfileName' => [ 'shape' => 'NetworkProfileName', ], 'Description' => [ 'shape' => 'NetworkProfileDescription', ], 'Ssid' => [ 'shape' => 'NetworkSsid', ], 'SecurityType' => [ 'shape' => 'NetworkSecurityType', ], 'EapMethod' => [ 'shape' => 'NetworkEapMethod', ], 'CurrentPassword' => [ 'shape' => 'CurrentWiFiPassword', ], 'NextPassword' => [ 'shape' => 'NextWiFiPassword', ], 'CertificateAuthorityArn' => [ 'shape' => 'Arn', ], 'TrustAnchors' => [ 'shape' => 'TrustAnchorList', ], ], ], 'NetworkProfileData' => [ 'type' => 'structure', 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], 'NetworkProfileName' => [ 'shape' => 'NetworkProfileName', ], 'Description' => [ 'shape' => 'NetworkProfileDescription', ], 'Ssid' => [ 'shape' => 'NetworkSsid', ], 'SecurityType' => [ 'shape' => 'NetworkSecurityType', ], 'EapMethod' => [ 'shape' => 'NetworkEapMethod', ], 'CertificateAuthorityArn' => [ 'shape' => 'Arn', ], ], ], 'NetworkProfileDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkProfileData', ], ], 'NetworkProfileDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'NetworkProfileName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'NetworkSecurityType' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'WEP', 'WPA_PSK', 'WPA2_PSK', 'WPA2_ENTERPRISE', ], ], 'NetworkSsid' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'NewInThisVersionBulletPoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'BulletPoint', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 1100, 'min' => 1, ], 'NextWiFiPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '(^$)|([\\x00-\\x7F]{5,})', 'sensitive' => true, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'OneClickIdDelay' => [ 'type' => 'string', 'max' => 2, 'min' => 1, ], 'OneClickPinDelay' => [ 'type' => 'string', 'max' => 2, 'min' => 1, ], 'OrganizationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'OutboundPhoneNumber' => [ 'type' => 'string', 'pattern' => '\\d{10}', ], 'PSTNDialIn' => [ 'type' => 'structure', 'required' => [ 'CountryCode', 'PhoneNumber', 'OneClickIdDelay', 'OneClickPinDelay', ], 'members' => [ 'CountryCode' => [ 'shape' => 'CountryCode', ], 'PhoneNumber' => [ 'shape' => 'OutboundPhoneNumber', ], 'OneClickIdDelay' => [ 'shape' => 'OneClickIdDelay', ], 'OneClickPinDelay' => [ 'shape' => 'OneClickPinDelay', ], ], ], 'PhoneNumber' => [ 'type' => 'structure', 'required' => [ 'Number', 'Type', ], 'members' => [ 'Number' => [ 'shape' => 'RawPhoneNumber', ], 'Type' => [ 'shape' => 'PhoneNumberType', ], ], ], 'PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], 'max' => 3, 'min' => 0, ], 'PhoneNumberType' => [ 'type' => 'string', 'enum' => [ 'MOBILE', 'WORK', 'HOME', ], 'sensitive' => true, ], 'PrivacyPolicy' => [ 'type' => 'string', ], 'ProactiveJoin' => [ 'type' => 'structure', 'members' => [ 'EnabledByMotion' => [ 'shape' => 'Boolean', ], ], ], 'ProductDescription' => [ 'type' => 'string', ], 'ProductId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_]{1,256}$', ], 'Profile' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Address' => [ 'shape' => 'Address', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'Locale' => [ 'shape' => 'DeviceLocale', ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], 'DataRetentionOptIn' => [ 'shape' => 'Boolean', ], 'AddressBookArn' => [ 'shape' => 'Arn', ], 'MeetingRoomConfiguration' => [ 'shape' => 'MeetingRoomConfiguration', ], ], ], 'ProfileData' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Address' => [ 'shape' => 'Address', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'Locale' => [ 'shape' => 'DeviceLocale', ], ], ], 'ProfileDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileData', ], ], 'ProfileName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'ProviderCalendarId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'PutConferencePreferenceRequest' => [ 'type' => 'structure', 'required' => [ 'ConferencePreference', ], 'members' => [ 'ConferencePreference' => [ 'shape' => 'ConferencePreference', ], ], ], 'PutConferencePreferenceResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutInvitationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationName', ], 'members' => [ 'OrganizationName' => [ 'shape' => 'OrganizationName', ], 'ContactEmail' => [ 'shape' => 'Email', ], 'PrivateSkillIds' => [ 'shape' => 'ShortSkillIdList', ], ], ], 'PutInvitationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'RoomSkillParameter', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'RoomSkillParameter' => [ 'shape' => 'RoomSkillParameter', ], ], ], 'PutRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutSkillAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'AuthorizationResult', 'SkillId', ], 'members' => [ 'AuthorizationResult' => [ 'shape' => 'AuthorizationResult', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'PutSkillAuthorizationResponse' => [ 'type' => 'structure', 'members' => [], ], 'RawPhoneNumber' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^[\\+0-9\\#\\,\\(][\\+0-9\\-\\.\\/\\(\\)\\,\\#\\s]+$', 'sensitive' => true, ], 'RegisterAVSDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'UserCode', 'ProductId', 'AmazonId', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientId', ], 'UserCode' => [ 'shape' => 'UserCode', ], 'ProductId' => [ 'shape' => 'ProductId', ], 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumberForAVS', ], 'AmazonId' => [ 'shape' => 'AmazonId', ], 'RoomArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'RegisterAVSDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'RejectSkillRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', ], 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'RejectSkillResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReleaseDate' => [ 'type' => 'string', ], 'RequireCheckIn' => [ 'type' => 'structure', 'members' => [ 'ReleaseAfterMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'RequirePin' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', 'OPTIONAL', ], ], 'ResolveRoomRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'SkillId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'ResolveRoomResponse' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'RoomSkillParameters' => [ 'shape' => 'RoomSkillParameters', ], ], ], 'ResourceAssociatedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], 'exception' => true, ], 'ReviewKey' => [ 'type' => 'string', ], 'ReviewValue' => [ 'type' => 'string', ], 'Reviews' => [ 'type' => 'map', 'key' => [ 'shape' => 'ReviewKey', ], 'value' => [ 'shape' => 'ReviewValue', ], ], 'RevokeInvitationRequest' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'RevokeInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Room' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'RoomData' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], ], ], 'RoomDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomData', ], ], 'RoomDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'RoomName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'RoomSkillParameter' => [ 'type' => 'structure', 'required' => [ 'ParameterKey', 'ParameterValue', ], 'members' => [ 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], 'ParameterValue' => [ 'shape' => 'RoomSkillParameterValue', ], ], ], 'RoomSkillParameterKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RoomSkillParameterValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'RoomSkillParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomSkillParameter', ], ], 'S3KeyPrefix' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '[A-Za-z0-9!_\\-\\.\\*\'()/]*', ], 'SampleUtterances' => [ 'type' => 'list', 'member' => [ 'shape' => 'Utterance', ], ], 'SearchAddressBooksRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchAddressBooksResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBooks' => [ 'shape' => 'AddressBookDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchContactsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchContactsResponse' => [ 'type' => 'structure', 'members' => [ 'Contacts' => [ 'shape' => 'ContactDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Devices' => [ 'shape' => 'DeviceDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchNetworkProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchNetworkProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkProfiles' => [ 'shape' => 'NetworkProfileDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Profiles' => [ 'shape' => 'ProfileDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchRoomsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchRoomsResponse' => [ 'type' => 'structure', 'members' => [ 'Rooms' => [ 'shape' => 'RoomDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchSkillGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchSkillGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroups' => [ 'shape' => 'SkillGroupDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchUsersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SendAnnouncementRequest' => [ 'type' => 'structure', 'required' => [ 'RoomFilters', 'Content', 'ClientRequestToken', ], 'members' => [ 'RoomFilters' => [ 'shape' => 'FilterList', ], 'Content' => [ 'shape' => 'Content', ], 'TimeToLiveInSeconds' => [ 'shape' => 'TimeToLiveInSeconds', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'SendAnnouncementResponse' => [ 'type' => 'structure', 'members' => [ 'AnnouncementArn' => [ 'shape' => 'Arn', ], ], ], 'SendInvitationRequest' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], ], ], 'SendInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ShortDescription' => [ 'type' => 'string', ], 'ShortSkillIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillId', ], 'max' => 3, 'min' => 0, ], 'SipAddress' => [ 'type' => 'structure', 'required' => [ 'Uri', 'Type', ], 'members' => [ 'Uri' => [ 'shape' => 'SipUri', ], 'Type' => [ 'shape' => 'SipType', ], ], ], 'SipAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SipAddress', ], 'max' => 1, 'min' => 0, ], 'SipType' => [ 'type' => 'string', 'enum' => [ 'WORK', ], 'sensitive' => true, ], 'SipUri' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^sip[s]?:([^@:]+)\\@([^@]+)$', 'sensitive' => true, ], 'SkillDetails' => [ 'type' => 'structure', 'members' => [ 'ProductDescription' => [ 'shape' => 'ProductDescription', ], 'InvocationPhrase' => [ 'shape' => 'InvocationPhrase', ], 'ReleaseDate' => [ 'shape' => 'ReleaseDate', ], 'EndUserLicenseAgreement' => [ 'shape' => 'EndUserLicenseAgreement', ], 'GenericKeywords' => [ 'shape' => 'GenericKeywords', ], 'BulletPoints' => [ 'shape' => 'BulletPoints', ], 'NewInThisVersionBulletPoints' => [ 'shape' => 'NewInThisVersionBulletPoints', ], 'SkillTypes' => [ 'shape' => 'SkillTypes', ], 'Reviews' => [ 'shape' => 'Reviews', ], 'DeveloperInfo' => [ 'shape' => 'DeveloperInfo', ], ], ], 'SkillGroup' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'SkillGroupData' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'SkillGroupDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillGroupData', ], ], 'SkillGroupDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillGroupName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillId' => [ 'type' => 'string', 'pattern' => '(^amzn1\\.ask\\.skill\\.[0-9a-f\\-]{1,200})|(^amzn1\\.echo-sdk-ams\\.app\\.[0-9a-f\\-]{1,200})', ], 'SkillListMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'SkillName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillNotLinkedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'SkillStoreType' => [ 'type' => 'string', ], 'SkillSummary' => [ 'type' => 'structure', 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], 'SkillName' => [ 'shape' => 'SkillName', ], 'SupportsLinking' => [ 'shape' => 'boolean', ], 'EnablementType' => [ 'shape' => 'EnablementType', ], 'SkillType' => [ 'shape' => 'SkillType', ], ], ], 'SkillSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillSummary', ], ], 'SkillType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'SkillTypeFilter' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', 'ALL', ], ], 'SkillTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillStoreType', ], ], 'SkillsStoreSkill' => [ 'type' => 'structure', 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], 'SkillName' => [ 'shape' => 'SkillName', ], 'ShortDescription' => [ 'shape' => 'ShortDescription', ], 'IconUrl' => [ 'shape' => 'IconUrl', ], 'SampleUtterances' => [ 'shape' => 'SampleUtterances', ], 'SkillDetails' => [ 'shape' => 'SkillDetails', ], 'SupportsLinking' => [ 'shape' => 'boolean', ], ], ], 'SkillsStoreSkillList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillsStoreSkill', ], ], 'SmartHomeAppliance' => [ 'type' => 'structure', 'members' => [ 'FriendlyName' => [ 'shape' => 'ApplianceFriendlyName', ], 'Description' => [ 'shape' => 'ApplianceDescription', ], 'ManufacturerName' => [ 'shape' => 'ApplianceManufacturerName', ], ], ], 'SmartHomeApplianceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SmartHomeAppliance', ], ], 'SoftwareVersion' => [ 'type' => 'string', ], 'Sort' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'SortKey', ], 'Value' => [ 'shape' => 'SortValue', ], ], ], 'SortKey' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'SortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sort', ], 'max' => 25, ], 'SortValue' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'Ssml' => [ 'type' => 'structure', 'required' => [ 'Locale', 'Value', ], 'members' => [ 'Locale' => [ 'shape' => 'Locale', ], 'Value' => [ 'shape' => 'SsmlValue', ], ], ], 'SsmlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ssml', ], 'max' => 1, ], 'SsmlValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'StartDeviceSyncRequest' => [ 'type' => 'structure', 'required' => [ 'Features', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'DeviceArn' => [ 'shape' => 'Arn', ], 'Features' => [ 'shape' => 'Features', ], ], ], 'StartDeviceSyncResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartSmartHomeApplianceDiscoveryRequest' => [ 'type' => 'structure', 'required' => [ 'RoomArn', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'StartSmartHomeApplianceDiscoveryResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TemperatureUnit' => [ 'type' => 'string', 'enum' => [ 'FAHRENHEIT', 'CELSIUS', ], ], 'Text' => [ 'type' => 'structure', 'required' => [ 'Locale', 'Value', ], 'members' => [ 'Locale' => [ 'shape' => 'Locale', ], 'Value' => [ 'shape' => 'TextValue', ], ], ], 'TextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Text', ], 'max' => 1, ], 'TextValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'TimeToLiveInSeconds' => [ 'type' => 'integer', 'max' => 3600, 'min' => 1, ], 'Timezone' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TotalCount' => [ 'type' => 'integer', ], 'TrustAnchor' => [ 'type' => 'string', 'pattern' => '-{5}BEGIN CERTIFICATE-{5}\\u000D?\\u000A([A-Za-z0-9/+]{64}\\u000D?\\u000A)*[A-Za-z0-9/+]{1,64}={0,2}\\u000D?\\u000A-{5}END CERTIFICATE-{5}(\\u000D?\\u000A)?', ], 'TrustAnchorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustAnchor', ], 'max' => 5, 'min' => 1, ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'TagKeys', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'UpdateAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBusinessReportScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'ScheduleArn', ], 'members' => [ 'ScheduleArn' => [ 'shape' => 'Arn', ], 'S3BucketName' => [ 'shape' => 'CustomerS3BucketName', ], 'S3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'Format' => [ 'shape' => 'BusinessReportFormat', ], 'ScheduleName' => [ 'shape' => 'BusinessReportScheduleName', ], 'Recurrence' => [ 'shape' => 'BusinessReportRecurrence', ], ], ], 'UpdateBusinessReportScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConferenceProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ConferenceProviderArn', 'ConferenceProviderType', 'MeetingSetting', ], 'members' => [ 'ConferenceProviderArn' => [ 'shape' => 'Arn', ], 'ConferenceProviderType' => [ 'shape' => 'ConferenceProviderType', ], 'IPDialIn' => [ 'shape' => 'IPDialIn', ], 'PSTNDialIn' => [ 'shape' => 'PSTNDialIn', ], 'MeetingSetting' => [ 'shape' => 'MeetingSetting', ], ], ], 'UpdateConferenceProviderResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'RawPhoneNumber', ], 'PhoneNumbers' => [ 'shape' => 'PhoneNumberList', ], 'SipAddresses' => [ 'shape' => 'SipAddressList', ], ], ], 'UpdateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], ], ], 'UpdateDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEndOfMeetingReminder' => [ 'type' => 'structure', 'members' => [ 'ReminderAtMinutes' => [ 'shape' => 'EndOfMeetingReminderMinutesList', ], 'ReminderType' => [ 'shape' => 'EndOfMeetingReminderType', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateGatewayGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayGroupArn', ], 'members' => [ 'GatewayGroupArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayGroupName', ], 'Description' => [ 'shape' => 'GatewayGroupDescription', ], ], ], 'UpdateGatewayGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'GatewayName', ], 'Description' => [ 'shape' => 'GatewayDescription', ], 'SoftwareVersion' => [ 'shape' => 'GatewayVersion', ], ], ], 'UpdateGatewayResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInstantBooking' => [ 'type' => 'structure', 'members' => [ 'DurationInMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateMeetingRoomConfiguration' => [ 'type' => 'structure', 'members' => [ 'RoomUtilizationMetricsEnabled' => [ 'shape' => 'Boolean', ], 'EndOfMeetingReminder' => [ 'shape' => 'UpdateEndOfMeetingReminder', ], 'InstantBooking' => [ 'shape' => 'UpdateInstantBooking', ], 'RequireCheckIn' => [ 'shape' => 'UpdateRequireCheckIn', ], 'ProactiveJoin' => [ 'shape' => 'UpdateProactiveJoin', ], ], ], 'UpdateNetworkProfileRequest' => [ 'type' => 'structure', 'required' => [ 'NetworkProfileArn', ], 'members' => [ 'NetworkProfileArn' => [ 'shape' => 'Arn', ], 'NetworkProfileName' => [ 'shape' => 'NetworkProfileName', ], 'Description' => [ 'shape' => 'NetworkProfileDescription', ], 'CurrentPassword' => [ 'shape' => 'CurrentWiFiPassword', ], 'NextPassword' => [ 'shape' => 'NextWiFiPassword', ], 'CertificateAuthorityArn' => [ 'shape' => 'Arn', ], 'TrustAnchors' => [ 'shape' => 'TrustAnchorList', ], ], ], 'UpdateNetworkProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateProactiveJoin' => [ 'type' => 'structure', 'required' => [ 'EnabledByMotion', ], 'members' => [ 'EnabledByMotion' => [ 'shape' => 'Boolean', ], ], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'Address' => [ 'shape' => 'Address', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'Locale' => [ 'shape' => 'DeviceLocale', ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], 'DataRetentionOptIn' => [ 'shape' => 'Boolean', ], 'MeetingRoomConfiguration' => [ 'shape' => 'UpdateMeetingRoomConfiguration', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRequireCheckIn' => [ 'type' => 'structure', 'members' => [ 'ReleaseAfterMinutes' => [ 'shape' => 'Minutes', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'UpdateSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', ], 'UserCode' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'UserData' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'FirstName' => [ 'shape' => 'user_FirstName', ], 'LastName' => [ 'shape' => 'user_LastName', ], 'Email' => [ 'shape' => 'Email', ], 'EnrollmentStatus' => [ 'shape' => 'EnrollmentStatus', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'UserDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserData', ], ], 'UserId' => [ 'type' => 'string', 'pattern' => 'amzn1\\.[A-Za-z0-9+-\\/=.]{1,300}', ], 'Utterance' => [ 'type' => 'string', ], 'Value' => [ 'type' => 'string', 'min' => 1, ], 'WakeWord' => [ 'type' => 'string', 'enum' => [ 'ALEXA', 'AMAZON', 'ECHO', 'COMPUTER', ], ], 'boolean' => [ 'type' => 'boolean', ], 'user_FirstName' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '([A-Za-z\\-\' 0-9._]|\\p{IsLetter})*', ], 'user_LastName' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '([A-Za-z\\-\' 0-9._]|\\p{IsLetter})*', ], 'user_UserId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9@_+.-]*', ], ], 'deprecated' => true, 'deprecatedMessage' => 'Alexa For Business is no longer supported',];
