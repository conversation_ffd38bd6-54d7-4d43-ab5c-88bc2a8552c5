Documentation
=============

If you're here, you're looking for documentation for Requests! The documents
here are prose; you might also want to check out the [API documentation][].

[API documentation]: http://requests.ryanmccue.info/api/

* Introduction
	* [Goals][goals]
	* [Why should I use Requests instead of X?][why-requests]
* Usage
	* [Making a request][usage]
	* [Advanced usage][usage-advanced]
	* [Authenticating your request][authentication]
* Advanced Usage
	* [Custom authentication][authentication-custom]
	* [Requests through proxy][proxy]
	* [Hooking system][hooks]

[goals]: goals.md
[why-requests]: why-requests.md
[usage]: usage.md
[usage-advanced]: usage-advanced.md
[authentication]: authentication.md
[authentication-custom]: authentication-custom.md
[hooks]: hooks.md
[proxy]: proxy.md