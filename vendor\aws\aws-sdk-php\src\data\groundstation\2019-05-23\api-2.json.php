<?php
// This file was auto-generated from sdk-root/src/data/groundstation/2019-05-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-05-23', 'endpointPrefix' => 'groundstation', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Ground Station', 'serviceId' => 'GroundStation', 'signatureVersion' => 'v4', 'signingName' => 'groundstation', 'uid' => 'groundstation-2019-05-23', ], 'operations' => [ 'CancelContact' => [ 'name' => 'CancelContact', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact/{contactId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelContactRequest', ], 'output' => [ 'shape' => 'ContactIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateConfig' => [ 'name' => 'CreateConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfigRequest', ], 'output' => [ 'shape' => 'ConfigIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateDataflowEndpointGroup' => [ 'name' => 'CreateDataflowEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/dataflowEndpointGroup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataflowEndpointGroupRequest', ], 'output' => [ 'shape' => 'DataflowEndpointGroupIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateEphemeris' => [ 'name' => 'CreateEphemeris', 'http' => [ 'method' => 'POST', 'requestUri' => '/ephemeris', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEphemerisRequest', ], 'output' => [ 'shape' => 'EphemerisIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateMissionProfile' => [ 'name' => 'CreateMissionProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/missionprofile', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMissionProfileRequest', ], 'output' => [ 'shape' => 'MissionProfileIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteConfig' => [ 'name' => 'DeleteConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/config/{configType}/{configId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfigRequest', ], 'output' => [ 'shape' => 'ConfigIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteDataflowEndpointGroup' => [ 'name' => 'DeleteDataflowEndpointGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dataflowEndpointGroup/{dataflowEndpointGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataflowEndpointGroupRequest', ], 'output' => [ 'shape' => 'DataflowEndpointGroupIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteEphemeris' => [ 'name' => 'DeleteEphemeris', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ephemeris/{ephemerisId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEphemerisRequest', ], 'output' => [ 'shape' => 'EphemerisIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteMissionProfile' => [ 'name' => 'DeleteMissionProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/missionprofile/{missionProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMissionProfileRequest', ], 'output' => [ 'shape' => 'MissionProfileIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeContact' => [ 'name' => 'DescribeContact', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/{contactId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeContactRequest', ], 'output' => [ 'shape' => 'DescribeContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeEphemeris' => [ 'name' => 'DescribeEphemeris', 'http' => [ 'method' => 'GET', 'requestUri' => '/ephemeris/{ephemerisId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeEphemerisRequest', ], 'output' => [ 'shape' => 'DescribeEphemerisResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentConfiguration' => [ 'name' => 'GetAgentConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/agent/{agentId}/configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentConfigurationRequest', ], 'output' => [ 'shape' => 'GetAgentConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfig' => [ 'name' => 'GetConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/config/{configType}/{configId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfigRequest', ], 'output' => [ 'shape' => 'GetConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDataflowEndpointGroup' => [ 'name' => 'GetDataflowEndpointGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataflowEndpointGroup/{dataflowEndpointGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataflowEndpointGroupRequest', ], 'output' => [ 'shape' => 'GetDataflowEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMinuteUsage' => [ 'name' => 'GetMinuteUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/minute-usage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMinuteUsageRequest', ], 'output' => [ 'shape' => 'GetMinuteUsageResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMissionProfile' => [ 'name' => 'GetMissionProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/missionprofile/{missionProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMissionProfileRequest', ], 'output' => [ 'shape' => 'GetMissionProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSatellite' => [ 'name' => 'GetSatellite', 'http' => [ 'method' => 'GET', 'requestUri' => '/satellite/{satelliteId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSatelliteRequest', ], 'output' => [ 'shape' => 'GetSatelliteResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListConfigs' => [ 'name' => 'ListConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfigsRequest', ], 'output' => [ 'shape' => 'ListConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListContacts' => [ 'name' => 'ListContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/contacts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListContactsRequest', ], 'output' => [ 'shape' => 'ListContactsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDataflowEndpointGroups' => [ 'name' => 'ListDataflowEndpointGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataflowEndpointGroup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataflowEndpointGroupsRequest', ], 'output' => [ 'shape' => 'ListDataflowEndpointGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEphemerides' => [ 'name' => 'ListEphemerides', 'http' => [ 'method' => 'POST', 'requestUri' => '/ephemerides', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEphemeridesRequest', ], 'output' => [ 'shape' => 'ListEphemeridesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListGroundStations' => [ 'name' => 'ListGroundStations', 'http' => [ 'method' => 'GET', 'requestUri' => '/groundstation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGroundStationsRequest', ], 'output' => [ 'shape' => 'ListGroundStationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListMissionProfiles' => [ 'name' => 'ListMissionProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/missionprofile', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMissionProfilesRequest', ], 'output' => [ 'shape' => 'ListMissionProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSatellites' => [ 'name' => 'ListSatellites', 'http' => [ 'method' => 'GET', 'requestUri' => '/satellite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSatellitesRequest', ], 'output' => [ 'shape' => 'ListSatellitesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterAgent' => [ 'name' => 'RegisterAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agent', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterAgentRequest', ], 'output' => [ 'shape' => 'RegisterAgentResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ReserveContact' => [ 'name' => 'ReserveContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ReserveContactRequest', ], 'output' => [ 'shape' => 'ContactIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateAgentStatus' => [ 'name' => 'UpdateAgentStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agent/{agentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentStatusRequest', ], 'output' => [ 'shape' => 'UpdateAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfig' => [ 'name' => 'UpdateConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/config/{configType}/{configId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfigRequest', ], 'output' => [ 'shape' => 'ConfigIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateEphemeris' => [ 'name' => 'UpdateEphemeris', 'http' => [ 'method' => 'PUT', 'requestUri' => '/ephemeris/{ephemerisId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEphemerisRequest', ], 'output' => [ 'shape' => 'EphemerisIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateMissionProfile' => [ 'name' => 'UpdateMissionProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/missionprofile/{missionProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMissionProfileRequest', ], 'output' => [ 'shape' => 'MissionProfileIdResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DependencyException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AWSRegion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\w-]+$', ], 'AgentCpuCoresList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], 'max' => 256, 'min' => 0, ], 'AgentDetails' => [ 'type' => 'structure', 'required' => [ 'agentVersion', 'componentVersions', 'instanceId', 'instanceType', ], 'members' => [ 'agentCpuCores' => [ 'shape' => 'AgentCpuCoresList', ], 'agentVersion' => [ 'shape' => 'VersionString', ], 'componentVersions' => [ 'shape' => 'ComponentVersionList', ], 'instanceId' => [ 'shape' => 'InstanceId', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'reservedCpuCores' => [ 'shape' => 'AgentCpuCoresList', ], ], ], 'AgentStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILED', 'ACTIVE', 'INACTIVE', ], ], 'AggregateStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'signatureMap' => [ 'shape' => 'SignatureMap', ], 'status' => [ 'shape' => 'AgentStatus', ], ], ], 'AngleUnits' => [ 'type' => 'string', 'enum' => [ 'DEGREE_ANGLE', 'RADIAN', ], ], 'AntennaDemodDecodeDetails' => [ 'type' => 'structure', 'members' => [ 'outputNode' => [ 'shape' => 'String', ], ], ], 'AntennaDownlinkConfig' => [ 'type' => 'structure', 'required' => [ 'spectrumConfig', ], 'members' => [ 'spectrumConfig' => [ 'shape' => 'SpectrumConfig', ], ], ], 'AntennaDownlinkDemodDecodeConfig' => [ 'type' => 'structure', 'required' => [ 'decodeConfig', 'demodulationConfig', 'spectrumConfig', ], 'members' => [ 'decodeConfig' => [ 'shape' => 'DecodeConfig', ], 'demodulationConfig' => [ 'shape' => 'DemodulationConfig', ], 'spectrumConfig' => [ 'shape' => 'SpectrumConfig', ], ], ], 'AntennaUplinkConfig' => [ 'type' => 'structure', 'required' => [ 'spectrumConfig', 'targetEirp', ], 'members' => [ 'spectrumConfig' => [ 'shape' => 'UplinkSpectrumConfig', ], 'targetEirp' => [ 'shape' => 'Eirp', ], 'transmitDisabled' => [ 'shape' => 'Boolean', ], ], ], 'AnyArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 5, 'pattern' => '^(arn:aws:)[\\s\\S]{0,1024}$', ], 'AuditResults' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'AwsGroundStationAgentEndpoint' => [ 'type' => 'structure', 'required' => [ 'egressAddress', 'ingressAddress', 'name', ], 'members' => [ 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'auditResults' => [ 'shape' => 'AuditResults', ], 'egressAddress' => [ 'shape' => 'ConnectionDetails', ], 'ingressAddress' => [ 'shape' => 'RangedConnectionDetails', ], 'name' => [ 'shape' => 'SafeName', ], ], ], 'BandwidthUnits' => [ 'type' => 'string', 'enum' => [ 'GHz', 'MHz', 'kHz', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BucketArn' => [ 'type' => 'string', ], 'CancelContactRequest' => [ 'type' => 'structure', 'required' => [ 'contactId', ], 'members' => [ 'contactId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'contactId', ], ], ], 'CapabilityArn' => [ 'type' => 'string', ], 'CapabilityArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityArn', ], 'max' => 20, 'min' => 1, ], 'CapabilityHealth' => [ 'type' => 'string', 'enum' => [ 'UNHEALTHY', 'HEALTHY', ], ], 'CapabilityHealthReason' => [ 'type' => 'string', 'enum' => [ 'NO_REGISTERED_AGENT', 'INVALID_IP_OWNERSHIP', 'NOT_AUTHORIZED_TO_CREATE_SLR', 'UNVERIFIED_IP_OWNERSHIP', 'INITIALIZING_DATAPLANE', 'DATAPLANE_FAILURE', 'HEALTHY', ], ], 'CapabilityHealthReasonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityHealthReason', ], 'max' => 500, 'min' => 0, ], 'ComponentStatusData' => [ 'type' => 'structure', 'required' => [ 'capabilityArn', 'componentType', 'dataflowId', 'status', ], 'members' => [ 'bytesReceived' => [ 'shape' => 'Long', ], 'bytesSent' => [ 'shape' => 'Long', ], 'capabilityArn' => [ 'shape' => 'CapabilityArn', ], 'componentType' => [ 'shape' => 'ComponentTypeString', ], 'dataflowId' => [ 'shape' => 'Uuid', ], 'packetsDropped' => [ 'shape' => 'Long', ], 'status' => [ 'shape' => 'AgentStatus', ], ], ], 'ComponentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentStatusData', ], 'max' => 20, 'min' => 0, ], 'ComponentTypeString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_]{1,64}$', ], 'ComponentVersion' => [ 'type' => 'structure', 'required' => [ 'componentType', 'versions', ], 'members' => [ 'componentType' => [ 'shape' => 'ComponentTypeString', ], 'versions' => [ 'shape' => 'VersionStringList', ], ], ], 'ComponentVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentVersion', ], 'max' => 20, 'min' => 1, ], 'ConfigArn' => [ 'type' => 'string', ], 'ConfigCapabilityType' => [ 'type' => 'string', 'enum' => [ 'antenna-downlink', 'antenna-downlink-demod-decode', 'antenna-uplink', 'dataflow-endpoint', 'tracking', 'uplink-echo', 's3-recording', ], ], 'ConfigDetails' => [ 'type' => 'structure', 'members' => [ 'antennaDemodDecodeDetails' => [ 'shape' => 'AntennaDemodDecodeDetails', ], 'endpointDetails' => [ 'shape' => 'EndpointDetails', ], 's3RecordingDetails' => [ 'shape' => 'S3RecordingDetails', ], ], 'union' => true, ], 'ConfigIdResponse' => [ 'type' => 'structure', 'members' => [ 'configArn' => [ 'shape' => 'ConfigArn', ], 'configId' => [ 'shape' => 'String', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', ], ], ], 'ConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigListItem', ], ], 'ConfigListItem' => [ 'type' => 'structure', 'members' => [ 'configArn' => [ 'shape' => 'ConfigArn', ], 'configId' => [ 'shape' => 'String', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', ], 'name' => [ 'shape' => 'String', ], ], ], 'ConfigTypeData' => [ 'type' => 'structure', 'members' => [ 'antennaDownlinkConfig' => [ 'shape' => 'AntennaDownlinkConfig', ], 'antennaDownlinkDemodDecodeConfig' => [ 'shape' => 'AntennaDownlinkDemodDecodeConfig', ], 'antennaUplinkConfig' => [ 'shape' => 'AntennaUplinkConfig', ], 'dataflowEndpointConfig' => [ 'shape' => 'DataflowEndpointConfig', ], 's3RecordingConfig' => [ 'shape' => 'S3RecordingConfig', ], 'trackingConfig' => [ 'shape' => 'TrackingConfig', ], 'uplinkEchoConfig' => [ 'shape' => 'UplinkEchoConfig', ], ], 'union' => true, ], 'ConnectionDetails' => [ 'type' => 'structure', 'required' => [ 'socketAddress', ], 'members' => [ 'mtu' => [ 'shape' => 'Integer', ], 'socketAddress' => [ 'shape' => 'SocketAddress', ], ], ], 'ContactData' => [ 'type' => 'structure', 'members' => [ 'contactId' => [ 'shape' => 'Uuid', ], 'contactStatus' => [ 'shape' => 'ContactStatus', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'errorMessage' => [ 'shape' => 'String', ], 'groundStation' => [ 'shape' => 'String', ], 'maximumElevation' => [ 'shape' => 'Elevation', ], 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'postPassEndTime' => [ 'shape' => 'Timestamp', ], 'prePassStartTime' => [ 'shape' => 'Timestamp', ], 'region' => [ 'shape' => 'String', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagsMap', ], 'visibilityEndTime' => [ 'shape' => 'Timestamp', ], 'visibilityStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'ContactIdResponse' => [ 'type' => 'structure', 'members' => [ 'contactId' => [ 'shape' => 'Uuid', ], ], ], 'ContactList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactData', ], ], 'ContactStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'AWS_CANCELLED', 'AWS_FAILED', 'CANCELLED', 'CANCELLING', 'COMPLETED', 'FAILED', 'FAILED_TO_SCHEDULE', 'PASS', 'POSTPASS', 'PREPASS', 'SCHEDULED', 'SCHEDULING', ], ], 'CreateConfigRequest' => [ 'type' => 'structure', 'required' => [ 'configData', 'name', ], 'members' => [ 'configData' => [ 'shape' => 'ConfigTypeData', ], 'name' => [ 'shape' => 'SafeName', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateDataflowEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'endpointDetails', ], 'members' => [ 'contactPostPassDurationSeconds' => [ 'shape' => 'DataflowEndpointGroupDurationInSeconds', ], 'contactPrePassDurationSeconds' => [ 'shape' => 'DataflowEndpointGroupDurationInSeconds', ], 'endpointDetails' => [ 'shape' => 'EndpointDetailsList', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateEphemerisRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'satelliteId', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'ephemeris' => [ 'shape' => 'EphemerisData', ], 'expirationTime' => [ 'shape' => 'Timestamp', ], 'kmsKeyArn' => [ 'shape' => 'KeyArn', ], 'name' => [ 'shape' => 'SafeName', ], 'priority' => [ 'shape' => 'CustomerEphemerisPriority', ], 'satelliteId' => [ 'shape' => 'Uuid', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateMissionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'dataflowEdges', 'minimumViableContactDurationSeconds', 'name', 'trackingConfigArn', ], 'members' => [ 'contactPostPassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'contactPrePassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'dataflowEdges' => [ 'shape' => 'DataflowEdgeList', ], 'minimumViableContactDurationSeconds' => [ 'shape' => 'PositiveDurationInSeconds', ], 'name' => [ 'shape' => 'SafeName', ], 'streamsKmsKey' => [ 'shape' => 'KmsKey', ], 'streamsKmsRole' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagsMap', ], 'trackingConfigArn' => [ 'shape' => 'ConfigArn', ], ], ], 'Criticality' => [ 'type' => 'string', 'enum' => [ 'PREFERRED', 'REMOVED', 'REQUIRED', ], ], 'CustomerEphemerisPriority' => [ 'type' => 'integer', 'box' => true, 'max' => 99999, 'min' => 1, ], 'DataflowDetail' => [ 'type' => 'structure', 'members' => [ 'destination' => [ 'shape' => 'Destination', ], 'errorMessage' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'Source', ], ], ], 'DataflowEdge' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigArn', ], 'max' => 2, 'min' => 2, ], 'DataflowEdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataflowEdge', ], 'max' => 500, 'min' => 0, ], 'DataflowEndpoint' => [ 'type' => 'structure', 'members' => [ 'address' => [ 'shape' => 'SocketAddress', ], 'mtu' => [ 'shape' => 'DataflowEndpointMtuInteger', ], 'name' => [ 'shape' => 'SafeName', ], 'status' => [ 'shape' => 'EndpointStatus', ], ], ], 'DataflowEndpointConfig' => [ 'type' => 'structure', 'required' => [ 'dataflowEndpointName', ], 'members' => [ 'dataflowEndpointName' => [ 'shape' => 'String', ], 'dataflowEndpointRegion' => [ 'shape' => 'String', ], ], ], 'DataflowEndpointGroupArn' => [ 'type' => 'string', ], 'DataflowEndpointGroupDurationInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 480, 'min' => 120, ], 'DataflowEndpointGroupIdResponse' => [ 'type' => 'structure', 'members' => [ 'dataflowEndpointGroupId' => [ 'shape' => 'Uuid', ], ], ], 'DataflowEndpointGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataflowEndpointListItem', ], ], 'DataflowEndpointListItem' => [ 'type' => 'structure', 'members' => [ 'dataflowEndpointGroupArn' => [ 'shape' => 'DataflowEndpointGroupArn', ], 'dataflowEndpointGroupId' => [ 'shape' => 'Uuid', ], ], ], 'DataflowEndpointMtuInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1500, 'min' => 1400, ], 'DataflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataflowDetail', ], ], 'DecodeConfig' => [ 'type' => 'structure', 'required' => [ 'unvalidatedJSON', ], 'members' => [ 'unvalidatedJSON' => [ 'shape' => 'JsonString', ], ], ], 'DeleteConfigRequest' => [ 'type' => 'structure', 'required' => [ 'configId', 'configType', ], 'members' => [ 'configId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'configId', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', 'location' => 'uri', 'locationName' => 'configType', ], ], ], 'DeleteDataflowEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'dataflowEndpointGroupId', ], 'members' => [ 'dataflowEndpointGroupId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'dataflowEndpointGroupId', ], ], ], 'DeleteEphemerisRequest' => [ 'type' => 'structure', 'required' => [ 'ephemerisId', ], 'members' => [ 'ephemerisId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'ephemerisId', ], ], ], 'DeleteMissionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'missionProfileId', ], 'members' => [ 'missionProfileId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'missionProfileId', ], ], ], 'DemodulationConfig' => [ 'type' => 'structure', 'required' => [ 'unvalidatedJSON', ], 'members' => [ 'unvalidatedJSON' => [ 'shape' => 'JsonString', ], ], ], 'DependencyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'parameterName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 531, ], 'exception' => true, 'fault' => true, ], 'DescribeContactRequest' => [ 'type' => 'structure', 'required' => [ 'contactId', ], 'members' => [ 'contactId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'contactId', ], ], ], 'DescribeContactResponse' => [ 'type' => 'structure', 'members' => [ 'contactId' => [ 'shape' => 'Uuid', ], 'contactStatus' => [ 'shape' => 'ContactStatus', ], 'dataflowList' => [ 'shape' => 'DataflowList', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'errorMessage' => [ 'shape' => 'String', ], 'groundStation' => [ 'shape' => 'String', ], 'maximumElevation' => [ 'shape' => 'Elevation', ], 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'postPassEndTime' => [ 'shape' => 'Timestamp', ], 'prePassStartTime' => [ 'shape' => 'Timestamp', ], 'region' => [ 'shape' => 'String', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagsMap', ], 'visibilityEndTime' => [ 'shape' => 'Timestamp', ], 'visibilityStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeEphemerisRequest' => [ 'type' => 'structure', 'required' => [ 'ephemerisId', ], 'members' => [ 'ephemerisId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'ephemerisId', ], ], ], 'DescribeEphemerisResponse' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'enabled' => [ 'shape' => 'Boolean', ], 'ephemerisId' => [ 'shape' => 'Uuid', ], 'invalidReason' => [ 'shape' => 'EphemerisInvalidReason', ], 'name' => [ 'shape' => 'SafeName', ], 'priority' => [ 'shape' => 'EphemerisPriority', ], 'satelliteId' => [ 'shape' => 'Uuid', ], 'status' => [ 'shape' => 'EphemerisStatus', ], 'suppliedData' => [ 'shape' => 'EphemerisTypeDescription', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'Destination' => [ 'type' => 'structure', 'members' => [ 'configDetails' => [ 'shape' => 'ConfigDetails', ], 'configId' => [ 'shape' => 'Uuid', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', ], 'dataflowDestinationRegion' => [ 'shape' => 'String', ], ], ], 'DiscoveryData' => [ 'type' => 'structure', 'required' => [ 'capabilityArns', 'privateIpAddresses', 'publicIpAddresses', ], 'members' => [ 'capabilityArns' => [ 'shape' => 'CapabilityArnList', ], 'privateIpAddresses' => [ 'shape' => 'IpAddressList', ], 'publicIpAddresses' => [ 'shape' => 'IpAddressList', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'DurationInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 21600, 'min' => 0, ], 'Eirp' => [ 'type' => 'structure', 'required' => [ 'units', 'value', ], 'members' => [ 'units' => [ 'shape' => 'EirpUnits', ], 'value' => [ 'shape' => 'Double', ], ], ], 'EirpUnits' => [ 'type' => 'string', 'enum' => [ 'dBW', ], ], 'Elevation' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'AngleUnits', ], 'value' => [ 'shape' => 'Double', ], ], ], 'EndpointDetails' => [ 'type' => 'structure', 'members' => [ 'awsGroundStationAgentEndpoint' => [ 'shape' => 'AwsGroundStationAgentEndpoint', ], 'endpoint' => [ 'shape' => 'DataflowEndpoint', ], 'healthReasons' => [ 'shape' => 'CapabilityHealthReasonList', ], 'healthStatus' => [ 'shape' => 'CapabilityHealth', ], 'securityDetails' => [ 'shape' => 'SecurityDetails', ], ], ], 'EndpointDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointDetails', ], 'max' => 500, 'min' => 0, ], 'EndpointStatus' => [ 'type' => 'string', 'enum' => [ 'created', 'creating', 'deleted', 'deleting', 'failed', ], ], 'EphemeridesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EphemerisItem', ], 'max' => 500, 'min' => 1, ], 'EphemerisData' => [ 'type' => 'structure', 'members' => [ 'oem' => [ 'shape' => 'OEMEphemeris', ], 'tle' => [ 'shape' => 'TLEEphemeris', ], ], 'union' => true, ], 'EphemerisDescription' => [ 'type' => 'structure', 'members' => [ 'ephemerisData' => [ 'shape' => 'UnboundedString', ], 'sourceS3Object' => [ 'shape' => 'S3Object', ], ], ], 'EphemerisIdResponse' => [ 'type' => 'structure', 'members' => [ 'ephemerisId' => [ 'shape' => 'Uuid', ], ], ], 'EphemerisInvalidReason' => [ 'type' => 'string', 'enum' => [ 'METADATA_INVALID', 'TIME_RANGE_INVALID', 'TRAJECTORY_INVALID', 'KMS_KEY_INVALID', 'VALIDATION_ERROR', ], ], 'EphemerisItem' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'enabled' => [ 'shape' => 'Boolean', ], 'ephemerisId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'SafeName', ], 'priority' => [ 'shape' => 'EphemerisPriority', ], 'sourceS3Object' => [ 'shape' => 'S3Object', ], 'status' => [ 'shape' => 'EphemerisStatus', ], ], ], 'EphemerisMetaData' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'ephemerisId' => [ 'shape' => 'Uuid', ], 'epoch' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'SafeName', ], 'source' => [ 'shape' => 'EphemerisSource', ], ], ], 'EphemerisPriority' => [ 'type' => 'integer', 'box' => true, 'max' => 99999, 'min' => 0, ], 'EphemerisSource' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_PROVIDED', 'SPACE_TRACK', ], ], 'EphemerisStatus' => [ 'type' => 'string', 'enum' => [ 'VALIDATING', 'INVALID', 'ERROR', 'ENABLED', 'DISABLED', 'EXPIRED', ], ], 'EphemerisStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EphemerisStatus', ], 'max' => 500, 'min' => 0, ], 'EphemerisTypeDescription' => [ 'type' => 'structure', 'members' => [ 'oem' => [ 'shape' => 'EphemerisDescription', ], 'tle' => [ 'shape' => 'EphemerisDescription', ], ], 'union' => true, ], 'Frequency' => [ 'type' => 'structure', 'required' => [ 'units', 'value', ], 'members' => [ 'units' => [ 'shape' => 'FrequencyUnits', ], 'value' => [ 'shape' => 'Double', ], ], ], 'FrequencyBandwidth' => [ 'type' => 'structure', 'required' => [ 'units', 'value', ], 'members' => [ 'units' => [ 'shape' => 'BandwidthUnits', ], 'value' => [ 'shape' => 'Double', ], ], ], 'FrequencyUnits' => [ 'type' => 'string', 'enum' => [ 'GHz', 'MHz', 'kHz', ], ], 'GetAgentConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'GetAgentConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'agentId' => [ 'shape' => 'Uuid', ], 'taskingDocument' => [ 'shape' => 'String', ], ], ], 'GetConfigRequest' => [ 'type' => 'structure', 'required' => [ 'configId', 'configType', ], 'members' => [ 'configId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'configId', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', 'location' => 'uri', 'locationName' => 'configType', ], ], ], 'GetConfigResponse' => [ 'type' => 'structure', 'required' => [ 'configArn', 'configData', 'configId', 'name', ], 'members' => [ 'configArn' => [ 'shape' => 'ConfigArn', ], 'configData' => [ 'shape' => 'ConfigTypeData', ], 'configId' => [ 'shape' => 'String', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', ], 'name' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetDataflowEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'dataflowEndpointGroupId', ], 'members' => [ 'dataflowEndpointGroupId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'dataflowEndpointGroupId', ], ], ], 'GetDataflowEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'contactPostPassDurationSeconds' => [ 'shape' => 'DataflowEndpointGroupDurationInSeconds', ], 'contactPrePassDurationSeconds' => [ 'shape' => 'DataflowEndpointGroupDurationInSeconds', ], 'dataflowEndpointGroupArn' => [ 'shape' => 'DataflowEndpointGroupArn', ], 'dataflowEndpointGroupId' => [ 'shape' => 'Uuid', ], 'endpointsDetails' => [ 'shape' => 'EndpointDetailsList', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetMinuteUsageRequest' => [ 'type' => 'structure', 'required' => [ 'month', 'year', ], 'members' => [ 'month' => [ 'shape' => 'Month', ], 'year' => [ 'shape' => 'Year', ], ], ], 'GetMinuteUsageResponse' => [ 'type' => 'structure', 'members' => [ 'estimatedMinutesRemaining' => [ 'shape' => 'Integer', ], 'isReservedMinutesCustomer' => [ 'shape' => 'Boolean', ], 'totalReservedMinuteAllocation' => [ 'shape' => 'Integer', ], 'totalScheduledMinutes' => [ 'shape' => 'Integer', ], 'upcomingMinutesScheduled' => [ 'shape' => 'Integer', ], ], ], 'GetMissionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'missionProfileId', ], 'members' => [ 'missionProfileId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'missionProfileId', ], ], ], 'GetMissionProfileResponse' => [ 'type' => 'structure', 'members' => [ 'contactPostPassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'contactPrePassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'dataflowEdges' => [ 'shape' => 'DataflowEdgeList', ], 'minimumViableContactDurationSeconds' => [ 'shape' => 'PositiveDurationInSeconds', ], 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'missionProfileId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'SafeName', ], 'region' => [ 'shape' => 'AWSRegion', ], 'streamsKmsKey' => [ 'shape' => 'KmsKey', ], 'streamsKmsRole' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagsMap', ], 'trackingConfigArn' => [ 'shape' => 'ConfigArn', ], ], ], 'GetSatelliteRequest' => [ 'type' => 'structure', 'required' => [ 'satelliteId', ], 'members' => [ 'satelliteId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'satelliteId', ], ], ], 'GetSatelliteResponse' => [ 'type' => 'structure', 'members' => [ 'currentEphemeris' => [ 'shape' => 'EphemerisMetaData', ], 'groundStations' => [ 'shape' => 'GroundStationIdList', ], 'noradSatelliteID' => [ 'shape' => 'noradSatelliteID', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'satelliteId' => [ 'shape' => 'Uuid', ], ], ], 'GroundStationData' => [ 'type' => 'structure', 'members' => [ 'groundStationId' => [ 'shape' => 'GroundStationName', ], 'groundStationName' => [ 'shape' => 'GroundStationName', ], 'region' => [ 'shape' => 'AWSRegion', ], ], ], 'GroundStationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroundStationName', ], 'max' => 500, 'min' => 0, ], 'GroundStationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroundStationData', ], ], 'GroundStationName' => [ 'type' => 'string', 'max' => 500, 'min' => 4, 'pattern' => '^[ a-zA-Z0-9-._:=]{4,256}$', ], 'InstanceId' => [ 'type' => 'string', 'max' => 64, 'min' => 10, 'pattern' => '^[a-z0-9-]{10,64}$', ], 'InstanceType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-z0-9.-]{1,64}$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntegerRange' => [ 'type' => 'structure', 'required' => [ 'maximum', 'minimum', ], 'members' => [ 'maximum' => [ 'shape' => 'Integer', ], 'minimum' => [ 'shape' => 'Integer', ], ], ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'parameterName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 431, 'senderFault' => true, ], 'exception' => true, ], 'IpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4Address', ], 'max' => 20, 'min' => 1, ], 'IpV4Address' => [ 'type' => 'string', 'max' => 16, 'min' => 7, 'pattern' => '^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$', ], 'JsonString' => [ 'type' => 'string', 'max' => 8192, 'min' => 2, 'pattern' => '^[{}\\[\\]:.,"0-9A-z\\-_\\s]{2,8192}$', ], 'KeyAliasArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^arn:aws[a-zA-Z-]{0,16}:kms:[a-z]{2}(-[a-z]{1,16}){1,3}-\\d{1}:\\d{12}:((alias/[a-zA-Z0-9:/_-]{1,256}))$', ], 'KeyAliasName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^alias/[a-zA-Z0-9:/_-]+$', ], 'KeyArn' => [ 'type' => 'string', ], 'KmsKey' => [ 'type' => 'structure', 'members' => [ 'kmsAliasArn' => [ 'shape' => 'KeyAliasArn', ], 'kmsAliasName' => [ 'shape' => 'KeyAliasName', ], 'kmsKeyArn' => [ 'shape' => 'KeyArn', ], ], 'union' => true, ], 'ListConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'configList' => [ 'shape' => 'ConfigList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListContactsRequest' => [ 'type' => 'structure', 'required' => [ 'endTime', 'startTime', 'statusList', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'groundStation' => [ 'shape' => 'GroundStationName', ], 'maxResults' => [ 'shape' => 'PaginationMaxResults', ], 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'statusList' => [ 'shape' => 'StatusList', ], ], ], 'ListContactsResponse' => [ 'type' => 'structure', 'members' => [ 'contactList' => [ 'shape' => 'ContactList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataflowEndpointGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDataflowEndpointGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'dataflowEndpointGroupList' => [ 'shape' => 'DataflowEndpointGroupList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEphemeridesRequest' => [ 'type' => 'structure', 'required' => [ 'endTime', 'satelliteId', 'startTime', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'satelliteId' => [ 'shape' => 'Uuid', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'statusList' => [ 'shape' => 'EphemerisStatusList', ], ], ], 'ListEphemeridesResponse' => [ 'type' => 'structure', 'members' => [ 'ephemerides' => [ 'shape' => 'EphemeridesList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListGroundStationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'satelliteId' => [ 'shape' => 'Uuid', 'location' => 'querystring', 'locationName' => 'satelliteId', ], ], ], 'ListGroundStationsResponse' => [ 'type' => 'structure', 'members' => [ 'groundStationList' => [ 'shape' => 'GroundStationList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMissionProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMissionProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'missionProfileList' => [ 'shape' => 'MissionProfileList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSatellitesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PaginationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSatellitesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'satellites' => [ 'shape' => 'SatelliteList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AnyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MissionProfileArn' => [ 'type' => 'string', ], 'MissionProfileIdResponse' => [ 'type' => 'structure', 'members' => [ 'missionProfileId' => [ 'shape' => 'Uuid', ], ], ], 'MissionProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MissionProfileListItem', ], ], 'MissionProfileListItem' => [ 'type' => 'structure', 'members' => [ 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'missionProfileId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'SafeName', ], 'region' => [ 'shape' => 'AWSRegion', ], ], ], 'Month' => [ 'type' => 'integer', 'box' => true, 'max' => 12, 'min' => 1, ], 'OEMEphemeris' => [ 'type' => 'structure', 'members' => [ 'oemData' => [ 'shape' => 'UnboundedString', ], 's3Object' => [ 'shape' => 'S3Object', ], ], ], 'PaginationMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 3, 'pattern' => '^[A-Za-z0-9-/+_.=]+$', ], 'Polarization' => [ 'type' => 'string', 'enum' => [ 'LEFT_HAND', 'NONE', 'RIGHT_HAND', ], ], 'PositiveDurationInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 21600, 'min' => 1, ], 'RangedConnectionDetails' => [ 'type' => 'structure', 'required' => [ 'socketAddress', ], 'members' => [ 'mtu' => [ 'shape' => 'RangedConnectionDetailsMtuInteger', ], 'socketAddress' => [ 'shape' => 'RangedSocketAddress', ], ], ], 'RangedConnectionDetailsMtuInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1500, 'min' => 1400, ], 'RangedSocketAddress' => [ 'type' => 'structure', 'required' => [ 'name', 'portRange', ], 'members' => [ 'name' => [ 'shape' => 'IpV4Address', ], 'portRange' => [ 'shape' => 'IntegerRange', ], ], ], 'RegisterAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentDetails', 'discoveryData', ], 'members' => [ 'agentDetails' => [ 'shape' => 'AgentDetails', ], 'discoveryData' => [ 'shape' => 'DiscoveryData', ], ], ], 'RegisterAgentResponse' => [ 'type' => 'structure', 'members' => [ 'agentId' => [ 'shape' => 'Uuid', ], ], ], 'ReserveContactRequest' => [ 'type' => 'structure', 'required' => [ 'endTime', 'groundStation', 'missionProfileArn', 'satelliteArn', 'startTime', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'groundStation' => [ 'shape' => 'GroundStationName', ], 'missionProfileArn' => [ 'shape' => 'MissionProfileArn', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'parameterName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 434, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9.-]{3,63}$', ], 'S3KeyPrefix' => [ 'type' => 'string', 'max' => 900, 'min' => 1, 'pattern' => '^([a-zA-Z0-9_\\-=/]|\\{satellite_id\\}|\\{config\\-name}|\\{s3\\-config-id}|\\{year\\}|\\{month\\}|\\{day\\}){1,900}$', ], 'S3Object' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3ObjectKey', ], 'version' => [ 'shape' => 'S3VersionId', ], ], ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z0-9!*\'\\)\\(./_-]{1,1024}$', ], 'S3RecordingConfig' => [ 'type' => 'structure', 'required' => [ 'bucketArn', 'roleArn', ], 'members' => [ 'bucketArn' => [ 'shape' => 'BucketArn', ], 'prefix' => [ 'shape' => 'S3KeyPrefix', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'S3RecordingDetails' => [ 'type' => 'structure', 'members' => [ 'bucketArn' => [ 'shape' => 'BucketArn', ], 'keyTemplate' => [ 'shape' => 'String', ], ], ], 'S3VersionId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\s\\S]{1,1024}$', ], 'SafeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[ a-zA-Z0-9_:-]{1,256}$', ], 'SatelliteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SatelliteListItem', ], ], 'SatelliteListItem' => [ 'type' => 'structure', 'members' => [ 'currentEphemeris' => [ 'shape' => 'EphemerisMetaData', ], 'groundStations' => [ 'shape' => 'GroundStationIdList', ], 'noradSatelliteID' => [ 'shape' => 'noradSatelliteID', ], 'satelliteArn' => [ 'shape' => 'satelliteArn', ], 'satelliteId' => [ 'shape' => 'Uuid', ], ], ], 'SecurityDetails' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'securityGroupIds', 'subnetIds', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], ], ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SignatureMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Boolean', ], ], 'SocketAddress' => [ 'type' => 'structure', 'required' => [ 'name', 'port', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Integer', ], ], ], 'Source' => [ 'type' => 'structure', 'members' => [ 'configDetails' => [ 'shape' => 'ConfigDetails', ], 'configId' => [ 'shape' => 'String', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', ], 'dataflowSourceRegion' => [ 'shape' => 'String', ], ], ], 'SpectrumConfig' => [ 'type' => 'structure', 'required' => [ 'bandwidth', 'centerFrequency', ], 'members' => [ 'bandwidth' => [ 'shape' => 'FrequencyBandwidth', ], 'centerFrequency' => [ 'shape' => 'Frequency', ], 'polarization' => [ 'shape' => 'Polarization', ], ], ], 'StatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactStatus', ], 'max' => 500, 'min' => 0, ], 'String' => [ 'type' => 'string', ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TLEData' => [ 'type' => 'structure', 'required' => [ 'tleLine1', 'tleLine2', 'validTimeRange', ], 'members' => [ 'tleLine1' => [ 'shape' => 'TleLineOne', ], 'tleLine2' => [ 'shape' => 'TleLineTwo', ], 'validTimeRange' => [ 'shape' => 'TimeRange', ], ], ], 'TLEDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TLEData', ], 'max' => 500, 'min' => 1, ], 'TLEEphemeris' => [ 'type' => 'structure', 'members' => [ 's3Object' => [ 'shape' => 'S3Object', ], 'tleData' => [ 'shape' => 'TLEDataList', ], ], ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnboundedString', ], 'max' => 500, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AnyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TimeRange' => [ 'type' => 'structure', 'required' => [ 'endTime', 'startTime', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TleLineOne' => [ 'type' => 'string', 'max' => 69, 'min' => 69, 'pattern' => '^1 [ 0-9]{5}[A-Z] [ 0-9]{5}[ A-Z]{3} [ 0-9]{5}[.][ 0-9]{8} (?:(?:[ 0+-][.][ 0-9]{8})|(?: [ +-][.][ 0-9]{7})) [ +-][ 0-9]{5}[+-][ 0-9] [ +-][ 0-9]{5}[+-][ 0-9] [ 0-9] [ 0-9]{4}[ 0-9]$', ], 'TleLineTwo' => [ 'type' => 'string', 'max' => 69, 'min' => 69, 'pattern' => '^2 [ 0-9]{5} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{7} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{3}[.][ 0-9]{4} [ 0-9]{2}[.][ 0-9]{13}[ 0-9]$', ], 'TrackingConfig' => [ 'type' => 'structure', 'required' => [ 'autotrack', ], 'members' => [ 'autotrack' => [ 'shape' => 'Criticality', ], ], ], 'UnboundedString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[\\s\\S]+$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AnyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'aggregateStatus', 'componentStatuses', 'taskId', ], 'members' => [ 'agentId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'agentId', ], 'aggregateStatus' => [ 'shape' => 'AggregateStatus', ], 'componentStatuses' => [ 'shape' => 'ComponentStatusList', ], 'taskId' => [ 'shape' => 'Uuid', ], ], ], 'UpdateAgentStatusResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Uuid', ], ], ], 'UpdateConfigRequest' => [ 'type' => 'structure', 'required' => [ 'configData', 'configId', 'configType', 'name', ], 'members' => [ 'configData' => [ 'shape' => 'ConfigTypeData', ], 'configId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'configId', ], 'configType' => [ 'shape' => 'ConfigCapabilityType', 'location' => 'uri', 'locationName' => 'configType', ], 'name' => [ 'shape' => 'SafeName', ], ], ], 'UpdateEphemerisRequest' => [ 'type' => 'structure', 'required' => [ 'enabled', 'ephemerisId', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'ephemerisId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'ephemerisId', ], 'name' => [ 'shape' => 'SafeName', ], 'priority' => [ 'shape' => 'EphemerisPriority', ], ], ], 'UpdateMissionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'missionProfileId', ], 'members' => [ 'contactPostPassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'contactPrePassDurationSeconds' => [ 'shape' => 'DurationInSeconds', ], 'dataflowEdges' => [ 'shape' => 'DataflowEdgeList', ], 'minimumViableContactDurationSeconds' => [ 'shape' => 'PositiveDurationInSeconds', ], 'missionProfileId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'missionProfileId', ], 'name' => [ 'shape' => 'SafeName', ], 'streamsKmsKey' => [ 'shape' => 'KmsKey', ], 'streamsKmsRole' => [ 'shape' => 'RoleArn', ], 'trackingConfigArn' => [ 'shape' => 'ConfigArn', ], ], ], 'UplinkEchoConfig' => [ 'type' => 'structure', 'required' => [ 'antennaUplinkConfigArn', 'enabled', ], 'members' => [ 'antennaUplinkConfigArn' => [ 'shape' => 'ConfigArn', ], 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'UplinkSpectrumConfig' => [ 'type' => 'structure', 'required' => [ 'centerFrequency', ], 'members' => [ 'centerFrequency' => [ 'shape' => 'Frequency', ], 'polarization' => [ 'shape' => 'Polarization', ], ], ], 'Uuid' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'VersionString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(0|[1-9]\\d*)(\\.(0|[1-9]\\d*))*$', ], 'VersionStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionString', ], 'max' => 20, 'min' => 1, ], 'Year' => [ 'type' => 'integer', 'box' => true, 'max' => 3000, 'min' => 2018, ], 'noradSatelliteID' => [ 'type' => 'integer', 'max' => 99999, 'min' => 0, ], 'satelliteArn' => [ 'type' => 'string', ], ],];
