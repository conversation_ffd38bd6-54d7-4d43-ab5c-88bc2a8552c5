<?xml version="1.0"?>
<psalm
    errorLevel="7"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    findUnusedBaselineEntry="true"
    findUnusedCode="true"
    findUnusedPsalmSuppress="true"
    findUnusedVariablesAndParams="true"
>
    <projectFiles>
        <directory name="src"/>
        <directory name="tests"/>
        <ignoreFiles>
            <directory name="vendor"/>
            <directory name="tests/factories/"/>
            <directory name="tests/Models/"/>
        </ignoreFiles>
    </projectFiles>
    <plugins>
        <pluginClass class="Psalm\LaravelPlugin\Plugin"/>
    </plugins>

    <issueHandlers>
      <ForbiddenCode errorLevel="error" />
      <UnusedClosureParam errorLevel="suppress" />
      <PossiblyUnusedMethod errorLevel="suppress" />
      <UnusedClass errorLevel="suppress" />
      <PossiblyUnusedParam errorLevel="suppress" />
      <PossiblyUnusedProperty errorLevel="suppress" />
      <PossiblyUnusedReturnValue errorLevel="suppress" />
      <MissingTemplateParam errorLevel="suppress" />
      <UnsupportedPropertyReferenceUsage errorLevel="suppress" />
      <InaccessibleProperty errorLevel="suppress" />
      <MissingDependency errorLevel="suppress" />
      <UnusedMethod errorLevel="suppress" />
    </issueHandlers>

    <forbiddenFunctions>
        <function name="var_dump" />
        <function name="dd" />
        <function name="dump" />
        <function name="print_r" />
    </forbiddenFunctions>
</psalm>
