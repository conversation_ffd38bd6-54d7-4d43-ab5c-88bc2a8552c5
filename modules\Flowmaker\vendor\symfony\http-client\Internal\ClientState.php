<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpClient\Internal;

/**
 * Internal representation of the client state.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
class ClientState
{
    public array $handlesActivity = [];
    public array $openHandles = [];
    public ?float $lastTimeout = null;
}
