<?php
namespace Aws\Omics;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Omics** service.
 * @method \Aws\Result abortMultipartReadSetUpload(array $args = [])
 * @method \GuzzleHttp\Promise\Promise abortMultipartReadSetUploadAsync(array $args = [])
 * @method \Aws\Result acceptShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptShareAsync(array $args = [])
 * @method \Aws\Result batchDeleteReadSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteReadSetAsync(array $args = [])
 * @method \Aws\Result cancelAnnotationImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelAnnotationImportJobAsync(array $args = [])
 * @method \Aws\Result cancelRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelRunAsync(array $args = [])
 * @method \Aws\Result cancelVariantImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelVariantImportJobAsync(array $args = [])
 * @method \Aws\Result completeMultipartReadSetUpload(array $args = [])
 * @method \GuzzleHttp\Promise\Promise completeMultipartReadSetUploadAsync(array $args = [])
 * @method \Aws\Result createAnnotationStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAnnotationStoreAsync(array $args = [])
 * @method \Aws\Result createAnnotationStoreVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAnnotationStoreVersionAsync(array $args = [])
 * @method \Aws\Result createMultipartReadSetUpload(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMultipartReadSetUploadAsync(array $args = [])
 * @method \Aws\Result createReferenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createReferenceStoreAsync(array $args = [])
 * @method \Aws\Result createRunGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createRunGroupAsync(array $args = [])
 * @method \Aws\Result createSequenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSequenceStoreAsync(array $args = [])
 * @method \Aws\Result createShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createShareAsync(array $args = [])
 * @method \Aws\Result createVariantStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVariantStoreAsync(array $args = [])
 * @method \Aws\Result createWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkflowAsync(array $args = [])
 * @method \Aws\Result deleteAnnotationStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAnnotationStoreAsync(array $args = [])
 * @method \Aws\Result deleteAnnotationStoreVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAnnotationStoreVersionsAsync(array $args = [])
 * @method \Aws\Result deleteReference(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReferenceAsync(array $args = [])
 * @method \Aws\Result deleteReferenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReferenceStoreAsync(array $args = [])
 * @method \Aws\Result deleteRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRunAsync(array $args = [])
 * @method \Aws\Result deleteRunGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRunGroupAsync(array $args = [])
 * @method \Aws\Result deleteSequenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSequenceStoreAsync(array $args = [])
 * @method \Aws\Result deleteShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteShareAsync(array $args = [])
 * @method \Aws\Result deleteVariantStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVariantStoreAsync(array $args = [])
 * @method \Aws\Result deleteWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkflowAsync(array $args = [])
 * @method \Aws\Result getAnnotationImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAnnotationImportJobAsync(array $args = [])
 * @method \Aws\Result getAnnotationStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAnnotationStoreAsync(array $args = [])
 * @method \Aws\Result getAnnotationStoreVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAnnotationStoreVersionAsync(array $args = [])
 * @method \Aws\Result getReadSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadSetAsync(array $args = [])
 * @method \Aws\Result getReadSetActivationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadSetActivationJobAsync(array $args = [])
 * @method \Aws\Result getReadSetExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadSetExportJobAsync(array $args = [])
 * @method \Aws\Result getReadSetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadSetImportJobAsync(array $args = [])
 * @method \Aws\Result getReadSetMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadSetMetadataAsync(array $args = [])
 * @method \Aws\Result getReference(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReferenceAsync(array $args = [])
 * @method \Aws\Result getReferenceImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReferenceImportJobAsync(array $args = [])
 * @method \Aws\Result getReferenceMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReferenceMetadataAsync(array $args = [])
 * @method \Aws\Result getReferenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReferenceStoreAsync(array $args = [])
 * @method \Aws\Result getRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRunAsync(array $args = [])
 * @method \Aws\Result getRunGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRunGroupAsync(array $args = [])
 * @method \Aws\Result getRunTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRunTaskAsync(array $args = [])
 * @method \Aws\Result getSequenceStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSequenceStoreAsync(array $args = [])
 * @method \Aws\Result getShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getShareAsync(array $args = [])
 * @method \Aws\Result getVariantImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getVariantImportJobAsync(array $args = [])
 * @method \Aws\Result getVariantStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getVariantStoreAsync(array $args = [])
 * @method \Aws\Result getWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkflowAsync(array $args = [])
 * @method \Aws\Result listAnnotationImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAnnotationImportJobsAsync(array $args = [])
 * @method \Aws\Result listAnnotationStoreVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAnnotationStoreVersionsAsync(array $args = [])
 * @method \Aws\Result listAnnotationStores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAnnotationStoresAsync(array $args = [])
 * @method \Aws\Result listMultipartReadSetUploads(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMultipartReadSetUploadsAsync(array $args = [])
 * @method \Aws\Result listReadSetActivationJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadSetActivationJobsAsync(array $args = [])
 * @method \Aws\Result listReadSetExportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadSetExportJobsAsync(array $args = [])
 * @method \Aws\Result listReadSetImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadSetImportJobsAsync(array $args = [])
 * @method \Aws\Result listReadSetUploadParts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadSetUploadPartsAsync(array $args = [])
 * @method \Aws\Result listReadSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadSetsAsync(array $args = [])
 * @method \Aws\Result listReferenceImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReferenceImportJobsAsync(array $args = [])
 * @method \Aws\Result listReferenceStores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReferenceStoresAsync(array $args = [])
 * @method \Aws\Result listReferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReferencesAsync(array $args = [])
 * @method \Aws\Result listRunGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRunGroupsAsync(array $args = [])
 * @method \Aws\Result listRunTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRunTasksAsync(array $args = [])
 * @method \Aws\Result listRuns(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRunsAsync(array $args = [])
 * @method \Aws\Result listSequenceStores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSequenceStoresAsync(array $args = [])
 * @method \Aws\Result listShares(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSharesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listVariantImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVariantImportJobsAsync(array $args = [])
 * @method \Aws\Result listVariantStores(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVariantStoresAsync(array $args = [])
 * @method \Aws\Result listWorkflows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkflowsAsync(array $args = [])
 * @method \Aws\Result startAnnotationImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startAnnotationImportJobAsync(array $args = [])
 * @method \Aws\Result startReadSetActivationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startReadSetActivationJobAsync(array $args = [])
 * @method \Aws\Result startReadSetExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startReadSetExportJobAsync(array $args = [])
 * @method \Aws\Result startReadSetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startReadSetImportJobAsync(array $args = [])
 * @method \Aws\Result startReferenceImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startReferenceImportJobAsync(array $args = [])
 * @method \Aws\Result startRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startRunAsync(array $args = [])
 * @method \Aws\Result startVariantImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startVariantImportJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAnnotationStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAnnotationStoreAsync(array $args = [])
 * @method \Aws\Result updateAnnotationStoreVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAnnotationStoreVersionAsync(array $args = [])
 * @method \Aws\Result updateRunGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateRunGroupAsync(array $args = [])
 * @method \Aws\Result updateVariantStore(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateVariantStoreAsync(array $args = [])
 * @method \Aws\Result updateWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkflowAsync(array $args = [])
 * @method \Aws\Result uploadReadSetPart(array $args = [])
 * @method \GuzzleHttp\Promise\Promise uploadReadSetPartAsync(array $args = [])
 */
class OmicsClient extends AwsClient {}
