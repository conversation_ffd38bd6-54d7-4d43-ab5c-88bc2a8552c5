<?php
// This file was auto-generated from sdk-root/src/data/freetier/2023-09-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-07', 'endpointPrefix' => 'freetier', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceFullName' => 'AWS Free Tier', 'serviceId' => 'FreeTier', 'signatureVersion' => 'v4', 'signingName' => 'freetier', 'targetPrefix' => 'AWSFreeTierService', 'uid' => 'freetier-2023-09-07', ], 'operations' => [ 'GetFreeTierUsage' => [ 'name' => 'GetFreeTierUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFreeTierUsageRequest', ], 'output' => [ 'shape' => 'GetFreeTierUsageResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'Dimension' => [ 'type' => 'string', 'enum' => [ 'SERVICE', 'OPERATION', 'USAGE_TYPE', 'REGION', 'FREE_TIER_TYPE', 'DESCRIPTION', 'USAGE_PERCENTAGE', ], ], 'DimensionValues' => [ 'type' => 'structure', 'required' => [ 'Key', 'MatchOptions', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'Dimension', ], 'MatchOptions' => [ 'shape' => 'MatchOptions', ], 'Values' => [ 'shape' => 'Values', ], ], ], 'Expression' => [ 'type' => 'structure', 'members' => [ 'And' => [ 'shape' => 'Expressions', ], 'Dimensions' => [ 'shape' => 'DimensionValues', ], 'Not' => [ 'shape' => 'Expression', ], 'Or' => [ 'shape' => 'Expressions', ], ], ], 'Expressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Expression', ], ], 'FreeTierUsage' => [ 'type' => 'structure', 'members' => [ 'actualUsageAmount' => [ 'shape' => 'GenericDouble', ], 'description' => [ 'shape' => 'GenericString', ], 'forecastedUsageAmount' => [ 'shape' => 'GenericDouble', ], 'freeTierType' => [ 'shape' => 'GenericString', ], 'limit' => [ 'shape' => 'GenericDouble', ], 'operation' => [ 'shape' => 'GenericString', ], 'region' => [ 'shape' => 'GenericString', ], 'service' => [ 'shape' => 'GenericString', ], 'unit' => [ 'shape' => 'GenericString', ], 'usageType' => [ 'shape' => 'GenericString', ], ], ], 'FreeTierUsages' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTierUsage', ], ], 'GenericDouble' => [ 'type' => 'double', ], 'GenericString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[\\S\\s]*$', ], 'GetFreeTierUsageRequest' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'Expression', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetFreeTierUsageResponse' => [ 'type' => 'structure', 'required' => [ 'freeTierUsages', ], 'members' => [ 'freeTierUsages' => [ 'shape' => 'FreeTierUsages', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'GenericString', ], ], 'exception' => true, 'fault' => true, ], 'MatchOption' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', 'GREATER_THAN_OR_EQUAL', ], ], 'MatchOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchOption', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'NextPageToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '^[\\S\\s]*$', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'GenericString', ], ], 'exception' => true, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'GenericString', ], ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[ a-zA-Z0-9\\-\\:\\.\\_\\/\\,\\$\\(\\)]*$', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], 'min' => 1, ], ],];
