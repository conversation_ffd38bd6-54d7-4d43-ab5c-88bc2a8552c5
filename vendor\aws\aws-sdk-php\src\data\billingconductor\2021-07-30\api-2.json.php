<?php
// This file was auto-generated from sdk-root/src/data/billingconductor/2021-07-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-07-30', 'endpointPrefix' => 'billingconductor', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWSBillingConductor', 'serviceId' => 'billingconductor', 'signatureVersion' => 'v4', 'signingName' => 'billingconductor', 'uid' => 'billingconductor-2021-07-30', ], 'operations' => [ 'AssociateAccounts' => [ 'name' => 'AssociateAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/associate-accounts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAccountsInput', ], 'output' => [ 'shape' => 'AssociateAccountsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'AssociatePricingRules' => [ 'name' => 'AssociatePricingRules', 'http' => [ 'method' => 'PUT', 'requestUri' => '/associate-pricing-rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociatePricingRulesInput', ], 'output' => [ 'shape' => 'AssociatePricingRulesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'BatchAssociateResourcesToCustomLineItem' => [ 'name' => 'BatchAssociateResourcesToCustomLineItem', 'http' => [ 'method' => 'PUT', 'requestUri' => '/batch-associate-resources-to-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchAssociateResourcesToCustomLineItemInput', ], 'output' => [ 'shape' => 'BatchAssociateResourcesToCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'BatchDisassociateResourcesFromCustomLineItem' => [ 'name' => 'BatchDisassociateResourcesFromCustomLineItem', 'http' => [ 'method' => 'PUT', 'requestUri' => '/batch-disassociate-resources-from-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDisassociateResourcesFromCustomLineItemInput', ], 'output' => [ 'shape' => 'BatchDisassociateResourcesFromCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateBillingGroup' => [ 'name' => 'CreateBillingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-billing-group', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBillingGroupInput', ], 'output' => [ 'shape' => 'CreateBillingGroupOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateCustomLineItem' => [ 'name' => 'CreateCustomLineItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCustomLineItemInput', ], 'output' => [ 'shape' => 'CreateCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreatePricingPlan' => [ 'name' => 'CreatePricingPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-pricing-plan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePricingPlanInput', ], 'output' => [ 'shape' => 'CreatePricingPlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreatePricingRule' => [ 'name' => 'CreatePricingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-pricing-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePricingRuleInput', ], 'output' => [ 'shape' => 'CreatePricingRuleOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteBillingGroup' => [ 'name' => 'DeleteBillingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-billing-group', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBillingGroupInput', ], 'output' => [ 'shape' => 'DeleteBillingGroupOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteCustomLineItem' => [ 'name' => 'DeleteCustomLineItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomLineItemInput', ], 'output' => [ 'shape' => 'DeleteCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePricingPlan' => [ 'name' => 'DeletePricingPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-pricing-plan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePricingPlanInput', ], 'output' => [ 'shape' => 'DeletePricingPlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePricingRule' => [ 'name' => 'DeletePricingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-pricing-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePricingRuleInput', ], 'output' => [ 'shape' => 'DeletePricingRuleOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DisassociateAccounts' => [ 'name' => 'DisassociateAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/disassociate-accounts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateAccountsInput', ], 'output' => [ 'shape' => 'DisassociateAccountsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisassociatePricingRules' => [ 'name' => 'DisassociatePricingRules', 'http' => [ 'method' => 'PUT', 'requestUri' => '/disassociate-pricing-rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociatePricingRulesInput', ], 'output' => [ 'shape' => 'DisassociatePricingRulesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetBillingGroupCostReport' => [ 'name' => 'GetBillingGroupCostReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-billing-group-cost-report', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBillingGroupCostReportInput', ], 'output' => [ 'shape' => 'GetBillingGroupCostReportOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAccountAssociations' => [ 'name' => 'ListAccountAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-account-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccountAssociationsInput', ], 'output' => [ 'shape' => 'ListAccountAssociationsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBillingGroupCostReports' => [ 'name' => 'ListBillingGroupCostReports', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-billing-group-cost-reports', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBillingGroupCostReportsInput', ], 'output' => [ 'shape' => 'ListBillingGroupCostReportsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBillingGroups' => [ 'name' => 'ListBillingGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-billing-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBillingGroupsInput', ], 'output' => [ 'shape' => 'ListBillingGroupsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListCustomLineItemVersions' => [ 'name' => 'ListCustomLineItemVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-custom-line-item-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomLineItemVersionsInput', ], 'output' => [ 'shape' => 'ListCustomLineItemVersionsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCustomLineItems' => [ 'name' => 'ListCustomLineItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-custom-line-items', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomLineItemsInput', ], 'output' => [ 'shape' => 'ListCustomLineItemsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPricingPlans' => [ 'name' => 'ListPricingPlans', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-pricing-plans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPricingPlansInput', ], 'output' => [ 'shape' => 'ListPricingPlansOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPricingPlansAssociatedWithPricingRule' => [ 'name' => 'ListPricingPlansAssociatedWithPricingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-pricing-plans-associated-with-pricing-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPricingPlansAssociatedWithPricingRuleInput', ], 'output' => [ 'shape' => 'ListPricingPlansAssociatedWithPricingRuleOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPricingRules' => [ 'name' => 'ListPricingRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-pricing-rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPricingRulesInput', ], 'output' => [ 'shape' => 'ListPricingRulesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPricingRulesAssociatedToPricingPlan' => [ 'name' => 'ListPricingRulesAssociatedToPricingPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-pricing-rules-associated-to-pricing-plan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPricingRulesAssociatedToPricingPlanInput', ], 'output' => [ 'shape' => 'ListPricingRulesAssociatedToPricingPlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListResourcesAssociatedToCustomLineItem' => [ 'name' => 'ListResourcesAssociatedToCustomLineItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-resources-associated-to-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourcesAssociatedToCustomLineItemInput', ], 'output' => [ 'shape' => 'ListResourcesAssociatedToCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateBillingGroup' => [ 'name' => 'UpdateBillingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-billing-group', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBillingGroupInput', ], 'output' => [ 'shape' => 'UpdateBillingGroupOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateCustomLineItem' => [ 'name' => 'UpdateCustomLineItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-custom-line-item', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCustomLineItemInput', ], 'output' => [ 'shape' => 'UpdateCustomLineItemOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdatePricingPlan' => [ 'name' => 'UpdatePricingPlan', 'http' => [ 'method' => 'PUT', 'requestUri' => '/update-pricing-plan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePricingPlanInput', ], 'output' => [ 'shape' => 'UpdatePricingPlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdatePricingRule' => [ 'name' => 'UpdatePricingRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/update-pricing-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePricingRuleInput', ], 'output' => [ 'shape' => 'UpdatePricingRuleOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AWSCost' => [ 'type' => 'string', ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssociationsListElement', ], ], 'AccountAssociationsListElement' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'BillingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'AccountName' => [ 'shape' => 'AccountName', ], 'AccountEmail' => [ 'shape' => 'AccountEmail', ], ], ], 'AccountEmail' => [ 'type' => 'string', 'sensitive' => true, ], 'AccountGrouping' => [ 'type' => 'structure', 'required' => [ 'LinkedAccountIds', ], 'members' => [ 'LinkedAccountIds' => [ 'shape' => 'AccountIdList', ], 'AutoAssociate' => [ 'shape' => 'Boolean', ], ], ], 'AccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AccountIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 30, 'min' => 1, ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 30, 'min' => 1, ], 'AccountName' => [ 'type' => 'string', 'sensitive' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'arn:aws(-cn)?:billingconductor::[0-9]{12}:billinggroup/?[0-9]{12}$|^arn:aws(-cn)?:billingconductor::[0-9]{12}:pricingplan/[a-zA-Z0-9]{10}$|^arn:aws(-cn)?:billingconductor::[0-9]{12}:pricingrule/[a-zA-Z0-9]{10}$|^(arn:aws(-cn)?:billingconductor::[0-9]{12}:customlineitem/)?[a-zA-Z0-9]{10}', ], 'AssociateAccountsInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'AccountIds', ], 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'AssociateAccountsOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], ], ], 'AssociatePricingRulesInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'PricingRuleArns', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], 'PricingRuleArns' => [ 'shape' => 'PricingRuleArnsNonEmptyInput', ], ], ], 'AssociatePricingRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], ], ], 'AssociateResourceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'AssociateResourceErrorReason', ], ], ], 'AssociateResourceErrorReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_ARN', 'SERVICE_LIMIT_EXCEEDED', 'ILLEGAL_CUSTOMLINEITEM', 'INTERNAL_SERVER_EXCEPTION', 'INVALID_BILLING_PERIOD_RANGE', ], ], 'AssociateResourceResponseElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'Error' => [ 'shape' => 'AssociateResourceError', ], ], ], 'AssociateResourcesResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociateResourceResponseElement', ], ], 'Association' => [ 'type' => 'string', 'pattern' => '((arn:aws(-cn)?:billingconductor::[0-9]{12}:billinggroup/)?[0-9]{12}|MONITORED|UNMONITORED)', ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'AttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'BatchAssociateResourcesToCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'TargetArn', 'ResourceArns', ], 'members' => [ 'TargetArn' => [ 'shape' => 'CustomLineItemArn', ], 'ResourceArns' => [ 'shape' => 'CustomLineItemBatchAssociationsList', ], 'BillingPeriodRange' => [ 'shape' => 'CustomLineItemBillingPeriodRange', ], ], ], 'BatchAssociateResourcesToCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'SuccessfullyAssociatedResources' => [ 'shape' => 'AssociateResourcesResponseList', ], 'FailedAssociatedResources' => [ 'shape' => 'AssociateResourcesResponseList', ], ], ], 'BatchDisassociateResourcesFromCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'TargetArn', 'ResourceArns', ], 'members' => [ 'TargetArn' => [ 'shape' => 'CustomLineItemArn', ], 'ResourceArns' => [ 'shape' => 'CustomLineItemBatchDisassociationsList', ], 'BillingPeriodRange' => [ 'shape' => 'CustomLineItemBillingPeriodRange', ], ], ], 'BatchDisassociateResourcesFromCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'SuccessfullyDisassociatedResources' => [ 'shape' => 'DisassociateResourcesResponseList', ], 'FailedDisassociatedResources' => [ 'shape' => 'DisassociateResourcesResponseList', ], ], ], 'BillingEntity' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9 ]+', ], 'BillingGroupArn' => [ 'type' => 'string', 'pattern' => '(arn:aws(-cn)?:billingconductor::[0-9]{12}:billinggroup/)?[0-9]{12}', ], 'BillingGroupArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingGroupArn', ], 'max' => 100, 'min' => 1, ], 'BillingGroupCostReportElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'AWSCost' => [ 'shape' => 'AWSCost', ], 'ProformaCost' => [ 'shape' => 'ProformaCost', ], 'Margin' => [ 'shape' => 'Margin', ], 'MarginPercentage' => [ 'shape' => 'MarginPercentage', ], 'Currency' => [ 'shape' => 'Currency', ], ], ], 'BillingGroupCostReportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingGroupCostReportElement', ], ], 'BillingGroupCostReportResultElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'AWSCost' => [ 'shape' => 'AWSCost', ], 'ProformaCost' => [ 'shape' => 'ProformaCost', ], 'Margin' => [ 'shape' => 'Margin', ], 'MarginPercentage' => [ 'shape' => 'MarginPercentage', ], 'Currency' => [ 'shape' => 'Currency', ], 'Attributes' => [ 'shape' => 'AttributesList', ], ], ], 'BillingGroupCostReportResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingGroupCostReportResultElement', ], ], 'BillingGroupDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'BillingGroupFullArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-cn)?:billingconductor::[0-9]{12}:billinggroup/[0-9]{12}', ], 'BillingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingGroupListElement', ], ], 'BillingGroupListElement' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'BillingGroupName', ], 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'Description' => [ 'shape' => 'BillingGroupDescription', ], 'PrimaryAccountId' => [ 'shape' => 'AccountId', ], 'ComputationPreference' => [ 'shape' => 'ComputationPreference', ], 'Size' => [ 'shape' => 'NumberOfAccounts', ], 'CreationTime' => [ 'shape' => 'Instant', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'Status' => [ 'shape' => 'BillingGroupStatus', ], 'StatusReason' => [ 'shape' => 'BillingGroupStatusReason', ], 'AccountGrouping' => [ 'shape' => 'ListBillingGroupAccountGrouping', ], ], ], 'BillingGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\+=\\.\\-@]+', 'sensitive' => true, ], 'BillingGroupStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PRIMARY_ACCOUNT_MISSING', ], ], 'BillingGroupStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingGroupStatus', ], 'max' => 2, 'min' => 1, ], 'BillingGroupStatusReason' => [ 'type' => 'string', ], 'BillingPeriod' => [ 'type' => 'string', 'pattern' => '\\d{4}-(0?[1-9]|1[012])', ], 'BillingPeriodRange' => [ 'type' => 'structure', 'required' => [ 'InclusiveStartBillingPeriod', 'ExclusiveEndBillingPeriod', ], 'members' => [ 'InclusiveStartBillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'ExclusiveEndBillingPeriod' => [ 'shape' => 'BillingPeriod', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'ComputationPreference' => [ 'type' => 'structure', 'required' => [ 'PricingPlanArn', ], 'members' => [ 'PricingPlanArn' => [ 'shape' => 'PricingPlanFullArn', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ConflictExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_NAME_CONFLICT', 'PRICING_RULE_IN_PRICING_PLAN_CONFLICT', 'PRICING_PLAN_ATTACHED_TO_BILLING_GROUP_DELETE_CONFLICT', 'PRICING_RULE_ATTACHED_TO_PRICING_PLAN_DELETE_CONFLICT', 'WRITE_CONFLICT_RETRY', ], ], 'CreateBillingGroupInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'AccountGrouping', 'ComputationPreference', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amzn-Client-Token', ], 'Name' => [ 'shape' => 'BillingGroupName', ], 'AccountGrouping' => [ 'shape' => 'AccountGrouping', ], 'ComputationPreference' => [ 'shape' => 'ComputationPreference', ], 'PrimaryAccountId' => [ 'shape' => 'AccountId', ], 'Description' => [ 'shape' => 'BillingGroupDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBillingGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], ], ], 'CreateCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'BillingGroupArn', 'ChargeDetails', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amzn-Client-Token', ], 'Name' => [ 'shape' => 'CustomLineItemName', ], 'Description' => [ 'shape' => 'CustomLineItemDescription', ], 'BillingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'BillingPeriodRange' => [ 'shape' => 'CustomLineItemBillingPeriodRange', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ChargeDetails' => [ 'shape' => 'CustomLineItemChargeDetails', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'CreateCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], ], ], 'CreateFreeTierConfig' => [ 'type' => 'structure', 'required' => [ 'Activated', ], 'members' => [ 'Activated' => [ 'shape' => 'TieringActivated', ], ], ], 'CreatePricingPlanInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amzn-Client-Token', ], 'Name' => [ 'shape' => 'PricingPlanName', ], 'Description' => [ 'shape' => 'PricingPlanDescription', ], 'PricingRuleArns' => [ 'shape' => 'PricingRuleArnsInput', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePricingPlanOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], ], ], 'CreatePricingRuleInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'Scope', 'Type', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amzn-Client-Token', ], 'Name' => [ 'shape' => 'PricingRuleName', ], 'Description' => [ 'shape' => 'PricingRuleDescription', ], 'Scope' => [ 'shape' => 'PricingRuleScope', ], 'Type' => [ 'shape' => 'PricingRuleType', ], 'ModifierPercentage' => [ 'shape' => 'ModifierPercentage', ], 'Service' => [ 'shape' => 'Service', ], 'Tags' => [ 'shape' => 'TagMap', ], 'BillingEntity' => [ 'shape' => 'BillingEntity', ], 'Tiering' => [ 'shape' => 'CreateTieringInput', ], 'UsageType' => [ 'shape' => 'UsageType', ], 'Operation' => [ 'shape' => 'Operation', ], ], ], 'CreatePricingRuleOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingRuleArn', ], ], ], 'CreateTieringInput' => [ 'type' => 'structure', 'required' => [ 'FreeTier', ], 'members' => [ 'FreeTier' => [ 'shape' => 'CreateFreeTierConfig', ], ], ], 'Currency' => [ 'type' => 'string', ], 'CurrencyCode' => [ 'type' => 'string', 'enum' => [ 'USD', 'CNY', ], ], 'CustomLineItemArn' => [ 'type' => 'string', 'pattern' => '(arn:aws(-cn)?:billingconductor::[0-9]{12}:customlineitem/)?[a-zA-Z0-9]{10}', ], 'CustomLineItemArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemArn', ], 'max' => 100, 'min' => 1, ], 'CustomLineItemAssociationElement' => [ 'type' => 'string', 'pattern' => '(arn:aws(-cn)?:billingconductor::[0-9]{12}:(customlineitem|billinggroup)/)?[a-zA-Z0-9]{10,12}', ], 'CustomLineItemAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'max' => 5, 'min' => 0, ], 'CustomLineItemBatchAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'max' => 30, 'min' => 1, ], 'CustomLineItemBatchDisassociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'max' => 30, 'min' => 1, ], 'CustomLineItemBillingPeriodRange' => [ 'type' => 'structure', 'required' => [ 'InclusiveStartBillingPeriod', ], 'members' => [ 'InclusiveStartBillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'ExclusiveEndBillingPeriod' => [ 'shape' => 'BillingPeriod', ], ], ], 'CustomLineItemChargeDetails' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Flat' => [ 'shape' => 'CustomLineItemFlatChargeDetails', ], 'Percentage' => [ 'shape' => 'CustomLineItemPercentageChargeDetails', ], 'Type' => [ 'shape' => 'CustomLineItemType', ], 'LineItemFilters' => [ 'shape' => 'LineItemFiltersList', ], ], ], 'CustomLineItemChargeValue' => [ 'type' => 'double', 'box' => true, 'max' => 1000000, 'min' => 0, ], 'CustomLineItemDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'CustomLineItemFlatChargeDetails' => [ 'type' => 'structure', 'required' => [ 'ChargeValue', ], 'members' => [ 'ChargeValue' => [ 'shape' => 'CustomLineItemChargeValue', ], ], ], 'CustomLineItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemListElement', ], ], 'CustomLineItemListElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'Name' => [ 'shape' => 'CustomLineItemName', ], 'ChargeDetails' => [ 'shape' => 'ListCustomLineItemChargeDetails', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], 'Description' => [ 'shape' => 'CustomLineItemDescription', ], 'ProductCode' => [ 'shape' => 'CustomLineItemProductCode', ], 'BillingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'CreationTime' => [ 'shape' => 'Instant', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'AssociationSize' => [ 'shape' => 'NumberOfAssociations', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'CustomLineItemName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\+=\\.\\-@]+', 'sensitive' => true, ], 'CustomLineItemNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemName', ], 'max' => 100, 'min' => 1, ], 'CustomLineItemPercentageChargeDetails' => [ 'type' => 'structure', 'required' => [ 'PercentageValue', ], 'members' => [ 'PercentageValue' => [ 'shape' => 'CustomLineItemPercentageChargeValue', ], 'AssociatedValues' => [ 'shape' => 'CustomLineItemAssociationsList', ], ], ], 'CustomLineItemPercentageChargeValue' => [ 'type' => 'double', 'box' => true, 'max' => 10000, 'min' => 0, ], 'CustomLineItemProductCode' => [ 'type' => 'string', 'max' => 29, 'min' => 1, ], 'CustomLineItemRelationship' => [ 'type' => 'string', 'enum' => [ 'PARENT', 'CHILD', ], ], 'CustomLineItemType' => [ 'type' => 'string', 'enum' => [ 'CREDIT', 'FEE', ], ], 'CustomLineItemVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLineItemVersionListElement', ], ], 'CustomLineItemVersionListElement' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CustomLineItemName', ], 'ChargeDetails' => [ 'shape' => 'ListCustomLineItemChargeDetails', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], 'Description' => [ 'shape' => 'CustomLineItemDescription', ], 'ProductCode' => [ 'shape' => 'CustomLineItemProductCode', ], 'BillingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'CreationTime' => [ 'shape' => 'Instant', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'AssociationSize' => [ 'shape' => 'NumberOfAssociations', ], 'StartBillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'EndBillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'StartTime' => [ 'shape' => 'Instant', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'DeleteBillingGroupInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], ], ], 'DeleteBillingGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], ], ], 'DeleteCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'BillingPeriodRange' => [ 'shape' => 'CustomLineItemBillingPeriodRange', ], ], ], 'DeleteCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], ], ], 'DeletePricingPlanInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], ], ], 'DeletePricingPlanOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], ], ], 'DeletePricingRuleInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingRuleArn', ], ], ], 'DeletePricingRuleOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingRuleArn', ], ], ], 'DisassociateAccountsInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'AccountIds', ], 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DisassociateAccountsOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], ], ], 'DisassociatePricingRulesInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'PricingRuleArns', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], 'PricingRuleArns' => [ 'shape' => 'PricingRuleArnsNonEmptyInput', ], ], ], 'DisassociatePricingRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], ], ], 'DisassociateResourceResponseElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'Error' => [ 'shape' => 'AssociateResourceError', ], ], ], 'DisassociateResourcesResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DisassociateResourceResponseElement', ], ], 'FreeTierConfig' => [ 'type' => 'structure', 'required' => [ 'Activated', ], 'members' => [ 'Activated' => [ 'shape' => 'TieringActivated', ], ], ], 'GetBillingGroupCostReportInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'BillingPeriodRange' => [ 'shape' => 'BillingPeriodRange', ], 'GroupBy' => [ 'shape' => 'GroupByAttributesList', ], 'MaxResults' => [ 'shape' => 'MaxBillingGroupCostReportResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetBillingGroupCostReportOutput' => [ 'type' => 'structure', 'members' => [ 'BillingGroupCostReportResults' => [ 'shape' => 'BillingGroupCostReportResultsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GroupByAttributeName' => [ 'type' => 'string', 'enum' => [ 'PRODUCT_NAME', 'BILLING_PERIOD', ], ], 'GroupByAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupByAttributeName', ], ], 'Instant' => [ 'type' => 'long', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LineItemFilter' => [ 'type' => 'structure', 'required' => [ 'Attribute', 'MatchOption', 'Values', ], 'members' => [ 'Attribute' => [ 'shape' => 'LineItemFilterAttributeName', ], 'MatchOption' => [ 'shape' => 'MatchOption', ], 'Values' => [ 'shape' => 'LineItemFilterValuesList', ], ], ], 'LineItemFilterAttributeName' => [ 'type' => 'string', 'enum' => [ 'LINE_ITEM_TYPE', ], ], 'LineItemFilterValue' => [ 'type' => 'string', 'enum' => [ 'SAVINGS_PLAN_NEGATION', ], ], 'LineItemFilterValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineItemFilterValue', ], 'max' => 1, 'min' => 1, ], 'LineItemFiltersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineItemFilter', ], 'max' => 1, 'min' => 0, ], 'ListAccountAssociationsFilter' => [ 'type' => 'structure', 'members' => [ 'Association' => [ 'shape' => 'Association', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AccountIds' => [ 'shape' => 'AccountIdFilterList', ], ], ], 'ListAccountAssociationsInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'Filters' => [ 'shape' => 'ListAccountAssociationsFilter', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssociationsOutput' => [ 'type' => 'structure', 'members' => [ 'LinkedAccounts' => [ 'shape' => 'AccountAssociationsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListBillingGroupAccountGrouping' => [ 'type' => 'structure', 'members' => [ 'AutoAssociate' => [ 'shape' => 'Boolean', ], ], ], 'ListBillingGroupCostReportsFilter' => [ 'type' => 'structure', 'members' => [ 'BillingGroupArns' => [ 'shape' => 'BillingGroupArnList', ], ], ], 'ListBillingGroupCostReportsInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'MaxResults' => [ 'shape' => 'MaxBillingGroupResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'ListBillingGroupCostReportsFilter', ], ], ], 'ListBillingGroupCostReportsOutput' => [ 'type' => 'structure', 'members' => [ 'BillingGroupCostReports' => [ 'shape' => 'BillingGroupCostReportList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListBillingGroupsFilter' => [ 'type' => 'structure', 'members' => [ 'Arns' => [ 'shape' => 'BillingGroupArnList', ], 'PricingPlan' => [ 'shape' => 'PricingPlanFullArn', ], 'Statuses' => [ 'shape' => 'BillingGroupStatusList', ], 'AutoAssociate' => [ 'shape' => 'Boolean', ], ], ], 'ListBillingGroupsInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'MaxResults' => [ 'shape' => 'MaxBillingGroupResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'ListBillingGroupsFilter', ], ], ], 'ListBillingGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'BillingGroups' => [ 'shape' => 'BillingGroupList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCustomLineItemChargeDetails' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Flat' => [ 'shape' => 'ListCustomLineItemFlatChargeDetails', ], 'Percentage' => [ 'shape' => 'ListCustomLineItemPercentageChargeDetails', ], 'Type' => [ 'shape' => 'CustomLineItemType', ], 'LineItemFilters' => [ 'shape' => 'LineItemFiltersList', ], ], ], 'ListCustomLineItemFlatChargeDetails' => [ 'type' => 'structure', 'required' => [ 'ChargeValue', ], 'members' => [ 'ChargeValue' => [ 'shape' => 'CustomLineItemChargeValue', ], ], ], 'ListCustomLineItemPercentageChargeDetails' => [ 'type' => 'structure', 'required' => [ 'PercentageValue', ], 'members' => [ 'PercentageValue' => [ 'shape' => 'CustomLineItemPercentageChargeValue', ], ], ], 'ListCustomLineItemVersionsBillingPeriodRangeFilter' => [ 'type' => 'structure', 'members' => [ 'StartBillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'EndBillingPeriod' => [ 'shape' => 'BillingPeriod', ], ], ], 'ListCustomLineItemVersionsFilter' => [ 'type' => 'structure', 'members' => [ 'BillingPeriodRange' => [ 'shape' => 'ListCustomLineItemVersionsBillingPeriodRangeFilter', ], ], ], 'ListCustomLineItemVersionsInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'MaxResults' => [ 'shape' => 'MaxCustomLineItemResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'ListCustomLineItemVersionsFilter', ], ], ], 'ListCustomLineItemVersionsOutput' => [ 'type' => 'structure', 'members' => [ 'CustomLineItemVersions' => [ 'shape' => 'CustomLineItemVersionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCustomLineItemsFilter' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'CustomLineItemNameList', ], 'BillingGroups' => [ 'shape' => 'BillingGroupArnList', ], 'Arns' => [ 'shape' => 'CustomLineItemArns', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'ListCustomLineItemsInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'MaxResults' => [ 'shape' => 'MaxCustomLineItemResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'ListCustomLineItemsFilter', ], ], ], 'ListCustomLineItemsOutput' => [ 'type' => 'structure', 'members' => [ 'CustomLineItems' => [ 'shape' => 'CustomLineItemList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingPlansAssociatedWithPricingRuleInput' => [ 'type' => 'structure', 'required' => [ 'PricingRuleArn', ], 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingRuleArn' => [ 'shape' => 'PricingRuleArn', ], 'MaxResults' => [ 'shape' => 'MaxPricingRuleResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingPlansAssociatedWithPricingRuleOutput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingRuleArn' => [ 'shape' => 'PricingRuleArn', ], 'PricingPlanArns' => [ 'shape' => 'PricingPlanArns', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingPlansFilter' => [ 'type' => 'structure', 'members' => [ 'Arns' => [ 'shape' => 'PricingPlanArns', ], ], ], 'ListPricingPlansInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'Filters' => [ 'shape' => 'ListPricingPlansFilter', ], 'MaxResults' => [ 'shape' => 'MaxPricingPlanResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingPlansOutput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingPlans' => [ 'shape' => 'PricingPlanList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingRulesAssociatedToPricingPlanInput' => [ 'type' => 'structure', 'required' => [ 'PricingPlanArn', ], 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingPlanArn' => [ 'shape' => 'PricingPlanArn', ], 'MaxResults' => [ 'shape' => 'MaxPricingPlanResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingRulesAssociatedToPricingPlanOutput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingPlanArn' => [ 'shape' => 'PricingPlanArn', ], 'PricingRuleArns' => [ 'shape' => 'PricingRuleArns', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingRulesFilter' => [ 'type' => 'structure', 'members' => [ 'Arns' => [ 'shape' => 'PricingRuleArns', ], ], ], 'ListPricingRulesInput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'Filters' => [ 'shape' => 'ListPricingRulesFilter', ], 'MaxResults' => [ 'shape' => 'MaxPricingRuleResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPricingRulesOutput' => [ 'type' => 'structure', 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'PricingRules' => [ 'shape' => 'PricingRuleList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesAssociatedToCustomLineItemFilter' => [ 'type' => 'structure', 'members' => [ 'Relationship' => [ 'shape' => 'CustomLineItemRelationship', ], ], ], 'ListResourcesAssociatedToCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'MaxResults' => [ 'shape' => 'MaxCustomLineItemResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'ListResourcesAssociatedToCustomLineItemFilter', ], ], ], 'ListResourcesAssociatedToCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'AssociatedResources' => [ 'shape' => 'ListResourcesAssociatedToCustomLineItemResponseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesAssociatedToCustomLineItemResponseElement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemAssociationElement', ], 'Relationship' => [ 'shape' => 'CustomLineItemRelationship', ], 'EndBillingPeriod' => [ 'shape' => 'BillingPeriod', ], ], ], 'ListResourcesAssociatedToCustomLineItemResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListResourcesAssociatedToCustomLineItemResponseElement', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'Margin' => [ 'type' => 'string', ], 'MarginPercentage' => [ 'type' => 'string', ], 'MatchOption' => [ 'type' => 'string', 'enum' => [ 'NOT_EQUAL', ], ], 'MaxBillingGroupCostReportResults' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 200, ], 'MaxBillingGroupResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxCustomLineItemResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxPricingPlanResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxPricingRuleResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ModifierPercentage' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'NumberOfAccounts' => [ 'type' => 'long', 'min' => 0, ], 'NumberOfAssociatedPricingRules' => [ 'type' => 'long', 'min' => 1, ], 'NumberOfAssociations' => [ 'type' => 'long', 'min' => 0, ], 'NumberOfPricingPlansAssociatedWith' => [ 'type' => 'long', 'min' => 0, ], 'Operation' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\S+', ], 'PricingPlanArn' => [ 'type' => 'string', 'pattern' => '(arn:aws(-cn)?:billingconductor::[0-9]{12}:pricingplan/)?[a-zA-Z0-9]{10}', ], 'PricingPlanArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingPlanArn', ], 'max' => 100, 'min' => 1, ], 'PricingPlanDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'PricingPlanFullArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-cn)?:billingconductor::[0-9]{12}:pricingplan/[a-zA-Z0-9]{10}', ], 'PricingPlanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingPlanListElement', ], ], 'PricingPlanListElement' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PricingPlanName', ], 'Arn' => [ 'shape' => 'PricingPlanArn', ], 'Description' => [ 'shape' => 'PricingPlanDescription', ], 'Size' => [ 'shape' => 'NumberOfAssociatedPricingRules', ], 'CreationTime' => [ 'shape' => 'Instant', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], ], ], 'PricingPlanName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\+=\\.\\-@]+', 'sensitive' => true, ], 'PricingRuleArn' => [ 'type' => 'string', 'pattern' => '(arn:aws(-cn)?:billingconductor::[0-9]{12}:pricingrule/)?[a-zA-Z0-9]{10}', ], 'PricingRuleArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingRuleArn', ], 'max' => 100, 'min' => 1, ], 'PricingRuleArnsInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingRuleArn', ], 'max' => 30, 'min' => 0, ], 'PricingRuleArnsNonEmptyInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingRuleArn', ], 'max' => 30, 'min' => 1, ], 'PricingRuleDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'PricingRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PricingRuleListElement', ], ], 'PricingRuleListElement' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PricingRuleName', ], 'Arn' => [ 'shape' => 'PricingRuleArn', ], 'Description' => [ 'shape' => 'PricingRuleDescription', ], 'Scope' => [ 'shape' => 'PricingRuleScope', ], 'Type' => [ 'shape' => 'PricingRuleType', ], 'ModifierPercentage' => [ 'shape' => 'ModifierPercentage', ], 'Service' => [ 'shape' => 'Service', ], 'AssociatedPricingPlanCount' => [ 'shape' => 'NumberOfPricingPlansAssociatedWith', ], 'CreationTime' => [ 'shape' => 'Instant', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'BillingEntity' => [ 'shape' => 'BillingEntity', ], 'Tiering' => [ 'shape' => 'Tiering', ], 'UsageType' => [ 'shape' => 'UsageType', ], 'Operation' => [ 'shape' => 'Operation', ], ], ], 'PricingRuleName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\+=\\.\\-@]+', 'sensitive' => true, ], 'PricingRuleScope' => [ 'type' => 'string', 'enum' => [ 'GLOBAL', 'SERVICE', 'BILLING_ENTITY', 'SKU', ], ], 'PricingRuleType' => [ 'type' => 'string', 'enum' => [ 'MARKUP', 'DISCOUNT', 'TIERING', ], ], 'ProformaCost' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'Service' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+', ], 'ServiceLimitExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'LimitCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'LimitCode' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Tiering' => [ 'type' => 'structure', 'required' => [ 'FreeTier', ], 'members' => [ 'FreeTier' => [ 'shape' => 'FreeTierConfig', ], ], ], 'TieringActivated' => [ 'type' => 'boolean', 'box' => true, ], 'Token' => [ 'type' => 'string', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBillingGroupAccountGrouping' => [ 'type' => 'structure', 'members' => [ 'AutoAssociate' => [ 'shape' => 'Boolean', ], ], ], 'UpdateBillingGroupInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'Name' => [ 'shape' => 'BillingGroupName', ], 'Status' => [ 'shape' => 'BillingGroupStatus', ], 'ComputationPreference' => [ 'shape' => 'ComputationPreference', ], 'Description' => [ 'shape' => 'BillingGroupDescription', ], 'AccountGrouping' => [ 'shape' => 'UpdateBillingGroupAccountGrouping', ], ], ], 'UpdateBillingGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'BillingGroupArn', ], 'Name' => [ 'shape' => 'BillingGroupName', ], 'Description' => [ 'shape' => 'BillingGroupDescription', ], 'PrimaryAccountId' => [ 'shape' => 'AccountId', ], 'PricingPlanArn' => [ 'shape' => 'PricingPlanArn', ], 'Size' => [ 'shape' => 'NumberOfAccounts', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'Status' => [ 'shape' => 'BillingGroupStatus', ], 'StatusReason' => [ 'shape' => 'BillingGroupStatusReason', ], 'AccountGrouping' => [ 'shape' => 'UpdateBillingGroupAccountGrouping', ], ], ], 'UpdateCustomLineItemChargeDetails' => [ 'type' => 'structure', 'members' => [ 'Flat' => [ 'shape' => 'UpdateCustomLineItemFlatChargeDetails', ], 'Percentage' => [ 'shape' => 'UpdateCustomLineItemPercentageChargeDetails', ], 'LineItemFilters' => [ 'shape' => 'LineItemFiltersList', ], ], ], 'UpdateCustomLineItemFlatChargeDetails' => [ 'type' => 'structure', 'required' => [ 'ChargeValue', ], 'members' => [ 'ChargeValue' => [ 'shape' => 'CustomLineItemChargeValue', ], ], ], 'UpdateCustomLineItemInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'Name' => [ 'shape' => 'CustomLineItemName', ], 'Description' => [ 'shape' => 'CustomLineItemDescription', ], 'ChargeDetails' => [ 'shape' => 'UpdateCustomLineItemChargeDetails', ], 'BillingPeriodRange' => [ 'shape' => 'CustomLineItemBillingPeriodRange', ], ], ], 'UpdateCustomLineItemOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'CustomLineItemArn', ], 'BillingGroupArn' => [ 'shape' => 'BillingGroupFullArn', ], 'Name' => [ 'shape' => 'CustomLineItemName', ], 'Description' => [ 'shape' => 'CustomLineItemDescription', ], 'ChargeDetails' => [ 'shape' => 'ListCustomLineItemChargeDetails', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'AssociationSize' => [ 'shape' => 'NumberOfAssociations', ], ], ], 'UpdateCustomLineItemPercentageChargeDetails' => [ 'type' => 'structure', 'required' => [ 'PercentageValue', ], 'members' => [ 'PercentageValue' => [ 'shape' => 'CustomLineItemPercentageChargeValue', ], ], ], 'UpdateFreeTierConfig' => [ 'type' => 'structure', 'required' => [ 'Activated', ], 'members' => [ 'Activated' => [ 'shape' => 'TieringActivated', ], ], ], 'UpdatePricingPlanInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], 'Name' => [ 'shape' => 'PricingPlanName', ], 'Description' => [ 'shape' => 'PricingPlanDescription', ], ], ], 'UpdatePricingPlanOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingPlanArn', ], 'Name' => [ 'shape' => 'PricingPlanName', ], 'Description' => [ 'shape' => 'PricingPlanDescription', ], 'Size' => [ 'shape' => 'NumberOfAssociatedPricingRules', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], ], ], 'UpdatePricingRuleInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'PricingRuleArn', ], 'Name' => [ 'shape' => 'PricingRuleName', ], 'Description' => [ 'shape' => 'PricingRuleDescription', ], 'Type' => [ 'shape' => 'PricingRuleType', ], 'ModifierPercentage' => [ 'shape' => 'ModifierPercentage', ], 'Tiering' => [ 'shape' => 'UpdateTieringInput', ], ], ], 'UpdatePricingRuleOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PricingRuleArn', ], 'Name' => [ 'shape' => 'PricingRuleName', ], 'Description' => [ 'shape' => 'PricingRuleDescription', ], 'Scope' => [ 'shape' => 'PricingRuleScope', ], 'Type' => [ 'shape' => 'PricingRuleType', ], 'ModifierPercentage' => [ 'shape' => 'ModifierPercentage', ], 'Service' => [ 'shape' => 'Service', ], 'AssociatedPricingPlanCount' => [ 'shape' => 'NumberOfPricingPlansAssociatedWith', ], 'LastModifiedTime' => [ 'shape' => 'Instant', ], 'BillingEntity' => [ 'shape' => 'BillingEntity', ], 'Tiering' => [ 'shape' => 'UpdateTieringInput', ], 'UsageType' => [ 'shape' => 'UsageType', ], 'Operation' => [ 'shape' => 'Operation', ], ], ], 'UpdateTieringInput' => [ 'type' => 'structure', 'required' => [ 'FreeTier', ], 'members' => [ 'FreeTier' => [ 'shape' => 'UpdateFreeTierConfig', ], ], ], 'UsageType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\S+', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', 'PRIMARY_NOT_ASSOCIATED', 'PRIMARY_CANNOT_DISASSOCIATE', 'ACCOUNTS_NOT_ASSOCIATED', 'ACCOUNTS_ALREADY_ASSOCIATED', 'ILLEGAL_PRIMARY_ACCOUNT', 'ILLEGAL_ACCOUNTS', 'MISMATCHED_BILLINGGROUP_ARN', 'MISSING_BILLINGGROUP', 'MISMATCHED_CUSTOMLINEITEM_ARN', 'ILLEGAL_BILLING_PERIOD', 'ILLEGAL_BILLING_PERIOD_RANGE', 'TOO_MANY_ACCOUNTS_IN_REQUEST', 'DUPLICATE_ACCOUNT', 'INVALID_BILLING_GROUP_STATUS', 'MISMATCHED_PRICINGPLAN_ARN', 'MISSING_PRICINGPLAN', 'MISMATCHED_PRICINGRULE_ARN', 'DUPLICATE_PRICINGRULE_ARNS', 'ILLEGAL_EXPRESSION', 'ILLEGAL_SCOPE', 'ILLEGAL_SERVICE', 'PRICINGRULES_NOT_EXIST', 'PRICINGRULES_ALREADY_ASSOCIATED', 'PRICINGRULES_NOT_ASSOCIATED', 'INVALID_TIME_RANGE', 'INVALID_BILLINGVIEW_ARN', 'MISMATCHED_BILLINGVIEW_ARN', 'ILLEGAL_CUSTOMLINEITEM', 'MISSING_CUSTOMLINEITEM', 'ILLEGAL_CUSTOMLINEITEM_UPDATE', 'TOO_MANY_CUSTOMLINEITEMS_IN_REQUEST', 'ILLEGAL_CHARGE_DETAILS', 'ILLEGAL_UPDATE_CHARGE_DETAILS', 'INVALID_ARN', 'ILLEGAL_RESOURCE_ARNS', 'ILLEGAL_CUSTOMLINEITEM_MODIFICATION', 'MISSING_LINKED_ACCOUNT_IDS', 'MULTIPLE_LINKED_ACCOUNT_IDS', 'MISSING_PRICING_PLAN_ARN', 'MULTIPLE_PRICING_PLAN_ARN', 'ILLEGAL_CHILD_ASSOCIATE_RESOURCE', 'CUSTOM_LINE_ITEM_ASSOCIATION_EXISTS', 'INVALID_BILLING_GROUP', 'INVALID_BILLING_PERIOD_FOR_OPERATION', 'ILLEGAL_BILLING_ENTITY', 'ILLEGAL_MODIFIER_PERCENTAGE', 'ILLEGAL_TYPE', 'ILLEGAL_ENDED_BILLINGGROUP', 'ILLEGAL_TIERING_INPUT', 'ILLEGAL_OPERATION', 'ILLEGAL_USAGE_TYPE', 'INVALID_SKU_COMBO', 'INVALID_FILTER', 'TOO_MANY_AUTO_ASSOCIATE_BILLING_GROUPS', 'CANNOT_DELETE_AUTO_ASSOCIATE_BILLING_GROUP', 'ILLEGAL_ACCOUNT_ID', ], ], ],];
