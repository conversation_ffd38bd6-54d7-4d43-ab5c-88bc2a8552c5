<?php
// This file was auto-generated from sdk-root/src/data/cleanroomsml/2023-09-06/paginators-1.json
return [ 'pagination' => [ 'ListAudienceExportJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceExportJobs', ], 'ListAudienceGenerationJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceGenerationJobs', ], 'ListAudienceModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceModels', ], 'ListConfiguredAudienceModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredAudienceModels', ], 'ListTrainingDatasets' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'trainingDatasets', ], ],];
