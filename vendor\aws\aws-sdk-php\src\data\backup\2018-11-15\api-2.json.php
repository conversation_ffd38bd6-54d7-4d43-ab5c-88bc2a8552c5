<?php
// This file was auto-generated from sdk-root/src/data/backup/2018-11-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-11-15', 'endpointPrefix' => 'backup', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Backup', 'serviceId' => 'Backup', 'signatureVersion' => 'v4', 'uid' => 'backup-2018-11-15', ], 'operations' => [ 'CancelLegalHold' => [ 'name' => 'CancelLegalHold', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/legal-holds/{legalHoldId}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CancelLegalHoldInput', ], 'output' => [ 'shape' => 'CancelLegalHoldOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateBackupPlan' => [ 'name' => 'CreateBackupPlan', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup/plans/', ], 'input' => [ 'shape' => 'CreateBackupPlanInput', ], 'output' => [ 'shape' => 'CreateBackupPlanOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'CreateBackupSelection' => [ 'name' => 'CreateBackupSelection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup/plans/{backupPlanId}/selections/', ], 'input' => [ 'shape' => 'CreateBackupSelectionInput', ], 'output' => [ 'shape' => 'CreateBackupSelectionOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'CreateBackupVault' => [ 'name' => 'CreateBackupVault', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup-vaults/{backupVaultName}', ], 'input' => [ 'shape' => 'CreateBackupVaultInput', ], 'output' => [ 'shape' => 'CreateBackupVaultOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], ], 'idempotent' => true, ], 'CreateFramework' => [ 'name' => 'CreateFramework', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/frameworks', ], 'input' => [ 'shape' => 'CreateFrameworkInput', ], 'output' => [ 'shape' => 'CreateFrameworkOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'CreateLegalHold' => [ 'name' => 'CreateLegalHold', 'http' => [ 'method' => 'POST', 'requestUri' => '/legal-holds/', ], 'input' => [ 'shape' => 'CreateLegalHoldInput', ], 'output' => [ 'shape' => 'CreateLegalHoldOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateLogicallyAirGappedBackupVault' => [ 'name' => 'CreateLogicallyAirGappedBackupVault', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logically-air-gapped-backup-vaults/{backupVaultName}', ], 'input' => [ 'shape' => 'CreateLogicallyAirGappedBackupVaultInput', ], 'output' => [ 'shape' => 'CreateLogicallyAirGappedBackupVaultOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], 'idempotent' => true, ], 'CreateReportPlan' => [ 'name' => 'CreateReportPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/report-plans', ], 'input' => [ 'shape' => 'CreateReportPlanInput', ], 'output' => [ 'shape' => 'CreateReportPlanOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], ], 'idempotent' => true, ], 'CreateRestoreTestingPlan' => [ 'name' => 'CreateRestoreTestingPlan', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-testing/plans', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRestoreTestingPlanInput', ], 'output' => [ 'shape' => 'CreateRestoreTestingPlanOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'CreateRestoreTestingSelection' => [ 'name' => 'CreateRestoreTestingSelection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}/selections', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRestoreTestingSelectionInput', ], 'output' => [ 'shape' => 'CreateRestoreTestingSelectionOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteBackupPlan' => [ 'name' => 'DeleteBackupPlan', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup/plans/{backupPlanId}', ], 'input' => [ 'shape' => 'DeleteBackupPlanInput', ], 'output' => [ 'shape' => 'DeleteBackupPlanOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteBackupSelection' => [ 'name' => 'DeleteBackupSelection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup/plans/{backupPlanId}/selections/{selectionId}', ], 'input' => [ 'shape' => 'DeleteBackupSelectionInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteBackupVault' => [ 'name' => 'DeleteBackupVault', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}', ], 'input' => [ 'shape' => 'DeleteBackupVaultInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteBackupVaultAccessPolicy' => [ 'name' => 'DeleteBackupVaultAccessPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}/access-policy', ], 'input' => [ 'shape' => 'DeleteBackupVaultAccessPolicyInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteBackupVaultLockConfiguration' => [ 'name' => 'DeleteBackupVaultLockConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}/vault-lock', ], 'input' => [ 'shape' => 'DeleteBackupVaultLockConfigurationInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteBackupVaultNotifications' => [ 'name' => 'DeleteBackupVaultNotifications', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}/notification-configuration', ], 'input' => [ 'shape' => 'DeleteBackupVaultNotificationsInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteFramework' => [ 'name' => 'DeleteFramework', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audit/frameworks/{frameworkName}', ], 'input' => [ 'shape' => 'DeleteFrameworkInput', ], 'errors' => [ [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteRecoveryPoint' => [ 'name' => 'DeleteRecoveryPoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}', ], 'input' => [ 'shape' => 'DeleteRecoveryPointInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], 'idempotent' => true, ], 'DeleteReportPlan' => [ 'name' => 'DeleteReportPlan', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audit/report-plans/{reportPlanName}', ], 'input' => [ 'shape' => 'DeleteReportPlanInput', ], 'errors' => [ [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteRestoreTestingPlan' => [ 'name' => 'DeleteRestoreTestingPlan', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRestoreTestingPlanInput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteRestoreTestingSelection' => [ 'name' => 'DeleteRestoreTestingSelection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRestoreTestingSelectionInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DescribeBackupJob' => [ 'name' => 'DescribeBackupJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-jobs/{backupJobId}', ], 'input' => [ 'shape' => 'DescribeBackupJobInput', ], 'output' => [ 'shape' => 'DescribeBackupJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DependencyFailureException', ], ], 'idempotent' => true, ], 'DescribeBackupVault' => [ 'name' => 'DescribeBackupVault', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}', ], 'input' => [ 'shape' => 'DescribeBackupVaultInput', ], 'output' => [ 'shape' => 'DescribeBackupVaultOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DescribeCopyJob' => [ 'name' => 'DescribeCopyJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/copy-jobs/{copyJobId}', ], 'input' => [ 'shape' => 'DescribeCopyJobInput', ], 'output' => [ 'shape' => 'DescribeCopyJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DescribeFramework' => [ 'name' => 'DescribeFramework', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/frameworks/{frameworkName}', ], 'input' => [ 'shape' => 'DescribeFrameworkInput', ], 'output' => [ 'shape' => 'DescribeFrameworkOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeGlobalSettings' => [ 'name' => 'DescribeGlobalSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-settings', ], 'input' => [ 'shape' => 'DescribeGlobalSettingsInput', ], 'output' => [ 'shape' => 'DescribeGlobalSettingsOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeProtectedResource' => [ 'name' => 'DescribeProtectedResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/{resourceArn}', ], 'input' => [ 'shape' => 'DescribeProtectedResourceInput', ], 'output' => [ 'shape' => 'DescribeProtectedResourceOutput', ], 'errors' => [ [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeRecoveryPoint' => [ 'name' => 'DescribeRecoveryPoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}', ], 'input' => [ 'shape' => 'DescribeRecoveryPointInput', ], 'output' => [ 'shape' => 'DescribeRecoveryPointOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DescribeRegionSettings' => [ 'name' => 'DescribeRegionSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/account-settings', ], 'input' => [ 'shape' => 'DescribeRegionSettingsInput', ], 'output' => [ 'shape' => 'DescribeRegionSettingsOutput', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeReportJob' => [ 'name' => 'DescribeReportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/report-jobs/{reportJobId}', ], 'input' => [ 'shape' => 'DescribeReportJobInput', ], 'output' => [ 'shape' => 'DescribeReportJobOutput', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeReportPlan' => [ 'name' => 'DescribeReportPlan', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/report-plans/{reportPlanName}', ], 'input' => [ 'shape' => 'DescribeReportPlanInput', ], 'output' => [ 'shape' => 'DescribeReportPlanOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeRestoreJob' => [ 'name' => 'DescribeRestoreJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-jobs/{restoreJobId}', ], 'input' => [ 'shape' => 'DescribeRestoreJobInput', ], 'output' => [ 'shape' => 'DescribeRestoreJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DependencyFailureException', ], ], 'idempotent' => true, ], 'DisassociateRecoveryPoint' => [ 'name' => 'DisassociateRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/disassociate', ], 'input' => [ 'shape' => 'DisassociateRecoveryPointInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DisassociateRecoveryPointFromParent' => [ 'name' => 'DisassociateRecoveryPointFromParent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/parentAssociation', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateRecoveryPointFromParentInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ExportBackupPlanTemplate' => [ 'name' => 'ExportBackupPlanTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/{backupPlanId}/toTemplate/', ], 'input' => [ 'shape' => 'ExportBackupPlanTemplateInput', ], 'output' => [ 'shape' => 'ExportBackupPlanTemplateOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetBackupPlan' => [ 'name' => 'GetBackupPlan', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/{backupPlanId}/', ], 'input' => [ 'shape' => 'GetBackupPlanInput', ], 'output' => [ 'shape' => 'GetBackupPlanOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'GetBackupPlanFromJSON' => [ 'name' => 'GetBackupPlanFromJSON', 'http' => [ 'method' => 'POST', 'requestUri' => '/backup/template/json/toPlan', ], 'input' => [ 'shape' => 'GetBackupPlanFromJSONInput', ], 'output' => [ 'shape' => 'GetBackupPlanFromJSONOutput', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetBackupPlanFromTemplate' => [ 'name' => 'GetBackupPlanFromTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/template/plans/{templateId}/toPlan', ], 'input' => [ 'shape' => 'GetBackupPlanFromTemplateInput', ], 'output' => [ 'shape' => 'GetBackupPlanFromTemplateOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetBackupSelection' => [ 'name' => 'GetBackupSelection', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/{backupPlanId}/selections/{selectionId}', ], 'input' => [ 'shape' => 'GetBackupSelectionInput', ], 'output' => [ 'shape' => 'GetBackupSelectionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'GetBackupVaultAccessPolicy' => [ 'name' => 'GetBackupVaultAccessPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/access-policy', ], 'input' => [ 'shape' => 'GetBackupVaultAccessPolicyInput', ], 'output' => [ 'shape' => 'GetBackupVaultAccessPolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'GetBackupVaultNotifications' => [ 'name' => 'GetBackupVaultNotifications', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/notification-configuration', ], 'input' => [ 'shape' => 'GetBackupVaultNotificationsInput', ], 'output' => [ 'shape' => 'GetBackupVaultNotificationsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'GetLegalHold' => [ 'name' => 'GetLegalHold', 'http' => [ 'method' => 'GET', 'requestUri' => '/legal-holds/{legalHoldId}/', ], 'input' => [ 'shape' => 'GetLegalHoldInput', ], 'output' => [ 'shape' => 'GetLegalHoldOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetRecoveryPointRestoreMetadata' => [ 'name' => 'GetRecoveryPointRestoreMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/restore-metadata', ], 'input' => [ 'shape' => 'GetRecoveryPointRestoreMetadataInput', ], 'output' => [ 'shape' => 'GetRecoveryPointRestoreMetadataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'GetRestoreJobMetadata' => [ 'name' => 'GetRestoreJobMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-jobs/{restoreJobId}/metadata', ], 'input' => [ 'shape' => 'GetRestoreJobMetadataInput', ], 'output' => [ 'shape' => 'GetRestoreJobMetadataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetRestoreTestingInferredMetadata' => [ 'name' => 'GetRestoreTestingInferredMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-testing/inferred-metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRestoreTestingInferredMetadataInput', ], 'output' => [ 'shape' => 'GetRestoreTestingInferredMetadataOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetRestoreTestingPlan' => [ 'name' => 'GetRestoreTestingPlan', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRestoreTestingPlanInput', ], 'output' => [ 'shape' => 'GetRestoreTestingPlanOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetRestoreTestingSelection' => [ 'name' => 'GetRestoreTestingSelection', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRestoreTestingSelectionInput', ], 'output' => [ 'shape' => 'GetRestoreTestingSelectionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetSupportedResourceTypes' => [ 'name' => 'GetSupportedResourceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/supported-resource-types', ], 'output' => [ 'shape' => 'GetSupportedResourceTypesOutput', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListBackupJobSummaries' => [ 'name' => 'ListBackupJobSummaries', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/backup-job-summaries', ], 'input' => [ 'shape' => 'ListBackupJobSummariesInput', ], 'output' => [ 'shape' => 'ListBackupJobSummariesOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListBackupJobs' => [ 'name' => 'ListBackupJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-jobs/', ], 'input' => [ 'shape' => 'ListBackupJobsInput', ], 'output' => [ 'shape' => 'ListBackupJobsOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListBackupPlanTemplates' => [ 'name' => 'ListBackupPlanTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/template/plans', ], 'input' => [ 'shape' => 'ListBackupPlanTemplatesInput', ], 'output' => [ 'shape' => 'ListBackupPlanTemplatesOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBackupPlanVersions' => [ 'name' => 'ListBackupPlanVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/{backupPlanId}/versions/', ], 'input' => [ 'shape' => 'ListBackupPlanVersionsInput', ], 'output' => [ 'shape' => 'ListBackupPlanVersionsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListBackupPlans' => [ 'name' => 'ListBackupPlans', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/', ], 'input' => [ 'shape' => 'ListBackupPlansInput', ], 'output' => [ 'shape' => 'ListBackupPlansOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListBackupSelections' => [ 'name' => 'ListBackupSelections', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup/plans/{backupPlanId}/selections/', ], 'input' => [ 'shape' => 'ListBackupSelectionsInput', ], 'output' => [ 'shape' => 'ListBackupSelectionsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListBackupVaults' => [ 'name' => 'ListBackupVaults', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/', ], 'input' => [ 'shape' => 'ListBackupVaultsInput', ], 'output' => [ 'shape' => 'ListBackupVaultsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListCopyJobSummaries' => [ 'name' => 'ListCopyJobSummaries', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/copy-job-summaries', ], 'input' => [ 'shape' => 'ListCopyJobSummariesInput', ], 'output' => [ 'shape' => 'ListCopyJobSummariesOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListCopyJobs' => [ 'name' => 'ListCopyJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/copy-jobs/', ], 'input' => [ 'shape' => 'ListCopyJobsInput', ], 'output' => [ 'shape' => 'ListCopyJobsOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListFrameworks' => [ 'name' => 'ListFrameworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/frameworks', ], 'input' => [ 'shape' => 'ListFrameworksInput', ], 'output' => [ 'shape' => 'ListFrameworksOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListLegalHolds' => [ 'name' => 'ListLegalHolds', 'http' => [ 'method' => 'GET', 'requestUri' => '/legal-holds/', ], 'input' => [ 'shape' => 'ListLegalHoldsInput', ], 'output' => [ 'shape' => 'ListLegalHoldsOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListProtectedResources' => [ 'name' => 'ListProtectedResources', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/', ], 'input' => [ 'shape' => 'ListProtectedResourcesInput', ], 'output' => [ 'shape' => 'ListProtectedResourcesOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListProtectedResourcesByBackupVault' => [ 'name' => 'ListProtectedResourcesByBackupVault', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/resources/', ], 'input' => [ 'shape' => 'ListProtectedResourcesByBackupVaultInput', ], 'output' => [ 'shape' => 'ListProtectedResourcesByBackupVaultOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListRecoveryPointsByBackupVault' => [ 'name' => 'ListRecoveryPointsByBackupVault', 'http' => [ 'method' => 'GET', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/', ], 'input' => [ 'shape' => 'ListRecoveryPointsByBackupVaultInput', ], 'output' => [ 'shape' => 'ListRecoveryPointsByBackupVaultOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListRecoveryPointsByLegalHold' => [ 'name' => 'ListRecoveryPointsByLegalHold', 'http' => [ 'method' => 'GET', 'requestUri' => '/legal-holds/{legalHoldId}/recovery-points', ], 'input' => [ 'shape' => 'ListRecoveryPointsByLegalHoldInput', ], 'output' => [ 'shape' => 'ListRecoveryPointsByLegalHoldOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListRecoveryPointsByResource' => [ 'name' => 'ListRecoveryPointsByResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/{resourceArn}/recovery-points/', ], 'input' => [ 'shape' => 'ListRecoveryPointsByResourceInput', ], 'output' => [ 'shape' => 'ListRecoveryPointsByResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListReportJobs' => [ 'name' => 'ListReportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/report-jobs', ], 'input' => [ 'shape' => 'ListReportJobsInput', ], 'output' => [ 'shape' => 'ListReportJobsOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListReportPlans' => [ 'name' => 'ListReportPlans', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/report-plans', ], 'input' => [ 'shape' => 'ListReportPlansInput', ], 'output' => [ 'shape' => 'ListReportPlansOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListRestoreJobSummaries' => [ 'name' => 'ListRestoreJobSummaries', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/restore-job-summaries', ], 'input' => [ 'shape' => 'ListRestoreJobSummariesInput', ], 'output' => [ 'shape' => 'ListRestoreJobSummariesOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListRestoreJobs' => [ 'name' => 'ListRestoreJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-jobs/', ], 'input' => [ 'shape' => 'ListRestoreJobsInput', ], 'output' => [ 'shape' => 'ListRestoreJobsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'ListRestoreJobsByProtectedResource' => [ 'name' => 'ListRestoreJobsByProtectedResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/{resourceArn}/restore-jobs/', ], 'input' => [ 'shape' => 'ListRestoreJobsByProtectedResourceInput', ], 'output' => [ 'shape' => 'ListRestoreJobsByProtectedResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListRestoreTestingPlans' => [ 'name' => 'ListRestoreTestingPlans', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-testing/plans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRestoreTestingPlansInput', ], 'output' => [ 'shape' => 'ListRestoreTestingPlansOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListRestoreTestingSelections' => [ 'name' => 'ListRestoreTestingSelections', 'http' => [ 'method' => 'GET', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}/selections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRestoreTestingSelectionsInput', ], 'output' => [ 'shape' => 'ListRestoreTestingSelectionsOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}/', ], 'input' => [ 'shape' => 'ListTagsInput', ], 'output' => [ 'shape' => 'ListTagsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'PutBackupVaultAccessPolicy' => [ 'name' => 'PutBackupVaultAccessPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup-vaults/{backupVaultName}/access-policy', ], 'input' => [ 'shape' => 'PutBackupVaultAccessPolicyInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'PutBackupVaultLockConfiguration' => [ 'name' => 'PutBackupVaultLockConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup-vaults/{backupVaultName}/vault-lock', ], 'input' => [ 'shape' => 'PutBackupVaultLockConfigurationInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'PutBackupVaultNotifications' => [ 'name' => 'PutBackupVaultNotifications', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup-vaults/{backupVaultName}/notification-configuration', ], 'input' => [ 'shape' => 'PutBackupVaultNotificationsInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'PutRestoreValidationResult' => [ 'name' => 'PutRestoreValidationResult', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-jobs/{restoreJobId}/validations', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutRestoreValidationResultInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'StartBackupJob' => [ 'name' => 'StartBackupJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/backup-jobs', ], 'input' => [ 'shape' => 'StartBackupJobInput', ], 'output' => [ 'shape' => 'StartBackupJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'StartCopyJob' => [ 'name' => 'StartCopyJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/copy-jobs', ], 'input' => [ 'shape' => 'StartCopyJobInput', ], 'output' => [ 'shape' => 'StartCopyJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], ], 'idempotent' => true, ], 'StartReportJob' => [ 'name' => 'StartReportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/report-jobs/{reportPlanName}', ], 'input' => [ 'shape' => 'StartReportJobInput', ], 'output' => [ 'shape' => 'StartReportJobOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'StartRestoreJob' => [ 'name' => 'StartRestoreJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-jobs', ], 'input' => [ 'shape' => 'StartRestoreJobInput', ], 'output' => [ 'shape' => 'StartRestoreJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], ], 'idempotent' => true, ], 'StopBackupJob' => [ 'name' => 'StopBackupJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/backup-jobs/{backupJobId}', ], 'input' => [ 'shape' => 'StopBackupJobInput', ], 'errors' => [ [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untag/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateBackupPlan' => [ 'name' => 'UpdateBackupPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/backup/plans/{backupPlanId}', ], 'input' => [ 'shape' => 'UpdateBackupPlanInput', ], 'output' => [ 'shape' => 'UpdateBackupPlanOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFramework' => [ 'name' => 'UpdateFramework', 'http' => [ 'method' => 'PUT', 'requestUri' => '/audit/frameworks/{frameworkName}', ], 'input' => [ 'shape' => 'UpdateFrameworkInput', ], 'output' => [ 'shape' => 'UpdateFrameworkOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateGlobalSettings' => [ 'name' => 'UpdateGlobalSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/global-settings', ], 'input' => [ 'shape' => 'UpdateGlobalSettingsInput', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateRecoveryPointLifecycle' => [ 'name' => 'UpdateRecoveryPointLifecycle', 'http' => [ 'method' => 'POST', 'requestUri' => '/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}', ], 'input' => [ 'shape' => 'UpdateRecoveryPointLifecycleInput', ], 'output' => [ 'shape' => 'UpdateRecoveryPointLifecycleOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateRegionSettings' => [ 'name' => 'UpdateRegionSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/account-settings', ], 'input' => [ 'shape' => 'UpdateRegionSettingsInput', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'UpdateReportPlan' => [ 'name' => 'UpdateReportPlan', 'http' => [ 'method' => 'PUT', 'requestUri' => '/audit/report-plans/{reportPlanName}', ], 'input' => [ 'shape' => 'UpdateReportPlanInput', ], 'output' => [ 'shape' => 'UpdateReportPlanOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateRestoreTestingPlan' => [ 'name' => 'UpdateRestoreTestingPlan', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRestoreTestingPlanInput', ], 'output' => [ 'shape' => 'UpdateRestoreTestingPlanOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateRestoreTestingSelection' => [ 'name' => 'UpdateRestoreTestingSelection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRestoreTestingSelectionInput', ], 'output' => [ 'shape' => 'UpdateRestoreTestingSelectionOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'AdvancedBackupSetting' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'BackupOptions' => [ 'shape' => 'BackupOptions', ], ], ], 'AdvancedBackupSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdvancedBackupSetting', ], ], 'AggregationPeriod' => [ 'type' => 'string', 'enum' => [ 'ONE_DAY', 'SEVEN_DAYS', 'FOURTEEN_DAYS', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'BackupJob' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'BackupJobId' => [ 'shape' => 'string', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'State' => [ 'shape' => 'BackupJobState', ], 'StatusMessage' => [ 'shape' => 'string', ], 'PercentDone' => [ 'shape' => 'string', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'CreatedBy' => [ 'shape' => 'RecoveryPointCreator', ], 'ExpectedCompletionDate' => [ 'shape' => 'timestamp', ], 'StartBy' => [ 'shape' => 'timestamp', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'BytesTransferred' => [ 'shape' => 'Long', ], 'BackupOptions' => [ 'shape' => 'BackupOptions', ], 'BackupType' => [ 'shape' => 'string', ], 'ParentJobId' => [ 'shape' => 'string', ], 'IsParent' => [ 'shape' => 'boolean', ], 'ResourceName' => [ 'shape' => 'string', ], 'InitiationDate' => [ 'shape' => 'timestamp', ], 'MessageCategory' => [ 'shape' => 'string', ], ], ], 'BackupJobChildJobsInState' => [ 'type' => 'map', 'key' => [ 'shape' => 'BackupJobState', ], 'value' => [ 'shape' => 'Long', ], ], 'BackupJobState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PENDING', 'RUNNING', 'ABORTING', 'ABORTED', 'COMPLETED', 'FAILED', 'EXPIRED', 'PARTIAL', ], ], 'BackupJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PENDING', 'RUNNING', 'ABORTING', 'ABORTED', 'COMPLETED', 'FAILED', 'EXPIRED', 'PARTIAL', 'AGGREGATE_ALL', 'ANY', ], ], 'BackupJobSummary' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'Region', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'BackupJobStatus', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'MessageCategory' => [ 'shape' => 'MessageCategory', ], 'Count' => [ 'shape' => 'integer', ], 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], ], ], 'BackupJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupJobSummary', ], ], 'BackupJobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupJob', ], ], 'BackupOptionKey' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]{1,50}$', ], 'BackupOptionValue' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]{1,50}$', ], 'BackupOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'BackupOptionKey', ], 'value' => [ 'shape' => 'BackupOptionValue', ], ], 'BackupPlan' => [ 'type' => 'structure', 'required' => [ 'BackupPlanName', 'Rules', ], 'members' => [ 'BackupPlanName' => [ 'shape' => 'BackupPlanName', ], 'Rules' => [ 'shape' => 'BackupRules', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'BackupPlanInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanName', 'Rules', ], 'members' => [ 'BackupPlanName' => [ 'shape' => 'BackupPlanName', ], 'Rules' => [ 'shape' => 'BackupRulesInput', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'BackupPlanName' => [ 'type' => 'string', ], 'BackupPlanTemplatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupPlanTemplatesListMember', ], ], 'BackupPlanTemplatesListMember' => [ 'type' => 'structure', 'members' => [ 'BackupPlanTemplateId' => [ 'shape' => 'string', ], 'BackupPlanTemplateName' => [ 'shape' => 'string', ], ], ], 'BackupPlanVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupPlansListMember', ], ], 'BackupPlansList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupPlansListMember', ], ], 'BackupPlansListMember' => [ 'type' => 'structure', 'members' => [ 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'BackupPlanId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'DeletionDate' => [ 'shape' => 'timestamp', ], 'VersionId' => [ 'shape' => 'string', ], 'BackupPlanName' => [ 'shape' => 'BackupPlanName', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'LastExecutionDate' => [ 'shape' => 'timestamp', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'BackupRule' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'TargetBackupVaultName', ], 'members' => [ 'RuleName' => [ 'shape' => 'BackupRuleName', ], 'TargetBackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'ScheduleExpression' => [ 'shape' => 'CronExpression', ], 'StartWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'CompletionWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'RecoveryPointTags' => [ 'shape' => 'Tags', ], 'RuleId' => [ 'shape' => 'string', ], 'CopyActions' => [ 'shape' => 'CopyActions', ], 'EnableContinuousBackup' => [ 'shape' => 'Boolean', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'Timezone', ], ], ], 'BackupRuleInput' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'TargetBackupVaultName', ], 'members' => [ 'RuleName' => [ 'shape' => 'BackupRuleName', ], 'TargetBackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'ScheduleExpression' => [ 'shape' => 'CronExpression', ], 'StartWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'CompletionWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'RecoveryPointTags' => [ 'shape' => 'Tags', ], 'CopyActions' => [ 'shape' => 'CopyActions', ], 'EnableContinuousBackup' => [ 'shape' => 'Boolean', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'Timezone', ], ], ], 'BackupRuleName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]{1,50}$', ], 'BackupRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupRule', ], ], 'BackupRulesInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupRuleInput', ], ], 'BackupSelection' => [ 'type' => 'structure', 'required' => [ 'SelectionName', 'IamRoleArn', ], 'members' => [ 'SelectionName' => [ 'shape' => 'BackupSelectionName', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'Resources' => [ 'shape' => 'ResourceArns', ], 'ListOfTags' => [ 'shape' => 'ListOfTags', ], 'NotResources' => [ 'shape' => 'ResourceArns', ], 'Conditions' => [ 'shape' => 'Conditions', ], ], ], 'BackupSelectionName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]{1,50}$', ], 'BackupSelectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupSelectionsListMember', ], ], 'BackupSelectionsListMember' => [ 'type' => 'structure', 'members' => [ 'SelectionId' => [ 'shape' => 'string', ], 'SelectionName' => [ 'shape' => 'BackupSelectionName', ], 'BackupPlanId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'BackupVaultEvent' => [ 'type' => 'string', 'enum' => [ 'BACKUP_JOB_STARTED', 'BACKUP_JOB_COMPLETED', 'BACKUP_JOB_SUCCESSFUL', 'BACKUP_JOB_FAILED', 'BACKUP_JOB_EXPIRED', 'RESTORE_JOB_STARTED', 'RESTORE_JOB_COMPLETED', 'RESTORE_JOB_SUCCESSFUL', 'RESTORE_JOB_FAILED', 'COPY_JOB_STARTED', 'COPY_JOB_SUCCESSFUL', 'COPY_JOB_FAILED', 'RECOVERY_POINT_MODIFIED', 'BACKUP_PLAN_CREATED', 'BACKUP_PLAN_MODIFIED', 'S3_BACKUP_OBJECT_FAILED', 'S3_RESTORE_OBJECT_FAILED', ], ], 'BackupVaultEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupVaultEvent', ], ], 'BackupVaultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackupVaultListMember', ], ], 'BackupVaultListMember' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'NumberOfRecoveryPoints' => [ 'shape' => 'long', ], 'Locked' => [ 'shape' => 'Boolean', ], 'MinRetentionDays' => [ 'shape' => 'Long', ], 'MaxRetentionDays' => [ 'shape' => 'Long', ], 'LockDate' => [ 'shape' => 'timestamp', ], ], ], 'BackupVaultName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_]{2,50}$', ], 'Boolean' => [ 'type' => 'boolean', ], 'CalculatedLifecycle' => [ 'type' => 'structure', 'members' => [ 'MoveToColdStorageAt' => [ 'shape' => 'timestamp', ], 'DeleteAt' => [ 'shape' => 'timestamp', ], ], ], 'CancelLegalHoldInput' => [ 'type' => 'structure', 'required' => [ 'LegalHoldId', 'CancelDescription', ], 'members' => [ 'LegalHoldId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'legalHoldId', ], 'CancelDescription' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'cancelDescription', ], 'RetainRecordInDays' => [ 'shape' => 'Long', 'location' => 'querystring', 'locationName' => 'retainRecordInDays', ], ], ], 'CancelLegalHoldOutput' => [ 'type' => 'structure', 'members' => [], ], 'ComplianceResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'max' => 100, 'min' => 1, ], 'Condition' => [ 'type' => 'structure', 'required' => [ 'ConditionType', 'ConditionKey', 'ConditionValue', ], 'members' => [ 'ConditionType' => [ 'shape' => 'ConditionType', ], 'ConditionKey' => [ 'shape' => 'ConditionKey', ], 'ConditionValue' => [ 'shape' => 'ConditionValue', ], ], ], 'ConditionKey' => [ 'type' => 'string', ], 'ConditionParameter' => [ 'type' => 'structure', 'members' => [ 'ConditionKey' => [ 'shape' => 'ConditionKey', ], 'ConditionValue' => [ 'shape' => 'ConditionValue', ], ], ], 'ConditionParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionParameter', ], ], 'ConditionType' => [ 'type' => 'string', 'enum' => [ 'STRINGEQUALS', ], ], 'ConditionValue' => [ 'type' => 'string', ], 'Conditions' => [ 'type' => 'structure', 'members' => [ 'StringEquals' => [ 'shape' => 'ConditionParameters', ], 'StringNotEquals' => [ 'shape' => 'ConditionParameters', ], 'StringLike' => [ 'shape' => 'ConditionParameters', ], 'StringNotLike' => [ 'shape' => 'ConditionParameters', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ControlInputParameter' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'ParameterName', ], 'ParameterValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ControlInputParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlInputParameter', ], ], 'ControlName' => [ 'type' => 'string', ], 'ControlScope' => [ 'type' => 'structure', 'members' => [ 'ComplianceResourceIds' => [ 'shape' => 'ComplianceResourceIdList', ], 'ComplianceResourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'Tags' => [ 'shape' => 'stringMap', ], ], ], 'CopyAction' => [ 'type' => 'structure', 'required' => [ 'DestinationBackupVaultArn', ], 'members' => [ 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'DestinationBackupVaultArn' => [ 'shape' => 'ARN', ], ], ], 'CopyActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CopyAction', ], ], 'CopyJob' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CopyJobId' => [ 'shape' => 'string', ], 'SourceBackupVaultArn' => [ 'shape' => 'ARN', ], 'SourceRecoveryPointArn' => [ 'shape' => 'ARN', ], 'DestinationBackupVaultArn' => [ 'shape' => 'ARN', ], 'DestinationRecoveryPointArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'State' => [ 'shape' => 'CopyJobState', ], 'StatusMessage' => [ 'shape' => 'string', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'CreatedBy' => [ 'shape' => 'RecoveryPointCreator', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ParentJobId' => [ 'shape' => 'string', ], 'IsParent' => [ 'shape' => 'boolean', ], 'CompositeMemberIdentifier' => [ 'shape' => 'string', ], 'NumberOfChildJobs' => [ 'shape' => 'Long', ], 'ChildJobsInState' => [ 'shape' => 'CopyJobChildJobsInState', ], 'ResourceName' => [ 'shape' => 'string', ], 'MessageCategory' => [ 'shape' => 'string', ], ], ], 'CopyJobChildJobsInState' => [ 'type' => 'map', 'key' => [ 'shape' => 'CopyJobState', ], 'value' => [ 'shape' => 'Long', ], ], 'CopyJobState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'RUNNING', 'COMPLETED', 'FAILED', 'PARTIAL', ], ], 'CopyJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'RUNNING', 'ABORTING', 'ABORTED', 'COMPLETING', 'COMPLETED', 'FAILING', 'FAILED', 'PARTIAL', 'AGGREGATE_ALL', 'ANY', ], ], 'CopyJobSummary' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'Region', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'CopyJobStatus', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'MessageCategory' => [ 'shape' => 'MessageCategory', ], 'Count' => [ 'shape' => 'integer', ], 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], ], ], 'CopyJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CopyJobSummary', ], ], 'CopyJobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CopyJob', ], ], 'CreateBackupPlanInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlan', ], 'members' => [ 'BackupPlan' => [ 'shape' => 'BackupPlanInput', ], 'BackupPlanTags' => [ 'shape' => 'Tags', ], 'CreatorRequestId' => [ 'shape' => 'string', ], ], ], 'CreateBackupPlanOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', ], 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'VersionId' => [ 'shape' => 'string', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'CreateBackupSelectionInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', 'BackupSelection', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'BackupSelection' => [ 'shape' => 'BackupSelection', ], 'CreatorRequestId' => [ 'shape' => 'string', ], ], ], 'CreateBackupSelectionOutput' => [ 'type' => 'structure', 'members' => [ 'SelectionId' => [ 'shape' => 'string', ], 'BackupPlanId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], ], ], 'CreateBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'BackupVaultTags' => [ 'shape' => 'Tags', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'CreatorRequestId' => [ 'shape' => 'string', ], ], ], 'CreateBackupVaultOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], ], ], 'CreateFrameworkInput' => [ 'type' => 'structure', 'required' => [ 'FrameworkName', 'FrameworkControls', ], 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', ], 'FrameworkDescription' => [ 'shape' => 'FrameworkDescription', ], 'FrameworkControls' => [ 'shape' => 'FrameworkControls', ], 'IdempotencyToken' => [ 'shape' => 'string', 'idempotencyToken' => true, ], 'FrameworkTags' => [ 'shape' => 'stringMap', ], ], ], 'CreateFrameworkOutput' => [ 'type' => 'structure', 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', ], 'FrameworkArn' => [ 'shape' => 'ARN', ], ], ], 'CreateLegalHoldInput' => [ 'type' => 'structure', 'required' => [ 'Title', 'Description', ], 'members' => [ 'Title' => [ 'shape' => 'string', ], 'Description' => [ 'shape' => 'string', ], 'IdempotencyToken' => [ 'shape' => 'string', ], 'RecoveryPointSelection' => [ 'shape' => 'RecoveryPointSelection', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLegalHoldOutput' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'LegalHoldStatus', ], 'Description' => [ 'shape' => 'string', ], 'LegalHoldId' => [ 'shape' => 'string', ], 'LegalHoldArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'RecoveryPointSelection' => [ 'shape' => 'RecoveryPointSelection', ], ], ], 'CreateLogicallyAirGappedBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'MinRetentionDays', 'MaxRetentionDays', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'BackupVaultTags' => [ 'shape' => 'Tags', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'MinRetentionDays' => [ 'shape' => 'Long', ], 'MaxRetentionDays' => [ 'shape' => 'Long', ], ], ], 'CreateLogicallyAirGappedBackupVaultOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'VaultState' => [ 'shape' => 'VaultState', ], ], ], 'CreateReportPlanInput' => [ 'type' => 'structure', 'required' => [ 'ReportPlanName', 'ReportDeliveryChannel', 'ReportSetting', ], 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', ], 'ReportPlanDescription' => [ 'shape' => 'ReportPlanDescription', ], 'ReportDeliveryChannel' => [ 'shape' => 'ReportDeliveryChannel', ], 'ReportSetting' => [ 'shape' => 'ReportSetting', ], 'ReportPlanTags' => [ 'shape' => 'stringMap', ], 'IdempotencyToken' => [ 'shape' => 'string', 'idempotencyToken' => true, ], ], ], 'CreateReportPlanOutput' => [ 'type' => 'structure', 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', ], 'ReportPlanArn' => [ 'shape' => 'ARN', ], 'CreationTime' => [ 'shape' => 'timestamp', ], ], ], 'CreateRestoreTestingPlanInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlan', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'String', ], 'RestoreTestingPlan' => [ 'shape' => 'RestoreTestingPlanForCreate', ], 'Tags' => [ 'shape' => 'SensitiveStringMap', ], ], ], 'CreateRestoreTestingPlanOutput' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], ], ], 'CreateRestoreTestingSelectionInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', 'RestoreTestingSelection', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], 'RestoreTestingSelection' => [ 'shape' => 'RestoreTestingSelectionForCreate', ], ], ], 'CreateRestoreTestingSelectionOutput' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', 'RestoreTestingSelectionName', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', ], ], ], 'CronExpression' => [ 'type' => 'string', ], 'DateRange' => [ 'type' => 'structure', 'required' => [ 'FromDate', 'ToDate', ], 'members' => [ 'FromDate' => [ 'shape' => 'timestamp', ], 'ToDate' => [ 'shape' => 'timestamp', ], ], ], 'DeleteBackupPlanInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], ], ], 'DeleteBackupPlanOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', ], 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'DeletionDate' => [ 'shape' => 'timestamp', ], 'VersionId' => [ 'shape' => 'string', ], ], ], 'DeleteBackupSelectionInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', 'SelectionId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'SelectionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'selectionId', ], ], ], 'DeleteBackupVaultAccessPolicyInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'DeleteBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'DeleteBackupVaultLockConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'DeleteBackupVaultNotificationsInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'DeleteFrameworkInput' => [ 'type' => 'structure', 'required' => [ 'FrameworkName', ], 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', 'location' => 'uri', 'locationName' => 'frameworkName', ], ], ], 'DeleteRecoveryPointInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], ], ], 'DeleteReportPlanInput' => [ 'type' => 'structure', 'required' => [ 'ReportPlanName', ], 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', 'location' => 'uri', 'locationName' => 'reportPlanName', ], ], ], 'DeleteRestoreTestingPlanInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', ], 'members' => [ 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], ], ], 'DeleteRestoreTestingSelectionInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', 'RestoreTestingSelectionName', ], 'members' => [ 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingSelectionName', ], ], ], 'DependencyFailureException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'DescribeBackupJobInput' => [ 'type' => 'structure', 'required' => [ 'BackupJobId', ], 'members' => [ 'BackupJobId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupJobId', ], ], ], 'DescribeBackupJobOutput' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'BackupJobId' => [ 'shape' => 'string', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'State' => [ 'shape' => 'BackupJobState', ], 'StatusMessage' => [ 'shape' => 'string', ], 'PercentDone' => [ 'shape' => 'string', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'CreatedBy' => [ 'shape' => 'RecoveryPointCreator', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'BytesTransferred' => [ 'shape' => 'Long', ], 'ExpectedCompletionDate' => [ 'shape' => 'timestamp', ], 'StartBy' => [ 'shape' => 'timestamp', ], 'BackupOptions' => [ 'shape' => 'BackupOptions', ], 'BackupType' => [ 'shape' => 'string', ], 'ParentJobId' => [ 'shape' => 'string', ], 'IsParent' => [ 'shape' => 'boolean', ], 'NumberOfChildJobs' => [ 'shape' => 'Long', ], 'ChildJobsInState' => [ 'shape' => 'BackupJobChildJobsInState', ], 'ResourceName' => [ 'shape' => 'string', ], 'InitiationDate' => [ 'shape' => 'timestamp', ], 'MessageCategory' => [ 'shape' => 'string', ], ], ], 'DescribeBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'BackupVaultAccountId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'backupVaultAccountId', ], ], ], 'DescribeBackupVaultOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'string', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'VaultType' => [ 'shape' => 'VaultType', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'NumberOfRecoveryPoints' => [ 'shape' => 'long', ], 'Locked' => [ 'shape' => 'Boolean', ], 'MinRetentionDays' => [ 'shape' => 'Long', ], 'MaxRetentionDays' => [ 'shape' => 'Long', ], 'LockDate' => [ 'shape' => 'timestamp', ], ], ], 'DescribeCopyJobInput' => [ 'type' => 'structure', 'required' => [ 'CopyJobId', ], 'members' => [ 'CopyJobId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'copyJobId', ], ], ], 'DescribeCopyJobOutput' => [ 'type' => 'structure', 'members' => [ 'CopyJob' => [ 'shape' => 'CopyJob', ], ], ], 'DescribeFrameworkInput' => [ 'type' => 'structure', 'required' => [ 'FrameworkName', ], 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', 'location' => 'uri', 'locationName' => 'frameworkName', ], ], ], 'DescribeFrameworkOutput' => [ 'type' => 'structure', 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', ], 'FrameworkArn' => [ 'shape' => 'ARN', ], 'FrameworkDescription' => [ 'shape' => 'FrameworkDescription', ], 'FrameworkControls' => [ 'shape' => 'FrameworkControls', ], 'CreationTime' => [ 'shape' => 'timestamp', ], 'DeploymentStatus' => [ 'shape' => 'string', ], 'FrameworkStatus' => [ 'shape' => 'string', ], 'IdempotencyToken' => [ 'shape' => 'string', ], ], ], 'DescribeGlobalSettingsInput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeGlobalSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'GlobalSettings' => [ 'shape' => 'GlobalSettings', ], 'LastUpdateTime' => [ 'shape' => 'timestamp', ], ], ], 'DescribeProtectedResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'DescribeProtectedResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastBackupTime' => [ 'shape' => 'timestamp', ], 'ResourceName' => [ 'shape' => 'string', ], 'LastBackupVaultArn' => [ 'shape' => 'ARN', ], 'LastRecoveryPointArn' => [ 'shape' => 'ARN', ], 'LatestRestoreExecutionTimeMinutes' => [ 'shape' => 'Long', ], 'LatestRestoreJobCreationDate' => [ 'shape' => 'timestamp', ], 'LatestRestoreRecoveryPointCreationDate' => [ 'shape' => 'timestamp', ], ], ], 'DescribeRecoveryPointInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], 'BackupVaultAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'backupVaultAccountId', ], ], ], 'DescribeRecoveryPointOutput' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'SourceBackupVaultArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'CreatedBy' => [ 'shape' => 'RecoveryPointCreator', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'Status' => [ 'shape' => 'RecoveryPointStatus', ], 'StatusMessage' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'CalculatedLifecycle' => [ 'shape' => 'CalculatedLifecycle', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'IsEncrypted' => [ 'shape' => 'boolean', ], 'StorageClass' => [ 'shape' => 'StorageClass', ], 'LastRestoreTime' => [ 'shape' => 'timestamp', ], 'ParentRecoveryPointArn' => [ 'shape' => 'ARN', ], 'CompositeMemberIdentifier' => [ 'shape' => 'string', ], 'IsParent' => [ 'shape' => 'boolean', ], 'ResourceName' => [ 'shape' => 'string', ], 'VaultType' => [ 'shape' => 'VaultType', ], ], ], 'DescribeRegionSettingsInput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeRegionSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceTypeOptInPreference' => [ 'shape' => 'ResourceTypeOptInPreference', ], 'ResourceTypeManagementPreference' => [ 'shape' => 'ResourceTypeManagementPreference', ], ], ], 'DescribeReportJobInput' => [ 'type' => 'structure', 'required' => [ 'ReportJobId', ], 'members' => [ 'ReportJobId' => [ 'shape' => 'ReportJobId', 'location' => 'uri', 'locationName' => 'reportJobId', ], ], ], 'DescribeReportJobOutput' => [ 'type' => 'structure', 'members' => [ 'ReportJob' => [ 'shape' => 'ReportJob', ], ], ], 'DescribeReportPlanInput' => [ 'type' => 'structure', 'required' => [ 'ReportPlanName', ], 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', 'location' => 'uri', 'locationName' => 'reportPlanName', ], ], ], 'DescribeReportPlanOutput' => [ 'type' => 'structure', 'members' => [ 'ReportPlan' => [ 'shape' => 'ReportPlan', ], ], ], 'DescribeRestoreJobInput' => [ 'type' => 'structure', 'required' => [ 'RestoreJobId', ], 'members' => [ 'RestoreJobId' => [ 'shape' => 'RestoreJobId', 'location' => 'uri', 'locationName' => 'restoreJobId', ], ], ], 'DescribeRestoreJobOutput' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RestoreJobId' => [ 'shape' => 'string', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'RestoreJobStatus', ], 'StatusMessage' => [ 'shape' => 'string', ], 'PercentDone' => [ 'shape' => 'string', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'ExpectedCompletionTimeMinutes' => [ 'shape' => 'Long', ], 'CreatedResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'RecoveryPointCreationDate' => [ 'shape' => 'timestamp', ], 'CreatedBy' => [ 'shape' => 'RestoreJobCreator', ], 'ValidationStatus' => [ 'shape' => 'RestoreValidationStatus', ], 'ValidationStatusMessage' => [ 'shape' => 'string', ], 'DeletionStatus' => [ 'shape' => 'RestoreDeletionStatus', ], 'DeletionStatusMessage' => [ 'shape' => 'string', ], ], ], 'DisassociateRecoveryPointFromParentInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], ], ], 'DisassociateRecoveryPointInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], ], ], 'ExportBackupPlanTemplateInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], ], ], 'ExportBackupPlanTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlanTemplateJson' => [ 'shape' => 'string', ], ], ], 'FormatList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'Framework' => [ 'type' => 'structure', 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', ], 'FrameworkArn' => [ 'shape' => 'ARN', ], 'FrameworkDescription' => [ 'shape' => 'FrameworkDescription', ], 'NumberOfControls' => [ 'shape' => 'integer', ], 'CreationTime' => [ 'shape' => 'timestamp', ], 'DeploymentStatus' => [ 'shape' => 'string', ], ], ], 'FrameworkControl' => [ 'type' => 'structure', 'required' => [ 'ControlName', ], 'members' => [ 'ControlName' => [ 'shape' => 'ControlName', ], 'ControlInputParameters' => [ 'shape' => 'ControlInputParameters', ], 'ControlScope' => [ 'shape' => 'ControlScope', ], ], ], 'FrameworkControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'FrameworkControl', ], ], 'FrameworkDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], 'FrameworkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Framework', ], ], 'FrameworkName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z][_a-zA-Z0-9]*', ], 'GetBackupPlanFromJSONInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanTemplateJson', ], 'members' => [ 'BackupPlanTemplateJson' => [ 'shape' => 'string', ], ], ], 'GetBackupPlanFromJSONOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlan' => [ 'shape' => 'BackupPlan', ], ], ], 'GetBackupPlanFromTemplateInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanTemplateId', ], 'members' => [ 'BackupPlanTemplateId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'templateId', ], ], ], 'GetBackupPlanFromTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlanDocument' => [ 'shape' => 'BackupPlan', ], ], ], 'GetBackupPlanInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'VersionId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'versionId', ], ], ], 'GetBackupPlanOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlan' => [ 'shape' => 'BackupPlan', ], 'BackupPlanId' => [ 'shape' => 'string', ], 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'VersionId' => [ 'shape' => 'string', ], 'CreatorRequestId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'DeletionDate' => [ 'shape' => 'timestamp', ], 'LastExecutionDate' => [ 'shape' => 'timestamp', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'GetBackupSelectionInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', 'SelectionId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'SelectionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'selectionId', ], ], ], 'GetBackupSelectionOutput' => [ 'type' => 'structure', 'members' => [ 'BackupSelection' => [ 'shape' => 'BackupSelection', ], 'SelectionId' => [ 'shape' => 'string', ], 'BackupPlanId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CreatorRequestId' => [ 'shape' => 'string', ], ], ], 'GetBackupVaultAccessPolicyInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'GetBackupVaultAccessPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'Policy' => [ 'shape' => 'IAMPolicy', ], ], ], 'GetBackupVaultNotificationsInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], ], ], 'GetBackupVaultNotificationsOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'SNSTopicArn' => [ 'shape' => 'ARN', ], 'BackupVaultEvents' => [ 'shape' => 'BackupVaultEvents', ], ], ], 'GetLegalHoldInput' => [ 'type' => 'structure', 'required' => [ 'LegalHoldId', ], 'members' => [ 'LegalHoldId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'legalHoldId', ], ], ], 'GetLegalHoldOutput' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'LegalHoldStatus', ], 'Description' => [ 'shape' => 'string', ], 'CancelDescription' => [ 'shape' => 'string', ], 'LegalHoldId' => [ 'shape' => 'string', ], 'LegalHoldArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CancellationDate' => [ 'shape' => 'timestamp', ], 'RetainRecordUntil' => [ 'shape' => 'timestamp', ], 'RecoveryPointSelection' => [ 'shape' => 'RecoveryPointSelection', ], ], ], 'GetRecoveryPointRestoreMetadataInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], 'BackupVaultAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'backupVaultAccountId', ], ], ], 'GetRecoveryPointRestoreMetadataOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'RestoreMetadata' => [ 'shape' => 'Metadata', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'GetRestoreJobMetadataInput' => [ 'type' => 'structure', 'required' => [ 'RestoreJobId', ], 'members' => [ 'RestoreJobId' => [ 'shape' => 'RestoreJobId', 'location' => 'uri', 'locationName' => 'restoreJobId', ], ], ], 'GetRestoreJobMetadataOutput' => [ 'type' => 'structure', 'members' => [ 'RestoreJobId' => [ 'shape' => 'RestoreJobId', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'GetRestoreTestingInferredMetadataInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultAccountId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'BackupVaultAccountId', ], 'BackupVaultName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'BackupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'RecoveryPointArn', ], ], ], 'GetRestoreTestingInferredMetadataOutput' => [ 'type' => 'structure', 'required' => [ 'InferredMetadata', ], 'members' => [ 'InferredMetadata' => [ 'shape' => 'stringMap', ], ], ], 'GetRestoreTestingPlanInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', ], 'members' => [ 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], ], ], 'GetRestoreTestingPlanOutput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlan', ], 'members' => [ 'RestoreTestingPlan' => [ 'shape' => 'RestoreTestingPlanForGet', ], ], ], 'GetRestoreTestingSelectionInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', 'RestoreTestingSelectionName', ], 'members' => [ 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingSelectionName', ], ], ], 'GetRestoreTestingSelectionOutput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingSelection', ], 'members' => [ 'RestoreTestingSelection' => [ 'shape' => 'RestoreTestingSelectionForGet', ], ], ], 'GetSupportedResourceTypesOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], ], ], 'GlobalSettings' => [ 'type' => 'map', 'key' => [ 'shape' => 'GlobalSettingsName', ], 'value' => [ 'shape' => 'GlobalSettingsValue', ], ], 'GlobalSettingsName' => [ 'type' => 'string', ], 'GlobalSettingsValue' => [ 'type' => 'string', ], 'IAMPolicy' => [ 'type' => 'string', ], 'IAMRoleArn' => [ 'type' => 'string', ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'InvalidResourceStateException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'IsEnabled' => [ 'type' => 'boolean', ], 'KeyValue' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'KeyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValue', ], ], 'LegalHold' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'LegalHoldStatus', ], 'Description' => [ 'shape' => 'string', ], 'LegalHoldId' => [ 'shape' => 'string', ], 'LegalHoldArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CancellationDate' => [ 'shape' => 'timestamp', ], ], ], 'LegalHoldStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'CANCELING', 'CANCELED', ], ], 'LegalHoldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LegalHold', ], ], 'Lifecycle' => [ 'type' => 'structure', 'members' => [ 'MoveToColdStorageAfterDays' => [ 'shape' => 'Long', ], 'DeleteAfterDays' => [ 'shape' => 'Long', ], 'OptInToArchiveForSupportedResources' => [ 'shape' => 'Boolean', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ListBackupJobSummariesInput' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'AccountId', ], 'State' => [ 'shape' => 'BackupJobStatus', 'location' => 'querystring', 'locationName' => 'State', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'ResourceType', ], 'MessageCategory' => [ 'shape' => 'MessageCategory', 'location' => 'querystring', 'locationName' => 'MessageCategory', ], 'AggregationPeriod' => [ 'shape' => 'AggregationPeriod', 'location' => 'querystring', 'locationName' => 'AggregationPeriod', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListBackupJobSummariesOutput' => [ 'type' => 'structure', 'members' => [ 'BackupJobSummaries' => [ 'shape' => 'BackupJobSummaryList', ], 'AggregationPeriod' => [ 'shape' => 'string', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListBackupJobsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ByResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'ByState' => [ 'shape' => 'BackupJobState', 'location' => 'querystring', 'locationName' => 'state', ], 'ByBackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'querystring', 'locationName' => 'backupVaultName', ], 'ByCreatedBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdBefore', ], 'ByCreatedAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdAfter', ], 'ByResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ByAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ByCompleteAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeAfter', ], 'ByCompleteBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeBefore', ], 'ByParentJobId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'parentJobId', ], 'ByMessageCategory' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'messageCategory', ], ], ], 'ListBackupJobsOutput' => [ 'type' => 'structure', 'members' => [ 'BackupJobs' => [ 'shape' => 'BackupJobsList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListBackupPlanTemplatesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBackupPlanTemplatesOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'BackupPlanTemplatesList' => [ 'shape' => 'BackupPlanTemplatesList', ], ], ], 'ListBackupPlanVersionsInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBackupPlanVersionsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'BackupPlanVersionsList' => [ 'shape' => 'BackupPlanVersionsList', ], ], ], 'ListBackupPlansInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'IncludeDeleted' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeDeleted', ], ], ], 'ListBackupPlansOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'BackupPlansList' => [ 'shape' => 'BackupPlansList', ], ], ], 'ListBackupSelectionsInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBackupSelectionsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'BackupSelectionsList' => [ 'shape' => 'BackupSelectionsList', ], ], ], 'ListBackupVaultsInput' => [ 'type' => 'structure', 'members' => [ 'ByVaultType' => [ 'shape' => 'VaultType', 'location' => 'querystring', 'locationName' => 'vaultType', ], 'ByShared' => [ 'shape' => 'boolean', 'location' => 'querystring', 'locationName' => 'shared', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBackupVaultsOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultList' => [ 'shape' => 'BackupVaultList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListCopyJobSummariesInput' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'AccountId', ], 'State' => [ 'shape' => 'CopyJobStatus', 'location' => 'querystring', 'locationName' => 'State', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'ResourceType', ], 'MessageCategory' => [ 'shape' => 'MessageCategory', 'location' => 'querystring', 'locationName' => 'MessageCategory', ], 'AggregationPeriod' => [ 'shape' => 'AggregationPeriod', 'location' => 'querystring', 'locationName' => 'AggregationPeriod', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListCopyJobSummariesOutput' => [ 'type' => 'structure', 'members' => [ 'CopyJobSummaries' => [ 'shape' => 'CopyJobSummaryList', ], 'AggregationPeriod' => [ 'shape' => 'string', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListCopyJobsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ByResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'ByState' => [ 'shape' => 'CopyJobState', 'location' => 'querystring', 'locationName' => 'state', ], 'ByCreatedBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdBefore', ], 'ByCreatedAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdAfter', ], 'ByResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ByDestinationVaultArn' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'destinationVaultArn', ], 'ByAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ByCompleteBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeBefore', ], 'ByCompleteAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeAfter', ], 'ByParentJobId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'parentJobId', ], 'ByMessageCategory' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'messageCategory', ], ], ], 'ListCopyJobsOutput' => [ 'type' => 'structure', 'members' => [ 'CopyJobs' => [ 'shape' => 'CopyJobsList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListFrameworksInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxFrameworkInputs', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListFrameworksOutput' => [ 'type' => 'structure', 'members' => [ 'Frameworks' => [ 'shape' => 'FrameworkList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListLegalHoldsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLegalHoldsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'LegalHolds' => [ 'shape' => 'LegalHoldsList', ], ], ], 'ListOfTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], ], 'ListProtectedResourcesByBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'BackupVaultAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'backupVaultAccountId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProtectedResourcesByBackupVaultOutput' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'ProtectedResourcesList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListProtectedResourcesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProtectedResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'ProtectedResourcesList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRecoveryPointsByBackupVaultInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'BackupVaultAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'backupVaultAccountId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ByResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'ByResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ByBackupPlanId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'backupPlanId', ], 'ByCreatedBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdBefore', ], 'ByCreatedAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdAfter', ], 'ByParentRecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'parentRecoveryPointArn', ], ], ], 'ListRecoveryPointsByBackupVaultOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'RecoveryPoints' => [ 'shape' => 'RecoveryPointByBackupVaultList', ], ], ], 'ListRecoveryPointsByLegalHoldInput' => [ 'type' => 'structure', 'required' => [ 'LegalHoldId', ], 'members' => [ 'LegalHoldId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'legalHoldId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRecoveryPointsByLegalHoldOutput' => [ 'type' => 'structure', 'members' => [ 'RecoveryPoints' => [ 'shape' => 'RecoveryPointsList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRecoveryPointsByResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ManagedByAWSBackupOnly' => [ 'shape' => 'boolean', 'location' => 'querystring', 'locationName' => 'managedByAWSBackupOnly', ], ], ], 'ListRecoveryPointsByResourceOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'RecoveryPoints' => [ 'shape' => 'RecoveryPointByResourceList', ], ], ], 'ListReportJobsInput' => [ 'type' => 'structure', 'members' => [ 'ByReportPlanName' => [ 'shape' => 'ReportPlanName', 'location' => 'querystring', 'locationName' => 'ReportPlanName', ], 'ByCreationBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'CreationBefore', ], 'ByCreationAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'CreationAfter', ], 'ByStatus' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Status', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListReportJobsOutput' => [ 'type' => 'structure', 'members' => [ 'ReportJobs' => [ 'shape' => 'ReportJobList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListReportPlansInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListReportPlansOutput' => [ 'type' => 'structure', 'members' => [ 'ReportPlans' => [ 'shape' => 'ReportPlanList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRestoreJobSummariesInput' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'AccountId', ], 'State' => [ 'shape' => 'RestoreJobState', 'location' => 'querystring', 'locationName' => 'State', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'ResourceType', ], 'AggregationPeriod' => [ 'shape' => 'AggregationPeriod', 'location' => 'querystring', 'locationName' => 'AggregationPeriod', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListRestoreJobSummariesOutput' => [ 'type' => 'structure', 'members' => [ 'RestoreJobSummaries' => [ 'shape' => 'RestoreJobSummaryList', ], 'AggregationPeriod' => [ 'shape' => 'string', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRestoreJobsByProtectedResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'ByStatus' => [ 'shape' => 'RestoreJobStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'ByRecoveryPointCreationDateAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'recoveryPointCreationDateAfter', ], 'ByRecoveryPointCreationDateBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'recoveryPointCreationDateBefore', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRestoreJobsByProtectedResourceOutput' => [ 'type' => 'structure', 'members' => [ 'RestoreJobs' => [ 'shape' => 'RestoreJobsList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRestoreJobsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ByAccountId' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ByResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ByCreatedBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdBefore', ], 'ByCreatedAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'createdAfter', ], 'ByStatus' => [ 'shape' => 'RestoreJobStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'ByCompleteBefore' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeBefore', ], 'ByCompleteAfter' => [ 'shape' => 'timestamp', 'location' => 'querystring', 'locationName' => 'completeAfter', ], 'ByRestoreTestingPlanArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'restoreTestingPlanArn', ], ], ], 'ListRestoreJobsOutput' => [ 'type' => 'structure', 'members' => [ 'RestoreJobs' => [ 'shape' => 'RestoreJobsList', ], 'NextToken' => [ 'shape' => 'string', ], ], ], 'ListRestoreTestingPlansInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListRestoreTestingPlansInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListRestoreTestingPlansInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListRestoreTestingPlansOutput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlans', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'RestoreTestingPlans' => [ 'shape' => 'RestoreTestingPlans', ], ], ], 'ListRestoreTestingSelectionsInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'ListRestoreTestingSelectionsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], ], ], 'ListRestoreTestingSelectionsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListRestoreTestingSelectionsOutput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingSelections', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'RestoreTestingSelections' => [ 'shape' => 'RestoreTestingSelections', ], ], ], 'ListTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTagsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'string', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Long' => [ 'type' => 'long', ], 'MaxFrameworkInputs' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MessageCategory' => [ 'type' => 'string', ], 'Metadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetadataKey', ], 'value' => [ 'shape' => 'MetadataValue', ], 'sensitive' => true, ], 'MetadataKey' => [ 'type' => 'string', ], 'MetadataValue' => [ 'type' => 'string', ], 'MissingParameterValueException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ParameterName' => [ 'type' => 'string', ], 'ParameterValue' => [ 'type' => 'string', ], 'ProtectedResource' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastBackupTime' => [ 'shape' => 'timestamp', ], 'ResourceName' => [ 'shape' => 'string', ], 'LastBackupVaultArn' => [ 'shape' => 'ARN', ], 'LastRecoveryPointArn' => [ 'shape' => 'ARN', ], ], ], 'ProtectedResourceConditions' => [ 'type' => 'structure', 'members' => [ 'StringEquals' => [ 'shape' => 'KeyValueList', ], 'StringNotEquals' => [ 'shape' => 'KeyValueList', ], ], ], 'ProtectedResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectedResource', ], ], 'PutBackupVaultAccessPolicyInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'Policy' => [ 'shape' => 'IAMPolicy', ], ], ], 'PutBackupVaultLockConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'MinRetentionDays' => [ 'shape' => 'Long', ], 'MaxRetentionDays' => [ 'shape' => 'Long', ], 'ChangeableForDays' => [ 'shape' => 'Long', ], ], ], 'PutBackupVaultNotificationsInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'SNSTopicArn', 'BackupVaultEvents', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'SNSTopicArn' => [ 'shape' => 'ARN', ], 'BackupVaultEvents' => [ 'shape' => 'BackupVaultEvents', ], ], ], 'PutRestoreValidationResultInput' => [ 'type' => 'structure', 'required' => [ 'RestoreJobId', 'ValidationStatus', ], 'members' => [ 'RestoreJobId' => [ 'shape' => 'RestoreJobId', 'location' => 'uri', 'locationName' => 'restoreJobId', ], 'ValidationStatus' => [ 'shape' => 'RestoreValidationStatus', ], 'ValidationStatusMessage' => [ 'shape' => 'string', ], ], ], 'RecoveryPointByBackupVault' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'SourceBackupVaultArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'CreatedBy' => [ 'shape' => 'RecoveryPointCreator', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'Status' => [ 'shape' => 'RecoveryPointStatus', ], 'StatusMessage' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'CalculatedLifecycle' => [ 'shape' => 'CalculatedLifecycle', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'IsEncrypted' => [ 'shape' => 'boolean', ], 'LastRestoreTime' => [ 'shape' => 'timestamp', ], 'ParentRecoveryPointArn' => [ 'shape' => 'ARN', ], 'CompositeMemberIdentifier' => [ 'shape' => 'string', ], 'IsParent' => [ 'shape' => 'boolean', ], 'ResourceName' => [ 'shape' => 'string', ], 'VaultType' => [ 'shape' => 'VaultType', ], ], ], 'RecoveryPointByBackupVaultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryPointByBackupVault', ], ], 'RecoveryPointByResource' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'RecoveryPointStatus', ], 'StatusMessage' => [ 'shape' => 'string', ], 'EncryptionKeyArn' => [ 'shape' => 'ARN', ], 'BackupSizeBytes' => [ 'shape' => 'Long', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'IsParent' => [ 'shape' => 'boolean', ], 'ParentRecoveryPointArn' => [ 'shape' => 'ARN', ], 'ResourceName' => [ 'shape' => 'string', ], 'VaultType' => [ 'shape' => 'VaultType', ], ], ], 'RecoveryPointByResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryPointByResource', ], ], 'RecoveryPointCreator' => [ 'type' => 'structure', 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', ], 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'BackupPlanVersion' => [ 'shape' => 'string', ], 'BackupRuleId' => [ 'shape' => 'string', ], ], ], 'RecoveryPointMember' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], ], ], 'RecoveryPointSelection' => [ 'type' => 'structure', 'members' => [ 'VaultNames' => [ 'shape' => 'VaultNames', ], 'ResourceIdentifiers' => [ 'shape' => 'ResourceIdentifiers', ], 'DateRange' => [ 'shape' => 'DateRange', ], ], ], 'RecoveryPointStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'PARTIAL', 'DELETING', 'EXPIRED', ], ], 'RecoveryPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryPointMember', ], ], 'Region' => [ 'type' => 'string', ], 'ReportDeliveryChannel' => [ 'type' => 'structure', 'required' => [ 'S3BucketName', ], 'members' => [ 'S3BucketName' => [ 'shape' => 'string', ], 'S3KeyPrefix' => [ 'shape' => 'string', ], 'Formats' => [ 'shape' => 'FormatList', ], ], ], 'ReportDestination' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 'string', ], 'S3Keys' => [ 'shape' => 'stringList', ], ], ], 'ReportJob' => [ 'type' => 'structure', 'members' => [ 'ReportJobId' => [ 'shape' => 'ReportJobId', ], 'ReportPlanArn' => [ 'shape' => 'ARN', ], 'ReportTemplate' => [ 'shape' => 'string', ], 'CreationTime' => [ 'shape' => 'timestamp', ], 'CompletionTime' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'string', ], 'StatusMessage' => [ 'shape' => 'string', ], 'ReportDestination' => [ 'shape' => 'ReportDestination', ], ], ], 'ReportJobId' => [ 'type' => 'string', ], 'ReportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportJob', ], ], 'ReportPlan' => [ 'type' => 'structure', 'members' => [ 'ReportPlanArn' => [ 'shape' => 'ARN', ], 'ReportPlanName' => [ 'shape' => 'ReportPlanName', ], 'ReportPlanDescription' => [ 'shape' => 'ReportPlanDescription', ], 'ReportSetting' => [ 'shape' => 'ReportSetting', ], 'ReportDeliveryChannel' => [ 'shape' => 'ReportDeliveryChannel', ], 'DeploymentStatus' => [ 'shape' => 'string', ], 'CreationTime' => [ 'shape' => 'timestamp', ], 'LastAttemptedExecutionTime' => [ 'shape' => 'timestamp', ], 'LastSuccessfulExecutionTime' => [ 'shape' => 'timestamp', ], ], ], 'ReportPlanDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], 'ReportPlanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportPlan', ], ], 'ReportPlanName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z][_a-zA-Z0-9]*', ], 'ReportSetting' => [ 'type' => 'structure', 'required' => [ 'ReportTemplate', ], 'members' => [ 'ReportTemplate' => [ 'shape' => 'string', ], 'FrameworkArns' => [ 'shape' => 'stringList', ], 'NumberOfFrameworks' => [ 'shape' => 'integer', ], 'Accounts' => [ 'shape' => 'stringList', ], 'OrganizationUnits' => [ 'shape' => 'stringList', ], 'Regions' => [ 'shape' => 'stringList', ], ], ], 'ResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ARN', ], ], 'ResourceIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]{1,50}$', ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ARN', ], ], 'ResourceTypeManagementPreference' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceType', ], 'value' => [ 'shape' => 'IsEnabled', ], ], 'ResourceTypeOptInPreference' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceType', ], 'value' => [ 'shape' => 'IsEnabled', ], ], 'ResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'RestoreDeletionStatus' => [ 'type' => 'string', 'enum' => [ 'DELETING', 'FAILED', 'SUCCESSFUL', ], ], 'RestoreJobCreator' => [ 'type' => 'structure', 'members' => [ 'RestoreTestingPlanArn' => [ 'shape' => 'ARN', ], ], ], 'RestoreJobId' => [ 'type' => 'string', ], 'RestoreJobState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PENDING', 'RUNNING', 'ABORTED', 'COMPLETED', 'FAILED', 'AGGREGATE_ALL', 'ANY', ], ], 'RestoreJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'COMPLETED', 'ABORTED', 'FAILED', ], ], 'RestoreJobSummary' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'Region', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'State' => [ 'shape' => 'RestoreJobState', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Count' => [ 'shape' => 'integer', ], 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], ], ], 'RestoreJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreJobSummary', ], ], 'RestoreJobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreJobsListMember', ], ], 'RestoreJobsListMember' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RestoreJobId' => [ 'shape' => 'string', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'CompletionDate' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'RestoreJobStatus', ], 'StatusMessage' => [ 'shape' => 'string', ], 'PercentDone' => [ 'shape' => 'string', ], 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'ExpectedCompletionTimeMinutes' => [ 'shape' => 'Long', ], 'CreatedResourceArn' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'RecoveryPointCreationDate' => [ 'shape' => 'timestamp', ], 'CreatedBy' => [ 'shape' => 'RestoreJobCreator', ], 'ValidationStatus' => [ 'shape' => 'RestoreValidationStatus', ], 'ValidationStatusMessage' => [ 'shape' => 'string', ], 'DeletionStatus' => [ 'shape' => 'RestoreDeletionStatus', ], 'DeletionStatusMessage' => [ 'shape' => 'string', ], ], ], 'RestoreTestingPlanForCreate' => [ 'type' => 'structure', 'required' => [ 'RecoveryPointSelection', 'RestoreTestingPlanName', 'ScheduleExpression', ], 'members' => [ 'RecoveryPointSelection' => [ 'shape' => 'RestoreTestingRecoveryPointSelection', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'ScheduleExpression' => [ 'shape' => 'String', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'String', ], 'StartWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingPlanForGet' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RecoveryPointSelection', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', 'ScheduleExpression', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatorRequestId' => [ 'shape' => 'String', ], 'LastExecutionTime' => [ 'shape' => 'Timestamp', ], 'LastUpdateTime' => [ 'shape' => 'Timestamp', ], 'RecoveryPointSelection' => [ 'shape' => 'RestoreTestingRecoveryPointSelection', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'ScheduleExpression' => [ 'shape' => 'String', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'String', ], 'StartWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingPlanForList' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', 'ScheduleExpression', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastExecutionTime' => [ 'shape' => 'Timestamp', ], 'LastUpdateTime' => [ 'shape' => 'Timestamp', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'ScheduleExpression' => [ 'shape' => 'String', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'String', ], 'StartWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingPlanForUpdate' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointSelection' => [ 'shape' => 'RestoreTestingRecoveryPointSelection', ], 'ScheduleExpression' => [ 'shape' => 'String', ], 'ScheduleExpressionTimezone' => [ 'shape' => 'String', ], 'StartWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingPlans' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreTestingPlanForList', ], ], 'RestoreTestingRecoveryPointSelection' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'RestoreTestingRecoveryPointSelectionAlgorithm', ], 'ExcludeVaults' => [ 'shape' => 'stringList', ], 'IncludeVaults' => [ 'shape' => 'stringList', ], 'RecoveryPointTypes' => [ 'shape' => 'RestoreTestingRecoveryPointTypeList', ], 'SelectionWindowDays' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingRecoveryPointSelectionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'LATEST_WITHIN_WINDOW', 'RANDOM_WITHIN_WINDOW', ], ], 'RestoreTestingRecoveryPointType' => [ 'type' => 'string', 'enum' => [ 'CONTINUOUS', 'SNAPSHOT', ], ], 'RestoreTestingRecoveryPointTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreTestingRecoveryPointType', ], ], 'RestoreTestingSelectionForCreate' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'ProtectedResourceType', 'RestoreTestingSelectionName', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'String', ], 'ProtectedResourceArns' => [ 'shape' => 'stringList', ], 'ProtectedResourceConditions' => [ 'shape' => 'ProtectedResourceConditions', ], 'ProtectedResourceType' => [ 'shape' => 'String', ], 'RestoreMetadataOverrides' => [ 'shape' => 'SensitiveStringMap', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', ], 'ValidationWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingSelectionForGet' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'IamRoleArn', 'ProtectedResourceType', 'RestoreTestingPlanName', 'RestoreTestingSelectionName', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatorRequestId' => [ 'shape' => 'String', ], 'IamRoleArn' => [ 'shape' => 'String', ], 'ProtectedResourceArns' => [ 'shape' => 'stringList', ], 'ProtectedResourceConditions' => [ 'shape' => 'ProtectedResourceConditions', ], 'ProtectedResourceType' => [ 'shape' => 'String', ], 'RestoreMetadataOverrides' => [ 'shape' => 'SensitiveStringMap', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', ], 'ValidationWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingSelectionForList' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'IamRoleArn', 'ProtectedResourceType', 'RestoreTestingPlanName', 'RestoreTestingSelectionName', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'IamRoleArn' => [ 'shape' => 'String', ], 'ProtectedResourceType' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', ], 'ValidationWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingSelectionForUpdate' => [ 'type' => 'structure', 'members' => [ 'IamRoleArn' => [ 'shape' => 'String', ], 'ProtectedResourceArns' => [ 'shape' => 'stringList', ], 'ProtectedResourceConditions' => [ 'shape' => 'ProtectedResourceConditions', ], 'RestoreMetadataOverrides' => [ 'shape' => 'SensitiveStringMap', ], 'ValidationWindowHours' => [ 'shape' => 'integer', ], ], ], 'RestoreTestingSelections' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestoreTestingSelectionForList', ], ], 'RestoreValidationStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCESSFUL', 'TIMED_OUT', 'VALIDATING', ], ], 'SensitiveStringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], 'sensitive' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'string', ], 'Message' => [ 'shape' => 'string', ], 'Type' => [ 'shape' => 'string', ], 'Context' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'StartBackupJobInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'ResourceArn', 'IamRoleArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'ResourceArn' => [ 'shape' => 'ARN', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'IdempotencyToken' => [ 'shape' => 'string', ], 'StartWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'CompleteWindowMinutes' => [ 'shape' => 'WindowMinutes', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'RecoveryPointTags' => [ 'shape' => 'Tags', ], 'BackupOptions' => [ 'shape' => 'BackupOptions', ], ], ], 'StartBackupJobOutput' => [ 'type' => 'structure', 'members' => [ 'BackupJobId' => [ 'shape' => 'string', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'IsParent' => [ 'shape' => 'boolean', ], ], ], 'StartCopyJobInput' => [ 'type' => 'structure', 'required' => [ 'RecoveryPointArn', 'SourceBackupVaultName', 'DestinationBackupVaultArn', 'IamRoleArn', ], 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'SourceBackupVaultName' => [ 'shape' => 'BackupVaultName', ], 'DestinationBackupVaultArn' => [ 'shape' => 'ARN', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'IdempotencyToken' => [ 'shape' => 'string', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], ], ], 'StartCopyJobOutput' => [ 'type' => 'structure', 'members' => [ 'CopyJobId' => [ 'shape' => 'string', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'IsParent' => [ 'shape' => 'boolean', ], ], ], 'StartReportJobInput' => [ 'type' => 'structure', 'required' => [ 'ReportPlanName', ], 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', 'location' => 'uri', 'locationName' => 'reportPlanName', ], 'IdempotencyToken' => [ 'shape' => 'string', 'idempotencyToken' => true, ], ], ], 'StartReportJobOutput' => [ 'type' => 'structure', 'members' => [ 'ReportJobId' => [ 'shape' => 'ReportJobId', ], ], ], 'StartRestoreJobInput' => [ 'type' => 'structure', 'required' => [ 'RecoveryPointArn', 'Metadata', ], 'members' => [ 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'IamRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'IdempotencyToken' => [ 'shape' => 'string', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'CopySourceTagsToRestoredResource' => [ 'shape' => 'boolean', ], ], ], 'StartRestoreJobOutput' => [ 'type' => 'structure', 'members' => [ 'RestoreJobId' => [ 'shape' => 'RestoreJobId', ], ], ], 'StopBackupJobInput' => [ 'type' => 'structure', 'required' => [ 'BackupJobId', ], 'members' => [ 'BackupJobId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupJobId', ], ], ], 'StorageClass' => [ 'type' => 'string', 'enum' => [ 'WARM', 'COLD', 'DELETED', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'sensitive' => true, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'sensitive' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timezone' => [ 'type' => 'string', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeyList', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeyList' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateBackupPlanInput' => [ 'type' => 'structure', 'required' => [ 'BackupPlanId', 'BackupPlan', ], 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'backupPlanId', ], 'BackupPlan' => [ 'shape' => 'BackupPlanInput', ], ], ], 'UpdateBackupPlanOutput' => [ 'type' => 'structure', 'members' => [ 'BackupPlanId' => [ 'shape' => 'string', ], 'BackupPlanArn' => [ 'shape' => 'ARN', ], 'CreationDate' => [ 'shape' => 'timestamp', ], 'VersionId' => [ 'shape' => 'string', ], 'AdvancedBackupSettings' => [ 'shape' => 'AdvancedBackupSettings', ], ], ], 'UpdateFrameworkInput' => [ 'type' => 'structure', 'required' => [ 'FrameworkName', ], 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', 'location' => 'uri', 'locationName' => 'frameworkName', ], 'FrameworkDescription' => [ 'shape' => 'FrameworkDescription', ], 'FrameworkControls' => [ 'shape' => 'FrameworkControls', ], 'IdempotencyToken' => [ 'shape' => 'string', 'idempotencyToken' => true, ], ], ], 'UpdateFrameworkOutput' => [ 'type' => 'structure', 'members' => [ 'FrameworkName' => [ 'shape' => 'FrameworkName', ], 'FrameworkArn' => [ 'shape' => 'ARN', ], 'CreationTime' => [ 'shape' => 'timestamp', ], ], ], 'UpdateGlobalSettingsInput' => [ 'type' => 'structure', 'members' => [ 'GlobalSettings' => [ 'shape' => 'GlobalSettings', ], ], ], 'UpdateRecoveryPointLifecycleInput' => [ 'type' => 'structure', 'required' => [ 'BackupVaultName', 'RecoveryPointArn', ], 'members' => [ 'BackupVaultName' => [ 'shape' => 'BackupVaultName', 'location' => 'uri', 'locationName' => 'backupVaultName', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'recoveryPointArn', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], ], ], 'UpdateRecoveryPointLifecycleOutput' => [ 'type' => 'structure', 'members' => [ 'BackupVaultArn' => [ 'shape' => 'ARN', ], 'RecoveryPointArn' => [ 'shape' => 'ARN', ], 'Lifecycle' => [ 'shape' => 'Lifecycle', ], 'CalculatedLifecycle' => [ 'shape' => 'CalculatedLifecycle', ], ], ], 'UpdateRegionSettingsInput' => [ 'type' => 'structure', 'members' => [ 'ResourceTypeOptInPreference' => [ 'shape' => 'ResourceTypeOptInPreference', ], 'ResourceTypeManagementPreference' => [ 'shape' => 'ResourceTypeManagementPreference', ], ], ], 'UpdateReportPlanInput' => [ 'type' => 'structure', 'required' => [ 'ReportPlanName', ], 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', 'location' => 'uri', 'locationName' => 'reportPlanName', ], 'ReportPlanDescription' => [ 'shape' => 'ReportPlanDescription', ], 'ReportDeliveryChannel' => [ 'shape' => 'ReportDeliveryChannel', ], 'ReportSetting' => [ 'shape' => 'ReportSetting', ], 'IdempotencyToken' => [ 'shape' => 'string', 'idempotencyToken' => true, ], ], ], 'UpdateReportPlanOutput' => [ 'type' => 'structure', 'members' => [ 'ReportPlanName' => [ 'shape' => 'ReportPlanName', ], 'ReportPlanArn' => [ 'shape' => 'ARN', ], 'CreationTime' => [ 'shape' => 'timestamp', ], ], ], 'UpdateRestoreTestingPlanInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlan', 'RestoreTestingPlanName', ], 'members' => [ 'RestoreTestingPlan' => [ 'shape' => 'RestoreTestingPlanForUpdate', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], ], ], 'UpdateRestoreTestingPlanOutput' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', 'UpdateTime', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateRestoreTestingSelectionInput' => [ 'type' => 'structure', 'required' => [ 'RestoreTestingPlanName', 'RestoreTestingSelection', 'RestoreTestingSelectionName', ], 'members' => [ 'RestoreTestingPlanName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingPlanName', ], 'RestoreTestingSelection' => [ 'shape' => 'RestoreTestingSelectionForUpdate', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'RestoreTestingSelectionName', ], ], ], 'UpdateRestoreTestingSelectionOutput' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'RestoreTestingPlanArn', 'RestoreTestingPlanName', 'RestoreTestingSelectionName', 'UpdateTime', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'RestoreTestingPlanArn' => [ 'shape' => 'String', ], 'RestoreTestingPlanName' => [ 'shape' => 'String', ], 'RestoreTestingSelectionName' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'VaultNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'VaultState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'AVAILABLE', 'FAILED', ], ], 'VaultType' => [ 'type' => 'string', 'enum' => [ 'BACKUP_VAULT', 'LOGICALLY_AIR_GAPPED_BACKUP_VAULT', ], ], 'WindowMinutes' => [ 'type' => 'long', ], 'boolean' => [ 'type' => 'boolean', ], 'integer' => [ 'type' => 'integer', ], 'long' => [ 'type' => 'long', ], 'string' => [ 'type' => 'string', ], 'stringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'stringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
