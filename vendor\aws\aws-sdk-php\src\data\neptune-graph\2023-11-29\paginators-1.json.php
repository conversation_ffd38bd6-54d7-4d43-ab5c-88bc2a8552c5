<?php
// This file was auto-generated from sdk-root/src/data/neptune-graph/2023-11-29/paginators-1.json
return [ 'pagination' => [ 'ListGraphSnapshots' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'graphSnapshots', ], 'ListGraphs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'graphs', ], 'ListImportTasks' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'tasks', ], 'ListPrivateGraphEndpoints' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'privateGraphEndpoints', ], ],];
