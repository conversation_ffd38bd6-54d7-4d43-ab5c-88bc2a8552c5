<?php
namespace Aws\IoTEvents;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS IoT Events** service.
 * @method \Aws\Result createAlarmModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAlarmModelAsync(array $args = [])
 * @method \Aws\Result createDetectorModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDetectorModelAsync(array $args = [])
 * @method \Aws\Result createInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInputAsync(array $args = [])
 * @method \Aws\Result deleteAlarmModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAlarmModelAsync(array $args = [])
 * @method \Aws\Result deleteDetectorModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDetectorModelAsync(array $args = [])
 * @method \Aws\Result deleteInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInputAsync(array $args = [])
 * @method \Aws\Result describeAlarmModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAlarmModelAsync(array $args = [])
 * @method \Aws\Result describeDetectorModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDetectorModelAsync(array $args = [])
 * @method \Aws\Result describeDetectorModelAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDetectorModelAnalysisAsync(array $args = [])
 * @method \Aws\Result describeInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInputAsync(array $args = [])
 * @method \Aws\Result describeLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result getDetectorModelAnalysisResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDetectorModelAnalysisResultsAsync(array $args = [])
 * @method \Aws\Result listAlarmModelVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAlarmModelVersionsAsync(array $args = [])
 * @method \Aws\Result listAlarmModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAlarmModelsAsync(array $args = [])
 * @method \Aws\Result listDetectorModelVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDetectorModelVersionsAsync(array $args = [])
 * @method \Aws\Result listDetectorModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDetectorModelsAsync(array $args = [])
 * @method \Aws\Result listInputRoutings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputRoutingsAsync(array $args = [])
 * @method \Aws\Result listInputs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result startDetectorModelAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDetectorModelAnalysisAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAlarmModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAlarmModelAsync(array $args = [])
 * @method \Aws\Result updateDetectorModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDetectorModelAsync(array $args = [])
 * @method \Aws\Result updateInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInputAsync(array $args = [])
 */
class IoTEventsClient extends AwsClient {}
