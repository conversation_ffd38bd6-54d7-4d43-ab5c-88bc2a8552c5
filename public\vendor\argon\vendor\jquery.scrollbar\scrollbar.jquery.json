{"name": "scrollbar", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Cross-browser CSS customizable scrollbar with advanced features: standard scroll behavior in all browsers/devices, responsive design support (no fixed height or width required), horizontal/vertical scrollbar or both, external scrollbars, automatically hide/show scrollbars (if content/container size is changed) and more...", "keywords": ["scroll", "scrollbar"], "version": "0.2.11", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "MIT", "url": "https://github.com/gromo/jquery.scrollbar/blob/master/license-mit.txt"}, {"type": "GPLv2", "url": "https://github.com/gromo/jquery.scrollbar/blob/master/license-gpl.txt"}], "homepage": "http://gromo.github.io/jquery.scrollbar/", "download": "http://gromo.github.io/jquery.scrollbar/jquery.scrollbar.zip", "demo": "http://gromo.github.io/jquery.scrollbar/demo/basic.html", "dependencies": {"jquery": ">=1.7"}}