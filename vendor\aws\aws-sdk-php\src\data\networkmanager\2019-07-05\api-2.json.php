<?php
// This file was auto-generated from sdk-root/src/data/networkmanager/2019-07-05/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-07-05', 'endpointPrefix' => 'networkmanager', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'NetworkManager', 'serviceFullName' => 'AWS Network Manager', 'serviceId' => 'NetworkManager', 'signatureVersion' => 'v4', 'signingName' => 'networkmanager', 'uid' => 'networkmanager-2019-07-05', ], 'operations' => [ 'AcceptAttachment' => [ 'name' => 'AcceptAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/attachments/{attachmentId}/accept', ], 'input' => [ 'shape' => 'AcceptAttachmentRequest', ], 'output' => [ 'shape' => 'AcceptAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateConnectPeer' => [ 'name' => 'AssociateConnectPeer', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/connect-peer-associations', ], 'input' => [ 'shape' => 'AssociateConnectPeerRequest', ], 'output' => [ 'shape' => 'AssociateConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateCustomerGateway' => [ 'name' => 'AssociateCustomerGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/customer-gateway-associations', ], 'input' => [ 'shape' => 'AssociateCustomerGatewayRequest', ], 'output' => [ 'shape' => 'AssociateCustomerGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateLink' => [ 'name' => 'AssociateLink', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/link-associations', ], 'input' => [ 'shape' => 'AssociateLinkRequest', ], 'output' => [ 'shape' => 'AssociateLinkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateTransitGatewayConnectPeer' => [ 'name' => 'AssociateTransitGatewayConnectPeer', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations', ], 'input' => [ 'shape' => 'AssociateTransitGatewayConnectPeerRequest', ], 'output' => [ 'shape' => 'AssociateTransitGatewayConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConnectAttachment' => [ 'name' => 'CreateConnectAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/connect-attachments', ], 'input' => [ 'shape' => 'CreateConnectAttachmentRequest', ], 'output' => [ 'shape' => 'CreateConnectAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConnectPeer' => [ 'name' => 'CreateConnectPeer', 'http' => [ 'method' => 'POST', 'requestUri' => '/connect-peers', ], 'input' => [ 'shape' => 'CreateConnectPeerRequest', ], 'output' => [ 'shape' => 'CreateConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConnection' => [ 'name' => 'CreateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/connections', ], 'input' => [ 'shape' => 'CreateConnectionRequest', ], 'output' => [ 'shape' => 'CreateConnectionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateCoreNetwork' => [ 'name' => 'CreateCoreNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/core-networks', ], 'input' => [ 'shape' => 'CreateCoreNetworkRequest', ], 'output' => [ 'shape' => 'CreateCoreNetworkResponse', ], 'errors' => [ [ 'shape' => 'CoreNetworkPolicyException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDevice' => [ 'name' => 'CreateDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/devices', ], 'input' => [ 'shape' => 'CreateDeviceRequest', ], 'output' => [ 'shape' => 'CreateDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateGlobalNetwork' => [ 'name' => 'CreateGlobalNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks', ], 'input' => [ 'shape' => 'CreateGlobalNetworkRequest', ], 'output' => [ 'shape' => 'CreateGlobalNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateLink' => [ 'name' => 'CreateLink', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/links', ], 'input' => [ 'shape' => 'CreateLinkRequest', ], 'output' => [ 'shape' => 'CreateLinkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSite' => [ 'name' => 'CreateSite', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/sites', ], 'input' => [ 'shape' => 'CreateSiteRequest', ], 'output' => [ 'shape' => 'CreateSiteResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSiteToSiteVpnAttachment' => [ 'name' => 'CreateSiteToSiteVpnAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/site-to-site-vpn-attachments', ], 'input' => [ 'shape' => 'CreateSiteToSiteVpnAttachmentRequest', ], 'output' => [ 'shape' => 'CreateSiteToSiteVpnAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateTransitGatewayPeering' => [ 'name' => 'CreateTransitGatewayPeering', 'http' => [ 'method' => 'POST', 'requestUri' => '/transit-gateway-peerings', ], 'input' => [ 'shape' => 'CreateTransitGatewayPeeringRequest', ], 'output' => [ 'shape' => 'CreateTransitGatewayPeeringResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateTransitGatewayRouteTableAttachment' => [ 'name' => 'CreateTransitGatewayRouteTableAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/transit-gateway-route-table-attachments', ], 'input' => [ 'shape' => 'CreateTransitGatewayRouteTableAttachmentRequest', ], 'output' => [ 'shape' => 'CreateTransitGatewayRouteTableAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateVpcAttachment' => [ 'name' => 'CreateVpcAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/vpc-attachments', ], 'input' => [ 'shape' => 'CreateVpcAttachmentRequest', ], 'output' => [ 'shape' => 'CreateVpcAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAttachment' => [ 'name' => 'DeleteAttachment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/attachments/{attachmentId}', ], 'input' => [ 'shape' => 'DeleteAttachmentRequest', ], 'output' => [ 'shape' => 'DeleteAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteConnectPeer' => [ 'name' => 'DeleteConnectPeer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/connect-peers/{connectPeerId}', ], 'input' => [ 'shape' => 'DeleteConnectPeerRequest', ], 'output' => [ 'shape' => 'DeleteConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/connections/{connectionId}', ], 'input' => [ 'shape' => 'DeleteConnectionRequest', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCoreNetwork' => [ 'name' => 'DeleteCoreNetwork', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/core-networks/{coreNetworkId}', ], 'input' => [ 'shape' => 'DeleteCoreNetworkRequest', ], 'output' => [ 'shape' => 'DeleteCoreNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCoreNetworkPolicyVersion' => [ 'name' => 'DeleteCoreNetworkPolicyVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-policy-versions/{policyVersionId}', ], 'input' => [ 'shape' => 'DeleteCoreNetworkPolicyVersionRequest', ], 'output' => [ 'shape' => 'DeleteCoreNetworkPolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteDevice' => [ 'name' => 'DeleteDevice', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/devices/{deviceId}', ], 'input' => [ 'shape' => 'DeleteDeviceRequest', ], 'output' => [ 'shape' => 'DeleteDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteGlobalNetwork' => [ 'name' => 'DeleteGlobalNetwork', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}', ], 'input' => [ 'shape' => 'DeleteGlobalNetworkRequest', ], 'output' => [ 'shape' => 'DeleteGlobalNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteLink' => [ 'name' => 'DeleteLink', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/links/{linkId}', ], 'input' => [ 'shape' => 'DeleteLinkRequest', ], 'output' => [ 'shape' => 'DeleteLinkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeletePeering' => [ 'name' => 'DeletePeering', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/peerings/{peeringId}', ], 'input' => [ 'shape' => 'DeletePeeringRequest', ], 'output' => [ 'shape' => 'DeletePeeringResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resource-policy/{resourceArn}', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSite' => [ 'name' => 'DeleteSite', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/sites/{siteId}', ], 'input' => [ 'shape' => 'DeleteSiteRequest', ], 'output' => [ 'shape' => 'DeleteSiteResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeregisterTransitGateway' => [ 'name' => 'DeregisterTransitGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-registrations/{transitGatewayArn}', ], 'input' => [ 'shape' => 'DeregisterTransitGatewayRequest', ], 'output' => [ 'shape' => 'DeregisterTransitGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeGlobalNetworks' => [ 'name' => 'DescribeGlobalNetworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks', ], 'input' => [ 'shape' => 'DescribeGlobalNetworksRequest', ], 'output' => [ 'shape' => 'DescribeGlobalNetworksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateConnectPeer' => [ 'name' => 'DisassociateConnectPeer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/connect-peer-associations/{connectPeerId}', ], 'input' => [ 'shape' => 'DisassociateConnectPeerRequest', ], 'output' => [ 'shape' => 'DisassociateConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateCustomerGateway' => [ 'name' => 'DisassociateCustomerGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/customer-gateway-associations/{customerGatewayArn}', ], 'input' => [ 'shape' => 'DisassociateCustomerGatewayRequest', ], 'output' => [ 'shape' => 'DisassociateCustomerGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateLink' => [ 'name' => 'DisassociateLink', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/link-associations', ], 'input' => [ 'shape' => 'DisassociateLinkRequest', ], 'output' => [ 'shape' => 'DisassociateLinkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateTransitGatewayConnectPeer' => [ 'name' => 'DisassociateTransitGatewayConnectPeer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations/{transitGatewayConnectPeerArn}', ], 'input' => [ 'shape' => 'DisassociateTransitGatewayConnectPeerRequest', ], 'output' => [ 'shape' => 'DisassociateTransitGatewayConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ExecuteCoreNetworkChangeSet' => [ 'name' => 'ExecuteCoreNetworkChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-change-sets/{policyVersionId}/execute', ], 'input' => [ 'shape' => 'ExecuteCoreNetworkChangeSetRequest', ], 'output' => [ 'shape' => 'ExecuteCoreNetworkChangeSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetConnectAttachment' => [ 'name' => 'GetConnectAttachment', 'http' => [ 'method' => 'GET', 'requestUri' => '/connect-attachments/{attachmentId}', ], 'input' => [ 'shape' => 'GetConnectAttachmentRequest', ], 'output' => [ 'shape' => 'GetConnectAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConnectPeer' => [ 'name' => 'GetConnectPeer', 'http' => [ 'method' => 'GET', 'requestUri' => '/connect-peers/{connectPeerId}', ], 'input' => [ 'shape' => 'GetConnectPeerRequest', ], 'output' => [ 'shape' => 'GetConnectPeerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConnectPeerAssociations' => [ 'name' => 'GetConnectPeerAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/connect-peer-associations', ], 'input' => [ 'shape' => 'GetConnectPeerAssociationsRequest', ], 'output' => [ 'shape' => 'GetConnectPeerAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConnections' => [ 'name' => 'GetConnections', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/connections', ], 'input' => [ 'shape' => 'GetConnectionsRequest', ], 'output' => [ 'shape' => 'GetConnectionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCoreNetwork' => [ 'name' => 'GetCoreNetwork', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks/{coreNetworkId}', ], 'input' => [ 'shape' => 'GetCoreNetworkRequest', ], 'output' => [ 'shape' => 'GetCoreNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCoreNetworkChangeEvents' => [ 'name' => 'GetCoreNetworkChangeEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-change-events/{policyVersionId}', ], 'input' => [ 'shape' => 'GetCoreNetworkChangeEventsRequest', ], 'output' => [ 'shape' => 'GetCoreNetworkChangeEventsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCoreNetworkChangeSet' => [ 'name' => 'GetCoreNetworkChangeSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-change-sets/{policyVersionId}', ], 'input' => [ 'shape' => 'GetCoreNetworkChangeSetRequest', ], 'output' => [ 'shape' => 'GetCoreNetworkChangeSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCoreNetworkPolicy' => [ 'name' => 'GetCoreNetworkPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-policy', ], 'input' => [ 'shape' => 'GetCoreNetworkPolicyRequest', ], 'output' => [ 'shape' => 'GetCoreNetworkPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCustomerGatewayAssociations' => [ 'name' => 'GetCustomerGatewayAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/customer-gateway-associations', ], 'input' => [ 'shape' => 'GetCustomerGatewayAssociationsRequest', ], 'output' => [ 'shape' => 'GetCustomerGatewayAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDevices' => [ 'name' => 'GetDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/devices', ], 'input' => [ 'shape' => 'GetDevicesRequest', ], 'output' => [ 'shape' => 'GetDevicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetLinkAssociations' => [ 'name' => 'GetLinkAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/link-associations', ], 'input' => [ 'shape' => 'GetLinkAssociationsRequest', ], 'output' => [ 'shape' => 'GetLinkAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetLinks' => [ 'name' => 'GetLinks', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/links', ], 'input' => [ 'shape' => 'GetLinksRequest', ], 'output' => [ 'shape' => 'GetLinksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetNetworkResourceCounts' => [ 'name' => 'GetNetworkResourceCounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/network-resource-count', ], 'input' => [ 'shape' => 'GetNetworkResourceCountsRequest', ], 'output' => [ 'shape' => 'GetNetworkResourceCountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetNetworkResourceRelationships' => [ 'name' => 'GetNetworkResourceRelationships', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/network-resource-relationships', ], 'input' => [ 'shape' => 'GetNetworkResourceRelationshipsRequest', ], 'output' => [ 'shape' => 'GetNetworkResourceRelationshipsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetNetworkResources' => [ 'name' => 'GetNetworkResources', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/network-resources', ], 'input' => [ 'shape' => 'GetNetworkResourcesRequest', ], 'output' => [ 'shape' => 'GetNetworkResourcesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetNetworkRoutes' => [ 'name' => 'GetNetworkRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/network-routes', ], 'input' => [ 'shape' => 'GetNetworkRoutesRequest', ], 'output' => [ 'shape' => 'GetNetworkRoutesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetNetworkTelemetry' => [ 'name' => 'GetNetworkTelemetry', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/network-telemetry', ], 'input' => [ 'shape' => 'GetNetworkTelemetryRequest', ], 'output' => [ 'shape' => 'GetNetworkTelemetryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-policy/{resourceArn}', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRouteAnalysis' => [ 'name' => 'GetRouteAnalysis', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/route-analyses/{routeAnalysisId}', ], 'input' => [ 'shape' => 'GetRouteAnalysisRequest', ], 'output' => [ 'shape' => 'GetRouteAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSiteToSiteVpnAttachment' => [ 'name' => 'GetSiteToSiteVpnAttachment', 'http' => [ 'method' => 'GET', 'requestUri' => '/site-to-site-vpn-attachments/{attachmentId}', ], 'input' => [ 'shape' => 'GetSiteToSiteVpnAttachmentRequest', ], 'output' => [ 'shape' => 'GetSiteToSiteVpnAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSites' => [ 'name' => 'GetSites', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/sites', ], 'input' => [ 'shape' => 'GetSitesRequest', ], 'output' => [ 'shape' => 'GetSitesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransitGatewayConnectPeerAssociations' => [ 'name' => 'GetTransitGatewayConnectPeerAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations', ], 'input' => [ 'shape' => 'GetTransitGatewayConnectPeerAssociationsRequest', ], 'output' => [ 'shape' => 'GetTransitGatewayConnectPeerAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransitGatewayPeering' => [ 'name' => 'GetTransitGatewayPeering', 'http' => [ 'method' => 'GET', 'requestUri' => '/transit-gateway-peerings/{peeringId}', ], 'input' => [ 'shape' => 'GetTransitGatewayPeeringRequest', ], 'output' => [ 'shape' => 'GetTransitGatewayPeeringResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransitGatewayRegistrations' => [ 'name' => 'GetTransitGatewayRegistrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-registrations', ], 'input' => [ 'shape' => 'GetTransitGatewayRegistrationsRequest', ], 'output' => [ 'shape' => 'GetTransitGatewayRegistrationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransitGatewayRouteTableAttachment' => [ 'name' => 'GetTransitGatewayRouteTableAttachment', 'http' => [ 'method' => 'GET', 'requestUri' => '/transit-gateway-route-table-attachments/{attachmentId}', ], 'input' => [ 'shape' => 'GetTransitGatewayRouteTableAttachmentRequest', ], 'output' => [ 'shape' => 'GetTransitGatewayRouteTableAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetVpcAttachment' => [ 'name' => 'GetVpcAttachment', 'http' => [ 'method' => 'GET', 'requestUri' => '/vpc-attachments/{attachmentId}', ], 'input' => [ 'shape' => 'GetVpcAttachmentRequest', ], 'output' => [ 'shape' => 'GetVpcAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAttachments' => [ 'name' => 'ListAttachments', 'http' => [ 'method' => 'GET', 'requestUri' => '/attachments', ], 'input' => [ 'shape' => 'ListAttachmentsRequest', ], 'output' => [ 'shape' => 'ListAttachmentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListConnectPeers' => [ 'name' => 'ListConnectPeers', 'http' => [ 'method' => 'GET', 'requestUri' => '/connect-peers', ], 'input' => [ 'shape' => 'ListConnectPeersRequest', ], 'output' => [ 'shape' => 'ListConnectPeersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoreNetworkPolicyVersions' => [ 'name' => 'ListCoreNetworkPolicyVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-policy-versions', ], 'input' => [ 'shape' => 'ListCoreNetworkPolicyVersionsRequest', ], 'output' => [ 'shape' => 'ListCoreNetworkPolicyVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoreNetworks' => [ 'name' => 'ListCoreNetworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/core-networks', ], 'input' => [ 'shape' => 'ListCoreNetworksRequest', ], 'output' => [ 'shape' => 'ListCoreNetworksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListOrganizationServiceAccessStatus' => [ 'name' => 'ListOrganizationServiceAccessStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/organizations/service-access', ], 'input' => [ 'shape' => 'ListOrganizationServiceAccessStatusRequest', ], 'output' => [ 'shape' => 'ListOrganizationServiceAccessStatusResponse', ], ], 'ListPeerings' => [ 'name' => 'ListPeerings', 'http' => [ 'method' => 'GET', 'requestUri' => '/peerings', ], 'input' => [ 'shape' => 'ListPeeringsRequest', ], 'output' => [ 'shape' => 'ListPeeringsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutCoreNetworkPolicy' => [ 'name' => 'PutCoreNetworkPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-policy', ], 'input' => [ 'shape' => 'PutCoreNetworkPolicyRequest', ], 'output' => [ 'shape' => 'PutCoreNetworkPolicyResponse', ], 'errors' => [ [ 'shape' => 'CoreNetworkPolicyException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/resource-policy/{resourceArn}', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RegisterTransitGateway' => [ 'name' => 'RegisterTransitGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/transit-gateway-registrations', ], 'input' => [ 'shape' => 'RegisterTransitGatewayRequest', ], 'output' => [ 'shape' => 'RegisterTransitGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RejectAttachment' => [ 'name' => 'RejectAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/attachments/{attachmentId}/reject', ], 'input' => [ 'shape' => 'RejectAttachmentRequest', ], 'output' => [ 'shape' => 'RejectAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RestoreCoreNetworkPolicyVersion' => [ 'name' => 'RestoreCoreNetworkPolicyVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/core-networks/{coreNetworkId}/core-network-policy-versions/{policyVersionId}/restore', ], 'input' => [ 'shape' => 'RestoreCoreNetworkPolicyVersionRequest', ], 'output' => [ 'shape' => 'RestoreCoreNetworkPolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartOrganizationServiceAccessUpdate' => [ 'name' => 'StartOrganizationServiceAccessUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/organizations/service-access', ], 'input' => [ 'shape' => 'StartOrganizationServiceAccessUpdateRequest', ], 'output' => [ 'shape' => 'StartOrganizationServiceAccessUpdateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartRouteAnalysis' => [ 'name' => 'StartRouteAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/global-networks/{globalNetworkId}/route-analyses', ], 'input' => [ 'shape' => 'StartRouteAnalysisRequest', ], 'output' => [ 'shape' => 'StartRouteAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateConnection' => [ 'name' => 'UpdateConnection', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}/connections/{connectionId}', ], 'input' => [ 'shape' => 'UpdateConnectionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateCoreNetwork' => [ 'name' => 'UpdateCoreNetwork', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/core-networks/{coreNetworkId}', ], 'input' => [ 'shape' => 'UpdateCoreNetworkRequest', ], 'output' => [ 'shape' => 'UpdateCoreNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDevice' => [ 'name' => 'UpdateDevice', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}/devices/{deviceId}', ], 'input' => [ 'shape' => 'UpdateDeviceRequest', ], 'output' => [ 'shape' => 'UpdateDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateGlobalNetwork' => [ 'name' => 'UpdateGlobalNetwork', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}', ], 'input' => [ 'shape' => 'UpdateGlobalNetworkRequest', ], 'output' => [ 'shape' => 'UpdateGlobalNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateLink' => [ 'name' => 'UpdateLink', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}/links/{linkId}', ], 'input' => [ 'shape' => 'UpdateLinkRequest', ], 'output' => [ 'shape' => 'UpdateLinkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateNetworkResourceMetadata' => [ 'name' => 'UpdateNetworkResourceMetadata', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}/network-resources/{resourceArn}/metadata', ], 'input' => [ 'shape' => 'UpdateNetworkResourceMetadataRequest', ], 'output' => [ 'shape' => 'UpdateNetworkResourceMetadataResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSite' => [ 'name' => 'UpdateSite', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/global-networks/{globalNetworkId}/sites/{siteId}', ], 'input' => [ 'shape' => 'UpdateSiteRequest', ], 'output' => [ 'shape' => 'UpdateSiteResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateVpcAttachment' => [ 'name' => 'UpdateVpcAttachment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/vpc-attachments/{attachmentId}', ], 'input' => [ 'shape' => 'UpdateVpcAttachmentRequest', ], 'output' => [ 'shape' => 'UpdateVpcAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AWSAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[\\s\\S]*', ], 'AWSLocation' => [ 'type' => 'structure', 'members' => [ 'Zone' => [ 'shape' => 'ConstrainedString', ], 'SubnetArn' => [ 'shape' => 'SubnetArn', ], ], ], 'AcceptAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'AcceptAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'AccountStatus' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'SLRDeploymentStatus' => [ 'shape' => 'SLRDeploymentStatus', ], ], ], 'AccountStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountStatus', ], ], 'Action' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'AssociateConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'ConnectPeerId', 'DeviceId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], ], ], 'AssociateConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeerAssociation' => [ 'shape' => 'ConnectPeerAssociation', ], ], ], 'AssociateCustomerGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'CustomerGatewayArn', 'GlobalNetworkId', 'DeviceId', ], 'members' => [ 'CustomerGatewayArn' => [ 'shape' => 'CustomerGatewayArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], ], ], 'AssociateCustomerGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'CustomerGatewayAssociation' => [ 'shape' => 'CustomerGatewayAssociation', ], ], ], 'AssociateLinkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'DeviceId', 'LinkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], ], ], 'AssociateLinkResponse' => [ 'type' => 'structure', 'members' => [ 'LinkAssociation' => [ 'shape' => 'LinkAssociation', ], ], ], 'AssociateTransitGatewayConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'TransitGatewayConnectPeerArn', 'DeviceId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayConnectPeerArn' => [ 'shape' => 'TransitGatewayConnectPeerArn', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], ], ], 'AssociateTransitGatewayConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayConnectPeerAssociation' => [ 'shape' => 'TransitGatewayConnectPeerAssociation', ], ], ], 'Attachment' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'CoreNetworkArn' => [ 'shape' => 'CoreNetworkArn', ], 'AttachmentId' => [ 'shape' => 'AttachmentId', ], 'OwnerAccountId' => [ 'shape' => 'AWSAccountId', ], 'AttachmentType' => [ 'shape' => 'AttachmentType', ], 'State' => [ 'shape' => 'AttachmentState', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'AttachmentPolicyRuleNumber' => [ 'shape' => 'Integer', ], 'SegmentName' => [ 'shape' => 'ConstrainedString', ], 'Tags' => [ 'shape' => 'TagList', ], 'ProposedSegmentChange' => [ 'shape' => 'ProposedSegmentChange', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'UpdatedAt' => [ 'shape' => 'DateTime', ], ], ], 'AttachmentId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^attachment-([0-9a-f]{8,17})$', ], 'AttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attachment', ], ], 'AttachmentState' => [ 'type' => 'string', 'enum' => [ 'REJECTED', 'PENDING_ATTACHMENT_ACCEPTANCE', 'CREATING', 'FAILED', 'AVAILABLE', 'UPDATING', 'PENDING_NETWORK_UPDATE', 'PENDING_TAG_ACCEPTANCE', 'DELETING', ], ], 'AttachmentType' => [ 'type' => 'string', 'enum' => [ 'CONNECT', 'SITE_TO_SITE_VPN', 'VPC', 'TRANSIT_GATEWAY_ROUTE_TABLE', ], ], 'Bandwidth' => [ 'type' => 'structure', 'members' => [ 'UploadSpeed' => [ 'shape' => 'Integer', ], 'DownloadSpeed' => [ 'shape' => 'Integer', ], ], ], 'BgpOptions' => [ 'type' => 'structure', 'members' => [ 'PeerAsn' => [ 'shape' => 'Long', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ChangeAction' => [ 'type' => 'string', 'enum' => [ 'ADD', 'MODIFY', 'REMOVE', ], ], 'ChangeSetState' => [ 'type' => 'string', 'enum' => [ 'PENDING_GENERATION', 'FAILED_GENERATION', 'READY_TO_EXECUTE', 'EXECUTING', 'EXECUTION_SUCCEEDED', 'OUT_OF_DATE', ], ], 'ChangeStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'CORE_NETWORK_SEGMENT', 'CORE_NETWORK_EDGE', 'ATTACHMENT_MAPPING', 'ATTACHMENT_ROUTE_PROPAGATION', 'ATTACHMENT_ROUTE_STATIC', 'CORE_NETWORK_CONFIGURATION', 'SEGMENTS_CONFIGURATION', 'SEGMENT_ACTIONS_CONFIGURATION', 'ATTACHMENT_POLICIES_CONFIGURATION', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'ResourceId' => [ 'shape' => 'ServerSideString', ], 'ResourceType' => [ 'shape' => 'ServerSideString', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectAttachment' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], 'TransportAttachmentId' => [ 'shape' => 'AttachmentId', ], 'Options' => [ 'shape' => 'ConnectAttachmentOptions', ], ], ], 'ConnectAttachmentOptions' => [ 'type' => 'structure', 'members' => [ 'Protocol' => [ 'shape' => 'TunnelProtocol', ], ], ], 'ConnectPeer' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'ConnectAttachmentId' => [ 'shape' => 'AttachmentId', ], 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'State' => [ 'shape' => 'ConnectPeerState', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'Configuration' => [ 'shape' => 'ConnectPeerConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], 'SubnetArn' => [ 'shape' => 'SubnetArn', ], ], ], 'ConnectPeerAssociation' => [ 'type' => 'structure', 'members' => [ 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'State' => [ 'shape' => 'ConnectPeerAssociationState', ], ], ], 'ConnectPeerAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectPeerAssociation', ], ], 'ConnectPeerAssociationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'DELETED', ], ], 'ConnectPeerBgpConfiguration' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkAsn' => [ 'shape' => 'Long', ], 'PeerAsn' => [ 'shape' => 'Long', ], 'CoreNetworkAddress' => [ 'shape' => 'IPAddress', ], 'PeerAddress' => [ 'shape' => 'IPAddress', ], ], ], 'ConnectPeerBgpConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectPeerBgpConfiguration', ], ], 'ConnectPeerConfiguration' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkAddress' => [ 'shape' => 'IPAddress', ], 'PeerAddress' => [ 'shape' => 'IPAddress', ], 'InsideCidrBlocks' => [ 'shape' => 'ConstrainedStringList', ], 'Protocol' => [ 'shape' => 'TunnelProtocol', ], 'BgpConfigurations' => [ 'shape' => 'ConnectPeerBgpConfigurationList', ], ], ], 'ConnectPeerId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^connect-peer-([0-9a-f]{8,17})$', ], 'ConnectPeerIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectPeerId', ], ], 'ConnectPeerState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'FAILED', 'AVAILABLE', 'DELETING', ], ], 'ConnectPeerSummary' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'ConnectAttachmentId' => [ 'shape' => 'AttachmentId', ], 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'ConnectPeerState' => [ 'shape' => 'ConnectPeerState', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'Tags' => [ 'shape' => 'TagList', ], 'SubnetArn' => [ 'shape' => 'SubnetArn', ], ], ], 'ConnectPeerSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectPeerSummary', ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'ConnectedDeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'ConnectedLinkId' => [ 'shape' => 'LinkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'ConnectionState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ConnectionArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ConnectionHealth' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ConnectionType', ], 'Status' => [ 'shape' => 'ConnectionStatus', ], 'Timestamp' => [ 'shape' => 'DateTime', ], ], ], 'ConnectionId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ConnectionIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionId', ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'ConnectionState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'UPDATING', ], ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'UP', 'DOWN', ], ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'BGP', 'IPSEC', ], ], 'ConstrainedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ConstrainedStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConstrainedString', ], ], 'CoreNetwork' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'CoreNetworkArn' => [ 'shape' => 'CoreNetworkArn', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'CoreNetworkState', ], 'Segments' => [ 'shape' => 'CoreNetworkSegmentList', ], 'Edges' => [ 'shape' => 'CoreNetworkEdgeList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CoreNetworkArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'CoreNetworkChange' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChangeType', ], 'Action' => [ 'shape' => 'ChangeAction', ], 'Identifier' => [ 'shape' => 'ConstrainedString', ], 'PreviousValues' => [ 'shape' => 'CoreNetworkChangeValues', ], 'NewValues' => [ 'shape' => 'CoreNetworkChangeValues', ], 'IdentifierPath' => [ 'shape' => 'ConstrainedString', ], ], ], 'CoreNetworkChangeEvent' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChangeType', ], 'Action' => [ 'shape' => 'ChangeAction', ], 'IdentifierPath' => [ 'shape' => 'ConstrainedString', ], 'EventTime' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'ChangeStatus', ], 'Values' => [ 'shape' => 'CoreNetworkChangeEventValues', ], ], ], 'CoreNetworkChangeEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkChangeEvent', ], ], 'CoreNetworkChangeEventValues' => [ 'type' => 'structure', 'members' => [ 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'SegmentName' => [ 'shape' => 'ConstrainedString', ], 'AttachmentId' => [ 'shape' => 'AttachmentId', ], 'Cidr' => [ 'shape' => 'ConstrainedString', ], ], ], 'CoreNetworkChangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkChange', ], ], 'CoreNetworkChangeValues' => [ 'type' => 'structure', 'members' => [ 'SegmentName' => [ 'shape' => 'ConstrainedString', ], 'EdgeLocations' => [ 'shape' => 'ExternalRegionCodeList', ], 'Asn' => [ 'shape' => 'Long', ], 'Cidr' => [ 'shape' => 'ConstrainedString', ], 'DestinationIdentifier' => [ 'shape' => 'ConstrainedString', ], 'InsideCidrBlocks' => [ 'shape' => 'ConstrainedStringList', ], 'SharedSegments' => [ 'shape' => 'ConstrainedStringList', ], ], ], 'CoreNetworkEdge' => [ 'type' => 'structure', 'members' => [ 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'Asn' => [ 'shape' => 'Long', ], 'InsideCidrBlocks' => [ 'shape' => 'ConstrainedStringList', ], ], ], 'CoreNetworkEdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkEdge', ], ], 'CoreNetworkId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^core-network-([0-9a-f]{8,17})$', ], 'CoreNetworkPolicy' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', ], 'Alias' => [ 'shape' => 'CoreNetworkPolicyAlias', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'ChangeSetState' => [ 'shape' => 'ChangeSetState', ], 'PolicyErrors' => [ 'shape' => 'CoreNetworkPolicyErrorList', ], 'PolicyDocument' => [ 'shape' => 'CoreNetworkPolicyDocument', 'jsonvalue' => true, ], ], ], 'CoreNetworkPolicyAlias' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'LATEST', ], ], 'CoreNetworkPolicyDocument' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'CoreNetworkPolicyError' => [ 'type' => 'structure', 'required' => [ 'ErrorCode', 'Message', ], 'members' => [ 'ErrorCode' => [ 'shape' => 'ServerSideString', ], 'Message' => [ 'shape' => 'ServerSideString', ], 'Path' => [ 'shape' => 'ServerSideString', ], ], ], 'CoreNetworkPolicyErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkPolicyError', ], ], 'CoreNetworkPolicyException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'Errors' => [ 'shape' => 'CoreNetworkPolicyErrorList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CoreNetworkPolicyVersion' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', ], 'Alias' => [ 'shape' => 'CoreNetworkPolicyAlias', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'ChangeSetState' => [ 'shape' => 'ChangeSetState', ], ], ], 'CoreNetworkPolicyVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkPolicyVersion', ], ], 'CoreNetworkSegment' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ConstrainedString', ], 'EdgeLocations' => [ 'shape' => 'ExternalRegionCodeList', ], 'SharedSegments' => [ 'shape' => 'ConstrainedStringList', ], ], ], 'CoreNetworkSegmentEdgeIdentifier' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'SegmentName' => [ 'shape' => 'ConstrainedString', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], ], ], 'CoreNetworkSegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkSegment', ], ], 'CoreNetworkState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'AVAILABLE', 'DELETING', ], ], 'CoreNetworkSummary' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'CoreNetworkArn' => [ 'shape' => 'CoreNetworkArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'OwnerAccountId' => [ 'shape' => 'AWSAccountId', ], 'State' => [ 'shape' => 'CoreNetworkState', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CoreNetworkSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreNetworkSummary', ], ], 'CreateConnectAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'EdgeLocation', 'TransportAttachmentId', 'Options', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'TransportAttachmentId' => [ 'shape' => 'AttachmentId', ], 'Options' => [ 'shape' => 'ConnectAttachmentOptions', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateConnectAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectAttachment' => [ 'shape' => 'ConnectAttachment', ], ], ], 'CreateConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectAttachmentId', 'PeerAddress', ], 'members' => [ 'ConnectAttachmentId' => [ 'shape' => 'AttachmentId', ], 'CoreNetworkAddress' => [ 'shape' => 'IPAddress', ], 'PeerAddress' => [ 'shape' => 'IPAddress', ], 'BgpOptions' => [ 'shape' => 'BgpOptions', ], 'InsideCidrBlocks' => [ 'shape' => 'ConstrainedStringList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'SubnetArn' => [ 'shape' => 'SubnetArn', ], ], ], 'CreateConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeer' => [ 'shape' => 'ConnectPeer', ], ], ], 'CreateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'DeviceId', 'ConnectedDeviceId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'ConnectedDeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'ConnectedLinkId' => [ 'shape' => 'LinkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'CreateCoreNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Tags' => [ 'shape' => 'TagList', ], 'PolicyDocument' => [ 'shape' => 'CoreNetworkPolicyDocument', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateCoreNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetwork' => [ 'shape' => 'CoreNetwork', ], ], ], 'CreateDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'AWSLocation' => [ 'shape' => 'AWSLocation', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Vendor' => [ 'shape' => 'ConstrainedString', ], 'Model' => [ 'shape' => 'ConstrainedString', ], 'SerialNumber' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'Device', ], ], ], 'CreateGlobalNetworkRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'ConstrainedString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGlobalNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'GlobalNetwork' => [ 'shape' => 'GlobalNetwork', ], ], ], 'CreateLinkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'Bandwidth', 'SiteId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Bandwidth' => [ 'shape' => 'Bandwidth', ], 'Provider' => [ 'shape' => 'ConstrainedString', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLinkResponse' => [ 'type' => 'structure', 'members' => [ 'Link' => [ 'shape' => 'Link', ], ], ], 'CreateSiteRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSiteResponse' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'CreateSiteToSiteVpnAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'VpnConnectionArn', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'VpnConnectionArn' => [ 'shape' => 'VpnConnectionArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateSiteToSiteVpnAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'SiteToSiteVpnAttachment' => [ 'shape' => 'SiteToSiteVpnAttachment', ], ], ], 'CreateTransitGatewayPeeringRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'TransitGatewayArn', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateTransitGatewayPeeringResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayPeering' => [ 'shape' => 'TransitGatewayPeering', ], ], ], 'CreateTransitGatewayRouteTableAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'PeeringId', 'TransitGatewayRouteTableArn', ], 'members' => [ 'PeeringId' => [ 'shape' => 'PeeringId', ], 'TransitGatewayRouteTableArn' => [ 'shape' => 'TransitGatewayRouteTableArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateTransitGatewayRouteTableAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRouteTableAttachment' => [ 'shape' => 'TransitGatewayRouteTableAttachment', ], ], ], 'CreateVpcAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'VpcArn', 'SubnetArns', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'VpcArn' => [ 'shape' => 'VpcArn', ], 'SubnetArns' => [ 'shape' => 'SubnetArnList', ], 'Options' => [ 'shape' => 'VpcOptions', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateVpcAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'VpcAttachment' => [ 'shape' => 'VpcAttachment', ], ], ], 'CustomerGatewayArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'CustomerGatewayArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerGatewayArn', ], ], 'CustomerGatewayAssociation' => [ 'type' => 'structure', 'members' => [ 'CustomerGatewayArn' => [ 'shape' => 'CustomerGatewayArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'State' => [ 'shape' => 'CustomerGatewayAssociationState', ], ], ], 'CustomerGatewayAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerGatewayAssociation', ], ], 'CustomerGatewayAssociationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'DELETED', ], ], 'DateTime' => [ 'type' => 'timestamp', ], 'DeleteAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'DeleteAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], ], ], 'DeleteConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectPeerId', ], 'members' => [ 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', 'location' => 'uri', 'locationName' => 'connectPeerId', ], ], ], 'DeleteConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeer' => [ 'shape' => 'ConnectPeer', ], ], ], 'DeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'ConnectionId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'connectionId', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'DeleteCoreNetworkPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyVersionId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'DeleteCoreNetworkPolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkPolicy' => [ 'shape' => 'CoreNetworkPolicy', ], ], ], 'DeleteCoreNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], ], ], 'DeleteCoreNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetwork' => [ 'shape' => 'CoreNetwork', ], ], ], 'DeleteDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'DeviceId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'deviceId', ], ], ], 'DeleteDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'Device', ], ], ], 'DeleteGlobalNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], ], ], 'DeleteGlobalNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'GlobalNetwork' => [ 'shape' => 'GlobalNetwork', ], ], ], 'DeleteLinkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'LinkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'LinkId' => [ 'shape' => 'LinkId', 'location' => 'uri', 'locationName' => 'linkId', ], ], ], 'DeleteLinkResponse' => [ 'type' => 'structure', 'members' => [ 'Link' => [ 'shape' => 'Link', ], ], ], 'DeletePeeringRequest' => [ 'type' => 'structure', 'required' => [ 'PeeringId', ], 'members' => [ 'PeeringId' => [ 'shape' => 'PeeringId', 'location' => 'uri', 'locationName' => 'peeringId', ], ], ], 'DeletePeeringResponse' => [ 'type' => 'structure', 'members' => [ 'Peering' => [ 'shape' => 'Peering', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSiteRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'SiteId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'siteId', ], ], ], 'DeleteSiteResponse' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'DeregisterTransitGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'TransitGatewayArn', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', 'location' => 'uri', 'locationName' => 'transitGatewayArn', ], ], ], 'DeregisterTransitGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRegistration' => [ 'shape' => 'TransitGatewayRegistration', ], ], ], 'DescribeGlobalNetworksRequest' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkIds' => [ 'shape' => 'GlobalNetworkIdList', 'location' => 'querystring', 'locationName' => 'globalNetworkIds', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeGlobalNetworksResponse' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworks' => [ 'shape' => 'GlobalNetworkList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Device' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], 'DeviceArn' => [ 'shape' => 'DeviceArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'AWSLocation' => [ 'shape' => 'AWSLocation', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Vendor' => [ 'shape' => 'ConstrainedString', ], 'Model' => [ 'shape' => 'ConstrainedString', ], 'SerialNumber' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'DeviceState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'DeviceArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'DeviceId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'DeviceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceId', ], ], 'DeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'DeviceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'UPDATING', ], ], 'DisassociateConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'ConnectPeerId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', 'location' => 'uri', 'locationName' => 'connectPeerId', ], ], ], 'DisassociateConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeerAssociation' => [ 'shape' => 'ConnectPeerAssociation', ], ], ], 'DisassociateCustomerGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'CustomerGatewayArn', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'CustomerGatewayArn' => [ 'shape' => 'CustomerGatewayArn', 'location' => 'uri', 'locationName' => 'customerGatewayArn', ], ], ], 'DisassociateCustomerGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'CustomerGatewayAssociation' => [ 'shape' => 'CustomerGatewayAssociation', ], ], ], 'DisassociateLinkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'DeviceId', 'LinkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'deviceId', ], 'LinkId' => [ 'shape' => 'LinkId', 'location' => 'querystring', 'locationName' => 'linkId', ], ], ], 'DisassociateLinkResponse' => [ 'type' => 'structure', 'members' => [ 'LinkAssociation' => [ 'shape' => 'LinkAssociation', ], ], ], 'DisassociateTransitGatewayConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'TransitGatewayConnectPeerArn', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayConnectPeerArn' => [ 'shape' => 'TransitGatewayConnectPeerArn', 'location' => 'uri', 'locationName' => 'transitGatewayConnectPeerArn', ], ], ], 'DisassociateTransitGatewayConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayConnectPeerAssociation' => [ 'shape' => 'TransitGatewayConnectPeerAssociation', ], ], ], 'ExceptionContextKey' => [ 'type' => 'string', ], 'ExceptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExceptionContextKey', ], 'value' => [ 'shape' => 'ExceptionContextValue', ], ], 'ExceptionContextValue' => [ 'type' => 'string', ], 'ExecuteCoreNetworkChangeSetRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyVersionId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'ExecuteCoreNetworkChangeSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'ExternalRegionCode' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'ExternalRegionCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalRegionCode', ], ], 'FilterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FilterName', ], 'value' => [ 'shape' => 'FilterValues', ], ], 'FilterName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^[0-9a-zA-Z\\.-]*$', ], 'FilterValue' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?-]*$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'GetConnectAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'GetConnectAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectAttachment' => [ 'shape' => 'ConnectAttachment', ], ], ], 'GetConnectPeerAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectPeerIds' => [ 'shape' => 'ConnectPeerIdList', 'location' => 'querystring', 'locationName' => 'connectPeerIds', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetConnectPeerAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeerAssociations' => [ 'shape' => 'ConnectPeerAssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetConnectPeerRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectPeerId', ], 'members' => [ 'ConnectPeerId' => [ 'shape' => 'ConnectPeerId', 'location' => 'uri', 'locationName' => 'connectPeerId', ], ], ], 'GetConnectPeerResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeer' => [ 'shape' => 'ConnectPeer', ], ], ], 'GetConnectionsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectionIds' => [ 'shape' => 'ConnectionIdList', 'location' => 'querystring', 'locationName' => 'connectionIds', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'deviceId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'ConnectionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCoreNetworkChangeEventsRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyVersionId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'policyVersionId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetCoreNetworkChangeEventsResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkChangeEvents' => [ 'shape' => 'CoreNetworkChangeEventList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCoreNetworkChangeSetRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyVersionId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'policyVersionId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetCoreNetworkChangeSetResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkChanges' => [ 'shape' => 'CoreNetworkChangeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCoreNetworkPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'policyVersionId', ], 'Alias' => [ 'shape' => 'CoreNetworkPolicyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], ], ], 'GetCoreNetworkPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkPolicy' => [ 'shape' => 'CoreNetworkPolicy', ], ], ], 'GetCoreNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], ], ], 'GetCoreNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetwork' => [ 'shape' => 'CoreNetwork', ], ], ], 'GetCustomerGatewayAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'CustomerGatewayArns' => [ 'shape' => 'CustomerGatewayArnList', 'location' => 'querystring', 'locationName' => 'customerGatewayArns', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetCustomerGatewayAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'CustomerGatewayAssociations' => [ 'shape' => 'CustomerGatewayAssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceIds' => [ 'shape' => 'DeviceIdList', 'location' => 'querystring', 'locationName' => 'deviceIds', ], 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'querystring', 'locationName' => 'siteId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Devices' => [ 'shape' => 'DeviceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetLinkAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'deviceId', ], 'LinkId' => [ 'shape' => 'LinkId', 'location' => 'querystring', 'locationName' => 'linkId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetLinkAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'LinkAssociations' => [ 'shape' => 'LinkAssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetLinksRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'LinkIds' => [ 'shape' => 'LinkIdList', 'location' => 'querystring', 'locationName' => 'linkIds', ], 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'querystring', 'locationName' => 'siteId', ], 'Type' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'type', ], 'Provider' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'provider', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetLinksResponse' => [ 'type' => 'structure', 'members' => [ 'Links' => [ 'shape' => 'LinkList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetNetworkResourceCountsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetNetworkResourceCountsResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkResourceCounts' => [ 'shape' => 'NetworkResourceCountList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetNetworkResourceRelationshipsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'registeredGatewayArn', ], 'AwsRegion' => [ 'shape' => 'ExternalRegionCode', 'location' => 'querystring', 'locationName' => 'awsRegion', ], 'AccountId' => [ 'shape' => 'AWSAccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetNetworkResourceRelationshipsResponse' => [ 'type' => 'structure', 'members' => [ 'Relationships' => [ 'shape' => 'RelationshipList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetNetworkResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'registeredGatewayArn', ], 'AwsRegion' => [ 'shape' => 'ExternalRegionCode', 'location' => 'querystring', 'locationName' => 'awsRegion', ], 'AccountId' => [ 'shape' => 'AWSAccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetNetworkResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkResources' => [ 'shape' => 'NetworkResourceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetNetworkRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'RouteTableIdentifier', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'RouteTableIdentifier' => [ 'shape' => 'RouteTableIdentifier', ], 'ExactCidrMatches' => [ 'shape' => 'ConstrainedStringList', ], 'LongestPrefixMatches' => [ 'shape' => 'ConstrainedStringList', ], 'SubnetOfMatches' => [ 'shape' => 'ConstrainedStringList', ], 'SupernetOfMatches' => [ 'shape' => 'ConstrainedStringList', ], 'PrefixListIds' => [ 'shape' => 'ConstrainedStringList', ], 'States' => [ 'shape' => 'RouteStateList', ], 'Types' => [ 'shape' => 'RouteTypeList', ], 'DestinationFilters' => [ 'shape' => 'FilterMap', ], ], ], 'GetNetworkRoutesResponse' => [ 'type' => 'structure', 'members' => [ 'RouteTableArn' => [ 'shape' => 'ResourceArn', ], 'CoreNetworkSegmentEdge' => [ 'shape' => 'CoreNetworkSegmentEdgeIdentifier', ], 'RouteTableType' => [ 'shape' => 'RouteTableType', ], 'RouteTableTimestamp' => [ 'shape' => 'DateTime', ], 'NetworkRoutes' => [ 'shape' => 'NetworkRouteList', ], ], ], 'GetNetworkTelemetryRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'registeredGatewayArn', ], 'AwsRegion' => [ 'shape' => 'ExternalRegionCode', 'location' => 'querystring', 'locationName' => 'awsRegion', ], 'AccountId' => [ 'shape' => 'AWSAccountId', 'location' => 'querystring', 'locationName' => 'accountId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetNetworkTelemetryResponse' => [ 'type' => 'structure', 'members' => [ 'NetworkTelemetry' => [ 'shape' => 'NetworkTelemetryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyDocument' => [ 'shape' => 'ResourcePolicyDocument', 'jsonvalue' => true, ], ], ], 'GetRouteAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'RouteAnalysisId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'RouteAnalysisId' => [ 'shape' => 'ConstrainedString', 'location' => 'uri', 'locationName' => 'routeAnalysisId', ], ], ], 'GetRouteAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'RouteAnalysis' => [ 'shape' => 'RouteAnalysis', ], ], ], 'GetSiteToSiteVpnAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'GetSiteToSiteVpnAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'SiteToSiteVpnAttachment' => [ 'shape' => 'SiteToSiteVpnAttachment', ], ], ], 'GetSitesRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'SiteIds' => [ 'shape' => 'SiteIdList', 'location' => 'querystring', 'locationName' => 'siteIds', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetSitesResponse' => [ 'type' => 'structure', 'members' => [ 'Sites' => [ 'shape' => 'SiteList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetTransitGatewayConnectPeerAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayConnectPeerArns' => [ 'shape' => 'TransitGatewayConnectPeerArnList', 'location' => 'querystring', 'locationName' => 'transitGatewayConnectPeerArns', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetTransitGatewayConnectPeerAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayConnectPeerAssociations' => [ 'shape' => 'TransitGatewayConnectPeerAssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetTransitGatewayPeeringRequest' => [ 'type' => 'structure', 'required' => [ 'PeeringId', ], 'members' => [ 'PeeringId' => [ 'shape' => 'PeeringId', 'location' => 'uri', 'locationName' => 'peeringId', ], ], ], 'GetTransitGatewayPeeringResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayPeering' => [ 'shape' => 'TransitGatewayPeering', ], ], ], 'GetTransitGatewayRegistrationsRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayArns' => [ 'shape' => 'TransitGatewayArnList', 'location' => 'querystring', 'locationName' => 'transitGatewayArns', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetTransitGatewayRegistrationsResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRegistrations' => [ 'shape' => 'TransitGatewayRegistrationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetTransitGatewayRouteTableAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'GetTransitGatewayRouteTableAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRouteTableAttachment' => [ 'shape' => 'TransitGatewayRouteTableAttachment', ], ], ], 'GetVpcAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'GetVpcAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'VpcAttachment' => [ 'shape' => 'VpcAttachment', ], ], ], 'GlobalNetwork' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'GlobalNetworkArn' => [ 'shape' => 'GlobalNetworkArn', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'GlobalNetworkState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'GlobalNetworkArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'GlobalNetworkId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'GlobalNetworkIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalNetworkId', ], ], 'GlobalNetworkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalNetwork', ], ], 'GlobalNetworkState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'UPDATING', ], ], 'IPAddress' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Link' => [ 'type' => 'structure', 'members' => [ 'LinkId' => [ 'shape' => 'LinkId', ], 'LinkArn' => [ 'shape' => 'LinkArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Bandwidth' => [ 'shape' => 'Bandwidth', ], 'Provider' => [ 'shape' => 'ConstrainedString', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'LinkState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LinkArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'LinkAssociation' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'LinkAssociationState' => [ 'shape' => 'LinkAssociationState', ], ], ], 'LinkAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinkAssociation', ], ], 'LinkAssociationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'DELETED', ], ], 'LinkId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'LinkIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinkId', ], ], 'LinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Link', ], ], 'LinkState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'UPDATING', ], ], 'ListAttachmentsRequest' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'AttachmentType' => [ 'shape' => 'AttachmentType', 'location' => 'querystring', 'locationName' => 'attachmentType', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', 'location' => 'querystring', 'locationName' => 'edgeLocation', ], 'State' => [ 'shape' => 'AttachmentState', 'location' => 'querystring', 'locationName' => 'state', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAttachmentsResponse' => [ 'type' => 'structure', 'members' => [ 'Attachments' => [ 'shape' => 'AttachmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectPeersRequest' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'ConnectAttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'querystring', 'locationName' => 'connectAttachmentId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListConnectPeersResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectPeers' => [ 'shape' => 'ConnectPeerSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoreNetworkPolicyVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListCoreNetworkPolicyVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkPolicyVersions' => [ 'shape' => 'CoreNetworkPolicyVersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCoreNetworksRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListCoreNetworksResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworks' => [ 'shape' => 'CoreNetworkSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOrganizationServiceAccessStatusRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListOrganizationServiceAccessStatusResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationStatus' => [ 'shape' => 'OrganizationStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPeeringsRequest' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'querystring', 'locationName' => 'coreNetworkId', ], 'PeeringType' => [ 'shape' => 'PeeringType', 'location' => 'querystring', 'locationName' => 'peeringType', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', 'location' => 'querystring', 'locationName' => 'edgeLocation', ], 'State' => [ 'shape' => 'PeeringState', 'location' => 'querystring', 'locationName' => 'state', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPeeringsResponse' => [ 'type' => 'structure', 'members' => [ 'Peerings' => [ 'shape' => 'PeeringList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'ConstrainedString', ], 'Latitude' => [ 'shape' => 'ConstrainedString', ], 'Longitude' => [ 'shape' => 'ConstrainedString', ], ], 'sensitive' => true, ], 'Long' => [ 'type' => 'long', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'NetworkResource' => [ 'type' => 'structure', 'members' => [ 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'AwsRegion' => [ 'shape' => 'ExternalRegionCode', ], 'AccountId' => [ 'shape' => 'AWSAccountId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', ], 'ResourceId' => [ 'shape' => 'ConstrainedString', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Definition' => [ 'shape' => 'ConstrainedString', ], 'DefinitionTimestamp' => [ 'shape' => 'DateTime', ], 'Tags' => [ 'shape' => 'TagList', ], 'Metadata' => [ 'shape' => 'NetworkResourceMetadataMap', ], ], ], 'NetworkResourceCount' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ConstrainedString', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'NetworkResourceCountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkResourceCount', ], ], 'NetworkResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkResource', ], ], 'NetworkResourceMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConstrainedString', ], 'value' => [ 'shape' => 'ConstrainedString', ], ], 'NetworkResourceSummary' => [ 'type' => 'structure', 'members' => [ 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', ], 'Definition' => [ 'shape' => 'ConstrainedString', ], 'NameTag' => [ 'shape' => 'ConstrainedString', ], 'IsMiddlebox' => [ 'shape' => 'Boolean', ], ], ], 'NetworkRoute' => [ 'type' => 'structure', 'members' => [ 'DestinationCidrBlock' => [ 'shape' => 'ConstrainedString', ], 'Destinations' => [ 'shape' => 'NetworkRouteDestinationList', ], 'PrefixListId' => [ 'shape' => 'ConstrainedString', ], 'State' => [ 'shape' => 'RouteState', ], 'Type' => [ 'shape' => 'RouteType', ], ], ], 'NetworkRouteDestination' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkAttachmentId' => [ 'shape' => 'AttachmentId', ], 'TransitGatewayAttachmentId' => [ 'shape' => 'TransitGatewayAttachmentId', ], 'SegmentName' => [ 'shape' => 'ConstrainedString', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', ], 'ResourceId' => [ 'shape' => 'ConstrainedString', ], ], ], 'NetworkRouteDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkRouteDestination', ], ], 'NetworkRouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkRoute', ], ], 'NetworkTelemetry' => [ 'type' => 'structure', 'members' => [ 'RegisteredGatewayArn' => [ 'shape' => 'ResourceArn', ], 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'AwsRegion' => [ 'shape' => 'ExternalRegionCode', ], 'AccountId' => [ 'shape' => 'AWSAccountId', ], 'ResourceType' => [ 'shape' => 'ConstrainedString', ], 'ResourceId' => [ 'shape' => 'ConstrainedString', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Address' => [ 'shape' => 'ConstrainedString', ], 'Health' => [ 'shape' => 'ConnectionHealth', ], ], ], 'NetworkTelemetryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkTelemetry', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'OrganizationAwsServiceAccessStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'OrganizationId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^o-([0-9a-f]{8,17})$', ], 'OrganizationStatus' => [ 'type' => 'structure', 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'OrganizationAwsServiceAccessStatus' => [ 'shape' => 'OrganizationAwsServiceAccessStatus', ], 'SLRDeploymentStatus' => [ 'shape' => 'SLRDeploymentStatus', ], 'AccountStatusList' => [ 'shape' => 'AccountStatusList', ], ], ], 'PathComponent' => [ 'type' => 'structure', 'members' => [ 'Sequence' => [ 'shape' => 'Integer', ], 'Resource' => [ 'shape' => 'NetworkResourceSummary', ], 'DestinationCidrBlock' => [ 'shape' => 'ConstrainedString', ], ], ], 'PathComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathComponent', ], ], 'Peering' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', ], 'CoreNetworkArn' => [ 'shape' => 'CoreNetworkArn', ], 'PeeringId' => [ 'shape' => 'PeeringId', ], 'OwnerAccountId' => [ 'shape' => 'AWSAccountId', ], 'PeeringType' => [ 'shape' => 'PeeringType', ], 'State' => [ 'shape' => 'PeeringState', ], 'EdgeLocation' => [ 'shape' => 'ExternalRegionCode', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], ], ], 'PeeringId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^peering-([0-9a-f]{8,17})$', ], 'PeeringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Peering', ], ], 'PeeringState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'FAILED', 'AVAILABLE', 'DELETING', ], ], 'PeeringType' => [ 'type' => 'string', 'enum' => [ 'TRANSIT_GATEWAY', ], ], 'ProposedSegmentChange' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'AttachmentPolicyRuleNumber' => [ 'shape' => 'Integer', ], 'SegmentName' => [ 'shape' => 'ConstrainedString', ], ], ], 'PutCoreNetworkPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyDocument', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyDocument' => [ 'shape' => 'CoreNetworkPolicyDocument', 'jsonvalue' => true, ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'LatestVersionId' => [ 'shape' => 'Integer', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'PutCoreNetworkPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkPolicy' => [ 'shape' => 'CoreNetworkPolicy', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyDocument', 'ResourceArn', ], 'members' => [ 'PolicyDocument' => [ 'shape' => 'ResourcePolicyDocument', 'jsonvalue' => true, ], 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReasonContextKey' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ReasonContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ReasonContextKey', ], 'value' => [ 'shape' => 'ReasonContextValue', ], ], 'ReasonContextValue' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'RegisterTransitGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'TransitGatewayArn', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', ], ], ], 'RegisterTransitGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRegistration' => [ 'shape' => 'TransitGatewayRegistration', ], ], ], 'RejectAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'RejectAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], ], ], 'Relationship' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'ConstrainedString', ], 'To' => [ 'shape' => 'ConstrainedString', ], ], ], 'RelationshipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Relationship', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'ResourceId' => [ 'shape' => 'ServerSideString', ], 'ResourceType' => [ 'shape' => 'ServerSideString', ], 'Context' => [ 'shape' => 'ExceptionContextMap', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePolicyDocument' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'RestoreCoreNetworkPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', 'PolicyVersionId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'PolicyVersionId' => [ 'shape' => 'Integer', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'RestoreCoreNetworkPolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetworkPolicy' => [ 'shape' => 'CoreNetworkPolicy', ], ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RouteAnalysis' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'OwnerAccountId' => [ 'shape' => 'AWSAccountId', ], 'RouteAnalysisId' => [ 'shape' => 'ConstrainedString', ], 'StartTimestamp' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'RouteAnalysisStatus', ], 'Source' => [ 'shape' => 'RouteAnalysisEndpointOptions', ], 'Destination' => [ 'shape' => 'RouteAnalysisEndpointOptions', ], 'IncludeReturnPath' => [ 'shape' => 'Boolean', ], 'UseMiddleboxes' => [ 'shape' => 'Boolean', ], 'ForwardPath' => [ 'shape' => 'RouteAnalysisPath', ], 'ReturnPath' => [ 'shape' => 'RouteAnalysisPath', ], ], ], 'RouteAnalysisCompletion' => [ 'type' => 'structure', 'members' => [ 'ResultCode' => [ 'shape' => 'RouteAnalysisCompletionResultCode', ], 'ReasonCode' => [ 'shape' => 'RouteAnalysisCompletionReasonCode', ], 'ReasonContext' => [ 'shape' => 'ReasonContextMap', ], ], ], 'RouteAnalysisCompletionReasonCode' => [ 'type' => 'string', 'enum' => [ 'TRANSIT_GATEWAY_ATTACHMENT_NOT_FOUND', 'TRANSIT_GATEWAY_ATTACHMENT_NOT_IN_TRANSIT_GATEWAY', 'CYCLIC_PATH_DETECTED', 'TRANSIT_GATEWAY_ATTACHMENT_STABLE_ROUTE_TABLE_NOT_FOUND', 'ROUTE_NOT_FOUND', 'BLACKHOLE_ROUTE_FOR_DESTINATION_FOUND', 'INACTIVE_ROUTE_FOR_DESTINATION_FOUND', 'TRANSIT_GATEWAY_ATTACHMENT_ATTACH_ARN_NO_MATCH', 'MAX_HOPS_EXCEEDED', 'POSSIBLE_MIDDLEBOX', 'NO_DESTINATION_ARN_PROVIDED', ], ], 'RouteAnalysisCompletionResultCode' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'NOT_CONNECTED', ], ], 'RouteAnalysisEndpointOptions' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayAttachmentArn' => [ 'shape' => 'TransitGatewayAttachmentArn', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', ], 'IpAddress' => [ 'shape' => 'IPAddress', ], ], ], 'RouteAnalysisEndpointOptionsSpecification' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayAttachmentArn' => [ 'shape' => 'TransitGatewayAttachmentArn', ], 'IpAddress' => [ 'shape' => 'IPAddress', ], ], ], 'RouteAnalysisPath' => [ 'type' => 'structure', 'members' => [ 'CompletionStatus' => [ 'shape' => 'RouteAnalysisCompletion', ], 'Path' => [ 'shape' => 'PathComponentList', ], ], ], 'RouteAnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'FAILED', ], ], 'RouteState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'BLACKHOLE', ], ], 'RouteStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteState', ], ], 'RouteTableIdentifier' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayRouteTableArn' => [ 'shape' => 'TransitGatewayRouteTableArn', ], 'CoreNetworkSegmentEdge' => [ 'shape' => 'CoreNetworkSegmentEdgeIdentifier', ], ], ], 'RouteTableType' => [ 'type' => 'string', 'enum' => [ 'TRANSIT_GATEWAY_ROUTE_TABLE', 'CORE_NETWORK_SEGMENT', ], ], 'RouteType' => [ 'type' => 'string', 'enum' => [ 'PROPAGATED', 'STATIC', ], ], 'RouteTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteType', ], ], 'SLRDeploymentStatus' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'ServerSideString' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'LimitCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'ResourceId' => [ 'shape' => 'ServerSideString', ], 'ResourceType' => [ 'shape' => 'ServerSideString', ], 'LimitCode' => [ 'shape' => 'ServerSideString', ], 'ServiceCode' => [ 'shape' => 'ServerSideString', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'Site' => [ 'type' => 'structure', 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', ], 'SiteArn' => [ 'shape' => 'SiteArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'State' => [ 'shape' => 'SiteState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'SiteArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'SiteId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'SiteIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SiteId', ], ], 'SiteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Site', ], ], 'SiteState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'UPDATING', ], ], 'SiteToSiteVpnAttachment' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], 'VpnConnectionArn' => [ 'shape' => 'VpnConnectionArn', ], ], ], 'StartOrganizationServiceAccessUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'Action', ], ], ], 'StartOrganizationServiceAccessUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationStatus' => [ 'shape' => 'OrganizationStatus', ], ], ], 'StartRouteAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'Source', 'Destination', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'Source' => [ 'shape' => 'RouteAnalysisEndpointOptionsSpecification', ], 'Destination' => [ 'shape' => 'RouteAnalysisEndpointOptionsSpecification', ], 'IncludeReturnPath' => [ 'shape' => 'Boolean', ], 'UseMiddleboxes' => [ 'shape' => 'Boolean', ], ], ], 'StartRouteAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'RouteAnalysis' => [ 'shape' => 'RouteAnalysis', ], ], ], 'SubnetArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:subnet\\/subnet-[0-9a-f]{8,17}$|^$', ], 'SubnetArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetArn', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 10000000, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TransitGatewayArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TransitGatewayArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransitGatewayArn', ], ], 'TransitGatewayAttachmentArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TransitGatewayAttachmentId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TransitGatewayConnectPeerArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TransitGatewayConnectPeerArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransitGatewayConnectPeerArn', ], ], 'TransitGatewayConnectPeerAssociation' => [ 'type' => 'structure', 'members' => [ 'TransitGatewayConnectPeerArn' => [ 'shape' => 'TransitGatewayConnectPeerArn', ], 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'State' => [ 'shape' => 'TransitGatewayConnectPeerAssociationState', ], ], ], 'TransitGatewayConnectPeerAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransitGatewayConnectPeerAssociation', ], ], 'TransitGatewayConnectPeerAssociationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'DELETED', ], ], 'TransitGatewayPeering' => [ 'type' => 'structure', 'members' => [ 'Peering' => [ 'shape' => 'Peering', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', ], 'TransitGatewayPeeringAttachmentId' => [ 'shape' => 'TransitGatewayPeeringAttachmentId', ], ], ], 'TransitGatewayPeeringAttachmentId' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^tgw-attach-([0-9a-f]{8,17})$', ], 'TransitGatewayRegistration' => [ 'type' => 'structure', 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', ], 'TransitGatewayArn' => [ 'shape' => 'TransitGatewayArn', ], 'State' => [ 'shape' => 'TransitGatewayRegistrationStateReason', ], ], ], 'TransitGatewayRegistrationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransitGatewayRegistration', ], ], 'TransitGatewayRegistrationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'DELETING', 'DELETED', 'FAILED', ], ], 'TransitGatewayRegistrationStateReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'TransitGatewayRegistrationState', ], 'Message' => [ 'shape' => 'ConstrainedString', ], ], ], 'TransitGatewayRouteTableArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TransitGatewayRouteTableAttachment' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], 'PeeringId' => [ 'shape' => 'PeeringId', ], 'TransitGatewayRouteTableArn' => [ 'shape' => 'TransitGatewayRouteTableArn', ], ], ], 'TunnelProtocol' => [ 'type' => 'string', 'enum' => [ 'GRE', 'NO_ENCAP', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'ConnectionId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'connectionId', ], 'LinkId' => [ 'shape' => 'LinkId', ], 'ConnectedLinkId' => [ 'shape' => 'LinkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], ], ], 'UpdateConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'UpdateCoreNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'CoreNetworkId', ], 'members' => [ 'CoreNetworkId' => [ 'shape' => 'CoreNetworkId', 'location' => 'uri', 'locationName' => 'coreNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], ], ], 'UpdateCoreNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'CoreNetwork' => [ 'shape' => 'CoreNetwork', ], ], ], 'UpdateDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'DeviceId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'deviceId', ], 'AWSLocation' => [ 'shape' => 'AWSLocation', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Vendor' => [ 'shape' => 'ConstrainedString', ], 'Model' => [ 'shape' => 'ConstrainedString', ], 'SerialNumber' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], 'SiteId' => [ 'shape' => 'SiteId', ], ], ], 'UpdateDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'Device', ], ], ], 'UpdateGlobalNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], ], ], 'UpdateGlobalNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'GlobalNetwork' => [ 'shape' => 'GlobalNetwork', ], ], ], 'UpdateLinkRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'LinkId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'LinkId' => [ 'shape' => 'LinkId', 'location' => 'uri', 'locationName' => 'linkId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Type' => [ 'shape' => 'ConstrainedString', ], 'Bandwidth' => [ 'shape' => 'Bandwidth', ], 'Provider' => [ 'shape' => 'ConstrainedString', ], ], ], 'UpdateLinkResponse' => [ 'type' => 'structure', 'members' => [ 'Link' => [ 'shape' => 'Link', ], ], ], 'UpdateNetworkResourceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'ResourceArn', 'Metadata', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Metadata' => [ 'shape' => 'NetworkResourceMetadataMap', ], ], ], 'UpdateNetworkResourceMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Metadata' => [ 'shape' => 'NetworkResourceMetadataMap', ], ], ], 'UpdateSiteRequest' => [ 'type' => 'structure', 'required' => [ 'GlobalNetworkId', 'SiteId', ], 'members' => [ 'GlobalNetworkId' => [ 'shape' => 'GlobalNetworkId', 'location' => 'uri', 'locationName' => 'globalNetworkId', ], 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'siteId', ], 'Description' => [ 'shape' => 'ConstrainedString', ], 'Location' => [ 'shape' => 'Location', ], ], ], 'UpdateSiteResponse' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'UpdateVpcAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'AttachmentId', ], 'members' => [ 'AttachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], 'AddSubnetArns' => [ 'shape' => 'SubnetArnList', ], 'RemoveSubnetArns' => [ 'shape' => 'SubnetArnList', ], 'Options' => [ 'shape' => 'VpcOptions', ], ], ], 'UpdateVpcAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'VpcAttachment' => [ 'shape' => 'VpcAttachment', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ServerSideString', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'ServerSideString', ], 'Message' => [ 'shape' => 'ServerSideString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'CannotParse', 'FieldValidationFailed', 'Other', ], ], 'VpcArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:vpc\\/vpc-[0-9a-f]{8,17}$', ], 'VpcAttachment' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], 'SubnetArns' => [ 'shape' => 'SubnetArnList', ], 'Options' => [ 'shape' => 'VpcOptions', ], ], ], 'VpcOptions' => [ 'type' => 'structure', 'members' => [ 'Ipv6Support' => [ 'shape' => 'Boolean', ], 'ApplianceModeSupport' => [ 'shape' => 'Boolean', ], ], ], 'VpnConnectionArn' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:vpn-connection\\/vpn-[0-9a-f]{8,17}$', ], ],];
