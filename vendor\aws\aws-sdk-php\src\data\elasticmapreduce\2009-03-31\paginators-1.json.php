<?php
// This file was auto-generated from sdk-root/src/data/elasticmapreduce/2009-03-31/paginators-1.json
return [ 'pagination' => [ 'DescribeJobFlows' => [ 'result_key' => 'JobFlows', ], 'ListBootstrapActions' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'BootstrapActions', ], 'ListClusters' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'Clusters', ], 'ListInstanceFleets' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'InstanceFleets', ], 'ListInstanceGroups' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'InstanceGroups', ], 'ListInstances' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'Instances', ], 'ListNotebookExecutions' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'NotebookExecutions', ], 'ListReleaseLabels' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListSecurityConfigurations' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'SecurityConfigurations', ], 'ListSteps' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'Steps', ], 'ListStudioSessionMappings' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'SessionMappings', ], 'ListStudios' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'result_key' => 'Studios', ], 'ListSupportedInstanceTypes' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', ], ],];
