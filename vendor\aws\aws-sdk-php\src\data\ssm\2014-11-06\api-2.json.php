<?php
// This file was auto-generated from sdk-root/src/data/ssm/2014-11-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-11-06', 'endpointPrefix' => 'ssm', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Amazon SSM', 'serviceFullName' => 'Amazon Simple Systems Manager (SSM)', 'serviceId' => 'SSM', 'signatureVersion' => 'v4', 'targetPrefix' => 'AmazonSSM', 'uid' => 'ssm-2014-11-06', ], 'operations' => [ 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceRequest', ], 'output' => [ 'shape' => 'AddTagsToResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceType', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TooManyTagsError', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'AssociateOpsItemRelatedItem' => [ 'name' => 'AssociateOpsItemRelatedItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateOpsItemRelatedItemRequest', ], 'output' => [ 'shape' => 'AssociateOpsItemRelatedItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemNotFoundException', ], [ 'shape' => 'OpsItemLimitExceededException', ], [ 'shape' => 'OpsItemInvalidParameterException', ], [ 'shape' => 'OpsItemRelatedItemAlreadyExistsException', ], [ 'shape' => 'OpsItemConflictException', ], ], ], 'CancelCommand' => [ 'name' => 'CancelCommand', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelCommandRequest', ], 'output' => [ 'shape' => 'CancelCommandResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidCommandId', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'DuplicateInstanceId', ], ], ], 'CancelMaintenanceWindowExecution' => [ 'name' => 'CancelMaintenanceWindowExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelMaintenanceWindowExecutionRequest', ], 'output' => [ 'shape' => 'CancelMaintenanceWindowExecutionResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'DoesNotExistException', ], ], ], 'CreateActivation' => [ 'name' => 'CreateActivation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateActivationRequest', ], 'output' => [ 'shape' => 'CreateActivationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameters', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateAssociation' => [ 'name' => 'CreateAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAssociationRequest', ], 'output' => [ 'shape' => 'CreateAssociationResult', ], 'errors' => [ [ 'shape' => 'AssociationAlreadyExists', ], [ 'shape' => 'AssociationLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'UnsupportedPlatformType', ], [ 'shape' => 'InvalidOutputLocation', ], [ 'shape' => 'InvalidParameters', ], [ 'shape' => 'InvalidTarget', ], [ 'shape' => 'InvalidSchedule', ], [ 'shape' => 'InvalidTargetMaps', ], [ 'shape' => 'InvalidTag', ], ], ], 'CreateAssociationBatch' => [ 'name' => 'CreateAssociationBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAssociationBatchRequest', ], 'output' => [ 'shape' => 'CreateAssociationBatchResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidParameters', ], [ 'shape' => 'DuplicateInstanceId', ], [ 'shape' => 'AssociationLimitExceeded', ], [ 'shape' => 'UnsupportedPlatformType', ], [ 'shape' => 'InvalidOutputLocation', ], [ 'shape' => 'InvalidTarget', ], [ 'shape' => 'InvalidSchedule', ], [ 'shape' => 'InvalidTargetMaps', ], ], ], 'CreateDocument' => [ 'name' => 'CreateDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDocumentRequest', ], 'output' => [ 'shape' => 'CreateDocumentResult', ], 'errors' => [ [ 'shape' => 'DocumentAlreadyExists', ], [ 'shape' => 'MaxDocumentSizeExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocumentContent', ], [ 'shape' => 'DocumentLimitExceeded', ], [ 'shape' => 'InvalidDocumentSchemaVersion', ], ], ], 'CreateMaintenanceWindow' => [ 'name' => 'CreateMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'CreateMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateOpsItem' => [ 'name' => 'CreateOpsItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOpsItemRequest', ], 'output' => [ 'shape' => 'CreateOpsItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemAlreadyExistsException', ], [ 'shape' => 'OpsItemLimitExceededException', ], [ 'shape' => 'OpsItemInvalidParameterException', ], [ 'shape' => 'OpsItemAccessDeniedException', ], ], ], 'CreateOpsMetadata' => [ 'name' => 'CreateOpsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOpsMetadataRequest', ], 'output' => [ 'shape' => 'CreateOpsMetadataResult', ], 'errors' => [ [ 'shape' => 'OpsMetadataAlreadyExistsException', ], [ 'shape' => 'OpsMetadataTooManyUpdatesException', ], [ 'shape' => 'OpsMetadataInvalidArgumentException', ], [ 'shape' => 'OpsMetadataLimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreatePatchBaseline' => [ 'name' => 'CreatePatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePatchBaselineRequest', ], 'output' => [ 'shape' => 'CreatePatchBaselineResult', ], 'errors' => [ [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'CreateResourceDataSync' => [ 'name' => 'CreateResourceDataSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceDataSyncRequest', ], 'output' => [ 'shape' => 'CreateResourceDataSyncResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceDataSyncCountExceededException', ], [ 'shape' => 'ResourceDataSyncAlreadyExistsException', ], [ 'shape' => 'ResourceDataSyncInvalidConfigurationException', ], ], ], 'DeleteActivation' => [ 'name' => 'DeleteActivation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteActivationRequest', ], 'output' => [ 'shape' => 'DeleteActivationResult', ], 'errors' => [ [ 'shape' => 'InvalidActivationId', ], [ 'shape' => 'InvalidActivation', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'DeleteAssociation' => [ 'name' => 'DeleteAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAssociationRequest', ], 'output' => [ 'shape' => 'DeleteAssociationResult', ], 'errors' => [ [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'DeleteDocument' => [ 'name' => 'DeleteDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDocumentRequest', ], 'output' => [ 'shape' => 'DeleteDocumentResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentOperation', ], [ 'shape' => 'AssociatedInstances', ], ], ], 'DeleteInventory' => [ 'name' => 'DeleteInventory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInventoryRequest', ], 'output' => [ 'shape' => 'DeleteInventoryResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidOptionException', ], [ 'shape' => 'InvalidDeleteInventoryParametersException', ], [ 'shape' => 'InvalidInventoryRequestException', ], ], ], 'DeleteMaintenanceWindow' => [ 'name' => 'DeleteMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'DeleteMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DeleteOpsItem' => [ 'name' => 'DeleteOpsItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOpsItemRequest', ], 'output' => [ 'shape' => 'DeleteOpsItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemInvalidParameterException', ], ], ], 'DeleteOpsMetadata' => [ 'name' => 'DeleteOpsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOpsMetadataRequest', ], 'output' => [ 'shape' => 'DeleteOpsMetadataResult', ], 'errors' => [ [ 'shape' => 'OpsMetadataNotFoundException', ], [ 'shape' => 'OpsMetadataInvalidArgumentException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteParameter' => [ 'name' => 'DeleteParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteParameterRequest', ], 'output' => [ 'shape' => 'DeleteParameterResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ParameterNotFound', ], ], ], 'DeleteParameters' => [ 'name' => 'DeleteParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteParametersRequest', ], 'output' => [ 'shape' => 'DeleteParametersResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DeletePatchBaseline' => [ 'name' => 'DeletePatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePatchBaselineRequest', ], 'output' => [ 'shape' => 'DeletePatchBaselineResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeleteResourceDataSync' => [ 'name' => 'DeleteResourceDataSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceDataSyncRequest', ], 'output' => [ 'shape' => 'DeleteResourceDataSyncResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceDataSyncNotFoundException', ], [ 'shape' => 'ResourceDataSyncInvalidConfigurationException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourcePolicyInvalidParameterException', ], [ 'shape' => 'ResourcePolicyConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MalformedResourcePolicyDocumentException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], ], ], 'DeregisterManagedInstance' => [ 'name' => 'DeregisterManagedInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterManagedInstanceRequest', ], 'output' => [ 'shape' => 'DeregisterManagedInstanceResult', ], 'errors' => [ [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeregisterPatchBaselineForPatchGroup' => [ 'name' => 'DeregisterPatchBaselineForPatchGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterPatchBaselineForPatchGroupRequest', ], 'output' => [ 'shape' => 'DeregisterPatchBaselineForPatchGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], ], ], 'DeregisterTargetFromMaintenanceWindow' => [ 'name' => 'DeregisterTargetFromMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterTargetFromMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'DeregisterTargetFromMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TargetInUseException', ], ], ], 'DeregisterTaskFromMaintenanceWindow' => [ 'name' => 'DeregisterTaskFromMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterTaskFromMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'DeregisterTaskFromMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeActivations' => [ 'name' => 'DescribeActivations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeActivationsRequest', ], 'output' => [ 'shape' => 'DescribeActivationsResult', ], 'errors' => [ [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeAssociation' => [ 'name' => 'DescribeAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssociationRequest', ], 'output' => [ 'shape' => 'DescribeAssociationResult', ], 'errors' => [ [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'InvalidAssociationVersion', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidInstanceId', ], ], ], 'DescribeAssociationExecutionTargets' => [ 'name' => 'DescribeAssociationExecutionTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssociationExecutionTargetsRequest', ], 'output' => [ 'shape' => 'DescribeAssociationExecutionTargetsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'AssociationExecutionDoesNotExist', ], ], ], 'DescribeAssociationExecutions' => [ 'name' => 'DescribeAssociationExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssociationExecutionsRequest', ], 'output' => [ 'shape' => 'DescribeAssociationExecutionsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeAutomationExecutions' => [ 'name' => 'DescribeAutomationExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAutomationExecutionsRequest', ], 'output' => [ 'shape' => 'DescribeAutomationExecutionsResult', ], 'errors' => [ [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidFilterValue', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeAutomationStepExecutions' => [ 'name' => 'DescribeAutomationStepExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAutomationStepExecutionsRequest', ], 'output' => [ 'shape' => 'DescribeAutomationStepExecutionsResult', ], 'errors' => [ [ 'shape' => 'AutomationExecutionNotFoundException', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidFilterValue', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeAvailablePatches' => [ 'name' => 'DescribeAvailablePatches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAvailablePatchesRequest', ], 'output' => [ 'shape' => 'DescribeAvailablePatchesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeDocument' => [ 'name' => 'DescribeDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDocumentRequest', ], 'output' => [ 'shape' => 'DescribeDocumentResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], ], ], 'DescribeDocumentPermission' => [ 'name' => 'DescribeDocumentPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDocumentPermissionRequest', ], 'output' => [ 'shape' => 'DescribeDocumentPermissionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidPermissionType', ], [ 'shape' => 'InvalidDocumentOperation', ], ], ], 'DescribeEffectiveInstanceAssociations' => [ 'name' => 'DescribeEffectiveInstanceAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEffectiveInstanceAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeEffectiveInstanceAssociationsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeEffectivePatchesForPatchBaseline' => [ 'name' => 'DescribeEffectivePatchesForPatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEffectivePatchesForPatchBaselineRequest', ], 'output' => [ 'shape' => 'DescribeEffectivePatchesForPatchBaselineResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'UnsupportedOperatingSystem', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeInstanceAssociationsStatus' => [ 'name' => 'DescribeInstanceAssociationsStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceAssociationsStatusRequest', ], 'output' => [ 'shape' => 'DescribeInstanceAssociationsStatusResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeInstanceInformation' => [ 'name' => 'DescribeInstanceInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceInformationRequest', ], 'output' => [ 'shape' => 'DescribeInstanceInformationResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidInstanceInformationFilterValue', ], [ 'shape' => 'InvalidFilterKey', ], ], ], 'DescribeInstancePatchStates' => [ 'name' => 'DescribeInstancePatchStates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstancePatchStatesRequest', ], 'output' => [ 'shape' => 'DescribeInstancePatchStatesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeInstancePatchStatesForPatchGroup' => [ 'name' => 'DescribeInstancePatchStatesForPatchGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstancePatchStatesForPatchGroupRequest', ], 'output' => [ 'shape' => 'DescribeInstancePatchStatesForPatchGroupResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeInstancePatches' => [ 'name' => 'DescribeInstancePatches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstancePatchesRequest', ], 'output' => [ 'shape' => 'DescribeInstancePatchesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeInventoryDeletions' => [ 'name' => 'DescribeInventoryDeletions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInventoryDeletionsRequest', ], 'output' => [ 'shape' => 'DescribeInventoryDeletionsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDeletionIdException', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeMaintenanceWindowExecutionTaskInvocations' => [ 'name' => 'DescribeMaintenanceWindowExecutionTaskInvocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowExecutionTaskInvocationsRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowExecutionTaskInvocationsResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindowExecutionTasks' => [ 'name' => 'DescribeMaintenanceWindowExecutionTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowExecutionTasksRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowExecutionTasksResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindowExecutions' => [ 'name' => 'DescribeMaintenanceWindowExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowExecutionsRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowExecutionsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindowSchedule' => [ 'name' => 'DescribeMaintenanceWindowSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowScheduleRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowScheduleResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'DoesNotExistException', ], ], ], 'DescribeMaintenanceWindowTargets' => [ 'name' => 'DescribeMaintenanceWindowTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowTargetsRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowTargetsResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindowTasks' => [ 'name' => 'DescribeMaintenanceWindowTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowTasksRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowTasksResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindows' => [ 'name' => 'DescribeMaintenanceWindows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowsRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeMaintenanceWindowsForTarget' => [ 'name' => 'DescribeMaintenanceWindowsForTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMaintenanceWindowsForTargetRequest', ], 'output' => [ 'shape' => 'DescribeMaintenanceWindowsForTargetResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeOpsItems' => [ 'name' => 'DescribeOpsItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOpsItemsRequest', ], 'output' => [ 'shape' => 'DescribeOpsItemsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeParameters' => [ 'name' => 'DescribeParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeParametersRequest', ], 'output' => [ 'shape' => 'DescribeParametersResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidFilterOption', ], [ 'shape' => 'InvalidFilterValue', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribePatchBaselines' => [ 'name' => 'DescribePatchBaselines', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePatchBaselinesRequest', ], 'output' => [ 'shape' => 'DescribePatchBaselinesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribePatchGroupState' => [ 'name' => 'DescribePatchGroupState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePatchGroupStateRequest', ], 'output' => [ 'shape' => 'DescribePatchGroupStateResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribePatchGroups' => [ 'name' => 'DescribePatchGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePatchGroupsRequest', ], 'output' => [ 'shape' => 'DescribePatchGroupsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribePatchProperties' => [ 'name' => 'DescribePatchProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePatchPropertiesRequest', ], 'output' => [ 'shape' => 'DescribePatchPropertiesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'DescribeSessions' => [ 'name' => 'DescribeSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSessionsRequest', ], 'output' => [ 'shape' => 'DescribeSessionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DisassociateOpsItemRelatedItem' => [ 'name' => 'DisassociateOpsItemRelatedItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateOpsItemRelatedItemRequest', ], 'output' => [ 'shape' => 'DisassociateOpsItemRelatedItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemRelatedItemAssociationNotFoundException', ], [ 'shape' => 'OpsItemNotFoundException', ], [ 'shape' => 'OpsItemInvalidParameterException', ], [ 'shape' => 'OpsItemConflictException', ], ], ], 'GetAutomationExecution' => [ 'name' => 'GetAutomationExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAutomationExecutionRequest', ], 'output' => [ 'shape' => 'GetAutomationExecutionResult', ], 'errors' => [ [ 'shape' => 'AutomationExecutionNotFoundException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetCalendarState' => [ 'name' => 'GetCalendarState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCalendarStateRequest', ], 'output' => [ 'shape' => 'GetCalendarStateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentType', ], [ 'shape' => 'UnsupportedCalendarException', ], ], ], 'GetCommandInvocation' => [ 'name' => 'GetCommandInvocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommandInvocationRequest', ], 'output' => [ 'shape' => 'GetCommandInvocationResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidCommandId', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidPluginName', ], [ 'shape' => 'InvocationDoesNotExist', ], ], ], 'GetConnectionStatus' => [ 'name' => 'GetConnectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionStatusRequest', ], 'output' => [ 'shape' => 'GetConnectionStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'GetDefaultPatchBaseline' => [ 'name' => 'GetDefaultPatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDefaultPatchBaselineRequest', ], 'output' => [ 'shape' => 'GetDefaultPatchBaselineResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'GetDeployablePatchSnapshotForInstance' => [ 'name' => 'GetDeployablePatchSnapshotForInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeployablePatchSnapshotForInstanceRequest', ], 'output' => [ 'shape' => 'GetDeployablePatchSnapshotForInstanceResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'UnsupportedOperatingSystem', ], [ 'shape' => 'UnsupportedFeatureRequiredException', ], ], ], 'GetDocument' => [ 'name' => 'GetDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDocumentRequest', ], 'output' => [ 'shape' => 'GetDocumentResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], ], ], 'GetInventory' => [ 'name' => 'GetInventory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInventoryRequest', ], 'output' => [ 'shape' => 'GetInventoryResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidInventoryGroupException', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidAggregatorException', ], [ 'shape' => 'InvalidResultAttributeException', ], ], ], 'GetInventorySchema' => [ 'name' => 'GetInventorySchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInventorySchemaRequest', ], 'output' => [ 'shape' => 'GetInventorySchemaResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'GetMaintenanceWindow' => [ 'name' => 'GetMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'GetMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetMaintenanceWindowExecution' => [ 'name' => 'GetMaintenanceWindowExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMaintenanceWindowExecutionRequest', ], 'output' => [ 'shape' => 'GetMaintenanceWindowExecutionResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetMaintenanceWindowExecutionTask' => [ 'name' => 'GetMaintenanceWindowExecutionTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMaintenanceWindowExecutionTaskRequest', ], 'output' => [ 'shape' => 'GetMaintenanceWindowExecutionTaskResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetMaintenanceWindowExecutionTaskInvocation' => [ 'name' => 'GetMaintenanceWindowExecutionTaskInvocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMaintenanceWindowExecutionTaskInvocationRequest', ], 'output' => [ 'shape' => 'GetMaintenanceWindowExecutionTaskInvocationResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetMaintenanceWindowTask' => [ 'name' => 'GetMaintenanceWindowTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMaintenanceWindowTaskRequest', ], 'output' => [ 'shape' => 'GetMaintenanceWindowTaskResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetOpsItem' => [ 'name' => 'GetOpsItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOpsItemRequest', ], 'output' => [ 'shape' => 'GetOpsItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemNotFoundException', ], [ 'shape' => 'OpsItemAccessDeniedException', ], ], ], 'GetOpsMetadata' => [ 'name' => 'GetOpsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOpsMetadataRequest', ], 'output' => [ 'shape' => 'GetOpsMetadataResult', ], 'errors' => [ [ 'shape' => 'OpsMetadataNotFoundException', ], [ 'shape' => 'OpsMetadataInvalidArgumentException', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetOpsSummary' => [ 'name' => 'GetOpsSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOpsSummaryRequest', ], 'output' => [ 'shape' => 'GetOpsSummaryResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceDataSyncNotFoundException', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidAggregatorException', ], ], ], 'GetParameter' => [ 'name' => 'GetParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetParameterRequest', ], 'output' => [ 'shape' => 'GetParameterResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidKeyId', ], [ 'shape' => 'ParameterNotFound', ], [ 'shape' => 'ParameterVersionNotFound', ], ], ], 'GetParameterHistory' => [ 'name' => 'GetParameterHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetParameterHistoryRequest', ], 'output' => [ 'shape' => 'GetParameterHistoryResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ParameterNotFound', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidKeyId', ], ], ], 'GetParameters' => [ 'name' => 'GetParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetParametersRequest', ], 'output' => [ 'shape' => 'GetParametersResult', ], 'errors' => [ [ 'shape' => 'InvalidKeyId', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetParametersByPath' => [ 'name' => 'GetParametersByPath', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetParametersByPathRequest', ], 'output' => [ 'shape' => 'GetParametersByPathResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidFilterOption', ], [ 'shape' => 'InvalidFilterValue', ], [ 'shape' => 'InvalidKeyId', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'GetPatchBaseline' => [ 'name' => 'GetPatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPatchBaselineRequest', ], 'output' => [ 'shape' => 'GetPatchBaselineResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], ], ], 'GetPatchBaselineForPatchGroup' => [ 'name' => 'GetPatchBaselineForPatchGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPatchBaselineForPatchGroupRequest', ], 'output' => [ 'shape' => 'GetPatchBaselineForPatchGroupResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'GetResourcePolicies' => [ 'name' => 'GetResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePoliciesRequest', ], 'output' => [ 'shape' => 'GetResourcePoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourcePolicyInvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetServiceSetting' => [ 'name' => 'GetServiceSetting', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceSettingRequest', ], 'output' => [ 'shape' => 'GetServiceSettingResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceSettingNotFound', ], ], ], 'LabelParameterVersion' => [ 'name' => 'LabelParameterVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'LabelParameterVersionRequest', ], 'output' => [ 'shape' => 'LabelParameterVersionResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TooManyUpdates', ], [ 'shape' => 'ParameterNotFound', ], [ 'shape' => 'ParameterVersionNotFound', ], [ 'shape' => 'ParameterVersionLabelLimitExceeded', ], ], ], 'ListAssociationVersions' => [ 'name' => 'ListAssociationVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociationVersionsRequest', ], 'output' => [ 'shape' => 'ListAssociationVersionsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'AssociationDoesNotExist', ], ], ], 'ListAssociations' => [ 'name' => 'ListAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociationsRequest', ], 'output' => [ 'shape' => 'ListAssociationsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListCommandInvocations' => [ 'name' => 'ListCommandInvocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCommandInvocationsRequest', ], 'output' => [ 'shape' => 'ListCommandInvocationsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidCommandId', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListCommands' => [ 'name' => 'ListCommands', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCommandsRequest', ], 'output' => [ 'shape' => 'ListCommandsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidCommandId', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidFilterKey', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListComplianceItems' => [ 'name' => 'ListComplianceItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComplianceItemsRequest', ], 'output' => [ 'shape' => 'ListComplianceItemsResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceType', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListComplianceSummaries' => [ 'name' => 'ListComplianceSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComplianceSummariesRequest', ], 'output' => [ 'shape' => 'ListComplianceSummariesResult', ], 'errors' => [ [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListDocumentMetadataHistory' => [ 'name' => 'ListDocumentMetadataHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentMetadataHistoryRequest', ], 'output' => [ 'shape' => 'ListDocumentMetadataHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListDocumentVersions' => [ 'name' => 'ListDocumentVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentVersionsRequest', ], 'output' => [ 'shape' => 'ListDocumentVersionsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidDocument', ], ], ], 'ListDocuments' => [ 'name' => 'ListDocuments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDocumentsRequest', ], 'output' => [ 'shape' => 'ListDocumentsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InvalidFilterKey', ], ], ], 'ListInventoryEntries' => [ 'name' => 'ListInventoryEntries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInventoryEntriesRequest', ], 'output' => [ 'shape' => 'ListInventoryEntriesResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListOpsItemEvents' => [ 'name' => 'ListOpsItemEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOpsItemEventsRequest', ], 'output' => [ 'shape' => 'ListOpsItemEventsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemNotFoundException', ], [ 'shape' => 'OpsItemLimitExceededException', ], [ 'shape' => 'OpsItemInvalidParameterException', ], ], ], 'ListOpsItemRelatedItems' => [ 'name' => 'ListOpsItemRelatedItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOpsItemRelatedItemsRequest', ], 'output' => [ 'shape' => 'ListOpsItemRelatedItemsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemInvalidParameterException', ], ], ], 'ListOpsMetadata' => [ 'name' => 'ListOpsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOpsMetadataRequest', ], 'output' => [ 'shape' => 'ListOpsMetadataResult', ], 'errors' => [ [ 'shape' => 'OpsMetadataInvalidArgumentException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListResourceComplianceSummaries' => [ 'name' => 'ListResourceComplianceSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceComplianceSummariesRequest', ], 'output' => [ 'shape' => 'ListResourceComplianceSummariesResult', ], 'errors' => [ [ 'shape' => 'InvalidFilter', ], [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListResourceDataSync' => [ 'name' => 'ListResourceDataSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceDataSyncRequest', ], 'output' => [ 'shape' => 'ListResourceDataSyncResult', ], 'errors' => [ [ 'shape' => 'ResourceDataSyncInvalidConfigurationException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceType', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], ], ], 'ModifyDocumentPermission' => [ 'name' => 'ModifyDocumentPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDocumentPermissionRequest', ], 'output' => [ 'shape' => 'ModifyDocumentPermissionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidPermissionType', ], [ 'shape' => 'DocumentPermissionLimit', ], [ 'shape' => 'DocumentLimitExceeded', ], ], ], 'PutComplianceItems' => [ 'name' => 'PutComplianceItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutComplianceItemsRequest', ], 'output' => [ 'shape' => 'PutComplianceItemsResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidItemContentException', ], [ 'shape' => 'TotalSizeLimitExceededException', ], [ 'shape' => 'ItemSizeLimitExceededException', ], [ 'shape' => 'ComplianceTypeCountLimitExceededException', ], [ 'shape' => 'InvalidResourceType', ], [ 'shape' => 'InvalidResourceId', ], ], ], 'PutInventory' => [ 'name' => 'PutInventory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInventoryRequest', ], 'output' => [ 'shape' => 'PutInventoryResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidTypeNameException', ], [ 'shape' => 'InvalidItemContentException', ], [ 'shape' => 'TotalSizeLimitExceededException', ], [ 'shape' => 'ItemSizeLimitExceededException', ], [ 'shape' => 'ItemContentMismatchException', ], [ 'shape' => 'CustomSchemaCountLimitExceededException', ], [ 'shape' => 'UnsupportedInventorySchemaVersionException', ], [ 'shape' => 'UnsupportedInventoryItemContextException', ], [ 'shape' => 'InvalidInventoryItemContextException', ], [ 'shape' => 'SubTypeCountLimitExceededException', ], ], ], 'PutParameter' => [ 'name' => 'PutParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutParameterRequest', ], 'output' => [ 'shape' => 'PutParameterResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidKeyId', ], [ 'shape' => 'ParameterLimitExceeded', ], [ 'shape' => 'TooManyUpdates', ], [ 'shape' => 'ParameterAlreadyExists', ], [ 'shape' => 'HierarchyLevelLimitExceededException', ], [ 'shape' => 'HierarchyTypeMismatchException', ], [ 'shape' => 'InvalidAllowedPatternException', ], [ 'shape' => 'ParameterMaxVersionLimitExceeded', ], [ 'shape' => 'ParameterPatternMismatchException', ], [ 'shape' => 'UnsupportedParameterType', ], [ 'shape' => 'PoliciesLimitExceededException', ], [ 'shape' => 'InvalidPolicyTypeException', ], [ 'shape' => 'InvalidPolicyAttributeException', ], [ 'shape' => 'IncompatiblePolicyException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourcePolicyInvalidParameterException', ], [ 'shape' => 'ResourcePolicyLimitExceededException', ], [ 'shape' => 'ResourcePolicyConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MalformedResourcePolicyDocumentException', ], [ 'shape' => 'ResourcePolicyNotFoundException', ], ], ], 'RegisterDefaultPatchBaseline' => [ 'name' => 'RegisterDefaultPatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterDefaultPatchBaselineRequest', ], 'output' => [ 'shape' => 'RegisterDefaultPatchBaselineResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RegisterPatchBaselineForPatchGroup' => [ 'name' => 'RegisterPatchBaselineForPatchGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterPatchBaselineForPatchGroupRequest', ], 'output' => [ 'shape' => 'RegisterPatchBaselineForPatchGroupResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RegisterTargetWithMaintenanceWindow' => [ 'name' => 'RegisterTargetWithMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterTargetWithMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'RegisterTargetWithMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RegisterTaskWithMaintenanceWindow' => [ 'name' => 'RegisterTaskWithMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterTaskWithMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'RegisterTaskWithMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'FeatureNotAvailableException', ], [ 'shape' => 'InternalServerError', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceRequest', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidResourceType', ], [ 'shape' => 'InvalidResourceId', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'ResetServiceSetting' => [ 'name' => 'ResetServiceSetting', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetServiceSettingRequest', ], 'output' => [ 'shape' => 'ResetServiceSettingResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceSettingNotFound', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'ResumeSession' => [ 'name' => 'ResumeSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResumeSessionRequest', ], 'output' => [ 'shape' => 'ResumeSessionResponse', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'SendAutomationSignal' => [ 'name' => 'SendAutomationSignal', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendAutomationSignalRequest', ], 'output' => [ 'shape' => 'SendAutomationSignalResult', ], 'errors' => [ [ 'shape' => 'AutomationExecutionNotFoundException', ], [ 'shape' => 'AutomationStepNotFoundException', ], [ 'shape' => 'InvalidAutomationSignalException', ], [ 'shape' => 'InternalServerError', ], ], ], 'SendCommand' => [ 'name' => 'SendCommand', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendCommandRequest', ], 'output' => [ 'shape' => 'SendCommandResult', ], 'errors' => [ [ 'shape' => 'DuplicateInstanceId', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidOutputFolder', ], [ 'shape' => 'InvalidParameters', ], [ 'shape' => 'UnsupportedPlatformType', ], [ 'shape' => 'MaxDocumentSizeExceeded', ], [ 'shape' => 'InvalidRole', ], [ 'shape' => 'InvalidNotificationConfig', ], ], ], 'StartAssociationsOnce' => [ 'name' => 'StartAssociationsOnce', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAssociationsOnceRequest', ], 'output' => [ 'shape' => 'StartAssociationsOnceResult', ], 'errors' => [ [ 'shape' => 'InvalidAssociation', ], [ 'shape' => 'AssociationDoesNotExist', ], ], ], 'StartAutomationExecution' => [ 'name' => 'StartAutomationExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAutomationExecutionRequest', ], 'output' => [ 'shape' => 'StartAutomationExecutionResult', ], 'errors' => [ [ 'shape' => 'AutomationDefinitionNotFoundException', ], [ 'shape' => 'InvalidAutomationExecutionParametersException', ], [ 'shape' => 'AutomationExecutionLimitExceededException', ], [ 'shape' => 'AutomationDefinitionVersionNotFoundException', ], [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'InvalidTarget', ], [ 'shape' => 'InternalServerError', ], ], ], 'StartChangeRequestExecution' => [ 'name' => 'StartChangeRequestExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartChangeRequestExecutionRequest', ], 'output' => [ 'shape' => 'StartChangeRequestExecutionResult', ], 'errors' => [ [ 'shape' => 'AutomationDefinitionNotFoundException', ], [ 'shape' => 'InvalidAutomationExecutionParametersException', ], [ 'shape' => 'AutomationExecutionLimitExceededException', ], [ 'shape' => 'AutomationDefinitionVersionNotFoundException', ], [ 'shape' => 'IdempotentParameterMismatch', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'AutomationDefinitionNotApprovedException', ], ], ], 'StartSession' => [ 'name' => 'StartSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSessionRequest', ], 'output' => [ 'shape' => 'StartSessionResponse', ], 'errors' => [ [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'TargetNotConnected', ], [ 'shape' => 'InternalServerError', ], ], ], 'StopAutomationExecution' => [ 'name' => 'StopAutomationExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAutomationExecutionRequest', ], 'output' => [ 'shape' => 'StopAutomationExecutionResult', ], 'errors' => [ [ 'shape' => 'AutomationExecutionNotFoundException', ], [ 'shape' => 'InvalidAutomationStatusUpdateException', ], [ 'shape' => 'InternalServerError', ], ], ], 'TerminateSession' => [ 'name' => 'TerminateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateSessionRequest', ], 'output' => [ 'shape' => 'TerminateSessionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], ], ], 'UnlabelParameterVersion' => [ 'name' => 'UnlabelParameterVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnlabelParameterVersionRequest', ], 'output' => [ 'shape' => 'UnlabelParameterVersionResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'TooManyUpdates', ], [ 'shape' => 'ParameterNotFound', ], [ 'shape' => 'ParameterVersionNotFound', ], ], ], 'UpdateAssociation' => [ 'name' => 'UpdateAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAssociationRequest', ], 'output' => [ 'shape' => 'UpdateAssociationResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidSchedule', ], [ 'shape' => 'InvalidParameters', ], [ 'shape' => 'InvalidOutputLocation', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'InvalidUpdate', ], [ 'shape' => 'TooManyUpdates', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidTarget', ], [ 'shape' => 'InvalidAssociationVersion', ], [ 'shape' => 'AssociationVersionLimitExceeded', ], [ 'shape' => 'InvalidTargetMaps', ], ], ], 'UpdateAssociationStatus' => [ 'name' => 'UpdateAssociationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAssociationStatusRequest', ], 'output' => [ 'shape' => 'UpdateAssociationStatusResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'AssociationDoesNotExist', ], [ 'shape' => 'StatusUnchanged', ], [ 'shape' => 'TooManyUpdates', ], ], ], 'UpdateDocument' => [ 'name' => 'UpdateDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDocumentRequest', ], 'output' => [ 'shape' => 'UpdateDocumentResult', ], 'errors' => [ [ 'shape' => 'MaxDocumentSizeExceeded', ], [ 'shape' => 'DocumentVersionLimitExceeded', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'DuplicateDocumentContent', ], [ 'shape' => 'DuplicateDocumentVersionName', ], [ 'shape' => 'InvalidDocumentContent', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidDocumentSchemaVersion', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentOperation', ], ], ], 'UpdateDocumentDefaultVersion' => [ 'name' => 'UpdateDocumentDefaultVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDocumentDefaultVersionRequest', ], 'output' => [ 'shape' => 'UpdateDocumentDefaultVersionResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentVersion', ], [ 'shape' => 'InvalidDocumentSchemaVersion', ], ], ], 'UpdateDocumentMetadata' => [ 'name' => 'UpdateDocumentMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDocumentMetadataRequest', ], 'output' => [ 'shape' => 'UpdateDocumentMetadataResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidDocument', ], [ 'shape' => 'InvalidDocumentOperation', ], [ 'shape' => 'InvalidDocumentVersion', ], ], ], 'UpdateMaintenanceWindow' => [ 'name' => 'UpdateMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'UpdateMaintenanceWindowResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateMaintenanceWindowTarget' => [ 'name' => 'UpdateMaintenanceWindowTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMaintenanceWindowTargetRequest', ], 'output' => [ 'shape' => 'UpdateMaintenanceWindowTargetResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateMaintenanceWindowTask' => [ 'name' => 'UpdateMaintenanceWindowTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMaintenanceWindowTaskRequest', ], 'output' => [ 'shape' => 'UpdateMaintenanceWindowTaskResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateManagedInstanceRole' => [ 'name' => 'UpdateManagedInstanceRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateManagedInstanceRoleRequest', ], 'output' => [ 'shape' => 'UpdateManagedInstanceRoleResult', ], 'errors' => [ [ 'shape' => 'InvalidInstanceId', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateOpsItem' => [ 'name' => 'UpdateOpsItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateOpsItemRequest', ], 'output' => [ 'shape' => 'UpdateOpsItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'OpsItemNotFoundException', ], [ 'shape' => 'OpsItemAlreadyExistsException', ], [ 'shape' => 'OpsItemLimitExceededException', ], [ 'shape' => 'OpsItemInvalidParameterException', ], [ 'shape' => 'OpsItemAccessDeniedException', ], [ 'shape' => 'OpsItemConflictException', ], ], ], 'UpdateOpsMetadata' => [ 'name' => 'UpdateOpsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateOpsMetadataRequest', ], 'output' => [ 'shape' => 'UpdateOpsMetadataResult', ], 'errors' => [ [ 'shape' => 'OpsMetadataNotFoundException', ], [ 'shape' => 'OpsMetadataInvalidArgumentException', ], [ 'shape' => 'OpsMetadataKeyLimitExceededException', ], [ 'shape' => 'OpsMetadataTooManyUpdatesException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdatePatchBaseline' => [ 'name' => 'UpdatePatchBaseline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePatchBaselineRequest', ], 'output' => [ 'shape' => 'UpdatePatchBaselineResult', ], 'errors' => [ [ 'shape' => 'DoesNotExistException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateResourceDataSync' => [ 'name' => 'UpdateResourceDataSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResourceDataSyncRequest', ], 'output' => [ 'shape' => 'UpdateResourceDataSyncResult', ], 'errors' => [ [ 'shape' => 'ResourceDataSyncNotFoundException', ], [ 'shape' => 'ResourceDataSyncInvalidConfigurationException', ], [ 'shape' => 'ResourceDataSyncConflictException', ], [ 'shape' => 'InternalServerError', ], ], ], 'UpdateServiceSetting' => [ 'name' => 'UpdateServiceSetting', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceSettingRequest', ], 'output' => [ 'shape' => 'UpdateServiceSettingResult', ], 'errors' => [ [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ServiceSettingNotFound', ], [ 'shape' => 'TooManyUpdates', ], ], ], ], 'shapes' => [ 'Account' => [ 'type' => 'string', ], 'AccountId' => [ 'type' => 'string', 'pattern' => '(?i)all|[0-9]{12}', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 20, ], 'AccountSharingInfo' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'SharedDocumentVersion' => [ 'shape' => 'SharedDocumentVersion', ], ], ], 'AccountSharingInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountSharingInfo', ], ], 'Accounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], 'max' => 50, 'min' => 1, ], 'Activation' => [ 'type' => 'structure', 'members' => [ 'ActivationId' => [ 'shape' => 'ActivationId', ], 'Description' => [ 'shape' => 'ActivationDescription', ], 'DefaultInstanceName' => [ 'shape' => 'DefaultInstanceName', ], 'IamRole' => [ 'shape' => 'IamRole', ], 'RegistrationLimit' => [ 'shape' => 'RegistrationLimit', ], 'RegistrationsCount' => [ 'shape' => 'RegistrationsCount', ], 'ExpirationDate' => [ 'shape' => 'ExpirationDate', ], 'Expired' => [ 'shape' => 'Boolean', ], 'CreatedDate' => [ 'shape' => 'CreatedDate', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ActivationCode' => [ 'type' => 'string', 'max' => 250, 'min' => 20, ], 'ActivationDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ActivationId' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'ActivationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Activation', ], ], 'AddTagsToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceId', 'Tags', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeForTagging', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsToResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'AgentErrorCode' => [ 'type' => 'string', 'max' => 10, ], 'AggregatorSchemaOnly' => [ 'type' => 'boolean', ], 'Alarm' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AlarmName', ], ], ], 'AlarmConfiguration' => [ 'type' => 'structure', 'required' => [ 'Alarms', ], 'members' => [ 'IgnorePollAlarmFailure' => [ 'shape' => 'Boolean', ], 'Alarms' => [ 'shape' => 'AlarmList', ], ], ], 'AlarmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alarm', ], 'max' => 1, 'min' => 1, ], 'AlarmName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'AlarmStateInformation' => [ 'type' => 'structure', 'required' => [ 'Name', 'State', ], 'members' => [ 'Name' => [ 'shape' => 'AlarmName', ], 'State' => [ 'shape' => 'ExternalAlarmState', ], ], ], 'AlarmStateInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmStateInformation', ], 'max' => 1, 'min' => 1, ], 'AllowedPattern' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ApplyOnlyAtCronInterval' => [ 'type' => 'boolean', ], 'ApproveAfterDays' => [ 'type' => 'integer', 'max' => 360, 'min' => 0, ], 'AssociateOpsItemRelatedItemRequest' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', 'AssociationType', 'ResourceType', 'ResourceUri', ], 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'AssociationType' => [ 'shape' => 'OpsItemRelatedItemAssociationType', ], 'ResourceType' => [ 'shape' => 'OpsItemRelatedItemAssociationResourceType', ], 'ResourceUri' => [ 'shape' => 'OpsItemRelatedItemAssociationResourceUri', ], ], ], 'AssociateOpsItemRelatedItemResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'OpsItemRelatedItemAssociationId', ], ], ], 'AssociatedInstances' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Association' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Targets' => [ 'shape' => 'Targets', ], 'LastExecutionDate' => [ 'shape' => 'DateTime', ], 'Overview' => [ 'shape' => 'AssociationOverview', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], ], ], 'AssociationAlreadyExists' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AssociationComplianceSeverity' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'UNSPECIFIED', ], ], 'AssociationDescription' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'Date' => [ 'shape' => 'DateTime', ], 'LastUpdateAssociationDate' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'AssociationStatus', ], 'Overview' => [ 'shape' => 'AssociationOverview', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'AutomationTargetParameterName' => [ 'shape' => 'AutomationTargetParameterName', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Targets' => [ 'shape' => 'Targets', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'OutputLocation' => [ 'shape' => 'InstanceAssociationOutputLocation', ], 'LastExecutionDate' => [ 'shape' => 'DateTime', ], 'LastSuccessfulExecutionDate' => [ 'shape' => 'DateTime', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'ComplianceSeverity' => [ 'shape' => 'AssociationComplianceSeverity', ], 'SyncCompliance' => [ 'shape' => 'AssociationSyncCompliance', ], 'ApplyOnlyAtCronInterval' => [ 'shape' => 'ApplyOnlyAtCronInterval', ], 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], ], ], 'AssociationDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationDescription', ], ], 'AssociationDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AssociationExecution' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'ExecutionId' => [ 'shape' => 'AssociationExecutionId', ], 'Status' => [ 'shape' => 'StatusName', ], 'DetailedStatus' => [ 'shape' => 'StatusName', ], 'CreatedTime' => [ 'shape' => 'DateTime', ], 'LastExecutionDate' => [ 'shape' => 'DateTime', ], 'ResourceCountByStatus' => [ 'shape' => 'ResourceCountByStatus', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], ], ], 'AssociationExecutionDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AssociationExecutionFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', 'Type', ], 'members' => [ 'Key' => [ 'shape' => 'AssociationExecutionFilterKey', ], 'Value' => [ 'shape' => 'AssociationExecutionFilterValue', ], 'Type' => [ 'shape' => 'AssociationFilterOperatorType', ], ], ], 'AssociationExecutionFilterKey' => [ 'type' => 'string', 'enum' => [ 'ExecutionId', 'Status', 'CreatedTime', ], ], 'AssociationExecutionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationExecutionFilter', ], 'min' => 1, ], 'AssociationExecutionFilterValue' => [ 'type' => 'string', 'min' => 1, ], 'AssociationExecutionId' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'AssociationExecutionTarget' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'ExecutionId' => [ 'shape' => 'AssociationExecutionId', ], 'ResourceId' => [ 'shape' => 'AssociationResourceId', ], 'ResourceType' => [ 'shape' => 'AssociationResourceType', ], 'Status' => [ 'shape' => 'StatusName', ], 'DetailedStatus' => [ 'shape' => 'StatusName', ], 'LastExecutionDate' => [ 'shape' => 'DateTime', ], 'OutputSource' => [ 'shape' => 'OutputSource', ], ], ], 'AssociationExecutionTargetsFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'AssociationExecutionTargetsFilterKey', ], 'Value' => [ 'shape' => 'AssociationExecutionTargetsFilterValue', ], ], ], 'AssociationExecutionTargetsFilterKey' => [ 'type' => 'string', 'enum' => [ 'Status', 'ResourceId', 'ResourceType', ], ], 'AssociationExecutionTargetsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationExecutionTargetsFilter', ], 'min' => 1, ], 'AssociationExecutionTargetsFilterValue' => [ 'type' => 'string', 'min' => 1, ], 'AssociationExecutionTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationExecutionTarget', ], ], 'AssociationExecutionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationExecution', ], ], 'AssociationFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'AssociationFilterKey', ], 'value' => [ 'shape' => 'AssociationFilterValue', ], ], ], 'AssociationFilterKey' => [ 'type' => 'string', 'enum' => [ 'InstanceId', 'Name', 'AssociationId', 'AssociationStatusName', 'LastExecutedBefore', 'LastExecutedAfter', 'AssociationName', 'ResourceGroupName', ], ], 'AssociationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationFilter', ], 'min' => 1, ], 'AssociationFilterOperatorType' => [ 'type' => 'string', 'enum' => [ 'EQUAL', 'LESS_THAN', 'GREATER_THAN', ], ], 'AssociationFilterValue' => [ 'type' => 'string', 'min' => 1, ], 'AssociationId' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'AssociationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationId', ], 'max' => 10, 'min' => 1, ], 'AssociationLimitExceeded' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Association', ], ], 'AssociationName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'AssociationOverview' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusName', ], 'DetailedStatus' => [ 'shape' => 'StatusName', ], 'AssociationStatusAggregatedCount' => [ 'shape' => 'AssociationStatusAggregatedCount', ], ], ], 'AssociationResourceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AssociationResourceType' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'AssociationStatus' => [ 'type' => 'structure', 'required' => [ 'Date', 'Name', 'Message', ], 'members' => [ 'Date' => [ 'shape' => 'DateTime', ], 'Name' => [ 'shape' => 'AssociationStatusName', ], 'Message' => [ 'shape' => 'StatusMessage', ], 'AdditionalInfo' => [ 'shape' => 'StatusAdditionalInfo', ], ], ], 'AssociationStatusAggregatedCount' => [ 'type' => 'map', 'key' => [ 'shape' => 'StatusName', ], 'value' => [ 'shape' => 'InstanceCount', ], ], 'AssociationStatusName' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Success', 'Failed', ], ], 'AssociationSyncCompliance' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'MANUAL', ], ], 'AssociationVersion' => [ 'type' => 'string', 'pattern' => '([$]LATEST)|([1-9][0-9]*)', ], 'AssociationVersionInfo' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'Name' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Targets' => [ 'shape' => 'Targets', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'OutputLocation' => [ 'shape' => 'InstanceAssociationOutputLocation', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'ComplianceSeverity' => [ 'shape' => 'AssociationComplianceSeverity', ], 'SyncCompliance' => [ 'shape' => 'AssociationSyncCompliance', ], 'ApplyOnlyAtCronInterval' => [ 'shape' => 'ApplyOnlyAtCronInterval', ], 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], ], ], 'AssociationVersionLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AssociationVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationVersionInfo', ], 'min' => 1, ], 'AttachmentContent' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AttachmentName', ], 'Size' => [ 'shape' => 'ContentLength', ], 'Hash' => [ 'shape' => 'AttachmentHash', ], 'HashType' => [ 'shape' => 'AttachmentHashType', ], 'Url' => [ 'shape' => 'AttachmentUrl', ], ], ], 'AttachmentContentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentContent', ], ], 'AttachmentHash' => [ 'type' => 'string', 'max' => 256, ], 'AttachmentHashType' => [ 'type' => 'string', 'enum' => [ 'Sha256', ], ], 'AttachmentIdentifier' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'AttachmentInformation' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AttachmentName', ], ], ], 'AttachmentInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentInformation', ], ], 'AttachmentName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'AttachmentUrl' => [ 'type' => 'string', ], 'AttachmentsSource' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'AttachmentsSourceKey', ], 'Values' => [ 'shape' => 'AttachmentsSourceValues', ], 'Name' => [ 'shape' => 'AttachmentIdentifier', ], ], ], 'AttachmentsSourceKey' => [ 'type' => 'string', 'enum' => [ 'SourceUrl', 'S3FileUrl', 'AttachmentReference', ], ], 'AttachmentsSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentsSource', ], 'max' => 20, 'min' => 0, ], 'AttachmentsSourceValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'AttachmentsSourceValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentsSourceValue', ], 'max' => 1, 'min' => 1, ], 'AttributeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'AttributeValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'AutomationActionName' => [ 'type' => 'string', 'pattern' => '^aws:[a-zA-Z]{3,25}$', ], 'AutomationDefinitionNotApprovedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationDefinitionNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationDefinitionVersionNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationExecution' => [ 'type' => 'structure', 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'ExecutionStartTime' => [ 'shape' => 'DateTime', ], 'ExecutionEndTime' => [ 'shape' => 'DateTime', ], 'AutomationExecutionStatus' => [ 'shape' => 'AutomationExecutionStatus', ], 'StepExecutions' => [ 'shape' => 'StepExecutionList', ], 'StepExecutionsTruncated' => [ 'shape' => 'Boolean', ], 'Parameters' => [ 'shape' => 'AutomationParameterMap', ], 'Outputs' => [ 'shape' => 'AutomationParameterMap', ], 'FailureMessage' => [ 'shape' => 'String', ], 'Mode' => [ 'shape' => 'ExecutionMode', ], 'ParentAutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'ExecutedBy' => [ 'shape' => 'String', ], 'CurrentStepName' => [ 'shape' => 'String', ], 'CurrentAction' => [ 'shape' => 'String', ], 'TargetParameterName' => [ 'shape' => 'AutomationParameterKey', ], 'Targets' => [ 'shape' => 'Targets', ], 'TargetMaps' => [ 'shape' => 'TargetMaps', ], 'ResolvedTargets' => [ 'shape' => 'ResolvedTargets', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'Target' => [ 'shape' => 'String', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', 'box' => true, ], 'ProgressCounters' => [ 'shape' => 'ProgressCounters', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], 'AutomationSubtype' => [ 'shape' => 'AutomationSubtype', ], 'ScheduledTime' => [ 'shape' => 'DateTime', ], 'Runbooks' => [ 'shape' => 'Runbooks', ], 'OpsItemId' => [ 'shape' => 'String', ], 'AssociationId' => [ 'shape' => 'String', ], 'ChangeRequestName' => [ 'shape' => 'ChangeRequestName', ], 'Variables' => [ 'shape' => 'AutomationParameterMap', ], ], ], 'AutomationExecutionFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'AutomationExecutionFilterKey', ], 'Values' => [ 'shape' => 'AutomationExecutionFilterValueList', ], ], ], 'AutomationExecutionFilterKey' => [ 'type' => 'string', 'enum' => [ 'DocumentNamePrefix', 'ExecutionStatus', 'ExecutionId', 'ParentExecutionId', 'CurrentAction', 'StartTimeBefore', 'StartTimeAfter', 'AutomationType', 'TagKey', 'TargetResourceGroup', 'AutomationSubtype', 'OpsItemId', ], ], 'AutomationExecutionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationExecutionFilter', ], 'max' => 10, 'min' => 1, ], 'AutomationExecutionFilterValue' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'AutomationExecutionFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationExecutionFilterValue', ], 'max' => 10, 'min' => 1, ], 'AutomationExecutionId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'AutomationExecutionLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationExecutionMetadata' => [ 'type' => 'structure', 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'AutomationExecutionStatus' => [ 'shape' => 'AutomationExecutionStatus', ], 'ExecutionStartTime' => [ 'shape' => 'DateTime', ], 'ExecutionEndTime' => [ 'shape' => 'DateTime', ], 'ExecutedBy' => [ 'shape' => 'String', ], 'LogFile' => [ 'shape' => 'String', ], 'Outputs' => [ 'shape' => 'AutomationParameterMap', ], 'Mode' => [ 'shape' => 'ExecutionMode', ], 'ParentAutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'CurrentStepName' => [ 'shape' => 'String', ], 'CurrentAction' => [ 'shape' => 'String', ], 'FailureMessage' => [ 'shape' => 'String', ], 'TargetParameterName' => [ 'shape' => 'AutomationParameterKey', ], 'Targets' => [ 'shape' => 'Targets', ], 'TargetMaps' => [ 'shape' => 'TargetMaps', ], 'ResolvedTargets' => [ 'shape' => 'ResolvedTargets', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'Target' => [ 'shape' => 'String', ], 'AutomationType' => [ 'shape' => 'AutomationType', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], 'AutomationSubtype' => [ 'shape' => 'AutomationSubtype', ], 'ScheduledTime' => [ 'shape' => 'DateTime', ], 'Runbooks' => [ 'shape' => 'Runbooks', ], 'OpsItemId' => [ 'shape' => 'String', ], 'AssociationId' => [ 'shape' => 'String', ], 'ChangeRequestName' => [ 'shape' => 'ChangeRequestName', ], ], ], 'AutomationExecutionMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationExecutionMetadata', ], ], 'AutomationExecutionNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Waiting', 'Success', 'TimedOut', 'Cancelling', 'Cancelled', 'Failed', 'PendingApproval', 'Approved', 'Rejected', 'Scheduled', 'RunbookInProgress', 'PendingChangeCalendarOverride', 'ChangeCalendarOverrideApproved', 'ChangeCalendarOverrideRejected', 'CompletedWithSuccess', 'CompletedWithFailure', 'Exited', ], ], 'AutomationParameterKey' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'AutomationParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AutomationParameterKey', ], 'value' => [ 'shape' => 'AutomationParameterValueList', ], 'max' => 200, 'min' => 1, ], 'AutomationParameterValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'AutomationParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationParameterValue', ], 'max' => 50, 'min' => 0, ], 'AutomationStepNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AutomationSubtype' => [ 'type' => 'string', 'enum' => [ 'ChangeRequest', ], ], 'AutomationTargetParameterName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'AutomationType' => [ 'type' => 'string', 'enum' => [ 'CrossAccount', 'Local', ], ], 'BaselineDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'BaselineId' => [ 'type' => 'string', 'max' => 128, 'min' => 20, 'pattern' => '^[a-zA-Z0-9_\\-:/]{20,128}$', ], 'BaselineName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'BaselineOverride' => [ 'type' => 'structure', 'members' => [ 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'GlobalFilters' => [ 'shape' => 'PatchFilterGroup', ], 'ApprovalRules' => [ 'shape' => 'PatchRuleGroup', ], 'ApprovedPatches' => [ 'shape' => 'PatchIdList', ], 'ApprovedPatchesComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'RejectedPatches' => [ 'shape' => 'PatchIdList', ], 'RejectedPatchesAction' => [ 'shape' => 'PatchAction', ], 'ApprovedPatchesEnableNonSecurity' => [ 'shape' => 'Boolean', ], 'Sources' => [ 'shape' => 'PatchSourceList', ], ], ], 'BatchErrorMessage' => [ 'type' => 'string', ], 'Boolean' => [ 'type' => 'boolean', ], 'CalendarNameOrARN' => [ 'type' => 'string', ], 'CalendarNameOrARNList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CalendarNameOrARN', ], ], 'CalendarState' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'CLOSED', ], ], 'CancelCommandRequest' => [ 'type' => 'structure', 'required' => [ 'CommandId', ], 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceIds' => [ 'shape' => 'InstanceIdList', ], ], ], 'CancelCommandResult' => [ 'type' => 'structure', 'members' => [], ], 'CancelMaintenanceWindowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], ], ], 'CancelMaintenanceWindowExecutionResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], ], ], 'Category' => [ 'type' => 'string', 'max' => 128, ], 'CategoryEnumList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], 'max' => 3, 'min' => 0, ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], 'max' => 3, 'min' => 0, ], 'ChangeDetailsValue' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, ], 'ChangeRequestName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CloudWatchLogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'CloudWatchOutputConfig' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogGroupName' => [ 'shape' => 'CloudWatchLogGroupName', ], 'CloudWatchOutputEnabled' => [ 'shape' => 'CloudWatchOutputEnabled', ], ], ], 'CloudWatchOutputEnabled' => [ 'type' => 'boolean', ], 'Command' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Comment' => [ 'shape' => 'Comment', ], 'ExpiresAfter' => [ 'shape' => 'DateTime', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'InstanceIds' => [ 'shape' => 'InstanceIdList', ], 'Targets' => [ 'shape' => 'Targets', ], 'RequestedDateTime' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'CommandStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'OutputS3Region' => [ 'shape' => 'S3Region', ], 'OutputS3BucketName' => [ 'shape' => 'S3BucketName', ], 'OutputS3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'TargetCount' => [ 'shape' => 'TargetCount', ], 'CompletedCount' => [ 'shape' => 'CompletedCount', ], 'ErrorCount' => [ 'shape' => 'ErrorCount', ], 'DeliveryTimedOutCount' => [ 'shape' => 'DeliveryTimedOutCount', ], 'ServiceRole' => [ 'shape' => 'ServiceRole', ], 'NotificationConfig' => [ 'shape' => 'NotificationConfig', ], 'CloudWatchOutputConfig' => [ 'shape' => 'CloudWatchOutputConfig', ], 'TimeoutSeconds' => [ 'shape' => 'TimeoutSeconds', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], ], ], 'CommandFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'CommandFilterKey', ], 'value' => [ 'shape' => 'CommandFilterValue', ], ], ], 'CommandFilterKey' => [ 'type' => 'string', 'enum' => [ 'InvokedAfter', 'InvokedBefore', 'Status', 'ExecutionStage', 'DocumentName', ], ], 'CommandFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandFilter', ], 'max' => 5, 'min' => 1, ], 'CommandFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CommandId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'CommandInvocation' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'InstanceName' => [ 'shape' => 'InstanceTagName', ], 'Comment' => [ 'shape' => 'Comment', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'RequestedDateTime' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'CommandInvocationStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'TraceOutput' => [ 'shape' => 'InvocationTraceOutput', ], 'StandardOutputUrl' => [ 'shape' => 'Url', ], 'StandardErrorUrl' => [ 'shape' => 'Url', ], 'CommandPlugins' => [ 'shape' => 'CommandPluginList', ], 'ServiceRole' => [ 'shape' => 'ServiceRole', ], 'NotificationConfig' => [ 'shape' => 'NotificationConfig', ], 'CloudWatchOutputConfig' => [ 'shape' => 'CloudWatchOutputConfig', ], ], ], 'CommandInvocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandInvocation', ], ], 'CommandInvocationStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Delayed', 'Success', 'Cancelled', 'TimedOut', 'Failed', 'Cancelling', ], ], 'CommandList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Command', ], ], 'CommandMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'CommandPlugin' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CommandPluginName', ], 'Status' => [ 'shape' => 'CommandPluginStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'ResponseCode' => [ 'shape' => 'ResponseCode', ], 'ResponseStartDateTime' => [ 'shape' => 'DateTime', ], 'ResponseFinishDateTime' => [ 'shape' => 'DateTime', ], 'Output' => [ 'shape' => 'CommandPluginOutput', ], 'StandardOutputUrl' => [ 'shape' => 'Url', ], 'StandardErrorUrl' => [ 'shape' => 'Url', ], 'OutputS3Region' => [ 'shape' => 'S3Region', ], 'OutputS3BucketName' => [ 'shape' => 'S3BucketName', ], 'OutputS3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], ], ], 'CommandPluginList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandPlugin', ], ], 'CommandPluginName' => [ 'type' => 'string', 'min' => 4, ], 'CommandPluginOutput' => [ 'type' => 'string', 'max' => 2500, ], 'CommandPluginStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Success', 'TimedOut', 'Cancelled', 'Failed', ], ], 'CommandStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Success', 'Cancelled', 'Failed', 'TimedOut', 'Cancelling', ], ], 'Comment' => [ 'type' => 'string', 'max' => 100, ], 'CompletedCount' => [ 'type' => 'integer', ], 'ComplianceExecutionId' => [ 'type' => 'string', 'max' => 100, ], 'ComplianceExecutionSummary' => [ 'type' => 'structure', 'required' => [ 'ExecutionTime', ], 'members' => [ 'ExecutionTime' => [ 'shape' => 'DateTime', ], 'ExecutionId' => [ 'shape' => 'ComplianceExecutionId', ], 'ExecutionType' => [ 'shape' => 'ComplianceExecutionType', ], ], ], 'ComplianceExecutionType' => [ 'type' => 'string', 'max' => 50, ], 'ComplianceFilterValue' => [ 'type' => 'string', ], 'ComplianceItem' => [ 'type' => 'structure', 'members' => [ 'ComplianceType' => [ 'shape' => 'ComplianceTypeName', ], 'ResourceType' => [ 'shape' => 'ComplianceResourceType', ], 'ResourceId' => [ 'shape' => 'ComplianceResourceId', ], 'Id' => [ 'shape' => 'ComplianceItemId', ], 'Title' => [ 'shape' => 'ComplianceItemTitle', ], 'Status' => [ 'shape' => 'ComplianceStatus', ], 'Severity' => [ 'shape' => 'ComplianceSeverity', ], 'ExecutionSummary' => [ 'shape' => 'ComplianceExecutionSummary', ], 'Details' => [ 'shape' => 'ComplianceItemDetails', ], ], ], 'ComplianceItemContentHash' => [ 'type' => 'string', 'max' => 256, ], 'ComplianceItemDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'ComplianceItemEntry' => [ 'type' => 'structure', 'required' => [ 'Severity', 'Status', ], 'members' => [ 'Id' => [ 'shape' => 'ComplianceItemId', ], 'Title' => [ 'shape' => 'ComplianceItemTitle', ], 'Severity' => [ 'shape' => 'ComplianceSeverity', ], 'Status' => [ 'shape' => 'ComplianceStatus', ], 'Details' => [ 'shape' => 'ComplianceItemDetails', ], ], ], 'ComplianceItemEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceItemEntry', ], 'max' => 10000, 'min' => 0, ], 'ComplianceItemId' => [ 'type' => 'string', ], 'ComplianceItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceItem', ], ], 'ComplianceItemTitle' => [ 'type' => 'string', 'max' => 500, ], 'ComplianceQueryOperatorType' => [ 'type' => 'string', 'enum' => [ 'EQUAL', 'NOT_EQUAL', 'BEGIN_WITH', 'LESS_THAN', 'GREATER_THAN', ], ], 'ComplianceResourceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ComplianceResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceResourceId', ], 'min' => 1, ], 'ComplianceResourceType' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ComplianceResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceResourceType', ], 'min' => 1, ], 'ComplianceSeverity' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFORMATIONAL', 'UNSPECIFIED', ], ], 'ComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLIANT', 'NON_COMPLIANT', ], ], 'ComplianceStringFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ComplianceStringFilterKey', ], 'Values' => [ 'shape' => 'ComplianceStringFilterValueList', ], 'Type' => [ 'shape' => 'ComplianceQueryOperatorType', ], ], ], 'ComplianceStringFilterKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'ComplianceStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceStringFilter', ], ], 'ComplianceStringFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceFilterValue', ], 'max' => 20, 'min' => 1, ], 'ComplianceSummaryCount' => [ 'type' => 'integer', ], 'ComplianceSummaryItem' => [ 'type' => 'structure', 'members' => [ 'ComplianceType' => [ 'shape' => 'ComplianceTypeName', ], 'CompliantSummary' => [ 'shape' => 'CompliantSummary', ], 'NonCompliantSummary' => [ 'shape' => 'NonCompliantSummary', ], ], ], 'ComplianceSummaryItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceSummaryItem', ], ], 'ComplianceTypeCountLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ComplianceTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9_\\-]\\w+|Custom:[a-zA-Z0-9_\\-]\\w+', ], 'ComplianceUploadType' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'PARTIAL', ], ], 'CompliantSummary' => [ 'type' => 'structure', 'members' => [ 'CompliantCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'SeveritySummary' => [ 'shape' => 'SeveritySummary', ], ], ], 'ComputerName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'connected', 'notconnected', ], ], 'ContentLength' => [ 'type' => 'long', ], 'CreateActivationRequest' => [ 'type' => 'structure', 'required' => [ 'IamRole', ], 'members' => [ 'Description' => [ 'shape' => 'ActivationDescription', ], 'DefaultInstanceName' => [ 'shape' => 'DefaultInstanceName', ], 'IamRole' => [ 'shape' => 'IamRole', ], 'RegistrationLimit' => [ 'shape' => 'RegistrationLimit', 'box' => true, ], 'ExpirationDate' => [ 'shape' => 'ExpirationDate', ], 'Tags' => [ 'shape' => 'TagList', ], 'RegistrationMetadata' => [ 'shape' => 'RegistrationMetadataList', ], ], ], 'CreateActivationResult' => [ 'type' => 'structure', 'members' => [ 'ActivationId' => [ 'shape' => 'ActivationId', ], 'ActivationCode' => [ 'shape' => 'ActivationCode', ], ], ], 'CreateAssociationBatchRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'CreateAssociationBatchRequestEntries', ], ], ], 'CreateAssociationBatchRequestEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateAssociationBatchRequestEntry', ], 'min' => 1, ], 'CreateAssociationBatchRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'AutomationTargetParameterName' => [ 'shape' => 'AutomationTargetParameterName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Targets' => [ 'shape' => 'Targets', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'OutputLocation' => [ 'shape' => 'InstanceAssociationOutputLocation', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'ComplianceSeverity' => [ 'shape' => 'AssociationComplianceSeverity', ], 'SyncCompliance' => [ 'shape' => 'AssociationSyncCompliance', ], 'ApplyOnlyAtCronInterval' => [ 'shape' => 'ApplyOnlyAtCronInterval', ], 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'CreateAssociationBatchResult' => [ 'type' => 'structure', 'members' => [ 'Successful' => [ 'shape' => 'AssociationDescriptionList', ], 'Failed' => [ 'shape' => 'FailedCreateAssociationList', ], ], ], 'CreateAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Targets' => [ 'shape' => 'Targets', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'OutputLocation' => [ 'shape' => 'InstanceAssociationOutputLocation', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'AutomationTargetParameterName' => [ 'shape' => 'AutomationTargetParameterName', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'ComplianceSeverity' => [ 'shape' => 'AssociationComplianceSeverity', ], 'SyncCompliance' => [ 'shape' => 'AssociationSyncCompliance', ], 'ApplyOnlyAtCronInterval' => [ 'shape' => 'ApplyOnlyAtCronInterval', ], 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'CreateAssociationResult' => [ 'type' => 'structure', 'members' => [ 'AssociationDescription' => [ 'shape' => 'AssociationDescription', ], ], ], 'CreateDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'Content', 'Name', ], 'members' => [ 'Content' => [ 'shape' => 'DocumentContent', ], 'Requires' => [ 'shape' => 'DocumentRequiresList', ], 'Attachments' => [ 'shape' => 'AttachmentsSourceList', ], 'Name' => [ 'shape' => 'DocumentName', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'DocumentType' => [ 'shape' => 'DocumentType', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDocumentResult' => [ 'type' => 'structure', 'members' => [ 'DocumentDescription' => [ 'shape' => 'DocumentDescription', ], ], ], 'CreateMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Schedule', 'Duration', 'Cutoff', 'AllowUnassociatedTargets', ], 'members' => [ 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'StartDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'EndDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'Schedule' => [ 'shape' => 'MaintenanceWindowSchedule', ], 'ScheduleTimezone' => [ 'shape' => 'MaintenanceWindowTimezone', ], 'ScheduleOffset' => [ 'shape' => 'MaintenanceWindowOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'MaintenanceWindowDurationHours', ], 'Cutoff' => [ 'shape' => 'MaintenanceWindowCutoff', ], 'AllowUnassociatedTargets' => [ 'shape' => 'MaintenanceWindowAllowUnassociatedTargets', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], ], ], 'CreateOpsItemRequest' => [ 'type' => 'structure', 'required' => [ 'Description', 'Source', 'Title', ], 'members' => [ 'Description' => [ 'shape' => 'OpsItemDescription', ], 'OpsItemType' => [ 'shape' => 'OpsItemType', ], 'OperationalData' => [ 'shape' => 'OpsItemOperationalData', ], 'Notifications' => [ 'shape' => 'OpsItemNotifications', ], 'Priority' => [ 'shape' => 'OpsItemPriority', ], 'RelatedOpsItems' => [ 'shape' => 'RelatedOpsItems', ], 'Source' => [ 'shape' => 'OpsItemSource', ], 'Title' => [ 'shape' => 'OpsItemTitle', ], 'Tags' => [ 'shape' => 'TagList', ], 'Category' => [ 'shape' => 'OpsItemCategory', ], 'Severity' => [ 'shape' => 'OpsItemSeverity', ], 'ActualStartTime' => [ 'shape' => 'DateTime', ], 'ActualEndTime' => [ 'shape' => 'DateTime', ], 'PlannedStartTime' => [ 'shape' => 'DateTime', ], 'PlannedEndTime' => [ 'shape' => 'DateTime', ], 'AccountId' => [ 'shape' => 'OpsItemAccountId', ], ], ], 'CreateOpsItemResponse' => [ 'type' => 'structure', 'members' => [ 'OpsItemId' => [ 'shape' => 'String', ], 'OpsItemArn' => [ 'shape' => 'OpsItemArn', ], ], ], 'CreateOpsMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'OpsMetadataResourceId', ], 'Metadata' => [ 'shape' => 'MetadataMap', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateOpsMetadataResult' => [ 'type' => 'structure', 'members' => [ 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], ], ], 'CreatePatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Name' => [ 'shape' => 'BaselineName', ], 'GlobalFilters' => [ 'shape' => 'PatchFilterGroup', ], 'ApprovalRules' => [ 'shape' => 'PatchRuleGroup', ], 'ApprovedPatches' => [ 'shape' => 'PatchIdList', ], 'ApprovedPatchesComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApprovedPatchesEnableNonSecurity' => [ 'shape' => 'Boolean', 'box' => true, ], 'RejectedPatches' => [ 'shape' => 'PatchIdList', ], 'RejectedPatchesAction' => [ 'shape' => 'PatchAction', ], 'Description' => [ 'shape' => 'BaselineDescription', ], 'Sources' => [ 'shape' => 'PatchSourceList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'CreateResourceDataSyncRequest' => [ 'type' => 'structure', 'required' => [ 'SyncName', ], 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'S3Destination' => [ 'shape' => 'ResourceDataSyncS3Destination', ], 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], 'SyncSource' => [ 'shape' => 'ResourceDataSyncSource', ], ], ], 'CreateResourceDataSyncResult' => [ 'type' => 'structure', 'members' => [], ], 'CreatedDate' => [ 'type' => 'timestamp', ], 'CustomSchemaCountLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DateTime' => [ 'type' => 'timestamp', ], 'DefaultBaseline' => [ 'type' => 'boolean', ], 'DefaultInstanceName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'DeleteActivationRequest' => [ 'type' => 'structure', 'required' => [ 'ActivationId', ], 'members' => [ 'ActivationId' => [ 'shape' => 'ActivationId', ], ], ], 'DeleteActivationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssociationRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', ], ], ], 'DeleteAssociationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'DeleteDocumentResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInventoryRequest' => [ 'type' => 'structure', 'required' => [ 'TypeName', ], 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'SchemaDeleteOption' => [ 'shape' => 'InventorySchemaDeleteOption', ], 'DryRun' => [ 'shape' => 'DryRun', ], 'ClientToken' => [ 'shape' => 'UUID', 'idempotencyToken' => true, ], ], ], 'DeleteInventoryResult' => [ 'type' => 'structure', 'members' => [ 'DeletionId' => [ 'shape' => 'UUID', ], 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'DeletionSummary' => [ 'shape' => 'InventoryDeletionSummary', ], ], ], 'DeleteMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], ], ], 'DeleteMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], ], ], 'DeleteOpsItemRequest' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', ], 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], ], ], 'DeleteOpsItemResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOpsMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'OpsMetadataArn', ], 'members' => [ 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], ], ], 'DeleteOpsMetadataResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteParameterRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], ], ], 'DeleteParameterResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteParametersRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'ParameterNameList', ], ], ], 'DeleteParametersResult' => [ 'type' => 'structure', 'members' => [ 'DeletedParameters' => [ 'shape' => 'ParameterNameList', ], 'InvalidParameters' => [ 'shape' => 'ParameterNameList', ], ], ], 'DeletePatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'DeletePatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'DeleteResourceDataSyncRequest' => [ 'type' => 'structure', 'required' => [ 'SyncName', ], 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], ], ], 'DeleteResourceDataSyncResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'PolicyId', 'PolicyHash', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyHash' => [ 'shape' => 'PolicyHash', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliveryTimedOutCount' => [ 'type' => 'integer', ], 'DeregisterManagedInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ManagedInstanceId', ], ], ], 'DeregisterManagedInstanceResult' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterPatchBaselineForPatchGroupRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', 'PatchGroup', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], ], ], 'DeregisterPatchBaselineForPatchGroupResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], ], ], 'DeregisterTargetFromMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'WindowTargetId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], 'Safe' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'DeregisterTargetFromMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], ], ], 'DeregisterTaskFromMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'WindowTaskId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], ], ], 'DeregisterTaskFromMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], ], ], 'DescribeActivationsFilter' => [ 'type' => 'structure', 'members' => [ 'FilterKey' => [ 'shape' => 'DescribeActivationsFilterKeys', ], 'FilterValues' => [ 'shape' => 'StringList', ], ], ], 'DescribeActivationsFilterKeys' => [ 'type' => 'string', 'enum' => [ 'ActivationIds', 'DefaultInstanceName', 'IamRole', ], ], 'DescribeActivationsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeActivationsFilter', ], ], 'DescribeActivationsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'DescribeActivationsFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeActivationsResult' => [ 'type' => 'structure', 'members' => [ 'ActivationList' => [ 'shape' => 'ActivationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAssociationExecutionTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', 'ExecutionId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'ExecutionId' => [ 'shape' => 'AssociationExecutionId', ], 'Filters' => [ 'shape' => 'AssociationExecutionTargetsFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAssociationExecutionTargetsResult' => [ 'type' => 'structure', 'members' => [ 'AssociationExecutionTargets' => [ 'shape' => 'AssociationExecutionTargetsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAssociationExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Filters' => [ 'shape' => 'AssociationExecutionFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAssociationExecutionsResult' => [ 'type' => 'structure', 'members' => [ 'AssociationExecutions' => [ 'shape' => 'AssociationExecutionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAssociationRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], ], ], 'DescribeAssociationResult' => [ 'type' => 'structure', 'members' => [ 'AssociationDescription' => [ 'shape' => 'AssociationDescription', ], ], ], 'DescribeAutomationExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'AutomationExecutionFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAutomationExecutionsResult' => [ 'type' => 'structure', 'members' => [ 'AutomationExecutionMetadataList' => [ 'shape' => 'AutomationExecutionMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAutomationStepExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationExecutionId', ], 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'Filters' => [ 'shape' => 'StepExecutionFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'ReverseOrder' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'DescribeAutomationStepExecutionsResult' => [ 'type' => 'structure', 'members' => [ 'StepExecutions' => [ 'shape' => 'StepExecutionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAvailablePatchesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'PatchOrchestratorFilterList', ], 'MaxResults' => [ 'shape' => 'PatchBaselineMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAvailablePatchesResult' => [ 'type' => 'structure', 'members' => [ 'Patches' => [ 'shape' => 'PatchList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDocumentPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PermissionType', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'PermissionType' => [ 'shape' => 'DocumentPermissionType', ], 'MaxResults' => [ 'shape' => 'DocumentPermissionMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDocumentPermissionResponse' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'AccountSharingInfoList' => [ 'shape' => 'AccountSharingInfoList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], ], ], 'DescribeDocumentResult' => [ 'type' => 'structure', 'members' => [ 'Document' => [ 'shape' => 'DocumentDescription', ], ], ], 'DescribeEffectiveInstanceAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'EffectiveInstanceAssociationMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEffectiveInstanceAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'InstanceAssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEffectivePatchesForPatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'MaxResults' => [ 'shape' => 'PatchBaselineMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEffectivePatchesForPatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'EffectivePatches' => [ 'shape' => 'EffectivePatchList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstanceAssociationsStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstanceAssociationsStatusResult' => [ 'type' => 'structure', 'members' => [ 'InstanceAssociationStatusInfos' => [ 'shape' => 'InstanceAssociationStatusInfos', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstanceInformationRequest' => [ 'type' => 'structure', 'members' => [ 'InstanceInformationFilterList' => [ 'shape' => 'InstanceInformationFilterList', ], 'Filters' => [ 'shape' => 'InstanceInformationStringFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResultsEC2Compatible', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstanceInformationResult' => [ 'type' => 'structure', 'members' => [ 'InstanceInformationList' => [ 'shape' => 'InstanceInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstancePatchStatesForPatchGroupRequest' => [ 'type' => 'structure', 'required' => [ 'PatchGroup', ], 'members' => [ 'PatchGroup' => [ 'shape' => 'PatchGroup', ], 'Filters' => [ 'shape' => 'InstancePatchStateFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'PatchComplianceMaxResults', 'box' => true, ], ], ], 'DescribeInstancePatchStatesForPatchGroupResult' => [ 'type' => 'structure', 'members' => [ 'InstancePatchStates' => [ 'shape' => 'InstancePatchStatesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstancePatchStatesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceIds', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIdList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'PatchComplianceMaxResults', 'box' => true, ], ], ], 'DescribeInstancePatchStatesResult' => [ 'type' => 'structure', 'members' => [ 'InstancePatchStates' => [ 'shape' => 'InstancePatchStateList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstancePatchesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Filters' => [ 'shape' => 'PatchOrchestratorFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'PatchComplianceMaxResults', 'box' => true, ], ], ], 'DescribeInstancePatchesResult' => [ 'type' => 'structure', 'members' => [ 'Patches' => [ 'shape' => 'PatchComplianceDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInventoryDeletionsRequest' => [ 'type' => 'structure', 'members' => [ 'DeletionId' => [ 'shape' => 'UUID', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'DescribeInventoryDeletionsResult' => [ 'type' => 'structure', 'members' => [ 'InventoryDeletions' => [ 'shape' => 'InventoryDeletionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionTaskInvocationsRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', 'TaskId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionTaskInvocationsResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionTaskInvocationIdentities' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationIdentityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionTasksRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionTasksResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionTaskIdentities' => [ 'shape' => 'MaintenanceWindowExecutionTaskIdentityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowExecutionsResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutions' => [ 'shape' => 'MaintenanceWindowExecutionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowScheduleRequest' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Targets' => [ 'shape' => 'Targets', ], 'ResourceType' => [ 'shape' => 'MaintenanceWindowResourceType', ], 'Filters' => [ 'shape' => 'PatchOrchestratorFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowSearchMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowScheduleResult' => [ 'type' => 'structure', 'members' => [ 'ScheduledWindowExecutions' => [ 'shape' => 'ScheduledWindowExecutionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowTargetsResult' => [ 'type' => 'structure', 'members' => [ 'Targets' => [ 'shape' => 'MaintenanceWindowTargetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowTasksRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowTasksResult' => [ 'type' => 'structure', 'members' => [ 'Tasks' => [ 'shape' => 'MaintenanceWindowTaskList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowsForTargetRequest' => [ 'type' => 'structure', 'required' => [ 'Targets', 'ResourceType', ], 'members' => [ 'Targets' => [ 'shape' => 'Targets', ], 'ResourceType' => [ 'shape' => 'MaintenanceWindowResourceType', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowSearchMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowsForTargetResult' => [ 'type' => 'structure', 'members' => [ 'WindowIdentities' => [ 'shape' => 'MaintenanceWindowsForTargetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'MaintenanceWindowFilterList', ], 'MaxResults' => [ 'shape' => 'MaintenanceWindowMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeMaintenanceWindowsResult' => [ 'type' => 'structure', 'members' => [ 'WindowIdentities' => [ 'shape' => 'MaintenanceWindowIdentityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOpsItemsRequest' => [ 'type' => 'structure', 'members' => [ 'OpsItemFilters' => [ 'shape' => 'OpsItemFilters', ], 'MaxResults' => [ 'shape' => 'OpsItemMaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOpsItemsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'OpsItemSummaries' => [ 'shape' => 'OpsItemSummaries', ], ], ], 'DescribeParametersRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ParametersFilterList', ], 'ParameterFilters' => [ 'shape' => 'ParameterStringFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Shared' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'DescribeParametersResult' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchBaselinesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'PatchOrchestratorFilterList', ], 'MaxResults' => [ 'shape' => 'PatchBaselineMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchBaselinesResult' => [ 'type' => 'structure', 'members' => [ 'BaselineIdentities' => [ 'shape' => 'PatchBaselineIdentityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchGroupStateRequest' => [ 'type' => 'structure', 'required' => [ 'PatchGroup', ], 'members' => [ 'PatchGroup' => [ 'shape' => 'PatchGroup', ], ], ], 'DescribePatchGroupStateResult' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'Integer', ], 'InstancesWithInstalledPatches' => [ 'shape' => 'Integer', ], 'InstancesWithInstalledOtherPatches' => [ 'shape' => 'Integer', ], 'InstancesWithInstalledPendingRebootPatches' => [ 'shape' => 'InstancesCount', 'box' => true, ], 'InstancesWithInstalledRejectedPatches' => [ 'shape' => 'InstancesCount', 'box' => true, ], 'InstancesWithMissingPatches' => [ 'shape' => 'Integer', ], 'InstancesWithFailedPatches' => [ 'shape' => 'Integer', ], 'InstancesWithNotApplicablePatches' => [ 'shape' => 'Integer', ], 'InstancesWithUnreportedNotApplicablePatches' => [ 'shape' => 'Integer', 'box' => true, ], 'InstancesWithCriticalNonCompliantPatches' => [ 'shape' => 'InstancesCount', 'box' => true, ], 'InstancesWithSecurityNonCompliantPatches' => [ 'shape' => 'InstancesCount', 'box' => true, ], 'InstancesWithOtherNonCompliantPatches' => [ 'shape' => 'InstancesCount', 'box' => true, ], ], ], 'DescribePatchGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PatchBaselineMaxResults', 'box' => true, ], 'Filters' => [ 'shape' => 'PatchOrchestratorFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchGroupsResult' => [ 'type' => 'structure', 'members' => [ 'Mappings' => [ 'shape' => 'PatchGroupPatchBaselineMappingList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'OperatingSystem', 'Property', ], 'members' => [ 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Property' => [ 'shape' => 'PatchProperty', ], 'PatchSet' => [ 'shape' => 'PatchSet', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePatchPropertiesResult' => [ 'type' => 'structure', 'members' => [ 'Properties' => [ 'shape' => 'PatchPropertiesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'SessionState', ], 'MaxResults' => [ 'shape' => 'SessionMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'SessionFilterList', ], ], ], 'DescribeSessionsResponse' => [ 'type' => 'structure', 'members' => [ 'Sessions' => [ 'shape' => 'SessionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescriptionInDocument' => [ 'type' => 'string', ], 'DisassociateOpsItemRelatedItemRequest' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', 'AssociationId', ], 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'AssociationId' => [ 'shape' => 'OpsItemRelatedItemAssociationId', ], ], ], 'DisassociateOpsItemRelatedItemResponse' => [ 'type' => 'structure', 'members' => [], ], 'DocumentARN' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.:/]{3,128}$', ], 'DocumentAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DocumentAuthor' => [ 'type' => 'string', ], 'DocumentContent' => [ 'type' => 'string', 'min' => 1, ], 'DocumentDefaultVersionDescription' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DefaultVersion' => [ 'shape' => 'DocumentVersion', ], 'DefaultVersionName' => [ 'shape' => 'DocumentVersionName', ], ], ], 'DocumentDescription' => [ 'type' => 'structure', 'members' => [ 'Sha1' => [ 'shape' => 'DocumentSha1', ], 'Hash' => [ 'shape' => 'DocumentHash', ], 'HashType' => [ 'shape' => 'DocumentHashType', ], 'Name' => [ 'shape' => 'DocumentARN', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'Owner' => [ 'shape' => 'DocumentOwner', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'DocumentStatus', ], 'StatusInformation' => [ 'shape' => 'DocumentStatusInformation', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Description' => [ 'shape' => 'DescriptionInDocument', ], 'Parameters' => [ 'shape' => 'DocumentParameterList', ], 'PlatformTypes' => [ 'shape' => 'PlatformTypeList', ], 'DocumentType' => [ 'shape' => 'DocumentType', ], 'SchemaVersion' => [ 'shape' => 'DocumentSchemaVersion', ], 'LatestVersion' => [ 'shape' => 'DocumentVersion', ], 'DefaultVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'Tags' => [ 'shape' => 'TagList', ], 'AttachmentsInformation' => [ 'shape' => 'AttachmentInformationList', ], 'Requires' => [ 'shape' => 'DocumentRequiresList', ], 'Author' => [ 'shape' => 'DocumentAuthor', ], 'ReviewInformation' => [ 'shape' => 'ReviewInformationList', ], 'ApprovedVersion' => [ 'shape' => 'DocumentVersion', ], 'PendingReviewVersion' => [ 'shape' => 'DocumentVersion', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'Category' => [ 'shape' => 'CategoryList', ], 'CategoryEnum' => [ 'shape' => 'CategoryEnumList', ], ], ], 'DocumentDisplayName' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^[\\w\\.\\-\\:\\/ ]*$', ], 'DocumentFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'DocumentFilterKey', ], 'value' => [ 'shape' => 'DocumentFilterValue', ], ], ], 'DocumentFilterKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'Owner', 'PlatformTypes', 'DocumentType', ], ], 'DocumentFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentFilter', ], 'min' => 1, ], 'DocumentFilterValue' => [ 'type' => 'string', 'min' => 1, ], 'DocumentFormat' => [ 'type' => 'string', 'enum' => [ 'YAML', 'JSON', 'TEXT', ], ], 'DocumentHash' => [ 'type' => 'string', 'max' => 256, ], 'DocumentHashType' => [ 'type' => 'string', 'enum' => [ 'Sha256', 'Sha1', ], ], 'DocumentIdentifier' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'Owner' => [ 'shape' => 'DocumentOwner', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'PlatformTypes' => [ 'shape' => 'PlatformTypeList', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentType' => [ 'shape' => 'DocumentType', ], 'SchemaVersion' => [ 'shape' => 'DocumentSchemaVersion', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'Tags' => [ 'shape' => 'TagList', ], 'Requires' => [ 'shape' => 'DocumentRequiresList', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'Author' => [ 'shape' => 'DocumentAuthor', ], ], ], 'DocumentIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentIdentifier', ], ], 'DocumentKeyValuesFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'DocumentKeyValuesFilterKey', ], 'Values' => [ 'shape' => 'DocumentKeyValuesFilterValues', ], ], ], 'DocumentKeyValuesFilterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DocumentKeyValuesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentKeyValuesFilter', ], 'max' => 6, 'min' => 0, ], 'DocumentKeyValuesFilterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DocumentKeyValuesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentKeyValuesFilterValue', ], ], 'DocumentLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DocumentMetadataEnum' => [ 'type' => 'string', 'enum' => [ 'DocumentReviews', ], ], 'DocumentMetadataResponseInfo' => [ 'type' => 'structure', 'members' => [ 'ReviewerResponse' => [ 'shape' => 'DocumentReviewerResponseList', ], ], ], 'DocumentName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'DocumentOwner' => [ 'type' => 'string', ], 'DocumentParameter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentParameterName', ], 'Type' => [ 'shape' => 'DocumentParameterType', ], 'Description' => [ 'shape' => 'DocumentParameterDescrption', ], 'DefaultValue' => [ 'shape' => 'DocumentParameterDefaultValue', ], ], ], 'DocumentParameterDefaultValue' => [ 'type' => 'string', ], 'DocumentParameterDescrption' => [ 'type' => 'string', ], 'DocumentParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentParameter', ], ], 'DocumentParameterName' => [ 'type' => 'string', ], 'DocumentParameterType' => [ 'type' => 'string', 'enum' => [ 'String', 'StringList', ], ], 'DocumentPermissionLimit' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DocumentPermissionMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'DocumentPermissionType' => [ 'type' => 'string', 'enum' => [ 'Share', ], ], 'DocumentRequires' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'Version' => [ 'shape' => 'DocumentVersion', ], 'RequireType' => [ 'shape' => 'RequireType', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], ], ], 'DocumentRequiresList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentRequires', ], 'min' => 1, ], 'DocumentReviewAction' => [ 'type' => 'string', 'enum' => [ 'SendForReview', 'UpdateReview', 'Approve', 'Reject', ], ], 'DocumentReviewComment' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'DocumentReviewCommentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentReviewCommentSource', ], 'max' => 1, 'min' => 0, ], 'DocumentReviewCommentSource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DocumentReviewCommentType', ], 'Content' => [ 'shape' => 'DocumentReviewComment', ], ], ], 'DocumentReviewCommentType' => [ 'type' => 'string', 'enum' => [ 'Comment', ], ], 'DocumentReviewerResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentReviewerResponseSource', ], ], 'DocumentReviewerResponseSource' => [ 'type' => 'structure', 'members' => [ 'CreateTime' => [ 'shape' => 'DateTime', ], 'UpdatedTime' => [ 'shape' => 'DateTime', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'Comment' => [ 'shape' => 'DocumentReviewCommentList', ], 'Reviewer' => [ 'shape' => 'Reviewer', ], ], ], 'DocumentReviews' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'DocumentReviewAction', ], 'Comment' => [ 'shape' => 'DocumentReviewCommentList', ], ], ], 'DocumentSchemaVersion' => [ 'type' => 'string', 'pattern' => '([0-9]+)\\.([0-9]+)', ], 'DocumentSha1' => [ 'type' => 'string', ], 'DocumentStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Active', 'Updating', 'Deleting', 'Failed', ], ], 'DocumentStatusInformation' => [ 'type' => 'string', ], 'DocumentType' => [ 'type' => 'string', 'enum' => [ 'Command', 'Policy', 'Automation', 'Session', 'Package', 'ApplicationConfiguration', 'ApplicationConfigurationSchema', 'DeploymentStrategy', 'ChangeCalendar', 'Automation.ChangeTemplate', 'ProblemAnalysis', 'ProblemAnalysisTemplate', 'CloudFormation', 'ConformancePackTemplate', 'QuickSetup', ], ], 'DocumentVersion' => [ 'type' => 'string', 'pattern' => '([$]LATEST|[$]DEFAULT|^[1-9][0-9]*$)', ], 'DocumentVersionInfo' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'IsDefaultVersion' => [ 'shape' => 'Boolean', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'Status' => [ 'shape' => 'DocumentStatus', ], 'StatusInformation' => [ 'shape' => 'DocumentStatusInformation', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], ], ], 'DocumentVersionLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DocumentVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentVersionInfo', ], 'min' => 1, ], 'DocumentVersionName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{1,128}$', ], 'DocumentVersionNumber' => [ 'type' => 'string', 'pattern' => '(^[1-9][0-9]*$)', ], 'DoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DryRun' => [ 'type' => 'boolean', ], 'DuplicateDocumentContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DuplicateDocumentVersionName' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DuplicateInstanceId' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Duration' => [ 'type' => 'integer', 'box' => true, 'max' => 24, 'min' => 1, ], 'EffectiveInstanceAssociationMaxResults' => [ 'type' => 'integer', 'max' => 5, 'min' => 1, ], 'EffectivePatch' => [ 'type' => 'structure', 'members' => [ 'Patch' => [ 'shape' => 'Patch', ], 'PatchStatus' => [ 'shape' => 'PatchStatus', ], ], ], 'EffectivePatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectivePatch', ], ], 'ErrorCount' => [ 'type' => 'integer', ], 'ExecutionMode' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Interactive', ], ], 'ExecutionRoleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\w+=,.@/-]+', ], 'ExpirationDate' => [ 'type' => 'timestamp', ], 'ExternalAlarmState' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'ALARM', ], ], 'FailedCreateAssociation' => [ 'type' => 'structure', 'members' => [ 'Entry' => [ 'shape' => 'CreateAssociationBatchRequestEntry', ], 'Message' => [ 'shape' => 'BatchErrorMessage', ], 'Fault' => [ 'shape' => 'Fault', ], ], ], 'FailedCreateAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCreateAssociation', ], ], 'FailureDetails' => [ 'type' => 'structure', 'members' => [ 'FailureStage' => [ 'shape' => 'String', ], 'FailureType' => [ 'shape' => 'String', ], 'Details' => [ 'shape' => 'AutomationParameterMap', ], ], ], 'Fault' => [ 'type' => 'string', 'enum' => [ 'Client', 'Server', 'Unknown', ], ], 'FeatureNotAvailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'GetAutomationExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationExecutionId', ], 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], ], ], 'GetAutomationExecutionResult' => [ 'type' => 'structure', 'members' => [ 'AutomationExecution' => [ 'shape' => 'AutomationExecution', ], ], ], 'GetCalendarStateRequest' => [ 'type' => 'structure', 'required' => [ 'CalendarNames', ], 'members' => [ 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'AtTime' => [ 'shape' => 'ISO8601String', ], ], ], 'GetCalendarStateResponse' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'CalendarState', ], 'AtTime' => [ 'shape' => 'ISO8601String', ], 'NextTransitionTime' => [ 'shape' => 'ISO8601String', ], ], ], 'GetCommandInvocationRequest' => [ 'type' => 'structure', 'required' => [ 'CommandId', 'InstanceId', ], 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PluginName' => [ 'shape' => 'CommandPluginName', ], ], ], 'GetCommandInvocationResult' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Comment' => [ 'shape' => 'Comment', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'PluginName' => [ 'shape' => 'CommandPluginName', ], 'ResponseCode' => [ 'shape' => 'ResponseCode', ], 'ExecutionStartDateTime' => [ 'shape' => 'StringDateTime', ], 'ExecutionElapsedTime' => [ 'shape' => 'StringDateTime', ], 'ExecutionEndDateTime' => [ 'shape' => 'StringDateTime', ], 'Status' => [ 'shape' => 'CommandInvocationStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'StandardOutputContent' => [ 'shape' => 'StandardOutputContent', ], 'StandardOutputUrl' => [ 'shape' => 'Url', ], 'StandardErrorContent' => [ 'shape' => 'StandardErrorContent', ], 'StandardErrorUrl' => [ 'shape' => 'Url', ], 'CloudWatchOutputConfig' => [ 'shape' => 'CloudWatchOutputConfig', ], ], ], 'GetConnectionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'Target', ], 'members' => [ 'Target' => [ 'shape' => 'SessionTarget', ], ], ], 'GetConnectionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'Target' => [ 'shape' => 'SessionTarget', ], 'Status' => [ 'shape' => 'ConnectionStatus', ], ], ], 'GetDefaultPatchBaselineRequest' => [ 'type' => 'structure', 'members' => [ 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], ], ], 'GetDefaultPatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], ], ], 'GetDeployablePatchSnapshotForInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'SnapshotId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'BaselineOverride' => [ 'shape' => 'BaselineOverride', ], ], ], 'GetDeployablePatchSnapshotForInstanceResult' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'SnapshotDownloadUrl' => [ 'shape' => 'SnapshotDownloadUrl', ], 'Product' => [ 'shape' => 'Product', ], ], ], 'GetDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], ], ], 'GetDocumentResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Status' => [ 'shape' => 'DocumentStatus', ], 'StatusInformation' => [ 'shape' => 'DocumentStatusInformation', ], 'Content' => [ 'shape' => 'DocumentContent', ], 'DocumentType' => [ 'shape' => 'DocumentType', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'Requires' => [ 'shape' => 'DocumentRequiresList', ], 'AttachmentsContent' => [ 'shape' => 'AttachmentContentList', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], ], ], 'GetInventoryRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'InventoryFilterList', ], 'Aggregators' => [ 'shape' => 'InventoryAggregatorList', ], 'ResultAttributes' => [ 'shape' => 'ResultAttributeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'GetInventoryResult' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'InventoryResultEntityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetInventorySchemaMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 50, ], 'GetInventorySchemaRequest' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeNameFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'GetInventorySchemaMaxResults', 'box' => true, ], 'Aggregator' => [ 'shape' => 'AggregatorSchemaOnly', ], 'SubType' => [ 'shape' => 'IsSubTypeSchema', 'box' => true, ], ], ], 'GetInventorySchemaResult' => [ 'type' => 'structure', 'members' => [ 'Schemas' => [ 'shape' => 'InventoryItemSchemaResultList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMaintenanceWindowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], ], ], 'GetMaintenanceWindowExecutionResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskIds' => [ 'shape' => 'MaintenanceWindowExecutionTaskIdList', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], ], ], 'GetMaintenanceWindowExecutionTaskInvocationRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', 'TaskId', 'InvocationId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'InvocationId' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationId', ], ], ], 'GetMaintenanceWindowExecutionTaskInvocationResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'InvocationId' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationId', ], 'ExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskExecutionId', ], 'TaskType' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'Parameters' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationParameters', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTaskTargetId', ], ], ], 'GetMaintenanceWindowExecutionTaskRequest' => [ 'type' => 'structure', 'required' => [ 'WindowExecutionId', 'TaskId', ], 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], ], ], 'GetMaintenanceWindowExecutionTaskResult' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'ServiceRole' => [ 'shape' => 'ServiceRole', ], 'Type' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParametersList', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], ], ], 'GetMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], ], ], 'GetMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'StartDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'EndDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'Schedule' => [ 'shape' => 'MaintenanceWindowSchedule', ], 'ScheduleTimezone' => [ 'shape' => 'MaintenanceWindowTimezone', ], 'ScheduleOffset' => [ 'shape' => 'MaintenanceWindowOffset', 'box' => true, ], 'NextExecutionTime' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'Duration' => [ 'shape' => 'MaintenanceWindowDurationHours', ], 'Cutoff' => [ 'shape' => 'MaintenanceWindowCutoff', ], 'AllowUnassociatedTargets' => [ 'shape' => 'MaintenanceWindowAllowUnassociatedTargets', ], 'Enabled' => [ 'shape' => 'MaintenanceWindowEnabled', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'ModifiedDate' => [ 'shape' => 'DateTime', ], ], ], 'GetMaintenanceWindowTaskRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'WindowTaskId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], ], ], 'GetMaintenanceWindowTaskResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], 'Targets' => [ 'shape' => 'Targets', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'TaskType' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'TaskInvocationParameters' => [ 'shape' => 'MaintenanceWindowTaskInvocationParameters', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'CutoffBehavior' => [ 'shape' => 'MaintenanceWindowTaskCutoffBehavior', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'GetOpsItemRequest' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', ], 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'OpsItemArn' => [ 'shape' => 'OpsItemArn', ], ], ], 'GetOpsItemResponse' => [ 'type' => 'structure', 'members' => [ 'OpsItem' => [ 'shape' => 'OpsItem', ], ], ], 'GetOpsMetadataMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'GetOpsMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'OpsMetadataArn', ], 'members' => [ 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], 'MaxResults' => [ 'shape' => 'GetOpsMetadataMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetOpsMetadataResult' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'OpsMetadataResourceId', ], 'Metadata' => [ 'shape' => 'MetadataMap', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetOpsSummaryRequest' => [ 'type' => 'structure', 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'Filters' => [ 'shape' => 'OpsFilterList', ], 'Aggregators' => [ 'shape' => 'OpsAggregatorList', ], 'ResultAttributes' => [ 'shape' => 'OpsResultAttributeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'GetOpsSummaryResult' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'OpsEntityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetParameterHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'WithDecryption' => [ 'shape' => 'Boolean', 'box' => true, ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetParameterHistoryResult' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterHistoryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetParameterRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'WithDecryption' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'GetParameterResult' => [ 'type' => 'structure', 'members' => [ 'Parameter' => [ 'shape' => 'Parameter', ], ], ], 'GetParametersByPathMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'GetParametersByPathRequest' => [ 'type' => 'structure', 'required' => [ 'Path', ], 'members' => [ 'Path' => [ 'shape' => 'PSParameterName', ], 'Recursive' => [ 'shape' => 'Boolean', 'box' => true, ], 'ParameterFilters' => [ 'shape' => 'ParameterStringFilterList', ], 'WithDecryption' => [ 'shape' => 'Boolean', 'box' => true, ], 'MaxResults' => [ 'shape' => 'GetParametersByPathMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetParametersByPathResult' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetParametersRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'ParameterNameList', ], 'WithDecryption' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'GetParametersResult' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterList', ], 'InvalidParameters' => [ 'shape' => 'ParameterNameList', ], ], ], 'GetPatchBaselineForPatchGroupRequest' => [ 'type' => 'structure', 'required' => [ 'PatchGroup', ], 'members' => [ 'PatchGroup' => [ 'shape' => 'PatchGroup', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], ], ], 'GetPatchBaselineForPatchGroupResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], ], ], 'GetPatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'GetPatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'Name' => [ 'shape' => 'BaselineName', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'GlobalFilters' => [ 'shape' => 'PatchFilterGroup', ], 'ApprovalRules' => [ 'shape' => 'PatchRuleGroup', ], 'ApprovedPatches' => [ 'shape' => 'PatchIdList', ], 'ApprovedPatchesComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApprovedPatchesEnableNonSecurity' => [ 'shape' => 'Boolean', 'box' => true, ], 'RejectedPatches' => [ 'shape' => 'PatchIdList', ], 'RejectedPatchesAction' => [ 'shape' => 'PatchAction', ], 'PatchGroups' => [ 'shape' => 'PatchGroupList', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'ModifiedDate' => [ 'shape' => 'DateTime', ], 'Description' => [ 'shape' => 'BaselineDescription', ], 'Sources' => [ 'shape' => 'PatchSourceList', ], ], ], 'GetResourcePoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'ResourcePolicyMaxResults', ], ], ], 'GetResourcePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Policies' => [ 'shape' => 'GetResourcePoliciesResponseEntries', ], ], ], 'GetResourcePoliciesResponseEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetResourcePoliciesResponseEntry', ], ], 'GetResourcePoliciesResponseEntry' => [ 'type' => 'structure', 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyHash' => [ 'shape' => 'PolicyHash', ], 'Policy' => [ 'shape' => 'Policy', ], ], ], 'GetServiceSettingRequest' => [ 'type' => 'structure', 'required' => [ 'SettingId', ], 'members' => [ 'SettingId' => [ 'shape' => 'ServiceSettingId', ], ], ], 'GetServiceSettingResult' => [ 'type' => 'structure', 'members' => [ 'ServiceSetting' => [ 'shape' => 'ServiceSetting', ], ], ], 'HierarchyLevelLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'HierarchyTypeMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'IPAddress' => [ 'type' => 'string', 'max' => 46, 'min' => 1, ], 'ISO8601String' => [ 'type' => 'string', ], 'IamRole' => [ 'type' => 'string', 'max' => 64, ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}', ], 'IdempotentParameterMismatch' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'IncompatiblePolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InstallOverrideList' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^https://.+$|^s3://([^/]+)/(.*?([^/]+))$', ], 'InstanceAggregatedAssociationOverview' => [ 'type' => 'structure', 'members' => [ 'DetailedStatus' => [ 'shape' => 'StatusName', ], 'InstanceAssociationStatusAggregatedCount' => [ 'shape' => 'InstanceAssociationStatusAggregatedCount', ], ], ], 'InstanceAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Content' => [ 'shape' => 'DocumentContent', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], ], ], 'InstanceAssociationExecutionSummary' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'InstanceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceAssociation', ], ], 'InstanceAssociationOutputLocation' => [ 'type' => 'structure', 'members' => [ 'S3Location' => [ 'shape' => 'S3OutputLocation', ], ], ], 'InstanceAssociationOutputUrl' => [ 'type' => 'structure', 'members' => [ 'S3OutputUrl' => [ 'shape' => 'S3OutputUrl', ], ], ], 'InstanceAssociationStatusAggregatedCount' => [ 'type' => 'map', 'key' => [ 'shape' => 'StatusName', ], 'value' => [ 'shape' => 'InstanceCount', ], ], 'InstanceAssociationStatusInfo' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Name' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ExecutionDate' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'StatusName', ], 'DetailedStatus' => [ 'shape' => 'StatusName', ], 'ExecutionSummary' => [ 'shape' => 'InstanceAssociationExecutionSummary', ], 'ErrorCode' => [ 'shape' => 'AgentErrorCode', ], 'OutputUrl' => [ 'shape' => 'InstanceAssociationOutputUrl', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], ], ], 'InstanceAssociationStatusInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceAssociationStatusInfo', ], ], 'InstanceCount' => [ 'type' => 'integer', ], 'InstanceId' => [ 'type' => 'string', 'pattern' => '(^i-(\\w{8}|\\w{17})$)|(^mi-\\w{17}$)', ], 'InstanceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceId', ], 'max' => 50, 'min' => 0, ], 'InstanceInformation' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PingStatus' => [ 'shape' => 'PingStatus', ], 'LastPingDateTime' => [ 'shape' => 'DateTime', 'box' => true, ], 'AgentVersion' => [ 'shape' => 'Version', ], 'IsLatestVersion' => [ 'shape' => 'Boolean', 'box' => true, ], 'PlatformType' => [ 'shape' => 'PlatformType', ], 'PlatformName' => [ 'shape' => 'String', ], 'PlatformVersion' => [ 'shape' => 'String', ], 'ActivationId' => [ 'shape' => 'ActivationId', ], 'IamRole' => [ 'shape' => 'IamRole', ], 'RegistrationDate' => [ 'shape' => 'DateTime', 'box' => true, ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Name' => [ 'shape' => 'String', ], 'IPAddress' => [ 'shape' => 'IPAddress', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'AssociationStatus' => [ 'shape' => 'StatusName', ], 'LastAssociationExecutionDate' => [ 'shape' => 'DateTime', ], 'LastSuccessfulAssociationExecutionDate' => [ 'shape' => 'DateTime', ], 'AssociationOverview' => [ 'shape' => 'InstanceAggregatedAssociationOverview', ], 'SourceId' => [ 'shape' => 'SourceId', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'InstanceInformationFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'valueSet', ], 'members' => [ 'key' => [ 'shape' => 'InstanceInformationFilterKey', ], 'valueSet' => [ 'shape' => 'InstanceInformationFilterValueSet', ], ], ], 'InstanceInformationFilterKey' => [ 'type' => 'string', 'enum' => [ 'InstanceIds', 'AgentVersion', 'PingStatus', 'PlatformTypes', 'ActivationIds', 'IamRole', 'ResourceType', 'AssociationStatus', ], ], 'InstanceInformationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceInformationFilter', ], 'min' => 0, ], 'InstanceInformationFilterValue' => [ 'type' => 'string', 'min' => 1, ], 'InstanceInformationFilterValueSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceInformationFilterValue', ], 'max' => 100, 'min' => 1, ], 'InstanceInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceInformation', ], ], 'InstanceInformationStringFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'InstanceInformationStringFilterKey', ], 'Values' => [ 'shape' => 'InstanceInformationFilterValueSet', ], ], ], 'InstanceInformationStringFilterKey' => [ 'type' => 'string', 'min' => 1, ], 'InstanceInformationStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceInformationStringFilter', ], 'min' => 0, ], 'InstancePatchState' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PatchGroup', 'BaselineId', 'OperationStartTime', 'OperationEndTime', 'Operation', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], 'BaselineId' => [ 'shape' => 'BaselineId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'InstallOverrideList' => [ 'shape' => 'InstallOverrideList', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'InstalledCount' => [ 'shape' => 'PatchInstalledCount', ], 'InstalledOtherCount' => [ 'shape' => 'PatchInstalledOtherCount', ], 'InstalledPendingRebootCount' => [ 'shape' => 'PatchInstalledPendingRebootCount', 'box' => true, ], 'InstalledRejectedCount' => [ 'shape' => 'PatchInstalledRejectedCount', 'box' => true, ], 'MissingCount' => [ 'shape' => 'PatchMissingCount', ], 'FailedCount' => [ 'shape' => 'PatchFailedCount', ], 'UnreportedNotApplicableCount' => [ 'shape' => 'PatchUnreportedNotApplicableCount', 'box' => true, ], 'NotApplicableCount' => [ 'shape' => 'PatchNotApplicableCount', ], 'OperationStartTime' => [ 'shape' => 'DateTime', ], 'OperationEndTime' => [ 'shape' => 'DateTime', ], 'Operation' => [ 'shape' => 'PatchOperationType', ], 'LastNoRebootInstallOperationTime' => [ 'shape' => 'DateTime', ], 'RebootOption' => [ 'shape' => 'RebootOption', ], 'CriticalNonCompliantCount' => [ 'shape' => 'PatchCriticalNonCompliantCount', 'box' => true, ], 'SecurityNonCompliantCount' => [ 'shape' => 'PatchSecurityNonCompliantCount', 'box' => true, ], 'OtherNonCompliantCount' => [ 'shape' => 'PatchOtherNonCompliantCount', 'box' => true, ], ], ], 'InstancePatchStateFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', 'Type', ], 'members' => [ 'Key' => [ 'shape' => 'InstancePatchStateFilterKey', ], 'Values' => [ 'shape' => 'InstancePatchStateFilterValues', ], 'Type' => [ 'shape' => 'InstancePatchStateOperatorType', ], ], ], 'InstancePatchStateFilterKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'InstancePatchStateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePatchStateFilter', ], 'max' => 4, 'min' => 0, ], 'InstancePatchStateFilterValue' => [ 'type' => 'string', ], 'InstancePatchStateFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePatchStateFilterValue', ], 'max' => 1, 'min' => 1, ], 'InstancePatchStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePatchState', ], ], 'InstancePatchStateOperatorType' => [ 'type' => 'string', 'enum' => [ 'Equal', 'NotEqual', 'LessThan', 'GreaterThan', ], ], 'InstancePatchStatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePatchState', ], 'max' => 5, 'min' => 1, ], 'InstanceTagName' => [ 'type' => 'string', 'max' => 255, ], 'InstancesCount' => [ 'type' => 'integer', ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'InvalidActivation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidActivationId' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAggregatorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAllowedPatternException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAssociationVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAutomationExecutionParametersException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAutomationSignalException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidAutomationStatusUpdateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidCommandId' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeleteInventoryParametersException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDeletionIdException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocument' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocumentContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocumentOperation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocumentSchemaVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocumentType' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidDocumentVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidFilter' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidFilterKey' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFilterOption' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidFilterValue' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidInstanceId' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidInstanceInformationFilterValue' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidInventoryGroupException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidInventoryItemContextException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidInventoryRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidItemContentException' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidKeyId' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidNextToken' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidNotificationConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidOptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidOutputFolder' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOutputLocation' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameters' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidPermissionType' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidPluginName' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPolicyAttributeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidPolicyTypeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidResourceId' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidResourceType' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidResultAttributeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidRole' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidSchedule' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidTag' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidTarget' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidTargetMaps' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidTypeNameException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidUpdate' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InventoryAggregator' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'InventoryAggregatorExpression', ], 'Aggregators' => [ 'shape' => 'InventoryAggregatorList', ], 'Groups' => [ 'shape' => 'InventoryGroupList', ], ], ], 'InventoryAggregatorExpression' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'InventoryAggregatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryAggregator', ], 'max' => 10, 'min' => 1, ], 'InventoryAttributeDataType' => [ 'type' => 'string', 'enum' => [ 'string', 'number', ], ], 'InventoryDeletionLastStatusMessage' => [ 'type' => 'string', ], 'InventoryDeletionLastStatusUpdateTime' => [ 'type' => 'timestamp', ], 'InventoryDeletionStartTime' => [ 'type' => 'timestamp', ], 'InventoryDeletionStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Complete', ], ], 'InventoryDeletionStatusItem' => [ 'type' => 'structure', 'members' => [ 'DeletionId' => [ 'shape' => 'UUID', ], 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'DeletionStartTime' => [ 'shape' => 'InventoryDeletionStartTime', ], 'LastStatus' => [ 'shape' => 'InventoryDeletionStatus', ], 'LastStatusMessage' => [ 'shape' => 'InventoryDeletionLastStatusMessage', ], 'DeletionSummary' => [ 'shape' => 'InventoryDeletionSummary', ], 'LastStatusUpdateTime' => [ 'shape' => 'InventoryDeletionLastStatusUpdateTime', ], ], ], 'InventoryDeletionSummary' => [ 'type' => 'structure', 'members' => [ 'TotalCount' => [ 'shape' => 'TotalCount', ], 'RemainingCount' => [ 'shape' => 'RemainingCount', ], 'SummaryItems' => [ 'shape' => 'InventoryDeletionSummaryItems', ], ], ], 'InventoryDeletionSummaryItem' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'InventoryItemSchemaVersion', ], 'Count' => [ 'shape' => 'ResourceCount', ], 'RemainingCount' => [ 'shape' => 'RemainingCount', ], ], ], 'InventoryDeletionSummaryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryDeletionSummaryItem', ], ], 'InventoryDeletionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryDeletionStatusItem', ], ], 'InventoryFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'InventoryFilterKey', ], 'Values' => [ 'shape' => 'InventoryFilterValueList', ], 'Type' => [ 'shape' => 'InventoryQueryOperatorType', ], ], ], 'InventoryFilterKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'InventoryFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryFilter', ], 'max' => 5, 'min' => 1, ], 'InventoryFilterValue' => [ 'type' => 'string', ], 'InventoryFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryFilterValue', ], 'max' => 40, 'min' => 1, ], 'InventoryGroup' => [ 'type' => 'structure', 'required' => [ 'Name', 'Filters', ], 'members' => [ 'Name' => [ 'shape' => 'InventoryGroupName', ], 'Filters' => [ 'shape' => 'InventoryFilterList', ], ], ], 'InventoryGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryGroup', ], 'max' => 15, 'min' => 1, ], 'InventoryGroupName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'InventoryItem' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'SchemaVersion', 'CaptureTime', ], 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'SchemaVersion' => [ 'shape' => 'InventoryItemSchemaVersion', ], 'CaptureTime' => [ 'shape' => 'InventoryItemCaptureTime', ], 'ContentHash' => [ 'shape' => 'InventoryItemContentHash', ], 'Content' => [ 'shape' => 'InventoryItemEntryList', ], 'Context' => [ 'shape' => 'InventoryItemContentContext', ], ], ], 'InventoryItemAttribute' => [ 'type' => 'structure', 'required' => [ 'Name', 'DataType', ], 'members' => [ 'Name' => [ 'shape' => 'InventoryItemAttributeName', ], 'DataType' => [ 'shape' => 'InventoryAttributeDataType', ], ], ], 'InventoryItemAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryItemAttribute', ], 'max' => 50, 'min' => 1, ], 'InventoryItemAttributeName' => [ 'type' => 'string', ], 'InventoryItemCaptureTime' => [ 'type' => 'string', 'pattern' => '^(20)[0-9][0-9]-(0[1-9]|1[012])-([12][0-9]|3[01]|0[1-9])(T)(2[0-3]|[0-1][0-9])(:[0-5][0-9])(:[0-5][0-9])(Z)$', ], 'InventoryItemContentContext' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], 'max' => 50, 'min' => 0, ], 'InventoryItemContentHash' => [ 'type' => 'string', 'max' => 256, ], 'InventoryItemEntry' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], 'max' => 50, 'min' => 0, ], 'InventoryItemEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryItemEntry', ], 'max' => 10000, 'min' => 0, ], 'InventoryItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryItem', ], 'max' => 30, 'min' => 1, ], 'InventoryItemSchema' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'Attributes', ], 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Version' => [ 'shape' => 'InventoryItemSchemaVersion', ], 'Attributes' => [ 'shape' => 'InventoryItemAttributeList', ], 'DisplayName' => [ 'shape' => 'InventoryTypeDisplayName', ], ], ], 'InventoryItemSchemaResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryItemSchema', ], ], 'InventoryItemSchemaVersion' => [ 'type' => 'string', 'pattern' => '^([0-9]{1,6})(\\.[0-9]{1,6})$', ], 'InventoryItemTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(AWS|Custom):.*$', ], 'InventoryItemTypeNameFilter' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'InventoryQueryOperatorType' => [ 'type' => 'string', 'enum' => [ 'Equal', 'NotEqual', 'BeginWith', 'LessThan', 'GreaterThan', 'Exists', ], ], 'InventoryResultEntity' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InventoryResultEntityId', ], 'Data' => [ 'shape' => 'InventoryResultItemMap', ], ], ], 'InventoryResultEntityId' => [ 'type' => 'string', ], 'InventoryResultEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryResultEntity', ], ], 'InventoryResultItem' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'SchemaVersion', 'Content', ], 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'SchemaVersion' => [ 'shape' => 'InventoryItemSchemaVersion', ], 'CaptureTime' => [ 'shape' => 'InventoryItemCaptureTime', ], 'ContentHash' => [ 'shape' => 'InventoryItemContentHash', ], 'Content' => [ 'shape' => 'InventoryItemEntryList', ], ], ], 'InventoryResultItemKey' => [ 'type' => 'string', ], 'InventoryResultItemMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'InventoryResultItemKey', ], 'value' => [ 'shape' => 'InventoryResultItem', ], ], 'InventorySchemaDeleteOption' => [ 'type' => 'string', 'enum' => [ 'DisableSchema', 'DeleteSchema', ], ], 'InventoryTypeDisplayName' => [ 'type' => 'string', ], 'InvocationDoesNotExist' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvocationTraceOutput' => [ 'type' => 'string', 'max' => 2500, ], 'IsSubTypeSchema' => [ 'type' => 'boolean', ], 'ItemContentMismatchException' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ItemSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'LabelParameterVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Labels', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'ParameterVersion' => [ 'shape' => 'PSParameterVersion', 'box' => true, ], 'Labels' => [ 'shape' => 'ParameterLabelList', ], ], ], 'LabelParameterVersionResult' => [ 'type' => 'structure', 'members' => [ 'InvalidLabels' => [ 'shape' => 'ParameterLabelList', ], 'ParameterVersion' => [ 'shape' => 'PSParameterVersion', ], ], ], 'LastResourceDataSyncMessage' => [ 'type' => 'string', ], 'LastResourceDataSyncStatus' => [ 'type' => 'string', 'enum' => [ 'Successful', 'Failed', 'InProgress', ], ], 'LastResourceDataSyncTime' => [ 'type' => 'timestamp', ], 'LastSuccessfulResourceDataSyncTime' => [ 'type' => 'timestamp', ], 'ListAssociationVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociationVersionsResult' => [ 'type' => 'structure', 'members' => [ 'AssociationVersions' => [ 'shape' => 'AssociationVersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'AssociationFilterList' => [ 'shape' => 'AssociationFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'AssociationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCommandInvocationsRequest' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'CommandMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'CommandFilterList', ], 'Details' => [ 'shape' => 'Boolean', ], ], ], 'ListCommandInvocationsResult' => [ 'type' => 'structure', 'members' => [ 'CommandInvocations' => [ 'shape' => 'CommandInvocationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCommandsRequest' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'CommandId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'CommandMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'CommandFilterList', ], ], ], 'ListCommandsResult' => [ 'type' => 'structure', 'members' => [ 'Commands' => [ 'shape' => 'CommandList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListComplianceItemsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ComplianceStringFilterList', ], 'ResourceIds' => [ 'shape' => 'ComplianceResourceIdList', ], 'ResourceTypes' => [ 'shape' => 'ComplianceResourceTypeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListComplianceItemsResult' => [ 'type' => 'structure', 'members' => [ 'ComplianceItems' => [ 'shape' => 'ComplianceItemList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListComplianceSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ComplianceStringFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListComplianceSummariesResult' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummaryItems' => [ 'shape' => 'ComplianceSummaryItemList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentMetadataHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Metadata', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Metadata' => [ 'shape' => 'DocumentMetadataEnum', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListDocumentMetadataHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Author' => [ 'shape' => 'DocumentAuthor', ], 'Metadata' => [ 'shape' => 'DocumentMetadataResponseInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentVersionsResult' => [ 'type' => 'structure', 'members' => [ 'DocumentVersions' => [ 'shape' => 'DocumentVersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentsRequest' => [ 'type' => 'structure', 'members' => [ 'DocumentFilterList' => [ 'shape' => 'DocumentFilterList', ], 'Filters' => [ 'shape' => 'DocumentKeyValuesFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentsResult' => [ 'type' => 'structure', 'members' => [ 'DocumentIdentifiers' => [ 'shape' => 'DocumentIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInventoryEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'TypeName', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Filters' => [ 'shape' => 'InventoryFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListInventoryEntriesResult' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'SchemaVersion' => [ 'shape' => 'InventoryItemSchemaVersion', ], 'CaptureTime' => [ 'shape' => 'InventoryItemCaptureTime', ], 'Entries' => [ 'shape' => 'InventoryItemEntryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOpsItemEventsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'OpsItemEventFilters', ], 'MaxResults' => [ 'shape' => 'OpsItemEventMaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListOpsItemEventsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Summaries' => [ 'shape' => 'OpsItemEventSummaries', ], ], ], 'ListOpsItemRelatedItemsRequest' => [ 'type' => 'structure', 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'Filters' => [ 'shape' => 'OpsItemRelatedItemsFilters', ], 'MaxResults' => [ 'shape' => 'OpsItemRelatedItemsMaxResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListOpsItemRelatedItemsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Summaries' => [ 'shape' => 'OpsItemRelatedItemSummaries', ], ], ], 'ListOpsMetadataMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListOpsMetadataRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'OpsMetadataFilterList', ], 'MaxResults' => [ 'shape' => 'ListOpsMetadataMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOpsMetadataResult' => [ 'type' => 'structure', 'members' => [ 'OpsMetadataList' => [ 'shape' => 'OpsMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceComplianceSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ComplianceStringFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListResourceComplianceSummariesResult' => [ 'type' => 'structure', 'members' => [ 'ResourceComplianceSummaryItems' => [ 'shape' => 'ResourceComplianceSummaryItemList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceDataSyncRequest' => [ 'type' => 'structure', 'members' => [ 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListResourceDataSyncResult' => [ 'type' => 'structure', 'members' => [ 'ResourceDataSyncItems' => [ 'shape' => 'ResourceDataSyncItemList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceId', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeForTagging', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'LoggingInfo' => [ 'type' => 'structure', 'required' => [ 'S3BucketName', 'S3Region', ], 'members' => [ 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'S3Region' => [ 'shape' => 'S3Region', ], ], ], 'Long' => [ 'type' => 'long', ], 'MaintenanceWindowAllowUnassociatedTargets' => [ 'type' => 'boolean', ], 'MaintenanceWindowAutomationParameters' => [ 'type' => 'structure', 'members' => [ 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'Parameters' => [ 'shape' => 'AutomationParameterMap', ], ], ], 'MaintenanceWindowCutoff' => [ 'type' => 'integer', 'max' => 23, 'min' => 0, ], 'MaintenanceWindowDescription' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'MaintenanceWindowDurationHours' => [ 'type' => 'integer', 'max' => 24, 'min' => 1, ], 'MaintenanceWindowEnabled' => [ 'type' => 'boolean', ], 'MaintenanceWindowExecution' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], ], ], 'MaintenanceWindowExecutionId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}$', ], 'MaintenanceWindowExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowExecution', ], ], 'MaintenanceWindowExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'TIMED_OUT', 'CANCELLING', 'CANCELLED', 'SKIPPED_OVERLAPPING', ], ], 'MaintenanceWindowExecutionStatusDetails' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'MaintenanceWindowExecutionTaskExecutionId' => [ 'type' => 'string', ], 'MaintenanceWindowExecutionTaskId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}$', ], 'MaintenanceWindowExecutionTaskIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], ], 'MaintenanceWindowExecutionTaskIdentity' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'TaskType' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], ], ], 'MaintenanceWindowExecutionTaskIdentityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowExecutionTaskIdentity', ], ], 'MaintenanceWindowExecutionTaskInvocationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}$', ], 'MaintenanceWindowExecutionTaskInvocationIdentity' => [ 'type' => 'structure', 'members' => [ 'WindowExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionId', ], 'TaskExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskId', ], 'InvocationId' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationId', ], 'ExecutionId' => [ 'shape' => 'MaintenanceWindowExecutionTaskExecutionId', ], 'TaskType' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'Parameters' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationParameters', ], 'Status' => [ 'shape' => 'MaintenanceWindowExecutionStatus', ], 'StatusDetails' => [ 'shape' => 'MaintenanceWindowExecutionStatusDetails', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTaskTargetId', ], ], ], 'MaintenanceWindowExecutionTaskInvocationIdentityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowExecutionTaskInvocationIdentity', ], ], 'MaintenanceWindowExecutionTaskInvocationParameters' => [ 'type' => 'string', 'sensitive' => true, ], 'MaintenanceWindowFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'MaintenanceWindowFilterKey', ], 'Values' => [ 'shape' => 'MaintenanceWindowFilterValues', ], ], ], 'MaintenanceWindowFilterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MaintenanceWindowFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowFilter', ], 'max' => 5, 'min' => 0, ], 'MaintenanceWindowFilterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MaintenanceWindowFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowFilterValue', ], ], 'MaintenanceWindowId' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^mw-[0-9a-f]{17}$', ], 'MaintenanceWindowIdentity' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'Enabled' => [ 'shape' => 'MaintenanceWindowEnabled', ], 'Duration' => [ 'shape' => 'MaintenanceWindowDurationHours', ], 'Cutoff' => [ 'shape' => 'MaintenanceWindowCutoff', ], 'Schedule' => [ 'shape' => 'MaintenanceWindowSchedule', ], 'ScheduleTimezone' => [ 'shape' => 'MaintenanceWindowTimezone', ], 'ScheduleOffset' => [ 'shape' => 'MaintenanceWindowOffset', 'box' => true, ], 'EndDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'StartDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'NextExecutionTime' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], ], ], 'MaintenanceWindowIdentityForTarget' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], ], ], 'MaintenanceWindowIdentityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowIdentity', ], ], 'MaintenanceWindowLambdaClientContext' => [ 'type' => 'string', 'max' => 8000, 'min' => 1, ], 'MaintenanceWindowLambdaParameters' => [ 'type' => 'structure', 'members' => [ 'ClientContext' => [ 'shape' => 'MaintenanceWindowLambdaClientContext', ], 'Qualifier' => [ 'shape' => 'MaintenanceWindowLambdaQualifier', ], 'Payload' => [ 'shape' => 'MaintenanceWindowLambdaPayload', ], ], ], 'MaintenanceWindowLambdaPayload' => [ 'type' => 'blob', 'max' => 4096, 'sensitive' => true, ], 'MaintenanceWindowLambdaQualifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MaintenanceWindowMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 10, ], 'MaintenanceWindowName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^[a-zA-Z0-9_\\-.]{3,128}$', ], 'MaintenanceWindowOffset' => [ 'type' => 'integer', 'max' => 6, 'min' => 1, ], 'MaintenanceWindowResourceType' => [ 'type' => 'string', 'enum' => [ 'INSTANCE', 'RESOURCE_GROUP', ], ], 'MaintenanceWindowRunCommandParameters' => [ 'type' => 'structure', 'members' => [ 'Comment' => [ 'shape' => 'Comment', ], 'CloudWatchOutputConfig' => [ 'shape' => 'CloudWatchOutputConfig', ], 'DocumentHash' => [ 'shape' => 'DocumentHash', ], 'DocumentHashType' => [ 'shape' => 'DocumentHashType', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'NotificationConfig' => [ 'shape' => 'NotificationConfig', ], 'OutputS3BucketName' => [ 'shape' => 'S3BucketName', ], 'OutputS3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'TimeoutSeconds' => [ 'shape' => 'TimeoutSeconds', 'box' => true, ], ], ], 'MaintenanceWindowSchedule' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MaintenanceWindowSearchMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'MaintenanceWindowStepFunctionsInput' => [ 'type' => 'string', 'max' => 4096, 'sensitive' => true, ], 'MaintenanceWindowStepFunctionsName' => [ 'type' => 'string', 'max' => 80, 'min' => 1, ], 'MaintenanceWindowStepFunctionsParameters' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'MaintenanceWindowStepFunctionsInput', ], 'Name' => [ 'shape' => 'MaintenanceWindowStepFunctionsName', ], ], ], 'MaintenanceWindowStringDateTime' => [ 'type' => 'string', ], 'MaintenanceWindowTarget' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], 'ResourceType' => [ 'shape' => 'MaintenanceWindowResourceType', ], 'Targets' => [ 'shape' => 'Targets', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], ], ], 'MaintenanceWindowTargetId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}$', ], 'MaintenanceWindowTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowTarget', ], ], 'MaintenanceWindowTask' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'Type' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'Targets' => [ 'shape' => 'Targets', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'CutoffBehavior' => [ 'shape' => 'MaintenanceWindowTaskCutoffBehavior', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'MaintenanceWindowTaskArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'MaintenanceWindowTaskCutoffBehavior' => [ 'type' => 'string', 'enum' => [ 'CONTINUE_TASK', 'CANCEL_TASK', ], ], 'MaintenanceWindowTaskId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}$', ], 'MaintenanceWindowTaskInvocationParameters' => [ 'type' => 'structure', 'members' => [ 'RunCommand' => [ 'shape' => 'MaintenanceWindowRunCommandParameters', ], 'Automation' => [ 'shape' => 'MaintenanceWindowAutomationParameters', ], 'StepFunctions' => [ 'shape' => 'MaintenanceWindowStepFunctionsParameters', ], 'Lambda' => [ 'shape' => 'MaintenanceWindowLambdaParameters', ], ], ], 'MaintenanceWindowTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowTask', ], ], 'MaintenanceWindowTaskParameterName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'MaintenanceWindowTaskParameterValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'MaintenanceWindowTaskParameterValueExpression' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'MaintenanceWindowTaskParameterValueList', ], ], 'sensitive' => true, ], 'MaintenanceWindowTaskParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowTaskParameterValue', ], 'sensitive' => true, ], 'MaintenanceWindowTaskParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'MaintenanceWindowTaskParameterName', ], 'value' => [ 'shape' => 'MaintenanceWindowTaskParameterValueExpression', ], 'sensitive' => true, ], 'MaintenanceWindowTaskParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'sensitive' => true, ], 'MaintenanceWindowTaskPriority' => [ 'type' => 'integer', 'min' => 0, ], 'MaintenanceWindowTaskTargetId' => [ 'type' => 'string', 'max' => 36, ], 'MaintenanceWindowTaskType' => [ 'type' => 'string', 'enum' => [ 'RUN_COMMAND', 'AUTOMATION', 'STEP_FUNCTIONS', 'LAMBDA', ], ], 'MaintenanceWindowTimezone' => [ 'type' => 'string', ], 'MaintenanceWindowsForTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindowIdentityForTarget', ], ], 'MalformedResourcePolicyDocumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ManagedInstanceId' => [ 'type' => 'string', 'max' => 124, 'min' => 20, 'pattern' => '(^mi-[0-9a-f]{17}$)|(^eks_c:[0-9A-Za-z][A-Za-z0-9\\-_]{0,99}_\\w{17}$)', ], 'MaxConcurrency' => [ 'type' => 'string', 'max' => 7, 'min' => 1, 'pattern' => '^([1-9][0-9]*|[1-9][0-9]%|[1-9]%|100%)$', ], 'MaxDocumentSizeExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'MaxErrors' => [ 'type' => 'string', 'max' => 7, 'min' => 1, 'pattern' => '^([1-9][0-9]*|[0]|[1-9][0-9]%|[0-9]%|100%)$', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxResultsEC2Compatible' => [ 'type' => 'integer', 'max' => 50, 'min' => 5, ], 'MaxSessionDuration' => [ 'type' => 'string', 'max' => 4, 'min' => 1, 'pattern' => '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1[0-4][0-3][0-9]|1440)$', ], 'MetadataKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'MetadataKeysToDeleteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataKey', ], 'max' => 10, 'min' => 1, ], 'MetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetadataKey', ], 'value' => [ 'shape' => 'MetadataValue', ], 'max' => 5, 'min' => 1, ], 'MetadataValue' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'MetadataValueString', ], ], ], 'MetadataValueString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ModifyDocumentPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PermissionType', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'PermissionType' => [ 'shape' => 'DocumentPermissionType', ], 'AccountIdsToAdd' => [ 'shape' => 'AccountIdList', ], 'AccountIdsToRemove' => [ 'shape' => 'AccountIdList', ], 'SharedDocumentVersion' => [ 'shape' => 'SharedDocumentVersion', ], ], ], 'ModifyDocumentPermissionResponse' => [ 'type' => 'structure', 'members' => [], ], 'NextToken' => [ 'type' => 'string', ], 'NonCompliantSummary' => [ 'type' => 'structure', 'members' => [ 'NonCompliantCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'SeveritySummary' => [ 'shape' => 'SeveritySummary', ], ], ], 'NormalStringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'NotificationArn' => [ 'type' => 'string', ], 'NotificationConfig' => [ 'type' => 'structure', 'members' => [ 'NotificationArn' => [ 'shape' => 'NotificationArn', ], 'NotificationEvents' => [ 'shape' => 'NotificationEventList', ], 'NotificationType' => [ 'shape' => 'NotificationType', ], ], ], 'NotificationEvent' => [ 'type' => 'string', 'enum' => [ 'All', 'InProgress', 'Success', 'TimedOut', 'Cancelled', 'Failed', ], ], 'NotificationEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationEvent', ], ], 'NotificationType' => [ 'type' => 'string', 'enum' => [ 'Command', 'Invocation', ], ], 'OperatingSystem' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'AMAZON_LINUX', 'AMAZON_LINUX_2', 'AMAZON_LINUX_2022', 'UBUNTU', 'REDHAT_ENTERPRISE_LINUX', 'SUSE', 'CENTOS', 'ORACLE_LINUX', 'DEBIAN', 'MACOS', 'RASPBIAN', 'ROCKY_LINUX', 'ALMA_LINUX', 'AMAZON_LINUX_2023', ], ], 'OpsAggregator' => [ 'type' => 'structure', 'members' => [ 'AggregatorType' => [ 'shape' => 'OpsAggregatorType', ], 'TypeName' => [ 'shape' => 'OpsDataTypeName', ], 'AttributeName' => [ 'shape' => 'OpsDataAttributeName', ], 'Values' => [ 'shape' => 'OpsAggregatorValueMap', ], 'Filters' => [ 'shape' => 'OpsFilterList', ], 'Aggregators' => [ 'shape' => 'OpsAggregatorList', ], ], ], 'OpsAggregatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsAggregator', ], 'max' => 12, 'min' => 1, ], 'OpsAggregatorType' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^(range|count|sum)', ], 'OpsAggregatorValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'OpsAggregatorValueKey' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'OpsAggregatorValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OpsAggregatorValueKey', ], 'value' => [ 'shape' => 'OpsAggregatorValue', ], 'max' => 5, 'min' => 0, ], 'OpsDataAttributeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'OpsDataTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(AWS|Custom):.*$', ], 'OpsEntity' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'OpsEntityId', ], 'Data' => [ 'shape' => 'OpsEntityItemMap', ], ], ], 'OpsEntityId' => [ 'type' => 'string', ], 'OpsEntityItem' => [ 'type' => 'structure', 'members' => [ 'CaptureTime' => [ 'shape' => 'OpsEntityItemCaptureTime', ], 'Content' => [ 'shape' => 'OpsEntityItemEntryList', ], ], ], 'OpsEntityItemCaptureTime' => [ 'type' => 'string', 'pattern' => '^(20)[0-9][0-9]-(0[1-9]|1[012])-([12][0-9]|3[01]|0[1-9])(T)(2[0-3]|[0-1][0-9])(:[0-5][0-9])(:[0-5][0-9])(Z)$', ], 'OpsEntityItemEntry' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], 'max' => 50, 'min' => 0, ], 'OpsEntityItemEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsEntityItemEntry', ], 'max' => 10000, 'min' => 0, ], 'OpsEntityItemKey' => [ 'type' => 'string', ], 'OpsEntityItemMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OpsEntityItemKey', ], 'value' => [ 'shape' => 'OpsEntityItem', ], ], 'OpsEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsEntity', ], ], 'OpsFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'OpsFilterKey', ], 'Values' => [ 'shape' => 'OpsFilterValueList', ], 'Type' => [ 'shape' => 'OpsFilterOperatorType', ], ], ], 'OpsFilterKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'OpsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsFilter', ], 'max' => 5, 'min' => 1, ], 'OpsFilterOperatorType' => [ 'type' => 'string', 'enum' => [ 'Equal', 'NotEqual', 'BeginWith', 'LessThan', 'GreaterThan', 'Exists', ], ], 'OpsFilterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'OpsFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsFilterValue', ], 'max' => 40, 'min' => 1, ], 'OpsItem' => [ 'type' => 'structure', 'members' => [ 'CreatedBy' => [ 'shape' => 'String', ], 'OpsItemType' => [ 'shape' => 'OpsItemType', ], 'CreatedTime' => [ 'shape' => 'DateTime', ], 'Description' => [ 'shape' => 'OpsItemDescription', ], 'LastModifiedBy' => [ 'shape' => 'String', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'Notifications' => [ 'shape' => 'OpsItemNotifications', ], 'Priority' => [ 'shape' => 'OpsItemPriority', ], 'RelatedOpsItems' => [ 'shape' => 'RelatedOpsItems', ], 'Status' => [ 'shape' => 'OpsItemStatus', ], 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'Version' => [ 'shape' => 'String', ], 'Title' => [ 'shape' => 'OpsItemTitle', ], 'Source' => [ 'shape' => 'OpsItemSource', ], 'OperationalData' => [ 'shape' => 'OpsItemOperationalData', ], 'Category' => [ 'shape' => 'OpsItemCategory', ], 'Severity' => [ 'shape' => 'OpsItemSeverity', ], 'ActualStartTime' => [ 'shape' => 'DateTime', ], 'ActualEndTime' => [ 'shape' => 'DateTime', ], 'PlannedStartTime' => [ 'shape' => 'DateTime', ], 'PlannedEndTime' => [ 'shape' => 'DateTime', ], 'OpsItemArn' => [ 'shape' => 'OpsItemArn', ], ], ], 'OpsItemAccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemAccountId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'OpsItemAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'OpsItemId' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:ssm:[a-z0-9-\\.]{0,63}:[0-9]{12}:opsitem.*', ], 'OpsItemCategory' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsItemConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemDataKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsItemDataType' => [ 'type' => 'string', 'enum' => [ 'SearchableString', 'String', ], ], 'OpsItemDataValue' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'OpsItemDataValueString', ], 'Type' => [ 'shape' => 'OpsItemDataType', ], ], ], 'OpsItemDataValueString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'OpsItemDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'OpsItemEventFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', 'Operator', ], 'members' => [ 'Key' => [ 'shape' => 'OpsItemEventFilterKey', ], 'Values' => [ 'shape' => 'OpsItemEventFilterValues', ], 'Operator' => [ 'shape' => 'OpsItemEventFilterOperator', ], ], ], 'OpsItemEventFilterKey' => [ 'type' => 'string', 'enum' => [ 'OpsItemId', ], ], 'OpsItemEventFilterOperator' => [ 'type' => 'string', 'enum' => [ 'Equal', ], ], 'OpsItemEventFilterValue' => [ 'type' => 'string', 'max' => 15, 'min' => 1, 'pattern' => '^(oi)-[0-9a-f]{12}$', ], 'OpsItemEventFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemEventFilterValue', ], ], 'OpsItemEventFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemEventFilter', ], ], 'OpsItemEventMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'OpsItemEventSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemEventSummary', ], ], 'OpsItemEventSummary' => [ 'type' => 'structure', 'members' => [ 'OpsItemId' => [ 'shape' => 'String', ], 'EventId' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'DetailType' => [ 'shape' => 'String', ], 'Detail' => [ 'shape' => 'String', ], 'CreatedBy' => [ 'shape' => 'OpsItemIdentity', ], 'CreatedTime' => [ 'shape' => 'DateTime', ], ], ], 'OpsItemFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', 'Operator', ], 'members' => [ 'Key' => [ 'shape' => 'OpsItemFilterKey', ], 'Values' => [ 'shape' => 'OpsItemFilterValues', ], 'Operator' => [ 'shape' => 'OpsItemFilterOperator', ], ], ], 'OpsItemFilterKey' => [ 'type' => 'string', 'enum' => [ 'Status', 'CreatedBy', 'Source', 'Priority', 'Title', 'OpsItemId', 'CreatedTime', 'LastModifiedTime', 'ActualStartTime', 'ActualEndTime', 'PlannedStartTime', 'PlannedEndTime', 'OperationalData', 'OperationalDataKey', 'OperationalDataValue', 'ResourceId', 'AutomationId', 'Category', 'Severity', 'OpsItemType', 'ChangeRequestByRequesterArn', 'ChangeRequestByRequesterName', 'ChangeRequestByApproverArn', 'ChangeRequestByApproverName', 'ChangeRequestByTemplate', 'ChangeRequestByTargetsResourceGroup', 'InsightByType', 'AccountId', ], ], 'OpsItemFilterOperator' => [ 'type' => 'string', 'enum' => [ 'Equal', 'Contains', 'GreaterThan', 'LessThan', ], ], 'OpsItemFilterValue' => [ 'type' => 'string', ], 'OpsItemFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemFilterValue', ], ], 'OpsItemFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemFilter', ], ], 'OpsItemId' => [ 'type' => 'string', 'pattern' => '^(oi)-[0-9a-f]{12}$', ], 'OpsItemIdentity' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], ], ], 'OpsItemInvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'ParameterNames' => [ 'shape' => 'OpsItemParameterNamesList', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'ResourceTypes' => [ 'shape' => 'OpsItemParameterNamesList', ], 'Limit' => [ 'shape' => 'Integer', ], 'LimitType' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'OpsItemNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemNotification' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], ], ], 'OpsItemNotifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemNotification', ], ], 'OpsItemOperationalData' => [ 'type' => 'map', 'key' => [ 'shape' => 'OpsItemDataKey', ], 'value' => [ 'shape' => 'OpsItemDataValue', ], ], 'OpsItemOpsDataKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'OpsItemParameterNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'OpsItemPriority' => [ 'type' => 'integer', 'max' => 5, 'min' => 1, ], 'OpsItemRelatedItemAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceUri' => [ 'shape' => 'OpsItemRelatedItemAssociationResourceUri', ], 'OpsItemId' => [ 'shape' => 'OpsItemId', ], ], 'exception' => true, ], 'OpsItemRelatedItemAssociationId' => [ 'type' => 'string', ], 'OpsItemRelatedItemAssociationNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsItemRelatedItemAssociationResourceType' => [ 'type' => 'string', ], 'OpsItemRelatedItemAssociationResourceUri' => [ 'type' => 'string', ], 'OpsItemRelatedItemAssociationType' => [ 'type' => 'string', ], 'OpsItemRelatedItemSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemRelatedItemSummary', ], ], 'OpsItemRelatedItemSummary' => [ 'type' => 'structure', 'members' => [ 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'AssociationId' => [ 'shape' => 'OpsItemRelatedItemAssociationId', ], 'ResourceType' => [ 'shape' => 'OpsItemRelatedItemAssociationResourceType', ], 'AssociationType' => [ 'shape' => 'OpsItemRelatedItemAssociationType', ], 'ResourceUri' => [ 'shape' => 'OpsItemRelatedItemAssociationResourceUri', ], 'CreatedBy' => [ 'shape' => 'OpsItemIdentity', ], 'CreatedTime' => [ 'shape' => 'DateTime', ], 'LastModifiedBy' => [ 'shape' => 'OpsItemIdentity', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], ], ], 'OpsItemRelatedItemsFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', 'Operator', ], 'members' => [ 'Key' => [ 'shape' => 'OpsItemRelatedItemsFilterKey', ], 'Values' => [ 'shape' => 'OpsItemRelatedItemsFilterValues', ], 'Operator' => [ 'shape' => 'OpsItemRelatedItemsFilterOperator', ], ], ], 'OpsItemRelatedItemsFilterKey' => [ 'type' => 'string', 'enum' => [ 'ResourceType', 'AssociationId', 'ResourceUri', ], ], 'OpsItemRelatedItemsFilterOperator' => [ 'type' => 'string', 'enum' => [ 'Equal', ], ], 'OpsItemRelatedItemsFilterValue' => [ 'type' => 'string', ], 'OpsItemRelatedItemsFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemRelatedItemsFilterValue', ], ], 'OpsItemRelatedItemsFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemRelatedItemsFilter', ], ], 'OpsItemRelatedItemsMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'OpsItemSeverity' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsItemSource' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsItemStatus' => [ 'type' => 'string', 'enum' => [ 'Open', 'InProgress', 'Resolved', 'Pending', 'TimedOut', 'Cancelling', 'Cancelled', 'Failed', 'CompletedWithSuccess', 'CompletedWithFailure', 'Scheduled', 'RunbookInProgress', 'PendingChangeCalendarOverride', 'ChangeCalendarOverrideApproved', 'ChangeCalendarOverrideRejected', 'PendingApproval', 'Approved', 'Rejected', 'Closed', ], ], 'OpsItemSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsItemSummary', ], ], 'OpsItemSummary' => [ 'type' => 'structure', 'members' => [ 'CreatedBy' => [ 'shape' => 'String', ], 'CreatedTime' => [ 'shape' => 'DateTime', ], 'LastModifiedBy' => [ 'shape' => 'String', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'Priority' => [ 'shape' => 'OpsItemPriority', ], 'Source' => [ 'shape' => 'OpsItemSource', ], 'Status' => [ 'shape' => 'OpsItemStatus', ], 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'Title' => [ 'shape' => 'OpsItemTitle', ], 'OperationalData' => [ 'shape' => 'OpsItemOperationalData', ], 'Category' => [ 'shape' => 'OpsItemCategory', ], 'Severity' => [ 'shape' => 'OpsItemSeverity', ], 'OpsItemType' => [ 'shape' => 'OpsItemType', ], 'ActualStartTime' => [ 'shape' => 'DateTime', ], 'ActualEndTime' => [ 'shape' => 'DateTime', ], 'PlannedStartTime' => [ 'shape' => 'DateTime', ], 'PlannedEndTime' => [ 'shape' => 'DateTime', ], ], ], 'OpsItemTitle' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsItemType' => [ 'type' => 'string', ], 'OpsMetadata' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'OpsMetadataResourceId', ], 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LastModifiedUser' => [ 'shape' => 'String', ], 'CreationDate' => [ 'shape' => 'DateTime', ], ], ], 'OpsMetadataAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsMetadataArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:ssm:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:opsmetadata\\/([a-zA-Z0-9-_\\.\\/]*)', ], 'OpsMetadataFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'OpsMetadataFilterKey', ], 'Values' => [ 'shape' => 'OpsMetadataFilterValueList', ], ], ], 'OpsMetadataFilterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsMetadataFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsMetadataFilter', ], 'max' => 10, 'min' => 0, ], 'OpsMetadataFilterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'OpsMetadataFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsMetadataFilterValue', ], 'max' => 10, 'min' => 1, ], 'OpsMetadataInvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsMetadataKeyLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsMetadataLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsMetadata', ], 'max' => 50, 'min' => 1, ], 'OpsMetadataNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsMetadataResourceId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'OpsMetadataTooManyUpdatesException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OpsResultAttribute' => [ 'type' => 'structure', 'required' => [ 'TypeName', ], 'members' => [ 'TypeName' => [ 'shape' => 'OpsDataTypeName', ], ], ], 'OpsResultAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpsResultAttribute', ], 'min' => 1, ], 'OutputSource' => [ 'type' => 'structure', 'members' => [ 'OutputSourceId' => [ 'shape' => 'OutputSourceId', ], 'OutputSourceType' => [ 'shape' => 'OutputSourceType', ], ], ], 'OutputSourceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'OutputSourceType' => [ 'type' => 'string', ], 'OwnerInformation' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'PSParameterName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PSParameterSelector' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PSParameterValue' => [ 'type' => 'string', 'sensitive' => true, ], 'PSParameterVersion' => [ 'type' => 'long', ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'Type' => [ 'shape' => 'ParameterType', ], 'Value' => [ 'shape' => 'PSParameterValue', ], 'Version' => [ 'shape' => 'PSParameterVersion', ], 'Selector' => [ 'shape' => 'PSParameterSelector', ], 'SourceResult' => [ 'shape' => 'String', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'ARN' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'ParameterDataType', ], ], ], 'ParameterAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterDataType' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'ParameterDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ParameterHistory' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'Type' => [ 'shape' => 'ParameterType', ], 'KeyId' => [ 'shape' => 'ParameterKeyId', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LastModifiedUser' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ParameterDescription', ], 'Value' => [ 'shape' => 'PSParameterValue', ], 'AllowedPattern' => [ 'shape' => 'AllowedPattern', ], 'Version' => [ 'shape' => 'PSParameterVersion', ], 'Labels' => [ 'shape' => 'ParameterLabelList', ], 'Tier' => [ 'shape' => 'ParameterTier', ], 'Policies' => [ 'shape' => 'ParameterPolicyList', ], 'DataType' => [ 'shape' => 'ParameterDataType', ], ], ], 'ParameterHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterHistory', ], ], 'ParameterInlinePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyText' => [ 'shape' => 'String', ], 'PolicyType' => [ 'shape' => 'String', ], 'PolicyStatus' => [ 'shape' => 'String', ], ], ], 'ParameterKeyId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^([a-zA-Z0-9:/_-]+)$', ], 'ParameterLabel' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ParameterLabelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterLabel', ], 'max' => 10, 'min' => 1, ], 'ParameterLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'ParameterMaxVersionLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterMetadata' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'ARN' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'ParameterType', ], 'KeyId' => [ 'shape' => 'ParameterKeyId', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LastModifiedUser' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ParameterDescription', ], 'AllowedPattern' => [ 'shape' => 'AllowedPattern', ], 'Version' => [ 'shape' => 'PSParameterVersion', ], 'Tier' => [ 'shape' => 'ParameterTier', ], 'Policies' => [ 'shape' => 'ParameterPolicyList', ], 'DataType' => [ 'shape' => 'ParameterDataType', ], ], ], 'ParameterMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterMetadata', ], ], 'ParameterName' => [ 'type' => 'string', ], 'ParameterNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PSParameterName', ], 'max' => 10, 'min' => 1, ], 'ParameterNotFound' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterPatternMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterPolicies' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ParameterPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterInlinePolicy', ], ], 'ParameterStringFilter' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'ParameterStringFilterKey', ], 'Option' => [ 'shape' => 'ParameterStringQueryOption', ], 'Values' => [ 'shape' => 'ParameterStringFilterValueList', ], ], ], 'ParameterStringFilterKey' => [ 'type' => 'string', 'max' => 132, 'min' => 1, 'pattern' => 'tag:.+|Name|Type|KeyId|Path|Label|Tier|DataType', ], 'ParameterStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterStringFilter', ], ], 'ParameterStringFilterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParameterStringFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterStringFilterValue', ], 'max' => 50, 'min' => 1, ], 'ParameterStringQueryOption' => [ 'type' => 'string', 'max' => 10, 'min' => 1, ], 'ParameterTier' => [ 'type' => 'string', 'enum' => [ 'Standard', 'Advanced', 'Intelligent-Tiering', ], ], 'ParameterType' => [ 'type' => 'string', 'enum' => [ 'String', 'StringList', 'SecureString', ], ], 'ParameterValue' => [ 'type' => 'string', ], 'ParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterValue', ], ], 'ParameterVersionLabelLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ParameterVersionNotFound' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Parameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValueList', ], 'sensitive' => true, ], 'ParametersFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'ParametersFilterKey', ], 'Values' => [ 'shape' => 'ParametersFilterValueList', ], ], ], 'ParametersFilterKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'Type', 'KeyId', ], ], 'ParametersFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParametersFilter', ], ], 'ParametersFilterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParametersFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParametersFilterValue', ], 'max' => 50, 'min' => 1, ], 'ParentStepDetails' => [ 'type' => 'structure', 'members' => [ 'StepExecutionId' => [ 'shape' => 'String', ], 'StepName' => [ 'shape' => 'String', ], 'Action' => [ 'shape' => 'AutomationActionName', ], 'Iteration' => [ 'shape' => 'Integer', 'box' => true, ], 'IteratorValue' => [ 'shape' => 'String', ], ], ], 'Patch' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PatchId', ], 'ReleaseDate' => [ 'shape' => 'DateTime', ], 'Title' => [ 'shape' => 'PatchTitle', ], 'Description' => [ 'shape' => 'PatchDescription', ], 'ContentUrl' => [ 'shape' => 'PatchContentUrl', ], 'Vendor' => [ 'shape' => 'PatchVendor', ], 'ProductFamily' => [ 'shape' => 'PatchProductFamily', ], 'Product' => [ 'shape' => 'PatchProduct', ], 'Classification' => [ 'shape' => 'PatchClassification', ], 'MsrcSeverity' => [ 'shape' => 'PatchMsrcSeverity', ], 'KbNumber' => [ 'shape' => 'PatchKbNumber', ], 'MsrcNumber' => [ 'shape' => 'PatchMsrcNumber', ], 'Language' => [ 'shape' => 'PatchLanguage', ], 'AdvisoryIds' => [ 'shape' => 'PatchAdvisoryIdList', ], 'BugzillaIds' => [ 'shape' => 'PatchBugzillaIdList', ], 'CVEIds' => [ 'shape' => 'PatchCVEIdList', ], 'Name' => [ 'shape' => 'PatchName', ], 'Epoch' => [ 'shape' => 'PatchEpoch', ], 'Version' => [ 'shape' => 'PatchVersion', ], 'Release' => [ 'shape' => 'PatchRelease', ], 'Arch' => [ 'shape' => 'PatchArch', ], 'Severity' => [ 'shape' => 'PatchSeverity', ], 'Repository' => [ 'shape' => 'PatchRepository', ], ], ], 'PatchAction' => [ 'type' => 'string', 'enum' => [ 'ALLOW_AS_DEPENDENCY', 'BLOCK', ], ], 'PatchAdvisoryId' => [ 'type' => 'string', ], 'PatchAdvisoryIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchAdvisoryId', ], ], 'PatchArch' => [ 'type' => 'string', ], 'PatchBaselineIdentity' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'BaselineName' => [ 'shape' => 'BaselineName', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'BaselineDescription' => [ 'shape' => 'BaselineDescription', ], 'DefaultBaseline' => [ 'shape' => 'DefaultBaseline', ], ], ], 'PatchBaselineIdentityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchBaselineIdentity', ], ], 'PatchBaselineMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'PatchBugzillaId' => [ 'type' => 'string', ], 'PatchBugzillaIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchBugzillaId', ], ], 'PatchCVEId' => [ 'type' => 'string', ], 'PatchCVEIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchCVEId', ], ], 'PatchCVEIds' => [ 'type' => 'string', ], 'PatchClassification' => [ 'type' => 'string', ], 'PatchComplianceData' => [ 'type' => 'structure', 'required' => [ 'Title', 'KBId', 'Classification', 'Severity', 'State', 'InstalledTime', ], 'members' => [ 'Title' => [ 'shape' => 'PatchTitle', ], 'KBId' => [ 'shape' => 'PatchKbNumber', ], 'Classification' => [ 'shape' => 'PatchClassification', ], 'Severity' => [ 'shape' => 'PatchSeverity', ], 'State' => [ 'shape' => 'PatchComplianceDataState', ], 'InstalledTime' => [ 'shape' => 'DateTime', ], 'CVEIds' => [ 'shape' => 'PatchCVEIds', ], ], ], 'PatchComplianceDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchComplianceData', ], ], 'PatchComplianceDataState' => [ 'type' => 'string', 'enum' => [ 'INSTALLED', 'INSTALLED_OTHER', 'INSTALLED_PENDING_REBOOT', 'INSTALLED_REJECTED', 'MISSING', 'NOT_APPLICABLE', 'FAILED', ], ], 'PatchComplianceLevel' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFORMATIONAL', 'UNSPECIFIED', ], ], 'PatchComplianceMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 10, ], 'PatchContentUrl' => [ 'type' => 'string', ], 'PatchCriticalNonCompliantCount' => [ 'type' => 'integer', ], 'PatchDeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'PENDING_APPROVAL', 'EXPLICIT_APPROVED', 'EXPLICIT_REJECTED', ], ], 'PatchDescription' => [ 'type' => 'string', ], 'PatchEpoch' => [ 'type' => 'integer', ], 'PatchFailedCount' => [ 'type' => 'integer', ], 'PatchFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'PatchFilterKey', ], 'Values' => [ 'shape' => 'PatchFilterValueList', ], ], ], 'PatchFilterGroup' => [ 'type' => 'structure', 'required' => [ 'PatchFilters', ], 'members' => [ 'PatchFilters' => [ 'shape' => 'PatchFilterList', ], ], ], 'PatchFilterKey' => [ 'type' => 'string', 'enum' => [ 'ARCH', 'ADVISORY_ID', 'BUGZILLA_ID', 'PATCH_SET', 'PRODUCT', 'PRODUCT_FAMILY', 'CLASSIFICATION', 'CVE_ID', 'EPOCH', 'MSRC_SEVERITY', 'NAME', 'PATCH_ID', 'SECTION', 'PRIORITY', 'REPOSITORY', 'RELEASE', 'SEVERITY', 'SECURITY', 'VERSION', ], ], 'PatchFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchFilter', ], 'max' => 4, 'min' => 0, ], 'PatchFilterValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PatchFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchFilterValue', ], 'max' => 20, 'min' => 1, ], 'PatchGroup' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'PatchGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchGroup', ], ], 'PatchGroupPatchBaselineMapping' => [ 'type' => 'structure', 'members' => [ 'PatchGroup' => [ 'shape' => 'PatchGroup', ], 'BaselineIdentity' => [ 'shape' => 'PatchBaselineIdentity', ], ], ], 'PatchGroupPatchBaselineMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchGroupPatchBaselineMapping', ], ], 'PatchId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'PatchIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchId', ], 'max' => 50, 'min' => 0, ], 'PatchInstalledCount' => [ 'type' => 'integer', ], 'PatchInstalledOtherCount' => [ 'type' => 'integer', ], 'PatchInstalledPendingRebootCount' => [ 'type' => 'integer', ], 'PatchInstalledRejectedCount' => [ 'type' => 'integer', ], 'PatchKbNumber' => [ 'type' => 'string', ], 'PatchLanguage' => [ 'type' => 'string', ], 'PatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Patch', ], ], 'PatchMissingCount' => [ 'type' => 'integer', ], 'PatchMsrcNumber' => [ 'type' => 'string', ], 'PatchMsrcSeverity' => [ 'type' => 'string', ], 'PatchName' => [ 'type' => 'string', ], 'PatchNotApplicableCount' => [ 'type' => 'integer', ], 'PatchOperationType' => [ 'type' => 'string', 'enum' => [ 'Scan', 'Install', ], ], 'PatchOrchestratorFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'PatchOrchestratorFilterKey', ], 'Values' => [ 'shape' => 'PatchOrchestratorFilterValues', ], ], ], 'PatchOrchestratorFilterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PatchOrchestratorFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchOrchestratorFilter', ], 'max' => 5, 'min' => 0, ], 'PatchOrchestratorFilterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PatchOrchestratorFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchOrchestratorFilterValue', ], ], 'PatchOtherNonCompliantCount' => [ 'type' => 'integer', ], 'PatchProduct' => [ 'type' => 'string', ], 'PatchProductFamily' => [ 'type' => 'string', ], 'PatchPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchPropertyEntry', ], ], 'PatchProperty' => [ 'type' => 'string', 'enum' => [ 'PRODUCT', 'PRODUCT_FAMILY', 'CLASSIFICATION', 'MSRC_SEVERITY', 'PRIORITY', 'SEVERITY', ], ], 'PatchPropertyEntry' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'PatchRelease' => [ 'type' => 'string', ], 'PatchRepository' => [ 'type' => 'string', ], 'PatchRule' => [ 'type' => 'structure', 'required' => [ 'PatchFilterGroup', ], 'members' => [ 'PatchFilterGroup' => [ 'shape' => 'PatchFilterGroup', ], 'ComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApproveAfterDays' => [ 'shape' => 'ApproveAfterDays', 'box' => true, ], 'ApproveUntilDate' => [ 'shape' => 'PatchStringDateTime', 'box' => true, ], 'EnableNonSecurity' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'PatchRuleGroup' => [ 'type' => 'structure', 'required' => [ 'PatchRules', ], 'members' => [ 'PatchRules' => [ 'shape' => 'PatchRuleList', ], ], ], 'PatchRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchRule', ], 'max' => 10, 'min' => 0, ], 'PatchSecurityNonCompliantCount' => [ 'type' => 'integer', ], 'PatchSet' => [ 'type' => 'string', 'enum' => [ 'OS', 'APPLICATION', ], ], 'PatchSeverity' => [ 'type' => 'string', ], 'PatchSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Products', 'Configuration', ], 'members' => [ 'Name' => [ 'shape' => 'PatchSourceName', ], 'Products' => [ 'shape' => 'PatchSourceProductList', ], 'Configuration' => [ 'shape' => 'PatchSourceConfiguration', ], ], ], 'PatchSourceConfiguration' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'PatchSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchSource', ], 'max' => 20, 'min' => 0, ], 'PatchSourceName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.]{3,50}$', ], 'PatchSourceProduct' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PatchSourceProductList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatchSourceProduct', ], 'max' => 20, 'min' => 1, ], 'PatchStatus' => [ 'type' => 'structure', 'members' => [ 'DeploymentStatus' => [ 'shape' => 'PatchDeploymentStatus', ], 'ComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApprovalDate' => [ 'shape' => 'DateTime', ], ], ], 'PatchStringDateTime' => [ 'type' => 'string', 'max' => 10, 'min' => 1, ], 'PatchTitle' => [ 'type' => 'string', ], 'PatchUnreportedNotApplicableCount' => [ 'type' => 'integer', ], 'PatchVendor' => [ 'type' => 'string', ], 'PatchVersion' => [ 'type' => 'string', ], 'PingStatus' => [ 'type' => 'string', 'enum' => [ 'Online', 'ConnectionLost', 'Inactive', ], ], 'PlatformType' => [ 'type' => 'string', 'enum' => [ 'Windows', 'Linux', 'MacOS', ], ], 'PlatformTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatformType', ], ], 'PoliciesLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Policy' => [ 'type' => 'string', 'pattern' => '^(?!\\s*$).+', ], 'PolicyHash' => [ 'type' => 'string', ], 'PolicyId' => [ 'type' => 'string', ], 'Product' => [ 'type' => 'string', ], 'ProgressCounters' => [ 'type' => 'structure', 'members' => [ 'TotalSteps' => [ 'shape' => 'Integer', ], 'SuccessSteps' => [ 'shape' => 'Integer', ], 'FailedSteps' => [ 'shape' => 'Integer', ], 'CancelledSteps' => [ 'shape' => 'Integer', ], 'TimedOutSteps' => [ 'shape' => 'Integer', ], ], ], 'PutComplianceItemsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'ResourceType', 'ComplianceType', 'ExecutionSummary', 'Items', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ComplianceResourceId', ], 'ResourceType' => [ 'shape' => 'ComplianceResourceType', ], 'ComplianceType' => [ 'shape' => 'ComplianceTypeName', ], 'ExecutionSummary' => [ 'shape' => 'ComplianceExecutionSummary', ], 'Items' => [ 'shape' => 'ComplianceItemEntryList', ], 'ItemContentHash' => [ 'shape' => 'ComplianceItemContentHash', ], 'UploadType' => [ 'shape' => 'ComplianceUploadType', 'box' => true, ], ], ], 'PutComplianceItemsResult' => [ 'type' => 'structure', 'members' => [], ], 'PutInventoryMessage' => [ 'type' => 'string', ], 'PutInventoryRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Items', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Items' => [ 'shape' => 'InventoryItemList', ], ], ], 'PutInventoryResult' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'PutInventoryMessage', ], ], ], 'PutParameterRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'Description' => [ 'shape' => 'ParameterDescription', ], 'Value' => [ 'shape' => 'PSParameterValue', ], 'Type' => [ 'shape' => 'ParameterType', ], 'KeyId' => [ 'shape' => 'ParameterKeyId', ], 'Overwrite' => [ 'shape' => 'Boolean', 'box' => true, ], 'AllowedPattern' => [ 'shape' => 'AllowedPattern', ], 'Tags' => [ 'shape' => 'TagList', ], 'Tier' => [ 'shape' => 'ParameterTier', ], 'Policies' => [ 'shape' => 'ParameterPolicies', ], 'DataType' => [ 'shape' => 'ParameterDataType', ], ], ], 'PutParameterResult' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'PSParameterVersion', ], 'Tier' => [ 'shape' => 'ParameterTier', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'Policy' => [ 'shape' => 'Policy', ], 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyHash' => [ 'shape' => 'PolicyHash', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyHash' => [ 'shape' => 'PolicyHash', ], ], ], 'RebootOption' => [ 'type' => 'string', 'enum' => [ 'RebootIfNeeded', 'NoReboot', ], ], 'Region' => [ 'type' => 'string', ], 'Regions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], 'max' => 50, 'min' => 1, ], 'RegisterDefaultPatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'RegisterDefaultPatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], ], ], 'RegisterPatchBaselineForPatchGroupRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', 'PatchGroup', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], ], ], 'RegisterPatchBaselineForPatchGroupResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'PatchGroup' => [ 'shape' => 'PatchGroup', ], ], ], 'RegisterTargetWithMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'ResourceType', 'Targets', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'ResourceType' => [ 'shape' => 'MaintenanceWindowResourceType', ], 'Targets' => [ 'shape' => 'Targets', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'RegisterTargetWithMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], ], ], 'RegisterTaskWithMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'TaskArn', 'TaskType', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Targets' => [ 'shape' => 'Targets', 'box' => true, ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'TaskType' => [ 'shape' => 'MaintenanceWindowTaskType', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'TaskInvocationParameters' => [ 'shape' => 'MaintenanceWindowTaskInvocationParameters', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', 'box' => true, ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', 'box' => true, ], 'MaxErrors' => [ 'shape' => 'MaxErrors', 'box' => true, ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'CutoffBehavior' => [ 'shape' => 'MaintenanceWindowTaskCutoffBehavior', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'RegisterTaskWithMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], ], ], 'RegistrationLimit' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'RegistrationMetadataItem' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'RegistrationMetadataKey', ], 'Value' => [ 'shape' => 'RegistrationMetadataValue', ], ], ], 'RegistrationMetadataKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'RegistrationMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationMetadataItem', ], ], 'RegistrationMetadataValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'RegistrationsCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'RelatedOpsItem' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', ], 'members' => [ 'OpsItemId' => [ 'shape' => 'String', ], ], ], 'RelatedOpsItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedOpsItem', ], ], 'RemainingCount' => [ 'type' => 'integer', ], 'RemoveTagsFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeForTagging', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'RemoveTagsFromResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'RequireType' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^[a-zA-Z0-9_\\-.]{1,128}$', ], 'ResetServiceSettingRequest' => [ 'type' => 'structure', 'required' => [ 'SettingId', ], 'members' => [ 'SettingId' => [ 'shape' => 'ServiceSettingId', ], ], ], 'ResetServiceSettingResult' => [ 'type' => 'structure', 'members' => [ 'ServiceSetting' => [ 'shape' => 'ServiceSetting', ], ], ], 'ResolvedTargets' => [ 'type' => 'structure', 'members' => [ 'ParameterValues' => [ 'shape' => 'TargetParameterList', ], 'Truncated' => [ 'shape' => 'Boolean', ], ], ], 'ResourceArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'ResourceComplianceSummaryItem' => [ 'type' => 'structure', 'members' => [ 'ComplianceType' => [ 'shape' => 'ComplianceTypeName', ], 'ResourceType' => [ 'shape' => 'ComplianceResourceType', ], 'ResourceId' => [ 'shape' => 'ComplianceResourceId', ], 'Status' => [ 'shape' => 'ComplianceStatus', ], 'OverallSeverity' => [ 'shape' => 'ComplianceSeverity', ], 'ExecutionSummary' => [ 'shape' => 'ComplianceExecutionSummary', ], 'CompliantSummary' => [ 'shape' => 'CompliantSummary', ], 'NonCompliantSummary' => [ 'shape' => 'NonCompliantSummary', ], ], ], 'ResourceComplianceSummaryItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceComplianceSummaryItem', ], ], 'ResourceCount' => [ 'type' => 'integer', ], 'ResourceCountByStatus' => [ 'type' => 'string', ], 'ResourceDataSyncAWSKMSKeyARN' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:.*', ], 'ResourceDataSyncAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], ], 'exception' => true, ], 'ResourceDataSyncAwsOrganizationsSource' => [ 'type' => 'structure', 'required' => [ 'OrganizationSourceType', ], 'members' => [ 'OrganizationSourceType' => [ 'shape' => 'ResourceDataSyncOrganizationSourceType', ], 'OrganizationalUnits' => [ 'shape' => 'ResourceDataSyncOrganizationalUnitList', ], ], ], 'ResourceDataSyncConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceDataSyncCountExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceDataSyncCreatedTime' => [ 'type' => 'timestamp', ], 'ResourceDataSyncDestinationDataSharing' => [ 'type' => 'structure', 'members' => [ 'DestinationDataSharingType' => [ 'shape' => 'ResourceDataSyncDestinationDataSharingType', ], ], ], 'ResourceDataSyncDestinationDataSharingType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncEnableAllOpsDataSources' => [ 'type' => 'boolean', ], 'ResourceDataSyncIncludeFutureRegions' => [ 'type' => 'boolean', ], 'ResourceDataSyncInvalidConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceDataSyncItem' => [ 'type' => 'structure', 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], 'SyncSource' => [ 'shape' => 'ResourceDataSyncSourceWithState', ], 'S3Destination' => [ 'shape' => 'ResourceDataSyncS3Destination', ], 'LastSyncTime' => [ 'shape' => 'LastResourceDataSyncTime', ], 'LastSuccessfulSyncTime' => [ 'shape' => 'LastSuccessfulResourceDataSyncTime', ], 'SyncLastModifiedTime' => [ 'shape' => 'ResourceDataSyncLastModifiedTime', ], 'LastStatus' => [ 'shape' => 'LastResourceDataSyncStatus', ], 'SyncCreatedTime' => [ 'shape' => 'ResourceDataSyncCreatedTime', ], 'LastSyncStatusMessage' => [ 'shape' => 'LastResourceDataSyncMessage', ], ], ], 'ResourceDataSyncItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDataSyncItem', ], ], 'ResourceDataSyncLastModifiedTime' => [ 'type' => 'timestamp', ], 'ResourceDataSyncName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncNotFoundException' => [ 'type' => 'structure', 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceDataSyncOrganizationSourceType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncOrganizationalUnit' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnitId' => [ 'shape' => 'ResourceDataSyncOrganizationalUnitId', ], ], ], 'ResourceDataSyncOrganizationalUnitId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$', ], 'ResourceDataSyncOrganizationalUnitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDataSyncOrganizationalUnit', ], 'max' => 1000, 'min' => 1, ], 'ResourceDataSyncS3BucketName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ResourceDataSyncS3Destination' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'SyncFormat', 'Region', ], 'members' => [ 'BucketName' => [ 'shape' => 'ResourceDataSyncS3BucketName', ], 'Prefix' => [ 'shape' => 'ResourceDataSyncS3Prefix', ], 'SyncFormat' => [ 'shape' => 'ResourceDataSyncS3Format', ], 'Region' => [ 'shape' => 'ResourceDataSyncS3Region', ], 'AWSKMSKeyARN' => [ 'shape' => 'ResourceDataSyncAWSKMSKeyARN', ], 'DestinationDataSharing' => [ 'shape' => 'ResourceDataSyncDestinationDataSharing', ], ], ], 'ResourceDataSyncS3Format' => [ 'type' => 'string', 'enum' => [ 'JsonSerDe', ], ], 'ResourceDataSyncS3Prefix' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceDataSyncS3Region' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncSource' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceRegions', ], 'members' => [ 'SourceType' => [ 'shape' => 'ResourceDataSyncSourceType', ], 'AwsOrganizationsSource' => [ 'shape' => 'ResourceDataSyncAwsOrganizationsSource', ], 'SourceRegions' => [ 'shape' => 'ResourceDataSyncSourceRegionList', ], 'IncludeFutureRegions' => [ 'shape' => 'ResourceDataSyncIncludeFutureRegions', ], 'EnableAllOpsDataSources' => [ 'shape' => 'ResourceDataSyncEnableAllOpsDataSources', ], ], ], 'ResourceDataSyncSourceRegion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncSourceRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDataSyncSourceRegion', ], ], 'ResourceDataSyncSourceType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncSourceWithState' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'ResourceDataSyncSourceType', ], 'AwsOrganizationsSource' => [ 'shape' => 'ResourceDataSyncAwsOrganizationsSource', ], 'SourceRegions' => [ 'shape' => 'ResourceDataSyncSourceRegionList', ], 'IncludeFutureRegions' => [ 'shape' => 'ResourceDataSyncIncludeFutureRegions', ], 'State' => [ 'shape' => 'ResourceDataSyncState', ], 'EnableAllOpsDataSources' => [ 'shape' => 'ResourceDataSyncEnableAllOpsDataSources', ], ], ], 'ResourceDataSyncState' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceDataSyncType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePolicyConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePolicyInvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'ParameterNames' => [ 'shape' => 'ResourcePolicyParameterNamesList', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePolicyLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'Integer', ], 'LimitType' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePolicyMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ResourcePolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourcePolicyParameterNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'ManagedInstance', 'EC2Instance', ], ], 'ResourceTypeForTagging' => [ 'type' => 'string', 'enum' => [ 'Document', 'ManagedInstance', 'MaintenanceWindow', 'Parameter', 'PatchBaseline', 'OpsItem', 'OpsMetadata', 'Automation', 'Association', ], ], 'ResponseCode' => [ 'type' => 'integer', ], 'ResultAttribute' => [ 'type' => 'structure', 'required' => [ 'TypeName', ], 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], ], ], 'ResultAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultAttribute', ], 'max' => 1, 'min' => 1, ], 'ResumeSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', ], 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], ], ], 'ResumeSessionResponse' => [ 'type' => 'structure', 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], 'TokenValue' => [ 'shape' => 'TokenValue', ], 'StreamUrl' => [ 'shape' => 'StreamUrl', ], ], ], 'ReviewInformation' => [ 'type' => 'structure', 'members' => [ 'ReviewedTime' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'ReviewStatus', ], 'Reviewer' => [ 'shape' => 'Reviewer', ], ], ], 'ReviewInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReviewInformation', ], 'min' => 1, ], 'ReviewStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'NOT_REVIEWED', 'PENDING', 'REJECTED', ], ], 'Reviewer' => [ 'type' => 'string', 'max' => 50, 'pattern' => '^[a-zA-Z0-9_\\-.]{1,128}$', ], 'Runbook' => [ 'type' => 'structure', 'required' => [ 'DocumentName', ], 'members' => [ 'DocumentName' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', 'box' => true, ], 'Parameters' => [ 'shape' => 'AutomationParameterMap', ], 'TargetParameterName' => [ 'shape' => 'AutomationParameterKey', ], 'Targets' => [ 'shape' => 'Targets', ], 'TargetMaps' => [ 'shape' => 'TargetMaps', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', 'box' => true, ], ], ], 'Runbooks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Runbook', ], 'max' => 1, 'min' => 1, ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'S3KeyPrefix' => [ 'type' => 'string', 'max' => 500, ], 'S3OutputLocation' => [ 'type' => 'structure', 'members' => [ 'OutputS3Region' => [ 'shape' => 'S3Region', ], 'OutputS3BucketName' => [ 'shape' => 'S3BucketName', ], 'OutputS3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], ], ], 'S3OutputUrl' => [ 'type' => 'structure', 'members' => [ 'OutputUrl' => [ 'shape' => 'Url', ], ], ], 'S3Region' => [ 'type' => 'string', 'max' => 20, 'min' => 3, ], 'ScheduleExpression' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ScheduleOffset' => [ 'type' => 'integer', 'max' => 6, 'min' => 1, ], 'ScheduledWindowExecution' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'ExecutionTime' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], ], ], 'ScheduledWindowExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledWindowExecution', ], ], 'SendAutomationSignalRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationExecutionId', 'SignalType', ], 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'SignalType' => [ 'shape' => 'SignalType', ], 'Payload' => [ 'shape' => 'AutomationParameterMap', ], ], ], 'SendAutomationSignalResult' => [ 'type' => 'structure', 'members' => [], ], 'SendCommandRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentName', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIdList', ], 'Targets' => [ 'shape' => 'Targets', ], 'DocumentName' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentHash' => [ 'shape' => 'DocumentHash', ], 'DocumentHashType' => [ 'shape' => 'DocumentHashType', ], 'TimeoutSeconds' => [ 'shape' => 'TimeoutSeconds', 'box' => true, ], 'Comment' => [ 'shape' => 'Comment', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'OutputS3Region' => [ 'shape' => 'S3Region', ], 'OutputS3BucketName' => [ 'shape' => 'S3BucketName', ], 'OutputS3KeyPrefix' => [ 'shape' => 'S3KeyPrefix', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'NotificationConfig' => [ 'shape' => 'NotificationConfig', ], 'CloudWatchOutputConfig' => [ 'shape' => 'CloudWatchOutputConfig', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'SendCommandResult' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'Command', ], ], ], 'ServiceRole' => [ 'type' => 'string', ], 'ServiceSetting' => [ 'type' => 'structure', 'members' => [ 'SettingId' => [ 'shape' => 'ServiceSettingId', ], 'SettingValue' => [ 'shape' => 'ServiceSettingValue', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LastModifiedUser' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'ServiceSettingId' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ServiceSettingNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ServiceSettingValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Session' => [ 'type' => 'structure', 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], 'Target' => [ 'shape' => 'SessionTarget', ], 'Status' => [ 'shape' => 'SessionStatus', ], 'StartDate' => [ 'shape' => 'DateTime', ], 'EndDate' => [ 'shape' => 'DateTime', ], 'DocumentName' => [ 'shape' => 'DocumentName', ], 'Owner' => [ 'shape' => 'SessionOwner', ], 'Reason' => [ 'shape' => 'SessionReason', ], 'Details' => [ 'shape' => 'SessionDetails', ], 'OutputUrl' => [ 'shape' => 'SessionManagerOutputUrl', ], 'MaxSessionDuration' => [ 'shape' => 'MaxSessionDuration', ], ], ], 'SessionDetails' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SessionFilter' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'SessionFilterKey', ], 'value' => [ 'shape' => 'SessionFilterValue', ], ], ], 'SessionFilterKey' => [ 'type' => 'string', 'enum' => [ 'InvokedAfter', 'InvokedBefore', 'Target', 'Owner', 'Status', 'SessionId', ], ], 'SessionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionFilter', ], 'max' => 6, 'min' => 1, ], 'SessionFilterValue' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'SessionId' => [ 'type' => 'string', 'max' => 96, 'min' => 1, ], 'SessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Session', ], ], 'SessionManagerCloudWatchOutputUrl' => [ 'type' => 'string', 'max' => 2083, 'min' => 1, ], 'SessionManagerOutputUrl' => [ 'type' => 'structure', 'members' => [ 'S3OutputUrl' => [ 'shape' => 'SessionManagerS3OutputUrl', ], 'CloudWatchOutputUrl' => [ 'shape' => 'SessionManagerCloudWatchOutputUrl', ], ], ], 'SessionManagerParameterName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SessionManagerParameterValue' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'SessionManagerParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionManagerParameterValue', ], ], 'SessionManagerParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionManagerParameterName', ], 'value' => [ 'shape' => 'SessionManagerParameterValueList', ], ], 'SessionManagerS3OutputUrl' => [ 'type' => 'string', 'max' => 2083, 'min' => 1, ], 'SessionMaxResults' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'SessionOwner' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SessionReason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.{1,256}$', ], 'SessionState' => [ 'type' => 'string', 'enum' => [ 'Active', 'History', ], ], 'SessionStatus' => [ 'type' => 'string', 'enum' => [ 'Connected', 'Connecting', 'Disconnected', 'Terminated', 'Terminating', 'Failed', ], ], 'SessionTarget' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'SeveritySummary' => [ 'type' => 'structure', 'members' => [ 'CriticalCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'HighCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'MediumCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'LowCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'InformationalCount' => [ 'shape' => 'ComplianceSummaryCount', ], 'UnspecifiedCount' => [ 'shape' => 'ComplianceSummaryCount', ], ], ], 'SharedDocumentVersion' => [ 'type' => 'string', 'max' => 8, 'pattern' => '([$]LATEST|[$]DEFAULT|[$]ALL)', ], 'SignalType' => [ 'type' => 'string', 'enum' => [ 'Approve', 'Reject', 'StartStep', 'StopStep', 'Resume', ], ], 'SnapshotDownloadUrl' => [ 'type' => 'string', ], 'SnapshotId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'SourceId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9:_-]*$', ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::EC2::Instance', 'AWS::IoT::Thing', 'AWS::SSM::ManagedInstance', ], ], 'StandardErrorContent' => [ 'type' => 'string', 'max' => 8000, ], 'StandardOutputContent' => [ 'type' => 'string', 'max' => 24000, ], 'StartAssociationsOnceRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationIds', ], 'members' => [ 'AssociationIds' => [ 'shape' => 'AssociationIdList', ], ], ], 'StartAssociationsOnceResult' => [ 'type' => 'structure', 'members' => [], ], 'StartAutomationExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentName', ], 'members' => [ 'DocumentName' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', 'box' => true, ], 'Parameters' => [ 'shape' => 'AutomationParameterMap', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', ], 'Mode' => [ 'shape' => 'ExecutionMode', ], 'TargetParameterName' => [ 'shape' => 'AutomationParameterKey', ], 'Targets' => [ 'shape' => 'Targets', ], 'TargetMaps' => [ 'shape' => 'TargetMaps', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', 'box' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'StartAutomationExecutionResult' => [ 'type' => 'structure', 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], ], ], 'StartChangeRequestExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentName', 'Runbooks', ], 'members' => [ 'ScheduledTime' => [ 'shape' => 'DateTime', ], 'DocumentName' => [ 'shape' => 'DocumentARN', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', 'box' => true, ], 'Parameters' => [ 'shape' => 'AutomationParameterMap', ], 'ChangeRequestName' => [ 'shape' => 'ChangeRequestName', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', ], 'AutoApprove' => [ 'shape' => 'Boolean', ], 'Runbooks' => [ 'shape' => 'Runbooks', ], 'Tags' => [ 'shape' => 'TagList', ], 'ScheduledEndTime' => [ 'shape' => 'DateTime', ], 'ChangeDetails' => [ 'shape' => 'ChangeDetailsValue', ], ], ], 'StartChangeRequestExecutionResult' => [ 'type' => 'structure', 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], ], ], 'StartSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Target', ], 'members' => [ 'Target' => [ 'shape' => 'SessionTarget', ], 'DocumentName' => [ 'shape' => 'DocumentARN', ], 'Reason' => [ 'shape' => 'SessionReason', ], 'Parameters' => [ 'shape' => 'SessionManagerParameters', ], ], ], 'StartSessionResponse' => [ 'type' => 'structure', 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], 'TokenValue' => [ 'shape' => 'TokenValue', ], 'StreamUrl' => [ 'shape' => 'StreamUrl', ], ], ], 'StatusAdditionalInfo' => [ 'type' => 'string', 'max' => 1024, ], 'StatusDetails' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'StatusName' => [ 'type' => 'string', ], 'StatusUnchanged' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'StepExecution' => [ 'type' => 'structure', 'members' => [ 'StepName' => [ 'shape' => 'String', ], 'Action' => [ 'shape' => 'AutomationActionName', ], 'TimeoutSeconds' => [ 'shape' => 'Long', 'box' => true, ], 'OnFailure' => [ 'shape' => 'String', ], 'MaxAttempts' => [ 'shape' => 'Integer', 'box' => true, ], 'ExecutionStartTime' => [ 'shape' => 'DateTime', ], 'ExecutionEndTime' => [ 'shape' => 'DateTime', ], 'StepStatus' => [ 'shape' => 'AutomationExecutionStatus', ], 'ResponseCode' => [ 'shape' => 'String', ], 'Inputs' => [ 'shape' => 'NormalStringMap', ], 'Outputs' => [ 'shape' => 'AutomationParameterMap', ], 'Response' => [ 'shape' => 'String', ], 'FailureMessage' => [ 'shape' => 'String', ], 'FailureDetails' => [ 'shape' => 'FailureDetails', ], 'StepExecutionId' => [ 'shape' => 'String', ], 'OverriddenParameters' => [ 'shape' => 'AutomationParameterMap', ], 'IsEnd' => [ 'shape' => 'Boolean', 'box' => true, ], 'NextStep' => [ 'shape' => 'String', 'box' => true, ], 'IsCritical' => [ 'shape' => 'Boolean', 'box' => true, ], 'ValidNextSteps' => [ 'shape' => 'ValidNextStepList', ], 'Targets' => [ 'shape' => 'Targets', 'box' => true, ], 'TargetLocation' => [ 'shape' => 'TargetLocation', 'box' => true, ], 'TriggeredAlarms' => [ 'shape' => 'AlarmStateInformationList', ], 'ParentStepDetails' => [ 'shape' => 'ParentStepDetails', ], ], ], 'StepExecutionFilter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'StepExecutionFilterKey', ], 'Values' => [ 'shape' => 'StepExecutionFilterValueList', ], ], ], 'StepExecutionFilterKey' => [ 'type' => 'string', 'enum' => [ 'StartTimeBefore', 'StartTimeAfter', 'StepExecutionStatus', 'StepExecutionId', 'StepName', 'Action', 'ParentStepExecutionId', 'ParentStepIteration', 'ParentStepIteratorValue', ], ], 'StepExecutionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepExecutionFilter', ], 'max' => 6, 'min' => 1, ], 'StepExecutionFilterValue' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'StepExecutionFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepExecutionFilterValue', ], 'max' => 10, 'min' => 1, ], 'StepExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepExecution', ], ], 'StopAutomationExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationExecutionId', ], 'members' => [ 'AutomationExecutionId' => [ 'shape' => 'AutomationExecutionId', ], 'Type' => [ 'shape' => 'StopType', ], ], ], 'StopAutomationExecutionResult' => [ 'type' => 'structure', 'members' => [], ], 'StopType' => [ 'type' => 'string', 'enum' => [ 'Complete', 'Cancel', ], ], 'StreamUrl' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'StringDateTime' => [ 'type' => 'string', 'pattern' => '^([\\-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))([T\\s]((([01]\\d|2[0-3])((:?)[0-5]\\d)?|24\\:?00)([\\.,]\\d(?!:))?)?(\\17[0-5]\\d([\\.,]\\d)?)?([zZ]|([\\-])([01]\\d|2[0-3]):?([0-5]\\d)?)?)?)?$', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubTypeCountLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 1000, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Target' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TargetKey', ], 'Values' => [ 'shape' => 'TargetValues', ], ], ], 'TargetCount' => [ 'type' => 'integer', ], 'TargetInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TargetKey' => [ 'type' => 'string', 'max' => 163, 'min' => 1, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:/=\\-@]*$|resource-groups:ResourceTypeFilters|resource-groups:Name', ], 'TargetLocation' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'Accounts', ], 'Regions' => [ 'shape' => 'Regions', ], 'TargetLocationMaxConcurrency' => [ 'shape' => 'MaxConcurrency', 'box' => true, ], 'TargetLocationMaxErrors' => [ 'shape' => 'MaxErrors', 'box' => true, ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', 'box' => true, ], 'TargetLocationAlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', 'box' => true, ], ], ], 'TargetLocations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetLocation', ], 'max' => 100, 'min' => 1, ], 'TargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TargetMapKey', ], 'value' => [ 'shape' => 'TargetMapValueList', ], 'max' => 20, 'min' => 1, ], 'TargetMapKey' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'TargetMapValue' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'TargetMapValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetMapValue', ], 'max' => 25, 'min' => 0, ], 'TargetMaps' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetMap', ], 'max' => 300, 'min' => 0, ], 'TargetNotConnected' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TargetParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterValue', ], ], 'TargetType' => [ 'type' => 'string', 'max' => 200, 'pattern' => '^\\/[\\w\\.\\-\\:\\/]*$', ], 'TargetValue' => [ 'type' => 'string', ], 'TargetValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetValue', ], 'max' => 50, 'min' => 0, ], 'Targets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 5, 'min' => 0, ], 'TerminateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', ], 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], ], ], 'TerminateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'SessionId' => [ 'shape' => 'SessionId', ], ], ], 'TimeoutSeconds' => [ 'type' => 'integer', 'max' => 2592000, 'min' => 30, ], 'TokenValue' => [ 'type' => 'string', 'max' => 300, 'min' => 0, ], 'TooManyTagsError' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TooManyUpdates' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TotalCount' => [ 'type' => 'integer', ], 'TotalSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UUID' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'UnlabelParameterVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ParameterVersion', 'Labels', ], 'members' => [ 'Name' => [ 'shape' => 'PSParameterName', ], 'ParameterVersion' => [ 'shape' => 'PSParameterVersion', 'box' => true, ], 'Labels' => [ 'shape' => 'ParameterLabelList', ], ], ], 'UnlabelParameterVersionResult' => [ 'type' => 'structure', 'members' => [ 'RemovedLabels' => [ 'shape' => 'ParameterLabelList', ], 'InvalidLabels' => [ 'shape' => 'ParameterLabelList', ], ], ], 'UnsupportedCalendarException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedFeatureRequiredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedInventoryItemContextException' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'InventoryItemTypeName', ], 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedInventorySchemaVersionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedOperatingSystem' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedParameterType' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedPlatformType' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UpdateAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AssociationId', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'OutputLocation' => [ 'shape' => 'InstanceAssociationOutputLocation', ], 'Name' => [ 'shape' => 'DocumentARN', ], 'Targets' => [ 'shape' => 'Targets', ], 'AssociationName' => [ 'shape' => 'AssociationName', ], 'AssociationVersion' => [ 'shape' => 'AssociationVersion', ], 'AutomationTargetParameterName' => [ 'shape' => 'AutomationTargetParameterName', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'ComplianceSeverity' => [ 'shape' => 'AssociationComplianceSeverity', ], 'SyncCompliance' => [ 'shape' => 'AssociationSyncCompliance', ], 'ApplyOnlyAtCronInterval' => [ 'shape' => 'ApplyOnlyAtCronInterval', ], 'CalendarNames' => [ 'shape' => 'CalendarNameOrARNList', ], 'TargetLocations' => [ 'shape' => 'TargetLocations', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'Duration', 'box' => true, ], 'TargetMaps' => [ 'shape' => 'TargetMaps', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'UpdateAssociationResult' => [ 'type' => 'structure', 'members' => [ 'AssociationDescription' => [ 'shape' => 'AssociationDescription', ], ], ], 'UpdateAssociationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceId', 'AssociationStatus', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AssociationStatus' => [ 'shape' => 'AssociationStatus', ], ], ], 'UpdateAssociationStatusResult' => [ 'type' => 'structure', 'members' => [ 'AssociationDescription' => [ 'shape' => 'AssociationDescription', ], ], ], 'UpdateDocumentDefaultVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DocumentVersion', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersionNumber', ], ], ], 'UpdateDocumentDefaultVersionResult' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DocumentDefaultVersionDescription', ], ], ], 'UpdateDocumentMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DocumentReviews', ], 'members' => [ 'Name' => [ 'shape' => 'DocumentName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentReviews' => [ 'shape' => 'DocumentReviews', ], ], ], 'UpdateDocumentMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'Content', 'Name', ], 'members' => [ 'Content' => [ 'shape' => 'DocumentContent', ], 'Attachments' => [ 'shape' => 'AttachmentsSourceList', ], 'Name' => [ 'shape' => 'DocumentName', ], 'DisplayName' => [ 'shape' => 'DocumentDisplayName', ], 'VersionName' => [ 'shape' => 'DocumentVersionName', ], 'DocumentVersion' => [ 'shape' => 'DocumentVersion', ], 'DocumentFormat' => [ 'shape' => 'DocumentFormat', ], 'TargetType' => [ 'shape' => 'TargetType', ], ], ], 'UpdateDocumentResult' => [ 'type' => 'structure', 'members' => [ 'DocumentDescription' => [ 'shape' => 'DocumentDescription', ], ], ], 'UpdateMaintenanceWindowRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'StartDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'EndDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'Schedule' => [ 'shape' => 'MaintenanceWindowSchedule', ], 'ScheduleTimezone' => [ 'shape' => 'MaintenanceWindowTimezone', ], 'ScheduleOffset' => [ 'shape' => 'MaintenanceWindowOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'MaintenanceWindowDurationHours', 'box' => true, ], 'Cutoff' => [ 'shape' => 'MaintenanceWindowCutoff', 'box' => true, ], 'AllowUnassociatedTargets' => [ 'shape' => 'MaintenanceWindowAllowUnassociatedTargets', 'box' => true, ], 'Enabled' => [ 'shape' => 'MaintenanceWindowEnabled', 'box' => true, ], 'Replace' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'UpdateMaintenanceWindowResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'StartDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'EndDate' => [ 'shape' => 'MaintenanceWindowStringDateTime', ], 'Schedule' => [ 'shape' => 'MaintenanceWindowSchedule', ], 'ScheduleTimezone' => [ 'shape' => 'MaintenanceWindowTimezone', ], 'ScheduleOffset' => [ 'shape' => 'MaintenanceWindowOffset', 'box' => true, ], 'Duration' => [ 'shape' => 'MaintenanceWindowDurationHours', ], 'Cutoff' => [ 'shape' => 'MaintenanceWindowCutoff', ], 'AllowUnassociatedTargets' => [ 'shape' => 'MaintenanceWindowAllowUnassociatedTargets', ], 'Enabled' => [ 'shape' => 'MaintenanceWindowEnabled', ], ], ], 'UpdateMaintenanceWindowTargetRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'WindowTargetId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], 'Targets' => [ 'shape' => 'Targets', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'Replace' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'UpdateMaintenanceWindowTargetResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTargetId' => [ 'shape' => 'MaintenanceWindowTargetId', ], 'Targets' => [ 'shape' => 'Targets', ], 'OwnerInformation' => [ 'shape' => 'OwnerInformation', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], ], ], 'UpdateMaintenanceWindowTaskRequest' => [ 'type' => 'structure', 'required' => [ 'WindowId', 'WindowTaskId', ], 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], 'Targets' => [ 'shape' => 'Targets', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'TaskInvocationParameters' => [ 'shape' => 'MaintenanceWindowTaskInvocationParameters', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', 'box' => true, ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'Replace' => [ 'shape' => 'Boolean', 'box' => true, ], 'CutoffBehavior' => [ 'shape' => 'MaintenanceWindowTaskCutoffBehavior', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'UpdateMaintenanceWindowTaskResult' => [ 'type' => 'structure', 'members' => [ 'WindowId' => [ 'shape' => 'MaintenanceWindowId', ], 'WindowTaskId' => [ 'shape' => 'MaintenanceWindowTaskId', ], 'Targets' => [ 'shape' => 'Targets', ], 'TaskArn' => [ 'shape' => 'MaintenanceWindowTaskArn', ], 'ServiceRoleArn' => [ 'shape' => 'ServiceRole', ], 'TaskParameters' => [ 'shape' => 'MaintenanceWindowTaskParameters', ], 'TaskInvocationParameters' => [ 'shape' => 'MaintenanceWindowTaskInvocationParameters', ], 'Priority' => [ 'shape' => 'MaintenanceWindowTaskPriority', ], 'MaxConcurrency' => [ 'shape' => 'MaxConcurrency', ], 'MaxErrors' => [ 'shape' => 'MaxErrors', ], 'LoggingInfo' => [ 'shape' => 'LoggingInfo', ], 'Name' => [ 'shape' => 'MaintenanceWindowName', ], 'Description' => [ 'shape' => 'MaintenanceWindowDescription', ], 'CutoffBehavior' => [ 'shape' => 'MaintenanceWindowTaskCutoffBehavior', 'box' => true, ], 'AlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'UpdateManagedInstanceRoleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IamRole', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ManagedInstanceId', ], 'IamRole' => [ 'shape' => 'IamRole', ], ], ], 'UpdateManagedInstanceRoleResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOpsItemRequest' => [ 'type' => 'structure', 'required' => [ 'OpsItemId', ], 'members' => [ 'Description' => [ 'shape' => 'OpsItemDescription', ], 'OperationalData' => [ 'shape' => 'OpsItemOperationalData', ], 'OperationalDataToDelete' => [ 'shape' => 'OpsItemOpsDataKeysList', ], 'Notifications' => [ 'shape' => 'OpsItemNotifications', ], 'Priority' => [ 'shape' => 'OpsItemPriority', ], 'RelatedOpsItems' => [ 'shape' => 'RelatedOpsItems', ], 'Status' => [ 'shape' => 'OpsItemStatus', ], 'OpsItemId' => [ 'shape' => 'OpsItemId', ], 'Title' => [ 'shape' => 'OpsItemTitle', ], 'Category' => [ 'shape' => 'OpsItemCategory', ], 'Severity' => [ 'shape' => 'OpsItemSeverity', ], 'ActualStartTime' => [ 'shape' => 'DateTime', ], 'ActualEndTime' => [ 'shape' => 'DateTime', ], 'PlannedStartTime' => [ 'shape' => 'DateTime', ], 'PlannedEndTime' => [ 'shape' => 'DateTime', ], 'OpsItemArn' => [ 'shape' => 'OpsItemArn', ], ], ], 'UpdateOpsItemResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOpsMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'OpsMetadataArn', ], 'members' => [ 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], 'MetadataToUpdate' => [ 'shape' => 'MetadataMap', ], 'KeysToDelete' => [ 'shape' => 'MetadataKeysToDeleteList', ], ], ], 'UpdateOpsMetadataResult' => [ 'type' => 'structure', 'members' => [ 'OpsMetadataArn' => [ 'shape' => 'OpsMetadataArn', ], ], ], 'UpdatePatchBaselineRequest' => [ 'type' => 'structure', 'required' => [ 'BaselineId', ], 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'Name' => [ 'shape' => 'BaselineName', ], 'GlobalFilters' => [ 'shape' => 'PatchFilterGroup', ], 'ApprovalRules' => [ 'shape' => 'PatchRuleGroup', ], 'ApprovedPatches' => [ 'shape' => 'PatchIdList', ], 'ApprovedPatchesComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApprovedPatchesEnableNonSecurity' => [ 'shape' => 'Boolean', 'box' => true, ], 'RejectedPatches' => [ 'shape' => 'PatchIdList', ], 'RejectedPatchesAction' => [ 'shape' => 'PatchAction', ], 'Description' => [ 'shape' => 'BaselineDescription', ], 'Sources' => [ 'shape' => 'PatchSourceList', ], 'Replace' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'UpdatePatchBaselineResult' => [ 'type' => 'structure', 'members' => [ 'BaselineId' => [ 'shape' => 'BaselineId', ], 'Name' => [ 'shape' => 'BaselineName', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'GlobalFilters' => [ 'shape' => 'PatchFilterGroup', ], 'ApprovalRules' => [ 'shape' => 'PatchRuleGroup', ], 'ApprovedPatches' => [ 'shape' => 'PatchIdList', ], 'ApprovedPatchesComplianceLevel' => [ 'shape' => 'PatchComplianceLevel', ], 'ApprovedPatchesEnableNonSecurity' => [ 'shape' => 'Boolean', 'box' => true, ], 'RejectedPatches' => [ 'shape' => 'PatchIdList', ], 'RejectedPatchesAction' => [ 'shape' => 'PatchAction', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'ModifiedDate' => [ 'shape' => 'DateTime', ], 'Description' => [ 'shape' => 'BaselineDescription', ], 'Sources' => [ 'shape' => 'PatchSourceList', ], ], ], 'UpdateResourceDataSyncRequest' => [ 'type' => 'structure', 'required' => [ 'SyncName', 'SyncType', 'SyncSource', ], 'members' => [ 'SyncName' => [ 'shape' => 'ResourceDataSyncName', ], 'SyncType' => [ 'shape' => 'ResourceDataSyncType', ], 'SyncSource' => [ 'shape' => 'ResourceDataSyncSource', ], ], ], 'UpdateResourceDataSyncResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateServiceSettingRequest' => [ 'type' => 'structure', 'required' => [ 'SettingId', 'SettingValue', ], 'members' => [ 'SettingId' => [ 'shape' => 'ServiceSettingId', ], 'SettingValue' => [ 'shape' => 'ServiceSettingValue', ], ], ], 'UpdateServiceSettingResult' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', ], 'ValidNextStep' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'ValidNextStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidNextStep', ], ], 'Version' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,6}(\\.[0-9]{1,6}){2,3}$', ], ],];
