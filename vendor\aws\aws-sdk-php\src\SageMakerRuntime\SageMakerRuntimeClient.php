<?php
namespace Aws\SageMakerRuntime;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon SageMaker Runtime** service.
 * @method \Aws\Result invokeEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeEndpointAsync(array $args = [])
 * @method \Aws\Result invokeEndpointAsync(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeEndpointAsyncAsync(array $args = [])
 * @method \Aws\Result invokeEndpointWithResponseStream(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeEndpointWithResponseStreamAsync(array $args = [])
 */
class SageMakerRuntimeClient extends AwsClient {}
