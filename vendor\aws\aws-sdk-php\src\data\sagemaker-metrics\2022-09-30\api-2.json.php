<?php
// This file was auto-generated from sdk-root/src/data/sagemaker-metrics/2022-09-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-09-30', 'endpointPrefix' => 'metrics.sagemaker', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'SageMaker Metrics', 'serviceFullName' => 'Amazon SageMaker Metrics Service', 'serviceId' => 'SageMaker Metrics', 'signatureVersion' => 'v4', 'signingName' => 'sagemaker', 'uid' => 'sagemaker-metrics-2022-09-30', ], 'operations' => [ 'BatchPutMetrics' => [ 'name' => 'BatchPutMetrics', 'http' => [ 'method' => 'PUT', 'requestUri' => '/BatchPutMetrics', ], 'input' => [ 'shape' => 'BatchPutMetricsRequest', ], 'output' => [ 'shape' => 'BatchPutMetricsResponse', ], ], ], 'shapes' => [ 'BatchPutMetricsError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'PutMetricsErrorCode', ], 'MetricIndex' => [ 'shape' => 'Integer', ], ], ], 'BatchPutMetricsErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutMetricsError', ], 'max' => 10, 'min' => 1, ], 'BatchPutMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', 'MetricData', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'MetricData' => [ 'shape' => 'RawMetricDataList', ], ], ], 'BatchPutMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'BatchPutMetricsErrorList', ], ], ], 'Double' => [ 'type' => 'double', ], 'ExperimentEntityName' => [ 'type' => 'string', 'max' => 120, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,119}', ], 'Integer' => [ 'type' => 'integer', ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'PutMetricsErrorCode' => [ 'type' => 'string', 'enum' => [ 'METRIC_LIMIT_EXCEEDED', 'INTERNAL_ERROR', 'VALIDATION_ERROR', 'CONFLICT_ERROR', ], ], 'RawMetricData' => [ 'type' => 'structure', 'required' => [ 'MetricName', 'Timestamp', 'Value', ], 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Step' => [ 'shape' => 'Step', ], 'Value' => [ 'shape' => 'Double', ], ], ], 'RawMetricDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RawMetricData', ], 'max' => 10, 'min' => 1, ], 'Step' => [ 'type' => 'integer', 'min' => 0, ], 'Timestamp' => [ 'type' => 'timestamp', ], ],];
