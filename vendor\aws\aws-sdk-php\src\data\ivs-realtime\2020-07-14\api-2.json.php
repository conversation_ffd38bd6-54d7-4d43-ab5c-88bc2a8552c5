<?php
// This file was auto-generated from sdk-root/src/data/ivs-realtime/2020-07-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-14', 'endpointPrefix' => 'ivsrealtime', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'ivsrealtime', 'serviceFullName' => 'Amazon Interactive Video Service RealTime', 'serviceId' => 'IVS RealTime', 'signatureVersion' => 'v4', 'signingName' => 'ivs', 'uid' => 'ivs-realtime-2020-07-14', ], 'operations' => [ 'CreateEncoderConfiguration' => [ 'name' => 'CreateEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'CreateEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateParticipantToken' => [ 'name' => 'CreateParticipantToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateParticipantToken', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateParticipantTokenRequest', ], 'output' => [ 'shape' => 'CreateParticipantTokenResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateStage' => [ 'name' => 'CreateStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStageRequest', ], 'output' => [ 'shape' => 'CreateStageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateStorageConfiguration' => [ 'name' => 'CreateStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStorageConfigurationRequest', ], 'output' => [ 'shape' => 'CreateStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeleteEncoderConfiguration' => [ 'name' => 'DeleteEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteStage' => [ 'name' => 'DeleteStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStageRequest', ], 'output' => [ 'shape' => 'DeleteStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeleteStorageConfiguration' => [ 'name' => 'DeleteStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStorageConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisconnectParticipant' => [ 'name' => 'DisconnectParticipant', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectParticipant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectParticipantRequest', ], 'output' => [ 'shape' => 'DisconnectParticipantResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PendingVerification', ], ], ], 'GetComposition' => [ 'name' => 'GetComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCompositionRequest', ], 'output' => [ 'shape' => 'GetCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetEncoderConfiguration' => [ 'name' => 'GetEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'GetEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetParticipant' => [ 'name' => 'GetParticipant', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetParticipant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetParticipantRequest', ], 'output' => [ 'shape' => 'GetParticipantResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStage' => [ 'name' => 'GetStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStageRequest', ], 'output' => [ 'shape' => 'GetStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStageSession' => [ 'name' => 'GetStageSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStageSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStageSessionRequest', ], 'output' => [ 'shape' => 'GetStageSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStorageConfiguration' => [ 'name' => 'GetStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageConfigurationRequest', ], 'output' => [ 'shape' => 'GetStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListCompositions' => [ 'name' => 'ListCompositions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListCompositions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCompositionsRequest', ], 'output' => [ 'shape' => 'ListCompositionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListEncoderConfigurations' => [ 'name' => 'ListEncoderConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListEncoderConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEncoderConfigurationsRequest', ], 'output' => [ 'shape' => 'ListEncoderConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListParticipantEvents' => [ 'name' => 'ListParticipantEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListParticipantEvents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListParticipantEventsRequest', ], 'output' => [ 'shape' => 'ListParticipantEventsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListParticipants' => [ 'name' => 'ListParticipants', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListParticipants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListParticipantsRequest', ], 'output' => [ 'shape' => 'ListParticipantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListStageSessions' => [ 'name' => 'ListStageSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStageSessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStageSessionsRequest', ], 'output' => [ 'shape' => 'ListStageSessionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListStages' => [ 'name' => 'ListStages', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStagesRequest', ], 'output' => [ 'shape' => 'ListStagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListStorageConfigurations' => [ 'name' => 'ListStorageConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStorageConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageConfigurationsRequest', ], 'output' => [ 'shape' => 'ListStorageConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartComposition' => [ 'name' => 'StartComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCompositionRequest', ], 'output' => [ 'shape' => 'StartCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'StopComposition' => [ 'name' => 'StopComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopCompositionRequest', ], 'output' => [ 'shape' => 'StopCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateStage' => [ 'name' => 'UpdateStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStageRequest', ], 'output' => [ 'shape' => 'UpdateStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'Bitrate' => [ 'type' => 'integer', 'box' => true, 'max' => 8500000, 'min' => 1, ], 'ChannelArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:channel/[a-zA-Z0-9-]+$', ], 'ChannelDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'encoderConfigurationArn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'Composition' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinations', 'layout', 'stageArn', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], 'destinations' => [ 'shape' => 'DestinationList', ], 'endTime' => [ 'shape' => 'Time', ], 'layout' => [ 'shape' => 'LayoutConfiguration', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'startTime' => [ 'shape' => 'Time', ], 'state' => [ 'shape' => 'CompositionState', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CompositionArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:composition/[a-zA-Z0-9-]+$', ], 'CompositionClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'CompositionState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'FAILED', 'STOPPED', ], ], 'CompositionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinations', 'stageArn', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], 'destinations' => [ 'shape' => 'DestinationSummaryList', ], 'endTime' => [ 'shape' => 'Time', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'startTime' => [ 'shape' => 'Time', ], 'state' => [ 'shape' => 'CompositionState', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CompositionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositionSummary', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateEncoderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'tags' => [ 'shape' => 'Tags', ], 'video' => [ 'shape' => 'Video', ], ], ], 'CreateEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'encoderConfiguration' => [ 'shape' => 'EncoderConfiguration', ], ], ], 'CreateParticipantTokenRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', ], 'members' => [ 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], ], ], 'CreateParticipantTokenResponse' => [ 'type' => 'structure', 'members' => [ 'participantToken' => [ 'shape' => 'ParticipantToken', ], ], ], 'CreateStageRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'StageName', ], 'participantTokenConfigurations' => [ 'shape' => 'ParticipantTokenConfigurations', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStageResponse' => [ 'type' => 'structure', 'members' => [ 'participantTokens' => [ 'shape' => 'ParticipantTokenList', ], 'stage' => [ 'shape' => 'Stage', ], ], ], 'CreateStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 's3', ], 'members' => [ 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'DeleteEncoderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'DeleteEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], ], ], 'DeleteStageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], ], ], 'DeleteStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'configuration', 'id', 'state', ], 'members' => [ 'configuration' => [ 'shape' => 'DestinationConfiguration', ], 'detail' => [ 'shape' => 'DestinationDetail', ], 'endTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Time', ], 'state' => [ 'shape' => 'DestinationState', ], ], ], 'DestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'ChannelDestinationConfiguration', ], 'name' => [ 'shape' => 'DestinationConfigurationName', ], 's3' => [ 'shape' => 'S3DestinationConfiguration', ], ], ], 'DestinationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationConfiguration', ], 'max' => 2, 'min' => 1, ], 'DestinationConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'DestinationDetail' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'S3Detail', ], ], ], 'DestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], 'max' => 2, 'min' => 1, ], 'DestinationState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'RECONNECTING', 'FAILED', 'STOPPED', ], ], 'DestinationSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'state', ], 'members' => [ 'endTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Time', ], 'state' => [ 'shape' => 'DestinationState', ], ], ], 'DestinationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationSummary', ], 'max' => 2, 'min' => 1, ], 'DisconnectParticipantReason' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'DisconnectParticipantRequest' => [ 'type' => 'structure', 'required' => [ 'participantId', 'stageArn', ], 'members' => [ 'participantId' => [ 'shape' => 'ParticipantTokenId', ], 'reason' => [ 'shape' => 'DisconnectParticipantReason', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'DisconnectParticipantResponse' => [ 'type' => 'structure', 'members' => [], ], 'EncoderConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'tags' => [ 'shape' => 'Tags', ], 'video' => [ 'shape' => 'Video', ], ], ], 'EncoderConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:encoder-configuration/[a-zA-Z0-9-]+$', ], 'EncoderConfigurationArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncoderConfigurationArn', ], 'max' => 1, 'min' => 1, ], 'EncoderConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'EncoderConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'EncoderConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncoderConfigurationSummary', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'EventErrorCode', ], 'eventTime' => [ 'shape' => 'Time', ], 'name' => [ 'shape' => 'EventName', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'remoteParticipantId' => [ 'shape' => 'ParticipantId', ], ], ], 'EventErrorCode' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_CAPABILITIES', 'QUOTA_EXCEEDED', 'PUBLISHER_NOT_FOUND', ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'EventName' => [ 'type' => 'string', 'enum' => [ 'JOINED', 'LEFT', 'PUBLISH_STARTED', 'PUBLISH_STOPPED', 'SUBSCRIBE_STARTED', 'SUBSCRIBE_STOPPED', 'PUBLISH_ERROR', 'SUBSCRIBE_ERROR', 'JOIN_ERROR', ], ], 'Framerate' => [ 'type' => 'float', 'box' => true, 'max' => 60, 'min' => 1, ], 'GetCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], ], ], 'GetCompositionResponse' => [ 'type' => 'structure', 'members' => [ 'composition' => [ 'shape' => 'Composition', ], ], ], 'GetEncoderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'GetEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'encoderConfiguration' => [ 'shape' => 'EncoderConfiguration', ], ], ], 'GetParticipantRequest' => [ 'type' => 'structure', 'required' => [ 'participantId', 'sessionId', 'stageArn', ], 'members' => [ 'participantId' => [ 'shape' => 'ParticipantId', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'GetParticipantResponse' => [ 'type' => 'structure', 'members' => [ 'participant' => [ 'shape' => 'Participant', ], ], ], 'GetStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], ], ], 'GetStageResponse' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], ], ], 'GetStageSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'stageArn', ], 'members' => [ 'sessionId' => [ 'shape' => 'StageSessionId', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'GetStageSessionResponse' => [ 'type' => 'structure', 'members' => [ 'stageSession' => [ 'shape' => 'StageSession', ], ], ], 'GetStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], ], ], 'GetStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'GridConfiguration' => [ 'type' => 'structure', 'members' => [ 'featuredParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'gridGap' => [ 'shape' => 'GridGap', ], 'omitStoppedVideo' => [ 'shape' => 'OmitStoppedVideo', ], 'videoAspectRatio' => [ 'shape' => 'VideoAspectRatio', ], 'videoFillMode' => [ 'shape' => 'VideoFillMode', ], ], ], 'GridGap' => [ 'type' => 'integer', 'min' => 0, ], 'Height' => [ 'type' => 'integer', 'box' => true, 'max' => 1920, 'min' => 1, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LayoutConfiguration' => [ 'type' => 'structure', 'members' => [ 'grid' => [ 'shape' => 'GridConfiguration', ], 'pip' => [ 'shape' => 'PipConfiguration', ], ], ], 'ListCompositionsRequest' => [ 'type' => 'structure', 'members' => [ 'filterByEncoderConfigurationArn' => [ 'shape' => 'EncoderConfigurationArn', ], 'filterByStageArn' => [ 'shape' => 'StageArn', ], 'maxResults' => [ 'shape' => 'MaxCompositionResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListCompositionsResponse' => [ 'type' => 'structure', 'required' => [ 'compositions', ], 'members' => [ 'compositions' => [ 'shape' => 'CompositionSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEncoderConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxEncoderConfigurationResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEncoderConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'encoderConfigurations', ], 'members' => [ 'encoderConfigurations' => [ 'shape' => 'EncoderConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListParticipantEventsRequest' => [ 'type' => 'structure', 'required' => [ 'participantId', 'sessionId', 'stageArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxParticipantEventResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'ListParticipantEventsResponse' => [ 'type' => 'structure', 'required' => [ 'events', ], 'members' => [ 'events' => [ 'shape' => 'EventList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListParticipantsRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'stageArn', ], 'members' => [ 'filterByPublished' => [ 'shape' => 'Published', ], 'filterByState' => [ 'shape' => 'ParticipantState', ], 'filterByUserId' => [ 'shape' => 'UserId', ], 'maxResults' => [ 'shape' => 'MaxParticipantResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'ListParticipantsResponse' => [ 'type' => 'structure', 'required' => [ 'participants', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'participants' => [ 'shape' => 'ParticipantList', ], ], ], 'ListStageSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxStageSessionResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'stageArn' => [ 'shape' => 'StageArn', ], ], ], 'ListStageSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'stageSessions', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'stageSessions' => [ 'shape' => 'StageSessionList', ], ], ], 'ListStagesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxStageResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStagesResponse' => [ 'type' => 'structure', 'required' => [ 'stages', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'stages' => [ 'shape' => 'StageSummaryList', ], ], ], 'ListStorageConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxStorageConfigurationResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStorageConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'storageConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'storageConfigurations' => [ 'shape' => 'StorageConfigurationSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'MaxCompositionResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxEncoderConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxParticipantEventResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxParticipantResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStageResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStageSessionResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStorageConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'OmitStoppedVideo' => [ 'type' => 'boolean', ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[a-zA-Z0-9+/=_-]*$', ], 'Participant' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'ParticipantAttributes', ], 'browserName' => [ 'shape' => 'ParticipantClientAttribute', ], 'browserVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'firstJoinTime' => [ 'shape' => 'Time', ], 'ispName' => [ 'shape' => 'ParticipantClientAttribute', ], 'osName' => [ 'shape' => 'ParticipantClientAttribute', ], 'osVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'published' => [ 'shape' => 'Published', ], 'sdkVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'state' => [ 'shape' => 'ParticipantState', ], 'userId' => [ 'shape' => 'UserId', ], ], ], 'ParticipantAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ParticipantClientAttribute' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_.,:;\\s]*$', ], 'ParticipantId' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-]*$', ], 'ParticipantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantSummary', ], ], 'ParticipantState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'DISCONNECTED', ], ], 'ParticipantSummary' => [ 'type' => 'structure', 'members' => [ 'firstJoinTime' => [ 'shape' => 'Time', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'published' => [ 'shape' => 'Published', ], 'state' => [ 'shape' => 'ParticipantState', ], 'userId' => [ 'shape' => 'UserId', ], ], ], 'ParticipantToken' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'expirationTime' => [ 'shape' => 'ParticipantTokenExpirationTime', ], 'participantId' => [ 'shape' => 'ParticipantTokenId', ], 'token' => [ 'shape' => 'ParticipantTokenString', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], ], ], 'ParticipantTokenAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ParticipantTokenCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantTokenCapability', ], 'max' => 2, 'min' => 0, ], 'ParticipantTokenCapability' => [ 'type' => 'string', 'enum' => [ 'PUBLISH', 'SUBSCRIBE', ], ], 'ParticipantTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], ], ], 'ParticipantTokenConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantTokenConfiguration', ], 'max' => 12, 'min' => 0, ], 'ParticipantTokenDurationMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 20160, 'min' => 1, ], 'ParticipantTokenExpirationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ParticipantTokenId' => [ 'type' => 'string', ], 'ParticipantTokenList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantToken', ], ], 'ParticipantTokenString' => [ 'type' => 'string', 'sensitive' => true, ], 'ParticipantTokenUserId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PendingVerification' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'PipBehavior' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DYNAMIC', ], ], 'PipConfiguration' => [ 'type' => 'structure', 'members' => [ 'featuredParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'gridGap' => [ 'shape' => 'GridGap', ], 'omitStoppedVideo' => [ 'shape' => 'OmitStoppedVideo', ], 'pipBehavior' => [ 'shape' => 'PipBehavior', ], 'pipHeight' => [ 'shape' => 'PipHeight', ], 'pipOffset' => [ 'shape' => 'PipOffset', ], 'pipParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'pipPosition' => [ 'shape' => 'PipPosition', ], 'pipWidth' => [ 'shape' => 'PipWidth', ], 'videoFillMode' => [ 'shape' => 'VideoFillMode', ], ], ], 'PipHeight' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PipOffset' => [ 'type' => 'integer', 'min' => 0, ], 'PipPosition' => [ 'type' => 'string', 'enum' => [ 'TOP_LEFT', 'TOP_RIGHT', 'BOTTOM_LEFT', 'BOTTOM_RIGHT', ], ], 'PipWidth' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Published' => [ 'type' => 'boolean', ], 'RecordingConfiguration' => [ 'type' => 'structure', 'members' => [ 'format' => [ 'shape' => 'RecordingConfigurationFormat', ], ], ], 'RecordingConfigurationFormat' => [ 'type' => 'string', 'enum' => [ 'HLS', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9-.]+$', ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'encoderConfigurationArns', 'storageConfigurationArn', ], 'members' => [ 'encoderConfigurationArns' => [ 'shape' => 'EncoderConfigurationArnList', ], 'recordingConfiguration' => [ 'shape' => 'RecordingConfiguration', ], 'storageConfigurationArn' => [ 'shape' => 'StorageConfigurationArn', ], ], ], 'S3Detail' => [ 'type' => 'structure', 'required' => [ 'recordingPrefix', ], 'members' => [ 'recordingPrefix' => [ 'shape' => 'String', ], ], ], 'S3StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Stage' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'activeSessionId' => [ 'shape' => 'StageSessionId', ], 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StageArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+$', ], 'StageName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'StageSession' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Time', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'startTime' => [ 'shape' => 'Time', ], ], ], 'StageSessionId' => [ 'type' => 'string', 'max' => 16, 'min' => 16, 'pattern' => '^st-[a-zA-Z0-9]+$', ], 'StageSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StageSessionSummary', ], ], 'StageSessionSummary' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Time', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'startTime' => [ 'shape' => 'Time', ], ], ], 'StageSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'activeSessionId' => [ 'shape' => 'StageSessionId', ], 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StageSummary', ], ], 'StartCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'destinations', 'stageArn', ], 'members' => [ 'destinations' => [ 'shape' => 'DestinationConfigurationList', ], 'idempotencyToken' => [ 'shape' => 'CompositionClientToken', 'idempotencyToken' => true, ], 'layout' => [ 'shape' => 'LayoutConfiguration', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StartCompositionResponse' => [ 'type' => 'structure', 'members' => [ 'composition' => [ 'shape' => 'Composition', ], ], ], 'StopCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], ], ], 'StopCompositionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StorageConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:storage-configuration/[a-zA-Z0-9-]+$', ], 'StorageConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'StorageConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StorageConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageConfigurationSummary', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'Time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], ], ], 'UpdateStageResponse' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], ], ], 'UserId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Video' => [ 'type' => 'structure', 'members' => [ 'bitrate' => [ 'shape' => 'Bitrate', ], 'framerate' => [ 'shape' => 'Framerate', ], 'height' => [ 'shape' => 'Height', ], 'width' => [ 'shape' => 'Width', ], ], ], 'VideoAspectRatio' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'VIDEO', 'SQUARE', 'PORTRAIT', ], ], 'VideoFillMode' => [ 'type' => 'string', 'enum' => [ 'FILL', 'COVER', 'CONTAIN', ], ], 'Width' => [ 'type' => 'integer', 'box' => true, 'max' => 1920, 'min' => 1, ], 'errorMessage' => [ 'type' => 'string', ], ],];
