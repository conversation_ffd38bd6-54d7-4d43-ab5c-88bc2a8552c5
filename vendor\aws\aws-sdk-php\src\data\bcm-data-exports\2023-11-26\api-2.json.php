<?php
// This file was auto-generated from sdk-root/src/data/bcm-data-exports/2023-11-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-11-26', 'endpointPrefix' => 'bcm-data-exports', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Billing and Cost Management Data Exports', 'serviceId' => 'BCM Data Exports', 'signatureVersion' => 'v4', 'signingName' => 'bcm-data-exports', 'targetPrefix' => 'AWSBillingAndCostManagementDataExports', 'uid' => 'bcm-data-exports-2023-11-26', ], 'operations' => [ 'CreateExport' => [ 'name' => 'CreateExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExportRequest', ], 'output' => [ 'shape' => 'CreateExportResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteExport' => [ 'name' => 'DeleteExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExportRequest', ], 'output' => [ 'shape' => 'DeleteExportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetExecution' => [ 'name' => 'GetExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetExecutionRequest', ], 'output' => [ 'shape' => 'GetExecutionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetExport' => [ 'name' => 'GetExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetExportRequest', ], 'output' => [ 'shape' => 'GetExportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTable' => [ 'name' => 'GetTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableRequest', ], 'output' => [ 'shape' => 'GetTableResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListExecutions' => [ 'name' => 'ListExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExecutionsRequest', ], 'output' => [ 'shape' => 'ListExecutionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExportsRequest', ], 'output' => [ 'shape' => 'ListExportsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTables' => [ 'name' => 'ListTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTablesRequest', ], 'output' => [ 'shape' => 'ListTablesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateExport' => [ 'name' => 'UpdateExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateExportRequest', ], 'output' => [ 'shape' => 'UpdateExportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z0-9]*:[-a-z0-9]+:[-a-z0-9]*:[0-9]{12}:[-a-zA-Z0-9/:_]+$', ], 'Column' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'Type' => [ 'shape' => 'GenericString', ], ], ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Column', ], ], 'CompressionOption' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'PARQUET', ], ], 'CreateExportRequest' => [ 'type' => 'structure', 'required' => [ 'Export', ], 'members' => [ 'Export' => [ 'shape' => 'Export', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'DataQuery' => [ 'type' => 'structure', 'required' => [ 'QueryStatement', ], 'members' => [ 'QueryStatement' => [ 'shape' => 'QueryStatement', ], 'TableConfigurations' => [ 'shape' => 'TableConfigurations', ], ], ], 'DeleteExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExportArn', ], 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'DestinationConfigurations' => [ 'type' => 'structure', 'required' => [ 'S3Destination', ], 'members' => [ 'S3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExecutionReference' => [ 'type' => 'structure', 'required' => [ 'ExecutionId', 'ExecutionStatus', ], 'members' => [ 'ExecutionId' => [ 'shape' => 'GenericString', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], ], ], 'ExecutionReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecutionReference', ], ], 'ExecutionStatus' => [ 'type' => 'structure', 'members' => [ 'CompletedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'CreatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'LastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'StatusCode' => [ 'shape' => 'ExecutionStatusCode', ], 'StatusReason' => [ 'shape' => 'ExecutionStatusReason', ], ], ], 'ExecutionStatusCode' => [ 'type' => 'string', 'enum' => [ 'INITIATION_IN_PROCESS', 'QUERY_QUEUED', 'QUERY_IN_PROCESS', 'QUERY_FAILURE', 'DELIVERY_IN_PROCESS', 'DELIVERY_SUCCESS', 'DELIVERY_FAILURE', ], ], 'ExecutionStatusReason' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_PERMISSION', 'BILL_OWNER_CHANGED', 'INTERNAL_FAILURE', ], ], 'Export' => [ 'type' => 'structure', 'required' => [ 'DataQuery', 'DestinationConfigurations', 'Name', 'RefreshCadence', ], 'members' => [ 'DataQuery' => [ 'shape' => 'DataQuery', ], 'Description' => [ 'shape' => 'GenericString', ], 'DestinationConfigurations' => [ 'shape' => 'DestinationConfigurations', ], 'ExportArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ExportName', ], 'RefreshCadence' => [ 'shape' => 'RefreshCadence', ], ], ], 'ExportName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9A-Za-z!\\-_.*\\\'()]+$', ], 'ExportReference' => [ 'type' => 'structure', 'required' => [ 'ExportArn', 'ExportName', 'ExportStatus', ], 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], 'ExportName' => [ 'shape' => 'ExportName', ], 'ExportStatus' => [ 'shape' => 'ExportStatus', ], ], ], 'ExportReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportReference', ], ], 'ExportStatus' => [ 'type' => 'structure', 'members' => [ 'CreatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'LastRefreshedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'LastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'StatusCode' => [ 'shape' => 'ExportStatusCode', ], 'StatusReason' => [ 'shape' => 'ExecutionStatusReason', ], ], ], 'ExportStatusCode' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'FormatOption' => [ 'type' => 'string', 'enum' => [ 'TEXT_OR_CSV', 'PARQUET', ], ], 'FrequencyOption' => [ 'type' => 'string', 'enum' => [ 'SYNCHRONOUS', ], ], 'GenericString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[\\S\\s]*$', ], 'GenericStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'GetExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'ExecutionId', 'ExportArn', ], 'members' => [ 'ExecutionId' => [ 'shape' => 'GenericString', ], 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'GetExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionId' => [ 'shape' => 'GenericString', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'Export' => [ 'shape' => 'Export', ], ], ], 'GetExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExportArn', ], 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'GetExportResponse' => [ 'type' => 'structure', 'members' => [ 'Export' => [ 'shape' => 'Export', ], 'ExportStatus' => [ 'shape' => 'ExportStatus', ], ], ], 'GetTableRequest' => [ 'type' => 'structure', 'required' => [ 'TableName', ], 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'TableProperties' => [ 'shape' => 'TableProperties', ], ], ], 'GetTableResponse' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'GenericString', ], 'Schema' => [ 'shape' => 'ColumnList', ], 'TableName' => [ 'shape' => 'TableName', ], 'TableProperties' => [ 'shape' => 'TableProperties', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], ], 'exception' => true, 'fault' => true, ], 'ListExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'ExportArn', ], 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'Executions' => [ 'shape' => 'ExecutionReferenceList', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListExportsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListExportsResponse' => [ 'type' => 'structure', 'members' => [ 'Exports' => [ 'shape' => 'ExportReferenceList', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListTablesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListTablesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextPageToken', ], 'Tables' => [ 'shape' => 'TableList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextPageToken', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NextPageToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'pattern' => '^[\\S\\s]*$', ], 'OverwriteOption' => [ 'type' => 'string', 'enum' => [ 'CREATE_NEW_REPORT', 'OVERWRITE_REPORT', ], ], 'QueryStatement' => [ 'type' => 'string', 'max' => 36000, 'min' => 1, 'pattern' => '^[\\S\\s]*$', ], 'RefreshCadence' => [ 'type' => 'structure', 'required' => [ 'Frequency', ], 'members' => [ 'Frequency' => [ 'shape' => 'FrequencyOption', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'ResourceId' => [ 'shape' => 'GenericString', ], 'ResourceType' => [ 'shape' => 'GenericString', ], ], 'exception' => true, ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'ResourceTagKey', ], 'Value' => [ 'shape' => 'ResourceTagValue', ], ], ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ResourceTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTagKey', ], 'max' => 200, 'min' => 0, ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], 'max' => 200, 'min' => 0, ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'S3Destination' => [ 'type' => 'structure', 'required' => [ 'S3Bucket', 'S3OutputConfigurations', 'S3Prefix', 'S3Region', ], 'members' => [ 'S3Bucket' => [ 'shape' => 'GenericString', ], 'S3OutputConfigurations' => [ 'shape' => 'S3OutputConfigurations', ], 'S3Prefix' => [ 'shape' => 'GenericString', ], 'S3Region' => [ 'shape' => 'GenericString', ], ], ], 'S3OutputConfigurations' => [ 'type' => 'structure', 'required' => [ 'Compression', 'Format', 'OutputType', 'Overwrite', ], 'members' => [ 'Compression' => [ 'shape' => 'CompressionOption', ], 'Format' => [ 'shape' => 'FormatOption', ], 'OutputType' => [ 'shape' => 'S3OutputType', ], 'Overwrite' => [ 'shape' => 'OverwriteOption', ], ], ], 'S3OutputType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'QuotaCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'QuotaCode' => [ 'shape' => 'GenericString', ], 'ResourceId' => [ 'shape' => 'GenericString', ], 'ResourceType' => [ 'shape' => 'GenericString', ], 'ServiceCode' => [ 'shape' => 'GenericString', ], ], 'exception' => true, ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Table' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'GenericString', ], 'TableName' => [ 'shape' => 'TableName', ], 'TableProperties' => [ 'shape' => 'TablePropertyDescriptionList', ], ], ], 'TableConfigurations' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableName', ], 'value' => [ 'shape' => 'TableProperties', ], ], 'TableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Table', ], ], 'TableName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[\\S\\s]*$', ], 'TableProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableProperty', ], 'value' => [ 'shape' => 'GenericString', ], ], 'TableProperty' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[\\S\\s]*$', ], 'TablePropertyDescription' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'GenericString', ], 'Description' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'ValidValues' => [ 'shape' => 'GenericStringList', ], ], ], 'TablePropertyDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TablePropertyDescription', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'QuotaCode' => [ 'shape' => 'GenericString', ], 'ServiceCode' => [ 'shape' => 'GenericString', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'ResourceTagKeys' => [ 'shape' => 'ResourceTagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateExportRequest' => [ 'type' => 'structure', 'required' => [ 'Export', 'ExportArn', ], 'members' => [ 'Export' => [ 'shape' => 'Export', ], 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExportArn' => [ 'shape' => 'Arn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], 'Message' => [ 'shape' => 'GenericString', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Message', 'Name', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], ],];
