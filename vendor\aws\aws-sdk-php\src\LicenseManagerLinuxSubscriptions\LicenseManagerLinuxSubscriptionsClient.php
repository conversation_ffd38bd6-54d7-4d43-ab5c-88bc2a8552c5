<?php
namespace Aws\LicenseManagerLinuxSubscriptions;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS License Manager Linux Subscriptions** service.
 * @method \Aws\Result getServiceSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getServiceSettingsAsync(array $args = [])
 * @method \Aws\Result listLinuxSubscriptionInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLinuxSubscriptionInstancesAsync(array $args = [])
 * @method \Aws\Result listLinuxSubscriptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLinuxSubscriptionsAsync(array $args = [])
 * @method \Aws\Result updateServiceSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateServiceSettingsAsync(array $args = [])
 */
class LicenseManagerLinuxSubscriptionsClient extends AwsClient {}
