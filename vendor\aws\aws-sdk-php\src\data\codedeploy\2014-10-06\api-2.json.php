<?php
// This file was auto-generated from sdk-root/src/data/codedeploy/2014-10-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-10-06', 'endpointPrefix' => 'codedeploy', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'CodeDeploy', 'serviceFullName' => 'AWS CodeDeploy', 'serviceId' => 'CodeDeploy', 'signatureVersion' => 'v4', 'targetPrefix' => 'CodeDeploy_20141006', 'uid' => 'codedeploy-2014-10-06', ], 'operations' => [ 'AddTagsToOnPremisesInstances' => [ 'name' => 'AddTagsToOnPremisesInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToOnPremisesInstancesInput', ], 'errors' => [ [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'TagRequiredException', ], [ 'shape' => 'InvalidTagException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'InstanceLimitExceededException', ], [ 'shape' => 'InstanceNotRegisteredException', ], ], ], 'BatchGetApplicationRevisions' => [ 'name' => 'BatchGetApplicationRevisions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetApplicationRevisionsInput', ], 'output' => [ 'shape' => 'BatchGetApplicationRevisionsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'RevisionRequiredException', ], [ 'shape' => 'InvalidRevisionException', ], [ 'shape' => 'BatchLimitExceededException', ], ], ], 'BatchGetApplications' => [ 'name' => 'BatchGetApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetApplicationsInput', ], 'output' => [ 'shape' => 'BatchGetApplicationsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'BatchLimitExceededException', ], ], ], 'BatchGetDeploymentGroups' => [ 'name' => 'BatchGetDeploymentGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDeploymentGroupsInput', ], 'output' => [ 'shape' => 'BatchGetDeploymentGroupsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'BatchLimitExceededException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], ], ], 'BatchGetDeploymentInstances' => [ 'name' => 'BatchGetDeploymentInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDeploymentInstancesInput', ], 'output' => [ 'shape' => 'BatchGetDeploymentInstancesOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'InstanceIdRequiredException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'BatchLimitExceededException', ], [ 'shape' => 'InvalidComputePlatformException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use BatchGetDeploymentTargets instead.', ], 'BatchGetDeploymentTargets' => [ 'name' => 'BatchGetDeploymentTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDeploymentTargetsInput', ], 'output' => [ 'shape' => 'BatchGetDeploymentTargetsOutput', ], 'errors' => [ [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentNotStartedException', ], [ 'shape' => 'DeploymentTargetIdRequiredException', ], [ 'shape' => 'InvalidDeploymentTargetIdException', ], [ 'shape' => 'DeploymentTargetDoesNotExistException', ], [ 'shape' => 'DeploymentTargetListSizeExceededException', ], [ 'shape' => 'InstanceDoesNotExistException', ], ], ], 'BatchGetDeployments' => [ 'name' => 'BatchGetDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDeploymentsInput', ], 'output' => [ 'shape' => 'BatchGetDeploymentsOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'BatchLimitExceededException', ], ], ], 'BatchGetOnPremisesInstances' => [ 'name' => 'BatchGetOnPremisesInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetOnPremisesInstancesInput', ], 'output' => [ 'shape' => 'BatchGetOnPremisesInstancesOutput', ], 'errors' => [ [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'BatchLimitExceededException', ], ], ], 'ContinueDeployment' => [ 'name' => 'ContinueDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ContinueDeploymentInput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentAlreadyCompletedException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'DeploymentIsNotInReadyStateException', ], [ 'shape' => 'UnsupportedActionForDeploymentTypeException', ], [ 'shape' => 'InvalidDeploymentWaitTypeException', ], [ 'shape' => 'InvalidDeploymentStatusException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApplicationInput', ], 'output' => [ 'shape' => 'CreateApplicationOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationAlreadyExistsException', ], [ 'shape' => 'ApplicationLimitExceededException', ], [ 'shape' => 'InvalidComputePlatformException', ], [ 'shape' => 'InvalidTagsToAddException', ], ], ], 'CreateDeployment' => [ 'name' => 'CreateDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeploymentInput', ], 'output' => [ 'shape' => 'CreateDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'RevisionRequiredException', ], [ 'shape' => 'RevisionDoesNotExistException', ], [ 'shape' => 'InvalidRevisionException', ], [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'DescriptionTooLongException', ], [ 'shape' => 'DeploymentLimitExceededException', ], [ 'shape' => 'InvalidTargetInstancesException', ], [ 'shape' => 'InvalidAlarmConfigException', ], [ 'shape' => 'AlarmsLimitExceededException', ], [ 'shape' => 'InvalidAutoRollbackConfigException', ], [ 'shape' => 'InvalidLoadBalancerInfoException', ], [ 'shape' => 'InvalidFileExistsBehaviorException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'InvalidAutoScalingGroupException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidUpdateOutdatedInstancesOnlyValueException', ], [ 'shape' => 'InvalidIgnoreApplicationStopFailuresValueException', ], [ 'shape' => 'InvalidGitHubAccountTokenException', ], [ 'shape' => 'InvalidTrafficRoutingConfigurationException', ], ], ], 'CreateDeploymentConfig' => [ 'name' => 'CreateDeploymentConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeploymentConfigInput', ], 'output' => [ 'shape' => 'CreateDeploymentConfigOutput', ], 'errors' => [ [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigNameRequiredException', ], [ 'shape' => 'DeploymentConfigAlreadyExistsException', ], [ 'shape' => 'InvalidMinimumHealthyHostValueException', ], [ 'shape' => 'DeploymentConfigLimitExceededException', ], [ 'shape' => 'InvalidComputePlatformException', ], [ 'shape' => 'InvalidTrafficRoutingConfigurationException', ], [ 'shape' => 'InvalidZonalDeploymentConfigurationException', ], ], ], 'CreateDeploymentGroup' => [ 'name' => 'CreateDeploymentGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeploymentGroupInput', ], 'output' => [ 'shape' => 'CreateDeploymentGroupOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'DeploymentGroupAlreadyExistsException', ], [ 'shape' => 'InvalidEC2TagException', ], [ 'shape' => 'InvalidTagException', ], [ 'shape' => 'InvalidAutoScalingGroupException', ], [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'RoleRequiredException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'DeploymentGroupLimitExceededException', ], [ 'shape' => 'LifecycleHookLimitExceededException', ], [ 'shape' => 'InvalidTriggerConfigException', ], [ 'shape' => 'TriggerTargetsLimitExceededException', ], [ 'shape' => 'InvalidAlarmConfigException', ], [ 'shape' => 'AlarmsLimitExceededException', ], [ 'shape' => 'InvalidAutoRollbackConfigException', ], [ 'shape' => 'InvalidLoadBalancerInfoException', ], [ 'shape' => 'InvalidDeploymentStyleException', ], [ 'shape' => 'InvalidBlueGreenDeploymentConfigurationException', ], [ 'shape' => 'InvalidEC2TagCombinationException', ], [ 'shape' => 'InvalidOnPremisesTagCombinationException', ], [ 'shape' => 'TagSetListLimitExceededException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidECSServiceException', ], [ 'shape' => 'InvalidTargetGroupPairException', ], [ 'shape' => 'ECSServiceMappingLimitExceededException', ], [ 'shape' => 'InvalidTagsToAddException', ], [ 'shape' => 'InvalidTrafficRoutingConfigurationException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationInput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'InvalidRoleException', ], ], ], 'DeleteDeploymentConfig' => [ 'name' => 'DeleteDeploymentConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeploymentConfigInput', ], 'errors' => [ [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigNameRequiredException', ], [ 'shape' => 'DeploymentConfigInUseException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DeleteDeploymentGroup' => [ 'name' => 'DeleteDeploymentGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeploymentGroupInput', ], 'output' => [ 'shape' => 'DeleteDeploymentGroupOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'InvalidRoleException', ], ], ], 'DeleteGitHubAccountToken' => [ 'name' => 'DeleteGitHubAccountToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGitHubAccountTokenInput', ], 'output' => [ 'shape' => 'DeleteGitHubAccountTokenOutput', ], 'errors' => [ [ 'shape' => 'GitHubAccountTokenNameRequiredException', ], [ 'shape' => 'GitHubAccountTokenDoesNotExistException', ], [ 'shape' => 'InvalidGitHubAccountTokenNameException', ], [ 'shape' => 'ResourceValidationException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DeleteResourcesByExternalId' => [ 'name' => 'DeleteResourcesByExternalId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcesByExternalIdInput', ], 'output' => [ 'shape' => 'DeleteResourcesByExternalIdOutput', ], 'errors' => [], ], 'DeregisterOnPremisesInstance' => [ 'name' => 'DeregisterOnPremisesInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterOnPremisesInstanceInput', ], 'errors' => [ [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'InvalidInstanceNameException', ], ], ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationInput', ], 'output' => [ 'shape' => 'GetApplicationOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], ], ], 'GetApplicationRevision' => [ 'name' => 'GetApplicationRevision', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationRevisionInput', ], 'output' => [ 'shape' => 'GetApplicationRevisionOutput', ], 'errors' => [ [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'RevisionDoesNotExistException', ], [ 'shape' => 'RevisionRequiredException', ], [ 'shape' => 'InvalidRevisionException', ], ], ], 'GetDeployment' => [ 'name' => 'GetDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentInput', ], 'output' => [ 'shape' => 'GetDeploymentOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], ], ], 'GetDeploymentConfig' => [ 'name' => 'GetDeploymentConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentConfigInput', ], 'output' => [ 'shape' => 'GetDeploymentConfigOutput', ], 'errors' => [ [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigNameRequiredException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'InvalidComputePlatformException', ], ], ], 'GetDeploymentGroup' => [ 'name' => 'GetDeploymentGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentGroupInput', ], 'output' => [ 'shape' => 'GetDeploymentGroupOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], ], ], 'GetDeploymentInstance' => [ 'name' => 'GetDeploymentInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentInstanceInput', ], 'output' => [ 'shape' => 'GetDeploymentInstanceOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'InstanceIdRequiredException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'InstanceDoesNotExistException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'InvalidComputePlatformException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use GetDeploymentTarget instead.', ], 'GetDeploymentTarget' => [ 'name' => 'GetDeploymentTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentTargetInput', ], 'output' => [ 'shape' => 'GetDeploymentTargetOutput', ], 'errors' => [ [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentNotStartedException', ], [ 'shape' => 'DeploymentTargetIdRequiredException', ], [ 'shape' => 'InvalidDeploymentTargetIdException', ], [ 'shape' => 'DeploymentTargetDoesNotExistException', ], [ 'shape' => 'InvalidInstanceNameException', ], ], ], 'GetOnPremisesInstance' => [ 'name' => 'GetOnPremisesInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOnPremisesInstanceInput', ], 'output' => [ 'shape' => 'GetOnPremisesInstanceOutput', ], 'errors' => [ [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'InstanceNotRegisteredException', ], [ 'shape' => 'InvalidInstanceNameException', ], ], ], 'ListApplicationRevisions' => [ 'name' => 'ListApplicationRevisions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationRevisionsInput', ], 'output' => [ 'shape' => 'ListApplicationRevisionsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'InvalidSortByException', ], [ 'shape' => 'InvalidSortOrderException', ], [ 'shape' => 'InvalidBucketNameFilterException', ], [ 'shape' => 'InvalidKeyPrefixFilterException', ], [ 'shape' => 'BucketNameFilterRequiredException', ], [ 'shape' => 'InvalidDeployedStateFilterException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationsInput', ], 'output' => [ 'shape' => 'ListApplicationsOutput', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListDeploymentConfigs' => [ 'name' => 'ListDeploymentConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentConfigsInput', ], 'output' => [ 'shape' => 'ListDeploymentConfigsOutput', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListDeploymentGroups' => [ 'name' => 'ListDeploymentGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentGroupsInput', ], 'output' => [ 'shape' => 'ListDeploymentGroupsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListDeploymentInstances' => [ 'name' => 'ListDeploymentInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentInstancesInput', ], 'output' => [ 'shape' => 'ListDeploymentInstancesOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentNotStartedException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'InvalidInstanceStatusException', ], [ 'shape' => 'InvalidInstanceTypeException', ], [ 'shape' => 'InvalidDeploymentInstanceTypeException', ], [ 'shape' => 'InvalidTargetFilterNameException', ], [ 'shape' => 'InvalidComputePlatformException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use ListDeploymentTargets instead.', ], 'ListDeploymentTargets' => [ 'name' => 'ListDeploymentTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentTargetsInput', ], 'output' => [ 'shape' => 'ListDeploymentTargetsOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentNotStartedException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'InvalidInstanceStatusException', ], [ 'shape' => 'InvalidInstanceTypeException', ], [ 'shape' => 'InvalidDeploymentInstanceTypeException', ], [ 'shape' => 'InvalidTargetFilterNameException', ], ], ], 'ListDeployments' => [ 'name' => 'ListDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentsInput', ], 'output' => [ 'shape' => 'ListDeploymentsOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'InvalidTimeRangeException', ], [ 'shape' => 'InvalidDeploymentStatusException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidExternalIdException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListGitHubAccountTokenNames' => [ 'name' => 'ListGitHubAccountTokenNames', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGitHubAccountTokenNamesInput', ], 'output' => [ 'shape' => 'ListGitHubAccountTokenNamesOutput', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceValidationException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ListOnPremisesInstances' => [ 'name' => 'ListOnPremisesInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOnPremisesInstancesInput', ], 'output' => [ 'shape' => 'ListOnPremisesInstancesOutput', ], 'errors' => [ [ 'shape' => 'InvalidRegistrationStatusException', ], [ 'shape' => 'InvalidTagFilterException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ArnNotSupportedException', ], [ 'shape' => 'InvalidArnException', ], [ 'shape' => 'ResourceArnRequiredException', ], ], ], 'PutLifecycleEventHookExecutionStatus' => [ 'name' => 'PutLifecycleEventHookExecutionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLifecycleEventHookExecutionStatusInput', ], 'output' => [ 'shape' => 'PutLifecycleEventHookExecutionStatusOutput', ], 'errors' => [ [ 'shape' => 'InvalidLifecycleEventHookExecutionStatusException', ], [ 'shape' => 'InvalidLifecycleEventHookExecutionIdException', ], [ 'shape' => 'LifecycleEventAlreadyCompletedException', ], [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'UnsupportedActionForDeploymentTypeException', ], ], ], 'RegisterApplicationRevision' => [ 'name' => 'RegisterApplicationRevision', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterApplicationRevisionInput', ], 'errors' => [ [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'DescriptionTooLongException', ], [ 'shape' => 'RevisionRequiredException', ], [ 'shape' => 'InvalidRevisionException', ], ], ], 'RegisterOnPremisesInstance' => [ 'name' => 'RegisterOnPremisesInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterOnPremisesInstanceInput', ], 'errors' => [ [ 'shape' => 'InstanceNameAlreadyRegisteredException', ], [ 'shape' => 'IamArnRequiredException', ], [ 'shape' => 'IamSessionArnAlreadyRegisteredException', ], [ 'shape' => 'IamUserArnAlreadyRegisteredException', ], [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'IamUserArnRequiredException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'InvalidIamSessionArnException', ], [ 'shape' => 'InvalidIamUserArnException', ], [ 'shape' => 'MultipleIamArnsProvidedException', ], ], ], 'RemoveTagsFromOnPremisesInstances' => [ 'name' => 'RemoveTagsFromOnPremisesInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromOnPremisesInstancesInput', ], 'errors' => [ [ 'shape' => 'InstanceNameRequiredException', ], [ 'shape' => 'InvalidInstanceNameException', ], [ 'shape' => 'TagRequiredException', ], [ 'shape' => 'InvalidTagException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'InstanceLimitExceededException', ], [ 'shape' => 'InstanceNotRegisteredException', ], ], ], 'SkipWaitTimeForInstanceTermination' => [ 'name' => 'SkipWaitTimeForInstanceTermination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SkipWaitTimeForInstanceTerminationInput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentAlreadyCompletedException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'DeploymentNotStartedException', ], [ 'shape' => 'UnsupportedActionForDeploymentTypeException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use ContinueDeployment with DeploymentWaitType instead.', ], 'StopDeployment' => [ 'name' => 'StopDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDeploymentInput', ], 'output' => [ 'shape' => 'StopDeploymentOutput', ], 'errors' => [ [ 'shape' => 'DeploymentIdRequiredException', ], [ 'shape' => 'DeploymentDoesNotExistException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'DeploymentAlreadyCompletedException', ], [ 'shape' => 'InvalidDeploymentIdException', ], [ 'shape' => 'UnsupportedActionForDeploymentTypeException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceArnRequiredException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'TagRequiredException', ], [ 'shape' => 'InvalidTagsToAddException', ], [ 'shape' => 'ArnNotSupportedException', ], [ 'shape' => 'InvalidArnException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceArnRequiredException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'TagRequiredException', ], [ 'shape' => 'InvalidTagsToAddException', ], [ 'shape' => 'ArnNotSupportedException', ], [ 'shape' => 'InvalidArnException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApplicationInput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationAlreadyExistsException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], ], ], 'UpdateDeploymentGroup' => [ 'name' => 'UpdateDeploymentGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeploymentGroupInput', ], 'output' => [ 'shape' => 'UpdateDeploymentGroupOutput', ], 'errors' => [ [ 'shape' => 'ApplicationNameRequiredException', ], [ 'shape' => 'InvalidApplicationNameException', ], [ 'shape' => 'ApplicationDoesNotExistException', ], [ 'shape' => 'InvalidDeploymentGroupNameException', ], [ 'shape' => 'DeploymentGroupAlreadyExistsException', ], [ 'shape' => 'DeploymentGroupNameRequiredException', ], [ 'shape' => 'DeploymentGroupDoesNotExistException', ], [ 'shape' => 'InvalidEC2TagException', ], [ 'shape' => 'InvalidTagException', ], [ 'shape' => 'InvalidAutoScalingGroupException', ], [ 'shape' => 'InvalidDeploymentConfigNameException', ], [ 'shape' => 'DeploymentConfigDoesNotExistException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'LifecycleHookLimitExceededException', ], [ 'shape' => 'InvalidTriggerConfigException', ], [ 'shape' => 'TriggerTargetsLimitExceededException', ], [ 'shape' => 'InvalidAlarmConfigException', ], [ 'shape' => 'AlarmsLimitExceededException', ], [ 'shape' => 'InvalidAutoRollbackConfigException', ], [ 'shape' => 'InvalidLoadBalancerInfoException', ], [ 'shape' => 'InvalidDeploymentStyleException', ], [ 'shape' => 'InvalidBlueGreenDeploymentConfigurationException', ], [ 'shape' => 'InvalidEC2TagCombinationException', ], [ 'shape' => 'InvalidOnPremisesTagCombinationException', ], [ 'shape' => 'TagSetListLimitExceededException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidECSServiceException', ], [ 'shape' => 'InvalidTargetGroupPairException', ], [ 'shape' => 'ECSServiceMappingLimitExceededException', ], [ 'shape' => 'InvalidTrafficRoutingConfigurationException', ], ], ], ], 'shapes' => [ 'AddTagsToOnPremisesInstancesInput' => [ 'type' => 'structure', 'required' => [ 'tags', 'instanceNames', ], 'members' => [ 'tags' => [ 'shape' => 'TagList', ], 'instanceNames' => [ 'shape' => 'InstanceNameList', ], ], ], 'AdditionalDeploymentStatusInfo' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'AdditionalDeploymentStatusInfo is deprecated, use DeploymentStatusMessageList instead.', ], 'Alarm' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AlarmName', ], ], ], 'AlarmConfiguration' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'ignorePollAlarmFailure' => [ 'shape' => 'Boolean', ], 'alarms' => [ 'shape' => 'AlarmList', ], ], ], 'AlarmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alarm', ], ], 'AlarmName' => [ 'type' => 'string', ], 'AlarmsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AppSpecContent' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'RawStringContent', ], 'sha256' => [ 'shape' => 'RawStringSha256', ], ], ], 'ApplicationAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApplicationDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApplicationId' => [ 'type' => 'string', ], 'ApplicationInfo' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'applicationName' => [ 'shape' => 'ApplicationName', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'linkedToGitHub' => [ 'shape' => 'Boolean', ], 'gitHubAccountName' => [ 'shape' => 'GitHubAccountTokenName', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], ], ], 'ApplicationLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApplicationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ApplicationNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApplicationRevisionSortBy' => [ 'type' => 'string', 'enum' => [ 'registerTime', 'firstUsedTime', 'lastUsedTime', ], ], 'ApplicationsInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationInfo', ], ], 'ApplicationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationName', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'ArnNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AutoRollbackConfiguration' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'events' => [ 'shape' => 'AutoRollbackEventsList', ], ], ], 'AutoRollbackEvent' => [ 'type' => 'string', 'enum' => [ 'DEPLOYMENT_FAILURE', 'DEPLOYMENT_STOP_ON_ALARM', 'DEPLOYMENT_STOP_ON_REQUEST', ], ], 'AutoRollbackEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoRollbackEvent', ], ], 'AutoScalingGroup' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AutoScalingGroupName', ], 'hook' => [ 'shape' => 'AutoScalingGroupHook', ], 'terminationHook' => [ 'shape' => 'AutoScalingGroupHook', ], ], ], 'AutoScalingGroupHook' => [ 'type' => 'string', ], 'AutoScalingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroup', ], ], 'AutoScalingGroupName' => [ 'type' => 'string', ], 'AutoScalingGroupNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroupName', ], ], 'BatchGetApplicationRevisionsInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'revisions', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'revisions' => [ 'shape' => 'RevisionLocationList', ], ], ], 'BatchGetApplicationRevisionsOutput' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'revisions' => [ 'shape' => 'RevisionInfoList', ], ], ], 'BatchGetApplicationsInput' => [ 'type' => 'structure', 'required' => [ 'applicationNames', ], 'members' => [ 'applicationNames' => [ 'shape' => 'ApplicationsList', ], ], ], 'BatchGetApplicationsOutput' => [ 'type' => 'structure', 'members' => [ 'applicationsInfo' => [ 'shape' => 'ApplicationsInfoList', ], ], ], 'BatchGetDeploymentGroupsInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'deploymentGroupNames', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupNames' => [ 'shape' => 'DeploymentGroupsList', ], ], ], 'BatchGetDeploymentGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentGroupsInfo' => [ 'shape' => 'DeploymentGroupInfoList', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchGetDeploymentInstancesInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', 'instanceIds', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'instanceIds' => [ 'shape' => 'InstancesList', ], ], ], 'BatchGetDeploymentInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'instancesSummary' => [ 'shape' => 'InstanceSummaryList', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchGetDeploymentTargetsInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', 'targetIds', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetIds' => [ 'shape' => 'TargetIdList', ], ], ], 'BatchGetDeploymentTargetsOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentTargets' => [ 'shape' => 'DeploymentTargetList', ], ], ], 'BatchGetDeploymentsInput' => [ 'type' => 'structure', 'required' => [ 'deploymentIds', ], 'members' => [ 'deploymentIds' => [ 'shape' => 'DeploymentsList', ], ], ], 'BatchGetDeploymentsOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentsInfo' => [ 'shape' => 'DeploymentsInfoList', ], ], ], 'BatchGetOnPremisesInstancesInput' => [ 'type' => 'structure', 'required' => [ 'instanceNames', ], 'members' => [ 'instanceNames' => [ 'shape' => 'InstanceNameList', ], ], ], 'BatchGetOnPremisesInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'instanceInfos' => [ 'shape' => 'InstanceInfoList', ], ], ], 'BatchLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlueGreenDeploymentConfiguration' => [ 'type' => 'structure', 'members' => [ 'terminateBlueInstancesOnDeploymentSuccess' => [ 'shape' => 'BlueInstanceTerminationOption', ], 'deploymentReadyOption' => [ 'shape' => 'DeploymentReadyOption', ], 'greenFleetProvisioningOption' => [ 'shape' => 'GreenFleetProvisioningOption', ], ], ], 'BlueInstanceTerminationOption' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'InstanceAction', ], 'terminationWaitTimeInMinutes' => [ 'shape' => 'Duration', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BucketNameFilterRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BundleType' => [ 'type' => 'string', 'enum' => [ 'tar', 'tgz', 'zip', 'YAML', 'JSON', ], ], 'CloudFormationResourceType' => [ 'type' => 'string', ], 'CloudFormationTarget' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetId' => [ 'shape' => 'TargetId', ], 'lastUpdatedAt' => [ 'shape' => 'Time', ], 'lifecycleEvents' => [ 'shape' => 'LifecycleEventList', ], 'status' => [ 'shape' => 'TargetStatus', ], 'resourceType' => [ 'shape' => 'CloudFormationResourceType', ], 'targetVersionWeight' => [ 'shape' => 'TrafficWeight', ], ], ], 'CommitId' => [ 'type' => 'string', ], 'ComputePlatform' => [ 'type' => 'string', 'enum' => [ 'Server', 'Lambda', 'ECS', ], ], 'ContinueDeploymentInput' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'deploymentWaitType' => [ 'shape' => 'DeploymentWaitType', ], ], ], 'CreateApplicationInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateApplicationOutput' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], ], ], 'CreateDeploymentConfigInput' => [ 'type' => 'structure', 'required' => [ 'deploymentConfigName', ], 'members' => [ 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'minimumHealthyHosts' => [ 'shape' => 'MinimumHealthyHosts', ], 'trafficRoutingConfig' => [ 'shape' => 'TrafficRoutingConfig', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'zonalConfig' => [ 'shape' => 'ZonalConfig', ], ], ], 'CreateDeploymentConfigOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentConfigId' => [ 'shape' => 'DeploymentConfigId', ], ], ], 'CreateDeploymentGroupInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'deploymentGroupName', 'serviceRoleArn', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'ec2TagFilters' => [ 'shape' => 'EC2TagFilterList', ], 'onPremisesInstanceTagFilters' => [ 'shape' => 'TagFilterList', ], 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupNameList', ], 'serviceRoleArn' => [ 'shape' => 'Role', ], 'triggerConfigurations' => [ 'shape' => 'TriggerConfigList', ], 'alarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'autoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfiguration', ], 'outdatedInstancesStrategy' => [ 'shape' => 'OutdatedInstancesStrategy', ], 'deploymentStyle' => [ 'shape' => 'DeploymentStyle', ], 'blueGreenDeploymentConfiguration' => [ 'shape' => 'BlueGreenDeploymentConfiguration', ], 'loadBalancerInfo' => [ 'shape' => 'LoadBalancerInfo', ], 'ec2TagSet' => [ 'shape' => 'EC2TagSet', ], 'ecsServices' => [ 'shape' => 'ECSServiceList', ], 'onPremisesTagSet' => [ 'shape' => 'OnPremisesTagSet', ], 'tags' => [ 'shape' => 'TagList', ], 'terminationHookEnabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'CreateDeploymentGroupOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentGroupId' => [ 'shape' => 'DeploymentGroupId', ], ], ], 'CreateDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'revision' => [ 'shape' => 'RevisionLocation', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'description' => [ 'shape' => 'Description', ], 'ignoreApplicationStopFailures' => [ 'shape' => 'Boolean', ], 'targetInstances' => [ 'shape' => 'TargetInstances', ], 'autoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfiguration', ], 'updateOutdatedInstancesOnly' => [ 'shape' => 'Boolean', ], 'fileExistsBehavior' => [ 'shape' => 'FileExistsBehavior', ], 'overrideAlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'CreateDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], ], ], 'DeleteApplicationInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], ], ], 'DeleteDeploymentConfigInput' => [ 'type' => 'structure', 'required' => [ 'deploymentConfigName', ], 'members' => [ 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], ], ], 'DeleteDeploymentGroupInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'deploymentGroupName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], ], ], 'DeleteDeploymentGroupOutput' => [ 'type' => 'structure', 'members' => [ 'hooksNotCleanedUp' => [ 'shape' => 'AutoScalingGroupList', ], ], ], 'DeleteGitHubAccountTokenInput' => [ 'type' => 'structure', 'members' => [ 'tokenName' => [ 'shape' => 'GitHubAccountTokenName', ], ], ], 'DeleteGitHubAccountTokenOutput' => [ 'type' => 'structure', 'members' => [ 'tokenName' => [ 'shape' => 'GitHubAccountTokenName', ], ], ], 'DeleteResourcesByExternalIdInput' => [ 'type' => 'structure', 'members' => [ 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'DeleteResourcesByExternalIdOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeploymentAlreadyCompletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentAlreadyStartedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigId' => [ 'type' => 'string', ], 'DeploymentConfigInUseException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigInfo' => [ 'type' => 'structure', 'members' => [ 'deploymentConfigId' => [ 'shape' => 'DeploymentConfigId', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'minimumHealthyHosts' => [ 'shape' => 'MinimumHealthyHosts', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'trafficRoutingConfig' => [ 'shape' => 'TrafficRoutingConfig', ], 'zonalConfig' => [ 'shape' => 'ZonalConfig', ], ], ], 'DeploymentConfigLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'DeploymentConfigNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentConfigsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentConfigName', ], ], 'DeploymentCreator' => [ 'type' => 'string', 'enum' => [ 'user', 'autoscaling', 'codeDeployRollback', 'CodeDeploy', 'CodeDeployAutoUpdate', 'CloudFormation', 'CloudFormationRollback', 'autoscalingTermination', ], ], 'DeploymentDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentGroupAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentGroupDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentGroupId' => [ 'type' => 'string', ], 'DeploymentGroupInfo' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupId' => [ 'shape' => 'DeploymentGroupId', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'ec2TagFilters' => [ 'shape' => 'EC2TagFilterList', ], 'onPremisesInstanceTagFilters' => [ 'shape' => 'TagFilterList', ], 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupList', ], 'serviceRoleArn' => [ 'shape' => 'Role', ], 'targetRevision' => [ 'shape' => 'RevisionLocation', ], 'triggerConfigurations' => [ 'shape' => 'TriggerConfigList', ], 'alarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'autoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfiguration', ], 'deploymentStyle' => [ 'shape' => 'DeploymentStyle', ], 'outdatedInstancesStrategy' => [ 'shape' => 'OutdatedInstancesStrategy', ], 'blueGreenDeploymentConfiguration' => [ 'shape' => 'BlueGreenDeploymentConfiguration', ], 'loadBalancerInfo' => [ 'shape' => 'LoadBalancerInfo', ], 'lastSuccessfulDeployment' => [ 'shape' => 'LastDeploymentInfo', ], 'lastAttemptedDeployment' => [ 'shape' => 'LastDeploymentInfo', ], 'ec2TagSet' => [ 'shape' => 'EC2TagSet', ], 'onPremisesTagSet' => [ 'shape' => 'OnPremisesTagSet', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'ecsServices' => [ 'shape' => 'ECSServiceList', ], 'terminationHookEnabled' => [ 'shape' => 'Boolean', ], ], ], 'DeploymentGroupInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentGroupInfo', ], ], 'DeploymentGroupLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentGroupName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'DeploymentGroupNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentGroupName', ], ], 'DeploymentId' => [ 'type' => 'string', ], 'DeploymentIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentInfo' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'previousRevision' => [ 'shape' => 'RevisionLocation', ], 'revision' => [ 'shape' => 'RevisionLocation', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'errorInformation' => [ 'shape' => 'ErrorInformation', ], 'createTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'completeTime' => [ 'shape' => 'Timestamp', ], 'deploymentOverview' => [ 'shape' => 'DeploymentOverview', ], 'description' => [ 'shape' => 'Description', ], 'creator' => [ 'shape' => 'DeploymentCreator', ], 'ignoreApplicationStopFailures' => [ 'shape' => 'Boolean', ], 'autoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfiguration', ], 'updateOutdatedInstancesOnly' => [ 'shape' => 'Boolean', ], 'rollbackInfo' => [ 'shape' => 'RollbackInfo', ], 'deploymentStyle' => [ 'shape' => 'DeploymentStyle', ], 'targetInstances' => [ 'shape' => 'TargetInstances', ], 'instanceTerminationWaitTimeStarted' => [ 'shape' => 'Boolean', ], 'blueGreenDeploymentConfiguration' => [ 'shape' => 'BlueGreenDeploymentConfiguration', ], 'loadBalancerInfo' => [ 'shape' => 'LoadBalancerInfo', ], 'additionalDeploymentStatusInfo' => [ 'shape' => 'AdditionalDeploymentStatusInfo', ], 'fileExistsBehavior' => [ 'shape' => 'FileExistsBehavior', ], 'deploymentStatusMessages' => [ 'shape' => 'DeploymentStatusMessageList', ], 'computePlatform' => [ 'shape' => 'ComputePlatform', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'relatedDeployments' => [ 'shape' => 'RelatedDeployments', ], 'overrideAlarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], ], ], 'DeploymentIsNotInReadyStateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentNotStartedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentOption' => [ 'type' => 'string', 'enum' => [ 'WITH_TRAFFIC_CONTROL', 'WITHOUT_TRAFFIC_CONTROL', ], ], 'DeploymentOverview' => [ 'type' => 'structure', 'members' => [ 'Pending' => [ 'shape' => 'InstanceCount', ], 'InProgress' => [ 'shape' => 'InstanceCount', ], 'Succeeded' => [ 'shape' => 'InstanceCount', ], 'Failed' => [ 'shape' => 'InstanceCount', ], 'Skipped' => [ 'shape' => 'InstanceCount', ], 'Ready' => [ 'shape' => 'InstanceCount', ], ], ], 'DeploymentReadyAction' => [ 'type' => 'string', 'enum' => [ 'CONTINUE_DEPLOYMENT', 'STOP_DEPLOYMENT', ], ], 'DeploymentReadyOption' => [ 'type' => 'structure', 'members' => [ 'actionOnTimeout' => [ 'shape' => 'DeploymentReadyAction', ], 'waitTimeInMinutes' => [ 'shape' => 'Duration', ], ], ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'Created', 'Queued', 'InProgress', 'Baking', 'Succeeded', 'Failed', 'Stopped', 'Ready', ], ], 'DeploymentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentStatus', ], ], 'DeploymentStatusMessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorMessage', ], ], 'DeploymentStyle' => [ 'type' => 'structure', 'members' => [ 'deploymentType' => [ 'shape' => 'DeploymentType', ], 'deploymentOption' => [ 'shape' => 'DeploymentOption', ], ], ], 'DeploymentTarget' => [ 'type' => 'structure', 'members' => [ 'deploymentTargetType' => [ 'shape' => 'DeploymentTargetType', ], 'instanceTarget' => [ 'shape' => 'InstanceTarget', ], 'lambdaTarget' => [ 'shape' => 'LambdaTarget', ], 'ecsTarget' => [ 'shape' => 'ECSTarget', ], 'cloudFormationTarget' => [ 'shape' => 'CloudFormationTarget', ], ], ], 'DeploymentTargetDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentTargetIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentTarget', ], ], 'DeploymentTargetListSizeExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeploymentTargetType' => [ 'type' => 'string', 'enum' => [ 'InstanceTarget', 'LambdaTarget', 'ECSTarget', 'CloudFormationTarget', ], ], 'DeploymentType' => [ 'type' => 'string', 'enum' => [ 'IN_PLACE', 'BLUE_GREEN', ], ], 'DeploymentWaitType' => [ 'type' => 'string', 'enum' => [ 'READY_WAIT', 'TERMINATION_WAIT', ], ], 'DeploymentsInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentInfo', ], ], 'DeploymentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentId', ], ], 'DeregisterOnPremisesInstanceInput' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'InstanceName', ], ], ], 'Description' => [ 'type' => 'string', ], 'DescriptionTooLongException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Diagnostics' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'LifecycleErrorCode', ], 'scriptName' => [ 'shape' => 'ScriptName', ], 'message' => [ 'shape' => 'LifecycleMessage', ], 'logTail' => [ 'shape' => 'LogTail', ], ], ], 'Duration' => [ 'type' => 'integer', ], 'EC2TagFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'Value' => [ 'shape' => 'Value', ], 'Type' => [ 'shape' => 'EC2TagFilterType', ], ], ], 'EC2TagFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2TagFilter', ], ], 'EC2TagFilterType' => [ 'type' => 'string', 'enum' => [ 'KEY_ONLY', 'VALUE_ONLY', 'KEY_AND_VALUE', ], ], 'EC2TagSet' => [ 'type' => 'structure', 'members' => [ 'ec2TagSetList' => [ 'shape' => 'EC2TagSetList', ], ], ], 'EC2TagSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2TagFilterList', ], ], 'ECSClusterName' => [ 'type' => 'string', ], 'ECSService' => [ 'type' => 'structure', 'members' => [ 'serviceName' => [ 'shape' => 'ECSServiceName', ], 'clusterName' => [ 'shape' => 'ECSClusterName', ], ], ], 'ECSServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSService', ], ], 'ECSServiceMappingLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ECSServiceName' => [ 'type' => 'string', ], 'ECSTarget' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetId' => [ 'shape' => 'TargetId', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'lastUpdatedAt' => [ 'shape' => 'Time', ], 'lifecycleEvents' => [ 'shape' => 'LifecycleEventList', ], 'status' => [ 'shape' => 'TargetStatus', ], 'taskSetsInfo' => [ 'shape' => 'ECSTaskSetList', ], ], ], 'ECSTaskSet' => [ 'type' => 'structure', 'members' => [ 'identifer' => [ 'shape' => 'ECSTaskSetIdentifier', ], 'desiredCount' => [ 'shape' => 'ECSTaskSetCount', ], 'pendingCount' => [ 'shape' => 'ECSTaskSetCount', ], 'runningCount' => [ 'shape' => 'ECSTaskSetCount', ], 'status' => [ 'shape' => 'ECSTaskSetStatus', ], 'trafficWeight' => [ 'shape' => 'TrafficWeight', ], 'targetGroup' => [ 'shape' => 'TargetGroupInfo', ], 'taskSetLabel' => [ 'shape' => 'TargetLabel', ], ], ], 'ECSTaskSetCount' => [ 'type' => 'long', ], 'ECSTaskSetIdentifier' => [ 'type' => 'string', ], 'ECSTaskSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSTaskSet', ], ], 'ECSTaskSetStatus' => [ 'type' => 'string', ], 'ELBInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ELBName', ], ], ], 'ELBInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ELBInfo', ], ], 'ELBName' => [ 'type' => 'string', ], 'ETag' => [ 'type' => 'string', ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'AGENT_ISSUE', 'ALARM_ACTIVE', 'APPLICATION_MISSING', 'AUTOSCALING_VALIDATION_ERROR', 'AUTO_SCALING_CONFIGURATION', 'AUTO_SCALING_IAM_ROLE_PERMISSIONS', 'CODEDEPLOY_RESOURCE_CANNOT_BE_FOUND', 'CUSTOMER_APPLICATION_UNHEALTHY', 'DEPLOYMENT_GROUP_MISSING', 'ECS_UPDATE_ERROR', 'ELASTIC_LOAD_BALANCING_INVALID', 'ELB_INVALID_INSTANCE', 'HEALTH_CONSTRAINTS', 'HEALTH_CONSTRAINTS_INVALID', 'HOOK_EXECUTION_FAILURE', 'IAM_ROLE_MISSING', 'IAM_ROLE_PERMISSIONS', 'INTERNAL_ERROR', 'INVALID_ECS_SERVICE', 'INVALID_LAMBDA_CONFIGURATION', 'INVALID_LAMBDA_FUNCTION', 'INVALID_REVISION', 'MANUAL_STOP', 'MISSING_BLUE_GREEN_DEPLOYMENT_CONFIGURATION', 'MISSING_ELB_INFORMATION', 'MISSING_GITHUB_TOKEN', 'NO_EC2_SUBSCRIPTION', 'NO_INSTANCES', 'OVER_MAX_INSTANCES', 'RESOURCE_LIMIT_EXCEEDED', 'REVISION_MISSING', 'THROTTLED', 'TIMEOUT', 'CLOUDFORMATION_STACK_FAILURE', ], ], 'ErrorInformation' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExternalId' => [ 'type' => 'string', ], 'FileExistsBehavior' => [ 'type' => 'string', 'enum' => [ 'DISALLOW', 'OVERWRITE', 'RETAIN', ], ], 'FilterValue' => [ 'type' => 'string', ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'GenericRevisionInfo' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'Description', ], 'deploymentGroups' => [ 'shape' => 'DeploymentGroupsList', ], 'firstUsedTime' => [ 'shape' => 'Timestamp', ], 'lastUsedTime' => [ 'shape' => 'Timestamp', ], 'registerTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetApplicationInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], ], ], 'GetApplicationOutput' => [ 'type' => 'structure', 'members' => [ 'application' => [ 'shape' => 'ApplicationInfo', ], ], ], 'GetApplicationRevisionInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'revision', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'revision' => [ 'shape' => 'RevisionLocation', ], ], ], 'GetApplicationRevisionOutput' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'revision' => [ 'shape' => 'RevisionLocation', ], 'revisionInfo' => [ 'shape' => 'GenericRevisionInfo', ], ], ], 'GetDeploymentConfigInput' => [ 'type' => 'structure', 'required' => [ 'deploymentConfigName', ], 'members' => [ 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], ], ], 'GetDeploymentConfigOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentConfigInfo' => [ 'shape' => 'DeploymentConfigInfo', ], ], ], 'GetDeploymentGroupInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'deploymentGroupName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], ], ], 'GetDeploymentGroupOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentGroupInfo' => [ 'shape' => 'DeploymentGroupInfo', ], ], ], 'GetDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], ], ], 'GetDeploymentInstanceInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', 'instanceId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'instanceId' => [ 'shape' => 'InstanceId', ], ], ], 'GetDeploymentInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'instanceSummary' => [ 'shape' => 'InstanceSummary', ], ], ], 'GetDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentInfo' => [ 'shape' => 'DeploymentInfo', ], ], ], 'GetDeploymentTargetInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', 'targetId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetId' => [ 'shape' => 'TargetId', ], ], ], 'GetDeploymentTargetOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentTarget' => [ 'shape' => 'DeploymentTarget', ], ], ], 'GetOnPremisesInstanceInput' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'InstanceName', ], ], ], 'GetOnPremisesInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'instanceInfo' => [ 'shape' => 'InstanceInfo', ], ], ], 'GitHubAccountTokenDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'GitHubAccountTokenName' => [ 'type' => 'string', ], 'GitHubAccountTokenNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GitHubAccountTokenName', ], ], 'GitHubAccountTokenNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'GitHubLocation' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'Repository', ], 'commitId' => [ 'shape' => 'CommitId', ], ], ], 'GreenFleetProvisioningAction' => [ 'type' => 'string', 'enum' => [ 'DISCOVER_EXISTING', 'COPY_AUTO_SCALING_GROUP', ], ], 'GreenFleetProvisioningOption' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GreenFleetProvisioningAction', ], ], ], 'IamArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IamSessionArn' => [ 'type' => 'string', ], 'IamSessionArnAlreadyRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IamUserArn' => [ 'type' => 'string', ], 'IamUserArnAlreadyRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IamUserArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InstanceAction' => [ 'type' => 'string', 'enum' => [ 'TERMINATE', 'KEEP_ALIVE', ], ], 'InstanceArn' => [ 'type' => 'string', ], 'InstanceCount' => [ 'type' => 'long', ], 'InstanceDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This exception is deprecated, use DeploymentTargetDoesNotExistException instead.', 'exception' => true, ], 'InstanceId' => [ 'type' => 'string', ], 'InstanceIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This exception is deprecated, use DeploymentTargetIdRequiredException instead.', 'exception' => true, ], 'InstanceInfo' => [ 'type' => 'structure', 'members' => [ 'instanceName' => [ 'shape' => 'InstanceName', ], 'iamSessionArn' => [ 'shape' => 'IamSessionArn', ], 'iamUserArn' => [ 'shape' => 'IamUserArn', ], 'instanceArn' => [ 'shape' => 'InstanceArn', ], 'registerTime' => [ 'shape' => 'Timestamp', ], 'deregisterTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'InstanceInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceInfo', ], ], 'InstanceLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InstanceName' => [ 'type' => 'string', ], 'InstanceNameAlreadyRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InstanceNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceName', ], ], 'InstanceNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InstanceNotRegisteredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InstanceStatus' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'InstanceStatus is deprecated, use TargetStatus instead.', 'enum' => [ 'Pending', 'InProgress', 'Succeeded', 'Failed', 'Skipped', 'Unknown', 'Ready', ], ], 'InstanceStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceStatus', ], ], 'InstanceSummary' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'instanceId' => [ 'shape' => 'InstanceId', ], 'status' => [ 'shape' => 'InstanceStatus', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'lifecycleEvents' => [ 'shape' => 'LifecycleEventList', ], 'instanceType' => [ 'shape' => 'InstanceType', ], ], 'deprecated' => true, 'deprecatedMessage' => 'InstanceSummary is deprecated, use DeploymentTarget instead.', ], 'InstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSummary', ], ], 'InstanceTarget' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetId' => [ 'shape' => 'TargetId', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'status' => [ 'shape' => 'TargetStatus', ], 'lastUpdatedAt' => [ 'shape' => 'Time', ], 'lifecycleEvents' => [ 'shape' => 'LifecycleEventList', ], 'instanceLabel' => [ 'shape' => 'TargetLabel', ], ], ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'Blue', 'Green', ], ], 'InstanceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceType', ], ], 'InstancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceId', ], ], 'InvalidAlarmConfigException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidApplicationNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidAutoRollbackConfigException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidAutoScalingGroupException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBlueGreenDeploymentConfigurationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBucketNameFilterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidComputePlatformException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeployedStateFilterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentConfigNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentGroupNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentInstanceTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentStyleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentTargetIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeploymentWaitTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEC2TagCombinationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEC2TagException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidECSServiceException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidExternalIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFileExistsBehaviorException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidGitHubAccountTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidGitHubAccountTokenNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidIamSessionArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidIamUserArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidIgnoreApplicationStopFailuresValueException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInstanceIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInstanceNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInstanceStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidInstanceTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidKeyPrefixFilterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidLifecycleEventHookExecutionIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidLifecycleEventHookExecutionStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidLoadBalancerInfoException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMinimumHealthyHostValueException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOnPremisesTagCombinationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRegistrationStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRevisionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRoleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSortByException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSortOrderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagFilterException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTagsToAddException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetFilterNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetGroupPairException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetInstancesException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTimeRangeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTrafficRoutingConfigurationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTriggerConfigException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidUpdateOutdatedInstancesOnlyValueException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidZonalDeploymentConfigurationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Key' => [ 'type' => 'string', ], 'LambdaFunctionAlias' => [ 'type' => 'string', ], 'LambdaFunctionInfo' => [ 'type' => 'structure', 'members' => [ 'functionName' => [ 'shape' => 'LambdaFunctionName', ], 'functionAlias' => [ 'shape' => 'LambdaFunctionAlias', ], 'currentVersion' => [ 'shape' => 'Version', ], 'targetVersion' => [ 'shape' => 'Version', ], 'targetVersionWeight' => [ 'shape' => 'TrafficWeight', ], ], ], 'LambdaFunctionName' => [ 'type' => 'string', ], 'LambdaTarget' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'targetId' => [ 'shape' => 'TargetId', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'status' => [ 'shape' => 'TargetStatus', ], 'lastUpdatedAt' => [ 'shape' => 'Time', ], 'lifecycleEvents' => [ 'shape' => 'LifecycleEventList', ], 'lambdaFunctionInfo' => [ 'shape' => 'LambdaFunctionInfo', ], ], ], 'LastDeploymentInfo' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'status' => [ 'shape' => 'DeploymentStatus', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'createTime' => [ 'shape' => 'Timestamp', ], ], ], 'LifecycleErrorCode' => [ 'type' => 'string', 'enum' => [ 'Success', 'ScriptMissing', 'ScriptNotExecutable', 'ScriptTimedOut', 'ScriptFailed', 'UnknownError', ], ], 'LifecycleEvent' => [ 'type' => 'structure', 'members' => [ 'lifecycleEventName' => [ 'shape' => 'LifecycleEventName', ], 'diagnostics' => [ 'shape' => 'Diagnostics', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'LifecycleEventStatus', ], ], ], 'LifecycleEventAlreadyCompletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'LifecycleEventHookExecutionId' => [ 'type' => 'string', ], 'LifecycleEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleEvent', ], ], 'LifecycleEventName' => [ 'type' => 'string', ], 'LifecycleEventStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Succeeded', 'Failed', 'Skipped', 'Unknown', ], ], 'LifecycleHookLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'LifecycleMessage' => [ 'type' => 'string', ], 'ListApplicationRevisionsInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'sortBy' => [ 'shape' => 'ApplicationRevisionSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3KeyPrefix' => [ 'shape' => 'S3Key', ], 'deployed' => [ 'shape' => 'ListStateFilterAction', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationRevisionsOutput' => [ 'type' => 'structure', 'members' => [ 'revisions' => [ 'shape' => 'RevisionLocationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationsOutput' => [ 'type' => 'structure', 'members' => [ 'applications' => [ 'shape' => 'ApplicationsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentConfigsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentConfigsOutput' => [ 'type' => 'structure', 'members' => [ 'deploymentConfigsList' => [ 'shape' => 'DeploymentConfigsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentGroupsInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroups' => [ 'shape' => 'DeploymentGroupsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentInstancesInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'instanceStatusFilter' => [ 'shape' => 'InstanceStatusList', ], 'instanceTypeFilter' => [ 'shape' => 'InstanceTypeList', ], ], ], 'ListDeploymentInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'instancesList' => [ 'shape' => 'InstancesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentTargetsInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'targetFilters' => [ 'shape' => 'TargetFilters', ], ], ], 'ListDeploymentTargetsOutput' => [ 'type' => 'structure', 'members' => [ 'targetIds' => [ 'shape' => 'TargetIdList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentsInput' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'deploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'includeOnlyStatuses' => [ 'shape' => 'DeploymentStatusList', ], 'createTimeRange' => [ 'shape' => 'TimeRange', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentsOutput' => [ 'type' => 'structure', 'members' => [ 'deployments' => [ 'shape' => 'DeploymentsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGitHubAccountTokenNamesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGitHubAccountTokenNamesOutput' => [ 'type' => 'structure', 'members' => [ 'tokenNameList' => [ 'shape' => 'GitHubAccountTokenNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOnPremisesInstancesInput' => [ 'type' => 'structure', 'members' => [ 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'tagFilters' => [ 'shape' => 'TagFilterList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOnPremisesInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'instanceNames' => [ 'shape' => 'InstanceNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStateFilterAction' => [ 'type' => 'string', 'enum' => [ 'include', 'exclude', 'ignore', ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListenerArn' => [ 'type' => 'string', ], 'ListenerArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListenerArn', ], ], 'LoadBalancerInfo' => [ 'type' => 'structure', 'members' => [ 'elbInfoList' => [ 'shape' => 'ELBInfoList', ], 'targetGroupInfoList' => [ 'shape' => 'TargetGroupInfoList', ], 'targetGroupPairInfoList' => [ 'shape' => 'TargetGroupPairInfoList', ], ], ], 'LogTail' => [ 'type' => 'string', ], 'Message' => [ 'type' => 'string', ], 'MinimumHealthyHosts' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'MinimumHealthyHostsType', ], 'value' => [ 'shape' => 'MinimumHealthyHostsValue', ], ], ], 'MinimumHealthyHostsPerZone' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'MinimumHealthyHostsPerZoneType', ], 'value' => [ 'shape' => 'MinimumHealthyHostsPerZoneValue', ], ], ], 'MinimumHealthyHostsPerZoneType' => [ 'type' => 'string', 'enum' => [ 'HOST_COUNT', 'FLEET_PERCENT', ], ], 'MinimumHealthyHostsPerZoneValue' => [ 'type' => 'integer', ], 'MinimumHealthyHostsType' => [ 'type' => 'string', 'enum' => [ 'HOST_COUNT', 'FLEET_PERCENT', ], ], 'MinimumHealthyHostsValue' => [ 'type' => 'integer', ], 'MultipleIamArnsProvidedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'OnPremisesTagSet' => [ 'type' => 'structure', 'members' => [ 'onPremisesTagSetList' => [ 'shape' => 'OnPremisesTagSetList', ], ], ], 'OnPremisesTagSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagFilterList', ], ], 'OperationNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OutdatedInstancesStrategy' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'IGNORE', ], ], 'Percentage' => [ 'type' => 'integer', ], 'PutLifecycleEventHookExecutionStatusInput' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'lifecycleEventHookExecutionId' => [ 'shape' => 'LifecycleEventHookExecutionId', ], 'status' => [ 'shape' => 'LifecycleEventStatus', ], ], ], 'PutLifecycleEventHookExecutionStatusOutput' => [ 'type' => 'structure', 'members' => [ 'lifecycleEventHookExecutionId' => [ 'shape' => 'LifecycleEventHookExecutionId', ], ], ], 'RawString' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'RawStringContent', ], 'sha256' => [ 'shape' => 'RawStringSha256', ], ], 'deprecated' => true, 'deprecatedMessage' => 'RawString and String revision type are deprecated, use AppSpecContent type instead.', ], 'RawStringContent' => [ 'type' => 'string', ], 'RawStringSha256' => [ 'type' => 'string', ], 'RegisterApplicationRevisionInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'revision', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'description' => [ 'shape' => 'Description', ], 'revision' => [ 'shape' => 'RevisionLocation', ], ], ], 'RegisterOnPremisesInstanceInput' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'InstanceName', ], 'iamSessionArn' => [ 'shape' => 'IamSessionArn', ], 'iamUserArn' => [ 'shape' => 'IamUserArn', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'Registered', 'Deregistered', ], ], 'RelatedDeployments' => [ 'type' => 'structure', 'members' => [ 'autoUpdateOutdatedInstancesRootDeploymentId' => [ 'shape' => 'DeploymentId', ], 'autoUpdateOutdatedInstancesDeploymentIds' => [ 'shape' => 'DeploymentsList', ], ], ], 'RemoveTagsFromOnPremisesInstancesInput' => [ 'type' => 'structure', 'required' => [ 'tags', 'instanceNames', ], 'members' => [ 'tags' => [ 'shape' => 'TagList', ], 'instanceNames' => [ 'shape' => 'InstanceNameList', ], ], ], 'Repository' => [ 'type' => 'string', ], 'ResourceArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceValidationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RevisionDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RevisionInfo' => [ 'type' => 'structure', 'members' => [ 'revisionLocation' => [ 'shape' => 'RevisionLocation', ], 'genericRevisionInfo' => [ 'shape' => 'GenericRevisionInfo', ], ], ], 'RevisionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevisionInfo', ], ], 'RevisionLocation' => [ 'type' => 'structure', 'members' => [ 'revisionType' => [ 'shape' => 'RevisionLocationType', ], 's3Location' => [ 'shape' => 'S3Location', ], 'gitHubLocation' => [ 'shape' => 'GitHubLocation', ], 'string' => [ 'shape' => 'RawString', ], 'appSpecContent' => [ 'shape' => 'AppSpecContent', ], ], ], 'RevisionLocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevisionLocation', ], ], 'RevisionLocationType' => [ 'type' => 'string', 'enum' => [ 'S3', 'GitHub', 'String', 'AppSpecContent', ], ], 'RevisionRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Role' => [ 'type' => 'string', ], 'RoleRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RollbackInfo' => [ 'type' => 'structure', 'members' => [ 'rollbackDeploymentId' => [ 'shape' => 'DeploymentId', ], 'rollbackTriggeringDeploymentId' => [ 'shape' => 'DeploymentId', ], 'rollbackMessage' => [ 'shape' => 'Description', ], ], ], 'S3Bucket' => [ 'type' => 'string', ], 'S3Key' => [ 'type' => 'string', ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3Bucket', ], 'key' => [ 'shape' => 'S3Key', ], 'bundleType' => [ 'shape' => 'BundleType', ], 'version' => [ 'shape' => 'VersionId', ], 'eTag' => [ 'shape' => 'ETag', ], ], ], 'ScriptName' => [ 'type' => 'string', ], 'SkipWaitTimeForInstanceTerminationInput' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ascending', 'descending', ], ], 'StopDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'autoRollbackEnabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'StopDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'StopStatus', ], 'statusMessage' => [ 'shape' => 'Message', ], ], ], 'StopStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Succeeded', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'Value' => [ 'shape' => 'Value', ], ], ], 'TagFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'Value' => [ 'shape' => 'Value', ], 'Type' => [ 'shape' => 'TagFilterType', ], ], ], 'TagFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagFilter', ], ], 'TagFilterType' => [ 'type' => 'string', 'enum' => [ 'KEY_ONLY', 'VALUE_ONLY', 'KEY_AND_VALUE', ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Key', ], ], 'TagLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagSetListLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TargetArn' => [ 'type' => 'string', ], 'TargetFilterName' => [ 'type' => 'string', 'enum' => [ 'TargetStatus', 'ServerInstanceLabel', ], ], 'TargetFilters' => [ 'type' => 'map', 'key' => [ 'shape' => 'TargetFilterName', ], 'value' => [ 'shape' => 'FilterValueList', ], ], 'TargetGroupInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'TargetGroupName', ], ], ], 'TargetGroupInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupInfo', ], ], 'TargetGroupName' => [ 'type' => 'string', ], 'TargetGroupPairInfo' => [ 'type' => 'structure', 'members' => [ 'targetGroups' => [ 'shape' => 'TargetGroupInfoList', ], 'prodTrafficRoute' => [ 'shape' => 'TrafficRoute', ], 'testTrafficRoute' => [ 'shape' => 'TrafficRoute', ], ], ], 'TargetGroupPairInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupPairInfo', ], ], 'TargetId' => [ 'type' => 'string', ], 'TargetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetId', ], ], 'TargetInstances' => [ 'type' => 'structure', 'members' => [ 'tagFilters' => [ 'shape' => 'EC2TagFilterList', ], 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupNameList', ], 'ec2TagSet' => [ 'shape' => 'EC2TagSet', ], ], ], 'TargetLabel' => [ 'type' => 'string', 'enum' => [ 'Blue', 'Green', ], ], 'TargetStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Succeeded', 'Failed', 'Skipped', 'Unknown', 'Ready', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Time' => [ 'type' => 'timestamp', ], 'TimeBasedCanary' => [ 'type' => 'structure', 'members' => [ 'canaryPercentage' => [ 'shape' => 'Percentage', ], 'canaryInterval' => [ 'shape' => 'WaitTimeInMins', ], ], ], 'TimeBasedLinear' => [ 'type' => 'structure', 'members' => [ 'linearPercentage' => [ 'shape' => 'Percentage', ], 'linearInterval' => [ 'shape' => 'WaitTimeInMins', ], ], ], 'TimeRange' => [ 'type' => 'structure', 'members' => [ 'start' => [ 'shape' => 'Timestamp', ], 'end' => [ 'shape' => 'Timestamp', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TrafficRoute' => [ 'type' => 'structure', 'members' => [ 'listenerArns' => [ 'shape' => 'ListenerArnList', ], ], ], 'TrafficRoutingConfig' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'TrafficRoutingType', ], 'timeBasedCanary' => [ 'shape' => 'TimeBasedCanary', ], 'timeBasedLinear' => [ 'shape' => 'TimeBasedLinear', ], ], ], 'TrafficRoutingType' => [ 'type' => 'string', 'enum' => [ 'TimeBasedCanary', 'TimeBasedLinear', 'AllAtOnce', ], ], 'TrafficWeight' => [ 'type' => 'double', ], 'TriggerConfig' => [ 'type' => 'structure', 'members' => [ 'triggerName' => [ 'shape' => 'TriggerName', ], 'triggerTargetArn' => [ 'shape' => 'TriggerTargetArn', ], 'triggerEvents' => [ 'shape' => 'TriggerEventTypeList', ], ], ], 'TriggerConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TriggerConfig', ], ], 'TriggerEventType' => [ 'type' => 'string', 'enum' => [ 'DeploymentStart', 'DeploymentSuccess', 'DeploymentFailure', 'DeploymentStop', 'DeploymentRollback', 'DeploymentReady', 'InstanceStart', 'InstanceSuccess', 'InstanceFailure', 'InstanceReady', ], ], 'TriggerEventTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TriggerEventType', ], ], 'TriggerName' => [ 'type' => 'string', ], 'TriggerTargetArn' => [ 'type' => 'string', ], 'TriggerTargetsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UnsupportedActionForDeploymentTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationInput' => [ 'type' => 'structure', 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'newApplicationName' => [ 'shape' => 'ApplicationName', ], ], ], 'UpdateDeploymentGroupInput' => [ 'type' => 'structure', 'required' => [ 'applicationName', 'currentDeploymentGroupName', ], 'members' => [ 'applicationName' => [ 'shape' => 'ApplicationName', ], 'currentDeploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'newDeploymentGroupName' => [ 'shape' => 'DeploymentGroupName', ], 'deploymentConfigName' => [ 'shape' => 'DeploymentConfigName', ], 'ec2TagFilters' => [ 'shape' => 'EC2TagFilterList', ], 'onPremisesInstanceTagFilters' => [ 'shape' => 'TagFilterList', ], 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupNameList', ], 'serviceRoleArn' => [ 'shape' => 'Role', ], 'triggerConfigurations' => [ 'shape' => 'TriggerConfigList', ], 'alarmConfiguration' => [ 'shape' => 'AlarmConfiguration', ], 'autoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfiguration', ], 'outdatedInstancesStrategy' => [ 'shape' => 'OutdatedInstancesStrategy', ], 'deploymentStyle' => [ 'shape' => 'DeploymentStyle', ], 'blueGreenDeploymentConfiguration' => [ 'shape' => 'BlueGreenDeploymentConfiguration', ], 'loadBalancerInfo' => [ 'shape' => 'LoadBalancerInfo', ], 'ec2TagSet' => [ 'shape' => 'EC2TagSet', ], 'ecsServices' => [ 'shape' => 'ECSServiceList', ], 'onPremisesTagSet' => [ 'shape' => 'OnPremisesTagSet', ], 'terminationHookEnabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'UpdateDeploymentGroupOutput' => [ 'type' => 'structure', 'members' => [ 'hooksNotCleanedUp' => [ 'shape' => 'AutoScalingGroupList', ], ], ], 'Value' => [ 'type' => 'string', ], 'Version' => [ 'type' => 'string', ], 'VersionId' => [ 'type' => 'string', ], 'WaitTimeInMins' => [ 'type' => 'integer', ], 'WaitTimeInSeconds' => [ 'type' => 'long', ], 'ZonalConfig' => [ 'type' => 'structure', 'members' => [ 'firstZoneMonitorDurationInSeconds' => [ 'shape' => 'WaitTimeInSeconds', ], 'monitorDurationInSeconds' => [ 'shape' => 'WaitTimeInSeconds', ], 'minimumHealthyHostsPerZone' => [ 'shape' => 'MinimumHealthyHostsPerZone', ], ], ], ],];
