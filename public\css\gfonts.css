/* open-sans-300 - vietnamese_latin-ext_latin_greek_cyrillic */
@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.eot'); /* IE9 Compat Modes */
    src: local(''),
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.woff2') format('woff2'), /* Super Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.woff') format('woff'), /* Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.ttf') format('truetype'), /* Safari, Android, iOS */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-300.svg#OpenSans') format('svg'); /* Legacy iOS */
  }
  /* open-sans-regular - vietnamese_latin-ext_latin_greek_cyrillic */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.eot'); /* IE9 Compat Modes */
    src: local(''),
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.woff2') format('woff2'), /* Super Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.woff') format('woff'), /* Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.ttf') format('truetype'), /* Safari, Android, iOS */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-regular.svg#OpenSans') format('svg'); /* Legacy iOS */
  }
  /* open-sans-600 - vietnamese_latin-ext_latin_greek_cyrillic */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    src: url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.eot'); /* IE9 Compat Modes */
    src: local(''),
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.woff2') format('woff2'), /* Super Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.woff') format('woff'), /* Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.ttf') format('truetype'), /* Safari, Android, iOS */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-600.svg#OpenSans') format('svg'); /* Legacy iOS */
  }
  /* open-sans-700 - vietnamese_latin-ext_latin_greek_cyrillic */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.eot'); /* IE9 Compat Modes */
    src: local(''),
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.woff2') format('woff2'), /* Super Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.woff') format('woff'), /* Modern Browsers */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.ttf') format('truetype'), /* Safari, Android, iOS */
         url('../fonts/open-sans-v20-vietnamese_latin-ext_latin_greek_cyrillic-700.svg#OpenSans') format('svg'); /* Legacy iOS */
  }