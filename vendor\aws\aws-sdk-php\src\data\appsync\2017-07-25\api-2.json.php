<?php
// This file was auto-generated from sdk-root/src/data/appsync/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'appsync', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'AWSAppSync', 'serviceFullName' => 'AWS AppSync', 'serviceId' => 'AppSync', 'signatureVersion' => 'v4', 'signingName' => 'appsync', 'uid' => 'appsync-2017-07-25', ], 'operations' => [ 'AssociateApi' => [ 'name' => 'AssociateApi', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domainnames/{domainName}/apiassociation', ], 'input' => [ 'shape' => 'AssociateApiRequest', ], 'output' => [ 'shape' => 'AssociateApiResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'AssociateMergedGraphqlApi' => [ 'name' => 'AssociateMergedGraphqlApi', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/sourceApis/{sourceApiIdentifier}/mergedApiAssociations', ], 'input' => [ 'shape' => 'AssociateMergedGraphqlApiRequest', ], 'output' => [ 'shape' => 'AssociateMergedGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'AssociateSourceGraphqlApi' => [ 'name' => 'AssociateSourceGraphqlApi', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations', ], 'input' => [ 'shape' => 'AssociateSourceGraphqlApiRequest', ], 'output' => [ 'shape' => 'AssociateSourceGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateApiCache' => [ 'name' => 'CreateApiCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/ApiCaches', ], 'input' => [ 'shape' => 'CreateApiCacheRequest', ], 'output' => [ 'shape' => 'CreateApiCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateApiKey' => [ 'name' => 'CreateApiKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/apikeys', ], 'input' => [ 'shape' => 'CreateApiKeyRequest', ], 'output' => [ 'shape' => 'CreateApiKeyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ApiKeyLimitExceededException', ], [ 'shape' => 'ApiKeyValidityOutOfBoundsException', ], ], ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/datasources', ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateDomainName' => [ 'name' => 'CreateDomainName', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domainnames', ], 'input' => [ 'shape' => 'CreateDomainNameRequest', ], 'output' => [ 'shape' => 'CreateDomainNameResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateFunction' => [ 'name' => 'CreateFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/functions', ], 'input' => [ 'shape' => 'CreateFunctionRequest', ], 'output' => [ 'shape' => 'CreateFunctionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateGraphqlApi' => [ 'name' => 'CreateGraphqlApi', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis', ], 'input' => [ 'shape' => 'CreateGraphqlApiRequest', ], 'output' => [ 'shape' => 'CreateGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ApiLimitExceededException', ], ], ], 'CreateResolver' => [ 'name' => 'CreateResolver', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}/resolvers', ], 'input' => [ 'shape' => 'CreateResolverRequest', ], 'output' => [ 'shape' => 'CreateResolverResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateType' => [ 'name' => 'CreateType', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/types', ], 'input' => [ 'shape' => 'CreateTypeRequest', ], 'output' => [ 'shape' => 'CreateTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteApiCache' => [ 'name' => 'DeleteApiCache', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/ApiCaches', ], 'input' => [ 'shape' => 'DeleteApiCacheRequest', ], 'output' => [ 'shape' => 'DeleteApiCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteApiKey' => [ 'name' => 'DeleteApiKey', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/apikeys/{id}', ], 'input' => [ 'shape' => 'DeleteApiKeyRequest', ], 'output' => [ 'shape' => 'DeleteApiKeyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/datasources/{name}', ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDomainName' => [ 'name' => 'DeleteDomainName', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/domainnames/{domainName}', ], 'input' => [ 'shape' => 'DeleteDomainNameRequest', ], 'output' => [ 'shape' => 'DeleteDomainNameResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DeleteFunction' => [ 'name' => 'DeleteFunction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/functions/{functionId}', ], 'input' => [ 'shape' => 'DeleteFunctionRequest', ], 'output' => [ 'shape' => 'DeleteFunctionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteGraphqlApi' => [ 'name' => 'DeleteGraphqlApi', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}', ], 'input' => [ 'shape' => 'DeleteGraphqlApiRequest', ], 'output' => [ 'shape' => 'DeleteGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteResolver' => [ 'name' => 'DeleteResolver', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}', ], 'input' => [ 'shape' => 'DeleteResolverRequest', ], 'output' => [ 'shape' => 'DeleteResolverResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteType' => [ 'name' => 'DeleteType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}', ], 'input' => [ 'shape' => 'DeleteTypeRequest', ], 'output' => [ 'shape' => 'DeleteTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DisassociateApi' => [ 'name' => 'DisassociateApi', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/domainnames/{domainName}/apiassociation', ], 'input' => [ 'shape' => 'DisassociateApiRequest', ], 'output' => [ 'shape' => 'DisassociateApiResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DisassociateMergedGraphqlApi' => [ 'name' => 'DisassociateMergedGraphqlApi', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/sourceApis/{sourceApiIdentifier}/mergedApiAssociations/{associationId}', ], 'input' => [ 'shape' => 'DisassociateMergedGraphqlApiRequest', ], 'output' => [ 'shape' => 'DisassociateMergedGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DisassociateSourceGraphqlApi' => [ 'name' => 'DisassociateSourceGraphqlApi', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}', ], 'input' => [ 'shape' => 'DisassociateSourceGraphqlApiRequest', ], 'output' => [ 'shape' => 'DisassociateSourceGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'EvaluateCode' => [ 'name' => 'EvaluateCode', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/dataplane-evaluatecode', ], 'input' => [ 'shape' => 'EvaluateCodeRequest', ], 'output' => [ 'shape' => 'EvaluateCodeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'EvaluateMappingTemplate' => [ 'name' => 'EvaluateMappingTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/dataplane-evaluatetemplate', ], 'input' => [ 'shape' => 'EvaluateMappingTemplateRequest', ], 'output' => [ 'shape' => 'EvaluateMappingTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'FlushApiCache' => [ 'name' => 'FlushApiCache', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apis/{apiId}/FlushCache', ], 'input' => [ 'shape' => 'FlushApiCacheRequest', ], 'output' => [ 'shape' => 'FlushApiCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetApiAssociation' => [ 'name' => 'GetApiAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/domainnames/{domainName}/apiassociation', ], 'input' => [ 'shape' => 'GetApiAssociationRequest', ], 'output' => [ 'shape' => 'GetApiAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetApiCache' => [ 'name' => 'GetApiCache', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/ApiCaches', ], 'input' => [ 'shape' => 'GetApiCacheRequest', ], 'output' => [ 'shape' => 'GetApiCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/datasources/{name}', ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetDataSourceIntrospection' => [ 'name' => 'GetDataSourceIntrospection', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/datasources/introspections/{introspectionId}', ], 'input' => [ 'shape' => 'GetDataSourceIntrospectionRequest', ], 'output' => [ 'shape' => 'GetDataSourceIntrospectionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetDomainName' => [ 'name' => 'GetDomainName', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/domainnames/{domainName}', ], 'input' => [ 'shape' => 'GetDomainNameRequest', ], 'output' => [ 'shape' => 'GetDomainNameResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetFunction' => [ 'name' => 'GetFunction', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/functions/{functionId}', ], 'input' => [ 'shape' => 'GetFunctionRequest', ], 'output' => [ 'shape' => 'GetFunctionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetGraphqlApi' => [ 'name' => 'GetGraphqlApi', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}', ], 'input' => [ 'shape' => 'GetGraphqlApiRequest', ], 'output' => [ 'shape' => 'GetGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetGraphqlApiEnvironmentVariables' => [ 'name' => 'GetGraphqlApiEnvironmentVariables', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/environmentVariables', ], 'input' => [ 'shape' => 'GetGraphqlApiEnvironmentVariablesRequest', ], 'output' => [ 'shape' => 'GetGraphqlApiEnvironmentVariablesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIntrospectionSchema' => [ 'name' => 'GetIntrospectionSchema', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/schema', ], 'input' => [ 'shape' => 'GetIntrospectionSchemaRequest', ], 'output' => [ 'shape' => 'GetIntrospectionSchemaResponse', ], 'errors' => [ [ 'shape' => 'GraphQLSchemaException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetResolver' => [ 'name' => 'GetResolver', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}', ], 'input' => [ 'shape' => 'GetResolverRequest', ], 'output' => [ 'shape' => 'GetResolverResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetSchemaCreationStatus' => [ 'name' => 'GetSchemaCreationStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/schemacreation', ], 'input' => [ 'shape' => 'GetSchemaCreationStatusRequest', ], 'output' => [ 'shape' => 'GetSchemaCreationStatusResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetSourceApiAssociation' => [ 'name' => 'GetSourceApiAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}', ], 'input' => [ 'shape' => 'GetSourceApiAssociationRequest', ], 'output' => [ 'shape' => 'GetSourceApiAssociationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetType' => [ 'name' => 'GetType', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}', ], 'input' => [ 'shape' => 'GetTypeRequest', ], 'output' => [ 'shape' => 'GetTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListApiKeys' => [ 'name' => 'ListApiKeys', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/apikeys', ], 'input' => [ 'shape' => 'ListApiKeysRequest', ], 'output' => [ 'shape' => 'ListApiKeysResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/datasources', ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDomainNames' => [ 'name' => 'ListDomainNames', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/domainnames', ], 'input' => [ 'shape' => 'ListDomainNamesRequest', ], 'output' => [ 'shape' => 'ListDomainNamesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListFunctions' => [ 'name' => 'ListFunctions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/functions', ], 'input' => [ 'shape' => 'ListFunctionsRequest', ], 'output' => [ 'shape' => 'ListFunctionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListGraphqlApis' => [ 'name' => 'ListGraphqlApis', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis', ], 'input' => [ 'shape' => 'ListGraphqlApisRequest', ], 'output' => [ 'shape' => 'ListGraphqlApisResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListResolvers' => [ 'name' => 'ListResolvers', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}/resolvers', ], 'input' => [ 'shape' => 'ListResolversRequest', ], 'output' => [ 'shape' => 'ListResolversResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListResolversByFunction' => [ 'name' => 'ListResolversByFunction', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/functions/{functionId}/resolvers', ], 'input' => [ 'shape' => 'ListResolversByFunctionRequest', ], 'output' => [ 'shape' => 'ListResolversByFunctionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListSourceApiAssociations' => [ 'name' => 'ListSourceApiAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/sourceApiAssociations', ], 'input' => [ 'shape' => 'ListSourceApiAssociationsRequest', ], 'output' => [ 'shape' => 'ListSourceApiAssociationsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTypes' => [ 'name' => 'ListTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apis/{apiId}/types', ], 'input' => [ 'shape' => 'ListTypesRequest', ], 'output' => [ 'shape' => 'ListTypesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTypesByAssociation' => [ 'name' => 'ListTypesByAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}/types', ], 'input' => [ 'shape' => 'ListTypesByAssociationRequest', ], 'output' => [ 'shape' => 'ListTypesByAssociationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'PutGraphqlApiEnvironmentVariables' => [ 'name' => 'PutGraphqlApiEnvironmentVariables', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apis/{apiId}/environmentVariables', ], 'input' => [ 'shape' => 'PutGraphqlApiEnvironmentVariablesRequest', ], 'output' => [ 'shape' => 'PutGraphqlApiEnvironmentVariablesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartDataSourceIntrospection' => [ 'name' => 'StartDataSourceIntrospection', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/datasources/introspections', ], 'input' => [ 'shape' => 'StartDataSourceIntrospectionRequest', ], 'output' => [ 'shape' => 'StartDataSourceIntrospectionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'StartSchemaCreation' => [ 'name' => 'StartSchemaCreation', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/schemacreation', ], 'input' => [ 'shape' => 'StartSchemaCreationRequest', ], 'output' => [ 'shape' => 'StartSchemaCreationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'StartSchemaMerge' => [ 'name' => 'StartSchemaMerge', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}/merge', ], 'input' => [ 'shape' => 'StartSchemaMergeRequest', ], 'output' => [ 'shape' => 'StartSchemaMergeResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateApiCache' => [ 'name' => 'UpdateApiCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/ApiCaches/update', ], 'input' => [ 'shape' => 'UpdateApiCacheRequest', ], 'output' => [ 'shape' => 'UpdateApiCacheResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateApiKey' => [ 'name' => 'UpdateApiKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/apikeys/{id}', ], 'input' => [ 'shape' => 'UpdateApiKeyRequest', ], 'output' => [ 'shape' => 'UpdateApiKeyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ApiKeyValidityOutOfBoundsException', ], ], ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/datasources/{name}', ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDomainName' => [ 'name' => 'UpdateDomainName', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/domainnames/{domainName}', ], 'input' => [ 'shape' => 'UpdateDomainNameRequest', ], 'output' => [ 'shape' => 'UpdateDomainNameResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UpdateFunction' => [ 'name' => 'UpdateFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/functions/{functionId}', ], 'input' => [ 'shape' => 'UpdateFunctionRequest', ], 'output' => [ 'shape' => 'UpdateFunctionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateGraphqlApi' => [ 'name' => 'UpdateGraphqlApi', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}', ], 'input' => [ 'shape' => 'UpdateGraphqlApiRequest', ], 'output' => [ 'shape' => 'UpdateGraphqlApiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResolver' => [ 'name' => 'UpdateResolver', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}', ], 'input' => [ 'shape' => 'UpdateResolverRequest', ], 'output' => [ 'shape' => 'UpdateResolverResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateSourceApiAssociation' => [ 'name' => 'UpdateSourceApiAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}', ], 'input' => [ 'shape' => 'UpdateSourceApiAssociationRequest', ], 'output' => [ 'shape' => 'UpdateSourceApiAssociationResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateType' => [ 'name' => 'UpdateType', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apis/{apiId}/types/{typeName}', ], 'input' => [ 'shape' => 'UpdateTypeRequest', ], 'output' => [ 'shape' => 'UpdateTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AdditionalAuthenticationProvider' => [ 'type' => 'structure', 'members' => [ 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'openIDConnectConfig' => [ 'shape' => 'OpenIDConnectConfig', ], 'userPoolConfig' => [ 'shape' => 'CognitoUserPoolConfig', ], 'lambdaAuthorizerConfig' => [ 'shape' => 'LambdaAuthorizerConfig', ], ], ], 'AdditionalAuthenticationProviders' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalAuthenticationProvider', ], ], 'ApiAssociation' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'apiId' => [ 'shape' => 'String', ], 'associationStatus' => [ 'shape' => 'AssociationStatus', ], 'deploymentDetail' => [ 'shape' => 'String', ], ], ], 'ApiCache' => [ 'type' => 'structure', 'members' => [ 'ttl' => [ 'shape' => 'Long', ], 'apiCachingBehavior' => [ 'shape' => 'ApiCachingBehavior', ], 'transitEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'atRestEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'type' => [ 'shape' => 'ApiCacheType', ], 'status' => [ 'shape' => 'ApiCacheStatus', ], 'healthMetricsConfig' => [ 'shape' => 'CacheHealthMetricsConfig', ], ], ], 'ApiCacheStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'DELETING', 'MODIFYING', 'FAILED', ], ], 'ApiCacheType' => [ 'type' => 'string', 'enum' => [ 'T2_SMALL', 'T2_MEDIUM', 'R4_LARGE', 'R4_XLARGE', 'R4_2XLARGE', 'R4_4XLARGE', 'R4_8XLARGE', 'SMALL', 'MEDIUM', 'LARGE', 'XLARGE', 'LARGE_2X', 'LARGE_4X', 'LARGE_8X', 'LARGE_12X', ], ], 'ApiCachingBehavior' => [ 'type' => 'string', 'enum' => [ 'FULL_REQUEST_CACHING', 'PER_RESOLVER_CACHING', ], ], 'ApiKey' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'expires' => [ 'shape' => 'Long', ], 'deletes' => [ 'shape' => 'Long', ], ], ], 'ApiKeyLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ApiKeyValidityOutOfBoundsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ApiKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiKey', ], ], 'ApiLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'AppSyncRuntime' => [ 'type' => 'structure', 'required' => [ 'name', 'runtimeVersion', ], 'members' => [ 'name' => [ 'shape' => 'RuntimeName', ], 'runtimeVersion' => [ 'shape' => 'String', ], ], ], 'AssociateApiRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'apiId', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], 'apiId' => [ 'shape' => 'String', ], ], ], 'AssociateApiResponse' => [ 'type' => 'structure', 'members' => [ 'apiAssociation' => [ 'shape' => 'ApiAssociation', ], ], ], 'AssociateMergedGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'sourceApiIdentifier', 'mergedApiIdentifier', ], 'members' => [ 'sourceApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sourceApiIdentifier', ], 'mergedApiIdentifier' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'sourceApiAssociationConfig' => [ 'shape' => 'SourceApiAssociationConfig', ], ], ], 'AssociateMergedGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociation' => [ 'shape' => 'SourceApiAssociation', ], ], ], 'AssociateSourceGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'mergedApiIdentifier', 'sourceApiIdentifier', ], 'members' => [ 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], 'sourceApiIdentifier' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'sourceApiAssociationConfig' => [ 'shape' => 'SourceApiAssociationConfig', ], ], ], 'AssociateSourceGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociation' => [ 'shape' => 'SourceApiAssociation', ], ], ], 'AssociationStatus' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'FAILED', 'SUCCESS', ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'API_KEY', 'AWS_IAM', 'AMAZON_COGNITO_USER_POOLS', 'OPENID_CONNECT', 'AWS_LAMBDA', ], ], 'AuthorizationConfig' => [ 'type' => 'structure', 'required' => [ 'authorizationType', ], 'members' => [ 'authorizationType' => [ 'shape' => 'AuthorizationType', ], 'awsIamConfig' => [ 'shape' => 'AwsIamConfig', ], ], ], 'AuthorizationType' => [ 'type' => 'string', 'enum' => [ 'AWS_IAM', ], ], 'AwsIamConfig' => [ 'type' => 'structure', 'members' => [ 'signingRegion' => [ 'shape' => 'String', ], 'signingServiceName' => [ 'shape' => 'String', ], ], ], 'BadRequestDetail' => [ 'type' => 'structure', 'members' => [ 'codeErrors' => [ 'shape' => 'CodeErrors', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'BadRequestReason', ], 'detail' => [ 'shape' => 'BadRequestDetail', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BadRequestReason' => [ 'type' => 'string', 'enum' => [ 'CODE_ERROR', ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanValue' => [ 'type' => 'boolean', ], 'CacheHealthMetricsConfig' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CachingConfig' => [ 'type' => 'structure', 'required' => [ 'ttl', ], 'members' => [ 'ttl' => [ 'shape' => 'Long', ], 'cachingKeys' => [ 'shape' => 'CachingKeys', ], ], ], 'CachingKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'CertificateArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z-]*:(acm|iam):[a-z0-9-]*:\\d{12}:(certificate|server-certificate)/[0-9A-Za-z_/-]*$', ], 'Code' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, ], 'CodeError' => [ 'type' => 'structure', 'members' => [ 'errorType' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], 'location' => [ 'shape' => 'CodeErrorLocation', ], ], ], 'CodeErrorColumn' => [ 'type' => 'integer', ], 'CodeErrorLine' => [ 'type' => 'integer', ], 'CodeErrorLocation' => [ 'type' => 'structure', 'members' => [ 'line' => [ 'shape' => 'CodeErrorLine', ], 'column' => [ 'shape' => 'CodeErrorColumn', ], 'span' => [ 'shape' => 'CodeErrorSpan', ], ], ], 'CodeErrorSpan' => [ 'type' => 'integer', ], 'CodeErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeError', ], ], 'CognitoUserPoolConfig' => [ 'type' => 'structure', 'required' => [ 'userPoolId', 'awsRegion', ], 'members' => [ 'userPoolId' => [ 'shape' => 'String', ], 'awsRegion' => [ 'shape' => 'String', ], 'appIdClientRegex' => [ 'shape' => 'String', ], ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConflictDetectionType' => [ 'type' => 'string', 'enum' => [ 'VERSION', 'NONE', ], ], 'ConflictHandlerType' => [ 'type' => 'string', 'enum' => [ 'OPTIMISTIC_CONCURRENCY', 'LAMBDA', 'AUTOMERGE', 'NONE', ], ], 'Context' => [ 'type' => 'string', 'max' => 28000, 'min' => 2, 'pattern' => '^[\\s\\S]*$', ], 'CreateApiCacheRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'ttl', 'apiCachingBehavior', 'type', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'ttl' => [ 'shape' => 'Long', ], 'transitEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'atRestEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'apiCachingBehavior' => [ 'shape' => 'ApiCachingBehavior', ], 'type' => [ 'shape' => 'ApiCacheType', ], 'healthMetricsConfig' => [ 'shape' => 'CacheHealthMetricsConfig', ], ], ], 'CreateApiCacheResponse' => [ 'type' => 'structure', 'members' => [ 'apiCache' => [ 'shape' => 'ApiCache', ], ], ], 'CreateApiKeyRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'description' => [ 'shape' => 'String', ], 'expires' => [ 'shape' => 'Long', ], ], ], 'CreateApiKeyResponse' => [ 'type' => 'structure', 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', 'type', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'DataSourceType', ], 'serviceRoleArn' => [ 'shape' => 'String', ], 'dynamodbConfig' => [ 'shape' => 'DynamodbDataSourceConfig', ], 'lambdaConfig' => [ 'shape' => 'LambdaDataSourceConfig', ], 'elasticsearchConfig' => [ 'shape' => 'ElasticsearchDataSourceConfig', ], 'openSearchServiceConfig' => [ 'shape' => 'OpenSearchServiceDataSourceConfig', ], 'httpConfig' => [ 'shape' => 'HttpDataSourceConfig', ], 'relationalDatabaseConfig' => [ 'shape' => 'RelationalDatabaseDataSourceConfig', ], 'eventBridgeConfig' => [ 'shape' => 'EventBridgeDataSourceConfig', ], 'metricsConfig' => [ 'shape' => 'DataSourceLevelMetricsConfig', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'CreateDomainNameRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'certificateArn', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'description' => [ 'shape' => 'Description', ], ], ], 'CreateDomainNameResponse' => [ 'type' => 'structure', 'members' => [ 'domainNameConfig' => [ 'shape' => 'DomainNameConfig', ], ], ], 'CreateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', 'dataSourceName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'functionVersion' => [ 'shape' => 'String', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], ], ], 'CreateFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'functionConfiguration' => [ 'shape' => 'FunctionConfiguration', ], ], ], 'CreateGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'authenticationType', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'logConfig' => [ 'shape' => 'LogConfig', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'userPoolConfig' => [ 'shape' => 'UserPoolConfig', ], 'openIDConnectConfig' => [ 'shape' => 'OpenIDConnectConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'additionalAuthenticationProviders' => [ 'shape' => 'AdditionalAuthenticationProviders', ], 'xrayEnabled' => [ 'shape' => 'Boolean', ], 'lambdaAuthorizerConfig' => [ 'shape' => 'LambdaAuthorizerConfig', ], 'visibility' => [ 'shape' => 'GraphQLApiVisibility', ], 'apiType' => [ 'shape' => 'GraphQLApiType', ], 'mergedApiExecutionRoleArn' => [ 'shape' => 'String', ], 'ownerContact' => [ 'shape' => 'String', ], 'introspectionConfig' => [ 'shape' => 'GraphQLApiIntrospectionConfig', ], 'queryDepthLimit' => [ 'shape' => 'QueryDepthLimit', ], 'resolverCountLimit' => [ 'shape' => 'ResolverCountLimit', ], 'enhancedMetricsConfig' => [ 'shape' => 'EnhancedMetricsConfig', ], ], ], 'CreateGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'graphqlApi' => [ 'shape' => 'GraphqlApi', ], ], ], 'CreateResolverRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'fieldName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'fieldName' => [ 'shape' => 'ResourceName', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'kind' => [ 'shape' => 'ResolverKind', ], 'pipelineConfig' => [ 'shape' => 'PipelineConfig', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'cachingConfig' => [ 'shape' => 'CachingConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], 'metricsConfig' => [ 'shape' => 'ResolverLevelMetricsConfig', ], ], ], 'CreateResolverResponse' => [ 'type' => 'structure', 'members' => [ 'resolver' => [ 'shape' => 'Resolver', ], ], ], 'CreateTypeRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'definition', 'format', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'definition' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', ], ], ], 'CreateTypeResponse' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'Type', ], ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'dataSourceArn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'DataSourceType', ], 'serviceRoleArn' => [ 'shape' => 'String', ], 'dynamodbConfig' => [ 'shape' => 'DynamodbDataSourceConfig', ], 'lambdaConfig' => [ 'shape' => 'LambdaDataSourceConfig', ], 'elasticsearchConfig' => [ 'shape' => 'ElasticsearchDataSourceConfig', ], 'openSearchServiceConfig' => [ 'shape' => 'OpenSearchServiceDataSourceConfig', ], 'httpConfig' => [ 'shape' => 'HttpDataSourceConfig', ], 'relationalDatabaseConfig' => [ 'shape' => 'RelationalDatabaseDataSourceConfig', ], 'eventBridgeConfig' => [ 'shape' => 'EventBridgeDataSourceConfig', ], 'metricsConfig' => [ 'shape' => 'DataSourceLevelMetricsConfig', ], ], ], 'DataSourceIntrospectionModel' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'fields' => [ 'shape' => 'DataSourceIntrospectionModelFields', ], 'primaryKey' => [ 'shape' => 'DataSourceIntrospectionModelIndex', ], 'indexes' => [ 'shape' => 'DataSourceIntrospectionModelIndexes', ], 'sdl' => [ 'shape' => 'String', ], ], ], 'DataSourceIntrospectionModelField' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'DataSourceIntrospectionModelFieldType', ], 'length' => [ 'shape' => 'Long', ], ], ], 'DataSourceIntrospectionModelFieldType' => [ 'type' => 'structure', 'members' => [ 'kind' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'DataSourceIntrospectionModelFieldType', ], 'values' => [ 'shape' => 'DataSourceIntrospectionModelFieldTypeValues', ], ], ], 'DataSourceIntrospectionModelFieldTypeValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DataSourceIntrospectionModelFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceIntrospectionModelField', ], ], 'DataSourceIntrospectionModelIndex' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'fields' => [ 'shape' => 'DataSourceIntrospectionModelIndexFields', ], ], ], 'DataSourceIntrospectionModelIndexFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DataSourceIntrospectionModelIndexes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceIntrospectionModelIndex', ], ], 'DataSourceIntrospectionModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceIntrospectionModel', ], ], 'DataSourceIntrospectionResult' => [ 'type' => 'structure', 'members' => [ 'models' => [ 'shape' => 'DataSourceIntrospectionModels', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DataSourceIntrospectionStatus' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'FAILED', 'SUCCESS', ], ], 'DataSourceLevelMetricsBehavior' => [ 'type' => 'string', 'enum' => [ 'FULL_REQUEST_DATA_SOURCE_METRICS', 'PER_DATA_SOURCE_METRICS', ], ], 'DataSourceLevelMetricsConfig' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'AWS_LAMBDA', 'AMAZON_DYNAMODB', 'AMAZON_ELASTICSEARCH', 'NONE', 'HTTP', 'RELATIONAL_DATABASE', 'AMAZON_OPENSEARCH_SERVICE', 'AMAZON_EVENTBRIDGE', ], ], 'DataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'Date' => [ 'type' => 'timestamp', ], 'DefaultAction' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'DeleteApiCacheRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'DeleteApiCacheResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteApiKeyRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'id', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteApiKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDomainNameRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'DeleteDomainNameResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'functionId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'functionId' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'functionId', ], ], ], 'DeleteFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'DeleteGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResolverRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'fieldName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'fieldName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'fieldName', ], ], ], 'DeleteResolverResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTypeRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], ], ], 'DeleteTypeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeltaSyncConfig' => [ 'type' => 'structure', 'members' => [ 'baseTableTTL' => [ 'shape' => 'Long', ], 'deltaSyncTableName' => [ 'shape' => 'String', ], 'deltaSyncTableTTL' => [ 'shape' => 'Long', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^.*$', ], 'DisassociateApiRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'DisassociateApiResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMergedGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'sourceApiIdentifier', 'associationId', ], 'members' => [ 'sourceApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sourceApiIdentifier', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], ], ], 'DisassociateMergedGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociationStatus' => [ 'shape' => 'SourceApiAssociationStatus', ], ], ], 'DisassociateSourceGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'mergedApiIdentifier', 'associationId', ], 'members' => [ 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], ], ], 'DisassociateSourceGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociationStatus' => [ 'shape' => 'SourceApiAssociationStatus', ], ], ], 'DomainName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '^(\\*[\\w\\d-]*\\.)?([\\w\\d-]+\\.)+[\\w\\d-]+$', ], 'DomainNameConfig' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'description' => [ 'shape' => 'Description', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'appsyncDomainName' => [ 'shape' => 'String', ], 'hostedZoneId' => [ 'shape' => 'String', ], ], ], 'DomainNameConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainNameConfig', ], ], 'DynamodbDataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'tableName', 'awsRegion', ], 'members' => [ 'tableName' => [ 'shape' => 'String', ], 'awsRegion' => [ 'shape' => 'String', ], 'useCallerCredentials' => [ 'shape' => 'Boolean', ], 'deltaSyncConfig' => [ 'shape' => 'DeltaSyncConfig', ], 'versioned' => [ 'shape' => 'Boolean', ], ], ], 'ElasticsearchDataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'endpoint', 'awsRegion', ], 'members' => [ 'endpoint' => [ 'shape' => 'String', ], 'awsRegion' => [ 'shape' => 'String', ], ], ], 'EnhancedMetricsConfig' => [ 'type' => 'structure', 'required' => [ 'resolverLevelMetricsBehavior', 'dataSourceLevelMetricsBehavior', 'operationLevelMetricsConfig', ], 'members' => [ 'resolverLevelMetricsBehavior' => [ 'shape' => 'ResolverLevelMetricsBehavior', ], 'dataSourceLevelMetricsBehavior' => [ 'shape' => 'DataSourceLevelMetricsBehavior', ], 'operationLevelMetricsConfig' => [ 'shape' => 'OperationLevelMetricsConfig', ], ], ], 'EnvironmentVariableKey' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '^[A-Za-z]+\\w*$', ], 'EnvironmentVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentVariableKey', ], 'value' => [ 'shape' => 'EnvironmentVariableValue', ], 'max' => 50, 'min' => 0, ], 'EnvironmentVariableValue' => [ 'type' => 'string', 'max' => 512, 'min' => 0, ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EvaluateCodeErrorDetail' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'codeErrors' => [ 'shape' => 'CodeErrors', ], ], ], 'EvaluateCodeRequest' => [ 'type' => 'structure', 'required' => [ 'runtime', 'code', 'context', ], 'members' => [ 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], 'context' => [ 'shape' => 'Context', ], 'function' => [ 'shape' => 'String', ], ], ], 'EvaluateCodeResponse' => [ 'type' => 'structure', 'members' => [ 'evaluationResult' => [ 'shape' => 'EvaluationResult', ], 'error' => [ 'shape' => 'EvaluateCodeErrorDetail', ], 'logs' => [ 'shape' => 'Logs', ], ], ], 'EvaluateMappingTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'template', 'context', ], 'members' => [ 'template' => [ 'shape' => 'Template', ], 'context' => [ 'shape' => 'Context', ], ], ], 'EvaluateMappingTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'evaluationResult' => [ 'shape' => 'EvaluationResult', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'logs' => [ 'shape' => 'Logs', ], ], ], 'EvaluationResult' => [ 'type' => 'string', 'max' => 65536, 'min' => 0, 'pattern' => '^[\\s\\S]*$', ], 'EventBridgeDataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'eventBusArn', ], 'members' => [ 'eventBusArn' => [ 'shape' => 'String', ], ], ], 'FieldLogLevel' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ERROR', 'ALL', ], ], 'FlushApiCacheRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'FlushApiCacheResponse' => [ 'type' => 'structure', 'members' => [], ], 'FunctionConfiguration' => [ 'type' => 'structure', 'members' => [ 'functionId' => [ 'shape' => 'String', ], 'functionArn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'functionVersion' => [ 'shape' => 'String', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], ], ], 'Functions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionConfiguration', ], ], 'FunctionsIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'GetApiAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'GetApiAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'apiAssociation' => [ 'shape' => 'ApiAssociation', ], ], ], 'GetApiCacheRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'GetApiCacheResponse' => [ 'type' => 'structure', 'members' => [ 'apiCache' => [ 'shape' => 'ApiCache', ], ], ], 'GetDataSourceIntrospectionRequest' => [ 'type' => 'structure', 'required' => [ 'introspectionId', ], 'members' => [ 'introspectionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'introspectionId', ], 'includeModelsSDL' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeModelsSDL', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetDataSourceIntrospectionResponse' => [ 'type' => 'structure', 'members' => [ 'introspectionId' => [ 'shape' => 'String', ], 'introspectionStatus' => [ 'shape' => 'DataSourceIntrospectionStatus', ], 'introspectionStatusDetail' => [ 'shape' => 'String', ], 'introspectionResult' => [ 'shape' => 'DataSourceIntrospectionResult', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'GetDomainNameRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'GetDomainNameResponse' => [ 'type' => 'structure', 'members' => [ 'domainNameConfig' => [ 'shape' => 'DomainNameConfig', ], ], ], 'GetFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'functionId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'functionId' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'functionId', ], ], ], 'GetFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'functionConfiguration' => [ 'shape' => 'FunctionConfiguration', ], ], ], 'GetGraphqlApiEnvironmentVariablesRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'GetGraphqlApiEnvironmentVariablesResponse' => [ 'type' => 'structure', 'members' => [ 'environmentVariables' => [ 'shape' => 'EnvironmentVariableMap', ], ], ], 'GetGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'GetGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'graphqlApi' => [ 'shape' => 'GraphqlApi', ], ], ], 'GetIntrospectionSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'format', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'format' => [ 'shape' => 'OutputType', 'location' => 'querystring', 'locationName' => 'format', ], 'includeDirectives' => [ 'shape' => 'BooleanValue', 'location' => 'querystring', 'locationName' => 'includeDirectives', ], ], ], 'GetIntrospectionSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'schema' => [ 'shape' => 'Blob', ], ], 'payload' => 'schema', ], 'GetResolverRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'fieldName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'fieldName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'fieldName', ], ], ], 'GetResolverResponse' => [ 'type' => 'structure', 'members' => [ 'resolver' => [ 'shape' => 'Resolver', ], ], ], 'GetSchemaCreationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], ], ], 'GetSchemaCreationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'SchemaStatus', ], 'details' => [ 'shape' => 'String', ], ], ], 'GetSourceApiAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'mergedApiIdentifier', 'associationId', ], 'members' => [ 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], ], ], 'GetSourceApiAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociation' => [ 'shape' => 'SourceApiAssociation', ], ], ], 'GetTypeRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'format', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', 'location' => 'querystring', 'locationName' => 'format', ], ], ], 'GetTypeResponse' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'Type', ], ], ], 'GraphQLApiIntrospectionConfig' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'GraphQLApiType' => [ 'type' => 'string', 'enum' => [ 'GRAPHQL', 'MERGED', ], ], 'GraphQLApiVisibility' => [ 'type' => 'string', 'enum' => [ 'GLOBAL', 'PRIVATE', ], ], 'GraphQLSchemaException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'GraphqlApi' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'apiId' => [ 'shape' => 'String', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'logConfig' => [ 'shape' => 'LogConfig', ], 'userPoolConfig' => [ 'shape' => 'UserPoolConfig', ], 'openIDConnectConfig' => [ 'shape' => 'OpenIDConnectConfig', ], 'arn' => [ 'shape' => 'String', ], 'uris' => [ 'shape' => 'MapOfStringToString', ], 'tags' => [ 'shape' => 'TagMap', ], 'additionalAuthenticationProviders' => [ 'shape' => 'AdditionalAuthenticationProviders', ], 'xrayEnabled' => [ 'shape' => 'Boolean', ], 'wafWebAclArn' => [ 'shape' => 'String', ], 'lambdaAuthorizerConfig' => [ 'shape' => 'LambdaAuthorizerConfig', ], 'dns' => [ 'shape' => 'MapOfStringToString', ], 'visibility' => [ 'shape' => 'GraphQLApiVisibility', ], 'apiType' => [ 'shape' => 'GraphQLApiType', ], 'mergedApiExecutionRoleArn' => [ 'shape' => 'String', ], 'owner' => [ 'shape' => 'String', ], 'ownerContact' => [ 'shape' => 'String', ], 'introspectionConfig' => [ 'shape' => 'GraphQLApiIntrospectionConfig', ], 'queryDepthLimit' => [ 'shape' => 'QueryDepthLimit', ], 'resolverCountLimit' => [ 'shape' => 'ResolverCountLimit', ], 'enhancedMetricsConfig' => [ 'shape' => 'EnhancedMetricsConfig', ], ], ], 'GraphqlApis' => [ 'type' => 'list', 'member' => [ 'shape' => 'GraphqlApi', ], ], 'HttpDataSourceConfig' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'String', ], 'authorizationConfig' => [ 'shape' => 'AuthorizationConfig', ], ], ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LambdaAuthorizerConfig' => [ 'type' => 'structure', 'required' => [ 'authorizerUri', ], 'members' => [ 'authorizerResultTtlInSeconds' => [ 'shape' => 'TTL', ], 'authorizerUri' => [ 'shape' => 'String', ], 'identityValidationExpression' => [ 'shape' => 'String', ], ], ], 'LambdaConflictHandlerConfig' => [ 'type' => 'structure', 'members' => [ 'lambdaConflictHandlerArn' => [ 'shape' => 'String', ], ], ], 'LambdaDataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'lambdaFunctionArn', ], 'members' => [ 'lambdaFunctionArn' => [ 'shape' => 'String', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListApiKeysRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApiKeysResponse' => [ 'type' => 'structure', 'members' => [ 'apiKeys' => [ 'shape' => 'ApiKeys', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'dataSources' => [ 'shape' => 'DataSources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDomainNamesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDomainNamesResponse' => [ 'type' => 'structure', 'members' => [ 'domainNameConfigs' => [ 'shape' => 'DomainNameConfigs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'functions' => [ 'shape' => 'Functions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListGraphqlApisRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'apiType' => [ 'shape' => 'GraphQLApiType', 'location' => 'querystring', 'locationName' => 'apiType', ], 'owner' => [ 'shape' => 'Ownership', 'location' => 'querystring', 'locationName' => 'owner', ], ], ], 'ListGraphqlApisResponse' => [ 'type' => 'structure', 'members' => [ 'graphqlApis' => [ 'shape' => 'GraphqlApis', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListResolversByFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'functionId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'functionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'functionId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListResolversByFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'resolvers' => [ 'shape' => 'Resolvers', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListResolversRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'typeName', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListResolversResponse' => [ 'type' => 'structure', 'members' => [ 'resolvers' => [ 'shape' => 'Resolvers', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSourceApiAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSourceApiAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociationSummaries' => [ 'shape' => 'SourceApiAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTypesByAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'mergedApiIdentifier', 'associationId', 'format', ], 'members' => [ 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTypesByAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'TypeList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTypesRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'format', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', 'location' => 'querystring', 'locationName' => 'format', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTypesResponse' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'TypeList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'LogConfig' => [ 'type' => 'structure', 'required' => [ 'fieldLogLevel', 'cloudWatchLogsRoleArn', ], 'members' => [ 'fieldLogLevel' => [ 'shape' => 'FieldLogLevel', ], 'cloudWatchLogsRoleArn' => [ 'shape' => 'String', ], 'excludeVerboseContent' => [ 'shape' => 'Boolean', ], ], ], 'Logs' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Long' => [ 'type' => 'long', ], 'MapOfStringToString' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'MappingTemplate' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '^.*$', ], 'MaxBatchSize' => [ 'type' => 'integer', 'max' => 2000, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 25, 'min' => 0, ], 'MergeType' => [ 'type' => 'string', 'enum' => [ 'MANUAL_MERGE', 'AUTO_MERGE', ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OpenIDConnectConfig' => [ 'type' => 'structure', 'required' => [ 'issuer', ], 'members' => [ 'issuer' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'String', ], 'iatTTL' => [ 'shape' => 'Long', ], 'authTTL' => [ 'shape' => 'Long', ], ], ], 'OpenSearchServiceDataSourceConfig' => [ 'type' => 'structure', 'required' => [ 'endpoint', 'awsRegion', ], 'members' => [ 'endpoint' => [ 'shape' => 'String', ], 'awsRegion' => [ 'shape' => 'String', ], ], ], 'OperationLevelMetricsConfig' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'OutputType' => [ 'type' => 'string', 'enum' => [ 'SDL', 'JSON', ], ], 'Ownership' => [ 'type' => 'string', 'enum' => [ 'CURRENT_ACCOUNT', 'OTHER_ACCOUNTS', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '[\\\\S]+', ], 'PipelineConfig' => [ 'type' => 'structure', 'members' => [ 'functions' => [ 'shape' => 'FunctionsIds', ], ], ], 'PutGraphqlApiEnvironmentVariablesRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'environmentVariables', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariableMap', ], ], ], 'PutGraphqlApiEnvironmentVariablesResponse' => [ 'type' => 'structure', 'members' => [ 'environmentVariables' => [ 'shape' => 'EnvironmentVariableMap', ], ], ], 'QueryDepthLimit' => [ 'type' => 'integer', 'max' => 75, 'min' => 0, ], 'RdsDataApiConfig' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'secretArn', 'databaseName', ], 'members' => [ 'resourceArn' => [ 'shape' => 'RdsDataApiConfigResourceArn', ], 'secretArn' => [ 'shape' => 'RdsDataApiConfigSecretArn', ], 'databaseName' => [ 'shape' => 'RdsDataApiConfigDatabaseName', ], ], ], 'RdsDataApiConfigDatabaseName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RdsDataApiConfigResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z-]*:rds:[a-z0-9-]*:\\d{12}:cluster:[0-9A-Za-z_/-]*$', ], 'RdsDataApiConfigSecretArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z-]*:secretsmanager:[a-z0-9-]*:\\d{12}:secret:[0-9A-Za-z_/-]*$', ], 'RdsHttpEndpointConfig' => [ 'type' => 'structure', 'members' => [ 'awsRegion' => [ 'shape' => 'String', ], 'dbClusterIdentifier' => [ 'shape' => 'String', ], 'databaseName' => [ 'shape' => 'String', ], 'schema' => [ 'shape' => 'String', ], 'awsSecretStoreArn' => [ 'shape' => 'String', ], ], ], 'RelationalDatabaseDataSourceConfig' => [ 'type' => 'structure', 'members' => [ 'relationalDatabaseSourceType' => [ 'shape' => 'RelationalDatabaseSourceType', ], 'rdsHttpEndpointConfig' => [ 'shape' => 'RdsHttpEndpointConfig', ], ], ], 'RelationalDatabaseSourceType' => [ 'type' => 'string', 'enum' => [ 'RDS_HTTP_ENDPOINT', ], ], 'Resolver' => [ 'type' => 'structure', 'members' => [ 'typeName' => [ 'shape' => 'ResourceName', ], 'fieldName' => [ 'shape' => 'ResourceName', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'resolverArn' => [ 'shape' => 'String', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'kind' => [ 'shape' => 'ResolverKind', ], 'pipelineConfig' => [ 'shape' => 'PipelineConfig', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'cachingConfig' => [ 'shape' => 'CachingConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], 'metricsConfig' => [ 'shape' => 'ResolverLevelMetricsConfig', ], ], ], 'ResolverCountLimit' => [ 'type' => 'integer', 'max' => 10000, 'min' => 0, ], 'ResolverKind' => [ 'type' => 'string', 'enum' => [ 'UNIT', 'PIPELINE', ], ], 'ResolverLevelMetricsBehavior' => [ 'type' => 'string', 'enum' => [ 'FULL_REQUEST_RESOLVER_METRICS', 'PER_RESOLVER_METRICS', ], ], 'ResolverLevelMetricsConfig' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Resolvers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resolver', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 75, 'min' => 70, 'pattern' => '^arn:aws:appsync:[A-Za-z0-9_/.-]{0,63}:\\d{12}:apis/[0-9A-Za-z_-]{26}$', ], 'ResourceName' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '[_A-Za-z][_0-9A-Za-z]*', ], 'RuntimeName' => [ 'type' => 'string', 'enum' => [ 'APPSYNC_JS', ], ], 'SchemaStatus' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'ACTIVE', 'DELETING', 'FAILED', 'SUCCESS', 'NOT_APPLICABLE', ], ], 'SourceApiAssociation' => [ 'type' => 'structure', 'members' => [ 'associationId' => [ 'shape' => 'String', ], 'associationArn' => [ 'shape' => 'String', ], 'sourceApiId' => [ 'shape' => 'String', ], 'sourceApiArn' => [ 'shape' => 'String', ], 'mergedApiArn' => [ 'shape' => 'String', ], 'mergedApiId' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'sourceApiAssociationConfig' => [ 'shape' => 'SourceApiAssociationConfig', ], 'sourceApiAssociationStatus' => [ 'shape' => 'SourceApiAssociationStatus', ], 'sourceApiAssociationStatusDetail' => [ 'shape' => 'String', ], 'lastSuccessfulMergeDate' => [ 'shape' => 'Date', ], ], ], 'SourceApiAssociationConfig' => [ 'type' => 'structure', 'members' => [ 'mergeType' => [ 'shape' => 'MergeType', ], ], ], 'SourceApiAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'MERGE_SCHEDULED', 'MERGE_FAILED', 'MERGE_SUCCESS', 'MERGE_IN_PROGRESS', 'AUTO_MERGE_SCHEDULE_FAILED', 'DELETION_SCHEDULED', 'DELETION_IN_PROGRESS', 'DELETION_FAILED', ], ], 'SourceApiAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'associationId' => [ 'shape' => 'String', ], 'associationArn' => [ 'shape' => 'String', ], 'sourceApiId' => [ 'shape' => 'String', ], 'sourceApiArn' => [ 'shape' => 'String', ], 'mergedApiId' => [ 'shape' => 'String', ], 'mergedApiArn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], ], ], 'SourceApiAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceApiAssociationSummary', ], ], 'StartDataSourceIntrospectionRequest' => [ 'type' => 'structure', 'members' => [ 'rdsDataApiConfig' => [ 'shape' => 'RdsDataApiConfig', ], ], ], 'StartDataSourceIntrospectionResponse' => [ 'type' => 'structure', 'members' => [ 'introspectionId' => [ 'shape' => 'String', ], 'introspectionStatus' => [ 'shape' => 'DataSourceIntrospectionStatus', ], 'introspectionStatusDetail' => [ 'shape' => 'String', ], ], ], 'StartSchemaCreationRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'definition', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'definition' => [ 'shape' => 'Blob', ], ], ], 'StartSchemaCreationResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'SchemaStatus', ], ], ], 'StartSchemaMergeRequest' => [ 'type' => 'structure', 'required' => [ 'associationId', 'mergedApiIdentifier', ], 'members' => [ 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], ], ], 'StartSchemaMergeResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociationStatus' => [ 'shape' => 'SourceApiAssociationStatus', ], ], ], 'String' => [ 'type' => 'string', ], 'SyncConfig' => [ 'type' => 'structure', 'members' => [ 'conflictHandler' => [ 'shape' => 'ConflictHandlerType', ], 'conflictDetection' => [ 'shape' => 'ConflictDetectionType', ], 'lambdaConflictHandlerConfig' => [ 'shape' => 'LambdaConflictHandlerConfig', ], ], ], 'TTL' => [ 'type' => 'integer', 'max' => 3600, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[ a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[\\s\\w+-=\\.:/@]*$', ], 'Template' => [ 'type' => 'string', 'max' => 65536, 'min' => 2, 'pattern' => '^[\\s\\S]*$', ], 'Type' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'definition' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', ], ], ], 'TypeDefinitionFormat' => [ 'type' => 'string', 'enum' => [ 'SDL', 'JSON', ], ], 'TypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Type', ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApiCacheRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'ttl', 'apiCachingBehavior', 'type', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'ttl' => [ 'shape' => 'Long', ], 'apiCachingBehavior' => [ 'shape' => 'ApiCachingBehavior', ], 'type' => [ 'shape' => 'ApiCacheType', ], 'healthMetricsConfig' => [ 'shape' => 'CacheHealthMetricsConfig', ], ], ], 'UpdateApiCacheResponse' => [ 'type' => 'structure', 'members' => [ 'apiCache' => [ 'shape' => 'ApiCache', ], ], ], 'UpdateApiKeyRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'id', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'description' => [ 'shape' => 'String', ], 'expires' => [ 'shape' => 'Long', ], ], ], 'UpdateApiKeyResponse' => [ 'type' => 'structure', 'members' => [ 'apiKey' => [ 'shape' => 'ApiKey', ], ], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', 'type', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'DataSourceType', ], 'serviceRoleArn' => [ 'shape' => 'String', ], 'dynamodbConfig' => [ 'shape' => 'DynamodbDataSourceConfig', ], 'lambdaConfig' => [ 'shape' => 'LambdaDataSourceConfig', ], 'elasticsearchConfig' => [ 'shape' => 'ElasticsearchDataSourceConfig', ], 'openSearchServiceConfig' => [ 'shape' => 'OpenSearchServiceDataSourceConfig', ], 'httpConfig' => [ 'shape' => 'HttpDataSourceConfig', ], 'relationalDatabaseConfig' => [ 'shape' => 'RelationalDatabaseDataSourceConfig', ], 'eventBridgeConfig' => [ 'shape' => 'EventBridgeDataSourceConfig', ], 'metricsConfig' => [ 'shape' => 'DataSourceLevelMetricsConfig', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'UpdateDomainNameRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateDomainNameResponse' => [ 'type' => 'structure', 'members' => [ 'domainNameConfig' => [ 'shape' => 'DomainNameConfig', ], ], ], 'UpdateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', 'functionId', 'dataSourceName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'String', ], 'functionId' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'functionId', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'functionVersion' => [ 'shape' => 'String', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], ], ], 'UpdateFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'functionConfiguration' => [ 'shape' => 'FunctionConfiguration', ], ], ], 'UpdateGraphqlApiRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'name', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'name' => [ 'shape' => 'String', ], 'logConfig' => [ 'shape' => 'LogConfig', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'userPoolConfig' => [ 'shape' => 'UserPoolConfig', ], 'openIDConnectConfig' => [ 'shape' => 'OpenIDConnectConfig', ], 'additionalAuthenticationProviders' => [ 'shape' => 'AdditionalAuthenticationProviders', ], 'xrayEnabled' => [ 'shape' => 'Boolean', ], 'lambdaAuthorizerConfig' => [ 'shape' => 'LambdaAuthorizerConfig', ], 'mergedApiExecutionRoleArn' => [ 'shape' => 'String', ], 'ownerContact' => [ 'shape' => 'String', ], 'introspectionConfig' => [ 'shape' => 'GraphQLApiIntrospectionConfig', ], 'queryDepthLimit' => [ 'shape' => 'QueryDepthLimit', ], 'resolverCountLimit' => [ 'shape' => 'ResolverCountLimit', ], 'enhancedMetricsConfig' => [ 'shape' => 'EnhancedMetricsConfig', ], ], ], 'UpdateGraphqlApiResponse' => [ 'type' => 'structure', 'members' => [ 'graphqlApi' => [ 'shape' => 'GraphqlApi', ], ], ], 'UpdateResolverRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'fieldName', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'fieldName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'fieldName', ], 'dataSourceName' => [ 'shape' => 'ResourceName', ], 'requestMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'responseMappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'kind' => [ 'shape' => 'ResolverKind', ], 'pipelineConfig' => [ 'shape' => 'PipelineConfig', ], 'syncConfig' => [ 'shape' => 'SyncConfig', ], 'cachingConfig' => [ 'shape' => 'CachingConfig', ], 'maxBatchSize' => [ 'shape' => 'MaxBatchSize', ], 'runtime' => [ 'shape' => 'AppSyncRuntime', ], 'code' => [ 'shape' => 'Code', ], 'metricsConfig' => [ 'shape' => 'ResolverLevelMetricsConfig', ], ], ], 'UpdateResolverResponse' => [ 'type' => 'structure', 'members' => [ 'resolver' => [ 'shape' => 'Resolver', ], ], ], 'UpdateSourceApiAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'associationId', 'mergedApiIdentifier', ], 'members' => [ 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], 'mergedApiIdentifier' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'mergedApiIdentifier', ], 'description' => [ 'shape' => 'String', ], 'sourceApiAssociationConfig' => [ 'shape' => 'SourceApiAssociationConfig', ], ], ], 'UpdateSourceApiAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceApiAssociation' => [ 'shape' => 'SourceApiAssociation', ], ], ], 'UpdateTypeRequest' => [ 'type' => 'structure', 'required' => [ 'apiId', 'typeName', 'format', ], 'members' => [ 'apiId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'apiId', ], 'typeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'typeName', ], 'definition' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'TypeDefinitionFormat', ], ], ], 'UpdateTypeResponse' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'Type', ], ], ], 'UserPoolConfig' => [ 'type' => 'structure', 'required' => [ 'userPoolId', 'awsRegion', 'defaultAction', ], 'members' => [ 'userPoolId' => [ 'shape' => 'String', ], 'awsRegion' => [ 'shape' => 'String', ], 'defaultAction' => [ 'shape' => 'DefaultAction', ], 'appIdClientRegex' => [ 'shape' => 'String', ], ], ], ],];
