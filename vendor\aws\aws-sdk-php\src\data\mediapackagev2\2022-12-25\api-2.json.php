<?php
// This file was auto-generated from sdk-root/src/data/mediapackagev2/2022-12-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-12-25', 'endpointPrefix' => 'mediapackagev2', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'mediapackagev2', 'serviceFullName' => 'AWS Elemental MediaPackage v2', 'serviceId' => 'MediaPackageV2', 'signatureVersion' => 'v4', 'signingName' => 'mediapackagev2', 'uid' => 'mediapackagev2-2022-12-25', ], 'operations' => [ 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateChannelGroup' => [ 'name' => 'CreateChannelGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/channelGroup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChannelGroupRequest', ], 'output' => [ 'shape' => 'CreateChannelGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateOriginEndpoint' => [ 'name' => 'CreateOriginEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateOriginEndpointRequest', ], 'output' => [ 'shape' => 'CreateOriginEndpointResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'output' => [ 'shape' => 'DeleteChannelResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteChannelGroup' => [ 'name' => 'DeleteChannelGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channelGroup/{ChannelGroupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelGroupRequest', ], 'output' => [ 'shape' => 'DeleteChannelGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteChannelPolicy' => [ 'name' => 'DeleteChannelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelPolicyRequest', ], 'output' => [ 'shape' => 'DeleteChannelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteOriginEndpoint' => [ 'name' => 'DeleteOriginEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteOriginEndpointRequest', ], 'output' => [ 'shape' => 'DeleteOriginEndpointResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteOriginEndpointPolicy' => [ 'name' => 'DeleteOriginEndpointPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteOriginEndpointPolicyRequest', ], 'output' => [ 'shape' => 'DeleteOriginEndpointPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetChannel' => [ 'name' => 'GetChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelRequest', ], 'output' => [ 'shape' => 'GetChannelResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetChannelGroup' => [ 'name' => 'GetChannelGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelGroupRequest', ], 'output' => [ 'shape' => 'GetChannelGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetChannelPolicy' => [ 'name' => 'GetChannelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelPolicyRequest', ], 'output' => [ 'shape' => 'GetChannelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetOriginEndpoint' => [ 'name' => 'GetOriginEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginEndpointRequest', ], 'output' => [ 'shape' => 'GetOriginEndpointResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetOriginEndpointPolicy' => [ 'name' => 'GetOriginEndpointPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginEndpointPolicyRequest', ], 'output' => [ 'shape' => 'GetOriginEndpointPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListChannelGroups' => [ 'name' => 'ListChannelGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelGroupsRequest', ], 'output' => [ 'shape' => 'ListChannelGroupsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListOriginEndpoints' => [ 'name' => 'ListOriginEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOriginEndpointsRequest', ], 'output' => [ 'shape' => 'ListOriginEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'PutChannelPolicy' => [ 'name' => 'PutChannelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutChannelPolicyRequest', ], 'output' => [ 'shape' => 'PutChannelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'PutOriginEndpointPolicy' => [ 'name' => 'PutOriginEndpointPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutOriginEndpointPolicyRequest', ], 'output' => [ 'shape' => 'PutOriginEndpointPolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateChannelGroup' => [ 'name' => 'UpdateChannelGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channelGroup/{ChannelGroupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelGroupRequest', ], 'output' => [ 'shape' => 'UpdateChannelGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateOriginEndpoint' => [ 'name' => 'UpdateOriginEndpoint', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOriginEndpointRequest', ], 'output' => [ 'shape' => 'UpdateOriginEndpointResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AdMarkerHls' => [ 'type' => 'string', 'enum' => [ 'DATERANGE', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChannelGroupListConfiguration' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'Arn', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'ChannelGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelGroupListConfiguration', ], ], 'ChannelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelListConfiguration', ], ], 'ChannelListConfiguration' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelGroupName', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelName' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CmafEncryptionMethod' => [ 'type' => 'string', 'enum' => [ 'CENC', 'CBCS', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ConflictExceptionType' => [ 'shape' => 'ConflictExceptionType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionType' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_IN_USE', 'RESOURCE_ALREADY_EXISTS', 'IDEMPOTENT_PARAMETER_MISMATCH', 'CONFLICTING_OPERATION', ], ], 'ContainerType' => [ 'type' => 'string', 'enum' => [ 'TS', 'CMAF', ], ], 'CreateChannelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'x-amzn-client-token', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateChannelGroupResponse' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'Arn', 'EgressDomain', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'EgressDomain' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'x-amzn-client-token', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelGroupName', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelName' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IngestEndpoints' => [ 'shape' => 'IngestEndpointList', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ManifestName', ], 'ChildManifestName' => [ 'shape' => 'ManifestName', ], 'ScteHls' => [ 'shape' => 'ScteHls', ], 'ManifestWindowSeconds' => [ 'shape' => 'CreateHlsManifestConfigurationManifestWindowSecondsInteger', ], 'ProgramDateTimeIntervalSeconds' => [ 'shape' => 'CreateHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], ], ], 'CreateHlsManifestConfigurationManifestWindowSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 30, ], 'CreateHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1209600, 'min' => 1, ], 'CreateHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateHlsManifestConfiguration', ], ], 'CreateLowLatencyHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ManifestName', ], 'ChildManifestName' => [ 'shape' => 'ManifestName', ], 'ScteHls' => [ 'shape' => 'ScteHls', ], 'ManifestWindowSeconds' => [ 'shape' => 'CreateLowLatencyHlsManifestConfigurationManifestWindowSecondsInteger', ], 'ProgramDateTimeIntervalSeconds' => [ 'shape' => 'CreateLowLatencyHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], ], ], 'CreateLowLatencyHlsManifestConfigurationManifestWindowSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 30, ], 'CreateLowLatencyHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1209600, 'min' => 1, ], 'CreateLowLatencyHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateLowLatencyHlsManifestConfiguration', ], ], 'CreateOriginEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Segment' => [ 'shape' => 'Segment', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'x-amzn-client-token', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'StartoverWindowSeconds' => [ 'shape' => 'CreateOriginEndpointRequestStartoverWindowSecondsInteger', ], 'HlsManifests' => [ 'shape' => 'CreateHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'CreateLowLatencyHlsManifests', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateOriginEndpointRequestStartoverWindowSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1209600, 'min' => 60, ], 'CreateOriginEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', 'Segment', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Segment' => [ 'shape' => 'Segment', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'StartoverWindowSeconds' => [ 'shape' => 'Integer', ], 'HlsManifests' => [ 'shape' => 'GetHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'GetLowLatencyHlsManifests', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DeleteChannelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], ], ], 'DeleteChannelGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOriginEndpointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], ], ], 'DeleteOriginEndpointPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOriginEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], ], ], 'DeleteOriginEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DrmSystem' => [ 'type' => 'string', 'enum' => [ 'CLEAR_KEY_AES_128', 'FAIRPLAY', 'PLAYREADY', 'WIDEVINE', ], ], 'Encryption' => [ 'type' => 'structure', 'required' => [ 'EncryptionMethod', 'SpekeKeyProvider', ], 'members' => [ 'ConstantInitializationVector' => [ 'shape' => 'EncryptionConstantInitializationVectorString', ], 'EncryptionMethod' => [ 'shape' => 'EncryptionMethod', ], 'KeyRotationIntervalSeconds' => [ 'shape' => 'EncryptionKeyRotationIntervalSecondsInteger', ], 'SpekeKeyProvider' => [ 'shape' => 'SpekeKeyProvider', ], ], ], 'EncryptionConstantInitializationVectorString' => [ 'type' => 'string', 'max' => 32, 'min' => 32, 'pattern' => '[0-9a-fA-F]+', ], 'EncryptionContractConfiguration' => [ 'type' => 'structure', 'required' => [ 'PresetSpeke20Audio', 'PresetSpeke20Video', ], 'members' => [ 'PresetSpeke20Audio' => [ 'shape' => 'PresetSpeke20Audio', ], 'PresetSpeke20Video' => [ 'shape' => 'PresetSpeke20Video', ], ], ], 'EncryptionKeyRotationIntervalSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 31536000, 'min' => 300, ], 'EncryptionMethod' => [ 'type' => 'structure', 'members' => [ 'TsEncryptionMethod' => [ 'shape' => 'TsEncryptionMethod', ], 'CmafEncryptionMethod' => [ 'shape' => 'CmafEncryptionMethod', ], ], ], 'EntityTag' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\S]+', ], 'FilterConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManifestFilter' => [ 'shape' => 'FilterConfigurationManifestFilterString', ], 'Start' => [ 'shape' => 'Timestamp', ], 'End' => [ 'shape' => 'Timestamp', ], 'TimeDelaySeconds' => [ 'shape' => 'FilterConfigurationTimeDelaySecondsInteger', ], ], ], 'FilterConfigurationManifestFilterString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FilterConfigurationTimeDelaySecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1209600, 'min' => 0, ], 'GetChannelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], ], ], 'GetChannelGroupResponse' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'Arn', 'EgressDomain', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'EgressDomain' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'GetChannelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'Policy', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'String', ], 'ChannelName' => [ 'shape' => 'String', ], 'Policy' => [ 'shape' => 'PolicyText', ], ], ], 'GetChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'GetChannelResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelGroupName', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelName' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IngestEndpoints' => [ 'shape' => 'IngestEndpointList', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'Url', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ResourceName', ], 'Url' => [ 'shape' => 'String', ], 'ChildManifestName' => [ 'shape' => 'ResourceName', ], 'ManifestWindowSeconds' => [ 'shape' => 'Integer', ], 'ProgramDateTimeIntervalSeconds' => [ 'shape' => 'Integer', ], 'ScteHls' => [ 'shape' => 'ScteHls', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], ], ], 'GetHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetHlsManifestConfiguration', ], ], 'GetLowLatencyHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'Url', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ResourceName', ], 'Url' => [ 'shape' => 'String', ], 'ChildManifestName' => [ 'shape' => 'ResourceName', ], 'ManifestWindowSeconds' => [ 'shape' => 'Integer', ], 'ProgramDateTimeIntervalSeconds' => [ 'shape' => 'Integer', ], 'ScteHls' => [ 'shape' => 'ScteHls', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], ], ], 'GetLowLatencyHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetLowLatencyHlsManifestConfiguration', ], ], 'GetOriginEndpointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], ], ], 'GetOriginEndpointPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'Policy', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'Policy' => [ 'shape' => 'PolicyText', ], ], ], 'GetOriginEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], ], ], 'GetOriginEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', 'Segment', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Segment' => [ 'shape' => 'Segment', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'StartoverWindowSeconds' => [ 'shape' => 'Integer', ], 'HlsManifests' => [ 'shape' => 'GetHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'GetLowLatencyHlsManifests', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\S]+', ], 'IngestEndpoint' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Url' => [ 'shape' => 'String', ], ], ], 'IngestEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestEndpoint', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListChannelGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListResourceMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ChannelGroupsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'MaxResults' => [ 'shape' => 'ListResourceMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ChannelList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ResourceName', ], 'ChildManifestName' => [ 'shape' => 'ResourceName', ], 'Url' => [ 'shape' => 'String', ], ], ], 'ListHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListHlsManifestConfiguration', ], ], 'ListLowLatencyHlsManifestConfiguration' => [ 'type' => 'structure', 'required' => [ 'ManifestName', ], 'members' => [ 'ManifestName' => [ 'shape' => 'ResourceName', ], 'ChildManifestName' => [ 'shape' => 'ResourceName', ], 'Url' => [ 'shape' => 'String', ], ], ], 'ListLowLatencyHlsManifests' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListLowLatencyHlsManifestConfiguration', ], ], 'ListOriginEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'MaxResults' => [ 'shape' => 'ListResourceMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListOriginEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'OriginEndpointsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListResourceMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'ManifestName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'OriginEndpointListConfiguration' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'HlsManifests' => [ 'shape' => 'ListHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'ListLowLatencyHlsManifests', ], ], ], 'OriginEndpointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginEndpointListConfiguration', ], ], 'PolicyText' => [ 'type' => 'string', 'max' => 6144, 'min' => 0, ], 'PresetSpeke20Audio' => [ 'type' => 'string', 'enum' => [ 'PRESET_AUDIO_1', 'PRESET_AUDIO_2', 'PRESET_AUDIO_3', 'SHARED', 'UNENCRYPTED', ], ], 'PresetSpeke20Video' => [ 'type' => 'string', 'enum' => [ 'PRESET_VIDEO_1', 'PRESET_VIDEO_2', 'PRESET_VIDEO_3', 'PRESET_VIDEO_4', 'PRESET_VIDEO_5', 'PRESET_VIDEO_6', 'PRESET_VIDEO_7', 'PRESET_VIDEO_8', 'SHARED', 'UNENCRYPTED', ], ], 'PutChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'Policy', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'Policy' => [ 'shape' => 'PolicyText', ], ], ], 'PutChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutOriginEndpointPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'Policy', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], 'Policy' => [ 'shape' => 'PolicyText', ], ], ], 'PutOriginEndpointPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceTypeNotFound' => [ 'shape' => 'ResourceTypeNotFound', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceTypeNotFound' => [ 'type' => 'string', 'enum' => [ 'CHANNEL_GROUP', 'CHANNEL', 'ORIGIN_ENDPOINT', ], ], 'Scte' => [ 'type' => 'structure', 'members' => [ 'ScteFilter' => [ 'shape' => 'ScteFilterList', ], ], ], 'ScteFilter' => [ 'type' => 'string', 'enum' => [ 'SPLICE_INSERT', 'BREAK', 'PROVIDER_ADVERTISEMENT', 'DISTRIBUTOR_ADVERTISEMENT', 'PROVIDER_PLACEMENT_OPPORTUNITY', 'DISTRIBUTOR_PLACEMENT_OPPORTUNITY', 'PROVIDER_OVERLAY_PLACEMENT_OPPORTUNITY', 'DISTRIBUTOR_OVERLAY_PLACEMENT_OPPORTUNITY', 'PROGRAM', ], ], 'ScteFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScteFilter', ], 'max' => 100, 'min' => 0, ], 'ScteHls' => [ 'type' => 'structure', 'members' => [ 'AdMarkerHls' => [ 'shape' => 'AdMarkerHls', ], ], ], 'Segment' => [ 'type' => 'structure', 'members' => [ 'SegmentDurationSeconds' => [ 'shape' => 'SegmentSegmentDurationSecondsInteger', ], 'SegmentName' => [ 'shape' => 'SegmentSegmentNameString', ], 'TsUseAudioRenditionGroup' => [ 'shape' => 'Boolean', ], 'IncludeIframeOnlyStreams' => [ 'shape' => 'Boolean', ], 'TsIncludeDvbSubtitles' => [ 'shape' => 'Boolean', ], 'Scte' => [ 'shape' => 'Scte', ], 'Encryption' => [ 'shape' => 'Encryption', ], ], ], 'SegmentSegmentDurationSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 30, 'min' => 1, ], 'SegmentSegmentNameString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SpekeKeyProvider' => [ 'type' => 'structure', 'required' => [ 'EncryptionContractConfiguration', 'ResourceId', 'DrmSystems', 'RoleArn', 'Url', ], 'members' => [ 'EncryptionContractConfiguration' => [ 'shape' => 'EncryptionContractConfiguration', ], 'ResourceId' => [ 'shape' => 'SpekeKeyProviderResourceIdString', ], 'DrmSystems' => [ 'shape' => 'SpekeKeyProviderDrmSystemsList', ], 'RoleArn' => [ 'shape' => 'SpekeKeyProviderRoleArnString', ], 'Url' => [ 'shape' => 'SpekeKeyProviderUrlString', ], ], ], 'SpekeKeyProviderDrmSystemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DrmSystem', ], 'max' => 4, 'min' => 1, ], 'SpekeKeyProviderResourceIdString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[0-9a-zA-Z_-]+', ], 'SpekeKeyProviderRoleArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'SpekeKeyProviderUrlString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'TagArn' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TsEncryptionMethod' => [ 'type' => 'string', 'enum' => [ 'AES_128', 'SAMPLE_AES', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateChannelGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ETag' => [ 'shape' => 'EntityTag', 'location' => 'header', 'locationName' => 'x-amzn-update-if-match', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateChannelGroupResponse' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'Arn', 'EgressDomain', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'EgressDomain' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ETag' => [ 'shape' => 'EntityTag', 'location' => 'header', 'locationName' => 'x-amzn-update-if-match', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelGroupName', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelName' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IngestEndpoints' => [ 'shape' => 'IngestEndpointList', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'UpdateOriginEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', ], 'members' => [ 'ChannelGroupName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelGroupName', ], 'ChannelName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'OriginEndpointName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Segment' => [ 'shape' => 'Segment', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'StartoverWindowSeconds' => [ 'shape' => 'UpdateOriginEndpointRequestStartoverWindowSecondsInteger', ], 'HlsManifests' => [ 'shape' => 'CreateHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'CreateLowLatencyHlsManifests', ], 'ETag' => [ 'shape' => 'EntityTag', 'location' => 'header', 'locationName' => 'x-amzn-update-if-match', ], ], ], 'UpdateOriginEndpointRequestStartoverWindowSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1209600, 'min' => 60, ], 'UpdateOriginEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelGroupName', 'ChannelName', 'OriginEndpointName', 'ContainerType', 'Segment', 'CreatedAt', 'ModifiedAt', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'ChannelGroupName' => [ 'shape' => 'ResourceName', ], 'ChannelName' => [ 'shape' => 'ResourceName', ], 'OriginEndpointName' => [ 'shape' => 'ResourceName', ], 'ContainerType' => [ 'shape' => 'ContainerType', ], 'Segment' => [ 'shape' => 'Segment', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'StartoverWindowSeconds' => [ 'shape' => 'Integer', ], 'HlsManifests' => [ 'shape' => 'GetHlsManifests', ], 'LowLatencyHlsManifests' => [ 'shape' => 'GetLowLatencyHlsManifests', ], 'ETag' => [ 'shape' => 'EntityTag', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ValidationExceptionType' => [ 'shape' => 'ValidationExceptionType', ], ], 'exception' => true, ], 'ValidationExceptionType' => [ 'type' => 'string', 'enum' => [ 'CONTAINER_TYPE_IMMUTABLE', 'INVALID_PAGINATION_TOKEN', 'INVALID_PAGINATION_MAX_RESULTS', 'INVALID_POLICY', 'INVALID_ROLE_ARN', 'MANIFEST_NAME_COLLISION', 'ENCRYPTION_METHOD_CONTAINER_TYPE_MISMATCH', 'CENC_IV_INCOMPATIBLE', 'ENCRYPTION_CONTRACT_WITHOUT_AUDIO_RENDITION_INCOMPATIBLE', 'ENCRYPTION_CONTRACT_UNENCRYPTED', 'ENCRYPTION_CONTRACT_SHARED', 'NUM_MANIFESTS_LOW', 'NUM_MANIFESTS_HIGH', 'DRM_SYSTEMS_ENCRYPTION_METHOD_INCOMPATIBLE', 'ROLE_ARN_NOT_ASSUMABLE', 'ROLE_ARN_LENGTH_OUT_OF_RANGE', 'ROLE_ARN_INVALID_FORMAT', 'URL_INVALID', 'URL_SCHEME', 'URL_USER_INFO', 'URL_PORT', 'URL_UNKNOWN_HOST', 'URL_LOCAL_ADDRESS', 'URL_LOOPBACK_ADDRESS', 'URL_LINK_LOCAL_ADDRESS', 'URL_MULTICAST_ADDRESS', 'MEMBER_INVALID', 'MEMBER_MISSING', 'MEMBER_MIN_VALUE', 'MEMBER_MAX_VALUE', 'MEMBER_MIN_LENGTH', 'MEMBER_MAX_LENGTH', 'MEMBER_INVALID_ENUM_VALUE', 'MEMBER_DOES_NOT_MATCH_PATTERN', 'INVALID_MANIFEST_FILTER', 'INVALID_TIME_DELAY_SECONDS', 'END_TIME_EARLIER_THAN_START_TIME', ], ], ],];
