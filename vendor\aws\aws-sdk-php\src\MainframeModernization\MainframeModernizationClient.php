<?php
namespace Aws\MainframeModernization;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWSMainframeModernization** service.
 * @method \Aws\Result cancelBatchJobExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelBatchJobExecutionAsync(array $args = [])
 * @method \Aws\Result createApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApplicationAsync(array $args = [])
 * @method \Aws\Result createDataSetImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataSetImportTaskAsync(array $args = [])
 * @method \Aws\Result createDeployment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDeploymentAsync(array $args = [])
 * @method \Aws\Result createEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEnvironmentAsync(array $args = [])
 * @method \Aws\Result deleteApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAsync(array $args = [])
 * @method \Aws\Result deleteApplicationFromEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationFromEnvironmentAsync(array $args = [])
 * @method \Aws\Result deleteEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEnvironmentAsync(array $args = [])
 * @method \Aws\Result getApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationAsync(array $args = [])
 * @method \Aws\Result getApplicationVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationVersionAsync(array $args = [])
 * @method \Aws\Result getBatchJobExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBatchJobExecutionAsync(array $args = [])
 * @method \Aws\Result getDataSetDetails(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataSetDetailsAsync(array $args = [])
 * @method \Aws\Result getDataSetImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataSetImportTaskAsync(array $args = [])
 * @method \Aws\Result getDeployment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDeploymentAsync(array $args = [])
 * @method \Aws\Result getEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEnvironmentAsync(array $args = [])
 * @method \Aws\Result getSignedBluinsightsUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSignedBluinsightsUrlAsync(array $args = [])
 * @method \Aws\Result listApplicationVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationVersionsAsync(array $args = [])
 * @method \Aws\Result listApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationsAsync(array $args = [])
 * @method \Aws\Result listBatchJobDefinitions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBatchJobDefinitionsAsync(array $args = [])
 * @method \Aws\Result listBatchJobExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBatchJobExecutionsAsync(array $args = [])
 * @method \Aws\Result listDataSetImportHistory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataSetImportHistoryAsync(array $args = [])
 * @method \Aws\Result listDataSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataSetsAsync(array $args = [])
 * @method \Aws\Result listDeployments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDeploymentsAsync(array $args = [])
 * @method \Aws\Result listEngineVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngineVersionsAsync(array $args = [])
 * @method \Aws\Result listEnvironments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEnvironmentsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startApplicationAsync(array $args = [])
 * @method \Aws\Result startBatchJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startBatchJobAsync(array $args = [])
 * @method \Aws\Result stopApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopApplicationAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApplicationAsync(array $args = [])
 * @method \Aws\Result updateEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEnvironmentAsync(array $args = [])
 */
class MainframeModernizationClient extends AwsClient {}
