<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitbd86f85d7e6a787e5254444613952b25
{
    public static $prefixLengthsPsr4 = array (
        'R' => 
        array (
            '<PERSON><PERSON>pay\\Tests\\' => 15,
            '<PERSON><PERSON>pay\\Api\\' => 13,
        ),
        'M' => 
        array (
            '<PERSON><PERSON><PERSON>\\Razorpay\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON><PERSON><PERSON>y\\Tests\\' => 
        array (
            0 => __DIR__ . '/..' . '/razorpay/razorpay/tests',
        ),
        'Razorpay\\Api\\' => 
        array (
            0 => __DIR__ . '/..' . '/razorpay/razorpay/src',
        ),
        'Modules\\Razorpay\\' => 
        array (
            0 => __DIR__ . '/../..' . '/',
        ),
    );

    public static $prefixesPsr0 = array (
        'R' => 
        array (
            'Requests' => 
            array (
                0 => __DIR__ . '/..' . '/rmccue/requests/library',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitbd86f85d7e6a787e5254444613952b25::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitbd86f85d7e6a787e5254444613952b25::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitbd86f85d7e6a787e5254444613952b25::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitbd86f85d7e6a787e5254444613952b25::$classMap;

        }, null, ClassLoader::class);
    }
}
