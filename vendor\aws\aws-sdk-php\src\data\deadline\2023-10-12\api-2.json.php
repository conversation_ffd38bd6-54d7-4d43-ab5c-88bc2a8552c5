<?php
// This file was auto-generated from sdk-root/src/data/deadline/2023-10-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-10-12', 'endpointPrefix' => 'deadline', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWSDeadlineCloud', 'serviceId' => 'deadline', 'signatureVersion' => 'v4', 'signingName' => 'deadline', 'uid' => 'deadline-2023-10-12', ], 'operations' => [ 'AssociateMemberToFarm' => [ 'name' => 'AssociateMemberToFarm', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToFarmRequest', ], 'output' => [ 'shape' => 'AssociateMemberToFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToFleet' => [ 'name' => 'AssociateMemberToFleet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToFleetRequest', ], 'output' => [ 'shape' => 'AssociateMemberToFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToJob' => [ 'name' => 'AssociateMemberToJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToJobRequest', ], 'output' => [ 'shape' => 'AssociateMemberToJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssociateMemberToQueue' => [ 'name' => 'AssociateMemberToQueue', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateMemberToQueueRequest', ], 'output' => [ 'shape' => 'AssociateMemberToQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'AssumeFleetRoleForRead' => [ 'name' => 'AssumeFleetRoleForRead', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/read-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeFleetRoleForReadRequest', ], 'output' => [ 'shape' => 'AssumeFleetRoleForReadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeFleetRoleForWorker' => [ 'name' => 'AssumeFleetRoleForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/fleet-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeFleetRoleForWorkerRequest', ], 'output' => [ 'shape' => 'AssumeFleetRoleForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'AssumeQueueRoleForRead' => [ 'name' => 'AssumeQueueRoleForRead', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/read-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForReadRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForReadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeQueueRoleForUser' => [ 'name' => 'AssumeQueueRoleForUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/user-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForUserRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'AssumeQueueRoleForWorker' => [ 'name' => 'AssumeQueueRoleForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/queue-roles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssumeQueueRoleForWorkerRequest', ], 'output' => [ 'shape' => 'AssumeQueueRoleForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'BatchGetJobEntity' => [ 'name' => 'BatchGetJobEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/batchGetJobEntity', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetJobEntityRequest', ], 'output' => [ 'shape' => 'BatchGetJobEntityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], ], 'CopyJobTemplate' => [ 'name' => 'CopyJobTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CopyJobTemplateRequest', ], 'output' => [ 'shape' => 'CopyJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'CreateBudget' => [ 'name' => 'CreateBudget', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBudgetRequest', ], 'output' => [ 'shape' => 'CreateBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateFarm' => [ 'name' => 'CreateFarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFarmRequest', ], 'output' => [ 'shape' => 'CreateFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFleetRequest', ], 'output' => [ 'shape' => 'CreateFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateLicenseEndpoint' => [ 'name' => 'CreateLicenseEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/license-endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLicenseEndpointRequest', ], 'output' => [ 'shape' => 'CreateLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateMonitor' => [ 'name' => 'CreateMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMonitorRequest', ], 'output' => [ 'shape' => 'CreateMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueueEnvironment' => [ 'name' => 'CreateQueueEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateQueueFleetAssociation' => [ 'name' => 'CreateQueueFleetAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'CreateQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateStorageProfile' => [ 'name' => 'CreateStorageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStorageProfileRequest', ], 'output' => [ 'shape' => 'CreateStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'CreateWorker' => [ 'name' => 'CreateWorker', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkerRequest', ], 'output' => [ 'shape' => 'CreateWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], 'DeleteBudget' => [ 'name' => 'DeleteBudget', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBudgetRequest', ], 'output' => [ 'shape' => 'DeleteBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteFarm' => [ 'name' => 'DeleteFarm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFarmRequest', ], 'output' => [ 'shape' => 'DeleteFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFleetRequest', ], 'output' => [ 'shape' => 'DeleteFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteLicenseEndpoint' => [ 'name' => 'DeleteLicenseEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLicenseEndpointRequest', ], 'output' => [ 'shape' => 'DeleteLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteMeteredProduct' => [ 'name' => 'DeleteMeteredProduct', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products/{productId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMeteredProductRequest', ], 'output' => [ 'shape' => 'DeleteMeteredProductResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteMonitor' => [ 'name' => 'DeleteMonitor', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMonitorRequest', ], 'output' => [ 'shape' => 'DeleteMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueue' => [ 'name' => 'DeleteQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueRequest', ], 'output' => [ 'shape' => 'DeleteQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueueEnvironment' => [ 'name' => 'DeleteQueueEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteQueueFleetAssociation' => [ 'name' => 'DeleteQueueFleetAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'DeleteQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteStorageProfile' => [ 'name' => 'DeleteStorageProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStorageProfileRequest', ], 'output' => [ 'shape' => 'DeleteStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DeleteWorker' => [ 'name' => 'DeleteWorker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkerRequest', ], 'output' => [ 'shape' => 'DeleteWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromFarm' => [ 'name' => 'DisassociateMemberFromFarm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromFarmRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromFleet' => [ 'name' => 'DisassociateMemberFromFleet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromFleetRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromJob' => [ 'name' => 'DisassociateMemberFromJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromJobRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'DisassociateMemberFromQueue' => [ 'name' => 'DisassociateMemberFromQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMemberFromQueueRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'GetBudget' => [ 'name' => 'GetBudget', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBudgetRequest', ], 'output' => [ 'shape' => 'GetBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetFarm' => [ 'name' => 'GetFarm', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFarmRequest', ], 'output' => [ 'shape' => 'GetFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetFleet' => [ 'name' => 'GetFleet', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFleetRequest', ], 'output' => [ 'shape' => 'GetFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetLicenseEndpoint' => [ 'name' => 'GetLicenseEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLicenseEndpointRequest', ], 'output' => [ 'shape' => 'GetLicenseEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetMonitor' => [ 'name' => 'GetMonitor', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMonitorRequest', ], 'output' => [ 'shape' => 'GetMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueue' => [ 'name' => 'GetQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueRequest', ], 'output' => [ 'shape' => 'GetQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueueEnvironment' => [ 'name' => 'GetQueueEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'GetQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetQueueFleetAssociation' => [ 'name' => 'GetQueueFleetAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'GetQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSessionAction' => [ 'name' => 'GetSessionAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/session-actions/{sessionActionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionActionRequest', ], 'output' => [ 'shape' => 'GetSessionActionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetSessionsStatisticsAggregation' => [ 'name' => 'GetSessionsStatisticsAggregation', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/sessions-statistics-aggregation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionsStatisticsAggregationRequest', ], 'output' => [ 'shape' => 'GetSessionsStatisticsAggregationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStep' => [ 'name' => 'GetStep', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStepRequest', ], 'output' => [ 'shape' => 'GetStepResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStorageProfile' => [ 'name' => 'GetStorageProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageProfileRequest', ], 'output' => [ 'shape' => 'GetStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetStorageProfileForQueue' => [ 'name' => 'GetStorageProfileForQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageProfileForQueueRequest', ], 'output' => [ 'shape' => 'GetStorageProfileForQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetTask' => [ 'name' => 'GetTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTaskRequest', ], 'output' => [ 'shape' => 'GetTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'GetWorker' => [ 'name' => 'GetWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkerRequest', ], 'output' => [ 'shape' => 'GetWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListAvailableMeteredProducts' => [ 'name' => 'ListAvailableMeteredProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/metered-products', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAvailableMeteredProductsRequest', ], 'output' => [ 'shape' => 'ListAvailableMeteredProductsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListBudgets' => [ 'name' => 'ListBudgets', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBudgetsRequest', ], 'output' => [ 'shape' => 'ListBudgetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFarmMembers' => [ 'name' => 'ListFarmMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFarmMembersRequest', ], 'output' => [ 'shape' => 'ListFarmMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFarms' => [ 'name' => 'ListFarms', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFarmsRequest', ], 'output' => [ 'shape' => 'ListFarmsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFleetMembers' => [ 'name' => 'ListFleetMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFleetMembersRequest', ], 'output' => [ 'shape' => 'ListFleetMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFleetsRequest', ], 'output' => [ 'shape' => 'ListFleetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListJobMembers' => [ 'name' => 'ListJobMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobMembersRequest', ], 'output' => [ 'shape' => 'ListJobMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListLicenseEndpoints' => [ 'name' => 'ListLicenseEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLicenseEndpointsRequest', ], 'output' => [ 'shape' => 'ListLicenseEndpointsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListMeteredProducts' => [ 'name' => 'ListMeteredProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMeteredProductsRequest', ], 'output' => [ 'shape' => 'ListMeteredProductsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListMonitors' => [ 'name' => 'ListMonitors', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMonitorsRequest', ], 'output' => [ 'shape' => 'ListMonitorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueEnvironments' => [ 'name' => 'ListQueueEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListQueueEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueFleetAssociations' => [ 'name' => 'ListQueueFleetAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueFleetAssociationsRequest', ], 'output' => [ 'shape' => 'ListQueueFleetAssociationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueueMembers' => [ 'name' => 'ListQueueMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/members', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueueMembersRequest', ], 'output' => [ 'shape' => 'ListQueueMembersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessionActions' => [ 'name' => 'ListSessionActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/session-actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionActionsRequest', ], 'output' => [ 'shape' => 'ListSessionActionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessions' => [ 'name' => 'ListSessions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsRequest', ], 'output' => [ 'shape' => 'ListSessionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSessionsForWorker' => [ 'name' => 'ListSessionsForWorker', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsForWorkerRequest', ], 'output' => [ 'shape' => 'ListSessionsForWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStepConsumers' => [ 'name' => 'ListStepConsumers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepConsumersRequest', ], 'output' => [ 'shape' => 'ListStepConsumersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStepDependencies' => [ 'name' => 'ListStepDependencies', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/dependencies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepDependenciesRequest', ], 'output' => [ 'shape' => 'ListStepDependenciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListSteps' => [ 'name' => 'ListSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStepsRequest', ], 'output' => [ 'shape' => 'ListStepsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStorageProfiles' => [ 'name' => 'ListStorageProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageProfilesRequest', ], 'output' => [ 'shape' => 'ListStorageProfilesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListStorageProfilesForQueue' => [ 'name' => 'ListStorageProfilesForQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/storage-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageProfilesForQueueRequest', ], 'output' => [ 'shape' => 'ListStorageProfilesForQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListTasks' => [ 'name' => 'ListTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTasksRequest', ], 'output' => [ 'shape' => 'ListTasksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'ListWorkers' => [ 'name' => 'ListWorkers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkersRequest', ], 'output' => [ 'shape' => 'ListWorkersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'PutMeteredProduct' => [ 'name' => 'PutMeteredProduct', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2023-10-12/license-endpoints/{licenseEndpointId}/metered-products/{productId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutMeteredProductRequest', ], 'output' => [ 'shape' => 'PutMeteredProductResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'SearchJobs' => [ 'name' => 'SearchJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchJobsRequest', ], 'output' => [ 'shape' => 'SearchJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchSteps' => [ 'name' => 'SearchSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchStepsRequest', ], 'output' => [ 'shape' => 'SearchStepsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchTasks' => [ 'name' => 'SearchTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchTasksRequest', ], 'output' => [ 'shape' => 'SearchTasksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'SearchWorkers' => [ 'name' => 'SearchWorkers', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/search/workers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchWorkersRequest', ], 'output' => [ 'shape' => 'SearchWorkersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'StartSessionsStatisticsAggregation' => [ 'name' => 'StartSessionsStatisticsAggregation', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/farms/{farmId}/sessions-statistics-aggregation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartSessionsStatisticsAggregationRequest', ], 'output' => [ 'shape' => 'StartSessionsStatisticsAggregationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2023-10-12/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateBudget' => [ 'name' => 'UpdateBudget', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/budgets/{budgetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBudgetRequest', ], 'output' => [ 'shape' => 'UpdateBudgetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateFarm' => [ 'name' => 'UpdateFarm', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFarmRequest', ], 'output' => [ 'shape' => 'UpdateFarmResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateFleet' => [ 'name' => 'UpdateFleet', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFleetRequest', ], 'output' => [ 'shape' => 'UpdateFleetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'output' => [ 'shape' => 'UpdateJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateMonitor' => [ 'name' => 'UpdateMonitor', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/monitors/{monitorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMonitorRequest', ], 'output' => [ 'shape' => 'UpdateMonitorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateQueue' => [ 'name' => 'UpdateQueue', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueRequest', ], 'output' => [ 'shape' => 'UpdateQueueResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateQueueEnvironment' => [ 'name' => 'UpdateQueueEnvironment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/environments/{queueEnvironmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateQueueEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], ], 'UpdateQueueFleetAssociation' => [ 'name' => 'UpdateQueueFleetAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queue-fleet-associations/{queueId}/{fleetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueFleetAssociationRequest', ], 'output' => [ 'shape' => 'UpdateQueueFleetAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateSession' => [ 'name' => 'UpdateSession', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionRequest', ], 'output' => [ 'shape' => 'UpdateSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateStep' => [ 'name' => 'UpdateStep', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStepRequest', ], 'output' => [ 'shape' => 'UpdateStepResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateStorageProfile' => [ 'name' => 'UpdateStorageProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/storage-profiles/{storageProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStorageProfileRequest', ], 'output' => [ 'shape' => 'UpdateStorageProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateTask' => [ 'name' => 'UpdateTask', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/queues/{queueId}/jobs/{jobId}/steps/{stepId}/tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTaskRequest', ], 'output' => [ 'shape' => 'UpdateTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'management.', ], 'idempotent' => true, ], 'UpdateWorker' => [ 'name' => 'UpdateWorker', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerRequest', ], 'output' => [ 'shape' => 'UpdateWorkerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], 'UpdateWorkerSchedule' => [ 'name' => 'UpdateWorkerSchedule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2023-10-12/farms/{farmId}/fleets/{fleetId}/workers/{workerId}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkerScheduleRequest', ], 'output' => [ 'shape' => 'UpdateWorkerScheduleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'scheduling.', ], 'idempotent' => true, ], ], 'shapes' => [ 'AcceleratorCountRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'max' => [ 'shape' => 'MinZeroMaxInteger', ], 'min' => [ 'shape' => 'MinZeroMaxInteger', ], ], ], 'AcceleratorTotalMemoryMiBRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'max' => [ 'shape' => 'MinZeroMaxInteger', ], 'min' => [ 'shape' => 'MinZeroMaxInteger', ], ], ], 'AcceleratorType' => [ 'type' => 'string', 'enum' => [ 'gpu', ], ], 'AcceleratorTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceleratorType', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessKeyId' => [ 'type' => 'string', 'sensitive' => true, ], 'AggregationId' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{32}$', ], 'AllowedStorageProfileIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageProfileId', ], 'max' => 20, 'min' => 0, ], 'AmountCapabilityName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([a-zA-Z][a-zA-Z0-9]{0,63}:)?amount(\\.[a-zA-Z][a-zA-Z0-9]{0,63})+$', ], 'AssignedEnvironmentEnterSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'AssignedEnvironmentExitSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'AssignedSession' => [ 'type' => 'structure', 'required' => [ 'jobId', 'logConfiguration', 'queueId', 'sessionActions', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'logConfiguration' => [ 'shape' => 'LogConfiguration', ], 'queueId' => [ 'shape' => 'QueueId', ], 'sessionActions' => [ 'shape' => 'AssignedSessionActions', ], ], ], 'AssignedSessionAction' => [ 'type' => 'structure', 'required' => [ 'definition', 'sessionActionId', ], 'members' => [ 'definition' => [ 'shape' => 'AssignedSessionActionDefinition', ], 'sessionActionId' => [ 'shape' => 'SessionActionId', ], ], ], 'AssignedSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'AssignedEnvironmentEnterSessionActionDefinition', ], 'envExit' => [ 'shape' => 'AssignedEnvironmentExitSessionActionDefinition', ], 'syncInputJobAttachments' => [ 'shape' => 'AssignedSyncInputJobAttachmentsSessionActionDefinition', ], 'taskRun' => [ 'shape' => 'AssignedTaskRunSessionActionDefinition', ], ], 'union' => true, ], 'AssignedSessionActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssignedSessionAction', ], ], 'AssignedSessions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionId', ], 'value' => [ 'shape' => 'AssignedSession', ], ], 'AssignedSyncInputJobAttachmentsSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'AssignedTaskRunSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'parameters', 'stepId', 'taskId', ], 'members' => [ 'parameters' => [ 'shape' => 'TaskParameters', ], 'stepId' => [ 'shape' => 'StepId', ], 'taskId' => [ 'shape' => 'TaskId', ], ], ], 'AssociateMemberToFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], ], ], 'AssociateMemberToFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], ], ], 'AssociateMemberToFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'jobId', 'membershipLevel', 'principalId', 'principalType', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssociateMemberToJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssociateMemberToQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssumeFleetRoleForReadRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'AssumeFleetRoleForReadResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeFleetRoleForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'AssumeFleetRoleForWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForReadRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssumeQueueRoleForReadResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForUserRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'AssumeQueueRoleForUserResponse' => [ 'type' => 'structure', 'required' => [ 'credentials', ], 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'AssumeQueueRoleForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'queueId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'querystring', 'locationName' => 'queueId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'AssumeQueueRoleForWorkerResponse' => [ 'type' => 'structure', 'members' => [ 'credentials' => [ 'shape' => 'AwsCredentials', ], ], 'sensitive' => true, ], 'Attachments' => [ 'type' => 'structure', 'required' => [ 'manifests', ], 'members' => [ 'fileSystem' => [ 'shape' => 'JobAttachmentsFileSystem', ], 'manifests' => [ 'shape' => 'ManifestPropertiesList', ], ], ], 'AttributeCapabilityName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([a-zA-Z][a-zA-Z0-9]{0,63}:)?attr(\\.[a-zA-Z][a-zA-Z0-9]{0,63})+$', ], 'AttributeCapabilityValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z_]([a-zA-Z0-9_\\-]{0,99})$', ], 'AttributeCapabilityValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeCapabilityValue', ], 'max' => 10, 'min' => 1, ], 'AutoScalingMode' => [ 'type' => 'string', 'enum' => [ 'NO_SCALING', 'EVENT_BASED_AUTO_SCALING', ], ], 'AutoScalingStatus' => [ 'type' => 'string', 'enum' => [ 'GROWING', 'STEADY', 'SHRINKING', ], ], 'AwsCredentials' => [ 'type' => 'structure', 'required' => [ 'accessKeyId', 'expiration', 'secretAccessKey', 'sessionToken', ], 'members' => [ 'accessKeyId' => [ 'shape' => 'AccessKeyId', ], 'expiration' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'secretAccessKey' => [ 'shape' => 'SecretAccessKey', ], 'sessionToken' => [ 'shape' => 'SessionToken', ], ], 'sensitive' => true, ], 'BatchGetJobEntityErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetJobEntityError', ], ], 'BatchGetJobEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEntity', ], 'max' => 25, 'min' => 0, ], 'BatchGetJobEntityRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'identifiers', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'identifiers' => [ 'shape' => 'JobEntityIdentifiers', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'BatchGetJobEntityResponse' => [ 'type' => 'structure', 'required' => [ 'entities', 'errors', ], 'members' => [ 'entities' => [ 'shape' => 'BatchGetJobEntityList', ], 'errors' => [ 'shape' => 'BatchGetJobEntityErrors', ], ], ], 'BoundedString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'BudgetActionToAdd' => [ 'type' => 'structure', 'required' => [ 'thresholdPercentage', 'type', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], 'type' => [ 'shape' => 'BudgetActionType', ], ], ], 'BudgetActionToRemove' => [ 'type' => 'structure', 'required' => [ 'thresholdPercentage', 'type', ], 'members' => [ 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], 'type' => [ 'shape' => 'BudgetActionType', ], ], ], 'BudgetActionType' => [ 'type' => 'string', 'enum' => [ 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'BudgetActionsToAdd' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetActionToAdd', ], 'max' => 10, 'min' => 0, ], 'BudgetActionsToRemove' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetActionToRemove', ], 'max' => 10, 'min' => 0, ], 'BudgetId' => [ 'type' => 'string', 'pattern' => '^budget-[0-9a-f]{32}$', ], 'BudgetSchedule' => [ 'type' => 'structure', 'members' => [ 'fixed' => [ 'shape' => 'FixedBudgetSchedule', ], ], 'union' => true, ], 'BudgetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'BudgetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BudgetSummary', ], ], 'BudgetSummary' => [ 'type' => 'structure', 'required' => [ 'approximateDollarLimit', 'budgetId', 'createdAt', 'createdBy', 'displayName', 'status', 'usageTrackingResource', 'usages', ], 'members' => [ 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'budgetId' => [ 'shape' => 'BudgetId', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'status' => [ 'shape' => 'BudgetStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], 'usages' => [ 'shape' => 'ConsumedUsages', ], ], ], 'CancelSessionActions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionId', ], 'value' => [ 'shape' => 'SessionActionIdList', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CombinationExpression' => [ 'type' => 'string', 'max' => 1280, 'min' => 1, ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQUAL', 'NOT_EQUAL', 'GREATER_THAN_EQUAL_TO', 'GREATER_THAN', 'LESS_THAN_EQUAL_TO', 'LESS_THAN', ], ], 'CompletedStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'INTERRUPTED', 'CANCELED', 'NEVER_ATTEMPTED', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', 'resourceId', 'resourceType', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ConflictExceptionReason', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CONFLICT_EXCEPTION', 'CONCURRENT_MODIFICATION', 'RESOURCE_ALREADY_EXISTS', 'RESOURCE_IN_USE', 'STATUS_CONFLICT', ], ], 'ConsumedUsageLimit' => [ 'type' => 'float', 'box' => true, 'min' => 0.01, ], 'ConsumedUsages' => [ 'type' => 'structure', 'required' => [ 'approximateDollarUsage', ], 'members' => [ 'approximateDollarUsage' => [ 'shape' => 'Float', ], ], ], 'CopyJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'targetS3Location', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'targetS3Location' => [ 'shape' => 'S3Location', ], ], ], 'CopyJobTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'templateType', ], 'members' => [ 'templateType' => [ 'shape' => 'JobTemplateType', ], ], ], 'CpuArchitectureType' => [ 'type' => 'string', 'enum' => [ 'x86_64', 'arm64', ], ], 'CreateBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'actions', 'approximateDollarLimit', 'displayName', 'farmId', 'schedule', 'usageTrackingResource', ], 'members' => [ 'actions' => [ 'shape' => 'BudgetActionsToAdd', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], ], ], 'CreateBudgetResponse' => [ 'type' => 'structure', 'required' => [ 'budgetId', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', ], ], ], 'CreateFarmRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateFarmResponse' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], ], ], 'CreateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'configuration', 'displayName', 'farmId', 'maxWorkerCount', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateFleetResponse' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'FleetId', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'priority', 'queueId', 'template', 'templateType', ], 'members' => [ 'attachments' => [ 'shape' => 'Attachments', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'priority' => [ 'shape' => 'JobPriority', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'targetTaskRunStatus' => [ 'shape' => 'CreateJobTargetTaskRunStatus', ], 'template' => [ 'shape' => 'JobTemplate', ], 'templateType' => [ 'shape' => 'JobTemplateType', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'CreateJobTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'SUSPENDED', ], ], 'CreateLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'subnetIds', 'vpcId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'securityGroupIds' => [ 'shape' => 'CreateLicenseEndpointRequestSecurityGroupIdsList', ], 'subnetIds' => [ 'shape' => 'CreateLicenseEndpointRequestSubnetIdsList', ], 'tags' => [ 'shape' => 'Tags', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'CreateLicenseEndpointRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'CreateLicenseEndpointRequestSubnetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 10, 'min' => 1, ], 'CreateLicenseEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], ], ], 'CreateMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', 'identityCenterInstanceArn', 'roleArn', 'subdomain', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'subdomain' => [ 'shape' => 'Subdomain', ], ], ], 'CreateMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'identityCenterApplicationArn', 'monitorId', ], 'members' => [ 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'monitorId' => [ 'shape' => 'MonitorId', ], ], ], 'CreateQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'priority', 'queueId', 'template', 'templateType', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'priority' => [ 'shape' => 'Priority', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], ], ], 'CreateQueueEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'queueEnvironmentId', ], 'members' => [ 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], ], ], 'CreateQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'queueId' => [ 'shape' => 'QueueId', ], ], ], 'CreateQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', 'farmId', ], 'members' => [ 'allowedStorageProfileIds' => [ 'shape' => 'AllowedStorageProfileIds', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'requiredFileSystemLocationNames' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateQueueResponse' => [ 'type' => 'structure', 'required' => [ 'queueId', ], 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], ], ], 'CreateStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', 'farmId', 'osFamily', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], ], ], 'CreateStorageProfileResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfileId', ], 'members' => [ 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], ], ], 'CreateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesRequest', ], ], ], 'CreateWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'workerId', ], 'members' => [ 'workerId' => [ 'shape' => 'WorkerId', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'CreatedBy' => [ 'type' => 'string', ], 'CustomFleetAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAmountCapability', ], 'max' => 15, 'min' => 1, ], 'CustomFleetAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttributeCapability', ], 'max' => 15, 'min' => 1, ], 'CustomerManagedFleetConfiguration' => [ 'type' => 'structure', 'required' => [ 'mode', 'workerCapabilities', ], 'members' => [ 'mode' => [ 'shape' => 'AutoScalingMode', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'workerCapabilities' => [ 'shape' => 'CustomerManagedWorkerCapabilities', ], ], ], 'CustomerManagedFleetOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'MACOS', ], ], 'CustomerManagedWorkerCapabilities' => [ 'type' => 'structure', 'required' => [ 'cpuArchitectureType', 'memoryMiB', 'osFamily', 'vCpuCount', ], 'members' => [ 'acceleratorCount' => [ 'shape' => 'AcceleratorCountRange', ], 'acceleratorTotalMemoryMiB' => [ 'shape' => 'AcceleratorTotalMemoryMiBRange', ], 'acceleratorTypes' => [ 'shape' => 'AcceleratorTypes', ], 'cpuArchitectureType' => [ 'shape' => 'CpuArchitectureType', ], 'customAmounts' => [ 'shape' => 'CustomFleetAmountCapabilities', ], 'customAttributes' => [ 'shape' => 'CustomFleetAttributeCapabilities', ], 'memoryMiB' => [ 'shape' => 'MemoryMiBRange', ], 'osFamily' => [ 'shape' => 'CustomerManagedFleetOperatingSystemFamily', ], 'vCpuCount' => [ 'shape' => 'VCpuCountRange', ], ], ], 'DateTimeFilterExpression' => [ 'type' => 'structure', 'required' => [ 'dateTime', 'name', 'operator', ], 'members' => [ 'dateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], ], ], 'DefaultQueueBudgetAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'DeleteBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'budgetId', 'farmId', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'DeleteBudgetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'DeleteFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'DeleteFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], ], ], 'DeleteLicenseEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMeteredProductRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'productId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'productId' => [ 'shape' => 'MeteredProductId', 'location' => 'uri', 'locationName' => 'productId', ], ], ], 'DeleteMeteredProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], ], ], 'DeleteMonitorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueEnvironmentId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DeleteQueueEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DeleteQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DeleteQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'DeleteStorageProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'DeleteWorkerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DependenciesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepId', ], ], 'DependencyConsumerResolutionStatus' => [ 'type' => 'string', 'enum' => [ 'RESOLVED', 'UNRESOLVED', ], ], 'DependencyCounts' => [ 'type' => 'structure', 'required' => [ 'consumersResolved', 'consumersUnresolved', 'dependenciesResolved', 'dependenciesUnresolved', ], 'members' => [ 'consumersResolved' => [ 'shape' => 'Integer', ], 'consumersUnresolved' => [ 'shape' => 'Integer', ], 'dependenciesResolved' => [ 'shape' => 'Integer', ], 'dependenciesUnresolved' => [ 'shape' => 'Integer', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'DesiredWorkerStatus' => [ 'type' => 'string', 'enum' => [ 'STOPPED', ], ], 'DisassociateMemberFromFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'principalId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], ], ], 'DisassociateMemberFromFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'principalId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DisassociateMemberFromJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'principalId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'uri', 'locationName' => 'principalId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'DisassociateMemberFromQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'DnsName' => [ 'type' => 'string', 'pattern' => '^vpce-[\\w]+-[\\w]+.vpce-svc-[\\w]+.*.vpce.amazonaws.com$', ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EbsIops' => [ 'type' => 'integer', 'box' => true, 'max' => 16000, 'min' => 3000, ], 'EbsThroughputMiB' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 125, ], 'Ec2EbsVolume' => [ 'type' => 'structure', 'members' => [ 'iops' => [ 'shape' => 'EbsIops', ], 'sizeGiB' => [ 'shape' => 'Integer', ], 'throughputMiB' => [ 'shape' => 'EbsThroughputMiB', ], ], ], 'Ec2MarketType' => [ 'type' => 'string', 'enum' => [ 'on-demand', 'spot', ], ], 'EndedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'EndsAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'EnvironmentDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'jobId', 'schemaVersion', 'template', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'jobId' => [ 'shape' => 'JobId', ], 'schemaVersion' => [ 'shape' => 'String', ], 'template' => [ 'shape' => 'Document', ], ], ], 'EnvironmentDetailsError' => [ 'type' => 'structure', 'required' => [ 'code', 'environmentId', 'jobId', 'message', ], 'members' => [ 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'jobId' => [ 'shape' => 'JobId', ], 'message' => [ 'shape' => 'String', ], ], ], 'EnvironmentDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'jobId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'jobId' => [ 'shape' => 'JobId', ], ], ], 'EnvironmentEnterSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentEnterSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentExitSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentExitSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'EnvironmentId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(STEP:step-[0-9a-f]{32}:.*)|(JOB:job-[0-9a-f]{32}:.*)$', ], 'EnvironmentName' => [ 'type' => 'string', ], 'EnvironmentTemplate' => [ 'type' => 'string', 'max' => 15000, 'min' => 1, 'sensitive' => true, ], 'EnvironmentTemplateType' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'ExceptionContext' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FarmId' => [ 'type' => 'string', 'pattern' => '^farm-[0-9a-f]{32}$', ], 'FarmMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], ], ], 'FarmMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'FarmMember', ], ], 'FarmSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FarmSummary', ], ], 'FarmSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'displayName', 'farmId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'FieldSortExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'sortOrder', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'FileSystemLocation' => [ 'type' => 'structure', 'required' => [ 'name', 'path', 'type', ], 'members' => [ 'name' => [ 'shape' => 'FileSystemLocationName', ], 'path' => [ 'shape' => 'PathString', ], 'type' => [ 'shape' => 'FileSystemLocationType', ], ], 'sensitive' => true, ], 'FileSystemLocationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9A-Za-z ]*$', 'sensitive' => true, ], 'FileSystemLocationType' => [ 'type' => 'string', 'enum' => [ 'SHARED', 'LOCAL', ], ], 'FileSystemLocationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemLocation', ], 'max' => 20, 'min' => 0, ], 'FixedBudgetSchedule' => [ 'type' => 'structure', 'required' => [ 'endTime', 'startTime', ], 'members' => [ 'endTime' => [ 'shape' => 'EndsAt', ], 'startTime' => [ 'shape' => 'StartsAt', ], ], ], 'FleetAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAmountCapability', ], 'max' => 25, 'min' => 1, ], 'FleetAmountCapability' => [ 'type' => 'structure', 'required' => [ 'min', 'name', ], 'members' => [ 'max' => [ 'shape' => 'Float', ], 'min' => [ 'shape' => 'Float', ], 'name' => [ 'shape' => 'AmountCapabilityName', ], ], ], 'FleetAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttributeCapability', ], 'max' => 25, 'min' => 1, ], 'FleetAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AttributeCapabilityName', ], 'values' => [ 'shape' => 'AttributeCapabilityValuesList', ], ], ], 'FleetCapabilities' => [ 'type' => 'structure', 'members' => [ 'amounts' => [ 'shape' => 'FleetAmountCapabilities', ], 'attributes' => [ 'shape' => 'FleetAttributeCapabilities', ], ], ], 'FleetConfiguration' => [ 'type' => 'structure', 'members' => [ 'customerManaged' => [ 'shape' => 'CustomerManagedFleetConfiguration', ], 'serviceManagedEc2' => [ 'shape' => 'ServiceManagedEc2FleetConfiguration', ], ], 'union' => true, ], 'FleetId' => [ 'type' => 'string', 'pattern' => '^fleet-[0-9a-f]{32}$', ], 'FleetMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], ], ], 'FleetMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetMember', ], ], 'FleetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'FleetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetSummary', ], ], 'FleetSummary' => [ 'type' => 'structure', 'required' => [ 'configuration', 'createdAt', 'createdBy', 'displayName', 'farmId', 'fleetId', 'maxWorkerCount', 'minWorkerCount', 'status', 'workerCount', ], 'members' => [ 'autoScalingStatus' => [ 'shape' => 'AutoScalingStatus', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'status' => [ 'shape' => 'FleetStatus', ], 'targetWorkerCount' => [ 'shape' => 'Integer', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerCount' => [ 'shape' => 'Integer', ], ], ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'FloatString' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '^[-]?(0|[1-9][0-9]*)([.][0-9]+)?([eE][+-]?[0-9]+)?$', ], 'GetBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'budgetId', 'farmId', ], 'members' => [ 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'GetBudgetResponse' => [ 'type' => 'structure', 'required' => [ 'actions', 'approximateDollarLimit', 'budgetId', 'createdAt', 'createdBy', 'displayName', 'schedule', 'status', 'usageTrackingResource', 'usages', ], 'members' => [ 'actions' => [ 'shape' => 'ResponseBudgetActionList', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'budgetId' => [ 'shape' => 'BudgetId', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'queueStoppedAt' => [ 'shape' => 'UpdatedAt', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], 'status' => [ 'shape' => 'BudgetStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'usageTrackingResource' => [ 'shape' => 'UsageTrackingResource', ], 'usages' => [ 'shape' => 'ConsumedUsages', ], ], ], 'GetFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'GetFarmResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'displayName', 'farmId', 'kmsKeyArn', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], ], ], 'GetFleetResponse' => [ 'type' => 'structure', 'required' => [ 'configuration', 'createdAt', 'createdBy', 'displayName', 'farmId', 'fleetId', 'maxWorkerCount', 'minWorkerCount', 'roleArn', 'status', 'workerCount', ], 'members' => [ 'autoScalingStatus' => [ 'shape' => 'AutoScalingStatus', ], 'capabilities' => [ 'shape' => 'FleetCapabilities', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'status' => [ 'shape' => 'FleetStatus', ], 'targetWorkerCount' => [ 'shape' => 'Integer', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerCount' => [ 'shape' => 'Integer', ], ], ], 'GetJobEntityError' => [ 'type' => 'structure', 'members' => [ 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsError', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsError', ], 'jobDetails' => [ 'shape' => 'JobDetailsError', ], 'stepDetails' => [ 'shape' => 'StepDetailsError', ], ], 'union' => true, ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'jobId', 'lifecycleStatus', 'lifecycleStatusMessage', 'name', 'priority', ], 'members' => [ 'attachments' => [ 'shape' => 'Attachments', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'description' => [ 'shape' => 'JobDescription', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'jobId' => [ 'shape' => 'JobId', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'name' => [ 'shape' => 'JobName', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'priority' => [ 'shape' => 'JobPriority', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetLicenseEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], ], ], 'GetLicenseEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'status', 'statusMessage', ], 'members' => [ 'dnsName' => [ 'shape' => 'DnsName', ], 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], 'securityGroupIds' => [ 'shape' => 'GetLicenseEndpointResponseSecurityGroupIdsList', ], 'status' => [ 'shape' => 'LicenseEndpointStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'subnetIds' => [ 'shape' => 'GetLicenseEndpointResponseSubnetIdsList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'GetLicenseEndpointResponseSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'GetLicenseEndpointResponseSubnetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 10, 'min' => 1, ], 'GetMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], ], ], 'GetMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'displayName', 'identityCenterApplicationArn', 'identityCenterInstanceArn', 'monitorId', 'roleArn', 'subdomain', 'url', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'monitorId' => [ 'shape' => 'MonitorId', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'url' => [ 'shape' => 'Url', ], ], ], 'GetQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueEnvironmentId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetQueueEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'name', 'priority', 'queueEnvironmentId', 'template', 'templateType', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'priority' => [ 'shape' => 'Priority', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetQueueFleetAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'fleetId', 'queueId', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'status' => [ 'shape' => 'QueueFleetAssociationStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'GetQueueResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'defaultBudgetAction', 'displayName', 'farmId', 'queueId', 'status', ], 'members' => [ 'allowedStorageProfileIds' => [ 'shape' => 'AllowedStorageProfileIds', ], 'blockedReason' => [ 'shape' => 'QueueBlockedReason', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'queueId' => [ 'shape' => 'QueueId', ], 'requiredFileSystemLocationNames' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'status' => [ 'shape' => 'QueueStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetSessionActionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'sessionActionId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'sessionActionId' => [ 'shape' => 'SessionActionId', 'location' => 'uri', 'locationName' => 'sessionActionId', ], ], ], 'GetSessionActionResponse' => [ 'type' => 'structure', 'required' => [ 'definition', 'sessionActionId', 'sessionId', 'status', ], 'members' => [ 'definition' => [ 'shape' => 'SessionActionDefinition', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'processExitCode' => [ 'shape' => 'ProcessExitCode', ], 'progressMessage' => [ 'shape' => 'SessionActionProgressMessage', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], 'sessionActionId' => [ 'shape' => 'SessionActionId', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'status' => [ 'shape' => 'SessionActionStatus', ], 'workerUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'sessionId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'required' => [ 'fleetId', 'lifecycleStatus', 'log', 'sessionId', 'startedAt', 'workerId', ], 'members' => [ 'endedAt' => [ 'shape' => 'EndedAt', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerId' => [ 'shape' => 'WorkerId', ], 'workerLog' => [ 'shape' => 'LogConfiguration', ], ], ], 'GetSessionsStatisticsAggregationRequest' => [ 'type' => 'structure', 'required' => [ 'aggregationId', 'farmId', ], 'members' => [ 'aggregationId' => [ 'shape' => 'AggregationId', 'location' => 'querystring', 'locationName' => 'aggregationId', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetSessionsStatisticsAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'statistics' => [ 'shape' => 'StatisticsList', ], 'status' => [ 'shape' => 'SessionsStatisticsAggregationStatus', ], 'statusMessage' => [ 'shape' => 'String', ], ], ], 'GetStepRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], ], ], 'GetStepResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'lifecycleStatus', 'name', 'stepId', 'taskRunStatus', 'taskRunStatusCounts', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'dependencyCounts' => [ 'shape' => 'DependencyCounts', ], 'description' => [ 'shape' => 'StepDescription', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'StepName', ], 'parameterSpace' => [ 'shape' => 'ParameterSpace', ], 'requiredCapabilities' => [ 'shape' => 'StepRequiredCapabilities', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'stepId' => [ 'shape' => 'StepId', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetStorageProfileForQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'GetStorageProfileForQueueResponse' => [ 'type' => 'structure', 'required' => [ 'displayName', 'osFamily', 'storageProfileId', ], 'members' => [ 'displayName' => [ 'shape' => 'ResourceName', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], ], ], 'GetStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'GetStorageProfileResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'displayName', 'osFamily', 'storageProfileId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'fileSystemLocations' => [ 'shape' => 'FileSystemLocationsList', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetTaskRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', 'taskId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'GetTaskResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'runStatus', 'taskId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'latestSessionActionId' => [ 'shape' => 'SessionActionId', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'taskId' => [ 'shape' => 'TaskId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'GetWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'GetWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'farmId', 'fleetId', 'status', 'workerId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerId' => [ 'shape' => 'WorkerId', ], ], ], 'HostName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\.\\-]{0,255}$', ], 'HostPropertiesRequest' => [ 'type' => 'structure', 'members' => [ 'hostName' => [ 'shape' => 'HostName', ], 'ipAddresses' => [ 'shape' => 'IpAddresses', ], ], ], 'HostPropertiesResponse' => [ 'type' => 'structure', 'members' => [ 'ec2InstanceArn' => [ 'shape' => 'String', ], 'ec2InstanceType' => [ 'shape' => 'InstanceType', ], 'hostName' => [ 'shape' => 'HostName', ], 'ipAddresses' => [ 'shape' => 'IpAddresses', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws[a-zA-Z-]*):iam::\\d{12}:role(/[!-.0-~]+)*/[\\w+=,.@-]+$', ], 'IdentityCenterApplicationArn' => [ 'type' => 'string', ], 'IdentityCenterInstanceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$', ], 'IdentityCenterPrincipalId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'IdentityStoreId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^d-[0-9a-f]{10}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'InstanceType' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9]+\\.[a-zA-Z0-9]+$', ], 'InstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceType', ], 'max' => 100, 'min' => 1, ], 'IntString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[-]?(0|[1-9][0-9]*)$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerErrorException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpAddresses' => [ 'type' => 'structure', 'members' => [ 'ipV4Addresses' => [ 'shape' => 'IpV4Addresses', ], 'ipV6Addresses' => [ 'shape' => 'IpV6Addresses', ], ], ], 'IpV4Address' => [ 'type' => 'string', 'pattern' => '^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$', ], 'IpV4Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4Address', ], ], 'IpV6Address' => [ 'type' => 'string', 'pattern' => '^(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}$', ], 'IpV6Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV6Address', ], ], 'JobAttachmentDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'attachments', 'jobId', ], 'members' => [ 'attachments' => [ 'shape' => 'Attachments', ], 'jobId' => [ 'shape' => 'JobId', ], ], ], 'JobAttachmentDetailsError' => [ 'type' => 'structure', 'required' => [ 'code', 'jobId', 'message', ], 'members' => [ 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'jobId' => [ 'shape' => 'JobId', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobAttachmentDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'JobAttachmentSettings' => [ 'type' => 'structure', 'required' => [ 'rootPrefix', 's3BucketName', ], 'members' => [ 'rootPrefix' => [ 'shape' => 'S3Prefix', ], 's3BucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'JobAttachmentsFileSystem' => [ 'type' => 'string', 'enum' => [ 'COPIED', 'VIRTUAL', ], ], 'JobDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'JobDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'jobId', 'logGroupName', 'schemaVersion', ], 'members' => [ 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'jobId' => [ 'shape' => 'JobId', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'logGroupName' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'JobParameters', ], 'pathMappingRules' => [ 'shape' => 'PathMappingRules', ], 'queueRoleArn' => [ 'shape' => 'IamRoleArn', ], 'schemaVersion' => [ 'shape' => 'String', ], ], ], 'JobDetailsError' => [ 'type' => 'structure', 'required' => [ 'code', 'jobId', 'message', ], 'members' => [ 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'jobId' => [ 'shape' => 'JobId', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'JobEntity' => [ 'type' => 'structure', 'members' => [ 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsEntity', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsEntity', ], 'jobDetails' => [ 'shape' => 'JobDetailsEntity', ], 'stepDetails' => [ 'shape' => 'StepDetailsEntity', ], ], 'union' => true, ], 'JobEntityErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedException', 'InternalServerException', 'ValidationException', 'ResourceNotFoundException', 'MaxPayloadSizeExceeded', 'ConflictException', ], ], 'JobEntityIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEntityIdentifiersUnion', ], 'max' => 10, 'min' => 1, ], 'JobEntityIdentifiersUnion' => [ 'type' => 'structure', 'members' => [ 'environmentDetails' => [ 'shape' => 'EnvironmentDetailsIdentifiers', ], 'jobAttachmentDetails' => [ 'shape' => 'JobAttachmentDetailsIdentifiers', ], 'jobDetails' => [ 'shape' => 'JobDetailsIdentifiers', ], 'stepDetails' => [ 'shape' => 'StepDetailsIdentifiers', ], ], 'union' => true, ], 'JobId' => [ 'type' => 'string', 'pattern' => '^job-[0-9a-f]{32}$', ], 'JobLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'UPLOAD_IN_PROGRESS', 'UPLOAD_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_SUCCEEDED', 'ARCHIVED', ], ], 'JobMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'jobId', 'membershipLevel', 'principalId', 'principalType', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'jobId' => [ 'shape' => 'JobId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'queueId' => [ 'shape' => 'QueueId', ], ], ], 'JobMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobMember', ], ], 'JobName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'JobParameter' => [ 'type' => 'structure', 'members' => [ 'float' => [ 'shape' => 'FloatString', ], 'int' => [ 'shape' => 'IntString', ], 'path' => [ 'shape' => 'PathString', ], 'string' => [ 'shape' => 'ParameterString', ], ], 'union' => true, ], 'JobParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'JobParameter', ], 'sensitive' => true, ], 'JobPriority' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'JobRunAsUser' => [ 'type' => 'structure', 'required' => [ 'runAs', ], 'members' => [ 'posix' => [ 'shape' => 'PosixUser', ], 'runAs' => [ 'shape' => 'RunAs', ], 'windows' => [ 'shape' => 'WindowsUser', ], ], ], 'JobSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSearchSummary', ], ], 'JobSearchSummary' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'jobId' => [ 'shape' => 'JobId', ], 'jobParameters' => [ 'shape' => 'JobParameters', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'name' => [ 'shape' => 'JobName', ], 'priority' => [ 'shape' => 'JobPriority', ], 'queueId' => [ 'shape' => 'QueueId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], ], ], 'JobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'jobId', 'lifecycleStatus', 'lifecycleStatusMessage', 'name', 'priority', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'jobId' => [ 'shape' => 'JobId', ], 'lifecycleStatus' => [ 'shape' => 'JobLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'name' => [ 'shape' => 'JobName', ], 'priority' => [ 'shape' => 'JobPriority', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'JobTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'JobTemplate' => [ 'type' => 'string', 'max' => 300000, 'min' => 1, 'sensitive' => true, ], 'JobTemplateType' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws[a-zA-Z-]*):kms:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:key/[\\w-]{1,120}$', ], 'LicenseEndpointId' => [ 'type' => 'string', 'pattern' => '^le-[0-9a-f]{32}$', ], 'LicenseEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'READY', 'NOT_READY', ], ], 'LicenseEndpointSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseEndpointSummary', ], ], 'LicenseEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', ], 'status' => [ 'shape' => 'LicenseEndpointStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'LicenseProduct' => [ 'type' => 'string', ], 'ListAttributeCapabilityValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeCapabilityValue', ], ], 'ListAvailableMeteredProductsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAvailableMeteredProductsResponse' => [ 'type' => 'structure', 'required' => [ 'meteredProducts', ], 'members' => [ 'meteredProducts' => [ 'shape' => 'MeteredProductSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBudgetsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'BudgetStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListBudgetsResponse' => [ 'type' => 'structure', 'required' => [ 'budgets', ], 'members' => [ 'budgets' => [ 'shape' => 'BudgetSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFarmMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFarmMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'FarmMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFarmsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], ], ], 'ListFarmsResponse' => [ 'type' => 'structure', 'required' => [ 'farms', ], 'members' => [ 'farms' => [ 'shape' => 'FarmSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFleetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFleetMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'FleetMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFleetsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'displayName' => [ 'shape' => 'ResourceName', 'location' => 'querystring', 'locationName' => 'displayName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'status' => [ 'shape' => 'FleetStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListFleetsResponse' => [ 'type' => 'structure', 'required' => [ 'fleets', ], 'members' => [ 'fleets' => [ 'shape' => 'FleetSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListJobMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListJobMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'JobMembers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'required' => [ 'jobs', ], 'members' => [ 'jobs' => [ 'shape' => 'JobSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListLicenseEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'licenseEndpoints', ], 'members' => [ 'licenseEndpoints' => [ 'shape' => 'LicenseEndpointSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMeteredProductsRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMeteredProductsResponse' => [ 'type' => 'structure', 'required' => [ 'meteredProducts', ], 'members' => [ 'meteredProducts' => [ 'shape' => 'MeteredProductSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMonitorsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMonitorsResponse' => [ 'type' => 'structure', 'required' => [ 'monitors', ], 'members' => [ 'monitors' => [ 'shape' => 'MonitorSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueueEnvironmentsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListQueueEnvironmentsResponse' => [ 'type' => 'structure', 'required' => [ 'environments', ], 'members' => [ 'environments' => [ 'shape' => 'QueueEnvironmentSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueueFleetAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'querystring', 'locationName' => 'fleetId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'querystring', 'locationName' => 'queueId', ], ], ], 'ListQueueFleetAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'queueFleetAssociations', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'queueFleetAssociations' => [ 'shape' => 'QueueFleetAssociationSummaries', ], ], ], 'ListQueueMembersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListQueueMembersResponse' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'QueueMemberList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', 'location' => 'querystring', 'locationName' => 'principalId', ], 'status' => [ 'shape' => 'QueueStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListQueuesResponse' => [ 'type' => 'structure', 'required' => [ 'queues', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'queues' => [ 'shape' => 'QueueSummaries', ], ], ], 'ListSessionActionsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'querystring', 'locationName' => 'sessionId', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'querystring', 'locationName' => 'taskId', ], ], ], 'ListSessionActionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessionActions', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'sessionActions' => [ 'shape' => 'SessionActionSummaries', ], ], ], 'ListSessionsForWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'ListSessionsForWorkerResponse' => [ 'type' => 'structure', 'required' => [ 'sessions', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'sessions' => [ 'shape' => 'ListSessionsForWorkerSummaries', ], ], ], 'ListSessionsForWorkerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSessionSummary', ], ], 'ListSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessions', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'sessions' => [ 'shape' => 'SessionSummaries', ], ], ], 'ListStepConsumersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'ListStepConsumersRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], ], ], 'ListStepConsumersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListStepConsumersResponse' => [ 'type' => 'structure', 'required' => [ 'consumers', ], 'members' => [ 'consumers' => [ 'shape' => 'StepConsumers', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStepDependenciesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'ListStepDependenciesRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], ], ], 'ListStepDependenciesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListStepDependenciesResponse' => [ 'type' => 'structure', 'required' => [ 'dependencies', ], 'members' => [ 'dependencies' => [ 'shape' => 'StepDependencies', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStepsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListStepsResponse' => [ 'type' => 'structure', 'required' => [ 'steps', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'steps' => [ 'shape' => 'StepSummaries', ], ], ], 'ListStorageProfilesForQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], ], ], 'ListStorageProfilesForQueueResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfiles', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'storageProfiles' => [ 'shape' => 'StorageProfileSummaries', ], ], ], 'ListStorageProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListStorageProfilesResponse' => [ 'type' => 'structure', 'required' => [ 'storageProfiles', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'storageProfiles' => [ 'shape' => 'StorageProfileSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListTasksRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], ], ], 'ListTasksResponse' => [ 'type' => 'structure', 'required' => [ 'tasks', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'tasks' => [ 'shape' => 'TaskSummaries', ], ], ], 'ListWorkersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListWorkersResponse' => [ 'type' => 'structure', 'required' => [ 'workers', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'workers' => [ 'shape' => 'WorkerSummaries', ], ], ], 'LogConfiguration' => [ 'type' => 'structure', 'required' => [ 'logDriver', ], 'members' => [ 'error' => [ 'shape' => 'LogError', ], 'logDriver' => [ 'shape' => 'LogDriver', ], 'options' => [ 'shape' => 'LogOptions', ], 'parameters' => [ 'shape' => 'LogParameters', ], ], ], 'LogDriver' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogError' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'LogParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'ManifestProperties' => [ 'type' => 'structure', 'required' => [ 'rootPath', 'rootPathFormat', ], 'members' => [ 'fileSystemLocationName' => [ 'shape' => 'FileSystemLocationName', ], 'inputManifestHash' => [ 'shape' => 'ManifestPropertiesInputManifestHashString', ], 'inputManifestPath' => [ 'shape' => 'ManifestPropertiesInputManifestPathString', ], 'outputRelativeDirectories' => [ 'shape' => 'OutputRelativeDirectoriesList', ], 'rootPath' => [ 'shape' => 'ManifestPropertiesRootPathString', ], 'rootPathFormat' => [ 'shape' => 'PathFormat', ], ], 'sensitive' => true, ], 'ManifestPropertiesInputManifestHashString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ManifestPropertiesInputManifestPathString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ManifestPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManifestProperties', ], 'max' => 10, 'min' => 1, ], 'ManifestPropertiesRootPathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'MaxFailedTasksCount' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxRetriesPerTask' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MembershipLevel' => [ 'type' => 'string', 'enum' => [ 'VIEWER', 'CONTRIBUTOR', 'OWNER', 'MANAGER', ], ], 'MemoryAmountMiB' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 512, ], 'MemoryMiBRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'max' => [ 'shape' => 'MemoryAmountMiB', ], 'min' => [ 'shape' => 'MemoryAmountMiB', ], ], ], 'MeteredProductId' => [ 'type' => 'string', 'pattern' => '^[0-9a-z]{1,32}-[.0-9a-z]{1,32}$', ], 'MeteredProductSummary' => [ 'type' => 'structure', 'required' => [ 'family', 'port', 'productId', 'vendor', ], 'members' => [ 'family' => [ 'shape' => 'BoundedString', ], 'port' => [ 'shape' => 'PortNumber', ], 'productId' => [ 'shape' => 'MeteredProductId', ], 'vendor' => [ 'shape' => 'BoundedString', ], ], ], 'MeteredProductSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MeteredProductSummary', ], ], 'MinOneMaxTenThousand' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'MinZeroMaxInteger' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'MonitorId' => [ 'type' => 'string', 'pattern' => '^monitor-[0-9a-f]{32}$', ], 'MonitorSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorSummary', ], ], 'MonitorSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'displayName', 'identityCenterApplicationArn', 'identityCenterInstanceArn', 'monitorId', 'roleArn', 'subdomain', 'url', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'identityCenterApplicationArn' => [ 'shape' => 'IdentityCenterApplicationArn', ], 'identityCenterInstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'monitorId' => [ 'shape' => 'MonitorId', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'subdomain' => [ 'shape' => 'Subdomain', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'url' => [ 'shape' => 'Url', ], ], ], 'NextItemOffset' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'OutputRelativeDirectoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputRelativeDirectoriesListMemberString', ], 'max' => 100, 'min' => 0, ], 'OutputRelativeDirectoriesListMemberString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParameterFilterExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], 'value' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterSortExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'sortOrder', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ParameterSpace' => [ 'type' => 'structure', 'required' => [ 'parameters', ], 'members' => [ 'combination' => [ 'shape' => 'CombinationExpression', ], 'parameters' => [ 'shape' => 'StepParameterList', ], ], ], 'ParameterString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ParameterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PathFormat' => [ 'type' => 'string', 'enum' => [ 'windows', 'posix', ], ], 'PathMappingRule' => [ 'type' => 'structure', 'required' => [ 'destinationPath', 'sourcePath', 'sourcePathFormat', ], 'members' => [ 'destinationPath' => [ 'shape' => 'String', ], 'sourcePath' => [ 'shape' => 'String', ], 'sourcePathFormat' => [ 'shape' => 'PathFormat', ], ], 'sensitive' => true, ], 'PathMappingRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathMappingRule', ], ], 'PathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Period' => [ 'type' => 'string', 'enum' => [ 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', ], ], 'PortNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1024, ], 'PosixUser' => [ 'type' => 'structure', 'required' => [ 'group', 'user', ], 'members' => [ 'group' => [ 'shape' => 'PosixUserGroupString', ], 'user' => [ 'shape' => 'PosixUserUserString', ], ], ], 'PosixUserGroupString' => [ 'type' => 'string', 'max' => 31, 'min' => 0, 'pattern' => '^(?:[a-z][a-z0-9-]{0,30})?$', ], 'PosixUserUserString' => [ 'type' => 'string', 'max' => 31, 'min' => 0, 'pattern' => '^(?:[a-z][a-z0-9-]{0,30})?$', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'Priority' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'ProcessExitCode' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => -**********, ], 'PutMeteredProductRequest' => [ 'type' => 'structure', 'required' => [ 'licenseEndpointId', 'productId', ], 'members' => [ 'licenseEndpointId' => [ 'shape' => 'LicenseEndpointId', 'location' => 'uri', 'locationName' => 'licenseEndpointId', ], 'productId' => [ 'shape' => 'MeteredProductId', 'location' => 'uri', 'locationName' => 'productId', ], ], ], 'PutMeteredProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueueBlockedReason' => [ 'type' => 'string', 'enum' => [ 'NO_BUDGET_CONFIGURED', 'BUDGET_THRESHOLD_REACHED', ], ], 'QueueEnvironmentId' => [ 'type' => 'string', 'pattern' => '^queueenv-[0-9a-f]{32}$', ], 'QueueEnvironmentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueEnvironmentSummary', ], ], 'QueueEnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'priority', 'queueEnvironmentId', ], 'members' => [ 'name' => [ 'shape' => 'EnvironmentName', ], 'priority' => [ 'shape' => 'Priority', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', ], ], ], 'QueueFleetAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', 'STOPPED', ], ], 'QueueFleetAssociationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueFleetAssociationSummary', ], ], 'QueueFleetAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'fleetId', 'queueId', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'status' => [ 'shape' => 'QueueFleetAssociationStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'QueueId' => [ 'type' => 'string', 'pattern' => '^queue-[0-9a-f]{32}$', ], 'QueueMember' => [ 'type' => 'structure', 'required' => [ 'farmId', 'identityStoreId', 'membershipLevel', 'principalId', 'principalType', 'queueId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', ], 'identityStoreId' => [ 'shape' => 'IdentityStoreId', ], 'membershipLevel' => [ 'shape' => 'MembershipLevel', ], 'principalId' => [ 'shape' => 'IdentityCenterPrincipalId', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'queueId' => [ 'shape' => 'QueueId', ], ], ], 'QueueMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueMember', ], ], 'QueueStatus' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'SCHEDULING', 'SCHEDULING_BLOCKED', ], ], 'QueueSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueSummary', ], ], 'QueueSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'defaultBudgetAction', 'displayName', 'farmId', 'queueId', 'status', ], 'members' => [ 'blockedReason' => [ 'shape' => 'QueueBlockedReason', ], 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', ], 'queueId' => [ 'shape' => 'QueueId', ], 'status' => [ 'shape' => 'QueueStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'RequiredFileSystemLocationNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileSystemLocationName', ], 'max' => 20, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseBudgetAction' => [ 'type' => 'structure', 'required' => [ 'thresholdPercentage', 'type', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'thresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], 'type' => [ 'shape' => 'BudgetActionType', ], ], ], 'ResponseBudgetActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseBudgetAction', ], 'max' => 10, 'min' => 0, ], 'RunAs' => [ 'type' => 'string', 'enum' => [ 'QUEUE_CONFIGURED_USER', 'WORKER_AGENT_USER', ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'key', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'S3Prefix' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'SearchFilterExpression' => [ 'type' => 'structure', 'members' => [ 'dateTimeFilter' => [ 'shape' => 'DateTimeFilterExpression', ], 'groupFilter' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'parameterFilter' => [ 'shape' => 'ParameterFilterExpression', ], 'searchTermFilter' => [ 'shape' => 'SearchTermFilterExpression', ], 'stringFilter' => [ 'shape' => 'StringFilterExpression', ], ], 'union' => true, ], 'SearchFilterExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchFilterExpression', ], 'max' => 3, 'min' => 1, ], 'SearchGroupedFilterExpressions' => [ 'type' => 'structure', 'required' => [ 'filters', 'operator', ], 'members' => [ 'filters' => [ 'shape' => 'SearchFilterExpressions', ], 'operator' => [ 'shape' => 'LogicalOperator', ], ], ], 'SearchJobsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'itemOffset', 'queueIds', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'itemOffset' => [ 'shape' => 'SearchJobsRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchJobsRequestPageSizeInteger', ], 'queueIds' => [ 'shape' => 'SearchJobsRequestQueueIdsList', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], ], ], 'SearchJobsRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchJobsRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchJobsRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchJobsResponse' => [ 'type' => 'structure', 'required' => [ 'jobs', 'totalResults', ], 'members' => [ 'jobs' => [ 'shape' => 'JobSearchSummaries', ], 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchSortExpression' => [ 'type' => 'structure', 'members' => [ 'fieldSort' => [ 'shape' => 'FieldSortExpression', ], 'parameterSort' => [ 'shape' => 'ParameterSortExpression', ], 'userJobsFirst' => [ 'shape' => 'UserJobsFirst', ], ], 'union' => true, ], 'SearchSortExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchSortExpression', ], 'max' => 1, 'min' => 1, ], 'SearchStepsRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'itemOffset', 'queueIds', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'itemOffset' => [ 'shape' => 'SearchStepsRequestItemOffsetInteger', ], 'jobId' => [ 'shape' => 'JobId', ], 'pageSize' => [ 'shape' => 'SearchStepsRequestPageSizeInteger', ], 'queueIds' => [ 'shape' => 'SearchStepsRequestQueueIdsList', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], ], ], 'SearchStepsRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchStepsRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchStepsRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchStepsResponse' => [ 'type' => 'structure', 'required' => [ 'steps', 'totalResults', ], 'members' => [ 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'steps' => [ 'shape' => 'StepSearchSummaries', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchTasksRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'itemOffset', 'queueIds', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'itemOffset' => [ 'shape' => 'SearchTasksRequestItemOffsetInteger', ], 'jobId' => [ 'shape' => 'JobId', ], 'pageSize' => [ 'shape' => 'SearchTasksRequestPageSizeInteger', ], 'queueIds' => [ 'shape' => 'SearchTasksRequestQueueIdsList', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], ], ], 'SearchTasksRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchTasksRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchTasksRequestQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SearchTasksResponse' => [ 'type' => 'structure', 'required' => [ 'tasks', 'totalResults', ], 'members' => [ 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'tasks' => [ 'shape' => 'TaskSearchSummaries', ], 'totalResults' => [ 'shape' => 'TotalResults', ], ], ], 'SearchTerm' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SearchTermFilterExpression' => [ 'type' => 'structure', 'required' => [ 'searchTerm', ], 'members' => [ 'searchTerm' => [ 'shape' => 'SearchTerm', ], ], ], 'SearchWorkersRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetIds', 'itemOffset', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'filterExpressions' => [ 'shape' => 'SearchGroupedFilterExpressions', ], 'fleetIds' => [ 'shape' => 'SearchWorkersRequestFleetIdsList', ], 'itemOffset' => [ 'shape' => 'SearchWorkersRequestItemOffsetInteger', ], 'pageSize' => [ 'shape' => 'SearchWorkersRequestPageSizeInteger', ], 'sortExpressions' => [ 'shape' => 'SearchSortExpressions', ], ], ], 'SearchWorkersRequestFleetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetId', ], 'max' => 10, 'min' => 1, ], 'SearchWorkersRequestItemOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'SearchWorkersRequestPageSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchWorkersResponse' => [ 'type' => 'structure', 'required' => [ 'totalResults', 'workers', ], 'members' => [ 'nextItemOffset' => [ 'shape' => 'NextItemOffset', ], 'totalResults' => [ 'shape' => 'TotalResults', ], 'workers' => [ 'shape' => 'WorkerSearchSummaries', ], ], ], 'SecretAccessKey' => [ 'type' => 'string', 'sensitive' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => '^sg-[\\w]{1,120}$', ], 'ServiceManagedEc2FleetConfiguration' => [ 'type' => 'structure', 'required' => [ 'instanceCapabilities', 'instanceMarketOptions', ], 'members' => [ 'instanceCapabilities' => [ 'shape' => 'ServiceManagedEc2InstanceCapabilities', ], 'instanceMarketOptions' => [ 'shape' => 'ServiceManagedEc2InstanceMarketOptions', ], ], ], 'ServiceManagedEc2InstanceCapabilities' => [ 'type' => 'structure', 'required' => [ 'cpuArchitectureType', 'memoryMiB', 'osFamily', 'vCpuCount', ], 'members' => [ 'allowedInstanceTypes' => [ 'shape' => 'InstanceTypes', ], 'cpuArchitectureType' => [ 'shape' => 'CpuArchitectureType', ], 'customAmounts' => [ 'shape' => 'CustomFleetAmountCapabilities', ], 'customAttributes' => [ 'shape' => 'CustomFleetAttributeCapabilities', ], 'excludedInstanceTypes' => [ 'shape' => 'InstanceTypes', ], 'memoryMiB' => [ 'shape' => 'MemoryMiBRange', ], 'osFamily' => [ 'shape' => 'ServiceManagedFleetOperatingSystemFamily', ], 'rootEbsVolume' => [ 'shape' => 'Ec2EbsVolume', ], 'vCpuCount' => [ 'shape' => 'VCpuCountRange', ], ], ], 'ServiceManagedEc2InstanceMarketOptions' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'Ec2MarketType', ], ], ], 'ServiceManagedFleetOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'reason', 'resourceType', 'serviceCode', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ServiceQuotaExceededExceptionReason', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededExceptionReason' => [ 'type' => 'string', 'enum' => [ 'SERVICE_QUOTA_EXCEEDED_EXCEPTION', 'KMS_KEY_LIMIT_EXCEEDED', ], ], 'SessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'EnvironmentEnterSessionActionDefinition', ], 'envExit' => [ 'shape' => 'EnvironmentExitSessionActionDefinition', ], 'syncInputJobAttachments' => [ 'shape' => 'SyncInputJobAttachmentsSessionActionDefinition', ], 'taskRun' => [ 'shape' => 'TaskRunSessionActionDefinition', ], ], 'union' => true, ], 'SessionActionDefinitionSummary' => [ 'type' => 'structure', 'members' => [ 'envEnter' => [ 'shape' => 'EnvironmentEnterSessionActionDefinitionSummary', ], 'envExit' => [ 'shape' => 'EnvironmentExitSessionActionDefinitionSummary', ], 'syncInputJobAttachments' => [ 'shape' => 'SyncInputJobAttachmentsSessionActionDefinitionSummary', ], 'taskRun' => [ 'shape' => 'TaskRunSessionActionDefinitionSummary', ], ], 'union' => true, ], 'SessionActionId' => [ 'type' => 'string', 'pattern' => '^sessionaction-[0-9a-f]{32}-(0|([1-9][0-9]{0,9}))$', ], 'SessionActionIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionActionId', ], 'max' => 100, 'min' => 0, ], 'SessionActionProgressMessage' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'SessionActionProgressPercent' => [ 'type' => 'float', 'box' => true, 'max' => 100, 'min' => 0, ], 'SessionActionStatus' => [ 'type' => 'string', 'enum' => [ 'ASSIGNED', 'RUNNING', 'CANCELING', 'SUCCEEDED', 'FAILED', 'INTERRUPTED', 'CANCELED', 'NEVER_ATTEMPTED', 'SCHEDULED', 'RECLAIMING', 'RECLAIMED', ], ], 'SessionActionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionActionSummary', ], ], 'SessionActionSummary' => [ 'type' => 'structure', 'required' => [ 'definition', 'sessionActionId', 'status', ], 'members' => [ 'definition' => [ 'shape' => 'SessionActionDefinitionSummary', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], 'sessionActionId' => [ 'shape' => 'SessionActionId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'status' => [ 'shape' => 'SessionActionStatus', ], 'workerUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'SessionId' => [ 'type' => 'string', 'pattern' => '^session-[0-9a-f]{32}$', ], 'SessionLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'UPDATE_IN_PROGRESS', 'UPDATE_SUCCEEDED', 'UPDATE_FAILED', 'ENDED', ], ], 'SessionLifecycleTargetStatus' => [ 'type' => 'string', 'enum' => [ 'ENDED', ], ], 'SessionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSummary', ], ], 'SessionSummary' => [ 'type' => 'structure', 'required' => [ 'fleetId', 'lifecycleStatus', 'sessionId', 'startedAt', 'workerId', ], 'members' => [ 'endedAt' => [ 'shape' => 'EndedAt', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerId' => [ 'shape' => 'WorkerId', ], ], ], 'SessionToken' => [ 'type' => 'string', 'sensitive' => true, ], 'SessionsStatisticsAggregationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'TIMEOUT', 'FAILED', 'COMPLETED', ], ], 'SessionsStatisticsResources' => [ 'type' => 'structure', 'members' => [ 'fleetIds' => [ 'shape' => 'SessionsStatisticsResourcesFleetIdsList', ], 'queueIds' => [ 'shape' => 'SessionsStatisticsResourcesQueueIdsList', ], ], 'union' => true, ], 'SessionsStatisticsResourcesFleetIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetId', ], 'max' => 10, 'min' => 1, ], 'SessionsStatisticsResourcesQueueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 10, 'min' => 1, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartSessionsStatisticsAggregationRequest' => [ 'type' => 'structure', 'required' => [ 'endTime', 'farmId', 'groupBy', 'resourceIds', 'startTime', 'statistics', ], 'members' => [ 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'groupBy' => [ 'shape' => 'UsageGroupBy', ], 'period' => [ 'shape' => 'Period', ], 'resourceIds' => [ 'shape' => 'SessionsStatisticsResources', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'statistics' => [ 'shape' => 'UsageStatistics', ], 'timezone' => [ 'shape' => 'Timezone', ], ], ], 'StartSessionsStatisticsAggregationResponse' => [ 'type' => 'structure', 'required' => [ 'aggregationId', ], 'members' => [ 'aggregationId' => [ 'shape' => 'AggregationId', ], ], ], 'StartedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'StartsAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Statistics' => [ 'type' => 'structure', 'required' => [ 'costInUsd', 'count', 'runtimeInSeconds', ], 'members' => [ 'aggregationEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'aggregationStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'costInUsd' => [ 'shape' => 'Stats', ], 'count' => [ 'shape' => 'Integer', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'jobId' => [ 'shape' => 'JobId', ], 'jobName' => [ 'shape' => 'JobName', ], 'licenseProduct' => [ 'shape' => 'LicenseProduct', ], 'queueId' => [ 'shape' => 'QueueId', ], 'runtimeInSeconds' => [ 'shape' => 'Stats', ], 'usageType' => [ 'shape' => 'UsageType', ], 'userId' => [ 'shape' => 'UserId', ], ], ], 'StatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statistics', ], ], 'Stats' => [ 'type' => 'structure', 'members' => [ 'avg' => [ 'shape' => 'Double', ], 'max' => [ 'shape' => 'Double', ], 'min' => [ 'shape' => 'Double', ], 'sum' => [ 'shape' => 'Double', ], ], ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'StepAmountCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepAmountCapability', ], ], 'StepAmountCapability' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'max' => [ 'shape' => 'Double', ], 'min' => [ 'shape' => 'Double', ], 'name' => [ 'shape' => 'AmountCapabilityName', ], 'value' => [ 'shape' => 'Double', ], ], ], 'StepAttributeCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepAttributeCapability', ], ], 'StepAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'allOf' => [ 'shape' => 'ListAttributeCapabilityValue', ], 'anyOf' => [ 'shape' => 'ListAttributeCapabilityValue', ], 'name' => [ 'shape' => 'AttributeCapabilityName', ], ], ], 'StepConsumer' => [ 'type' => 'structure', 'required' => [ 'status', 'stepId', ], 'members' => [ 'status' => [ 'shape' => 'DependencyConsumerResolutionStatus', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'StepConsumers' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepConsumer', ], ], 'StepDependencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepDependency', ], ], 'StepDependency' => [ 'type' => 'structure', 'required' => [ 'status', 'stepId', ], 'members' => [ 'status' => [ 'shape' => 'DependencyConsumerResolutionStatus', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'StepDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'StepDetailsEntity' => [ 'type' => 'structure', 'required' => [ 'dependencies', 'jobId', 'schemaVersion', 'stepId', 'template', ], 'members' => [ 'dependencies' => [ 'shape' => 'DependenciesList', ], 'jobId' => [ 'shape' => 'JobId', ], 'schemaVersion' => [ 'shape' => 'String', ], 'stepId' => [ 'shape' => 'StepId', ], 'template' => [ 'shape' => 'Document', ], ], ], 'StepDetailsError' => [ 'type' => 'structure', 'required' => [ 'code', 'jobId', 'message', 'stepId', ], 'members' => [ 'code' => [ 'shape' => 'JobEntityErrorCode', ], 'jobId' => [ 'shape' => 'JobId', ], 'message' => [ 'shape' => 'String', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'StepDetailsIdentifiers' => [ 'type' => 'structure', 'required' => [ 'jobId', 'stepId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'stepId' => [ 'shape' => 'StepId', ], ], ], 'StepId' => [ 'type' => 'string', 'pattern' => '^step-[0-9a-f]{32}$', ], 'StepLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_COMPLETE', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_SUCCEEDED', ], ], 'StepName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StepParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'StepParameterName', ], 'type' => [ 'shape' => 'StepParameterType', ], ], ], 'StepParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepParameter', ], ], 'StepParameterName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StepParameterType' => [ 'type' => 'string', 'enum' => [ 'INT', 'FLOAT', 'STRING', 'PATH', ], ], 'StepRequiredCapabilities' => [ 'type' => 'structure', 'required' => [ 'amounts', 'attributes', ], 'members' => [ 'amounts' => [ 'shape' => 'StepAmountCapabilities', ], 'attributes' => [ 'shape' => 'StepAttributeCapabilities', ], ], ], 'StepSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepSearchSummary', ], ], 'StepSearchSummary' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'jobId' => [ 'shape' => 'JobId', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'StepName', ], 'parameterSpace' => [ 'shape' => 'ParameterSpace', ], 'queueId' => [ 'shape' => 'QueueId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'stepId' => [ 'shape' => 'StepId', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], ], ], 'StepSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepSummary', ], ], 'StepSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'lifecycleStatus', 'name', 'stepId', 'taskRunStatus', 'taskRunStatusCounts', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'dependencyCounts' => [ 'shape' => 'DependencyCounts', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'lifecycleStatus' => [ 'shape' => 'StepLifecycleStatus', ], 'lifecycleStatusMessage' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'StepName', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'stepId' => [ 'shape' => 'StepId', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], 'taskRunStatus' => [ 'shape' => 'TaskRunStatus', ], 'taskRunStatusCounts' => [ 'shape' => 'TaskRunStatusCounts', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'StepTargetTaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'StorageProfileId' => [ 'type' => 'string', 'pattern' => '^sp-[0-9a-f]{32}$', ], 'StorageProfileOperatingSystemFamily' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', 'MACOS', ], ], 'StorageProfileSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageProfileSummary', ], ], 'StorageProfileSummary' => [ 'type' => 'structure', 'required' => [ 'displayName', 'osFamily', 'storageProfileId', ], 'members' => [ 'displayName' => [ 'shape' => 'ResourceName', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', ], ], ], 'String' => [ 'type' => 'string', ], 'StringFilter' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'StringFilterExpression' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'ComparisonOperator', ], 'value' => [ 'shape' => 'StringFilter', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subdomain' => [ 'type' => 'string', 'pattern' => '^[a-z0-9-]{1,100}$', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^subnet-[\\w]{1,120}$', ], 'SyncInputJobAttachmentsSessionActionDefinition' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'SyncInputJobAttachmentsSessionActionDefinitionSummary' => [ 'type' => 'structure', 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TaskId' => [ 'type' => 'string', 'pattern' => '^task-[0-9a-f]{32}-(0|([1-9][0-9]{0,9}))$', ], 'TaskParameterValue' => [ 'type' => 'structure', 'members' => [ 'float' => [ 'shape' => 'FloatString', ], 'int' => [ 'shape' => 'IntString', ], 'path' => [ 'shape' => 'PathString', ], 'string' => [ 'shape' => 'ParameterString', ], ], 'sensitive' => true, 'union' => true, ], 'TaskParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'TaskParameterValue', ], 'sensitive' => true, ], 'TaskRetryCount' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 0, ], 'TaskRunSessionActionDefinition' => [ 'type' => 'structure', 'required' => [ 'parameters', 'stepId', 'taskId', ], 'members' => [ 'parameters' => [ 'shape' => 'TaskParameters', ], 'stepId' => [ 'shape' => 'StepId', ], 'taskId' => [ 'shape' => 'TaskId', ], ], ], 'TaskRunSessionActionDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'stepId', 'taskId', ], 'members' => [ 'stepId' => [ 'shape' => 'StepId', ], 'taskId' => [ 'shape' => 'TaskId', ], ], ], 'TaskRunStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'ASSIGNED', 'STARTING', 'SCHEDULED', 'INTERRUPTING', 'RUNNING', 'SUSPENDED', 'CANCELED', 'FAILED', 'SUCCEEDED', 'NOT_COMPATIBLE', ], ], 'TaskRunStatusCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'TaskRunStatus', ], 'value' => [ 'shape' => 'Integer', ], ], 'TaskSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskSearchSummary', ], ], 'TaskSearchSummary' => [ 'type' => 'structure', 'members' => [ 'endedAt' => [ 'shape' => 'EndedAt', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'jobId' => [ 'shape' => 'JobId', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'queueId' => [ 'shape' => 'QueueId', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'stepId' => [ 'shape' => 'StepId', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'taskId' => [ 'shape' => 'TaskId', ], ], ], 'TaskSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskSummary', ], ], 'TaskSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'runStatus', 'taskId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'endedAt' => [ 'shape' => 'EndedAt', ], 'failureRetryCount' => [ 'shape' => 'TaskRetryCount', ], 'latestSessionActionId' => [ 'shape' => 'SessionActionId', ], 'parameters' => [ 'shape' => 'TaskParameters', ], 'runStatus' => [ 'shape' => 'TaskRunStatus', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'taskId' => [ 'shape' => 'TaskId', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], ], ], 'TaskTargetRunStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'FAILED', 'SUCCEEDED', 'CANCELED', 'SUSPENDED', 'PENDING', ], ], 'ThresholdPercentage' => [ 'type' => 'float', 'box' => true, 'max' => 100, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Timezone' => [ 'type' => 'string', 'max' => 9, 'min' => 9, 'pattern' => '^UTC[-+][01][0-9]:(30|00)$', ], 'TotalResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBudgetRequest' => [ 'type' => 'structure', 'required' => [ 'budgetId', 'farmId', ], 'members' => [ 'actionsToAdd' => [ 'shape' => 'BudgetActionsToAdd', ], 'actionsToRemove' => [ 'shape' => 'BudgetActionsToRemove', ], 'approximateDollarLimit' => [ 'shape' => 'ConsumedUsageLimit', ], 'budgetId' => [ 'shape' => 'BudgetId', 'location' => 'uri', 'locationName' => 'budgetId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'schedule' => [ 'shape' => 'BudgetSchedule', ], 'status' => [ 'shape' => 'BudgetStatus', ], ], ], 'UpdateBudgetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFarmRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], ], ], 'UpdateFarmResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'configuration' => [ 'shape' => 'FleetConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'maxWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'minWorkerCount' => [ 'shape' => 'MinZeroMaxInteger', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'UpdateFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateJobLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'ARCHIVED', ], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'lifecycleStatus' => [ 'shape' => 'UpdateJobLifecycleStatus', ], 'maxFailedTasksCount' => [ 'shape' => 'MaxFailedTasksCount', ], 'maxRetriesPerTask' => [ 'shape' => 'MaxRetriesPerTask', ], 'priority' => [ 'shape' => 'JobPriority', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'targetTaskRunStatus' => [ 'shape' => 'JobTargetTaskRunStatus', ], ], ], 'UpdateJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'monitorId', ], 'members' => [ 'displayName' => [ 'shape' => 'ResourceName', ], 'monitorId' => [ 'shape' => 'MonitorId', 'location' => 'uri', 'locationName' => 'monitorId', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'subdomain' => [ 'shape' => 'Subdomain', ], ], ], 'UpdateMonitorResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueEnvironmentId', 'queueId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'priority' => [ 'shape' => 'Priority', ], 'queueEnvironmentId' => [ 'shape' => 'QueueEnvironmentId', 'location' => 'uri', 'locationName' => 'queueEnvironmentId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'template' => [ 'shape' => 'EnvironmentTemplate', ], 'templateType' => [ 'shape' => 'EnvironmentTemplateType', ], ], ], 'UpdateQueueEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueFleetAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'queueId', 'status', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'status' => [ 'shape' => 'UpdateQueueFleetAssociationStatus', ], ], ], 'UpdateQueueFleetAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateQueueFleetAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STOP_SCHEDULING_AND_COMPLETE_TASKS', 'STOP_SCHEDULING_AND_CANCEL_TASKS', ], ], 'UpdateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'queueId', ], 'members' => [ 'allowedStorageProfileIdsToAdd' => [ 'shape' => 'AllowedStorageProfileIds', ], 'allowedStorageProfileIdsToRemove' => [ 'shape' => 'AllowedStorageProfileIds', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'defaultBudgetAction' => [ 'shape' => 'DefaultQueueBudgetAction', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobAttachmentSettings' => [ 'shape' => 'JobAttachmentSettings', ], 'jobRunAsUser' => [ 'shape' => 'JobRunAsUser', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'requiredFileSystemLocationNamesToAdd' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'requiredFileSystemLocationNamesToRemove' => [ 'shape' => 'RequiredFileSystemLocationNames', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'UpdateQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'sessionId', 'targetLifecycleStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], ], ], 'UpdateSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStepRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', 'targetTaskRunStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'targetTaskRunStatus' => [ 'shape' => 'StepTargetTaskRunStatus', ], ], ], 'UpdateStepResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStorageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'storageProfileId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'ResourceName', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fileSystemLocationsToAdd' => [ 'shape' => 'FileSystemLocationsList', ], 'fileSystemLocationsToRemove' => [ 'shape' => 'FileSystemLocationsList', ], 'osFamily' => [ 'shape' => 'StorageProfileOperatingSystemFamily', ], 'storageProfileId' => [ 'shape' => 'StorageProfileId', 'location' => 'uri', 'locationName' => 'storageProfileId', ], ], ], 'UpdateStorageProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTaskRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'jobId', 'queueId', 'stepId', 'targetRunStatus', 'taskId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'queueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'queueId', ], 'stepId' => [ 'shape' => 'StepId', 'location' => 'uri', 'locationName' => 'stepId', ], 'targetRunStatus' => [ 'shape' => 'TaskTargetRunStatus', ], 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'UpdateTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkerRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'capabilities' => [ 'shape' => 'WorkerCapabilities', ], 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesRequest', ], 'status' => [ 'shape' => 'UpdatedWorkerStatus', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'UpdateWorkerResponse' => [ 'type' => 'structure', 'members' => [ 'log' => [ 'shape' => 'LogConfiguration', ], ], ], 'UpdateWorkerScheduleInterval' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'UpdateWorkerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'farmId', 'fleetId', 'workerId', ], 'members' => [ 'farmId' => [ 'shape' => 'FarmId', 'location' => 'uri', 'locationName' => 'farmId', ], 'fleetId' => [ 'shape' => 'FleetId', 'location' => 'uri', 'locationName' => 'fleetId', ], 'updatedSessionActions' => [ 'shape' => 'UpdatedSessionActions', ], 'workerId' => [ 'shape' => 'WorkerId', 'location' => 'uri', 'locationName' => 'workerId', ], ], ], 'UpdateWorkerScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'assignedSessions', 'cancelSessionActions', 'updateIntervalSeconds', ], 'members' => [ 'assignedSessions' => [ 'shape' => 'AssignedSessions', ], 'cancelSessionActions' => [ 'shape' => 'CancelSessionActions', ], 'desiredWorkerStatus' => [ 'shape' => 'DesiredWorkerStatus', ], 'updateIntervalSeconds' => [ 'shape' => 'UpdateWorkerScheduleInterval', ], ], ], 'UpdatedAt' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UpdatedBy' => [ 'type' => 'string', ], 'UpdatedSessionActionInfo' => [ 'type' => 'structure', 'members' => [ 'completedStatus' => [ 'shape' => 'CompletedStatus', ], 'endedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'processExitCode' => [ 'shape' => 'ProcessExitCode', ], 'progressMessage' => [ 'shape' => 'SessionActionProgressMessage', ], 'progressPercent' => [ 'shape' => 'SessionActionProgressPercent', ], 'startedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'UpdatedSessionActions' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionActionId', ], 'value' => [ 'shape' => 'UpdatedSessionActionInfo', ], ], 'UpdatedWorkerStatus' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'STOPPING', 'STOPPED', ], ], 'Url' => [ 'type' => 'string', ], 'UsageGroupBy' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageGroupByField', ], 'max' => 2, 'min' => 1, ], 'UsageGroupByField' => [ 'type' => 'string', 'enum' => [ 'QUEUE_ID', 'FLEET_ID', 'JOB_ID', 'USER_ID', 'USAGE_TYPE', 'INSTANCE_TYPE', 'LICENSE_PRODUCT', ], ], 'UsageStatistic' => [ 'type' => 'string', 'enum' => [ 'SUM', 'MIN', 'MAX', 'AVG', ], ], 'UsageStatistics' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageStatistic', ], 'max' => 4, 'min' => 1, ], 'UsageTrackingResource' => [ 'type' => 'structure', 'members' => [ 'queueId' => [ 'shape' => 'QueueId', ], ], 'union' => true, ], 'UsageType' => [ 'type' => 'string', 'enum' => [ 'COMPUTE', 'LICENSE', ], ], 'UserId' => [ 'type' => 'string', ], 'UserJobsFirst' => [ 'type' => 'structure', 'required' => [ 'userIdentityId', ], 'members' => [ 'userIdentityId' => [ 'shape' => 'String', ], ], ], 'VCpuCountRange' => [ 'type' => 'structure', 'required' => [ 'min', ], 'members' => [ 'max' => [ 'shape' => 'MinOneMaxTenThousand', ], 'min' => [ 'shape' => 'MinOneMaxTenThousand', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'context' => [ 'shape' => 'ExceptionContext', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VpcId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^vpc-[\\w]{1,120}$', ], 'WindowsUser' => [ 'type' => 'structure', 'required' => [ 'passwordArn', 'user', ], 'members' => [ 'passwordArn' => [ 'shape' => 'WindowsUserPasswordArnString', ], 'user' => [ 'shape' => 'WindowsUserUserString', ], ], ], 'WindowsUserPasswordArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws[a-zA-Z-]*):secretsmanager:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:secret:[a-zA-Z0-9-/_+=.@]{1,2028}$', ], 'WindowsUserUserString' => [ 'type' => 'string', 'max' => 111, 'min' => 0, 'pattern' => '^[^"\'/\\[\\]:;|=,+*?<>\\s]*$', ], 'WorkerAmountCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'AmountCapabilityName', ], 'value' => [ 'shape' => 'Float', ], ], ], 'WorkerAmountCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerAmountCapability', ], 'max' => 17, 'min' => 2, ], 'WorkerAttributeCapability' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AttributeCapabilityName', ], 'values' => [ 'shape' => 'AttributeCapabilityValuesList', ], ], ], 'WorkerAttributeCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerAttributeCapability', ], 'max' => 17, 'min' => 2, ], 'WorkerCapabilities' => [ 'type' => 'structure', 'required' => [ 'amounts', 'attributes', ], 'members' => [ 'amounts' => [ 'shape' => 'WorkerAmountCapabilityList', ], 'attributes' => [ 'shape' => 'WorkerAttributeCapabilityList', ], ], ], 'WorkerId' => [ 'type' => 'string', 'pattern' => '^worker-[0-9a-f]{32}$', ], 'WorkerSearchSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSearchSummary', ], ], 'WorkerSearchSummary' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerId' => [ 'shape' => 'WorkerId', ], ], ], 'WorkerSessionSummary' => [ 'type' => 'structure', 'required' => [ 'jobId', 'lifecycleStatus', 'queueId', 'sessionId', 'startedAt', ], 'members' => [ 'endedAt' => [ 'shape' => 'EndedAt', ], 'jobId' => [ 'shape' => 'JobId', ], 'lifecycleStatus' => [ 'shape' => 'SessionLifecycleStatus', ], 'queueId' => [ 'shape' => 'QueueId', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'targetLifecycleStatus' => [ 'shape' => 'SessionLifecycleTargetStatus', ], ], ], 'WorkerStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'STARTED', 'STOPPING', 'STOPPED', 'NOT_RESPONDING', 'NOT_COMPATIBLE', 'RUNNING', 'IDLE', ], ], 'WorkerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkerSummary', ], ], 'WorkerSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdBy', 'farmId', 'fleetId', 'status', 'workerId', ], 'members' => [ 'createdAt' => [ 'shape' => 'CreatedAt', ], 'createdBy' => [ 'shape' => 'CreatedBy', ], 'farmId' => [ 'shape' => 'FarmId', ], 'fleetId' => [ 'shape' => 'FleetId', ], 'hostProperties' => [ 'shape' => 'HostPropertiesResponse', ], 'log' => [ 'shape' => 'LogConfiguration', ], 'status' => [ 'shape' => 'WorkerStatus', ], 'updatedAt' => [ 'shape' => 'UpdatedAt', ], 'updatedBy' => [ 'shape' => 'UpdatedBy', ], 'workerId' => [ 'shape' => 'WorkerId', ], ], ], ],];
