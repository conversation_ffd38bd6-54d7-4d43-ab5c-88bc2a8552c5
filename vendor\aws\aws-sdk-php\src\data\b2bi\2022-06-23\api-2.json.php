<?php
// This file was auto-generated from sdk-root/src/data/b2bi/2022-06-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-06-23', 'endpointPrefix' => 'b2bi', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceAbbreviation' => 'AWS B2BI', 'serviceFullName' => 'AWS B2B Data Interchange', 'serviceId' => 'b2bi', 'signatureVersion' => 'v4', 'signingName' => 'b2bi', 'targetPrefix' => 'B2BI', 'uid' => 'b2bi-2022-06-23', ], 'operations' => [ 'CreateCapability' => [ 'name' => 'CreateCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCapabilityRequest', ], 'output' => [ 'shape' => 'CreateCapabilityResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreatePartnership' => [ 'name' => 'CreatePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartnershipRequest', ], 'output' => [ 'shape' => 'CreatePartnershipResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTransformer' => [ 'name' => 'CreateTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTransformerRequest', ], 'output' => [ 'shape' => 'CreateTransformerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteCapability' => [ 'name' => 'DeleteCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCapabilityRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePartnership' => [ 'name' => 'DeletePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartnershipRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTransformer' => [ 'name' => 'DeleteTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTransformerRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetCapability' => [ 'name' => 'GetCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCapabilityRequest', ], 'output' => [ 'shape' => 'GetCapabilityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPartnership' => [ 'name' => 'GetPartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartnershipRequest', ], 'output' => [ 'shape' => 'GetPartnershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProfile' => [ 'name' => 'GetProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProfileRequest', ], 'output' => [ 'shape' => 'GetProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransformer' => [ 'name' => 'GetTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTransformerRequest', ], 'output' => [ 'shape' => 'GetTransformerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransformerJob' => [ 'name' => 'GetTransformerJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTransformerJobRequest', ], 'output' => [ 'shape' => 'GetTransformerJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCapabilities' => [ 'name' => 'ListCapabilities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCapabilitiesRequest', ], 'output' => [ 'shape' => 'ListCapabilitiesResponse', ], ], 'ListPartnerships' => [ 'name' => 'ListPartnerships', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPartnershipsRequest', ], 'output' => [ 'shape' => 'ListPartnershipsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListProfiles' => [ 'name' => 'ListProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProfilesRequest', ], 'output' => [ 'shape' => 'ListProfilesResponse', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTransformers' => [ 'name' => 'ListTransformers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTransformersRequest', ], 'output' => [ 'shape' => 'ListTransformersResponse', ], ], 'StartTransformerJob' => [ 'name' => 'StartTransformerJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTransformerJobRequest', ], 'output' => [ 'shape' => 'StartTransformerJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TestMapping' => [ 'name' => 'TestMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestMappingRequest', ], 'output' => [ 'shape' => 'TestMappingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TestParsing' => [ 'name' => 'TestParsing', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestParsingRequest', ], 'output' => [ 'shape' => 'TestParsingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateCapability' => [ 'name' => 'UpdateCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCapabilityRequest', ], 'output' => [ 'shape' => 'UpdateCapabilityResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdatePartnership' => [ 'name' => 'UpdatePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePartnershipRequest', ], 'output' => [ 'shape' => 'UpdatePartnershipResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTransformer' => [ 'name' => 'UpdateTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTransformerRequest', ], 'output' => [ 'shape' => 'UpdateTransformerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'BusinessName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'CapabilityConfiguration' => [ 'type' => 'structure', 'members' => [ 'edi' => [ 'shape' => 'EdiConfiguration', ], ], 'union' => true, ], 'CapabilityId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilitySummary', ], ], 'CapabilityName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'CapabilitySummary' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'name', 'type', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'CapabilityType' => [ 'type' => 'string', 'enum' => [ 'edi', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CreateCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'configuration', ], 'members' => [ 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreatePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', 'name', 'email', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'phone', 'businessName', 'logging', ], 'members' => [ 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'businessName', 'phone', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'phone' => [ 'shape' => 'Phone', ], 'email' => [ 'shape' => 'Email', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreateTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'fileFormat', 'mappingTemplate', 'ediType', ], 'members' => [ 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'fileFormat', 'mappingTemplate', 'status', 'ediType', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreatedDate' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], ], ], 'DeletePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], ], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], ], ], 'DeleteTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'EdiConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', 'inputLocation', 'outputLocation', 'transformerId', ], 'members' => [ 'type' => [ 'shape' => 'EdiType', ], 'inputLocation' => [ 'shape' => 'S3Location', ], 'outputLocation' => [ 'shape' => 'S3Location', ], 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'EdiType' => [ 'type' => 'structure', 'members' => [ 'x12Details' => [ 'shape' => 'X12Details', ], ], 'union' => true, ], 'Email' => [ 'type' => 'string', 'max' => 254, 'min' => 5, 'pattern' => '[\\w\\.\\-]+@[\\w\\.\\-]+', 'sensitive' => true, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 10, ], 'FileFormat' => [ 'type' => 'string', 'enum' => [ 'XML', 'JSON', ], ], 'FileLocation' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'GetCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], ], ], 'GetCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetPartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], ], ], 'GetPartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], ], ], 'GetProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'phone', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetTransformerJobRequest' => [ 'type' => 'structure', 'required' => [ 'transformerJobId', 'transformerId', ], 'members' => [ 'transformerJobId' => [ 'shape' => 'TransformerJobId', ], 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'GetTransformerJobResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'TransformerJobStatus', ], 'outputFiles' => [ 'shape' => 'S3LocationList', ], 'message' => [ 'shape' => 'String', ], ], ], 'GetTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'GetTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'fileFormat', 'mappingTemplate', 'status', 'ediType', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'InstructionsDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Location', ], 'max' => 5, 'min' => 0, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListCapabilitiesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCapabilitiesResponse' => [ 'type' => 'structure', 'required' => [ 'capabilities', ], 'members' => [ 'capabilities' => [ 'shape' => 'CapabilityList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListPartnershipsRequest' => [ 'type' => 'structure', 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPartnershipsResponse' => [ 'type' => 'structure', 'required' => [ 'partnerships', ], 'members' => [ 'partnerships' => [ 'shape' => 'PartnershipList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListProfilesResponse' => [ 'type' => 'structure', 'required' => [ 'profiles', ], 'members' => [ 'profiles' => [ 'shape' => 'ProfileList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTransformersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTransformersResponse' => [ 'type' => 'structure', 'required' => [ 'transformers', ], 'members' => [ 'transformers' => [ 'shape' => 'TransformerList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'Logging' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'MappingTemplate' => [ 'type' => 'string', 'max' => 350000, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ModifiedDate' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'PageToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PartnerName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'PartnershipCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityId', ], ], 'PartnershipId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'PartnershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartnershipSummary', ], ], 'PartnershipSummary' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'name' => [ 'shape' => 'PartnerName', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'Phone' => [ 'type' => 'string', 'max' => 22, 'min' => 7, 'pattern' => '\\+?([0-9 \\t\\-()\\/]{7,})(?:\\s*(?:#|x\\.?|ext\\.?|extension) \\t*(\\d+))?', 'sensitive' => true, ], 'ProfileId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileSummary', ], ], 'ProfileName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'ProfileSummary' => [ 'type' => 'structure', 'required' => [ 'profileId', 'name', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'ProfileName', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'S3LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Location', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'StartTransformerJobRequest' => [ 'type' => 'structure', 'required' => [ 'inputFile', 'outputLocation', 'transformerId', ], 'members' => [ 'inputFile' => [ 'shape' => 'S3Location', ], 'outputLocation' => [ 'shape' => 'S3Location', ], 'transformerId' => [ 'shape' => 'TransformerId', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'StartTransformerJobResponse' => [ 'type' => 'structure', 'required' => [ 'transformerJobId', ], 'members' => [ 'transformerJobId' => [ 'shape' => 'TransformerJobId', ], ], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TestMappingInputFileContent' => [ 'type' => 'string', 'max' => 5000000, 'min' => 0, ], 'TestMappingRequest' => [ 'type' => 'structure', 'required' => [ 'inputFileContent', 'mappingTemplate', 'fileFormat', ], 'members' => [ 'inputFileContent' => [ 'shape' => 'TestMappingInputFileContent', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], ], ], 'TestMappingResponse' => [ 'type' => 'structure', 'required' => [ 'mappedFileContent', ], 'members' => [ 'mappedFileContent' => [ 'shape' => 'String', ], ], ], 'TestParsingRequest' => [ 'type' => 'structure', 'required' => [ 'inputFile', 'fileFormat', 'ediType', ], 'members' => [ 'inputFile' => [ 'shape' => 'S3Location', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'ediType' => [ 'shape' => 'EdiType', ], ], ], 'TestParsingResponse' => [ 'type' => 'structure', 'required' => [ 'parsedFileContent', ], 'members' => [ 'parsedFileContent' => [ 'shape' => 'String', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TradingPartnerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerJobId' => [ 'type' => 'string', 'max' => 25, 'min' => 25, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerJobStatus' => [ 'type' => 'string', 'enum' => [ 'running', 'succeeded', 'failed', ], ], 'TransformerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformerSummary', ], ], 'TransformerName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'TransformerStatus' => [ 'type' => 'string', 'enum' => [ 'active', 'inactive', ], ], 'TransformerSummary' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'name', 'fileFormat', 'mappingTemplate', 'status', 'ediType', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], ], ], 'UpdateCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdatePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'name' => [ 'shape' => 'PartnerName', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], ], ], 'UpdatePartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'phone', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdateTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], ], ], 'UpdateTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'fileFormat', 'mappingTemplate', 'status', 'ediType', 'createdAt', 'modifiedAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'ediType' => [ 'shape' => 'EdiType', ], 'sampleDocument' => [ 'shape' => 'FileLocation', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'X12Details' => [ 'type' => 'structure', 'members' => [ 'transactionSet' => [ 'shape' => 'X12TransactionSet', ], 'version' => [ 'shape' => 'X12Version', ], ], ], 'X12TransactionSet' => [ 'type' => 'string', 'enum' => [ 'X12_110', 'X12_180', 'X12_204', 'X12_210', 'X12_211', 'X12_214', 'X12_215', 'X12_259', 'X12_260', 'X12_266', 'X12_269', 'X12_270', 'X12_271', 'X12_274', 'X12_275', 'X12_276', 'X12_277', 'X12_278', 'X12_310', 'X12_315', 'X12_322', 'X12_404', 'X12_410', 'X12_417', 'X12_421', 'X12_426', 'X12_810', 'X12_820', 'X12_824', 'X12_830', 'X12_832', 'X12_834', 'X12_835', 'X12_837', 'X12_844', 'X12_846', 'X12_849', 'X12_850', 'X12_852', 'X12_855', 'X12_856', 'X12_860', 'X12_861', 'X12_864', 'X12_865', 'X12_869', 'X12_870', 'X12_940', 'X12_945', 'X12_990', 'X12_997', 'X12_999', 'X12_270_X279', 'X12_271_X279', 'X12_275_X210', 'X12_275_X211', 'X12_276_X212', 'X12_277_X212', 'X12_277_X214', 'X12_277_X364', 'X12_278_X217', 'X12_820_X218', 'X12_820_X306', 'X12_824_X186', 'X12_834_X220', 'X12_834_X307', 'X12_834_X318', 'X12_835_X221', 'X12_837_X222', 'X12_837_X223', 'X12_837_X224', 'X12_837_X291', 'X12_837_X292', 'X12_837_X298', 'X12_999_X231', ], ], 'X12Version' => [ 'type' => 'string', 'enum' => [ 'VERSION_4010', 'VERSION_4030', 'VERSION_5010', 'VERSION_5010_HIPAA', ], ], ],];
