{"name": "filament/forms", "description": "Easily add beautiful forms to any Livewire component.", "license": "MIT", "homepage": "https://github.com/filamentphp/filament", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "require": {"php": "^8.1", "danharrin/date-format-converter": "^0.3", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/console": "^10.45|^11.0", "illuminate/contracts": "^10.45|^11.0", "illuminate/database": "^10.45|^11.0", "illuminate/filesystem": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/validation": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "spatie/laravel-package-tools": "^1.9"}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Filament\\Forms\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\Forms\\FormsServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}