<?php
// This file was auto-generated from sdk-root/src/data/qbusiness/2023-11-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-11-27', 'endpointPrefix' => 'qbusiness', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'QBusiness', 'serviceId' => 'QBusiness', 'signatureVersion' => 'v4', 'signingName' => 'qbusiness', 'uid' => 'qbusiness-2023-11-27', ], 'operations' => [ 'BatchDeleteDocument' => [ 'name' => 'BatchDeleteDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/documents/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteDocumentRequest', ], 'output' => [ 'shape' => 'BatchDeleteDocumentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchPutDocument' => [ 'name' => 'BatchPutDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/documents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutDocumentRequest', ], 'output' => [ 'shape' => 'BatchPutDocumentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ChatSync' => [ 'name' => 'ChatSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/conversations?sync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ChatSyncInput', ], 'output' => [ 'shape' => 'ChatSyncOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateIndex' => [ 'name' => 'CreateIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIndexRequest', ], 'output' => [ 'shape' => 'CreateIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreatePlugin' => [ 'name' => 'CreatePlugin', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/plugins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePluginRequest', ], 'output' => [ 'shape' => 'CreatePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateRetriever' => [ 'name' => 'CreateRetriever', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/retrievers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRetrieverRequest', ], 'output' => [ 'shape' => 'CreateRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateWebExperience' => [ 'name' => 'CreateWebExperience', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/experiences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWebExperienceRequest', ], 'output' => [ 'shape' => 'CreateWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteChatControlsConfiguration' => [ 'name' => 'DeleteChatControlsConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConversation' => [ 'name' => 'DeleteConversation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConversationRequest', ], 'output' => [ 'shape' => 'DeleteConversationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups/{groupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'output' => [ 'shape' => 'DeleteGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteIndex' => [ 'name' => 'DeleteIndex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIndexRequest', ], 'output' => [ 'shape' => 'DeleteIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeletePlugin' => [ 'name' => 'DeletePlugin', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePluginRequest', ], 'output' => [ 'shape' => 'DeletePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteRetriever' => [ 'name' => 'DeleteRetriever', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRetrieverRequest', ], 'output' => [ 'shape' => 'DeleteRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteWebExperience' => [ 'name' => 'DeleteWebExperience', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWebExperienceRequest', ], 'output' => [ 'shape' => 'DeleteWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationRequest', ], 'output' => [ 'shape' => 'GetApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetChatControlsConfiguration' => [ 'name' => 'GetChatControlsConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'GetChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetGroup' => [ 'name' => 'GetGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups/{groupName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGroupRequest', ], 'output' => [ 'shape' => 'GetGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIndex' => [ 'name' => 'GetIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIndexRequest', ], 'output' => [ 'shape' => 'GetIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetPlugin' => [ 'name' => 'GetPlugin', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPluginRequest', ], 'output' => [ 'shape' => 'GetPluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetRetriever' => [ 'name' => 'GetRetriever', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRetrieverRequest', ], 'output' => [ 'shape' => 'GetRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetWebExperience' => [ 'name' => 'GetWebExperience', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWebExperienceRequest', ], 'output' => [ 'shape' => 'GetWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConversations' => [ 'name' => 'ListConversations', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/conversations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConversationsRequest', ], 'output' => [ 'shape' => 'ListConversationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataSourceSyncJobs' => [ 'name' => 'ListDataSourceSyncJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/syncjobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourceSyncJobsRequest', ], 'output' => [ 'shape' => 'ListDataSourceSyncJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDocuments' => [ 'name' => 'ListDocuments', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/index/{indexId}/documents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDocumentsRequest', ], 'output' => [ 'shape' => 'ListDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGroupsRequest', ], 'output' => [ 'shape' => 'ListGroupsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListIndices' => [ 'name' => 'ListIndices', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/indices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIndicesRequest', ], 'output' => [ 'shape' => 'ListIndicesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMessages' => [ 'name' => 'ListMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMessagesRequest', ], 'output' => [ 'shape' => 'ListMessagesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LicenseNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPlugins' => [ 'name' => 'ListPlugins', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/plugins', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPluginsRequest', ], 'output' => [ 'shape' => 'ListPluginsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListRetrievers' => [ 'name' => 'ListRetrievers', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/retrievers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRetrieversRequest', ], 'output' => [ 'shape' => 'ListRetrieversResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListWebExperiences' => [ 'name' => 'ListWebExperiences', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/experiences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWebExperiencesRequest', ], 'output' => [ 'shape' => 'ListWebExperiencesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/conversations/{conversationId}/messages/{messageId}/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutGroup' => [ 'name' => 'PutGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutGroupRequest', ], 'output' => [ 'shape' => 'PutGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartDataSourceSyncJob' => [ 'name' => 'StartDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/startsync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartDataSourceSyncJobRequest', ], 'output' => [ 'shape' => 'StartDataSourceSyncJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StopDataSourceSyncJob' => [ 'name' => 'StopDataSourceSyncJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/stopsync', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopDataSourceSyncJobRequest', ], 'output' => [ 'shape' => 'StopDataSourceSyncJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateChatControlsConfiguration' => [ 'name' => 'UpdateChatControlsConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{applicationId}/chatcontrols', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChatControlsConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateChatControlsConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateIndex' => [ 'name' => 'UpdateIndex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/indices/{indexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIndexRequest', ], 'output' => [ 'shape' => 'UpdateIndexResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdatePlugin' => [ 'name' => 'UpdatePlugin', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/plugins/{pluginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePluginRequest', ], 'output' => [ 'shape' => 'UpdatePluginResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateRetriever' => [ 'name' => 'UpdateRetriever', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/retrievers/{retrieverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRetrieverRequest', ], 'output' => [ 'shape' => 'UpdateRetrieverResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/users/{userId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateWebExperience' => [ 'name' => 'UpdateWebExperience', 'http' => [ 'method' => 'PUT', 'requestUri' => '/applications/{applicationId}/experiences/{webExperienceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWebExperienceRequest', ], 'output' => [ 'shape' => 'UpdateWebExperienceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessConfiguration' => [ 'type' => 'structure', 'required' => [ 'accessControls', ], 'members' => [ 'accessControls' => [ 'shape' => 'AccessControls', ], 'memberRelation' => [ 'shape' => 'MemberRelation', ], ], ], 'AccessControl' => [ 'type' => 'structure', 'required' => [ 'principals', ], 'members' => [ 'memberRelation' => [ 'shape' => 'MemberRelation', ], 'principals' => [ 'shape' => 'Principals', ], ], ], 'AccessControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControl', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionExecution' => [ 'type' => 'structure', 'required' => [ 'payload', 'payloadFieldNameSeparator', 'pluginId', ], 'members' => [ 'payload' => [ 'shape' => 'ActionExecutionPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], 'pluginId' => [ 'shape' => 'PluginId', ], ], ], 'ActionExecutionPayload' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionPayloadFieldKey', ], 'value' => [ 'shape' => 'ActionExecutionPayloadField', ], ], 'ActionExecutionPayloadField' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], ], ], 'ActionPayloadFieldKey' => [ 'type' => 'string', 'min' => 1, ], 'ActionPayloadFieldNameSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'ActionPayloadFieldType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'NUMBER', 'ARRAY', 'BOOLEAN', ], ], 'ActionPayloadFieldValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ActionReview' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'ActionReviewPayload', ], 'payloadFieldNameSeparator' => [ 'shape' => 'ActionPayloadFieldNameSeparator', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'pluginType' => [ 'shape' => 'PluginType', ], ], ], 'ActionReviewPayload' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionPayloadFieldKey', ], 'value' => [ 'shape' => 'ActionReviewPayloadField', ], ], 'ActionReviewPayloadField' => [ 'type' => 'structure', 'members' => [ 'allowedValues' => [ 'shape' => 'ActionReviewPayloadFieldAllowedValues', ], 'displayName' => [ 'shape' => 'String', ], 'displayOrder' => [ 'shape' => 'Integer', ], 'required' => [ 'shape' => 'Boolean', ], 'type' => [ 'shape' => 'ActionPayloadFieldType', ], 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], ], ], 'ActionReviewPayloadFieldAllowedValue' => [ 'type' => 'structure', 'members' => [ 'displayValue' => [ 'shape' => 'ActionPayloadFieldValue', ], 'value' => [ 'shape' => 'ActionPayloadFieldValue', ], ], ], 'ActionReviewPayloadFieldAllowedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionReviewPayloadFieldAllowedValue', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'Application' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'status' => [ 'shape' => 'ApplicationStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ApplicationArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'ApplicationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'ApplicationName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'ApplicationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'Applications' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], ], 'AppliedAttachmentsConfiguration' => [ 'type' => 'structure', 'members' => [ 'attachmentsControlMode' => [ 'shape' => 'AttachmentsControlMode', ], ], ], 'AttachmentInput' => [ 'type' => 'structure', 'required' => [ 'data', 'name', ], 'members' => [ 'data' => [ 'shape' => 'Blob', ], 'name' => [ 'shape' => 'AttachmentName', ], ], ], 'AttachmentName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'AttachmentOutput' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'ErrorDetail', ], 'name' => [ 'shape' => 'AttachmentName', ], 'status' => [ 'shape' => 'AttachmentStatus', ], ], ], 'AttachmentStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', ], ], 'AttachmentsConfiguration' => [ 'type' => 'structure', 'required' => [ 'attachmentsControlMode', ], 'members' => [ 'attachmentsControlMode' => [ 'shape' => 'AttachmentsControlMode', ], ], ], 'AttachmentsControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AttachmentsInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentInput', ], 'min' => 1, ], 'AttachmentsOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachmentOutput', ], ], 'AttributeFilter' => [ 'type' => 'structure', 'members' => [ 'andAllFilters' => [ 'shape' => 'AttributeFilters', ], 'containsAll' => [ 'shape' => 'DocumentAttribute', ], 'containsAny' => [ 'shape' => 'DocumentAttribute', ], 'equalsTo' => [ 'shape' => 'DocumentAttribute', ], 'greaterThan' => [ 'shape' => 'DocumentAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], 'lessThan' => [ 'shape' => 'DocumentAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], 'notFilter' => [ 'shape' => 'AttributeFilter', ], 'orAllFilters' => [ 'shape' => 'AttributeFilters', ], ], ], 'AttributeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeFilter', ], ], 'AttributeType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'STRING_LIST', 'NUMBER', 'DATE', ], ], 'AttributeValueOperator' => [ 'type' => 'string', 'enum' => [ 'DELETE', ], ], 'BasicAuthConfiguration' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'secretArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'secretArn' => [ 'shape' => 'SecretArn', ], ], ], 'BatchDeleteDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'documents', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceSyncId' => [ 'shape' => 'ExecutionId', ], 'documents' => [ 'shape' => 'DeleteDocuments', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'BatchDeleteDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'failedDocuments' => [ 'shape' => 'FailedDocuments', ], ], ], 'BatchPutDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'documents', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceSyncId' => [ 'shape' => 'ExecutionId', ], 'documents' => [ 'shape' => 'Documents', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'BatchPutDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'failedDocuments' => [ 'shape' => 'FailedDocuments', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'BlockedPhrase' => [ 'type' => 'string', 'max' => 36, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'BlockedPhrases' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockedPhrase', ], 'max' => 5, 'min' => 0, ], 'BlockedPhrasesConfiguration' => [ 'type' => 'structure', 'members' => [ 'blockedPhrases' => [ 'shape' => 'BlockedPhrases', ], 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'BlockedPhrasesConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'blockedPhrasesToCreateOrUpdate' => [ 'shape' => 'BlockedPhrases', ], 'blockedPhrasesToDelete' => [ 'shape' => 'BlockedPhrases', ], 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoostingDurationInSeconds' => [ 'type' => 'long', 'box' => true, 'max' => 999999999, 'min' => 0, ], 'ChatSyncInput' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'actionExecution' => [ 'shape' => 'ActionExecution', ], 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'attachments' => [ 'shape' => 'AttachmentsInput', ], 'attributeFilter' => [ 'shape' => 'AttributeFilter', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'conversationId' => [ 'shape' => 'ConversationId', ], 'parentMessageId' => [ 'shape' => 'MessageId', ], 'userGroups' => [ 'shape' => 'UserGroups', 'location' => 'querystring', 'locationName' => 'userGroups', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], 'userMessage' => [ 'shape' => 'UserMessage', ], ], ], 'ChatSyncOutput' => [ 'type' => 'structure', 'members' => [ 'actionReview' => [ 'shape' => 'ActionReview', ], 'conversationId' => [ 'shape' => 'ConversationId', ], 'failedAttachments' => [ 'shape' => 'AttachmentsOutput', ], 'sourceAttributions' => [ 'shape' => 'SourceAttributions', ], 'systemMessage' => [ 'shape' => 'String', ], 'systemMessageId' => [ 'shape' => 'MessageId', ], 'userMessageId' => [ 'shape' => 'MessageId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentBlockerRule' => [ 'type' => 'structure', 'members' => [ 'systemMessageOverride' => [ 'shape' => 'SystemMessageOverride', ], ], ], 'ContentRetrievalRule' => [ 'type' => 'structure', 'members' => [ 'eligibleDataSources' => [ 'shape' => 'EligibleDataSources', ], ], ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'PDF', 'HTML', 'MS_WORD', 'PLAIN_TEXT', 'PPT', 'RTF', 'XML', 'XSLT', 'MS_EXCEL', 'CSV', 'JSON', 'MD', ], ], 'Conversation' => [ 'type' => 'structure', 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'title' => [ 'shape' => 'ConversationTitle', ], ], ], 'ConversationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'ConversationTitle' => [ 'type' => 'string', ], 'Conversations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Conversation', ], ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'displayName', 'roleArn', ], 'members' => [ 'attachmentsConfiguration' => [ 'shape' => 'AttachmentsConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'applicationArn' => [ 'shape' => 'ApplicationArn', ], 'applicationId' => [ 'shape' => 'ApplicationId', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'configuration', 'displayName', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'tags' => [ 'shape' => 'Tags', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'dataSourceArn' => [ 'shape' => 'DataSourceArn', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], ], ], 'CreateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'displayName', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'IndexName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateIndexResponse' => [ 'type' => 'structure', 'members' => [ 'indexArn' => [ 'shape' => 'IndexArn', ], 'indexId' => [ 'shape' => 'IndexId', ], ], ], 'CreatePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'authConfiguration', 'displayName', 'serverUrl', 'type', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'displayName' => [ 'shape' => 'PluginName', ], 'serverUrl' => [ 'shape' => 'Url', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'PluginType', ], ], ], 'CreatePluginResponse' => [ 'type' => 'structure', 'members' => [ 'pluginArn' => [ 'shape' => 'PluginArn', ], 'pluginId' => [ 'shape' => 'PluginId', ], ], ], 'CreateRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'configuration', 'displayName', 'type', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'RetrieverType', ], ], ], 'CreateRetrieverResponse' => [ 'type' => 'structure', 'members' => [ 'retrieverArn' => [ 'shape' => 'RetrieverArn', ], 'retrieverId' => [ 'shape' => 'RetrieverId', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'userAliases' => [ 'shape' => 'CreateUserRequestUserAliasesList', ], 'userId' => [ 'shape' => 'String', ], ], ], 'CreateUserRequestUserAliasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAlias', ], 'max' => 100, 'min' => 0, ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'tags' => [ 'shape' => 'Tags', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], ], ], 'CreateWebExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'webExperienceArn' => [ 'shape' => 'WebExperienceArn', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DataSourceArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DataSourceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'DataSourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceId', ], 'max' => 1, 'min' => 1, ], 'DataSourceName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_CREATION', 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'DataSourceSyncJob' => [ 'type' => 'structure', 'members' => [ 'dataSourceErrorCode' => [ 'shape' => 'String', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'executionId' => [ 'shape' => 'ExecutionId', ], 'metrics' => [ 'shape' => 'DataSourceSyncJobMetrics', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DataSourceSyncJobStatus', ], ], ], 'DataSourceSyncJobMetrics' => [ 'type' => 'structure', 'members' => [ 'documentsAdded' => [ 'shape' => 'MetricValue', ], 'documentsDeleted' => [ 'shape' => 'MetricValue', ], 'documentsFailed' => [ 'shape' => 'MetricValue', ], 'documentsModified' => [ 'shape' => 'MetricValue', ], 'documentsScanned' => [ 'shape' => 'MetricValue', ], ], ], 'DataSourceSyncJobStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'SYNCING', 'INCOMPLETE', 'STOPPING', 'ABORTED', 'SYNCING_INDEXING', ], ], 'DataSourceSyncJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSyncJob', ], ], 'DataSourceUserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'DataSourceVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'subnetIds', ], 'members' => [ 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'subnetIds' => [ 'shape' => 'SubnetIds', ], ], ], 'DataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DateAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingDurationInSeconds' => [ 'shape' => 'BoostingDurationInSeconds', ], 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], ], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DeleteChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConversationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'conversationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], ], ], 'DeleteConversationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDocument' => [ 'type' => 'structure', 'required' => [ 'documentId', ], 'members' => [ 'documentId' => [ 'shape' => 'DocumentId', ], ], ], 'DeleteDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteDocument', ], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'groupName', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], 'groupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'groupName', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'DeleteGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'DeleteIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], ], ], 'DeletePluginResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], ], ], 'DeleteRetrieverResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], ], ], 'DeleteWebExperienceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'Document' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'accessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'attributes' => [ 'shape' => 'DocumentAttributes', ], 'content' => [ 'shape' => 'DocumentContent', ], 'contentType' => [ 'shape' => 'ContentType', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], 'id' => [ 'shape' => 'DocumentId', ], 'title' => [ 'shape' => 'Title', ], ], ], 'DocumentAttribute' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeBoostingConfiguration' => [ 'type' => 'structure', 'members' => [ 'dateConfiguration' => [ 'shape' => 'DateAttributeBoostingConfiguration', ], 'numberConfiguration' => [ 'shape' => 'NumberAttributeBoostingConfiguration', ], 'stringConfiguration' => [ 'shape' => 'StringAttributeBoostingConfiguration', ], 'stringListConfiguration' => [ 'shape' => 'StringListAttributeBoostingConfiguration', ], ], 'union' => true, ], 'DocumentAttributeBoostingLevel' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH', ], ], 'DocumentAttributeBoostingOverrideMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeBoostingConfiguration', ], 'min' => 1, ], 'DocumentAttributeCondition' => [ 'type' => 'structure', 'required' => [ 'key', 'operator', ], 'members' => [ 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'operator' => [ 'shape' => 'DocumentEnrichmentConditionOperator', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeConfiguration' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'search' => [ 'shape' => 'Status', ], 'type' => [ 'shape' => 'AttributeType', ], ], ], 'DocumentAttributeConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttributeConfiguration', ], 'max' => 500, 'min' => 1, ], 'DocumentAttributeKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_][a-zA-Z0-9_-]*$', ], 'DocumentAttributeStringListValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DocumentAttributeTarget' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'attributeValueOperator' => [ 'shape' => 'AttributeValueOperator', ], 'key' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'dateValue' => [ 'shape' => 'Timestamp', ], 'longValue' => [ 'shape' => 'Long', ], 'stringListValue' => [ 'shape' => 'DocumentAttributeStringListValue', ], 'stringValue' => [ 'shape' => 'DocumentAttributeValueStringValueString', ], ], 'union' => true, ], 'DocumentAttributeValueStringValueString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'DocumentAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentAttribute', ], 'max' => 500, 'min' => 1, ], 'DocumentContent' => [ 'type' => 'structure', 'members' => [ 'blob' => [ 'shape' => 'Blob', ], 's3' => [ 'shape' => 'S3', ], ], 'union' => true, ], 'DocumentContentOperator' => [ 'type' => 'string', 'enum' => [ 'DELETE', ], ], 'DocumentDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentDetails', ], ], 'DocumentDetails' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'documentId' => [ 'shape' => 'DocumentId', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'status' => [ 'shape' => 'DocumentStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DocumentEnrichmentConditionOperator' => [ 'type' => 'string', 'enum' => [ 'GREATER_THAN', 'GREATER_THAN_OR_EQUALS', 'LESS_THAN', 'LESS_THAN_OR_EQUALS', 'EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', 'EXISTS', 'NOT_EXISTS', 'BEGINS_WITH', ], ], 'DocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'inlineConfigurations' => [ 'shape' => 'InlineDocumentEnrichmentConfigurations', ], 'postExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], 'preExtractionHookConfiguration' => [ 'shape' => 'HookConfiguration', ], ], ], 'DocumentId' => [ 'type' => 'string', 'max' => 1825, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'DocumentStatus' => [ 'type' => 'string', 'enum' => [ 'RECEIVED', 'PROCESSING', 'INDEXED', 'UPDATED', 'FAILED', 'DELETING', 'DELETED', 'DOCUMENT_FAILED_TO_INDEX', ], ], 'Documents' => [ 'type' => 'list', 'member' => [ 'shape' => 'Document', ], 'max' => 10, 'min' => 1, ], 'EligibleDataSource' => [ 'type' => 'structure', 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', ], ], ], 'EligibleDataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'EligibleDataSource', ], 'max' => 5, 'min' => 0, ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'InternalError', 'InvalidRequest', 'ResourceInactive', 'ResourceNotFound', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'ExampleChatMessage' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'ExampleChatMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExampleChatMessage', ], 'max' => 5, 'min' => 0, ], 'ExecutionId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'FailedDocument' => [ 'type' => 'structure', 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'id' => [ 'shape' => 'DocumentId', ], ], ], 'FailedDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedDocument', ], ], 'GetApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'GetApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'applicationArn' => [ 'shape' => 'ApplicationArn', ], 'applicationId' => [ 'shape' => 'ApplicationId', ], 'attachmentsConfiguration' => [ 'shape' => 'AppliedAttachmentsConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ApplicationStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForGetTopicConfigurations', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'blockedPhrases' => [ 'shape' => 'BlockedPhrasesConfiguration', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'responseScope' => [ 'shape' => 'ResponseScope', ], 'topicConfigurations' => [ 'shape' => 'TopicConfigurations', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'dataSourceArn' => [ 'shape' => 'DataSourceArn', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'indexId' => [ 'shape' => 'IndexId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'type' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'GetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'groupName', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], 'groupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'groupName', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'GetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'GroupStatusDetail', ], 'statusHistory' => [ 'shape' => 'GroupStatusDetails', ], ], ], 'GetIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'GetIndexResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'IndexName', ], 'documentAttributeConfigurations' => [ 'shape' => 'DocumentAttributeConfigurations', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'indexArn' => [ 'shape' => 'IndexArn', ], 'indexId' => [ 'shape' => 'IndexId', ], 'indexStatistics' => [ 'shape' => 'IndexStatistics', ], 'status' => [ 'shape' => 'IndexStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetPluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], ], ], 'GetPluginResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'PluginName', ], 'pluginArn' => [ 'shape' => 'PluginArn', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'serverUrl' => [ 'shape' => 'Url', ], 'state' => [ 'shape' => 'PluginState', ], 'type' => [ 'shape' => 'PluginType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], ], ], 'GetRetrieverResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'retrieverArn' => [ 'shape' => 'RetrieverArn', ], 'retrieverId' => [ 'shape' => 'RetrieverId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'RetrieverStatus', ], 'type' => [ 'shape' => 'RetrieverType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'members' => [ 'userAliases' => [ 'shape' => 'UserAliases', ], ], ], 'GetWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], ], ], 'GetWebExperienceResponse' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'authenticationConfiguration' => [ 'shape' => 'WebExperienceAuthConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'defaultEndpoint' => [ 'shape' => 'Url', ], 'error' => [ 'shape' => 'ErrorDetail', ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'status' => [ 'shape' => 'WebExperienceStatus', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'webExperienceArn' => [ 'shape' => 'WebExperienceArn', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], ], ], 'GroupMembers' => [ 'type' => 'structure', 'members' => [ 'memberGroups' => [ 'shape' => 'MemberGroups', ], 'memberUsers' => [ 'shape' => 'MemberUsers', ], ], ], 'GroupName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'GroupStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'PROCESSING', 'DELETING', 'DELETED', ], ], 'GroupStatusDetail' => [ 'type' => 'structure', 'members' => [ 'errorDetail' => [ 'shape' => 'ErrorDetail', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'GroupStatus', ], ], ], 'GroupStatusDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupStatusDetail', ], ], 'GroupSummary' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'GroupName', ], ], ], 'GroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupSummary', ], ], 'HookConfiguration' => [ 'type' => 'structure', 'members' => [ 'invocationCondition' => [ 'shape' => 'DocumentAttributeCondition', ], 'lambdaArn' => [ 'shape' => 'LambdaArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 's3BucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'Index' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'IndexName', ], 'indexId' => [ 'shape' => 'IndexId', ], 'status' => [ 'shape' => 'IndexStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IndexArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'IndexCapacityConfiguration' => [ 'type' => 'structure', 'members' => [ 'units' => [ 'shape' => 'IndexCapacityInteger', ], ], ], 'IndexCapacityInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'IndexName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'IndexStatistics' => [ 'type' => 'structure', 'members' => [ 'textDocumentStatistics' => [ 'shape' => 'TextDocumentStatistics', ], ], ], 'IndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', ], ], 'IndexedTextBytes' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'IndexedTextDocument' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'Indices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Index', ], ], 'InlineDocumentEnrichmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'condition' => [ 'shape' => 'DocumentAttributeCondition', ], 'documentContentOperator' => [ 'shape' => 'DocumentContentOperator', ], 'target' => [ 'shape' => 'DocumentAttributeTarget', ], ], ], 'InlineDocumentEnrichmentConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'InlineDocumentEnrichmentConfiguration', ], 'max' => 100, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KendraIndexConfiguration' => [ 'type' => 'structure', 'required' => [ 'indexId', ], 'members' => [ 'indexId' => [ 'shape' => 'KendraIndexId', ], ], ], 'KendraIndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws[a-zA-Z-]*:lambda:[a-z-]*-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?$', ], 'LicenseNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListApplications', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'applications' => [ 'shape' => 'Applications', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConversationsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListConversations', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], ], ], 'ListConversationsResponse' => [ 'type' => 'structure', 'members' => [ 'conversations' => [ 'shape' => 'Conversations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourceSyncJobsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSourcesSyncJobs', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'statusFilter' => [ 'shape' => 'DataSourceSyncJobStatus', 'location' => 'querystring', 'locationName' => 'syncStatus', ], ], ], 'ListDataSourceSyncJobsResponse' => [ 'type' => 'structure', 'members' => [ 'history' => [ 'shape' => 'DataSourceSyncJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDataSources', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'dataSources' => [ 'shape' => 'DataSources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceIds' => [ 'shape' => 'DataSourceIds', 'location' => 'querystring', 'locationName' => 'dataSourceIds', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListDocuments', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDocumentsResponse' => [ 'type' => 'structure', 'members' => [ 'documentDetailList' => [ 'shape' => 'DocumentDetailList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', 'updatedEarlierThan', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'querystring', 'locationName' => 'dataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListGroupsRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'updatedEarlierThan' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'updatedEarlierThan', ], ], ], 'ListGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'GroupSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIndicesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListIndices', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'indices' => [ 'shape' => 'Indices', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'conversationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListMessages', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], ], ], 'ListMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'messages' => [ 'shape' => 'Messages', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPluginsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListPlugins', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPluginsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'plugins' => [ 'shape' => 'Plugins', ], ], ], 'ListRetrieversRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListRetrieversRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListRetrieversResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievers' => [ 'shape' => 'Retrievers', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListWebExperiencesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResultsIntegerForListWebExperiencesRequest', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListWebExperiencesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'webExperiences' => [ 'shape' => 'WebExperiences', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResultsIntegerForGetTopicConfigurations' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListApplications' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListConversations' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListDataSources' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListDataSourcesSyncJobs' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListDocuments' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListGroupsRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MaxResultsIntegerForListIndices' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListMessages' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsIntegerForListPlugins' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListRetrieversRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxResultsIntegerForListWebExperiencesRequest' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MemberGroup' => [ 'type' => 'structure', 'required' => [ 'groupName', ], 'members' => [ 'groupName' => [ 'shape' => 'GroupName', ], 'type' => [ 'shape' => 'MembershipType', ], ], ], 'MemberGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberGroup', ], 'max' => 1000, 'min' => 1, ], 'MemberRelation' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'MemberUser' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'type' => [ 'shape' => 'MembershipType', ], 'userId' => [ 'shape' => 'DataSourceUserId', ], ], ], 'MemberUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberUser', ], 'max' => 1000, 'min' => 1, ], 'MembershipType' => [ 'type' => 'string', 'enum' => [ 'INDEX', 'DATASOURCE', ], ], 'Message' => [ 'type' => 'structure', 'members' => [ 'actionExecution' => [ 'shape' => 'ActionExecution', ], 'actionReview' => [ 'shape' => 'ActionReview', ], 'attachments' => [ 'shape' => 'AttachmentsOutput', ], 'body' => [ 'shape' => 'MessageBody', ], 'messageId' => [ 'shape' => 'String', ], 'sourceAttribution' => [ 'shape' => 'SourceAttributions', ], 'time' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'MessageType', ], ], ], 'MessageBody' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^\\P{C}*$}$', ], 'MessageId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'USER', 'SYSTEM', ], ], 'MessageUsefulness' => [ 'type' => 'string', 'enum' => [ 'USEFUL', 'NOT_USEFUL', ], ], 'MessageUsefulnessComment' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'MessageUsefulnessFeedback' => [ 'type' => 'structure', 'required' => [ 'submittedAt', 'usefulness', ], 'members' => [ 'comment' => [ 'shape' => 'MessageUsefulnessComment', ], 'reason' => [ 'shape' => 'MessageUsefulnessReason', ], 'submittedAt' => [ 'shape' => 'Timestamp', ], 'usefulness' => [ 'shape' => 'MessageUsefulness', ], ], ], 'MessageUsefulnessReason' => [ 'type' => 'string', 'enum' => [ 'NOT_FACTUALLY_CORRECT', 'HARMFUL_OR_UNSAFE', 'INCORRECT_OR_MISSING_SOURCES', 'NOT_HELPFUL', 'FACTUALLY_CORRECT', 'COMPLETE', 'RELEVANT_SOURCES', 'HELPFUL', 'NOT_BASED_ON_DOCUMENTS', 'NOT_COMPLETE', 'NOT_CONCISE', 'OTHER', ], ], 'Messages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], ], 'MetricValue' => [ 'type' => 'string', 'pattern' => '^(([1-9][0-9]*)|0)$', ], 'NativeIndexConfiguration' => [ 'type' => 'structure', 'required' => [ 'indexId', ], 'members' => [ 'boostingOverride' => [ 'shape' => 'DocumentAttributeBoostingOverrideMap', ], 'indexId' => [ 'shape' => 'IndexId', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 800, 'min' => 1, ], 'NumberAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], 'boostingType' => [ 'shape' => 'NumberAttributeBoostingType', ], ], ], 'NumberAttributeBoostingType' => [ 'type' => 'string', 'enum' => [ 'PRIORITIZE_LARGER_VALUES', 'PRIORITIZE_SMALLER_VALUES', ], ], 'OAuth2ClientCredentialConfiguration' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'secretArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'secretArn' => [ 'shape' => 'SecretArn', ], ], ], 'Plugin' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'PluginName', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'serverUrl' => [ 'shape' => 'Url', ], 'state' => [ 'shape' => 'PluginState', ], 'type' => [ 'shape' => 'PluginType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'PluginArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'PluginAuthConfiguration' => [ 'type' => 'structure', 'members' => [ 'basicAuthConfiguration' => [ 'shape' => 'BasicAuthConfiguration', ], 'oAuth2ClientCredentialConfiguration' => [ 'shape' => 'OAuth2ClientCredentialConfiguration', ], ], 'union' => true, ], 'PluginId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'PluginName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'PluginState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PluginType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_NOW', 'SALESFORCE', 'JIRA', 'ZENDESK', ], ], 'Plugins' => [ 'type' => 'list', 'member' => [ 'shape' => 'Plugin', ], ], 'Principal' => [ 'type' => 'structure', 'members' => [ 'group' => [ 'shape' => 'PrincipalGroup', ], 'user' => [ 'shape' => 'PrincipalUser', ], ], 'union' => true, ], 'PrincipalGroup' => [ 'type' => 'structure', 'required' => [ 'access', ], 'members' => [ 'access' => [ 'shape' => 'ReadAccessType', ], 'membershipType' => [ 'shape' => 'MembershipType', ], 'name' => [ 'shape' => 'GroupName', ], ], ], 'PrincipalUser' => [ 'type' => 'structure', 'required' => [ 'access', ], 'members' => [ 'access' => [ 'shape' => 'ReadAccessType', ], 'id' => [ 'shape' => 'UserId', ], 'membershipType' => [ 'shape' => 'MembershipType', ], ], ], 'Principals' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'conversationId', 'messageId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'uri', 'locationName' => 'conversationId', ], 'messageCopiedAt' => [ 'shape' => 'Timestamp', ], 'messageId' => [ 'shape' => 'SystemMessageId', 'location' => 'uri', 'locationName' => 'messageId', ], 'messageUsefulness' => [ 'shape' => 'MessageUsefulnessFeedback', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'userId', ], ], ], 'PutGroupRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'groupMembers', 'groupName', 'indexId', 'type', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'groupMembers' => [ 'shape' => 'GroupMembers', ], 'groupName' => [ 'shape' => 'GroupName', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'type' => [ 'shape' => 'MembershipType', ], ], ], 'PutGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReadAccessType' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseScope' => [ 'type' => 'string', 'enum' => [ 'ENTERPRISE_CONTENT_ONLY', 'EXTENDED_KNOWLEDGE_ENABLED', ], ], 'Retriever' => [ 'type' => 'structure', 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'retrieverId' => [ 'shape' => 'RetrieverId', ], 'status' => [ 'shape' => 'RetrieverStatus', ], 'type' => [ 'shape' => 'RetrieverType', ], ], ], 'RetrieverArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'RetrieverConfiguration' => [ 'type' => 'structure', 'members' => [ 'kendraIndexConfiguration' => [ 'shape' => 'KendraIndexConfiguration', ], 'nativeIndexConfiguration' => [ 'shape' => 'NativeIndexConfiguration', ], ], 'union' => true, ], 'RetrieverId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'RetrieverName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'RetrieverStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'FAILED', ], ], 'RetrieverType' => [ 'type' => 'string', 'enum' => [ 'NATIVE_INDEX', 'KENDRA_INDEX', ], ], 'Retrievers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Retriever', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'ruleType', ], 'members' => [ 'excludedUsersAndGroups' => [ 'shape' => 'UsersAndGroups', ], 'includedUsersAndGroups' => [ 'shape' => 'UsersAndGroups', ], 'ruleConfiguration' => [ 'shape' => 'RuleConfiguration', ], 'ruleType' => [ 'shape' => 'RuleType', ], ], ], 'RuleConfiguration' => [ 'type' => 'structure', 'members' => [ 'contentBlockerRule' => [ 'shape' => 'ContentBlockerRule', ], 'contentRetrievalRule' => [ 'shape' => 'ContentRetrievalRule', ], ], 'union' => true, ], 'RuleType' => [ 'type' => 'string', 'enum' => [ 'CONTENT_BLOCKER_RULE', 'CONTENT_RETRIEVAL_RULE', ], ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 10, 'min' => 0, ], 'S3' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3ObjectKey', ], ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SamlAttribute' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SamlConfiguration' => [ 'type' => 'structure', 'required' => [ 'metadataXML', 'roleArn', 'userIdAttribute', ], 'members' => [ 'metadataXML' => [ 'shape' => 'SamlMetadataXML', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'userGroupAttribute' => [ 'shape' => 'SamlAttribute', ], 'userIdAttribute' => [ 'shape' => 'SamlAttribute', ], ], ], 'SamlMetadataXML' => [ 'type' => 'string', 'max' => 10000000, 'min' => 1000, 'pattern' => '^.*$', ], 'SecretArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[-0-9a-zA-Z]+$', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 10, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SourceAttribution' => [ 'type' => 'structure', 'members' => [ 'citationNumber' => [ 'shape' => 'Integer', ], 'snippet' => [ 'shape' => 'String', ], 'textMessageSegments' => [ 'shape' => 'TextSegmentList', ], 'title' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'url' => [ 'shape' => 'String', ], ], ], 'SourceAttributions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceAttribution', ], ], 'StartDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'StartDataSourceSyncJobResponse' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'ExecutionId', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'StopDataSourceSyncJobRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'StopDataSourceSyncJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'attributeValueBoosting' => [ 'shape' => 'StringAttributeValueBoosting', ], 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], ], ], 'StringAttributeValueBoosting' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'StringAttributeValueBoostingLevel', ], 'max' => 10, 'min' => 1, ], 'StringAttributeValueBoostingLevel' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH', ], ], 'StringListAttributeBoostingConfiguration' => [ 'type' => 'structure', 'required' => [ 'boostingLevel', ], 'members' => [ 'boostingLevel' => [ 'shape' => 'DocumentAttributeBoostingLevel', ], ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[-0-9a-zA-Z]+$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'SyncSchedule' => [ 'type' => 'string', 'max' => 998, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'SystemMessageId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{35}$', ], 'SystemMessageOverride' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TextDocumentStatistics' => [ 'type' => 'structure', 'members' => [ 'indexedTextBytes' => [ 'shape' => 'IndexedTextBytes', ], 'indexedTextDocumentCount' => [ 'shape' => 'IndexedTextDocument', ], ], ], 'TextSegment' => [ 'type' => 'structure', 'members' => [ 'beginOffset' => [ 'shape' => 'Integer', ], 'endOffset' => [ 'shape' => 'Integer', ], ], ], 'TextSegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextSegment', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Title' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TopicConfiguration' => [ 'type' => 'structure', 'required' => [ 'name', 'rules', ], 'members' => [ 'description' => [ 'shape' => 'TopicDescription', ], 'exampleChatMessages' => [ 'shape' => 'ExampleChatMessages', ], 'name' => [ 'shape' => 'TopicConfigurationName', ], 'rules' => [ 'shape' => 'Rules', ], ], ], 'TopicConfigurationName' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]{0,35}$', ], 'TopicConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicConfiguration', ], 'max' => 10, 'min' => 0, ], 'TopicDescription' => [ 'type' => 'string', 'max' => 350, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'attachmentsConfiguration' => [ 'shape' => 'AttachmentsConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateChatControlsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'blockedPhrasesConfigurationUpdate' => [ 'shape' => 'BlockedPhrasesConfigurationUpdate', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'responseScope' => [ 'shape' => 'ResponseScope', ], 'topicConfigurationsToCreateOrUpdate' => [ 'shape' => 'TopicConfigurations', ], 'topicConfigurationsToDelete' => [ 'shape' => 'TopicConfigurations', ], ], ], 'UpdateChatControlsConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSourceId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'configuration' => [ 'shape' => 'DataSourceConfiguration', ], 'dataSourceId' => [ 'shape' => 'DataSourceId', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DataSourceName', ], 'documentEnrichmentConfiguration' => [ 'shape' => 'DocumentEnrichmentConfiguration', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'syncSchedule' => [ 'shape' => 'SyncSchedule', ], 'vpcConfiguration' => [ 'shape' => 'DataSourceVpcConfiguration', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIndexRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'indexId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'capacityConfiguration' => [ 'shape' => 'IndexCapacityConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'ApplicationName', ], 'documentAttributeConfigurations' => [ 'shape' => 'DocumentAttributeConfigurations', ], 'indexId' => [ 'shape' => 'IndexId', 'location' => 'uri', 'locationName' => 'indexId', ], ], ], 'UpdateIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePluginRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'pluginId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authConfiguration' => [ 'shape' => 'PluginAuthConfiguration', ], 'displayName' => [ 'shape' => 'PluginName', ], 'pluginId' => [ 'shape' => 'PluginId', 'location' => 'uri', 'locationName' => 'pluginId', ], 'serverUrl' => [ 'shape' => 'Url', ], 'state' => [ 'shape' => 'PluginState', ], ], ], 'UpdatePluginResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRetrieverRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'retrieverId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'configuration' => [ 'shape' => 'RetrieverConfiguration', ], 'displayName' => [ 'shape' => 'RetrieverName', ], 'retrieverId' => [ 'shape' => 'RetrieverId', 'location' => 'uri', 'locationName' => 'retrieverId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateRetrieverResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'userId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'userAliasesToDelete' => [ 'shape' => 'UserAliases', ], 'userAliasesToUpdate' => [ 'shape' => 'UserAliases', ], 'userId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'userAliasesAdded' => [ 'shape' => 'UserAliases', ], 'userAliasesDeleted' => [ 'shape' => 'UserAliases', ], 'userAliasesUpdated' => [ 'shape' => 'UserAliases', ], ], ], 'UpdateWebExperienceRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'webExperienceId', ], 'members' => [ 'applicationId' => [ 'shape' => 'ApplicationId', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authenticationConfiguration' => [ 'shape' => 'WebExperienceAuthConfiguration', ], 'samplePromptsControlMode' => [ 'shape' => 'WebExperienceSamplePromptsControlMode', ], 'subtitle' => [ 'shape' => 'WebExperienceSubtitle', ], 'title' => [ 'shape' => 'WebExperienceTitle', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', 'location' => 'uri', 'locationName' => 'webExperienceId', ], 'welcomeMessage' => [ 'shape' => 'WebExperienceWelcomeMessage', ], ], ], 'UpdateWebExperienceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(https?|ftp|file)://([^\\s]*)$', ], 'UserAlias' => [ 'type' => 'structure', 'required' => [ 'userId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'DataSourceId', ], 'indexId' => [ 'shape' => 'IndexId', ], 'userId' => [ 'shape' => 'String', ], ], ], 'UserAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAlias', ], ], 'UserGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'UserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'UserIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'UserMessage' => [ 'type' => 'string', 'max' => 7000, 'min' => 1, ], 'UsersAndGroups' => [ 'type' => 'structure', 'members' => [ 'userGroups' => [ 'shape' => 'UserGroups', ], 'userIds' => [ 'shape' => 'UserIds', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fields' => [ 'shape' => 'ValidationExceptionFields', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'UNKNOWN_OPERATION', ], ], 'WebExperience' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'defaultEndpoint' => [ 'shape' => 'Url', ], 'status' => [ 'shape' => 'WebExperienceStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'webExperienceId' => [ 'shape' => 'WebExperienceId', ], ], ], 'WebExperienceArn' => [ 'type' => 'string', 'max' => 1284, 'min' => 0, 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}$', ], 'WebExperienceAuthConfiguration' => [ 'type' => 'structure', 'members' => [ 'samlConfiguration' => [ 'shape' => 'SamlConfiguration', ], ], 'union' => true, ], 'WebExperienceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]*$', ], 'WebExperienceSamplePromptsControlMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'WebExperienceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'PENDING_AUTH_CONFIG', ], ], 'WebExperienceSubtitle' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'WebExperienceTitle' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^\\P{C}*$', ], 'WebExperienceWelcomeMessage' => [ 'type' => 'string', 'max' => 300, 'min' => 0, ], 'WebExperiences' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebExperience', ], ], ],];
