/*!
 * BootstrapVue 2.23.1
 *
 * @link https://bootstrap-vue.org
 * @source https://github.com/bootstrap-vue/bootstrap-vue
 * @copyright (c) 2016-2022 BootstrapVue
 * @license MIT
 * https://github.com/bootstrap-vue/bootstrap-vue/blob/master/LICENSE
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("vue")):"function"==typeof define&&define.amd?define(["vue"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrapVue=e(t.Vue)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=e(t);function n(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function r(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?n(Object(i),!0).forEach((function(e){c(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function l(t,e,i){return e&&a(t.prototype,e),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function c(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");Object.defineProperty(t,"prototype",{value:Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),writable:!1}),e&&h(t,e)}function d(t){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},d(t)}function h(t,e){return h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},h(t,e)}function f(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function p(t,e,i){return p=f()?Reflect.construct:function(t,e,i){var n=[null];n.push.apply(n,e);var r=new(Function.bind.apply(t,n));return i&&h(r,i.prototype),r},p.apply(null,arguments)}function m(t){var e="function"==typeof Map?new Map:void 0;return m=function(t){if(null===t||(i=t,-1===Function.toString.call(i).indexOf("[native code]")))return t;var i;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if("undefined"!=typeof e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return p(t,arguments,d(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),h(n,t)},m(t)}function v(t,e){if(null==t)return{};var i,n,r=function(t,e){if(null==t)return{};var i,n,r={},o=Object.keys(t);for(n=0;n<o.length;n++)i=o[n],e.indexOf(i)>=0||(r[i]=t[i]);return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)i=o[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(r[i]=t[i])}return r}function g(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function b(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return g(t)}function y(t){var e=f();return function(){var i,n=d(t);if(e){var r=d(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return b(this,i)}}function T(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=d(t)););return t}function w(){return w="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var n=T(t,e);if(n){var r=Object.getOwnPropertyDescriptor(n,e);return r.get?r.get.call(arguments.length<3?t:i):r.value}},w.apply(this,arguments)}function C(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==i)return;var n,r,o=[],s=!0,a=!1;try{for(i=i.call(t);!(s=(n=i.next()).done)&&(o.push(n.value),!e||o.length!==e);s=!0);}catch(t){a=!0,r=t}finally{try{s||null==i.return||i.return()}finally{if(a)throw r}}return o}(t,e)||k(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(t){return function(t){if(Array.isArray(t))return x(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||k(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(t,e){if(t){if("string"==typeof t)return x(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?x(t,e):void 0}}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var $=function(){return($=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},B=/-(\w)/g,D=/:(.*)/,_=/;(?![^(]*\))/g;function F(t,e){return e?e.toUpperCase():""}function P(t){for(var e,i={},n=0,r=t.split(_);n<r.length;n++){var o=r[n].split(D),s=o[0],a=o[1];(s=s.trim())&&("string"==typeof a&&(a=a.trim()),i[(e=s,e.replace(B,F))]=a)}return i}function I(){for(var t,e,i={},n=arguments.length;n--;)for(var r=0,o=Object.keys(arguments[n]);r<o.length;r++)switch(t=o[r]){case"class":case"style":case"directives":if(Array.isArray(i[t])||(i[t]=[]),"style"===t){var s=void 0;s=Array.isArray(arguments[n].style)?arguments[n].style:[arguments[n].style];for(var a=0;a<s.length;a++){var l=s[a];"string"==typeof l&&(s[a]=P(l))}arguments[n].style=s}i[t]=i[t].concat(arguments[n][t]);break;case"staticClass":if(!arguments[n][t])break;void 0===i[t]&&(i[t]=""),i[t]&&(i[t]+=" "),i[t]+=arguments[n][t].trim();break;case"on":case"nativeOn":i[t]||(i[t]={});for(var c=0,u=Object.keys(arguments[n][t]||{});c<u.length;c++)e=u[c],i[t][e]?i[t][e]=[].concat(i[t][e],arguments[n][t][e]):i[t][e]=arguments[n][t][e];break;case"attrs":case"props":case"domProps":case"scopedSlots":case"staticStyle":case"hook":case"transition":i[t]||(i[t]={}),i[t]=$({},arguments[n][t],i[t]);break;default:i[t]||(i[t]=arguments[n][t])}return i}var O=i.default.version.startsWith("3"),E=O?"ref_for":"refInFor",V=["class","staticClass","style","attrs","props","domProps","on","nativeOn","directives","scopedSlots","slot","key","ref","refInFor"],L=i.default.extend.bind(i.default);if(O){var R=i.default.extend,A=["router-link","transition","transition-group"],M=i.default.vModelDynamic.created,H=i.default.vModelDynamic.beforeUpdate;i.default.vModelDynamic.created=function(t,e,i){M.call(this,t,e,i),t._assign||(t._assign=function(){})},i.default.vModelDynamic.beforeUpdate=function(t,e,i){H.call(this,t,e,i),t._assign||(t._assign=function(){})},L=function(t){if("object"===o(t)&&t.render&&!t.__alreadyPatched){var e=t.render;t.__alreadyPatched=!0,t.render=function(i){var n=function(t,e,n){var s=void 0===n?[]:[Array.isArray(n)?n.filter(Boolean):n],a="string"==typeof t&&!A.includes(t);if(!(e&&"object"===o(e)&&!Array.isArray(e)))return i.apply(void 0,[t,e].concat(s));var l=e.attrs,c=e.props,u=r(r({},v(e,["attrs","props"])),{},{attrs:l,props:a?{}:c});return"router-link"!==t||u.slots||u.scopedSlots||(u.scopedSlots={$hasNormal:function(){}}),i.apply(void 0,[t,u].concat(s))};if(t.functional){var s,a,l=arguments[1],c=r({},l);c.data={attrs:r({},l.data.attrs||{}),props:r({},l.data.props||{})},Object.keys(l.data||{}).forEach((function(t){V.includes(t)?c.data[t]=l.data[t]:t in l.props?c.data.props[t]=l.data[t]:t.startsWith("on")||(c.data.attrs[t]=l.data[t])}));var u=["_ctx"],d=(null===(s=l.children)||void 0===s||null===(a=s.default)||void 0===a?void 0:a.call(s))||l.children;return d&&0===Object.keys(c.children).filter((function(t){return!u.includes(t)})).length?delete c.children:c.children=d,c.data.on=l.listeners,e.call(this,n,c)}return e.call(this,n)}}return R.call(this,t)}.bind(i.default)}var z=i.default.nextTick,N="undefined"!=typeof window,j="undefined"!=typeof document,G="undefined"!=typeof navigator,W="undefined"!=typeof Promise,U="undefined"!=typeof MutationObserver||"undefined"!=typeof WebKitMutationObserver||"undefined"!=typeof MozMutationObserver,Y=N&&j&&G,q=N?window:{},K=j?document:{},X=G?navigator:{},Z=(X.userAgent||"").toLowerCase(),J=Z.indexOf("jsdom")>0;/msie|trident/.test(Z);var Q,tt,et,it=function(){var t=!1;if(Y)try{var e={get passive(){t=!0}};q.addEventListener("test",e,e),q.removeEventListener("test",e,e)}catch(e){t=!1}return t}(),nt=Y&&("ontouchstart"in K.documentElement||X.maxTouchPoints>0),rt=Y&&Boolean(q.PointerEvent||q.MSPointerEvent),ot=Y&&"IntersectionObserver"in q&&"IntersectionObserverEntry"in q&&"intersectionRatio"in q.IntersectionObserverEntry.prototype,st="BvConfig",at=["xs","sm","md","lg","xl"],lt=/\[(\d+)]/g,ct=/^(BV?)/,ut=/^\d+$/,dt=/^\..+/,ht=/^#/,ft=/^#[A-Za-z]+[\w\-:.]*$/,pt=/(<([^>]+)>)/gi,mt=/\B([A-Z])/g,vt=/([a-z])([A-Z])/g,gt=/^[0-9]*\.?[0-9]+$/,bt=/\+/g,yt=/[-/\\^$*+?.()|[\]{}]/g,Tt=/[\s\uFEFF\xA0]+/g,wt=/\s+/,Ct=/\/\*$/,St=/(\s|^)(\w)/g,kt=/^\s+/,xt=/_/g,$t=/-(\w)/g,Bt=/^\d+-\d\d?-\d\d?(?:\s|T|$)/,Dt=/-|\s|T/,_t=/^([0-1]?[0-9]|2[0-3]):[0-5]?[0-9](:[0-5]?[0-9])?$/,Ft=/^.*(#[^#]+)$/,Pt=/%2C/g,It=/[!'()*]/g,Ot=/^(\?|#|&)/,Et=/^\d+(\.\d*)?[/:]\d+(\.\d*)?$/,Vt=/[/:]/,Lt=/^col-/,Rt=/^BIcon/,At=/-u-.+/,Element=N?q.Element:function(t){u(Element,t);var e=y(Element);function Element(){return s(this,Element),e.apply(this,arguments)}return Element}(m(Object)),HTMLElement=N?q.HTMLElement:function(t){u(HTMLElement,t);var e=y(HTMLElement);function HTMLElement(){return s(this,HTMLElement),e.apply(this,arguments)}return HTMLElement}(Element),SVGElement=N?q.SVGElement:function(t){u(SVGElement,t);var e=y(SVGElement);function SVGElement(){return s(this,SVGElement),e.apply(this,arguments)}return SVGElement}(Element),Mt=N?q.File:function(t){u(i,t);var e=y(i);function i(){return s(this,i),e.apply(this,arguments)}return i}(m(Object)),Ht=function(t){return o(t)},zt=function(t){return void 0===t},Nt=function(t){return null===t},jt=function(t){return zt(t)||Nt(t)},Gt=function(t){return"function"===Ht(t)},Wt=function(t){return"boolean"===Ht(t)},Ut=function(t){return"string"===Ht(t)},Yt=function(t){return"number"===Ht(t)},qt=function(t){return gt.test(String(t))},Kt=function(t){return Array.isArray(t)},Xt=function(t){return null!==t&&"object"===o(t)},Zt=function(t){return"[object Object]"===Object.prototype.toString.call(t)},Jt=function(t){return t instanceof Date},Qt=function(t){return t instanceof Event},te=function(t){return"RegExp"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)},ee=function(){return Object.assign.apply(Object,arguments)},ie=function(t,e){return Object.create(t,e)},ne=function(t,e){return Object.defineProperties(t,e)},re=function(t,e,i){return Object.defineProperty(t,e,i)},oe=function(t){return Object.getOwnPropertyNames(t)},se=function(t){return Object.keys(t)},ae=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},le=function(t){return r({},t)},ce=function(t,e){return se(t).filter((function(t){return-1!==e.indexOf(t)})).reduce((function(e,i){return r(r({},e),{},c({},i,t[i]))}),{})},ue=function(t,e){return se(t).filter((function(t){return-1===e.indexOf(t)})).reduce((function(e,i){return r(r({},e),{},c({},i,t[i]))}),{})},de=function t(e,i){return Xt(e)&&Xt(i)&&se(i).forEach((function(n){Xt(i[n])?(e[n]&&Xt(e[n])||(e[n]=i[n]),t(e[n],i[n])):ee(e,c({},n,i[n]))})),e},he=function(t){return se(t).sort().reduce((function(e,i){return r(r({},e),{},c({},i,t[i]))}),{})},fe=function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return Kt(e)?e.reduce((function(e,i){return[].concat(S(e),[t(i,i)])}),[]):Zt(e)?se(e).reduce((function(i,n){return r(r({},i),{},c({},n,t(e[n],e[n])))}),{}):i},pe=function(t){return t},me=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(!(e=Kt(e)?e.join("."):e)||!Xt(t))return i;if(e in t)return t[e];var n=(e=String(e).replace(lt,".$1")).split(".").filter(pe);return 0===n.length?i:n.every((function(e){return Xt(t)&&e in t&&!jt(t=t[e])}))?t:Nt(t)?null:i},ve=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=me(t,e);return jt(n)?i:n},ge=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i="undefined"!=typeof process&&process&&process.env||{};return t?i[t]||e:i},be=function(){return ge("BOOTSTRAP_VUE_NO_WARN")||"production"===ge("NODE_ENV")},ye=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;be()||console.warn("[BootstrapVue warn]: ".concat(e?"".concat(e," - "):"").concat(t))},Te=function(t){return!Y&&(ye("".concat(t,": Can not be called during SSR.")),!0)},we=function(t){return!W&&(ye("".concat(t,": Requires Promise support.")),!0)},Ce=function(){function t(){s(this,t),this.$_config={}}return l(t,[{key:"setConfig",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(Zt(e)){var i=oe(e);i.forEach((function(i){var n=e[i];"breakpoints"===i?!Kt(n)||n.length<2||n.some((function(t){return!Ut(t)||0===t.length}))?ye('"breakpoints" must be an array of at least 2 breakpoint names',st):t.$_config[i]=fe(n):Zt(n)&&(t.$_config[i]=oe(n).reduce((function(t,e){return zt(n[e])||(t[e]=fe(n[e])),t}),t.$_config[i]||{}))}))}}},{key:"resetConfig",value:function(){this.$_config={}}},{key:"getConfig",value:function(){return fe(this.$_config)}},{key:"getConfigValue",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return fe(me(this.$_config,t,e))}}]),t}(),Se=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.default;e.prototype.$bvConfig=i.default.prototype.$bvConfig=e.prototype.$bvConfig||i.default.prototype.$bvConfig||new Ce,e.prototype.$bvConfig.setConfig(t)},ke=(Q=!1,tt=["Multiple instances of Vue detected!","You may need to set up an alias for Vue in your bundler config.","See: https://bootstrap-vue.org/docs#using-module-bundlers"].join("\n"),function(t){Q||i.default===t||J||ye(tt),Q=!0}),xe=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.components,i=t.directives,n=t.plugins,r=function t(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.installed||(t.installed=!0,ke(r),Se(o,r),_e(r,e),Pe(r,i),Be(r,n))};return r.installed=!1,r},$e=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(r({},e),{},{install:xe(t)})},Be=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)i&&e[i]&&t.use(e[i])},De=function(t,e,i){t&&e&&i&&t.component(e,i)},_e=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)De(t,i,e[i])},Fe=function(t,e,i){t&&e&&i&&t.directive(e.replace(/^VB/,"B"),i)},Pe=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)Fe(t,i,e[i])},Ie="BAlert",Oe="BAspect",Ee="BAvatar",Ve="BAvatarGroup",Le="BBadge",Re="BBreadcrumb",Ae="BBreadcrumbItem",Me="BBreadcrumbLink",He="BButton",ze="BButtonClose",Ne="BButtonGroup",je="BButtonToolbar",Ge="BCalendar",We="BCard",Ue="BCardBody",Ye="BCardFooter",qe="BCardGroup",Ke="BCardHeader",Xe="BCardImg",Ze="BCardImgLazy",Je="BCardSubTitle",Qe="BCardText",ti="BCardTitle",ei="BCarousel",ii="BCarouselSlide",ni="BCol",ri="BCollapse",oi="BContainer",si="BDropdown",ai="BDropdownDivider",li="BDropdownForm",ci="BDropdownGroup",ui="BDropdownHeader",di="BDropdownItem",hi="BDropdownItemButton",fi="BDropdownText",pi="BEmbed",mi="BForm",vi="BFormCheckbox",gi="BFormCheckboxGroup",bi="BFormDatalist",yi="BFormDatepicker",Ti="BFormFile",wi="BFormGroup",Ci="BFormInput",Si="BFormInvalidFeedback",ki="BFormRadio",xi="BFormRadioGroup",$i="BFormRating",Bi="BFormRow",Di="BFormSelect",_i="BFormSelectOption",Fi="BFormSelectOptionGroup",Pi="BFormSpinbutton",Ii="BFormTag",Oi="BFormTags",Ei="BFormText",Vi="BFormTextarea",Li="BFormTimepicker",Ri="BFormValidFeedback",Ai="BIcon",Mi="BImg",Hi="BImgLazy",zi="BInputGroup",Ni="BInputGroupAddon",ji="BInputGroupAppend",Gi="BInputGroupPrepend",Wi="BInputGroupText",Ui="BJumbotron",Yi="BLink",qi="BListGroup",Ki="BListGroupItem",Xi="BMedia",Zi="BMediaAside",Ji="BMediaBody",Qi="BModal",tn="BNav",en="BNavbar",nn="BNavbarBrand",rn="BNavbarNav",on="BNavbarToggle",sn="BNavForm",an="BNavItem",ln="BNavItemDropdown",cn="BOverlay",un="BPagination",dn="BPaginationNav",hn="BPopover",fn="BProgress",pn="BProgressBar",mn="BRow",vn="BSidebar",gn="BSkeleton",bn="BSkeletonIcon",yn="BSkeletonImg",Tn="BSkeletonTable",wn="BSkeletonWrapper",Cn="BSpinner",Sn="BTab",kn="BTable",xn="BTableCell",$n="BTableLite",Bn="BTableSimple",Dn="BTabs",_n="BTbody",Fn="BTfoot",Pn="BThead",In="BTime",On="BToast",En="BToaster",Vn="BTooltip",Ln="BVTransporter",Rn="blur",An="cancel",Mn="change",Hn="click",zn="close",Nn="context",jn="context-changed",Gn="destroyed",Wn="disable",Un="disabled",Yn="enable",qn="enabled",Kn="filtered",Xn="first",Zn="focusin",Jn="focusout",Qn="head-clicked",tr="hidden",er="hide",ir="input",nr="last",rr="mouseenter",or="mouseleave",sr="next",ar="open",lr="page-click",cr="prev",ur="refreshed",dr="row-clicked",hr="selected",fr="show",pr="shown",mr="sliding-end",vr="toggle",gr=O?"vnodeBeforeUnmount":"hook:beforeDestroy",br=O?"vNodeUnmounted":"hook:destroyed",yr="bv",Tr={passive:!0},wr={passive:!0,capture:!1},Cr=void 0,Sr=Array,kr=Boolean,xr=Date,$r=Function,Br=Number,Dr=Object,_r=RegExp,Fr=String,Pr=[Sr,$r],Ir=[Sr,Dr],Or=[Sr,Dr,Fr],Er=[Sr,Fr],Vr=[kr,Br],Lr=[kr,Br,Fr],Rr=[kr,Fr],Ar=[xr,Fr],Mr=[$r,Fr],Hr=[Br,Fr],zr=[Br,Dr,Fr],Nr=[Dr,$r],jr=[Dr,Fr],Gr="append",Wr="badge",Ur="bottom-row",Yr="button-content",qr="custom-foot",Kr="default",Xr="empty",Zr="file-name",Jr="first",Qr="footer",to="header",eo="label",io="lead",no="modal-cancel",ro="modal-ok",oo="modal-title",so="prepend",ao="row-details",lo="table-busy",co="table-caption",uo="table-colgroup",ho="thead-top",fo="title",po="top-row",mo=function(){return Array.from.apply(Array,arguments)},vo=function(t,e){return-1!==t.indexOf(e)},go=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return Array.prototype.concat.apply([],e)},bo=function(t,e){var i=Gt(e)?e:function(){return e};return Array.apply(null,{length:t}).map(i)},yo=function(t){return t.reduce((function(t,e){return go(t,e)}),[])},To=function t(e){return e.reduce((function(e,i){return go(e,Array.isArray(i)?t(i):i)}),[])},wo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(t=go(t).filter(pe)).some((function(t){return e[t]||i[t]}))},Co=function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=go(t).filter(pe);for(var o=0;o<t.length&&!e;o++){var s=t[o];e=n[s]||r[s]}return Gt(e)?e(i):e},So=L({methods:{hasNormalizedSlot:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Kr,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.$scopedSlots,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.$slots;return wo(t,e,i)},normalizeSlot:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Kr,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.$scopedSlots,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.$slots,r=Co(t,e,i,n);return r?go(r):r}}}),ko=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,i=parseInt(t,10);return isNaN(i)?e:i},xo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,i=parseFloat(t);return isNaN(i)?e:i},$o=function(t,e){return xo(t).toFixed(ko(e,0))},Bo=function(t){return t.replace(mt,"-$1").toLowerCase()},Do=function(t){return(t=Bo(t).replace($t,(function(t,e){return e?e.toUpperCase():""}))).charAt(0).toUpperCase()+t.slice(1)},_o=function(t){return t.replace(xt," ").replace(vt,(function(t,e,i){return e+" "+i})).replace(St,(function(t,e,i){return e+i.toUpperCase()}))},Fo=function(t){return(t=Ut(t)?t.trim():String(t)).charAt(0).toUpperCase()+t.slice(1)},Po=function(t){return t.replace(yt,"\\$&")},Io=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return jt(t)?"":Kt(t)||Zt(t)&&t.toString===Object.prototype.toString?JSON.stringify(t,null,e):String(t)},Oo=function(t){return Io(t).trim()},Eo=function(t){return Io(t).toLowerCase()},Vo=Element.prototype,Lo=["button","[href]:not(.disabled)","input","select","textarea","[tabindex]","[contenteditable]"].map((function(t){return"".concat(t,":not(:disabled):not([disabled])")})).join(", "),Ro=Vo.matches||Vo.msMatchesSelector||Vo.webkitMatchesSelector,Ao=Vo.closest||function(t){var e=this;do{if(Xo(e,t))return e;e=e.parentElement||e.parentNode}while(!Nt(e)&&e.nodeType===Node.ELEMENT_NODE);return null},Mo=(q.requestAnimationFrame||q.webkitRequestAnimationFrame||q.mozRequestAnimationFrame||q.msRequestAnimationFrame||q.oRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(q),Ho=q.MutationObserver||q.WebKitMutationObserver||q.MozMutationObserver||null,zo=function(t){return!(!t||t.nodeType!==Node.ELEMENT_NODE)},No=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=K.activeElement;return e&&!t.some((function(t){return t===e}))?e:null},jo=function(t,e){return Io(t).toLowerCase()===Io(e).toLowerCase()},Go=function(t){return zo(t)&&t===No()},Wo=function(t){if(!zo(t)||!t.parentNode||!Jo(K.body,t))return!1;if("none"===cs(t,"display"))return!1;var e=us(t);return!!(e&&e.height>0&&e.width>0)},Uo=function(t){return!zo(t)||t.disabled||ss(t,"disabled")||is(t,"disabled")},Yo=function(t){return zo(t)&&t.offsetHeight},qo=function(t,e){return mo((zo(e)?e:K).querySelectorAll(t))},Ko=function(t,e){return(zo(e)?e:K).querySelector(t)||null},Xo=function(t,e){return!!zo(t)&&Ro.call(t,e)},Zo=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!zo(e))return null;var n=Ao.call(e,t);return i?n:n===e?null:n},Jo=function(t,e){return!(!t||!Gt(t.contains))&&t.contains(e)},Qo=function(t){return K.getElementById(/^#/.test(t)?t.slice(1):t)||null},ts=function(t,e){e&&zo(t)&&t.classList&&t.classList.add(e)},es=function(t,e){e&&zo(t)&&t.classList&&t.classList.remove(e)},is=function(t,e){return!!(e&&zo(t)&&t.classList)&&t.classList.contains(e)},ns=function(t,e,i){e&&zo(t)&&t.setAttribute(e,i)},rs=function(t,e){e&&zo(t)&&t.removeAttribute(e)},os=function(t,e){return e&&zo(t)?t.getAttribute(e):null},ss=function(t,e){return e&&zo(t)?t.hasAttribute(e):null},as=function(t,e,i){e&&zo(t)&&(t.style[e]=i)},ls=function(t,e){e&&zo(t)&&(t.style[e]="")},cs=function(t,e){return e&&zo(t)&&t.style[e]||null},us=function(t){return zo(t)?t.getBoundingClientRect():null},ds=function(t){var e=q.getComputedStyle;return e&&zo(t)?e(t):{}},hs=function(){return q.getSelection?q.getSelection():null},fs=function(t){var e={top:0,left:0};if(!zo(t)||0===t.getClientRects().length)return e;var i=us(t);if(i){var n=t.ownerDocument.defaultView;e.top=i.top+n.pageYOffset,e.left=i.left+n.pageXOffset}return e},ps=function(t){var e={top:0,left:0};if(!zo(t))return e;var i={top:0,left:0},n=ds(t);if("fixed"===n.position)e=us(t)||e;else{e=fs(t);for(var r=t.ownerDocument,o=t.offsetParent||r.documentElement;o&&(o===r.body||o===r.documentElement)&&"static"===ds(o).position;)o=o.parentNode;if(o&&o!==t&&o.nodeType===Node.ELEMENT_NODE){i=fs(o);var s=ds(o);i.top+=xo(s.borderTopWidth,0),i.left+=xo(s.borderLeftWidth,0)}}return{top:e.top-i.top-xo(n.marginTop,0),left:e.left-i.left-xo(n.marginLeft,0)}},ms=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;return qo(Lo,t).filter(Wo).filter((function(t){return t.tabIndex>-1&&!t.disabled}))},vs=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{t.focus(e)}catch(t){}return Go(t)},gs=function(t){try{t.blur()}catch(t){}return!Go(t)},bs=function(t){var e=ie(null);return function(){for(var i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];var o=JSON.stringify(n);return e[o]=e[o]||t.apply(null,n)}},ys=i.default.prototype,Ts=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=ys.$bvConfig;return i?i.getConfigValue(t,e):fe(e)},ws=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return e?Ts("".concat(t,".").concat(e),i):Ts(t,{})},Cs=function(){return Ts("breakpoints",at)},Ss=bs((function(){return Cs()})),ks=bs((function(){var t=fe(Ss());return t[0]="",t})),xs=function(t,e){return t+Fo(e)},$s=function(t,e){return i=e.replace(t,""),(i=Ut(i)?i.trim():String(i)).charAt(0).toLowerCase()+i.slice(1);var i},Bs=function(t,e){return e+(t?Fo(t):"")},Ds=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Cr,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,o=!0===i;return n=o?n:i,r(r(r({},t?{type:t}:{}),o?{required:o}:zt(e)?{}:{default:Xt(e)?function(){return e}:e}),zt(n)?{}:{validator:n})},_s=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:pe;if(Kt(t))return t.map(e);var i={};for(var n in t)ae(t,n)&&(i[e(n)]=Xt(t[n])?le(t[n]):t[n]);return i},Fs=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:pe;return(Kt(t)?t.slice():se(t)).reduce((function(t,n){return t[i(n)]=e[n],t}),{})},Ps=function(t,e,i){return r(r({},fe(t)),{},{default:function(){var n=ws(i,e,t.default);return Gt(n)?n():n}})},Is=function(t,e){return se(t).reduce((function(i,n){return r(r({},i),{},c({},n,Ps(t[n],n,e)))}),{})},Os=Ps({},"","").default.name,Es=function(t){return Gt(t)&&t.name&&t.name!==Os},Vs=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.type,n=void 0===i?Cr:i,r=e.defaultValue,o=void 0===r?void 0:r,s=e.validator,a=void 0===s?void 0:s,l=e.event,u=void 0===l?ir:l,d=c({},t,Ds(n,o,a)),h=L({model:{prop:t,event:u},props:d});return{mixin:h,props:d,prop:t,event:u}},Ls=function(t){return it?Xt(t)?t:{capture:!!t||!1}:!!(Xt(t)?t.capture:t)},Rs=function(t,e,i,n){t&&t.addEventListener&&t.addEventListener(e,i,Ls(n))},As=function(t,e,i,n){t&&t.removeEventListener&&t.removeEventListener(e,i,Ls(n))},Ms=function(t){for(var e=t?Rs:As,i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];e.apply(void 0,n)},Hs=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.preventDefault,n=void 0===i||i,r=e.propagation,o=void 0===r||r,s=e.immediatePropagation,a=void 0!==s&&s;n&&t.preventDefault(),o&&t.stopPropagation(),a&&t.stopImmediatePropagation()},zs=function(t){return Bo(t.replace(ct,""))},Ns=function(t,e){return[yr,zs(t),e].join("::")},js=function(t,e){return[yr,e,zs(t)].join("::")},Gs=Is({ariaLabel:Ds(Fr,"Close"),content:Ds(Fr,"&times;"),disabled:Ds(kr,!1),textVariant:Ds(Fr)},ze),Ws=L({name:ze,functional:!0,props:Gs,render:function(t,e){var i=e.props,n=e.data,r=e.slots,o=e.scopedSlots,s=r(),a=o||{},l={staticClass:"close",class:c({},"text-".concat(i.textVariant),i.textVariant),attrs:{type:"button",disabled:i.disabled,"aria-label":i.ariaLabel?String(i.ariaLabel):null},on:{click:function(t){i.disabled&&Qt(t)&&Hs(t)}}};return wo(Kr,a,s)||(l.domProps={innerHTML:i.content}),t("button",I(n,l),Co(Kr,{},a,s))}}),Us={name:"",enterClass:"",enterActiveClass:"",enterToClass:"show",leaveClass:"show",leaveActiveClass:"",leaveToClass:""},Ys=r(r({},Us),{},{enterActiveClass:"fade",leaveActiveClass:"fade"}),qs={appear:Ds(kr,!1),mode:Ds(Fr),noFade:Ds(kr,!1),transProps:Ds(Dr)},Ks=L({name:"BVTransition",functional:!0,props:qs,render:function(t,e){var i=e.children,n=e.data,o=e.props,s=o.transProps;Zt(s)||(s=o.noFade?Us:Ys,o.appear&&(s=r(r({},s),{},{appear:!0,appearClass:s.enterClass,appearActiveClass:s.enterActiveClass,appearToClass:s.enterToClass}))),s=r(r({mode:o.mode},s),{},{css:!0});var a=r({},n);return delete a.props,t("transition",I(a,{props:s}),i)}}),Xs=Vs("show",{type:Lr,defaultValue:!1}),Zs=Xs.mixin,Js=Xs.props,Qs=Xs.prop,ta=Xs.event,ea=function(t){return""===t||Wt(t)?0:(t=ko(t,0))>0?t:0},ia=function(t){return""===t||!0===t||!(ko(t,0)<1)&&!!t},na=Is(he(r(r({},Js),{},{dismissLabel:Ds(Fr,"Close"),dismissible:Ds(kr,!1),fade:Ds(kr,!1),variant:Ds(Fr,"info")})),Ie),ra=$e({components:{BAlert:L({name:Ie,mixins:[Zs,So],props:na,data:function(){return{countDown:0,localShow:ia(this[Qs])}},watch:(et={},c(et,Qs,(function(t){this.countDown=ea(t),this.localShow=ia(t)})),c(et,"countDown",(function(t){var e=this;this.clearCountDownInterval();var i=this[Qs];qt(i)&&(this.$emit("dismiss-count-down",t),i!==t&&this.$emit(ta,t),t>0?(this.localShow=!0,this.$_countDownTimeout=setTimeout((function(){e.countDown--}),1e3)):this.$nextTick((function(){Mo((function(){e.localShow=!1}))})))})),c(et,"localShow",(function(t){var e=this[Qs];t||!this.dismissible&&!qt(e)||this.$emit("dismissed"),qt(e)||e===t||this.$emit(ta,t)})),et),created:function(){this.$_filterTimer=null;var t=this[Qs];this.countDown=ea(t),this.localShow=ia(t)},beforeDestroy:function(){this.clearCountDownInterval()},methods:{dismiss:function(){this.clearCountDownInterval(),this.countDown=0,this.localShow=!1},clearCountDownInterval:function(){clearTimeout(this.$_countDownTimeout),this.$_countDownTimeout=null}},render:function(t){var e=t();if(this.localShow){var i=this.dismissible,n=this.variant,r=t();i&&(r=t(Ws,{attrs:{"aria-label":this.dismissLabel},on:{click:this.dismiss}},[this.normalizeSlot("dismiss")])),e=t("div",{staticClass:"alert",class:c({"alert-dismissible":i},"alert-".concat(n),n),attrs:{role:"alert","aria-live":"polite","aria-atomic":!0},key:this._uid},[r,this.normalizeSlot()])}return t(Ks,{props:{noFade:!this.fade}},[e])}})}}),oa=Math.min,sa=Math.max,aa=Math.abs,la=Math.ceil,ca=Math.floor,ua=Math.pow,da=Math.round,ha="b-aspect",fa=Is({aspect:Ds(Hr,"1:1"),tag:Ds(Fr,"div")},Oe),pa=L({name:Oe,mixins:[So],props:fa,computed:{padding:function(){var t=this.aspect,e=1;if(Et.test(t)){var i=C(t.split(Vt).map((function(t){return xo(t)||1})),2);e=i[0]/i[1]}else e=xo(t)||1;return"".concat(100/aa(e),"%")}},render:function(t){var e=t("div",{staticClass:"".concat(ha,"-sizer flex-grow-1"),style:{paddingBottom:this.padding,height:0}}),i=t("div",{staticClass:"".concat(ha,"-content flex-grow-1 w-100 mw-100"),style:{marginLeft:"-100%"}},this.normalizeSlot());return t(this.tag,{staticClass:"".concat(ha," d-flex")},[e,i])}}),ma=$e({components:{BAspect:pa}});function va(t){return O?new Proxy(t,{get:function(t,e){return e in t?t[e]:void 0}}):t}var ga=function(t){return"%"+t.charCodeAt(0).toString(16)},ba=function(t){return encodeURIComponent(Io(t)).replace(It,ga).replace(Pt,",")},ya=decodeURIComponent,Ta=function(t){if(!Zt(t))return"";var e=se(t).map((function(e){var i=t[e];return zt(i)?"":Nt(i)?ba(e):Kt(i)?i.reduce((function(t,i){return Nt(i)?t.push(ba(e)):zt(i)||t.push(ba(e)+"="+ba(i)),t}),[]).join("&"):ba(e)+"="+ba(i)})).filter((function(t){return t.length>0})).join("&");return e?"?".concat(e):""},wa=function(t){var e={};return(t=Io(t).trim().replace(Ot,""))?(t.split("&").forEach((function(t){var i=t.replace(bt," ").split("="),n=ya(i.shift()),r=i.length>0?ya(i.join("=")):null;zt(e[n])?e[n]=r:Kt(e[n])?e[n].push(r):e[n]=[e[n],r]})),e):e},Ca=function(t){return!(!t.href&&!t.to)},Sa=function(t){return!(!t||jo(t,"a"))},ka=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.href,i=t.to,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"a",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"#",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"/";if(e)return e;if(Sa(n))return null;if(Ut(i))return i||o;if(Zt(i)&&(i.path||i.query||i.hash)){var s=Io(i.path),a=Ta(i.query),l=Io(i.hash);return l=l&&"#"!==l.charAt(0)?"#".concat(l):l,"".concat(s).concat(a).concat(l)||o}return r},xa={viewBox:"0 0 16 16",width:"1em",height:"1em",focusable:"false",role:"img","aria-label":"icon"},$a={width:null,height:null,focusable:null,role:null,"aria-label":null},Ba={animation:Ds(Fr),content:Ds(Fr),flipH:Ds(kr,!1),flipV:Ds(kr,!1),fontScale:Ds(Hr,1),rotate:Ds(Hr,0),scale:Ds(Hr,1),shiftH:Ds(Hr,0),shiftV:Ds(Hr,0),stacked:Ds(kr,!1),title:Ds(Fr),variant:Ds(Fr)},Da=L({name:"BIconBase",functional:!0,props:Ba,render:function(t,e){var i,n=e.data,r=e.props,o=e.children,s=r.animation,a=r.content,l=r.flipH,u=r.flipV,d=r.stacked,h=r.title,f=r.variant,p=sa(xo(r.fontScale,1),0)||1,m=sa(xo(r.scale,1),0)||1,v=xo(r.rotate,0),g=xo(r.shiftH,0),b=xo(r.shiftV,0),y=l||u||1!==m,T=y||v,w=g||b,C=!jt(a),S=t("g",{attrs:{transform:[T?"translate(8 8)":null,y?"scale(".concat((l?-1:1)*m," ").concat((u?-1:1)*m,")"):null,v?"rotate(".concat(v,")"):null,T?"translate(-8 -8)":null].filter(pe).join(" ")||null},domProps:C?{innerHTML:a||""}:{}},o);w&&(S=t("g",{attrs:{transform:"translate(".concat(16*g/16," ").concat(-16*b/16,")")}},[S])),d&&(S=t("g",[S]));var k=[h?t("title",h):null,S].filter(pe);return t("svg",I({staticClass:"b-icon bi",class:(i={},c(i,"text-".concat(f),f),c(i,"b-icon-animation-".concat(s),s),i),attrs:xa,style:d?{}:{fontSize:1===p?null:"".concat(100*p,"%")}},n,d?{attrs:$a}:{},{attrs:{xmlns:d?null:"http://www.w3.org/2000/svg",fill:"currentColor"}}),k)}}),_a=ue(Ba,["content"]),Fa=function(t,e){var i=Bo(t),n="BIcon".concat(Do(t)),o="bi-".concat(i),s=i.replace(/-/g," "),a=Oo(e||"");return L({name:n,functional:!0,props:_a,render:function(t,e){var i=e.data,n=e.props;return t(Da,I({props:{title:s},attrs:{"aria-label":s}},i,{staticClass:o,props:r(r({},n),{},{content:a})}))}})},Pa=Fa("Blank",""),Ia=Fa("Calendar",'<path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>'),Oa=Fa("CalendarFill",'<path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V5h16V4H0V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5z"/>'),Ea=Fa("ChevronBarLeft",'<path fill-rule="evenodd" d="M11.854 3.646a.5.5 0 0 1 0 .708L8.207 8l3.647 3.646a.5.5 0 0 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 0 1 .708 0zM4.5 1a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 1 0v-13a.5.5 0 0 0-.5-.5z"/>'),Va=Fa("ChevronDoubleLeft",'<path fill-rule="evenodd" d="M8.354 1.646a.5.5 0 0 1 0 .708L2.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/><path fill-rule="evenodd" d="M12.354 1.646a.5.5 0 0 1 0 .708L6.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>'),La=Fa("ChevronDown",'<path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>'),Ra=Fa("ChevronLeft",'<path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>'),Aa=Fa("ChevronUp",'<path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>'),Ma=Fa("CircleFill",'<circle cx="8" cy="8" r="8"/>'),Ha=Fa("Clock",'<path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/><path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"/>'),za=Fa("ClockFill",'<path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>'),Na=Fa("Dash",'<path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>'),ja=Fa("PersonFill",'<path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>'),Ga=Fa("Plus",'<path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>'),Wa=Fa("Star",'<path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288L8 2.223l1.847 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.565.565 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z"/>'),Ua=Fa("StarFill",'<path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>'),Ya=Fa("StarHalf",'<path d="M5.354 5.119 7.538.792A.516.516 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.537.537 0 0 1 16 6.32a.548.548 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.52.52 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.58.58 0 0 1 .085-.302.513.513 0 0 1 .37-.245l4.898-.696zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.565.565 0 0 1 .162-.505l2.907-2.77-4.052-.576a.525.525 0 0 1-.393-.288L8.001 2.223 8 2.226v9.8z"/>'),qa=Fa("X",'<path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>'),Ka=function t(e,n){if(!e)return i.default.component(n);var r=(e.$options||{}).components;return r&&r[n]||t(e.$parent,n)},Xa=ue(Ba,["content"]),Za=Is(he(r(r({},Xa),{},{icon:Ds(Fr)})),Ai),Ja=L({name:Ai,functional:!0,props:Za,render:function(t,e){var i=e.data,n=e.props,r=e.parent,o=Do(Oo(n.icon||"")).replace(Rt,"");return t(o&&Ka(r,"BIcon".concat(o))||Pa,I(i,{props:Fs(Xa,n)}))}}),Qa=40,tl=35,el=13,il=36,nl=37,rl=39,ol=32,sl=38,al=function(t,e){if(t.length!==e.length)return!1;for(var i=!0,n=0;i&&n<t.length;n++)i=ll(t[n],e[n]);return i},ll=function t(e,i){if(e===i)return!0;var n=Jt(e),r=Jt(i);if(n||r)return!(!n||!r)&&e.getTime()===i.getTime();if(n=Kt(e),r=Kt(i),n||r)return!(!n||!r)&&al(e,i);if(n=Xt(e),r=Xt(i),n||r){if(!n||!r)return!1;if(se(e).length!==se(i).length)return!1;for(var o in e){var s=ae(e,o),a=ae(i,o);if(s&&!a||!s&&a||!t(e[o],i[o]))return!1}}return String(e)===String(i)},cl=function(t){return!t||0===se(t).length},ul=function(t){return{handler:function(e,i){if(!ll(e,i))if(cl(e)||cl(i))this[t]=fe(e);else{for(var n in i)ae(e,n)||this.$delete(this.$data[t],n);for(var r in e)this.$set(this.$data[t],r,e[r])}}}},dl=function(t,e){return L({data:function(){return c({},e,fe(this[t]))},watch:c({},t,ul(e))})},hl=dl("$attrs","bvAttrs"),fl=L({computed:{bvAttrs:function(){var t=r({},this.$attrs);return Object.keys(t).forEach((function(e){void 0===t[e]&&delete t[e]})),t}}}),pl=O?fl:hl,ml=function(t){return t.$root.$options.bvEventRoot||t.$root},vl="$_rootListeners",gl=L({computed:{bvEventRoot:function(){return ml(this)}},created:function(){this[vl]={}},beforeDestroy:function(){var t=this;se(this[vl]||{}).forEach((function(e){t[vl][e].forEach((function(i){t.listenOffRoot(e,i)}))})),this[vl]=null},methods:{registerRootListener:function(t,e){this[vl]&&(this[vl][t]=this[vl][t]||[],vo(this[vl][t],e)||this[vl][t].push(e))},unregisterRootListener:function(t,e){this[vl]&&this[vl][t]&&(this[vl][t]=this[vl][t].filter((function(t){return t!==e})))},listenOnRoot:function(t,e){this.bvEventRoot&&(this.bvEventRoot.$on(t,e),this.registerRootListener(t,e))},listenOnRootOnce:function(t,e){var i=this;if(this.bvEventRoot){var n=function t(){i.unregisterRootListener(t),e.apply(void 0,arguments)};this.bvEventRoot.$once(t,n),this.registerRootListener(t,n)}},listenOffRoot:function(t,e){this.unregisterRootListener(t,e),this.bvEventRoot&&this.bvEventRoot.$off(t,e)},emitOnRoot:function(t){if(this.bvEventRoot){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];(e=this.bvEventRoot).$emit.apply(e,[t].concat(n))}}}}),bl=dl("$listeners","bvListeners"),yl=L({data:function(){return{bvListeners:{}}},created:function(){this.bvListeners=r({},this.$listeners)},beforeUpdate:function(){this.bvListeners=r({},this.$listeners)}}),Tl=O?yl:bl,wl=Ns(Yi,"clicked"),Cl={activeClass:Ds(Fr),append:Ds(kr,!1),event:Ds(Er),exact:Ds(kr,!1),exactActiveClass:Ds(Fr),exactPath:Ds(kr,!1),exactPathActiveClass:Ds(Fr),replace:Ds(kr,!1),routerTag:Ds(Fr),to:Ds(jr)},Sl={noPrefetch:Ds(kr,!1),prefetch:Ds(kr,null)},kl=Is(he(r(r(r({},Sl),Cl),{},{active:Ds(kr,!1),disabled:Ds(kr,!1),href:Ds(Fr),rel:Ds(Fr,null),routerComponentName:Ds(Fr),target:Ds(Fr,"_self")})),Yi),xl=L({name:Yi,mixins:[pl,Tl,gl,So],inheritAttrs:!1,props:kl,computed:{computedTag:function(){return function(t,e){var i=t.to,n=t.disabled,r=t.routerComponentName,o=!!va(e).$router,s=!!va(e).$nuxt;return!o||o&&(n||!i)?"a":r||(s?"nuxt-link":"router-link")}({to:this.to,disabled:this.disabled,routerComponentName:this.routerComponentName},this)},isRouterLink:function(){return Sa(this.computedTag)},computedRel:function(){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.target,i=t.rel;return"_blank"===e&&Nt(i)?"noopener":i||null}({target:this.target,rel:this.rel})},computedHref:function(){var t=this.to,e=this.href;return ka({to:t,href:e},this.computedTag)},computedProps:function(){var t=this.event,e=this.prefetch,i=this.routerTag;return this.isRouterLink?r(r(r(r({},Fs(ue(r(r({},Cl),"nuxt-link"===this.computedTag?Sl:{}),["event","prefetch","routerTag"]),this)),t?{event:t}:{}),Wt(e)?{prefetch:e}:{}),i?{tag:i}:{}):{}},computedAttrs:function(){var t=this.bvAttrs,e=this.computedHref,i=this.computedRel,n=this.disabled,o=this.target,s=this.routerTag,a=this.isRouterLink;return r(r(r(r({},t),e?{href:e}:{}),a&&s&&!jo(s,"a")?{}:{rel:i,target:o}),{},{tabindex:n?"-1":zt(t.tabindex)?null:t.tabindex,"aria-disabled":n?"true":null})},computedListeners:function(){return r(r({},this.bvListeners),{},{click:this.onClick})}},methods:{onClick:function(t){var e=arguments,i=Qt(t),n=this.isRouterLink,r=this.bvListeners.click;if(i&&this.disabled)Hs(t,{immediatePropagation:!0});else{var o;if(n)null===(o=t.currentTarget.__vue__)||void 0===o||o.$emit(Hn,t);go(r).filter((function(t){return Gt(t)})).forEach((function(t){t.apply(void 0,S(e))})),this.emitOnRoot(wl,t),this.emitOnRoot("clicked::link",t)}i&&!n&&"#"===this.computedHref&&Hs(t,{propagation:!1})},focus:function(){vs(this.$el)},blur:function(){gs(this.$el)}},render:function(t){var e=this.active,i=this.disabled;return t(this.computedTag,c({class:{active:e,disabled:i},attrs:this.computedAttrs,props:this.computedProps},this.isRouterLink?"nativeOn":"on",this.computedListeners),this.normalizeSlot())}}),$l=ue(kl,["event","routerTag"]);delete $l.href.default,delete $l.to.default;var Bl=Is(he(r(r({},$l),{},{block:Ds(kr,!1),disabled:Ds(kr,!1),pill:Ds(kr,!1),pressed:Ds(kr,null),size:Ds(Fr),squared:Ds(kr,!1),tag:Ds(Fr,"button"),type:Ds(Fr,"button"),variant:Ds(Fr,"secondary")})),He),Dl=function(t){"focusin"===t.type?ts(t.target,"focus"):"focusout"===t.type&&es(t.target,"focus")},_l=function(t){return Ca(t)||jo(t.tag,"a")},Fl=function(t){return Wt(t.pressed)},Pl=function(t){return!(_l(t)||t.tag&&!jo(t.tag,"button"))},Il=function(t){return!_l(t)&&!Pl(t)},Ol=function(t){var e;return["btn-".concat(t.variant||"secondary"),(e={},c(e,"btn-".concat(t.size),t.size),c(e,"btn-block",t.block),c(e,"rounded-pill",t.pill),c(e,"rounded-0",t.squared&&!t.pill),c(e,"disabled",t.disabled),c(e,"active",t.pressed),e)]},El=function(t){return _l(t)?Fs($l,t):{}},Vl=function(t,e){var i=Pl(t),n=_l(t),r=Fl(t),o=Il(t),s=n&&"#"===t.href,a=e.attrs&&e.attrs.role?e.attrs.role:null,l=e.attrs?e.attrs.tabindex:null;return(o||s)&&(l="0"),{type:i&&!n?t.type:null,disabled:i?t.disabled:null,role:o||s?"button":a,"aria-disabled":o?String(t.disabled):null,"aria-pressed":r?String(t.pressed):null,autocomplete:r?"off":null,tabindex:t.disabled&&!i?"-1":l}},Ll=L({name:He,functional:!0,props:Bl,render:function(t,e){var i=e.props,n=e.data,o=e.listeners,s=e.children,a=Fl(i),l=_l(i),c=Il(i),u=l&&"#"===i.href,d={keydown:function(t){if(!i.disabled&&(c||u)){var e=t.keyCode;if(e===ol||e===el&&c){var n=t.currentTarget||t.target;Hs(t,{propagation:!1}),n.click()}}},click:function(t){i.disabled&&Qt(t)?Hs(t):a&&o&&o["update:pressed"]&&go(o["update:pressed"]).forEach((function(t){Gt(t)&&t(!i.pressed)}))}};a&&(d.focusin=Dl,d.focusout=Dl);var h={staticClass:"btn",class:Ol(i),props:El(i),attrs:Vl(i,n),on:d};return t(l?xl:i.tag,I(r(r({},n),{},{props:void 0}),h),s)}}),Rl="b-avatar",Al=["sm",null,"lg"],Ml=function(t){return t=Ut(t)&&qt(t)?xo(t,0):t,Yt(t)?"".concat(t,"px"):t||null},Hl=ue(kl,["active","event","routerTag"]),zl=Is(he(r(r({},Hl),{},{alt:Ds(Fr,"avatar"),ariaLabel:Ds(Fr),badge:Ds(Rr,!1),badgeLeft:Ds(kr,!1),badgeOffset:Ds(Fr),badgeTop:Ds(kr,!1),badgeVariant:Ds(Fr,"primary"),button:Ds(kr,!1),buttonType:Ds(Fr,"button"),icon:Ds(Fr),rounded:Ds(Rr,!1),size:Ds(Hr),square:Ds(kr,!1),src:Ds(Fr),text:Ds(Fr),variant:Ds(Fr,"secondary")})),Ee),Nl=L({name:Ee,mixins:[So],inject:{getBvAvatarGroup:{default:function(){return function(){return null}}}},props:zl,data:function(){return{localSrc:this.src||null}},computed:{bvAvatarGroup:function(){return this.getBvAvatarGroup()},computedSize:function(){var t=this.bvAvatarGroup;return Ml(t?t.size:this.size)},computedVariant:function(){var t=this.bvAvatarGroup;return t&&t.variant?t.variant:this.variant},computedRounded:function(){var t=this.bvAvatarGroup,e=!(!t||!t.square)||this.square,i=t&&t.rounded?t.rounded:this.rounded;return e?"0":""===i||(i||"circle")},fontStyle:function(){var t=this.computedSize,e=-1===Al.indexOf(t)?"calc(".concat(t," * ").concat(.4,")"):null;return e?{fontSize:e}:{}},marginStyle:function(){var t=this.computedSize,e=this.bvAvatarGroup,i=e?e.overlapScale:0,n=t&&i?"calc(".concat(t," * -").concat(i,")"):null;return n?{marginLeft:n,marginRight:n}:{}},badgeStyle:function(){var t=this.computedSize,e=this.badgeTop,i=this.badgeLeft,n=this.badgeOffset||"0px";return{fontSize:-1===Al.indexOf(t)?"calc(".concat(t," * ").concat(.27999999999999997," )"):null,top:e?n:null,bottom:e?null:n,left:i?n:null,right:i?null:n}}},watch:{src:function(t,e){t!==e&&(this.localSrc=t||null)}},methods:{onImgError:function(t){this.localSrc=null,this.$emit("img-error",t)},onClick:function(t){this.$emit(Hn,t)}},render:function(t){var e,i=this.computedVariant,n=this.disabled,o=this.computedRounded,s=this.icon,a=this.localSrc,l=this.text,u=this.fontStyle,d=this.marginStyle,h=this.computedSize,f=this.button,p=this.buttonType,m=this.badge,v=this.badgeVariant,g=this.badgeStyle,b=!f&&Ca(this),y=f?Ll:b?xl:"span",T=this.alt,w=this.ariaLabel||null,C=null;this.hasNormalizedSlot()?C=t("span",{staticClass:"b-avatar-custom"},[this.normalizeSlot()]):a?(C=t("img",{style:i?{}:{width:"100%",height:"100%"},attrs:{src:a,alt:T},on:{error:this.onImgError}}),C=t("span",{staticClass:"b-avatar-img"},[C])):C=s?t(Ja,{props:{icon:s},attrs:{"aria-hidden":"true",alt:T}}):l?t("span",{staticClass:"b-avatar-text",style:u},[t("span",l)]):t(ja,{attrs:{"aria-hidden":"true",alt:T}});var S=t(),k=this.hasNormalizedSlot(Wr);if(m||""===m||k){var x=!0===m?"":m;S=t("span",{staticClass:"b-avatar-badge",class:c({},"badge-".concat(v),v),style:g},[k?this.normalizeSlot(Wr):x])}return t(y,{staticClass:Rl,class:(e={},c(e,"".concat(Rl,"-").concat(h),h&&-1!==Al.indexOf(h)),c(e,"badge-".concat(i),!f&&i),c(e,"rounded",!0===o),c(e,"rounded-".concat(o),o&&!0!==o),c(e,"disabled",n),e),style:r(r({},d),{},{width:h,height:h}),attrs:{"aria-label":w||null},props:f?{variant:i,disabled:n,type:p}:b?Fs(Hl,this):{},on:f||b?{click:this.onClick}:{}},[C,S])}}),jl=Is({overlap:Ds(Hr,.3),rounded:Ds(Rr,!1),size:Ds(Fr),square:Ds(kr,!1),tag:Ds(Fr,"div"),variant:Ds(Fr)},Ve),Gl=$e({components:{BAvatar:Nl,BAvatarGroup:L({name:Ve,mixins:[So],provide:function(){var t=this;return{getBvAvatarGroup:function(){return t}}},props:jl,computed:{computedSize:function(){return Ml(this.size)},overlapScale:function(){return oa(sa(xo(this.overlap,0),0),1)/2},paddingStyle:function(){var t=this.computedSize;return(t=t?"calc(".concat(t," * ").concat(this.overlapScale,")"):null)?{paddingLeft:t,paddingRight:t}:{}}},render:function(t){var e=t("div",{staticClass:"b-avatar-group-inner",style:this.paddingStyle},this.normalizeSlot());return t(this.tag,{staticClass:"b-avatar-group",attrs:{role:"group"}},[e])}})}}),Wl=ue(kl,["event","routerTag"]);delete Wl.href.default,delete Wl.to.default;var Ul,Yl=Is(he(r(r({},Wl),{},{pill:Ds(kr,!1),tag:Ds(Fr,"span"),variant:Ds(Fr,"secondary")})),Le),ql=L({name:Le,functional:!0,props:Yl,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.active,s=i.disabled,a=Ca(i),l=a?xl:i.tag,c=i.variant||"secondary";return t(l,I(n,{staticClass:"badge",class:["badge-".concat(c),{"badge-pill":i.pill,active:o,disabled:s}],props:a?Fs(Wl,i):{}}),r)}}),Kl=$e({components:{BBadge:ql}}),Xl=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return String(t).replace(pt,"")},Zl=function(t,e){return t?{innerHTML:t}:e?{textContent:e}:{}},Jl=Is(he(r(r({},ue(kl,["event","routerTag"])),{},{ariaCurrent:Ds(Fr,"location"),html:Ds(Fr),text:Ds(Fr)})),Me),Ql=L({name:Me,functional:!0,props:Jl,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.active,s=o?"span":xl,a={attrs:{"aria-current":o?i.ariaCurrent:null},props:Fs(Jl,i)};return r||(a.domProps=Zl(i.html,i.text)),t(s,I(n,a),r)}}),tc=Is(Jl,Ae),ec=L({name:Ae,functional:!0,props:tc,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t("li",I(n,{staticClass:"breadcrumb-item",class:{active:i.active}}),[t(Ql,{props:i},r)])}}),ic=Is({items:Ds(Sr)},Re),nc=L({name:Re,functional:!0,props:ic,render:function(t,e){var i=e.props,n=e.data,o=e.children,s=i.items,a=o;if(Kt(s)){var l=!1;a=s.map((function(e,i){Xt(e)||(e={text:Io(e)});var n=e.active;return n&&(l=!0),n||l||(n=i+1===s.length),t(ec,{props:r(r({},e),{},{active:n})})}))}return t("ol",I(n,{staticClass:"breadcrumb"}),a)}}),rc=$e({components:{BBreadcrumb:nc,BBreadcrumbItem:ec,BBreadcrumbLink:Ql}}),oc=$e({components:{BButton:Ll,BBtn:Ll,BButtonClose:Ws,BBtnClose:Ws}}),sc=Is(he(r(r({},ce(Bl,["size"])),{},{ariaRole:Ds(Fr,"group"),size:Ds(Fr),tag:Ds(Fr,"div"),vertical:Ds(kr,!1)})),Ne),ac=L({name:Ne,functional:!0,props:sc,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{class:c({"btn-group":!i.vertical,"btn-group-vertical":i.vertical},"btn-group-".concat(i.size),i.size),attrs:{role:i.ariaRole}}),r)}}),lc=$e({components:{BButtonGroup:ac,BBtnGroup:ac}}),cc=[".btn:not(.disabled):not([disabled]):not(.dropdown-item)",".form-control:not(.disabled):not([disabled])","select:not(.disabled):not([disabled])",'input[type="checkbox"]:not(.disabled)','input[type="radio"]:not(.disabled)'].join(","),uc=Is({justify:Ds(kr,!1),keyNav:Ds(kr,!1)},je),dc=L({name:je,mixins:[So],props:uc,mounted:function(){this.keyNav&&this.getItems()},methods:{getItems:function(){var t=qo(cc,this.$el);return t.forEach((function(t){t.tabIndex=-1})),t.filter((function(t){return Wo(t)}))},focusFirst:function(){var t=this.getItems();vs(t[0])},focusPrev:function(t){var e=this.getItems(),i=e.indexOf(t.target);i>-1&&(e=e.slice(0,i).reverse(),vs(e[0]))},focusNext:function(t){var e=this.getItems(),i=e.indexOf(t.target);i>-1&&(e=e.slice(i+1),vs(e[0]))},focusLast:function(){var t=this.getItems().reverse();vs(t[0])},onFocusin:function(t){var e=this.$el;t.target!==e||Jo(e,t.relatedTarget)||(Hs(t),this.focusFirst(t))},onKeydown:function(t){var e=t.keyCode,i=t.shiftKey;e===sl||e===nl?(Hs(t),i?this.focusFirst(t):this.focusPrev(t)):e!==Qa&&e!==rl||(Hs(t),i?this.focusLast(t):this.focusNext(t))}},render:function(t){var e=this.keyNav;return t("div",{staticClass:"btn-toolbar",class:{"justify-content-between":this.justify},attrs:{role:"toolbar",tabindex:e?"0":null},on:e?{focusin:this.onFocusin,keydown:this.onKeydown}:{}},[this.normalizeSlot()])}}),hc=$e({components:{BButtonToolbar:dc,BBtnToolbar:dc}}),fc="gregory",pc="long",mc="short",vc="2-digit",gc="numeric",bc=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return p(Date,e)},yc=function(t){if(Ut(t)&&Bt.test(t.trim())){var e=C(t.split(Dt).map((function(t){return ko(t,1)})),3),i=e[0],n=e[1],r=e[2];return bc(i,n-1,r)}return Jt(t)?bc(t.getFullYear(),t.getMonth(),t.getDate()):null},Tc=function(t){if(!(t=yc(t)))return null;var e=t.getFullYear(),i="0".concat(t.getMonth()+1).slice(-2),n="0".concat(t.getDate()).slice(-2);return"".concat(e,"-").concat(i,"-").concat(n)},wc=function(t,e){return new Intl.DateTimeFormat(t,e).format},Cc=function(t,e){return Tc(t)===Tc(e)},Sc=function(t){return(t=bc(t)).setDate(1),t},kc=function(t){return(t=bc(t)).setMonth(t.getMonth()+1),t.setDate(0),t},xc=function(t,e){var i=(t=bc(t)).getMonth();return t.setFullYear(t.getFullYear()+e),t.getMonth()!==i&&t.setDate(0),t},$c=function(t){var e=(t=bc(t)).getMonth();return t.setMonth(e-1),t.getMonth()===e&&t.setDate(0),t},Bc=function(t){var e=(t=bc(t)).getMonth();return t.setMonth(e+1),t.getMonth()===(e+2)%12&&t.setDate(0),t},Dc=function(t){return xc(t,-1)},_c=function(t){return xc(t,1)},Fc=function(t){return xc(t,-10)},Pc=function(t){return xc(t,10)},Ic=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return t=yc(t),e=yc(e)||t,i=yc(i)||t,t?t<e?e:t>i?i:t:null},Oc=["ar","az","ckb","fa","he","ks","lrc","mzn","ps","sd","te","ug","ur","yi"].map((function(t){return t.toLowerCase()})),Ec=function(t){var e=Io(t).toLowerCase().replace(At,"").split("-"),i=e.slice(0,2).join("-"),n=e[0];return vo(Oc,i)||vo(Oc,n)},Vc={id:Ds(Fr)},Lc=L({props:Vc,data:function(){return{localId_:null}},computed:{safeId:function(){var t=this.id||this.localId_;return function(e){return t?(e=String(e||"").replace(/\s+/g,"_"))?t+"_"+e:t:null}}},mounted:function(){var t=this;this.$nextTick((function(){t.localId_="__BVID__".concat(t._uid)}))}}),Rc=Vs("value",{type:Ar}),Ac=Rc.mixin,Mc=Rc.props,Hc=Rc.prop,zc=Rc.event,Nc=Is(he(r(r(r({},Vc),Mc),{},{ariaControls:Ds(Fr),block:Ds(kr,!1),dateDisabledFn:Ds($r),dateFormatOptions:Ds(Dr,{year:gc,month:pc,day:gc,weekday:pc}),dateInfoFn:Ds($r),direction:Ds(Fr),disabled:Ds(kr,!1),headerTag:Ds(Fr,"header"),hidden:Ds(kr,!1),hideHeader:Ds(kr,!1),initialDate:Ds(Ar),labelCalendar:Ds(Fr,"Calendar"),labelCurrentMonth:Ds(Fr,"Current month"),labelHelp:Ds(Fr,"Use cursor keys to navigate calendar dates"),labelNav:Ds(Fr,"Calendar navigation"),labelNextDecade:Ds(Fr,"Next decade"),labelNextMonth:Ds(Fr,"Next month"),labelNextYear:Ds(Fr,"Next year"),labelNoDateSelected:Ds(Fr,"No date selected"),labelPrevDecade:Ds(Fr,"Previous decade"),labelPrevMonth:Ds(Fr,"Previous month"),labelPrevYear:Ds(Fr,"Previous year"),labelSelected:Ds(Fr,"Selected date"),labelToday:Ds(Fr,"Today"),locale:Ds(Er),max:Ds(Ar),min:Ds(Ar),navButtonVariant:Ds(Fr,"secondary"),noHighlightToday:Ds(kr,!1),noKeyNav:Ds(kr,!1),readonly:Ds(kr,!1),roleDescription:Ds(Fr),selectedVariant:Ds(Fr,"primary"),showDecadeNav:Ds(kr,!1),startWeekday:Ds(Hr,0),todayVariant:Ds(Fr),valueAsDate:Ds(kr,!1),weekdayHeaderFormat:Ds(Fr,mc,(function(t){return vo([pc,mc,"narrow"],t)})),width:Ds(Fr,"270px")})),Ge),jc=L({name:Ge,mixins:[pl,Lc,Ac,So],props:Nc,data:function(){var t=Tc(this[Hc])||"";return{selectedYMD:t,activeYMD:t||Tc(Ic(this.initialDate||this.getToday()),this.min,this.max),gridHasFocus:!1,isLive:!1}},computed:{valueId:function(){return this.safeId()},widgetId:function(){return this.safeId("_calendar-wrapper_")},navId:function(){return this.safeId("_calendar-nav_")},gridId:function(){return this.safeId("_calendar-grid_")},gridCaptionId:function(){return this.safeId("_calendar-grid-caption_")},gridHelpId:function(){return this.safeId("_calendar-grid-help_")},activeId:function(){return this.activeYMD?this.safeId("_cell-".concat(this.activeYMD,"_")):null},selectedDate:function(){return yc(this.selectedYMD)},activeDate:function(){return yc(this.activeYMD)},computedMin:function(){return yc(this.min)},computedMax:function(){return yc(this.max)},computedWeekStarts:function(){return sa(ko(this.startWeekday,0),0)%7},computedLocale:function(){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:fc;return t=go(t).filter(pe),new Intl.DateTimeFormat(t,{calendar:e}).resolvedOptions().locale}(go(this.locale).filter(pe),fc)},computedDateDisabledFn:function(){var t=this.dateDisabledFn;return Es(t)?t:function(){return!1}},computedDateInfoFn:function(){var t=this.dateInfoFn;return Es(t)?t:function(){return{}}},calendarLocale:function(){var t=new Intl.DateTimeFormat(this.computedLocale,{calendar:fc}),e=t.resolvedOptions().calendar,i=t.resolvedOptions().locale;return e!==fc&&(i=i.replace(/-u-.+$/i,"").concat("-u-ca-gregory")),i},calendarYear:function(){return this.activeDate.getFullYear()},calendarMonth:function(){return this.activeDate.getMonth()},calendarFirstDay:function(){return bc(this.calendarYear,this.calendarMonth,1,12)},calendarDaysInMonth:function(){var t=bc(this.calendarFirstDay);return t.setMonth(t.getMonth()+1,0),t.getDate()},computedVariant:function(){return"btn-".concat(this.selectedVariant||"primary")},computedTodayVariant:function(){return"btn-outline-".concat(this.todayVariant||this.selectedVariant||"primary")},computedNavButtonVariant:function(){return"btn-outline-".concat(this.navButtonVariant||"primary")},isRTL:function(){var t=Io(this.direction).toLowerCase();return"rtl"===t||"ltr"!==t&&Ec(this.computedLocale)},context:function(){var t=this.selectedYMD,e=this.activeYMD,i=yc(t),n=yc(e);return{selectedYMD:t,selectedDate:i,selectedFormatted:i?this.formatDateString(i):this.labelNoDateSelected,activeYMD:e,activeDate:n,activeFormatted:n?this.formatDateString(n):"",disabled:this.dateDisabled(n),locale:this.computedLocale,calendarLocale:this.calendarLocale,rtl:this.isRTL}},dateOutOfRange:function(){var t=this.computedMin,e=this.computedMax;return function(i){return i=yc(i),t&&i<t||e&&i>e}},dateDisabled:function(){var t=this,e=this.dateOutOfRange;return function(i){i=yc(i);var n=Tc(i);return!(!e(i)&&!t.computedDateDisabledFn(n,i))}},formatDateString:function(){return wc(this.calendarLocale,r(r({year:gc,month:vc,day:vc},this.dateFormatOptions),{},{hour:void 0,minute:void 0,second:void 0,calendar:fc}))},formatYearMonth:function(){return wc(this.calendarLocale,{year:gc,month:pc,calendar:fc})},formatWeekdayName:function(){return wc(this.calendarLocale,{weekday:pc,calendar:fc})},formatWeekdayNameShort:function(){return wc(this.calendarLocale,{weekday:this.weekdayHeaderFormat||mc,calendar:fc})},formatDay:function(){var t=new Intl.NumberFormat([this.computedLocale],{style:"decimal",minimumIntegerDigits:1,minimumFractionDigits:0,maximumFractionDigits:0,notation:"standard"});return function(e){return t.format(e.getDate())}},prevDecadeDisabled:function(){var t=this.computedMin;return this.disabled||t&&kc(Fc(this.activeDate))<t},prevYearDisabled:function(){var t=this.computedMin;return this.disabled||t&&kc(Dc(this.activeDate))<t},prevMonthDisabled:function(){var t=this.computedMin;return this.disabled||t&&kc($c(this.activeDate))<t},thisMonthDisabled:function(){return this.disabled},nextMonthDisabled:function(){var t=this.computedMax;return this.disabled||t&&Sc(Bc(this.activeDate))>t},nextYearDisabled:function(){var t=this.computedMax;return this.disabled||t&&Sc(_c(this.activeDate))>t},nextDecadeDisabled:function(){var t=this.computedMax;return this.disabled||t&&Sc(Pc(this.activeDate))>t},calendar:function(){for(var t=[],e=this.calendarFirstDay,i=e.getFullYear(),n=e.getMonth(),o=this.calendarDaysInMonth,s=e.getDay(),a=0-((this.computedWeekStarts>s?7:0)-this.computedWeekStarts)-s,l=0;l<6&&a<o;l++){t[l]=[];for(var c=0;c<7;c++){a++;var u=bc(i,n,a),d=u.getMonth(),h=Tc(u),f=this.dateDisabled(u),p=this.computedDateInfoFn(h,yc(h));p=Ut(p)||Kt(p)?{class:p}:Zt(p)?r({class:""},p):{class:""},t[l].push({ymd:h,day:this.formatDay(u),label:this.formatDateString(u),isThisMonth:d===n,isDisabled:f,info:p})}}return t},calendarHeadings:function(){var t=this;return this.calendar[0].map((function(e){return{text:t.formatWeekdayNameShort(yc(e.ymd)),label:t.formatWeekdayName(yc(e.ymd))}}))}},watch:(Ul={},c(Ul,Hc,(function(t,e){var i=Tc(t)||"",n=Tc(e)||"";Cc(i,n)||(this.activeYMD=i||this.activeYMD,this.selectedYMD=i)})),c(Ul,"selectedYMD",(function(t,e){t!==e&&this.$emit(zc,this.valueAsDate?yc(t)||null:t||"")})),c(Ul,"context",(function(t,e){ll(t,e)||this.$emit(Nn,t)})),c(Ul,"hidden",(function(t){this.activeYMD=this.selectedYMD||Tc(this[Hc]||this.constrainDate(this.initialDate||this.getToday())),this.setLive(!t)})),Ul),created:function(){var t=this;this.$nextTick((function(){t.$emit(Nn,t.context)}))},mounted:function(){this.setLive(!0)},activated:function(){this.setLive(!0)},deactivated:function(){this.setLive(!1)},beforeDestroy:function(){this.setLive(!1)},methods:{focus:function(){this.disabled||vs(this.$refs.grid)},blur:function(){this.disabled||gs(this.$refs.grid)},setLive:function(t){var e=this;t?this.$nextTick((function(){Mo((function(){e.isLive=!0}))})):this.isLive=!1},getToday:function(){return yc(bc())},constrainDate:function(t){return Ic(t,this.computedMin,this.computedMax)},emitSelected:function(t){var e=this;this.$nextTick((function(){e.$emit(hr,Tc(t)||"",yc(t)||null)}))},setGridFocusFlag:function(t){this.gridHasFocus=!this.disabled&&"focus"===t.type},onKeydownWrapper:function(t){if(!this.noKeyNav){var e=t.altKey,i=t.ctrlKey,n=t.keyCode;if(vo([33,34,tl,il,nl,sl,rl,Qa],n)){Hs(t);var r=bc(this.activeDate),o=bc(this.activeDate),s=r.getDate(),a=this.constrainDate(this.getToday()),l=this.isRTL;33===n?(r=(e?i?Fc:Dc:$c)(r),(o=bc(r)).setDate(1)):34===n?(r=(e?i?Pc:_c:Bc)(r),(o=bc(r)).setMonth(o.getMonth()+1),o.setDate(0)):n===nl?(r.setDate(s+(l?1:-1)),o=r=this.constrainDate(r)):n===rl?(r.setDate(s+(l?-1:1)),o=r=this.constrainDate(r)):n===sl?(r.setDate(s-7),o=r=this.constrainDate(r)):n===Qa?(r.setDate(s+7),o=r=this.constrainDate(r)):n===il?o=r=a:n===tl&&(o=r=yc(this.selectedDate)||a),this.dateOutOfRange(o)||Cc(r,this.activeDate)||(this.activeYMD=Tc(r)),this.focus()}}},onKeydownGrid:function(t){var e=t.keyCode,i=this.activeDate;e!==el&&e!==ol||(Hs(t),this.disabled||this.readonly||this.dateDisabled(i)||(this.selectedYMD=Tc(i),this.emitSelected(i)),this.focus())},onClickDay:function(t){var e=this.selectedDate,i=this.activeDate,n=yc(t.ymd);this.disabled||t.isDisabled||this.dateDisabled(n)||(this.readonly||(this.selectedYMD=Tc(Cc(n,e)?e:n),this.emitSelected(n)),this.activeYMD=Tc(Cc(n,i)?i:bc(n)),this.focus())},gotoPrevDecade:function(){this.activeYMD=Tc(this.constrainDate(Fc(this.activeDate)))},gotoPrevYear:function(){this.activeYMD=Tc(this.constrainDate(Dc(this.activeDate)))},gotoPrevMonth:function(){this.activeYMD=Tc(this.constrainDate($c(this.activeDate)))},gotoCurrentMonth:function(){this.activeYMD=Tc(this.constrainDate(this.getToday()))},gotoNextMonth:function(){this.activeYMD=Tc(this.constrainDate(Bc(this.activeDate)))},gotoNextYear:function(){this.activeYMD=Tc(this.constrainDate(_c(this.activeDate)))},gotoNextDecade:function(){this.activeYMD=Tc(this.constrainDate(Pc(this.activeDate)))},onHeaderClick:function(){this.disabled||(this.activeYMD=this.selectedYMD||Tc(this.getToday()),this.focus())}},render:function(t){var e=this;if(this.hidden)return t();var i=this.valueId,n=this.widgetId,o=this.navId,s=this.gridId,a=this.gridCaptionId,l=this.gridHelpId,u=this.activeId,d=this.disabled,h=this.noKeyNav,f=this.isLive,p=this.isRTL,m=this.activeYMD,v=this.selectedYMD,g=this.safeId,b=!this.showDecadeNav,y=Tc(this.getToday()),T=!this.noHighlightToday,w=t("output",{staticClass:"form-control form-control-sm text-center",class:{"text-muted":d,readonly:this.readonly||d},attrs:{id:i,for:s,role:"status",tabindex:d?null:"-1","data-selected":Io(v),"aria-live":f?"polite":"off","aria-atomic":f?"true":null},on:{click:this.onHeaderClick,focus:this.onHeaderClick}},this.selectedDate?[t("bdi",{staticClass:"sr-only"}," (".concat(Io(this.labelSelected),") ")),t("bdi",this.formatDateString(this.selectedDate))]:this.labelNoDateSelected||" ");w=t(this.headerTag,{staticClass:"b-calendar-header",class:{"sr-only":this.hideHeader},attrs:{title:this.selectedDate&&this.labelSelected||null}},[w]);var C={isRTL:p},S={shiftV:.5},k=r(r({},S),{},{flipH:p}),x=r(r({},S),{},{flipH:!p}),$=this.normalizeSlot("nav-prev-decade",C)||t(Ea,{props:k}),B=this.normalizeSlot("nav-prev-year",C)||t(Va,{props:k}),D=this.normalizeSlot("nav-prev-month",C)||t(Ra,{props:k}),_=this.normalizeSlot("nav-this-month",C)||t(Ma,{props:S}),F=this.normalizeSlot("nav-next-month",C)||t(Ra,{props:x}),P=this.normalizeSlot("nav-next-year",C)||t(Va,{props:x}),I=this.normalizeSlot("nav-next-decade",C)||t(Ea,{props:x}),O=function(i,n,r,o,s){return t("button",{staticClass:"btn btn-sm border-0 flex-fill",class:[e.computedNavButtonVariant,{disabled:o}],attrs:{title:n||null,type:"button",tabindex:h?"-1":null,"aria-label":n||null,"aria-disabled":o?"true":null,"aria-keyshortcuts":s||null},on:o?{}:{click:r}},[t("div",{attrs:{"aria-hidden":"true"}},[i])])},E=t("div",{staticClass:"b-calendar-nav d-flex",attrs:{id:o,role:"group",tabindex:h?"-1":null,"aria-hidden":d?"true":null,"aria-label":this.labelNav||null,"aria-controls":s}},[b?t():O($,this.labelPrevDecade,this.gotoPrevDecade,this.prevDecadeDisabled,"Ctrl+Alt+PageDown"),O(B,this.labelPrevYear,this.gotoPrevYear,this.prevYearDisabled,"Alt+PageDown"),O(D,this.labelPrevMonth,this.gotoPrevMonth,this.prevMonthDisabled,"PageDown"),O(_,this.labelCurrentMonth,this.gotoCurrentMonth,this.thisMonthDisabled,"Home"),O(F,this.labelNextMonth,this.gotoNextMonth,this.nextMonthDisabled,"PageUp"),O(P,this.labelNextYear,this.gotoNextYear,this.nextYearDisabled,"Alt+PageUp"),b?t():O(I,this.labelNextDecade,this.gotoNextDecade,this.nextDecadeDisabled,"Ctrl+Alt+PageUp")]),V=t("div",{staticClass:"b-calendar-grid-caption text-center font-weight-bold",class:{"text-muted":d},attrs:{id:a,"aria-live":f?"polite":null,"aria-atomic":f?"true":null},key:"grid-caption"},this.formatYearMonth(this.calendarFirstDay)),L=t("div",{staticClass:"b-calendar-grid-weekdays row no-gutters border-bottom",attrs:{"aria-hidden":"true"}},this.calendarHeadings.map((function(e,i){return t("small",{staticClass:"col text-truncate",class:{"text-muted":d},attrs:{title:e.label===e.text?null:e.label,"aria-label":e.label},key:i},e.text)}))),R=this.calendar.map((function(i){var n=i.map((function(i,n){var r,o=i.ymd===v,s=i.ymd===m,a=i.ymd===y,l=g("_cell-".concat(i.ymd,"_")),u=t("span",{staticClass:"btn border-0 rounded-circle text-nowrap",class:(r={focus:s&&e.gridHasFocus,disabled:i.isDisabled||d,active:o},c(r,e.computedVariant,o),c(r,e.computedTodayVariant,a&&T&&!o&&i.isThisMonth),c(r,"btn-outline-light",!(a&&T||o||s)),c(r,"btn-light",!(a&&T)&&!o&&s),c(r,"text-muted",!i.isThisMonth&&!o),c(r,"text-dark",!(a&&T)&&!o&&!s&&i.isThisMonth),c(r,"font-weight-bold",(o||i.isThisMonth)&&!i.isDisabled),r),on:{click:function(){return e.onClickDay(i)}}},i.day);return t("div",{staticClass:"col p-0",class:i.isDisabled?"bg-light":i.info.class||"",attrs:{id:l,role:"button","data-date":i.ymd,"aria-hidden":i.isThisMonth?null:"true","aria-disabled":i.isDisabled||d?"true":null,"aria-label":[i.label,o?"(".concat(e.labelSelected,")"):null,a?"(".concat(e.labelToday,")"):null].filter(pe).join(" "),"aria-selected":o?"true":null,"aria-current":o?"date":null},key:n},[u])}));return t("div",{staticClass:"row no-gutters",key:i[0].ymd},n)}));R=t("div",{staticClass:"b-calendar-grid-body",style:d?{pointerEvents:"none"}:{}},R);var A=t("div",{staticClass:"b-calendar-grid-help border-top small text-muted text-center bg-light",attrs:{id:l}},[t("div",{staticClass:"small"},this.labelHelp)]),M=t("div",{staticClass:"b-calendar-grid form-control h-auto text-center",attrs:{id:s,role:"application",tabindex:h?"-1":d?null:"0","data-month":m.slice(0,-3),"aria-roledescription":this.labelCalendar||null,"aria-labelledby":a,"aria-describedby":l,"aria-disabled":d?"true":null,"aria-activedescendant":u},on:{keydown:this.onKeydownGrid,focus:this.setGridFocusFlag,blur:this.setGridFocusFlag},ref:"grid"},[V,L,R,A]),H=this.normalizeSlot();H=H?t("footer",{staticClass:"b-calendar-footer"},H):t();var z=t("div",{staticClass:"b-calendar-inner",style:this.block?{}:{width:this.width},attrs:{id:n,dir:p?"rtl":"ltr",lang:this.computedLocale||null,role:"group","aria-disabled":d?"true":null,"aria-controls":this.ariaControls||null,"aria-roledescription":this.roleDescription||null,"aria-describedby":[this.bvAttrs["aria-describedby"],i,l].filter(pe).join(" ")},on:{keydown:this.onKeydownWrapper}},[w,E,M,H]);return t("div",{staticClass:"b-calendar",class:{"d-block":this.block}},[z])}}),Gc=$e({components:{BCalendar:jc}}),Wc=Is({bgVariant:Ds(Fr),borderVariant:Ds(Fr),tag:Ds(Fr,"div"),textVariant:Ds(Fr)},We);L({props:Wc});var Uc=Is({title:Ds(Fr),titleTag:Ds(Fr,"h4")},ti),Yc=L({name:ti,functional:!0,props:Uc,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.titleTag,I(n,{staticClass:"card-title"}),r||Io(i.title))}}),qc=Is({subTitle:Ds(Fr),subTitleTag:Ds(Fr,"h6"),subTitleTextVariant:Ds(Fr,"muted")},Je),Kc=L({name:Je,functional:!0,props:qc,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.subTitleTag,I(n,{staticClass:"card-subtitle",class:[i.subTitleTextVariant?"text-".concat(i.subTitleTextVariant):null]}),r||Io(i.subTitle))}}),Xc=Is(he(r(r(r(r({},Uc),qc),_s(Wc,xs.bind(null,"body"))),{},{bodyClass:Ds(Or),overlay:Ds(kr,!1)})),Ue),Zc=L({name:Ue,functional:!0,props:Xc,render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.bodyBgVariant,a=n.bodyBorderVariant,l=n.bodyTextVariant,u=t();n.title&&(u=t(Yc,{props:Fs(Uc,n)}));var d=t();return n.subTitle&&(d=t(Kc,{props:Fs(qc,n),class:["mb-2"]})),t(n.bodyTag,I(r,{staticClass:"card-body",class:[(i={"card-img-overlay":n.overlay},c(i,"bg-".concat(s),s),c(i,"border-".concat(a),a),c(i,"text-".concat(l),l),i),n.bodyClass]}),[u,d,o])}}),Jc=Is(he(r(r({},_s(Wc,xs.bind(null,"header"))),{},{header:Ds(Fr),headerClass:Ds(Or),headerHtml:Ds(Fr)})),Ke),Qc=L({name:Ke,functional:!0,props:Jc,render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.headerBgVariant,a=n.headerBorderVariant,l=n.headerTextVariant;return t(n.headerTag,I(r,{staticClass:"card-header",class:[n.headerClass,(i={},c(i,"bg-".concat(s),s),c(i,"border-".concat(a),a),c(i,"text-".concat(l),l),i)],domProps:o?{}:Zl(n.headerHtml,n.header)}),o)}}),tu=Is(he(r(r({},_s(Wc,xs.bind(null,"footer"))),{},{footer:Ds(Fr),footerClass:Ds(Or),footerHtml:Ds(Fr)})),Ye),eu=L({name:Ye,functional:!0,props:tu,render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.footerBgVariant,a=n.footerBorderVariant,l=n.footerTextVariant;return t(n.footerTag,I(r,{staticClass:"card-footer",class:[n.footerClass,(i={},c(i,"bg-".concat(s),s),c(i,"border-".concat(a),a),c(i,"text-".concat(l),l),i)],domProps:o?{}:Zl(n.footerHtml,n.footer)}),o)}}),iu='<svg width="%{w}" height="%{h}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 %{w} %{h}" preserveAspectRatio="none"><rect width="100%" height="100%" style="fill:%{f};"></rect></svg>',nu=Is({alt:Ds(Fr),blank:Ds(kr,!1),blankColor:Ds(Fr,"transparent"),block:Ds(kr,!1),center:Ds(kr,!1),fluid:Ds(kr,!1),fluidGrow:Ds(kr,!1),height:Ds(Hr),left:Ds(kr,!1),right:Ds(kr,!1),rounded:Ds(Rr,!1),sizes:Ds(Er),src:Ds(Fr),srcset:Ds(Er),thumbnail:Ds(kr,!1),width:Ds(Hr)},Mi),ru=L({name:Mi,functional:!0,props:nu,render:function(t,e){var i,n=e.props,r=e.data,o=n.alt,s=n.src,a=n.block,l=n.fluidGrow,u=n.rounded,d=ko(n.width)||null,h=ko(n.height)||null,f=null,p=go(n.srcset).filter(pe).join(","),m=go(n.sizes).filter(pe).join(",");return n.blank&&(!h&&d?h=d:!d&&h&&(d=h),d||h||(d=1,h=1),s=function(t,e,i){var n=encodeURIComponent(iu.replace("%{w}",Io(t)).replace("%{h}",Io(e)).replace("%{f}",i));return"data:image/svg+xml;charset=UTF-8,".concat(n)}(d,h,n.blankColor||"transparent"),p=null,m=null),n.left?f="float-left":n.right?f="float-right":n.center&&(f="mx-auto",a=!0),t("img",I(r,{attrs:{src:s,alt:o,width:d?Io(d):null,height:h?Io(h):null,srcset:p||null,sizes:m||null},class:(i={"img-thumbnail":n.thumbnail,"img-fluid":n.fluid||l,"w-100":l,rounded:""===u||!0===u},c(i,"rounded-".concat(u),Ut(u)&&""!==u),c(i,f,f),c(i,"d-block",a),i)}))}}),ou=Is(he(r(r({},ce(nu,["src","alt","width","height","left","right"])),{},{bottom:Ds(kr,!1),end:Ds(kr,!1),start:Ds(kr,!1),top:Ds(kr,!1)})),Xe),su=L({name:Xe,functional:!0,props:ou,render:function(t,e){var i=e.props,n=e.data,r=i.src,o=i.alt,s=i.width,a=i.height,l="card-img";return i.top?l+="-top":i.right||i.end?l+="-right":i.bottom?l+="-bottom":(i.left||i.start)&&(l+="-left"),t("img",I(n,{class:l,attrs:{src:r,alt:o,width:s,height:a}}))}}),au=_s(ou,xs.bind(null,"img"));au.imgSrc.required=!1;var lu,cu,uu,du=Is(he(r(r(r(r(r(r({},Xc),Jc),tu),au),Wc),{},{align:Ds(Fr),noBody:Ds(kr,!1)})),We),hu=L({name:We,functional:!0,props:du,render:function(t,e){var i,n=e.props,r=e.data,o=e.slots,s=e.scopedSlots,a=n.imgSrc,l=n.imgLeft,u=n.imgRight,d=n.imgStart,h=n.imgEnd,f=n.imgBottom,p=n.header,m=n.headerHtml,v=n.footer,g=n.footerHtml,b=n.align,y=n.textVariant,T=n.bgVariant,w=n.borderVariant,C=s||{},S=o(),k={},x=t(),$=t();if(a){var B=t(su,{props:Fs(au,n,$s.bind(null,"img"))});f?$=B:x=B}var D=t(),_=wo(to,C,S);(_||p||m)&&(D=t(Qc,{props:Fs(Jc,n),domProps:_?{}:Zl(m,p)},Co(to,k,C,S)));var F=Co(Kr,k,C,S);n.noBody||(F=t(Zc,{props:Fs(Xc,n)},F),n.overlay&&a&&(F=t("div",{staticClass:"position-relative"},[x,F,$]),x=t(),$=t()));var P=t();return(wo(Qr,C,S)||v||g)&&(P=t(eu,{props:Fs(tu,n),domProps:_?{}:Zl(g,v)},Co(Qr,k,C,S))),t(n.tag,I(r,{staticClass:"card",class:(i={"flex-row":l||d,"flex-row-reverse":(u||h)&&!(l||d)},c(i,"text-".concat(b),b),c(i,"bg-".concat(T),T),c(i,"border-".concat(w),w),c(i,"text-".concat(y),y),i)}),[x,D,F,P,$])}}),fu="__bv__visibility_observer",pu=function(){function t(e,i){s(this,t),this.el=e,this.callback=i.callback,this.margin=i.margin||0,this.once=i.once||!1,this.observer=null,this.visible=void 0,this.doneOnce=!1,this.createObserver()}return l(t,[{key:"createObserver",value:function(){var t=this;if(this.observer&&this.stop(),!this.doneOnce&&Gt(this.callback)){try{this.observer=new IntersectionObserver(this.handler.bind(this),{root:null,rootMargin:this.margin,threshold:0})}catch(t){return this.doneOnce=!0,this.observer=void 0,void this.callback(null)}z((function(){Mo((function(){t.observer&&t.observer.observe(t.el)}))}))}}},{key:"handler",value:function(t){var e=t?t[0]:{},i=Boolean(e.isIntersecting||e.intersectionRatio>0);i!==this.visible&&(this.visible=i,this.callback(i),this.once&&this.visible&&(this.doneOnce=!0,this.stop()))}},{key:"stop",value:function(){this.observer&&this.observer.disconnect(),this.observer=null}}]),t}(),mu=function(t){var e=t[fu];e&&e.stop&&e.stop(),delete t[fu]},vu=function(t,e){var i=e.value,n=e.modifiers,r={margin:"0px",once:!1,callback:i};se(n).forEach((function(t){ut.test(t)?r.margin="".concat(t,"px"):"once"===t.toLowerCase()&&(r.once=!0)})),mu(t),t[fu]=new pu(t,r),t[fu]._prevModifiers=le(n)},gu={bind:vu,componentUpdated:function(t,e,i){var n=e.value,r=e.oldValue,o=e.modifiers;o=le(o),!t||n===r&&t[fu]&&ll(o,t[fu]._prevModifiers)||vu(t,{value:n,modifiers:o})},unbind:function(t){mu(t)}},bu="show",yu=ue(nu,["blank"]),Tu=Is(r(r({},yu),{},c({blankHeight:Ds(Hr),blankSrc:Ds(Fr,null),blankWidth:Ds(Hr),offset:Ds(Hr,360)},bu,Ds(kr,!1))),Hi),wu=L({name:Hi,directives:{"b-visible":gu},props:Tu,data:function(){return{isShown:this.show}},computed:{computedSrc:function(){var t=this.blankSrc;return!t||this.isShown?this.src:t},computedBlank:function(){return!(this.isShown||this.blankSrc)},computedWidth:function(){var t=this.width;return this.isShown?t:this.blankWidth||t},computedHeight:function(){var t=this.height;return this.isShown?t:this.blankHeight||t},computedSrcset:function(){var t=go(this.srcset).filter(pe).join(",");return!t||this.blankSrc&&!this.isShown?null:t},computedSizes:function(){var t=go(this.sizes).filter(pe).join(",");return!t||this.blankSrc&&!this.isShown?null:t}},watch:(lu={},c(lu,bu,(function(t,e){if(t!==e){var i=!ot||t;this.isShown=i,t!==i&&this.$nextTick(this.updateShowProp)}})),c(lu,"isShown",(function(t,e){t!==e&&this.updateShowProp()})),lu),mounted:function(){var t=this;this.$nextTick((function(){t.isShown=!ot||t.show}))},methods:{updateShowProp:function(){this.$emit("update:show",this.isShown)},doShow:function(t){var e=this;!t&&null!==t||this.isShown||Mo((function(){e.isShown=!0}))}},render:function(t){var e,i=[];this.isShown||i.push({name:"b-visible",value:this.doShow,modifiers:(e={},c(e,"".concat(ko(this.offset,0)),!0),c(e,"once",!0),e)});return t(ru,{directives:i,props:r(r({},Fs(yu,this.$props)),{},{src:this.computedSrc,blank:this.computedBlank,width:this.computedWidth,height:this.computedHeight,srcset:this.computedSrcset,sizes:this.computedSizes})})}}),Cu=Is(he(r(r({},ue(Tu,se(nu))),ue(ou,["src","alt","width","height"]))),Ze),Su=L({name:Ze,functional:!0,props:Cu,render:function(t,e){var i=e.props,n=e.data,r="card-img";return i.top?r+="-top":i.right||i.end?r+="-right":i.bottom?r+="-bottom":(i.left||i.start)&&(r+="-left"),t(wu,I(n,{class:[r],props:ue(i,["left","right"])}))}}),ku=Is({textTag:Ds(Fr,"p")},Qe),xu=L({name:Qe,functional:!0,props:ku,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.textTag,I(n,{staticClass:"card-text"}),r)}}),$u=Is({columns:Ds(kr,!1),deck:Ds(kr,!1),tag:Ds(Fr,"div")},qe),Bu=L({name:qe,functional:!0,props:$u,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{class:i.deck?"card-deck":i.columns?"card-columns":"card-group"}),r)}}),Du=$e({components:{BCard:hu,BCardHeader:Qc,BCardBody:Zc,BCardTitle:Yc,BCardSubTitle:Kc,BCardFooter:eu,BCardImg:su,BCardImgLazy:Su,BCardText:xu,BCardGroup:Bu}}),_u=function(){},Fu=function(t,e,i){if(t=t?t.$el||t:null,!zo(t))return null;if(n="observeDom",!U&&(ye("".concat(n,": Requires MutationObserver support.")),1))return null;var n,o=new Ho((function(t){for(var i=!1,n=0;n<t.length&&!i;n++){var r=t[n],o=r.type,s=r.target;("characterData"===o&&s.nodeType===Node.TEXT_NODE||"attributes"===o||"childList"===o&&(r.addedNodes.length>0||r.removedNodes.length>0))&&(i=!0)}i&&e()}));return o.observe(t,r({childList:!0,subtree:!0},i)),o},Pu=Vs("value",{type:Br,defaultValue:0}),Iu=Pu.mixin,Ou=Pu.props,Eu=Pu.prop,Vu=Pu.event,Lu={next:{dirClass:"carousel-item-left",overlayClass:"carousel-item-next"},prev:{dirClass:"carousel-item-right",overlayClass:"carousel-item-prev"}},Ru={TOUCH:"touch",PEN:"pen"},Au={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"otransitionend oTransitionEnd",transition:"transitionend"},Mu=Is(he(r(r(r({},Vc),Ou),{},{background:Ds(Fr),controls:Ds(kr,!1),fade:Ds(kr,!1),imgHeight:Ds(Hr),imgWidth:Ds(Hr),indicators:Ds(kr,!1),interval:Ds(Br,5e3),labelGotoSlide:Ds(Fr,"Goto slide"),labelIndicators:Ds(Fr,"Select a slide to display"),labelNext:Ds(Fr,"Next slide"),labelPrev:Ds(Fr,"Previous slide"),noAnimation:Ds(kr,!1),noHoverPause:Ds(kr,!1),noTouch:Ds(kr,!1),noWrap:Ds(kr,!1)})),ei),Hu=L({name:ei,mixins:[Lc,Iu,So],provide:function(){var t=this;return{getBvCarousel:function(){return t}}},props:Mu,data:function(){return{index:this[Eu]||0,isSliding:!1,transitionEndEvent:null,slides:[],direction:null,isPaused:!(ko(this.interval,0)>0),touchStartX:0,touchDeltaX:0}},computed:{numSlides:function(){return this.slides.length}},watch:(cu={},c(cu,Eu,(function(t,e){t!==e&&this.setSlide(ko(t,0))})),c(cu,"interval",(function(t,e){t!==e&&(t?(this.pause(!0),this.start(!1)):this.pause(!1))})),c(cu,"isPaused",(function(t,e){t!==e&&this.$emit(t?"paused":"unpaused")})),c(cu,"index",(function(t,e){t===e||this.isSliding||this.doSlide(t,e)})),cu),created:function(){this.$_interval=null,this.$_animationTimeout=null,this.$_touchTimeout=null,this.$_observer=null,this.isPaused=!(ko(this.interval,0)>0)},mounted:function(){this.transitionEndEvent=function(t){for(var e in Au)if(!zt(t.style[e]))return Au[e];return null}(this.$el)||null,this.updateSlides(),this.setObserver(!0)},beforeDestroy:function(){this.clearInterval(),this.clearAnimationTimeout(),this.clearTouchTimeout(),this.setObserver(!1)},methods:{clearInterval:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){clearInterval(this.$_interval),this.$_interval=null})),clearAnimationTimeout:function(){clearTimeout(this.$_animationTimeout),this.$_animationTimeout=null},clearTouchTimeout:function(){clearTimeout(this.$_touchTimeout),this.$_touchTimeout=null},setObserver:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,t&&(this.$_observer=Fu(this.$refs.inner,this.updateSlides.bind(this),{subtree:!1,childList:!0,attributes:!0,attributeFilter:["id"]}))},setSlide:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!(Y&&document.visibilityState&&document.hidden)){var n=this.noWrap,r=this.numSlides;t=ca(t),0!==r&&(this.isSliding?this.$once(mr,(function(){Mo((function(){return e.setSlide(t,i)}))})):(this.direction=i,this.index=t>=r?n?r-1:0:t<0?n?0:r-1:t,n&&this.index!==t&&this.index!==this[Eu]&&this.$emit(Vu,this.index)))}},prev:function(){this.setSlide(this.index-1,"prev")},next:function(){this.setSlide(this.index+1,"next")},pause:function(t){t||(this.isPaused=!0),this.clearInterval()},start:function(t){t||(this.isPaused=!1),this.clearInterval(),this.interval&&this.numSlides>1&&(this.$_interval=setInterval(this.next,sa(1e3,this.interval)))},restart:function(){this.$el.contains(No())||this.start()},doSlide:function(t,e){var i=this,n=Boolean(this.interval),r=this.calcDirection(this.direction,e,t),o=r.overlayClass,s=r.dirClass,a=this.slides[e],l=this.slides[t];if(a&&l){if(this.isSliding=!0,n&&this.pause(!1),this.$emit("sliding-start",t),this.$emit(Vu,this.index),this.noAnimation)ts(l,"active"),es(a,"active"),this.isSliding=!1,this.$nextTick((function(){return i.$emit(mr,t)}));else{ts(l,o),Yo(l),ts(a,s),ts(l,s);var c=!1,u=function e(){if(!c){if(c=!0,i.transitionEndEvent)i.transitionEndEvent.split(/\s+/).forEach((function(t){return As(l,t,e,wr)}));i.clearAnimationTimeout(),es(l,s),es(l,o),ts(l,"active"),es(a,"active"),es(a,s),es(a,o),ns(a,"aria-current","false"),ns(l,"aria-current","true"),ns(a,"aria-hidden","true"),ns(l,"aria-hidden","false"),i.isSliding=!1,i.direction=null,i.$nextTick((function(){return i.$emit(mr,t)}))}};if(this.transitionEndEvent)this.transitionEndEvent.split(/\s+/).forEach((function(t){return Rs(l,t,u,wr)}));this.$_animationTimeout=setTimeout(u,650)}n&&this.start(!1)}},updateSlides:function(){this.pause(!0),this.slides=qo(".carousel-item",this.$refs.inner);var t=this.slides.length,e=sa(0,oa(ca(this.index),t-1));this.slides.forEach((function(i,n){var r=n+1;n===e?(ts(i,"active"),ns(i,"aria-current","true")):(es(i,"active"),ns(i,"aria-current","false")),ns(i,"aria-posinset",String(r)),ns(i,"aria-setsize",String(t))})),this.setSlide(e),this.start(this.isPaused)},calcDirection:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return t?Lu[t]:i>e?Lu.next:Lu.prev},handleClick:function(t,e){var i=t.keyCode;"click"!==t.type&&i!==ol&&i!==el||(Hs(t),e())},handleSwipe:function(){var t=aa(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;this.touchDeltaX=0,e>0?this.prev():e<0&&this.next()}},touchStart:function(t){rt&&Ru[t.pointerType.toUpperCase()]?this.touchStartX=t.clientX:rt||(this.touchStartX=t.touches[0].clientX)},touchMove:function(t){t.touches&&t.touches.length>1?this.touchDeltaX=0:this.touchDeltaX=t.touches[0].clientX-this.touchStartX},touchEnd:function(t){rt&&Ru[t.pointerType.toUpperCase()]&&(this.touchDeltaX=t.clientX-this.touchStartX),this.handleSwipe(),this.pause(!1),this.clearTouchTimeout(),this.$_touchTimeout=setTimeout(this.start,500+sa(1e3,this.interval))}},render:function(t){var e=this,i=this.indicators,n=this.background,r=this.noAnimation,o=this.noHoverPause,s=this.noTouch,a=this.index,l=this.isSliding,c=this.pause,u=this.restart,d=this.touchStart,h=this.touchEnd,f=this.safeId("__BV_inner_"),p=t("div",{staticClass:"carousel-inner",attrs:{id:f,role:"list"},ref:"inner"},[this.normalizeSlot()]),m=t();if(this.controls){var v=function(i,n,r){var o=function(t){l?Hs(t,{propagation:!1}):e.handleClick(t,r)};return t("a",{staticClass:"carousel-control-".concat(i),attrs:{href:"#",role:"button","aria-controls":f,"aria-disabled":l?"true":null},on:{click:o,keydown:o}},[t("span",{staticClass:"carousel-control-".concat(i,"-icon"),attrs:{"aria-hidden":"true"}}),t("span",{class:"sr-only"},[n])])};m=[v("prev",this.labelPrev,this.prev),v("next",this.labelNext,this.next)]}var g=t("ol",{staticClass:"carousel-indicators",directives:[{name:"show",value:i}],attrs:{id:this.safeId("__BV_indicators_"),"aria-hidden":i?"false":"true","aria-label":this.labelIndicators,"aria-owns":f}},this.slides.map((function(n,r){var o=function(t){e.handleClick(t,(function(){e.setSlide(r)}))};return t("li",{class:{active:r===a},attrs:{role:"button",id:e.safeId("__BV_indicator_".concat(r+1,"_")),tabindex:i?"0":"-1","aria-current":r===a?"true":"false","aria-label":"".concat(e.labelGotoSlide," ").concat(r+1),"aria-describedby":n.id||null,"aria-controls":f},on:{click:o,keydown:o},key:"slide_".concat(r)})}))),b={mouseenter:o?_u:c,mouseleave:o?_u:u,focusin:c,focusout:u,keydown:function(t){if(!/input|textarea/i.test(t.target.tagName)){var i=t.keyCode;i!==nl&&i!==rl||(Hs(t),e[i===nl?"prev":"next"]())}}};return nt&&!s&&(rt?(b["&pointerdown"]=d,b["&pointerup"]=h):(b["&touchstart"]=d,b["&touchmove"]=this.touchMove,b["&touchend"]=h)),t("div",{staticClass:"carousel",class:{slide:!r,"carousel-fade":!r&&this.fade,"pointer-event":nt&&rt&&!s},style:{background:n},attrs:{role:"region",id:this.safeId(),"aria-busy":l?"true":"false"},on:b},[p,m,g])}}),zu={imgAlt:Ds(Fr),imgBlank:Ds(kr,!1),imgBlankColor:Ds(Fr,"transparent"),imgHeight:Ds(Hr),imgSrc:Ds(Fr),imgWidth:Ds(Hr)},Nu=Is(he(r(r(r({},Vc),zu),{},{background:Ds(Fr),caption:Ds(Fr),captionHtml:Ds(Fr),captionTag:Ds(Fr,"h3"),contentTag:Ds(Fr,"div"),contentVisibleUp:Ds(Fr),text:Ds(Fr),textHtml:Ds(Fr),textTag:Ds(Fr,"p")})),ii),ju=L({name:ii,mixins:[Lc,So],inject:{getBvCarousel:{default:function(){return function(){return{noTouch:!0}}}}},props:Nu,computed:{bvCarousel:function(){return this.getBvCarousel()},contentClasses:function(){return[this.contentVisibleUp?"d-none":"",this.contentVisibleUp?"d-".concat(this.contentVisibleUp,"-block"):""]},computedWidth:function(){return this.imgWidth||this.bvCarousel.imgWidth||null},computedHeight:function(){return this.imgHeight||this.bvCarousel.imgHeight||null}},render:function(t){var e=this.normalizeSlot("img");if(!e&&(this.imgSrc||this.imgBlank)){var i={};!this.bvCarousel.noTouch&&nt&&(i.dragstart=function(t){return Hs(t,{propagation:!1})}),e=t(ru,{props:r(r({},Fs(zu,this.$props,$s.bind(null,"img"))),{},{width:this.computedWidth,height:this.computedHeight,fluidGrow:!0,block:!0}),on:i})}var n=[!(!this.caption&&!this.captionHtml)&&t(this.captionTag,{domProps:Zl(this.captionHtml,this.caption)}),!(!this.text&&!this.textHtml)&&t(this.textTag,{domProps:Zl(this.textHtml,this.text)}),this.normalizeSlot()||!1],o=t();return n.some(pe)&&(o=t(this.contentTag,{staticClass:"carousel-caption",class:this.contentClasses},n.map((function(e){return e||t()})))),t("div",{staticClass:"carousel-item",style:{background:this.background||this.bvCarousel.background||null},attrs:{id:this.safeId(),role:"listitem"}},[e,o])}}),Gu=$e({components:{BCarousel:Hu,BCarouselSlide:ju}}),Wu="show",Uu={css:!0,enterClass:"",enterActiveClass:"collapsing",enterToClass:"collapse show",leaveClass:"collapse show",leaveActiveClass:"collapsing",leaveToClass:"collapse"},Yu={enter:function(t){as(t,"height",0),Mo((function(){Yo(t),as(t,"height","".concat(t.scrollHeight,"px"))}))},afterEnter:function(t){ls(t,"height")},leave:function(t){as(t,"height","auto"),as(t,"display","block"),as(t,"height","".concat(us(t).height,"px")),Yo(t),as(t,"height",0)},afterLeave:function(t){ls(t,"height")}},qu={appear:Ds(kr,!1)},Ku=L({name:"BVCollapse",functional:!0,props:qu,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t("transition",I(n,{props:Uu,on:Yu},{props:i}),r)}}),Xu=js(ri,"toggle"),Zu=js(ri,"request-state"),Ju=Ns(ri,"accordion"),Qu=Ns(ri,"state"),td=Ns(ri,"sync-state"),ed=Vs("visible",{type:kr,defaultValue:!1}),id=ed.mixin,nd=ed.props,rd=ed.prop,od=ed.event,sd=Is(he(r(r(r({},Vc),nd),{},{accordion:Ds(Fr),appear:Ds(kr,!1),isNav:Ds(kr,!1),tag:Ds(Fr,"div")})),ri),ad=L({name:ri,mixins:[Lc,id,So,gl],props:sd,data:function(){return{show:this[rd],transitioning:!1}},computed:{classObject:function(){var t=this.transitioning;return{"navbar-collapse":this.isNav,collapse:!t,show:this.show&&!t}},slotScope:function(){var t=this;return{visible:this.show,close:function(){t.show=!1}}}},watch:(uu={},c(uu,rd,(function(t){t!==this.show&&(this.show=t)})),c(uu,"show",(function(t,e){t!==e&&this.emitState()})),uu),created:function(){this.show=this[rd]},mounted:function(){var t=this;this.show=this[rd],this.listenOnRoot(Xu,this.handleToggleEvent),this.listenOnRoot(Ju,this.handleAccordionEvent),this.isNav&&(this.setWindowEvents(!0),this.handleResize()),this.$nextTick((function(){t.emitState()})),this.listenOnRoot(Zu,(function(e){e===t.safeId()&&t.$nextTick(t.emitSync)}))},updated:function(){this.emitSync()},deactivated:function(){this.isNav&&this.setWindowEvents(!1)},activated:function(){this.isNav&&this.setWindowEvents(!0),this.emitSync()},beforeDestroy:function(){this.show=!1,this.isNav&&Y&&this.setWindowEvents(!1)},methods:{setWindowEvents:function(t){Ms(t,window,"resize",this.handleResize,wr),Ms(t,window,"orientationchange",this.handleResize,wr)},toggle:function(){this.show=!this.show},onEnter:function(){this.transitioning=!0,this.$emit(fr)},onAfterEnter:function(){this.transitioning=!1,this.$emit(pr)},onLeave:function(){this.transitioning=!0,this.$emit(er)},onAfterLeave:function(){this.transitioning=!1,this.$emit(tr)},emitState:function(){var t=this.show,e=this.accordion,i=this.safeId();this.$emit(od,t),this.emitOnRoot(Qu,i,t),e&&t&&this.emitOnRoot(Ju,i,e)},emitSync:function(){this.emitOnRoot(td,this.safeId(),this.show)},checkDisplayBlock:function(){var t=this.$el,e=is(t,Wu);es(t,Wu);var i="block"===ds(t).display;return e&&ts(t,Wu),i},clickHandler:function(t){var e=t.target;this.isNav&&e&&"block"===ds(this.$el).display&&(!Xo(e,".nav-link,.dropdown-item")&&!Zo(".nav-link,.dropdown-item",e)||this.checkDisplayBlock()||(this.show=!1))},handleToggleEvent:function(t){t===this.safeId()&&this.toggle()},handleAccordionEvent:function(t,e){var i=this.accordion,n=this.show;if(i&&i===e){var r=t===this.safeId();(r&&!n||!r&&n)&&this.toggle()}},handleResize:function(){this.show="block"===ds(this.$el).display}},render:function(t){var e=this.appear,i=t(this.tag,{class:this.classObject,directives:[{name:"show",value:this.show}],attrs:{id:this.safeId()},on:{click:this.clickHandler}},this.normalizeSlot(Kr,this.slotScope));return t(Ku,{props:{appear:e},on:{enter:this.onEnter,afterEnter:this.onAfterEnter,leave:this.onLeave,afterLeave:this.onAfterLeave}},[i])}}),ld=function(t,e){return O?e.instance:t.context},cd="collapsed",ud="not-collapsed",dd="__BV_toggle",hd="".concat(dd,"_HANDLER__"),fd="".concat(dd,"_CLICK__"),pd="".concat(dd,"_STATE__"),md="".concat(dd,"_TARGETS__"),vd="aria-controls",gd="aria-expanded",bd="role",yd="tabindex",Td="overflow-anchor",wd=js(ri,"toggle"),Cd=Ns(ri,"state"),Sd=Ns(ri,"sync-state"),kd=js(ri,"request-state"),xd=[el,ol],$d=function(t){return!vo(["button","a"],t.tagName.toLowerCase())},Bd=function(t){var e=t[fd];e&&(As(t,"click",e,Tr),As(t,"keydown",e,Tr)),t[fd]=null},Dd=function(t,e){t[hd]&&e&&ml(e).$off([Cd,Sd],t[hd]),t[hd]=null},_d=function(t,e){e?(es(t,cd),ts(t,ud),ns(t,gd,"true")):(es(t,ud),ts(t,cd),ns(t,gd,"false"))},Fd=function(t,e){t[e]=null,delete t[e]},Pd=function(t,e,i){if(Y&&ld(i,e)){$d(t)&&(ss(t,bd)||ns(t,bd,"button"),ss(t,yd)||ns(t,yd,"0")),_d(t,t[pd]);var n=function(t,e){var i=t.modifiers,n=t.arg,r=t.value,o=se(i||{});if(r=Ut(r)?r.split(wt):r,jo(e.tagName,"a")){var s=os(e,"href")||"";ft.test(s)&&o.push(s.replace(ht,""))}return go(n,r).forEach((function(t){return Ut(t)&&o.push(t)})),o.filter((function(t,e,i){return t&&i.indexOf(t)===e}))}(e,t);n.length>0?(ns(t,vd,n.join(" ")),as(t,Td,"none")):(rs(t,vd),ls(t,Td)),Mo((function(){!function(t,e){if(Bd(t),e){var i=function(i){"keydown"===i.type&&!vo(xd,i.keyCode)||Uo(t)||(t[md]||[]).forEach((function(t){ml(e).$emit(wd,t)}))};t[fd]=i,Rs(t,"click",i,Tr),$d(t)&&Rs(t,"keydown",i,Tr)}}(t,ld(i,e))})),ll(n,t[md])||(t[md]=n,n.forEach((function(t){ml(ld(i,e)).$emit(kd,t)})))}},Id={bind:function(t,e,i){t[pd]=!1,t[md]=[],function(t,e){if(Dd(t,e),e){var i=function(e,i){vo(t[md]||[],e)&&(t[pd]=i,_d(t,i))};t[hd]=i,ml(e).$on([Cd,Sd],i)}}(t,ld(i,e)),Pd(t,e,i)},componentUpdated:Pd,updated:Pd,unbind:function(t,e,i){Bd(t),Dd(t,ld(i,e)),Fd(t,hd),Fd(t,fd),Fd(t,pd),Fd(t,md),es(t,cd),es(t,ud),rs(t,gd),rs(t,vd),rs(t,bd),ls(t,Td)}},Od=$e({directives:{VBToggle:Id}}),Ed=$e({components:{BCollapse:ad},plugins:{VBTogglePlugin:Od}}),Vd="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Ld=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(Vd&&navigator.userAgent.indexOf(t[e])>=0)return 1;return 0}();var Rd=Vd&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then((function(){e=!1,t()})))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout((function(){e=!1,t()}),Ld))}};function Ad(t){return t&&"[object Function]"==={}.toString.call(t)}function Md(t,e){if(1!==t.nodeType)return[];var i=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?i[e]:i}function Hd(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function zd(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=Md(t),i=e.overflow,n=e.overflowX,r=e.overflowY;return/(auto|scroll|overlay)/.test(i+r+n)?t:zd(Hd(t))}function Nd(t){return t&&t.referenceNode?t.referenceNode:t}var jd=Vd&&!(!window.MSInputMethodContext||!document.documentMode),Gd=Vd&&/MSIE 10/.test(navigator.userAgent);function Wd(t){return 11===t?jd:10===t?Gd:jd||Gd}function Ud(t){if(!t)return document.documentElement;for(var e=Wd(10)?document.body:null,i=t.offsetParent||null;i===e&&t.nextElementSibling;)i=(t=t.nextElementSibling).offsetParent;var n=i&&i.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===Md(i,"position")?Ud(i):i:t?t.ownerDocument.documentElement:document.documentElement}function Yd(t){return null!==t.parentNode?Yd(t.parentNode):t}function qd(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var i=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,n=i?t:e,r=i?e:t,o=document.createRange();o.setStart(n,0),o.setEnd(r,0);var s,a,l=o.commonAncestorContainer;if(t!==l&&e!==l||n.contains(r))return"BODY"===(a=(s=l).nodeName)||"HTML"!==a&&Ud(s.firstElementChild)!==s?Ud(l):l;var c=Yd(t);return c.host?qd(c.host,e):qd(t,Yd(e).host)}function Kd(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",i="top"===e?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"===n||"HTML"===n){var r=t.ownerDocument.documentElement,o=t.ownerDocument.scrollingElement||r;return o[i]}return t[i]}function Xd(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=Kd(e,"top"),r=Kd(e,"left"),o=i?-1:1;return t.top+=n*o,t.bottom+=n*o,t.left+=r*o,t.right+=r*o,t}function Zd(t,e){var i="x"===e?"Left":"Top",n="Left"===i?"Right":"Bottom";return parseFloat(t["border"+i+"Width"])+parseFloat(t["border"+n+"Width"])}function Jd(t,e,i,n){return Math.max(e["offset"+t],e["scroll"+t],i["client"+t],i["offset"+t],i["scroll"+t],Wd(10)?parseInt(i["offset"+t])+parseInt(n["margin"+("Height"===t?"Top":"Left")])+parseInt(n["margin"+("Height"===t?"Bottom":"Right")]):0)}function Qd(t){var e=t.body,i=t.documentElement,n=Wd(10)&&getComputedStyle(i);return{height:Jd("Height",e,i,n),width:Jd("Width",e,i,n)}}var th=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},eh=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),ih=function(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t},nh=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t};function rh(t){return nh({},t,{right:t.left+t.width,bottom:t.top+t.height})}function oh(t){var e={};try{if(Wd(10)){e=t.getBoundingClientRect();var i=Kd(t,"top"),n=Kd(t,"left");e.top+=i,e.left+=n,e.bottom+=i,e.right+=n}else e=t.getBoundingClientRect()}catch(t){}var r={left:e.left,top:e.top,width:e.right-e.left,height:e.bottom-e.top},o="HTML"===t.nodeName?Qd(t.ownerDocument):{},s=o.width||t.clientWidth||r.width,a=o.height||t.clientHeight||r.height,l=t.offsetWidth-s,c=t.offsetHeight-a;if(l||c){var u=Md(t);l-=Zd(u,"x"),c-=Zd(u,"y"),r.width-=l,r.height-=c}return rh(r)}function sh(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=Wd(10),r="HTML"===e.nodeName,o=oh(t),s=oh(e),a=zd(t),l=Md(e),c=parseFloat(l.borderTopWidth),u=parseFloat(l.borderLeftWidth);i&&r&&(s.top=Math.max(s.top,0),s.left=Math.max(s.left,0));var d=rh({top:o.top-s.top-c,left:o.left-s.left-u,width:o.width,height:o.height});if(d.marginTop=0,d.marginLeft=0,!n&&r){var h=parseFloat(l.marginTop),f=parseFloat(l.marginLeft);d.top-=c-h,d.bottom-=c-h,d.left-=u-f,d.right-=u-f,d.marginTop=h,d.marginLeft=f}return(n&&!i?e.contains(a):e===a&&"BODY"!==a.nodeName)&&(d=Xd(d,e)),d}function ah(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=t.ownerDocument.documentElement,n=sh(t,i),r=Math.max(i.clientWidth,window.innerWidth||0),o=Math.max(i.clientHeight,window.innerHeight||0),s=e?0:Kd(i),a=e?0:Kd(i,"left"),l={top:s-n.top+n.marginTop,left:a-n.left+n.marginLeft,width:r,height:o};return rh(l)}function lh(t){var e=t.nodeName;if("BODY"===e||"HTML"===e)return!1;if("fixed"===Md(t,"position"))return!0;var i=Hd(t);return!!i&&lh(i)}function ch(t){if(!t||!t.parentElement||Wd())return document.documentElement;for(var e=t.parentElement;e&&"none"===Md(e,"transform");)e=e.parentElement;return e||document.documentElement}function uh(t,e,i,n){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o={top:0,left:0},s=r?ch(t):qd(t,Nd(e));if("viewport"===n)o=ah(s,r);else{var a=void 0;"scrollParent"===n?"BODY"===(a=zd(Hd(e))).nodeName&&(a=t.ownerDocument.documentElement):a="window"===n?t.ownerDocument.documentElement:n;var l=sh(a,s,r);if("HTML"!==a.nodeName||lh(s))o=l;else{var c=Qd(t.ownerDocument),u=c.height,d=c.width;o.top+=l.top-l.marginTop,o.bottom=u+l.top,o.left+=l.left-l.marginLeft,o.right=d+l.left}}var h="number"==typeof(i=i||0);return o.left+=h?i:i.left||0,o.top+=h?i:i.top||0,o.right-=h?i:i.right||0,o.bottom-=h?i:i.bottom||0,o}function dh(t){return t.width*t.height}function hh(t,e,i,n,r){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var s=uh(i,n,o,r),a={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},l=Object.keys(a).map((function(t){return nh({key:t},a[t],{area:dh(a[t])})})).sort((function(t,e){return e.area-t.area})),c=l.filter((function(t){var e=t.width,n=t.height;return e>=i.clientWidth&&n>=i.clientHeight})),u=c.length>0?c[0].key:l[0].key,d=t.split("-")[1];return u+(d?"-"+d:"")}function fh(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=n?ch(e):qd(e,Nd(i));return sh(i,r,n)}function ph(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),i=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),n=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+n,height:t.offsetHeight+i}}function mh(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,(function(t){return e[t]}))}function vh(t,e,i){i=i.split("-")[0];var n=ph(t),r={width:n.width,height:n.height},o=-1!==["right","left"].indexOf(i),s=o?"top":"left",a=o?"left":"top",l=o?"height":"width",c=o?"width":"height";return r[s]=e[s]+e[l]/2-n[l]/2,r[a]=i===a?e[a]-n[c]:e[mh(a)],r}function gh(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function bh(t,e,i){var n=void 0===i?t:t.slice(0,function(t,e,i){if(Array.prototype.findIndex)return t.findIndex((function(t){return t[e]===i}));var n=gh(t,(function(t){return t[e]===i}));return t.indexOf(n)}(t,"name",i));return n.forEach((function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=t.function||t.fn;t.enabled&&Ad(i)&&(e.offsets.popper=rh(e.offsets.popper),e.offsets.reference=rh(e.offsets.reference),e=i(e,t))})),e}function yh(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=fh(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=hh(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=vh(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=bh(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function Th(t,e){return t.some((function(t){var i=t.name;return t.enabled&&i===e}))}function wh(t){for(var e=[!1,"ms","Webkit","Moz","O"],i=t.charAt(0).toUpperCase()+t.slice(1),n=0;n<e.length;n++){var r=e[n],o=r?""+r+i:t;if("undefined"!=typeof document.body.style[o])return o}return null}function Ch(){return this.state.isDestroyed=!0,Th(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[wh("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function Sh(t){var e=t.ownerDocument;return e?e.defaultView:window}function kh(t,e,i,n){var r="BODY"===t.nodeName,o=r?t.ownerDocument.defaultView:t;o.addEventListener(e,i,{passive:!0}),r||kh(zd(o.parentNode),e,i,n),n.push(o)}function xh(t,e,i,n){i.updateBound=n,Sh(t).addEventListener("resize",i.updateBound,{passive:!0});var r=zd(t);return kh(r,"scroll",i.updateBound,i.scrollParents),i.scrollElement=r,i.eventsEnabled=!0,i}function $h(){this.state.eventsEnabled||(this.state=xh(this.reference,this.options,this.state,this.scheduleUpdate))}function Bh(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,Sh(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach((function(t){t.removeEventListener("scroll",e.updateBound)})),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function Dh(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function _h(t,e){Object.keys(e).forEach((function(i){var n="";-1!==["width","height","top","right","bottom","left"].indexOf(i)&&Dh(e[i])&&(n="px"),t.style[i]=e[i]+n}))}var Fh=Vd&&/Firefox/i.test(navigator.userAgent);function Ph(t,e,i){var n=gh(t,(function(t){return t.name===e})),r=!!n&&t.some((function(t){return t.name===i&&t.enabled&&t.order<n.order}));if(!r){var o="`"+e+"`",s="`"+i+"`";console.warn(s+" modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return r}var Ih=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Oh=Ih.slice(3);function Eh(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=Oh.indexOf(t),n=Oh.slice(i+1).concat(Oh.slice(0,i));return e?n.reverse():n}var Vh="flip",Lh="clockwise",Rh="counterclockwise";function Ah(t,e,i,n){var r=[0,0],o=-1!==["right","left"].indexOf(n),s=t.split(/(\+|\-)/).map((function(t){return t.trim()})),a=s.indexOf(gh(s,(function(t){return-1!==t.search(/,|\s/)})));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,c=-1!==a?[s.slice(0,a).concat([s[a].split(l)[0]]),[s[a].split(l)[1]].concat(s.slice(a+1))]:[s];return c=c.map((function(t,n){var r=(1===n?!o:o)?"height":"width",s=!1;return t.reduce((function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,s=!0,t):s?(t[t.length-1]+=e,s=!1,t):t.concat(e)}),[]).map((function(t){return function(t,e,i,n){var r=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],s=r[2];if(!o)return t;if(0===s.indexOf("%")){return rh("%p"===s?i:n)[e]/100*o}if("vh"===s||"vw"===s)return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o;return o}(t,r,e,i)}))})),c.forEach((function(t,e){t.forEach((function(i,n){Dh(i)&&(r[e]+=i*("-"===t[n-1]?-1:1))}))})),r}var Mh={shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,i=e.split("-")[0],n=e.split("-")[1];if(n){var r=t.offsets,o=r.reference,s=r.popper,a=-1!==["bottom","top"].indexOf(i),l=a?"left":"top",c=a?"width":"height",u={start:ih({},l,o[l]),end:ih({},l,o[l]+o[c]-s[c])};t.offsets.popper=nh({},s,u[n])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var i=e.offset,n=t.placement,r=t.offsets,o=r.popper,s=r.reference,a=n.split("-")[0],l=void 0;return l=Dh(+i)?[+i,0]:Ah(i,o,s,a),"left"===a?(o.top+=l[0],o.left-=l[1]):"right"===a?(o.top+=l[0],o.left+=l[1]):"top"===a?(o.left+=l[0],o.top-=l[1]):"bottom"===a&&(o.left+=l[0],o.top+=l[1]),t.popper=o,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var i=e.boundariesElement||Ud(t.instance.popper);t.instance.reference===i&&(i=Ud(i));var n=wh("transform"),r=t.instance.popper.style,o=r.top,s=r.left,a=r[n];r.top="",r.left="",r[n]="";var l=uh(t.instance.popper,t.instance.reference,e.padding,i,t.positionFixed);r.top=o,r.left=s,r[n]=a,e.boundaries=l;var c=e.priority,u=t.offsets.popper,d={primary:function(t){var i=u[t];return u[t]<l[t]&&!e.escapeWithReference&&(i=Math.max(u[t],l[t])),ih({},t,i)},secondary:function(t){var i="right"===t?"left":"top",n=u[i];return u[t]>l[t]&&!e.escapeWithReference&&(n=Math.min(u[i],l[t]-("right"===t?u.width:u.height))),ih({},i,n)}};return c.forEach((function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";u=nh({},u,d[e](t))})),t.offsets.popper=u,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,i=e.popper,n=e.reference,r=t.placement.split("-")[0],o=Math.floor,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",l=s?"left":"top",c=s?"width":"height";return i[a]<o(n[l])&&(t.offsets.popper[l]=o(n[l])-i[c]),i[l]>o(n[a])&&(t.offsets.popper[l]=o(n[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var i;if(!Ph(t.instance.modifiers,"arrow","keepTogether"))return t;var n=e.element;if("string"==typeof n){if(!(n=t.instance.popper.querySelector(n)))return t}else if(!t.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var r=t.placement.split("-")[0],o=t.offsets,s=o.popper,a=o.reference,l=-1!==["left","right"].indexOf(r),c=l?"height":"width",u=l?"Top":"Left",d=u.toLowerCase(),h=l?"left":"top",f=l?"bottom":"right",p=ph(n)[c];a[f]-p<s[d]&&(t.offsets.popper[d]-=s[d]-(a[f]-p)),a[d]+p>s[f]&&(t.offsets.popper[d]+=a[d]+p-s[f]),t.offsets.popper=rh(t.offsets.popper);var m=a[d]+a[c]/2-p/2,v=Md(t.instance.popper),g=parseFloat(v["margin"+u]),b=parseFloat(v["border"+u+"Width"]),y=m-t.offsets.popper[d]-g-b;return y=Math.max(Math.min(s[c]-p,y),0),t.arrowElement=n,t.offsets.arrow=(ih(i={},d,Math.round(y)),ih(i,h,""),i),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(Th(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var i=uh(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),n=t.placement.split("-")[0],r=mh(n),o=t.placement.split("-")[1]||"",s=[];switch(e.behavior){case Vh:s=[n,r];break;case Lh:s=Eh(n);break;case Rh:s=Eh(n,!0);break;default:s=e.behavior}return s.forEach((function(a,l){if(n!==a||s.length===l+1)return t;n=t.placement.split("-")[0],r=mh(n);var c=t.offsets.popper,u=t.offsets.reference,d=Math.floor,h="left"===n&&d(c.right)>d(u.left)||"right"===n&&d(c.left)<d(u.right)||"top"===n&&d(c.bottom)>d(u.top)||"bottom"===n&&d(c.top)<d(u.bottom),f=d(c.left)<d(i.left),p=d(c.right)>d(i.right),m=d(c.top)<d(i.top),v=d(c.bottom)>d(i.bottom),g="left"===n&&f||"right"===n&&p||"top"===n&&m||"bottom"===n&&v,b=-1!==["top","bottom"].indexOf(n),y=!!e.flipVariations&&(b&&"start"===o&&f||b&&"end"===o&&p||!b&&"start"===o&&m||!b&&"end"===o&&v),T=!!e.flipVariationsByContent&&(b&&"start"===o&&p||b&&"end"===o&&f||!b&&"start"===o&&v||!b&&"end"===o&&m),w=y||T;(h||g||w)&&(t.flipped=!0,(h||g)&&(n=s[l+1]),w&&(o=function(t){return"end"===t?"start":"start"===t?"end":t}(o)),t.placement=n+(o?"-"+o:""),t.offsets.popper=nh({},t.offsets.popper,vh(t.instance.popper,t.offsets.reference,t.placement)),t=bh(t.instance.modifiers,t,"flip"))})),t},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,i=e.split("-")[0],n=t.offsets,r=n.popper,o=n.reference,s=-1!==["left","right"].indexOf(i),a=-1===["top","left"].indexOf(i);return r[s?"left":"top"]=o[i]-(a?r[s?"width":"height"]:0),t.placement=mh(e),t.offsets.popper=rh(r),t}},hide:{order:800,enabled:!0,fn:function(t){if(!Ph(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,i=gh(t.instance.modifiers,(function(t){return"preventOverflow"===t.name})).boundaries;if(e.bottom<i.top||e.left>i.right||e.top>i.bottom||e.right<i.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var i=e.x,n=e.y,r=t.offsets.popper,o=gh(t.instance.modifiers,(function(t){return"applyStyle"===t.name})).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s=void 0!==o?o:e.gpuAcceleration,a=Ud(t.instance.popper),l=oh(a),c={position:r.position},u=function(t,e){var i=t.offsets,n=i.popper,r=i.reference,o=Math.round,s=Math.floor,a=function(t){return t},l=o(r.width),c=o(n.width),u=-1!==["left","right"].indexOf(t.placement),d=-1!==t.placement.indexOf("-"),h=e?u||d||l%2==c%2?o:s:a,f=e?o:a;return{left:h(l%2==1&&c%2==1&&!d&&e?n.left-1:n.left),top:f(n.top),bottom:f(n.bottom),right:h(n.right)}}(t,window.devicePixelRatio<2||!Fh),d="bottom"===i?"top":"bottom",h="right"===n?"left":"right",f=wh("transform"),p=void 0,m=void 0;if(m="bottom"===d?"HTML"===a.nodeName?-a.clientHeight+u.bottom:-l.height+u.bottom:u.top,p="right"===h?"HTML"===a.nodeName?-a.clientWidth+u.right:-l.width+u.right:u.left,s&&f)c[f]="translate3d("+p+"px, "+m+"px, 0)",c[d]=0,c[h]=0,c.willChange="transform";else{var v="bottom"===d?-1:1,g="right"===h?-1:1;c[d]=m*v,c[h]=p*g,c.willChange=d+", "+h}var b={"x-placement":t.placement};return t.attributes=nh({},b,t.attributes),t.styles=nh({},c,t.styles),t.arrowStyles=nh({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,i;return _h(t.instance.popper,t.styles),e=t.instance.popper,i=t.attributes,Object.keys(i).forEach((function(t){!1!==i[t]?e.setAttribute(t,i[t]):e.removeAttribute(t)})),t.arrowElement&&Object.keys(t.arrowStyles).length&&_h(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,i,n,r){var o=fh(r,e,t,i.positionFixed),s=hh(i.placement,o,e,t,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return e.setAttribute("x-placement",s),_h(e,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}},Hh={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:Mh},zh=function(){function t(e,i){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};th(this,t),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=Rd(this.update.bind(this)),this.options=nh({},t.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=i&&i.jquery?i[0]:i,this.options.modifiers={},Object.keys(nh({},t.Defaults.modifiers,r.modifiers)).forEach((function(e){n.options.modifiers[e]=nh({},t.Defaults.modifiers[e]||{},r.modifiers?r.modifiers[e]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(t){return nh({name:t},n.options.modifiers[t])})).sort((function(t,e){return t.order-e.order})),this.modifiers.forEach((function(t){t.enabled&&Ad(t.onLoad)&&t.onLoad(n.reference,n.popper,n.options,t,n.state)})),this.update();var o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}return eh(t,[{key:"update",value:function(){return yh.call(this)}},{key:"destroy",value:function(){return Ch.call(this)}},{key:"enableEventListeners",value:function(){return $h.call(this)}},{key:"disableEventListeners",value:function(){return Bh.call(this)}}]),t}();zh.Utils=("undefined"!=typeof window?window:global).PopperUtils,zh.placements=Ih,zh.Defaults=Hh;var BvEvent=function(){function BvEvent(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s(this,BvEvent),!t)throw new TypeError("Failed to construct '".concat(this.constructor.name,"'. 1 argument required, ").concat(arguments.length," given."));ee(this,BvEvent.Defaults,this.constructor.Defaults,e,{type:t}),ne(this,{type:{enumerable:!0,configurable:!1,writable:!1},cancelable:{enumerable:!0,configurable:!1,writable:!1},nativeEvent:{enumerable:!0,configurable:!1,writable:!1},target:{enumerable:!0,configurable:!1,writable:!1},relatedTarget:{enumerable:!0,configurable:!1,writable:!1},vueTarget:{enumerable:!0,configurable:!1,writable:!1},componentId:{enumerable:!0,configurable:!1,writable:!1}});var i=!1;this.preventDefault=function(){this.cancelable&&(i=!0)},re(this,"defaultPrevented",{enumerable:!0,get:function(){return i}})}return l(BvEvent,null,[{key:"Defaults",get:function(){return{type:"",cancelable:!0,nativeEvent:null,target:null,relatedTarget:null,vueTarget:null,componentId:null}}}]),BvEvent}(),Nh=L({data:function(){return{listenForClickOut:!1}},watch:{listenForClickOut:function(t,e){t!==e&&(As(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,wr),t&&Rs(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,wr))}},beforeCreate:function(){this.clickOutElement=null,this.clickOutEventName=null},mounted:function(){this.clickOutElement||(this.clickOutElement=document),this.clickOutEventName||(this.clickOutEventName="click"),this.listenForClickOut&&Rs(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,wr)},beforeDestroy:function(){As(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,wr)},methods:{isClickOut:function(t){return!Jo(this.$el,t.target)},_clickOutHandler:function(t){this.clickOutHandler&&this.isClickOut(t)&&this.clickOutHandler(t)}}}),jh=L({data:function(){return{listenForFocusIn:!1}},watch:{listenForFocusIn:function(t,e){t!==e&&(As(this.focusInElement,"focusin",this._focusInHandler,wr),t&&Rs(this.focusInElement,"focusin",this._focusInHandler,wr))}},beforeCreate:function(){this.focusInElement=null},mounted:function(){this.focusInElement||(this.focusInElement=document),this.listenForFocusIn&&Rs(this.focusInElement,"focusin",this._focusInHandler,wr)},beforeDestroy:function(){As(this.focusInElement,"focusin",this._focusInHandler,wr)},methods:{_focusInHandler:function(t){this.focusInHandler&&this.focusInHandler(t)}}}),Gh=null;O&&(Gh=new WeakMap);var Wh,Uh,Yh,qh,Kh,Xh,Zh,Jh,Qh,tf,ef,nf,rf=Ns(si,pr),of=Ns(si,tr),sf=[".dropdown-item",".b-dropdown-form"].map((function(t){return"".concat(t,":not(.disabled):not([disabled])")})).join(", "),af=Is(he(r(r({},Vc),{},{boundary:Ds([HTMLElement,Fr],"scrollParent"),disabled:Ds(kr,!1),dropleft:Ds(kr,!1),dropright:Ds(kr,!1),dropup:Ds(kr,!1),noFlip:Ds(kr,!1),offset:Ds(Hr,0),popperOpts:Ds(Dr,{}),right:Ds(kr,!1)})),si),lf=L({mixins:[Lc,gl,Nh,jh],provide:function(){var t=this;return{getBvDropdown:function(){return t}}},inject:{getBvNavbar:{default:function(){return function(){return null}}}},props:af,data:function(){return{visible:!1,visibleChangePrevented:!1}},computed:{bvNavbar:function(){return this.getBvNavbar()},inNavbar:function(){return!Nt(this.bvNavbar)},toggler:function(){var t=this.$refs.toggle;return t?t.$el||t:null},directionClass:function(){return this.dropup?"dropup":this.dropright?"dropright":this.dropleft?"dropleft":""},boundaryClass:function(){return"scrollParent"===this.boundary||this.inNavbar?"":"position-static"},hideDelay:function(){return this.inNavbar?nt?300:50:0}},watch:{visible:function(t,e){if(this.visibleChangePrevented)this.visibleChangePrevented=!1;else if(t!==e){var i=new BvEvent(t?fr:er,{cancelable:!0,vueTarget:this,target:this.$refs.menu,relatedTarget:null,componentId:this.safeId?this.safeId():this.id||null});if(this.emitEvent(i),i.defaultPrevented)return this.visibleChangePrevented=!0,this.visible=e,void this.$off(tr,this.focusToggler);t?this.showMenu():this.hideMenu()}},disabled:function(t,e){t!==e&&t&&this.visible&&(this.visible=!1)}},created:function(){this.$_popper=null,this.$_hideTimeout=null},deactivated:function(){this.visible=!1,this.whileOpenListen(!1),this.destroyPopper()},mounted:function(){var t,e;t=this.$el,e=this,O&&Gh.set(t,e)},beforeDestroy:function(){var t;this.visible=!1,this.whileOpenListen(!1),this.destroyPopper(),this.clearHideTimeout(),t=this.$el,O&&Gh.delete(t)},methods:{emitEvent:function(t){var e=t.type;this.emitOnRoot(Ns(si,e),t),this.$emit(e,t)},showMenu:function(){var t=this;if(!this.disabled){if(!this.inNavbar)if("undefined"==typeof zh)ye("Popper.js not found. Falling back to CSS positioning",si);else{var e=this.dropup&&this.right||this.split?this.$el:this.$refs.toggle;e=e.$el||e,this.createPopper(e)}this.emitOnRoot(rf,this),this.whileOpenListen(!0),this.$nextTick((function(){t.focusMenu(),t.$emit(pr)}))}},hideMenu:function(){this.whileOpenListen(!1),this.emitOnRoot(of,this),this.$emit(tr),this.destroyPopper()},createPopper:function(t){this.destroyPopper(),this.$_popper=new zh(t,this.$refs.menu,this.getPopperConfig())},destroyPopper:function(){this.$_popper&&this.$_popper.destroy(),this.$_popper=null},updatePopper:function(){try{this.$_popper.scheduleUpdate()}catch(t){}},clearHideTimeout:function(){clearTimeout(this.$_hideTimeout),this.$_hideTimeout=null},getPopperConfig:function(){var t="bottom-start";this.dropup?t=this.right?"top-end":"top-start":this.dropright?t="right-start":this.dropleft?t="left-start":this.right&&(t="bottom-end");var e={placement:t,modifiers:{offset:{offset:this.offset||0},flip:{enabled:!this.noFlip}}},i=this.boundary;return i&&(e.modifiers.preventOverflow={boundariesElement:i}),de(e,this.popperOpts||{})},whileOpenListen:function(t){this.listenForClickOut=t,this.listenForFocusIn=t,this[t?"listenOnRoot":"listenOffRoot"](rf,this.rootCloseListener)},rootCloseListener:function(t){t!==this&&(this.visible=!1)},show:function(){var t=this;this.disabled||Mo((function(){t.visible=!0}))},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.disabled||(this.visible=!1,t&&this.$once(tr,this.focusToggler))},toggle:function(t){var e=t=t||{},i=e.type,n=e.keyCode;("click"===i||"keydown"===i&&-1!==[el,ol,Qa].indexOf(n))&&(this.disabled?this.visible=!1:(this.$emit(vr,t),Hs(t),this.visible?this.hide(!0):this.show()))},onMousedown:function(t){Hs(t,{propagation:!1})},onKeydown:function(t){var e=t.keyCode;27===e?this.onEsc(t):e===Qa?this.focusNext(t,!1):e===sl&&this.focusNext(t,!0)},onEsc:function(t){this.visible&&(this.visible=!1,Hs(t),this.$once(tr,this.focusToggler))},onSplitClick:function(t){this.disabled?this.visible=!1:this.$emit(Hn,t)},hideHandler:function(t){var e=this,i=t.target;!this.visible||Jo(this.$refs.menu,i)||Jo(this.toggler,i)||(this.clearHideTimeout(),this.$_hideTimeout=setTimeout((function(){return e.hide()}),this.hideDelay))},clickOutHandler:function(t){this.hideHandler(t)},focusInHandler:function(t){this.hideHandler(t)},focusNext:function(t,e){var i=this,n=t.target;!this.visible||t&&Zo(".dropdown form",n)||(Hs(t),this.$nextTick((function(){var t=i.getItems();if(!(t.length<1)){var r=t.indexOf(n);e&&r>0?r--:!e&&r<t.length-1&&r++,r<0&&(r=0),i.focusItem(r,t)}})))},focusItem:function(t,e){var i=e.find((function(e,i){return i===t}));vs(i)},getItems:function(){return(qo(sf,this.$refs.menu)||[]).filter(Wo)},focusMenu:function(){vs(this.$refs.menu)},focusToggler:function(){var t=this;this.$nextTick((function(){vs(t.toggler)}))}}}),cf=Is(he(r(r(r({},Vc),af),{},{block:Ds(kr,!1),html:Ds(Fr),lazy:Ds(kr,!1),menuClass:Ds(Or),noCaret:Ds(kr,!1),role:Ds(Fr,"menu"),size:Ds(Fr),split:Ds(kr,!1),splitButtonType:Ds(Fr,"button",(function(t){return vo(["button","submit","reset"],t)})),splitClass:Ds(Or),splitHref:Ds(Fr),splitTo:Ds(jr),splitVariant:Ds(Fr),text:Ds(Fr),toggleAttrs:Ds(Dr,{}),toggleClass:Ds(Or),toggleTag:Ds(Fr,"button"),toggleText:Ds(Fr,"Toggle dropdown"),variant:Ds(Fr,"secondary")})),si),uf=L({name:si,mixins:[Lc,lf,So],props:cf,computed:{dropdownClasses:function(){var t=this.block,e=this.split;return[this.directionClass,this.boundaryClass,{show:this.visible,"btn-group":e||!t,"d-flex":t&&e}]},menuClasses:function(){return[this.menuClass,{"dropdown-menu-right":this.right,show:this.visible}]},toggleClasses:function(){var t=this.split;return[this.toggleClass,{"dropdown-toggle-split":t,"dropdown-toggle-no-caret":this.noCaret&&!t}]}},render:function(t){var e=this.visible,i=this.variant,n=this.size,o=this.block,s=this.disabled,a=this.split,l=this.role,c=this.hide,u=this.toggle,d={variant:i,size:n,block:o,disabled:s},h=this.normalizeSlot(Yr),f=this.hasNormalizedSlot(Yr)?{}:Zl(this.html,this.text),p=t();if(a){var m=this.splitTo,v=this.splitHref,g=this.splitButtonType,b=r(r({},d),{},{variant:this.splitVariant||i});m?b.to=m:v?b.href=v:g&&(b.type=g),p=t(Ll,{class:this.splitClass,attrs:{id:this.safeId("_BV_button_")},props:b,domProps:f,on:{click:this.onSplitClick},ref:"button"},h),h=[t("span",{class:["sr-only"]},[this.toggleText])],f={}}var y=t(Ll,{staticClass:"dropdown-toggle",class:this.toggleClasses,attrs:r(r({},this.toggleAttrs),{},{id:this.safeId("_BV_toggle_"),"aria-haspopup":["menu","listbox","tree","grid","dialog"].includes(l)?l:"false","aria-expanded":Io(e)}),props:r(r({},d),{},{tag:this.toggleTag,block:o&&!a}),domProps:f,on:{mousedown:this.onMousedown,click:u,keydown:u},ref:"toggle"},h),T=t("ul",{staticClass:"dropdown-menu",class:this.menuClasses,attrs:{role:l,tabindex:"-1","aria-labelledby":this.safeId(a?"_BV_button_":"_BV_toggle_")},on:{keydown:this.onKeydown},ref:"menu"},[!this.lazy||e?this.normalizeSlot(Kr,{hide:c}):t()]);return t("div",{staticClass:"dropdown b-dropdown",class:this.dropdownClasses,attrs:{id:this.safeId()}},[p,y,T])}}),df=ue(kl,["event","routerTag"]),hf=Is(he(r(r({},df),{},{linkClass:Ds(Or),variant:Ds(Fr)})),di),ff=L({name:di,mixins:[pl,So],inject:{getBvDropdown:{default:function(){return function(){return null}}}},inheritAttrs:!1,props:hf,computed:{bvDropdown:function(){return this.getBvDropdown()},computedAttrs:function(){return r(r({},this.bvAttrs),{},{role:"menuitem"})}},methods:{closeDropdown:function(){var t=this;Mo((function(){t.bvDropdown&&t.bvDropdown.hide(!0)}))},onClick:function(t){this.$emit(Hn,t),this.closeDropdown()}},render:function(t){var e=this.linkClass,i=this.variant,n=this.active,r=this.disabled,o=this.onClick,s=this.bvAttrs;return t("li",{class:s.class,style:s.style,attrs:{role:"presentation"}},[t(xl,{staticClass:"dropdown-item",class:[e,c({},"text-".concat(i),i&&!(n||r))],props:Fs(df,this.$props),attrs:this.computedAttrs,on:{click:o},ref:"item"},this.normalizeSlot())])}}),pf=Is({active:Ds(kr,!1),activeClass:Ds(Fr,"active"),buttonClass:Ds(Or),disabled:Ds(kr,!1),variant:Ds(Fr)},hi),mf=L({name:hi,mixins:[pl,So],inject:{getBvDropdown:{default:function(){return function(){return null}}}},inheritAttrs:!1,props:pf,computed:{bvDropdown:function(){return this.getBvDropdown()},computedAttrs:function(){return r(r({},this.bvAttrs),{},{role:"menuitem",type:"button",disabled:this.disabled})}},methods:{closeDropdown:function(){this.bvDropdown&&this.bvDropdown.hide(!0)},onClick:function(t){this.$emit(Hn,t),this.closeDropdown()}},render:function(t){var e,i=this.active,n=this.variant,r=this.bvAttrs;return t("li",{class:r.class,style:r.style,attrs:{role:"presentation"}},[t("button",{staticClass:"dropdown-item",class:[this.buttonClass,(e={},c(e,this.activeClass,i),c(e,"text-".concat(n),n&&!(i||this.disabled)),e)],attrs:this.computedAttrs,on:{click:this.onClick},ref:"button"},this.normalizeSlot())])}}),vf=Is({id:Ds(Fr),tag:Ds(Fr,"header"),variant:Ds(Fr)},ui),gf=L({name:ui,functional:!0,props:vf,render:function(t,e){var i=e.props,n=e.data,o=e.children,s=i.tag,a=i.variant;return t("li",I(ue(n,["attrs"]),{attrs:{role:"presentation"}}),[t(s,{staticClass:"dropdown-header",class:c({},"text-".concat(a),a),attrs:r(r({},n.attrs||{}),{},{id:i.id||null,role:jo(s,"header")?null:"heading"}),ref:"header"},o)])}}),bf=Is({tag:Ds(Fr,"hr")},ai),yf=L({name:ai,functional:!0,props:bf,render:function(t,e){var i=e.props,n=e.data;return t("li",I(ue(n,["attrs"]),{attrs:{role:"presentation"}}),[t(i.tag,{staticClass:"dropdown-divider",attrs:r(r({},n.attrs||{}),{},{role:"separator","aria-orientation":"horizontal"}),ref:"divider"})])}}),Tf=Is({id:Ds(Fr),inline:Ds(kr,!1),novalidate:Ds(kr,!1),validated:Ds(kr,!1)},mi),wf=L({name:mi,functional:!0,props:Tf,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t("form",I(n,{class:{"form-inline":i.inline,"was-validated":i.validated},attrs:{id:i.id,novalidate:i.novalidate}}),r)}}),Cf=Is(he(r(r({},Tf),{},{disabled:Ds(kr,!1),formClass:Ds(Or)})),li),Sf=L({name:li,functional:!0,props:Cf,render:function(t,e){var i=e.props,n=e.data,o=e.listeners,s=e.children;return t("li",I(ue(n,["attrs","on"]),{attrs:{role:"presentation"}}),[t(wf,{staticClass:"b-dropdown-form",class:[i.formClass,{disabled:i.disabled}],props:i,attrs:r(r({},n.attrs||{}),{},{disabled:i.disabled,tabindex:i.disabled?null:"-1"}),on:o,ref:"form"},s)])}}),kf=Is({tag:Ds(Fr,"p"),textClass:Ds(Or),variant:Ds(Fr)},fi),xf=L({name:fi,functional:!0,props:kf,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.tag,s=i.textClass,a=i.variant;return t("li",I(ue(n,["attrs"]),{attrs:{role:"presentation"}}),[t(o,{staticClass:"b-dropdown-text",class:[s,c({},"text-".concat(a),a)],props:i,attrs:n.attrs||{},ref:"text"},r)])}}),$f=Is({ariaDescribedby:Ds(Fr),header:Ds(Fr),headerClasses:Ds(Or),headerTag:Ds(Fr,"header"),headerVariant:Ds(Fr),id:Ds(Fr)},ci),Bf=L({name:ci,functional:!0,props:$f,render:function(t,e){var i=e.props,n=e.data,o=e.slots,s=e.scopedSlots,a=i.id,l=i.variant,u=i.header,d=i.headerTag,h=o(),f=s||{},p={},m=a?"_bv_".concat(a,"_group_dd_header"):null,v=t();return(wo(to,f,h)||u)&&(v=t(d,{staticClass:"dropdown-header",class:[i.headerClasses,c({},"text-".concat(l),l)],attrs:{id:m,role:jo(d,"header")?null:"heading"}},Co(to,p,f,h)||u)),t("li",I(ue(n,["attrs"]),{attrs:{role:"presentation"}}),[v,t("ul",{staticClass:"list-unstyled",attrs:r(r({},n.attrs||{}),{},{id:a,role:"group","aria-describedby":[m,i.ariaDescribedBy].filter(pe).join(" ").trim()||null})},Co(Kr,p,f,h))])}}),Df=$e({components:{BDropdown:uf,BDd:uf,BDropdownItem:ff,BDdItem:ff,BDropdownItemButton:mf,BDropdownItemBtn:mf,BDdItemButton:mf,BDdItemBtn:mf,BDropdownHeader:gf,BDdHeader:gf,BDropdownDivider:yf,BDdDivider:yf,BDropdownForm:Sf,BDdForm:Sf,BDropdownText:xf,BDdText:xf,BDropdownGroup:Bf,BDdGroup:Bf}}),_f=["iframe","embed","video","object","img","b-img","b-img-lazy"],Ff=Is({aspect:Ds(Fr,"16by9"),tag:Ds(Fr,"div"),type:Ds(Fr,"iframe",(function(t){return vo(_f,t)}))},pi),Pf=L({name:pi,functional:!0,props:Ff,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.aspect;return t(i.tag,{staticClass:"embed-responsive",class:c({},"embed-responsive-".concat(o),o),ref:n.ref},[t(i.type,I(ue(n,["ref"]),{staticClass:"embed-responsive-item"}),r)])}}),If=$e({components:{BEmbed:Pf}}),Of=Is({disabledField:Ds(Fr,"disabled"),htmlField:Ds(Fr,"html"),options:Ds(Ir,[]),textField:Ds(Fr,"text"),valueField:Ds(Fr,"value")},"formOptionControls"),Ef=L({props:Of,computed:{formOptions:function(){return this.normalizeOptions(this.options)}},methods:{normalizeOption:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(Zt(t)){var i=ve(t,this.valueField),n=ve(t,this.textField);return{value:zt(i)?e||n:i,text:Xl(String(zt(n)?e:n)),html:ve(t,this.htmlField),disabled:Boolean(ve(t,this.disabledField))}}return{value:e||t,text:Xl(String(t)),disabled:!1}},normalizeOptions:function(t){var e=this;return Kt(t)?t.map((function(t){return e.normalizeOption(t)})):Zt(t)?(ye('Setting prop "options" to an object is deprecated. Use the array format instead.',this.$options.name),se(t).map((function(i){return e.normalizeOption(t[i]||{},i)}))):[]}}}),Vf=Is(he(r(r({},Of),{},{id:Ds(Fr,void 0,!0)})),bi),Lf=L({name:bi,mixins:[Ef,So],props:Vf,render:function(t){var e=this.id,i=this.formOptions.map((function(e,i){var n=e.value,r=e.text,o=e.html,s=e.disabled;return t("option",{attrs:{value:n,disabled:s},domProps:Zl(o,r),key:"option_".concat(i)})}));return t("datalist",{attrs:{id:e}},[i,this.normalizeSlot()])}}),Rf=Is({id:Ds(Fr),inline:Ds(kr,!1),tag:Ds(Fr,"small"),textVariant:Ds(Fr,"muted")},Ei),Af=L({name:Ei,functional:!0,props:Rf,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{class:c({"form-text":!i.inline},"text-".concat(i.textVariant),i.textVariant),attrs:{id:i.id}}),r)}}),Mf=Is({ariaLive:Ds(Fr),forceShow:Ds(kr,!1),id:Ds(Fr),role:Ds(Fr),state:Ds(kr,null),tag:Ds(Fr,"div"),tooltip:Ds(kr,!1)},Si),Hf=L({name:Si,functional:!0,props:Mf,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.tooltip,s=i.ariaLive,a=!0===i.forceShow||!1===i.state;return t(i.tag,I(n,{class:{"d-block":a,"invalid-feedback":!o,"invalid-tooltip":o},attrs:{id:i.id||null,role:i.role||null,"aria-live":s||null,"aria-atomic":s?"true":null}}),r)}}),zf=Is({ariaLive:Ds(Fr),forceShow:Ds(kr,!1),id:Ds(Fr),role:Ds(Fr),state:Ds(kr,null),tag:Ds(Fr,"div"),tooltip:Ds(kr,!1)},Ri),Nf=L({name:Ri,functional:!0,props:zf,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.tooltip,s=i.ariaLive,a=!0===i.forceShow||!0===i.state;return t(i.tag,I(n,{class:{"d-block":a,"valid-feedback":!o,"valid-tooltip":o},attrs:{id:i.id||null,role:i.role||null,"aria-live":s||null,"aria-atomic":s?"true":null}}),r)}}),jf=Is({tag:Ds(Fr,"div")},Bi),Gf=L({name:Bi,functional:!0,props:jf,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{staticClass:"form-row"}),r)}}),Wf=$e({components:{BForm:wf,BFormDatalist:Lf,BDatalist:Lf,BFormText:Af,BFormInvalidFeedback:Hf,BFormFeedback:Hf,BFormValidFeedback:Nf,BFormRow:Gf}}),Uf=function(t,e){for(var i=0;i<t.length;i++)if(ll(t[i],e))return i;return-1},Yf="input, textarea, select",qf=Is({autofocus:Ds(kr,!1),disabled:Ds(kr,!1),form:Ds(Fr),id:Ds(Fr),name:Ds(Fr),required:Ds(kr,!1)},"formControls"),Kf=L({props:qf,mounted:function(){this.handleAutofocus()},activated:function(){this.handleAutofocus()},methods:{handleAutofocus:function(){var t=this;this.$nextTick((function(){Mo((function(){var e=t.$el;t.autofocus&&Wo(e)&&(Xo(e,Yf)||(e=Ko(Yf,e)),vs(e))}))}))}}}),Xf=Is({plain:Ds(kr,!1)},"formControls"),Zf=L({props:Xf,computed:{custom:function(){return!this.plain}}}),Jf=Is({size:Ds(Fr)},"formControls"),Qf=L({props:Jf,computed:{sizeFormClass:function(){return[this.size?"form-control-".concat(this.size):null]}}}),tp=Is({state:Ds(kr,null)},"formState"),ep=L({props:tp,computed:{computedState:function(){return Wt(this.state)?this.state:null},stateClass:function(){var t=this.computedState;return!0===t?"is-valid":!1===t?"is-invalid":null},computedAriaInvalid:function(){var t=va(this).ariaInvalid;return!0===t||"true"===t||""===t||!1===this.computedState?"true":t}}}),ip=Vs("checked",{defaultValue:null}),np=ip.mixin,rp=ip.props,op=ip.prop,sp=ip.event,ap=Is(he(r(r(r(r(r(r(r({},Vc),rp),qf),Jf),tp),Xf),{},{ariaLabel:Ds(Fr),ariaLabelledby:Ds(Fr),button:Ds(kr,!1),buttonVariant:Ds(Fr),inline:Ds(kr,!1),value:Ds(Cr)})),"formRadioCheckControls"),lp=L({mixins:[pl,Lc,np,So,Kf,Qf,ep,Zf],inheritAttrs:!1,props:ap,data:function(){return{localChecked:this.isGroup?this.bvGroup[op]:this[op],hasFocus:!1}},computed:{computedLocalChecked:{get:function(){return this.isGroup?this.bvGroup.localChecked:this.localChecked},set:function(t){this.isGroup?this.bvGroup.localChecked=t:this.localChecked=t}},isChecked:function(){return ll(this.value,this.computedLocalChecked)},isRadio:function(){return!0},isGroup:function(){return!!this.bvGroup},isBtnMode:function(){return this.isGroup?this.bvGroup.buttons:this.button},isPlain:function(){return!this.isBtnMode&&(this.isGroup?this.bvGroup.plain:this.plain)},isCustom:function(){return!this.isBtnMode&&!this.isPlain},isSwitch:function(){return!(this.isBtnMode||this.isRadio||this.isPlain)&&(this.isGroup?this.bvGroup.switches:this.switch)},isInline:function(){return this.isGroup?this.bvGroup.inline:this.inline},isDisabled:function(){return this.isGroup&&this.bvGroup.disabled||this.disabled},isRequired:function(){return this.computedName&&(this.isGroup?this.bvGroup.required:this.required)},computedName:function(){return(this.isGroup?this.bvGroup.groupName:this.name)||null},computedForm:function(){return(this.isGroup?this.bvGroup.form:this.form)||null},computedSize:function(){return(this.isGroup?this.bvGroup.size:this.size)||""},computedState:function(){return this.isGroup?this.bvGroup.computedState:Wt(this.state)?this.state:null},computedButtonVariant:function(){var t=this.buttonVariant;return t||(this.isGroup&&this.bvGroup.buttonVariant?this.bvGroup.buttonVariant:"secondary")},buttonClasses:function(){var t,e=this.computedSize;return["btn","btn-".concat(this.computedButtonVariant),(t={},c(t,"btn-".concat(e),e),c(t,"disabled",this.isDisabled),c(t,"active",this.isChecked),c(t,"focus",this.hasFocus),t)]},computedAttrs:function(){var t=this.isDisabled,e=this.isRequired;return r(r({},this.bvAttrs),{},{id:this.safeId(),type:this.isRadio?"radio":"checkbox",name:this.computedName,form:this.computedForm,disabled:t,required:e,"aria-required":e||null,"aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||null})}},watch:(Wh={},c(Wh,op,(function(){this["".concat(op,"Watcher")].apply(this,arguments)})),c(Wh,"computedLocalChecked",(function(){this.computedLocalCheckedWatcher.apply(this,arguments)})),Wh),methods:(Uh={},c(Uh,"".concat(op,"Watcher"),(function(t){ll(t,this.computedLocalChecked)||(this.computedLocalChecked=t)})),c(Uh,"computedLocalCheckedWatcher",(function(t,e){ll(t,e)||this.$emit(sp,t)})),c(Uh,"handleChange",(function(t){var e=this,i=t.target.checked,n=this.value,r=i?n:null;this.computedLocalChecked=n,this.$nextTick((function(){e.$emit(Mn,r),e.isGroup&&e.bvGroup.$emit(Mn,r)}))})),c(Uh,"handleFocus",(function(t){t.target&&("focus"===t.type?this.hasFocus=!0:"blur"===t.type&&(this.hasFocus=!1))})),c(Uh,"focus",(function(){this.isDisabled||vs(this.$refs.input)})),c(Uh,"blur",(function(){this.isDisabled||gs(this.$refs.input)})),Uh),render:function(t){var e=this.isRadio,i=this.isBtnMode,n=this.isPlain,o=this.isCustom,s=this.isInline,a=this.isSwitch,l=this.computedSize,u=this.bvAttrs,d=this.normalizeSlot(),h=t("input",{class:[{"form-check-input":n,"custom-control-input":o,"position-static":n&&!d},i?"":this.stateClass],directives:[{name:"model",value:this.computedLocalChecked}],attrs:this.computedAttrs,domProps:{value:this.value,checked:this.isChecked},on:r({change:this.handleChange},i?{focus:this.handleFocus,blur:this.handleFocus}:{}),key:"input",ref:"input"});if(i){var f=t("label",{class:this.buttonClasses},[h,d]);return this.isGroup||(f=t("div",{class:["btn-group-toggle","d-inline-block"]},[f])),f}var p=t();return n&&!d||(p=t("label",{class:{"form-check-label":n,"custom-control-label":o},attrs:{for:this.safeId()}},d)),t("div",{class:[c({"form-check":n,"form-check-inline":n&&s,"custom-control":o,"custom-control-inline":o&&s,"custom-checkbox":o&&!e&&!a,"custom-switch":a,"custom-radio":o&&e},"b-custom-control-".concat(l),l&&!i),u.class],style:u.style},[h,p])}}),cp="indeterminate",up="update:indeterminate",dp=Is(he(r(r({},ap),{},(c(Yh={},cp,Ds(kr,!1)),c(Yh,"switch",Ds(kr,!1)),c(Yh,"uncheckedValue",Ds(Cr,!1)),c(Yh,"value",Ds(Cr,!0)),Yh))),vi),hp=L({name:vi,mixins:[lp],inject:{getBvGroup:{from:"getBvCheckGroup",default:function(){return function(){return null}}}},props:dp,computed:{bvGroup:function(){return this.getBvGroup()},isChecked:function(){var t=this.value,e=this.computedLocalChecked;return Kt(e)?Uf(e,t)>-1:ll(e,t)},isRadio:function(){return!1}},watch:c({},cp,(function(t,e){ll(t,e)||this.setIndeterminate(t)})),mounted:function(){this.setIndeterminate(this.indeterminate)},methods:{computedLocalCheckedWatcher:function(t,e){if(!ll(t,e)){this.$emit(sp,t);var i=this.$refs.input;i&&this.$emit(up,i.indeterminate)}},handleChange:function(t){var e=this,i=t.target,n=i.checked,r=i.indeterminate,o=this.value,s=this.uncheckedValue,a=this.computedLocalChecked;if(Kt(a)){var l=Uf(a,o);n&&l<0?a=a.concat(o):!n&&l>-1&&(a=a.slice(0,l).concat(a.slice(l+1)))}else a=n?o:s;this.computedLocalChecked=a,this.$nextTick((function(){e.$emit(Mn,a),e.isGroup&&e.bvGroup.$emit(Mn,a),e.$emit(up,r)}))},setIndeterminate:function(t){Kt(this.computedLocalChecked)&&(t=!1);var e=this.$refs.input;e&&(e.indeterminate=t,this.$emit(up,t))}}}),fp=Is(ap,ki),pp=L({name:ki,mixins:[lp],inject:{getBvGroup:{from:"getBvRadioGroup",default:function(){return function(){return null}}}},props:fp,computed:{bvGroup:function(){return this.getBvGroup()}}}),mp=["aria-describedby","aria-labelledby"],vp=Vs("checked"),gp=vp.mixin,bp=vp.props,yp=vp.prop,Tp=vp.event,wp=Is(he(r(r(r(r(r(r(r(r({},Vc),bp),qf),Of),Jf),tp),Xf),{},{ariaInvalid:Ds(Rr,!1),buttonVariant:Ds(Fr),buttons:Ds(kr,!1),stacked:Ds(kr,!1),validated:Ds(kr,!1)})),"formRadioCheckGroups"),Cp=L({mixins:[Lc,gp,So,Kf,Ef,Qf,ep,Zf],inheritAttrs:!1,props:wp,data:function(){return{localChecked:this[yp]}},computed:{inline:function(){return!this.stacked},groupName:function(){return this.name||this.safeId()},groupClasses:function(){var t=this.inline,e=this.size,i={"was-validated":this.validated};return this.buttons&&(i=[i,"btn-group-toggle",c({"btn-group":t,"btn-group-vertical":!t},"btn-group-".concat(e),e)]),i}},watch:(qh={},c(qh,yp,(function(t){ll(t,this.localChecked)||(this.localChecked=t)})),c(qh,"localChecked",(function(t,e){ll(t,e)||this.$emit(Tp,t)})),qh),render:function(t){var e=this,i=this.isRadioGroup,n=ce(this.$attrs,mp),o=i?pp:hp,s=this.formOptions.map((function(i,r){var s="BV_option_".concat(r);return t(o,{props:{disabled:i.disabled||!1,id:e.safeId(s),value:i.value},attrs:n,key:s},[t("span",{domProps:Zl(i.html,i.text)})])}));return t("div",{class:[this.groupClasses,"bv-no-focus-ring"],attrs:r(r({},ue(this.$attrs,mp)),{},{"aria-invalid":this.computedAriaInvalid,"aria-required":this.required?"true":null,id:this.safeId(),role:i?"radiogroup":"group",tabindex:"-1"})},[this.normalizeSlot(Jr),s,this.normalizeSlot()])}}),Sp=Is(he(r(r({},wp),{},(c(Kh={},yp,Ds(Sr,[])),c(Kh,"switches",Ds(kr,!1)),Kh))),gi),kp=L({name:gi,mixins:[Cp],provide:function(){var t=this;return{getBvCheckGroup:function(){return t}}},props:Sp,computed:{isRadioGroup:function(){return!1}}}),xp=$e({components:{BFormCheckbox:hp,BCheckbox:hp,BCheck:hp,BFormCheckboxGroup:kp,BCheckboxGroup:kp,BCheckGroup:kp}}),$p="__BV_hover_handler__",Bp="mouseenter",Dp=function(t,e,i){Ms(t,e,Bp,i,wr),Ms(t,e,"mouseleave",i,wr)},_p=function(t,e){var i=e.value,n=void 0===i?null:i;if(Y){var r=t[$p],o=Gt(r),s=!(o&&r.fn===n);o&&s&&(Dp(!1,t,r),delete t[$p]),Gt(n)&&s&&(t[$p]=function(t){var e=function(e){t(e.type===Bp,e)};return e.fn=t,e}(n),Dp(!0,t,t[$p]))}},Fp={bind:_p,componentUpdated:_p,unbind:function(t){_p(t,{value:null})}},Pp=he(r(r(r(r(r(r({},Vc),Jf),tp),ue(af,["disabled"])),ue(qf,["autofocus"])),{},{buttonOnly:Ds(kr,!1),buttonVariant:Ds(Fr,"secondary"),formattedValue:Ds(Fr),labelSelected:Ds(Fr),lang:Ds(Fr),menuClass:Ds(Or),placeholder:Ds(Fr),readonly:Ds(kr,!1),rtl:Ds(kr,null),value:Ds(Fr,"")})),Ip=L({name:"BVFormBtnLabelControl",directives:{"b-hover":Fp},mixins:[Lc,Qf,ep,lf,So],props:Pp,data:function(){return{isHovered:!1,hasFocus:!1}},computed:{idButton:function(){return this.safeId()},idLabel:function(){return this.safeId("_value_")},idMenu:function(){return this.safeId("_dialog_")},idWrapper:function(){return this.safeId("_outer_")},computedDir:function(){return!0===this.rtl?"rtl":!1===this.rtl?"ltr":null}},methods:{focus:function(){this.disabled||vs(this.$refs.toggle)},blur:function(){this.disabled||gs(this.$refs.toggle)},setFocus:function(t){this.hasFocus="focus"===t.type},handleHover:function(t){this.isHovered=t}},render:function(t){var e,i=this.idButton,n=this.idLabel,r=this.idMenu,o=this.idWrapper,s=this.disabled,a=this.readonly,l=this.required,u=this.name,d=this.state,h=this.visible,f=this.size,p=this.isHovered,m=this.hasFocus,v=this.labelSelected,g=this.buttonVariant,b=this.buttonOnly,y=Io(this.value)||"",T=!1===d||l&&!y,w={isHovered:p,hasFocus:m,state:d,opened:h},C=t("button",{staticClass:"btn",class:(e={},c(e,"btn-".concat(g),b),c(e,"btn-".concat(f),f),c(e,"h-auto",!b),c(e,"dropdown-toggle",b),c(e,"dropdown-toggle-no-caret",b),e),attrs:{id:i,type:"button",disabled:s,"aria-haspopup":"dialog","aria-expanded":h?"true":"false","aria-invalid":T?"true":null,"aria-required":l?"true":null},directives:[{name:"b-hover",value:this.handleHover}],on:{mousedown:this.onMousedown,click:this.toggle,keydown:this.toggle,"!focus":this.setFocus,"!blur":this.setFocus},ref:"toggle"},[this.hasNormalizedSlot(Yr)?this.normalizeSlot(Yr,w):t(La,{props:{scale:1.25}})]),S=t();u&&!s&&(S=t("input",{attrs:{type:"hidden",name:u||null,form:this.form||null,value:y}}));var k=t("div",{staticClass:"dropdown-menu",class:[this.menuClass,{show:h,"dropdown-menu-right":this.right}],attrs:{id:r,role:"dialog",tabindex:"-1","aria-modal":"false","aria-labelledby":n},on:{keydown:this.onKeydown},ref:"menu"},[this.normalizeSlot(Kr,{opened:h})]),x=t("label",{class:b?"sr-only":["form-control",{"text-muted":!y},this.stateClass,this.sizeFormClass],attrs:{id:n,for:i,"aria-invalid":T?"true":null,"aria-required":l?"true":null},directives:[{name:"b-hover",value:this.handleHover}],on:{"!click":function(t){Hs(t,{preventDefault:!1})}}},[y?this.formattedValue||y:this.placeholder||"",y&&v?t("bdi",{staticClass:"sr-only"},v):""]);return t("div",{staticClass:"b-form-btn-label-control dropdown",class:[this.directionClass,this.boundaryClass,[{"btn-group":b,"form-control":!b,focus:m&&!b,show:h,"is-valid":!0===d,"is-invalid":!1===d},b?null:this.sizeFormClass]],attrs:{id:o,role:b?null:"group",lang:this.lang||null,dir:this.computedDir,"aria-disabled":s,"aria-readonly":a&&!s,"aria-labelledby":n,"aria-invalid":!1===d||l&&!y?"true":null,"aria-required":l?"true":null}},[C,S,k,x])}}),Op=Vs("value",{type:Ar}),Ep=Op.mixin,Vp=Op.props,Lp=Op.prop,Rp=Op.event,Ap=ue(Nc,["block","hidden","id","noKeyNav","roleDescription","value","width"]),Mp=ue(Pp,["formattedValue","id","lang","rtl","value"]),Hp=Is(he(r(r(r(r(r({},Vc),Vp),Ap),Mp),{},{calendarWidth:Ds(Fr,"270px"),closeButton:Ds(kr,!1),closeButtonVariant:Ds(Fr,"outline-secondary"),dark:Ds(kr,!1),labelCloseButton:Ds(Fr,"Close"),labelResetButton:Ds(Fr,"Reset"),labelTodayButton:Ds(Fr,"Select today"),noCloseOnSelect:Ds(kr,!1),resetButton:Ds(kr,!1),resetButtonVariant:Ds(Fr,"outline-danger"),resetValue:Ds(Ar),todayButton:Ds(kr,!1),todayButtonVariant:Ds(Fr,"outline-primary")})),yi),zp=L({name:yi,mixins:[Lc,Ep],props:Hp,data:function(){return{localYMD:Tc(this[Lp])||"",isVisible:!1,localLocale:null,isRTL:!1,formattedValue:"",activeYMD:""}},computed:{calendarYM:function(){return this.activeYMD.slice(0,-3)},computedLang:function(){return(this.localLocale||"").replace(/-u-.*$/i,"")||null},computedResetValue:function(){return Tc(Ic(this.resetValue))||""}},watch:(Xh={},c(Xh,Lp,(function(t){this.localYMD=Tc(t)||""})),c(Xh,"localYMD",(function(t){this.isVisible&&this.$emit(Rp,this.valueAsDate?yc(t)||null:t||"")})),c(Xh,"calendarYM",(function(t,e){if(t!==e&&e)try{this.$refs.control.updatePopper()}catch(t){}})),Xh),methods:{focus:function(){this.disabled||vs(this.$refs.control)},blur:function(){this.disabled||gs(this.$refs.control)},setAndClose:function(t){var e=this;this.localYMD=t,this.noCloseOnSelect||this.$nextTick((function(){e.$refs.control.hide(!0)}))},onSelected:function(t){var e=this;this.$nextTick((function(){e.setAndClose(t)}))},onInput:function(t){this.localYMD!==t&&(this.localYMD=t)},onContext:function(t){var e=t.activeYMD,i=t.isRTL,n=t.locale,r=t.selectedYMD,o=t.selectedFormatted;this.isRTL=i,this.localLocale=n,this.formattedValue=o,this.localYMD=r,this.activeYMD=e,this.$emit(Nn,t)},onTodayButton:function(){this.setAndClose(Tc(Ic(bc(),this.min,this.max)))},onResetButton:function(){this.setAndClose(this.computedResetValue)},onCloseButton:function(){this.$refs.control.hide(!0)},onShow:function(){this.isVisible=!0},onShown:function(){var t=this;this.$nextTick((function(){vs(t.$refs.calendar),t.$emit(pr)}))},onHidden:function(){this.isVisible=!1,this.$emit(tr)},defaultButtonFn:function(t){var e=t.isHovered,i=t.hasFocus;return this.$createElement(e||i?Oa:Ia,{attrs:{"aria-hidden":"true"}})}},render:function(t){var e=this.localYMD,i=this.disabled,n=this.readonly,o=this.dark,s=this.$props,a=this.$scopedSlots,l=jt(this.placeholder)?this.labelNoDateSelected:this.placeholder,u=[];if(this.todayButton){var d=this.labelTodayButton;u.push(t(Ll,{props:{disabled:i||n,size:"sm",variant:this.todayButtonVariant},attrs:{"aria-label":d||null},on:{click:this.onTodayButton}},d))}if(this.resetButton){var h=this.labelResetButton;u.push(t(Ll,{props:{disabled:i||n,size:"sm",variant:this.resetButtonVariant},attrs:{"aria-label":h||null},on:{click:this.onResetButton}},h))}if(this.closeButton){var f=this.labelCloseButton;u.push(t(Ll,{props:{disabled:i,size:"sm",variant:this.closeButtonVariant},attrs:{"aria-label":f||null},on:{click:this.onCloseButton}},f))}u.length>0&&(u=[t("div",{staticClass:"b-form-date-controls d-flex flex-wrap",class:{"justify-content-between":u.length>1,"justify-content-end":u.length<2}},u)]);var p=t(jc,{staticClass:"b-form-date-calendar w-100",props:r(r({},Fs(Ap,s)),{},{hidden:!this.isVisible,value:e,valueAsDate:!1,width:this.calendarWidth}),on:{selected:this.onSelected,input:this.onInput,context:this.onContext},scopedSlots:ce(a,["nav-prev-decade","nav-prev-year","nav-prev-month","nav-this-month","nav-next-month","nav-next-year","nav-next-decade"]),key:"calendar",ref:"calendar"},u);return t(Ip,{staticClass:"b-form-datepicker",props:r(r({},Fs(Mp,s)),{},{formattedValue:e?this.formattedValue:"",id:this.safeId(),lang:this.computedLang,menuClass:[{"bg-dark":o,"text-light":o},this.menuClass],placeholder:l,rtl:this.isRTL,value:e}),on:{show:this.onShow,shown:this.onShown,hidden:this.onHidden},scopedSlots:c({},Yr,a["button-content"]||this.defaultButtonFn),ref:"control"},[p])}}),Np=$e({components:{BFormDatepicker:zp,BDatepicker:zp}}),jp=Vs("value",{type:[Sr,Mt],defaultValue:null,validator:function(t){return""===t?(ye(qp,Ti),!0):jt(t)||Kp(t)}}),Gp=jp.mixin,Wp=jp.props,Up=jp.prop,Yp=jp.event,qp='Setting "value"/"v-model" to an empty string for reset is deprecated. Set to "null" instead.',Kp=function t(e){return function(t){return t instanceof Mt}(e)||Kt(e)&&e.every((function(e){return t(e)}))},Xp=function(t){return Gt(t.getAsEntry)?t.getAsEntry():Gt(t.webkitGetAsEntry)?t.webkitGetAsEntry():null},Zp=function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return new Promise((function(n){var r=[];!function o(){e.readEntries((function(e){0===e.length?n(Promise.all(r).then((function(t){return yo(t)}))):(r.push(Promise.all(e.map((function(e){if(e){if(e.isDirectory)return t(e.createReader(),"".concat(i).concat(e.name,"/"));if(e.isFile)return new Promise((function(t){e.file((function(e){e.$path="".concat(i).concat(e.name),t(e)}))}))}return null})).filter(pe))),o())}))}()}))},Jp=Is(he(r(r(r(r(r(r(r({},Vc),Wp),qf),Xf),tp),Jf),{},{accept:Ds(Fr,""),browseText:Ds(Fr,"Browse"),capture:Ds(kr,!1),directory:Ds(kr,!1),dropPlaceholder:Ds(Fr,"Drop files here"),fileNameFormatter:Ds($r),multiple:Ds(kr,!1),noDrop:Ds(kr,!1),noDropPlaceholder:Ds(Fr,"Not allowed"),noTraverse:Ds(kr,!1),placeholder:Ds(Fr,"No file chosen")})),Ti),Qp=L({name:Ti,mixins:[pl,Lc,Gp,So,Kf,ep,Zf,So],inheritAttrs:!1,props:Jp,data:function(){return{files:[],dragging:!1,dropAllowed:!this.noDrop,hasFocus:!1}},computed:{computedAccept:function(){var t=this.accept;return 0===(t=(t||"").trim().split(/[,\s]+/).filter(pe)).length?null:t.map((function(t){var e="name",i="^",n="$";return dt.test(t)?i="":(e="type",Ct.test(t)&&(n=".+$",t=t.slice(0,-1))),t=Po(t),{rx:new RegExp("".concat(i).concat(t).concat(n)),prop:e}}))},computedCapture:function(){var t=this.capture;return!0===t||""===t||(t||null)},computedAttrs:function(){var t=this.name,e=this.disabled,i=this.required,n=this.form,o=this.computedCapture,s=this.accept,a=this.multiple,l=this.directory;return r(r({},this.bvAttrs),{},{type:"file",id:this.safeId(),name:t,disabled:e,required:i,form:n||null,capture:o,accept:s||null,multiple:a,directory:l,webkitdirectory:l,"aria-required":i?"true":null})},computedFileNameFormatter:function(){var t=this.fileNameFormatter;return Es(t)?t:this.defaultFileNameFormatter},clonedFiles:function(){return fe(this.files)},flattenedFiles:function(){return To(this.files)},fileNames:function(){return this.flattenedFiles.map((function(t){return t.name}))},labelContent:function(){if(this.dragging&&!this.noDrop)return this.normalizeSlot("drop-placeholder",{allowed:this.dropAllowed})||(this.dropAllowed?this.dropPlaceholder:this.$createElement("span",{staticClass:"text-danger"},this.noDropPlaceholder));if(0===this.files.length)return this.normalizeSlot("placeholder")||this.placeholder;var t=this.flattenedFiles,e=this.clonedFiles,i=this.fileNames,n=this.computedFileNameFormatter;return this.hasNormalizedSlot(Zr)?this.normalizeSlot(Zr,{files:t,filesTraversed:e,names:i}):n(t,e,i)}},watch:(Zh={},c(Zh,Up,(function(t){(!t||Kt(t)&&0===t.length)&&this.reset()})),c(Zh,"files",(function(t,e){if(!ll(t,e)){var i=this.multiple,n=this.noTraverse,r=!i||n?To(t):t;this.$emit(Yp,i?r:r[0]||null)}})),Zh),created:function(){this.$_form=null},mounted:function(){var t=Zo("form",this.$el);t&&(Rs(t,"reset",this.reset,Tr),this.$_form=t)},beforeDestroy:function(){var t=this.$_form;t&&As(t,"reset",this.reset,Tr)},methods:{isFileValid:function(t){if(!t)return!1;var e=this.computedAccept;return!e||e.some((function(e){return e.rx.test(t[e.prop])}))},isFilesArrayValid:function(t){var e=this;return Kt(t)?t.every((function(t){return e.isFileValid(t)})):this.isFileValid(t)},defaultFileNameFormatter:function(t,e,i){return i.join(", ")},setFiles:function(t){this.dropAllowed=!this.noDrop,this.dragging=!1,this.files=this.multiple?this.directory?t:To(t):To(t).slice(0,1)},setInputFiles:function(t){try{var e=new ClipboardEvent("").clipboardData||new DataTransfer;To(fe(t)).forEach((function(t){delete t.$path,e.items.add(t)})),this.$refs.input.files=e.files}catch(t){}},reset:function(){try{var t=this.$refs.input;t.value="",t.type="",t.type="file"}catch(t){}this.files=[]},handleFiles:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e){var i=t.filter(this.isFilesArrayValid);i.length>0&&(this.setFiles(i),this.setInputFiles(i))}else this.setFiles(t)},focusHandler:function(t){this.plain||"focusout"===t.type?this.hasFocus=!1:this.hasFocus=!0},onChange:function(t){var e=this,i=t.type,n=t.target,r=t.dataTransfer,o=void 0===r?{}:r,s="drop"===i;this.$emit(Mn,t);var a=mo(o.items||[]);if(W&&a.length>0&&!Nt(Xp(a[0])))(function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Promise.all(mo(t).filter((function(t){return"file"===t.kind})).map((function(t){var i=Xp(t);if(i){if(i.isDirectory&&e)return Zp(i.createReader(),"".concat(i.name,"/"));if(i.isFile)return new Promise((function(t){i.file((function(e){e.$path="",t(e)}))}))}return null})).filter(pe))})(a,this.directory).then((function(t){return e.handleFiles(t,s)}));else{var l=mo(n.files||o.files||[]).map((function(t){return t.$path=t.webkitRelativePath||"",t}));this.handleFiles(l,s)}},onDragenter:function(t){Hs(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragover:function(t){Hs(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragleave:function(t){var e=this;Hs(t),this.$nextTick((function(){e.dragging=!1,e.dropAllowed=!e.noDrop}))},onDrop:function(t){var e=this;Hs(t),this.dragging=!1,this.noDrop||this.disabled||!this.dropAllowed?this.$nextTick((function(){e.dropAllowed=!e.noDrop})):this.onChange(t)}},render:function(t){var e=this.custom,i=this.plain,n=this.size,r=this.dragging,o=this.stateClass,s=this.bvAttrs,a=t("input",{class:[{"form-control-file":i,"custom-file-input":e,focus:e&&this.hasFocus},o],style:e?{zIndex:-5}:{},attrs:this.computedAttrs,on:{change:this.onChange,focusin:this.focusHandler,focusout:this.focusHandler,reset:this.reset},ref:"input"});if(i)return a;var l=t("label",{staticClass:"custom-file-label",class:{dragging:r},attrs:{for:this.safeId(),"data-browse":this.browseText||null}},[t("span",{staticClass:"d-block form-file-text",style:{pointerEvents:"none"}},[this.labelContent])]);return t("div",{staticClass:"custom-file b-form-file",class:[c({},"b-custom-control-".concat(n),n),o,s.class],style:s.style,attrs:{id:this.safeId("_BV_file_outer_")},on:{dragenter:this.onDragenter,dragover:this.onDragover,dragleave:this.onDragleave,drop:this.onDrop}},[a,l])}}),tm=$e({components:{BFormFile:Qp,BFile:Qp}}),em=function(t){return"\\"+t},im=function(t){var e=(t=Io(t)).length,i=t.charCodeAt(0);return t.split("").reduce((function(n,r,o){var s=t.charCodeAt(o);return 0===s?n+"�":127===s||s>=1&&s<=31||0===o&&s>=48&&s<=57||1===o&&s>=48&&s<=57&&45===i?n+em("".concat(s.toString(16)," ")):0===o&&45===s&&1===e?n+em(r):s>=128||45===s||95===s||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122?n+r:n+em(r)}),"")},nm=["auto","start","end","center","baseline","stretch"],rm=bs((function(t,e,i){var n=t;if(!jt(i)&&!1!==i)return e&&(n+="-".concat(e)),"col"!==t||""!==i&&!0!==i?(n+="-".concat(i),Eo(n)):Eo(n)})),om=ie(null),sm={name:ni,functional:!0,get props(){return delete this.props,this.props=(t=ks().filter(pe),e=t.reduce((function(t,e){return t[e]=Ds(Lr),t}),ie(null)),i=t.reduce((function(t,e){return t[Bs(e,"offset")]=Ds(Hr),t}),ie(null)),n=t.reduce((function(t,e){return t[Bs(e,"order")]=Ds(Hr),t}),ie(null)),om=ee(ie(null),{col:se(e),offset:se(i),order:se(n)}),Is(he(r(r(r(r({},e),i),n),{},{alignSelf:Ds(Fr,null,(function(t){return vo(nm,t)})),col:Ds(kr,!1),cols:Ds(Hr),offset:Ds(Hr),order:Ds(Hr),tag:Ds(Fr,"div")})),ni));var t,e,i,n},render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.cols,a=n.offset,l=n.order,u=n.alignSelf,d=[];for(var h in om)for(var f=om[h],p=0;p<f.length;p++){var m=rm(h,f[p].replace(h,""),n[f[p]]);m&&d.push(m)}var v=d.some((function(t){return Lt.test(t)}));return d.push((c(i={col:n.col||!v&&!s},"col-".concat(s),s),c(i,"offset-".concat(a),a),c(i,"order-".concat(l),l),c(i,"align-self-".concat(u),u),i)),t(n.tag,I(r,{class:d}),o)}},am=["input","select","textarea"],lm=am.map((function(t){return"".concat(t,":not([disabled])")})).join(),cm=[].concat(am,["a","button","label"]),um={name:wi,mixins:[Lc,ep,So],get props(){return delete this.props,this.props=Is(he(r(r(r(r({},Vc),tp),ks().reduce((function(t,e){return t[Bs(e,"contentCols")]=Ds(Lr),t[Bs(e,"labelAlign")]=Ds(Fr),t[Bs(e,"labelCols")]=Ds(Lr),t}),ie(null))),{},{description:Ds(Fr),disabled:Ds(kr,!1),feedbackAriaLive:Ds(Fr,"assertive"),invalidFeedback:Ds(Fr),label:Ds(Fr),labelClass:Ds(Or),labelFor:Ds(Fr),labelSize:Ds(Fr),labelSrOnly:Ds(kr,!1),tooltip:Ds(kr,!1),validFeedback:Ds(Fr),validated:Ds(kr,!1)})),wi)},data:function(){return{ariaDescribedby:null}},computed:{contentColProps:function(){return this.getColProps(this.$props,"content")},labelAlignClasses:function(){return this.getAlignClasses(this.$props,"label")},labelColProps:function(){return this.getColProps(this.$props,"label")},isHorizontal:function(){return se(this.contentColProps).length>0||se(this.labelColProps).length>0}},watch:{ariaDescribedby:function(t,e){t!==e&&this.updateAriaDescribedby(t,e)}},mounted:function(){var t=this;this.$nextTick((function(){t.updateAriaDescribedby(t.ariaDescribedby)}))},methods:{getAlignClasses:function(t,e){return ks().reduce((function(i,n){var r=t[Bs(n,"".concat(e,"Align"))]||null;return r&&i.push(["text",n,r].filter(pe).join("-")),i}),[])},getColProps:function(t,e){return ks().reduce((function(i,n){var r=t[Bs(n,"".concat(e,"Cols"))];return Wt(r=""===r||(r||!1))||"auto"===r||(r=(r=ko(r,0))>0&&r),r&&(i[n||(Wt(r)?"col":"cols")]=r),i}),{})},updateAriaDescribedby:function(t,e){var i=this.labelFor;if(Y&&i){var n=Ko("#".concat(im(i)),this.$refs.content);if(n){var r="aria-describedby",o=(t||"").split(wt),s=(e||"").split(wt),a=(os(n,r)||"").split(wt).filter((function(t){return!vo(s,t)})).concat(o).filter((function(t,e,i){return i.indexOf(t)===e})).filter(pe).join(" ").trim();a?ns(n,r,a):rs(n,r)}}},onLegendClick:function(t){if(!this.labelFor){var e=t.target,i=e?e.tagName:"";if(-1===cm.indexOf(i)){var n=qo(lm,this.$refs.content).filter(Wo);1===n.length&&vs(n[0])}}}},render:function(t){var e=this.computedState,i=this.feedbackAriaLive,n=this.isHorizontal,o=this.labelFor,s=this.normalizeSlot,a=this.safeId,l=this.tooltip,c=a(),u=!o,d=t(),h=s(eo)||this.label,f=h?a("_BV_label_"):null;if(h||n){var p=this.labelSize,m=this.labelColProps,v=u?"legend":"label";this.labelSrOnly?(h&&(d=t(v,{class:"sr-only",attrs:{id:f,for:o||null}},[h])),d=t(n?sm:"div",{props:n?m:{}},[d])):d=t(n?sm:v,{on:u?{click:this.onLegendClick}:{},props:n?r(r({},m),{},{tag:v}):{},attrs:{id:f,for:o||null,tabindex:u?"-1":null},class:[u?"bv-no-focus-ring":"",n||u?"col-form-label":"",!n&&u?"pt-0":"",n||u?"":"d-block",p?"col-form-label-".concat(p):"",this.labelAlignClasses,this.labelClass]},[h])}var g=t(),b=s("invalid-feedback")||this.invalidFeedback,y=b?a("_BV_feedback_invalid_"):null;b&&(g=t(Hf,{props:{ariaLive:i,id:y,state:e,tooltip:l},attrs:{tabindex:b?"-1":null}},[b]));var T=t(),w=s("valid-feedback")||this.validFeedback,C=w?a("_BV_feedback_valid_"):null;w&&(T=t(Nf,{props:{ariaLive:i,id:C,state:e,tooltip:l},attrs:{tabindex:w?"-1":null}},[w]));var S=t(),k=s("description")||this.description,x=k?a("_BV_description_"):null;k&&(S=t(Af,{attrs:{id:x,tabindex:"-1"}},[k]));var $=this.ariaDescribedby=[x,!1===e?y:null,!0===e?C:null].filter(pe).join(" ")||null,B=t(n?sm:"div",{props:n?this.contentColProps:{},ref:"content"},[s(Kr,{ariaDescribedby:$,descriptionId:x,id:c,labelId:f})||t(),g,T,S]);return t(u?"fieldset":n?Gf:"div",{staticClass:"form-group",class:[{"was-validated":this.validated},this.stateClass],attrs:{id:c,disabled:u?this.disabled:null,role:u?null:"group","aria-invalid":this.computedAriaInvalid,"aria-labelledby":u&&n?f:null}},n&&u?[t(Gf,[d,B])]:[d,B])}},dm=$e({components:{BFormGroup:um,BFormFieldset:um}}),hm=L({computed:{selectionStart:{cache:!1,get:function(){return this.$refs.input.selectionStart},set:function(t){this.$refs.input.selectionStart=t}},selectionEnd:{cache:!1,get:function(){return this.$refs.input.selectionEnd},set:function(t){this.$refs.input.selectionEnd=t}},selectionDirection:{cache:!1,get:function(){return this.$refs.input.selectionDirection},set:function(t){this.$refs.input.selectionDirection=t}}},methods:{select:function(){var t;(t=this.$refs.input).select.apply(t,arguments)},setSelectionRange:function(){var t;(t=this.$refs.input).setSelectionRange.apply(t,arguments)},setRangeText:function(){var t;(t=this.$refs.input).setRangeText.apply(t,arguments)}}}),fm=Vs("value",{type:Hr,defaultValue:"",event:"update"}),pm=fm.mixin,mm=fm.props,vm=fm.prop,gm=fm.event,bm=Is(he(r(r({},mm),{},{ariaInvalid:Ds(Rr,!1),autocomplete:Ds(Fr),debounce:Ds(Hr,0),formatter:Ds($r),lazy:Ds(kr,!1),lazyFormatter:Ds(kr,!1),number:Ds(kr,!1),placeholder:Ds(Fr),plaintext:Ds(kr,!1),readonly:Ds(kr,!1),trim:Ds(kr,!1)})),"formTextControls"),ym=L({mixins:[pm],props:bm,data:function(){var t=this[vm];return{localValue:Io(t),vModelValue:this.modifyValue(t)}},computed:{computedClass:function(){var t=this.plaintext,e=this.type,i="range"===e,n="color"===e;return[{"custom-range":i,"form-control-plaintext":t&&!i&&!n,"form-control":n||!t&&!i},this.sizeFormClass,this.stateClass]},computedDebounce:function(){return sa(ko(this.debounce,0),0)},hasFormatter:function(){return Es(this.formatter)}},watch:c({},vm,(function(t){var e=Io(t),i=this.modifyValue(t);e===this.localValue&&i===this.vModelValue||(this.clearDebounce(),this.localValue=e,this.vModelValue=i)})),created:function(){this.$_inputDebounceTimer=null},beforeDestroy:function(){this.clearDebounce()},methods:{clearDebounce:function(){clearTimeout(this.$_inputDebounceTimer),this.$_inputDebounceTimer=null},formatValue:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=Io(t),!this.hasFormatter||this.lazyFormatter&&!i||(t=this.formatter(t,e)),t},modifyValue:function(t){return t=Io(t),this.trim&&(t=t.trim()),this.number&&(t=xo(t,t)),t},updateValue:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.lazy;if(!n||i){this.clearDebounce();var r=function(){if((t=e.modifyValue(t))!==e.vModelValue)e.vModelValue=t,e.$emit(gm,t);else if(e.hasFormatter){var i=e.$refs.input;i&&t!==i.value&&(i.value=t)}},o=this.computedDebounce;o>0&&!n&&!i?this.$_inputDebounceTimer=setTimeout(r,o):r()}},onInput:function(t){if(!t.target.composing){var e=t.target.value,i=this.formatValue(e,t);!1===i||t.defaultPrevented?Hs(t,{propagation:!1}):(this.localValue=i,this.updateValue(i),this.$emit(ir,i))}},onChange:function(t){var e=t.target.value,i=this.formatValue(e,t);!1===i||t.defaultPrevented?Hs(t,{propagation:!1}):(this.localValue=i,this.updateValue(i,!0),this.$emit(Mn,i))},onBlur:function(t){var e=t.target.value,i=this.formatValue(e,t,!0);!1!==i&&(this.localValue=Io(this.modifyValue(i)),this.updateValue(i,!0)),this.$emit(Rn,t)},focus:function(){this.disabled||vs(this.$el)},blur:function(){this.disabled||gs(this.$el)}}}),Tm=L({computed:{validity:{cache:!1,get:function(){return this.$refs.input.validity}},validationMessage:{cache:!1,get:function(){return this.$refs.input.validationMessage}},willValidate:{cache:!1,get:function(){return this.$refs.input.willValidate}}},methods:{setCustomValidity:function(){var t;return(t=this.$refs.input).setCustomValidity.apply(t,arguments)},checkValidity:function(){var t;return(t=this.$refs.input).checkValidity.apply(t,arguments)},reportValidity:function(){var t;return(t=this.$refs.input).reportValidity.apply(t,arguments)}}}),wm=["text","password","email","number","url","tel","search","range","color","date","time","datetime","datetime-local","month","week"],Cm=Is(he(r(r(r(r(r(r({},Vc),qf),Jf),tp),bm),{},{list:Ds(Fr),max:Ds(Hr),min:Ds(Hr),noWheel:Ds(kr,!1),step:Ds(Hr),type:Ds(Fr,"text",(function(t){return vo(wm,t)}))})),Ci),Sm=L({name:Ci,mixins:[Tl,Lc,Kf,Qf,ep,ym,hm,Tm],props:Cm,computed:{localType:function(){var t=this.type;return vo(wm,t)?t:"text"},computedAttrs:function(){var t=this.localType,e=this.name,i=this.form,n=this.disabled,r=this.placeholder,o=this.required,s=this.min,a=this.max,l=this.step;return{id:this.safeId(),name:e,form:i,type:t,disabled:n,placeholder:r,required:o,autocomplete:this.autocomplete||null,readonly:this.readonly||this.plaintext,min:s,max:a,step:l,list:"password"!==t?this.list:null,"aria-required":o?"true":null,"aria-invalid":this.computedAriaInvalid}},computedListeners:function(){return r(r({},this.bvListeners),{},{input:this.onInput,change:this.onChange,blur:this.onBlur})}},watch:{noWheel:function(t){this.setWheelStopper(t)}},mounted:function(){this.setWheelStopper(this.noWheel)},deactivated:function(){this.setWheelStopper(!1)},activated:function(){this.setWheelStopper(this.noWheel)},beforeDestroy:function(){this.setWheelStopper(!1)},methods:{setWheelStopper:function(t){var e=this.$el;Ms(t,e,"focus",this.onWheelFocus),Ms(t,e,"blur",this.onWheelBlur),t||As(document,"wheel",this.stopWheel)},onWheelFocus:function(){Rs(document,"wheel",this.stopWheel)},onWheelBlur:function(){As(document,"wheel",this.stopWheel)},stopWheel:function(t){Hs(t,{propagation:!1}),gs(this.$el)}},render:function(t){return t("input",{class:this.computedClass,attrs:this.computedAttrs,domProps:{value:this.localValue},on:this.computedListeners,ref:"input"})}}),km=$e({components:{BFormInput:Sm,BInput:Sm}}),xm=Is(wp,xi),$m=L({name:xi,mixins:[Cp],provide:function(){var t=this;return{getBvRadioGroup:function(){return t}}},props:xm,computed:{isRadioGroup:function(){return!0}}}),Bm=$e({components:{BFormRadio:pp,BRadio:pp,BFormRadioGroup:$m,BRadioGroup:$m}}),Dm=Vs("value",{type:Hr,event:Mn}),_m=Dm.mixin,Fm=Dm.props,Pm=Dm.prop,Im=Dm.event,Om=function(t){return sa(3,ko(t,5))},Em=function(t,e,i){return sa(oa(t,i),e)},Vm=L({name:"BVFormRatingStar",mixins:[So],props:{disabled:Ds(kr,!1),focused:Ds(kr,!1),hasClear:Ds(kr,!1),rating:Ds(Br,0),readonly:Ds(kr,!1),star:Ds(Br,0),variant:Ds(Fr)},methods:{onClick:function(t){this.disabled||this.readonly||(Hs(t,{propagation:!1}),this.$emit(hr,this.star))}},render:function(t){var e=this.rating,i=this.star,n=this.focused,r=this.hasClear,o=this.variant,s=this.disabled,a=this.readonly,l=r?0:1,c=e>=i?"full":e>=i-.5?"half":"empty",u={variant:o,disabled:s,readonly:a};return t("span",{staticClass:"b-rating-star",class:{focused:n&&e===i||!ko(e)&&i===l,"b-rating-star-empty":"empty"===c,"b-rating-star-half":"half"===c,"b-rating-star-full":"full"===c},attrs:{tabindex:s||a?null:"-1"},on:{click:this.onClick}},[t("span",{staticClass:"b-rating-icon"},[this.normalizeSlot(c,u)])])}}),Lm=Is(he(r(r(r(r(r({},Vc),Fm),ue(qf,["required","autofocus"])),Jf),{},{color:Ds(Fr),iconClear:Ds(Fr,"x"),iconEmpty:Ds(Fr,"star"),iconFull:Ds(Fr,"star-fill"),iconHalf:Ds(Fr,"star-half"),inline:Ds(kr,!1),locale:Ds(Er),noBorder:Ds(kr,!1),precision:Ds(Hr),readonly:Ds(kr,!1),showClear:Ds(kr,!1),showValue:Ds(kr,!1),showValueMax:Ds(kr,!1),stars:Ds(Hr,5,(function(t){return ko(t)>=3})),variant:Ds(Fr)})),$i),Rm=L({name:$i,components:{BIconStar:Wa,BIconStarHalf:Ya,BIconStarFill:Ua,BIconX:qa},mixins:[Lc,_m,Qf],props:Lm,data:function(){var t=xo(this[Pm],null),e=Om(this.stars);return{localValue:Nt(t)?null:Em(t,0,e),hasFocus:!1}},computed:{computedStars:function(){return Om(this.stars)},computedRating:function(){var t=xo(this.localValue,0),e=ko(this.precision,3);return Em(xo(t.toFixed(e)),0,this.computedStars)},computedLocale:function(){var t=go(this.locale).filter(pe);return new Intl.NumberFormat(t).resolvedOptions().locale},isInteractive:function(){return!this.disabled&&!this.readonly},isRTL:function(){return Ec(this.computedLocale)},formattedRating:function(){var t=ko(this.precision),e=this.showValueMax,i=this.computedLocale,n={notation:"standard",minimumFractionDigits:isNaN(t)?0:t,maximumFractionDigits:isNaN(t)?3:t},r=this.computedStars.toLocaleString(i),o=this.localValue;return o=Nt(o)?e?"-":"":o.toLocaleString(i,n),e?"".concat(o,"/").concat(r):o}},watch:(Jh={},c(Jh,Pm,(function(t,e){if(t!==e){var i=xo(t,null);this.localValue=Nt(i)?null:Em(i,0,this.computedStars)}})),c(Jh,"localValue",(function(t,e){t!==e&&t!==(this.value||0)&&this.$emit(Im,t||null)})),c(Jh,"disabled",(function(t){t&&(this.hasFocus=!1,this.blur())})),Jh),methods:{focus:function(){this.disabled||vs(this.$el)},blur:function(){this.disabled||gs(this.$el)},onKeydown:function(t){var e=t.keyCode;if(this.isInteractive&&vo([nl,Qa,rl,sl],e)){Hs(t,{propagation:!1});var i=ko(this.localValue,0),n=this.showClear?0:1,r=this.computedStars,o=this.isRTL?-1:1;e===nl?this.localValue=Em(i-o,n,r)||null:e===rl?this.localValue=Em(i+o,n,r):e===Qa?this.localValue=Em(i-1,n,r)||null:e===sl&&(this.localValue=Em(i+1,n,r))}},onSelected:function(t){this.isInteractive&&(this.localValue=t)},onFocus:function(t){this.hasFocus=!!this.isInteractive&&"focus"===t.type},renderIcon:function(t){return this.$createElement(Ja,{props:{icon:t,variant:this.disabled||this.color?null:this.variant||null}})},iconEmptyFn:function(){return this.renderIcon(this.iconEmpty)},iconHalfFn:function(){return this.renderIcon(this.iconHalf)},iconFullFn:function(){return this.renderIcon(this.iconFull)},iconClearFn:function(){return this.$createElement(Ja,{props:{icon:this.iconClear}})}},render:function(t){var e=this,i=this.disabled,n=this.readonly,r=this.name,o=this.form,s=this.inline,a=this.variant,l=this.color,c=this.noBorder,u=this.hasFocus,d=this.computedRating,h=this.computedStars,f=this.formattedRating,p=this.showClear,m=this.isRTL,v=this.isInteractive,g=this.$scopedSlots,b=[];if(p&&!i&&!n){var y=t("span",{staticClass:"b-rating-icon"},[(g["icon-clear"]||this.iconClearFn)()]);b.push(t("span",{staticClass:"b-rating-star b-rating-star-clear flex-grow-1",class:{focused:u&&0===d},attrs:{tabindex:v?"-1":null},on:{click:function(){return e.onSelected(null)}},key:"clear"},[y]))}for(var T=0;T<h;T++){var w=T+1;b.push(t(Vm,{staticClass:"flex-grow-1",style:l&&!i?{color:l}:{},props:{rating:d,star:w,variant:i?null:a||null,disabled:i,readonly:n,focused:u,hasClear:p},on:{selected:this.onSelected},scopedSlots:{empty:g["icon-empty"]||this.iconEmptyFn,half:g["icon-half"]||this.iconHalfFn,full:g["icon-full"]||this.iconFullFn},key:T}))}return r&&b.push(t("input",{attrs:{type:"hidden",value:Nt(this.localValue)?"":d,name:r,form:o||null},key:"hidden"})),this.showValue&&b.push(t("b",{staticClass:"b-rating-value flex-grow-1",attrs:{"aria-hidden":"true"},key:"value"},Io(f))),t("output",{staticClass:"b-rating form-control align-items-center",class:[{"d-inline-flex":s,"d-flex":!s,"border-0":c,disabled:i,readonly:!i&&n},this.sizeFormClass],attrs:{id:this.safeId(),dir:m?"rtl":"ltr",tabindex:i?null:"0",disabled:i,role:"slider","aria-disabled":i?"true":null,"aria-readonly":!i&&n?"true":null,"aria-live":"off","aria-valuemin":p?"0":"1","aria-valuemax":Io(h),"aria-valuenow":d?Io(d):null},on:{keydown:this.onKeydown,focus:this.onFocus,blur:this.onFocus}},b)}}),Am=$e({components:{BFormRating:Rm,BRating:Rm}}),Mm=Vs("value"),Hm=Mm.mixin,zm=Mm.props,Nm=Mm.prop,jm=Mm.event,Gm=Is(he(r(r({},Of),{},{labelField:Ds(Fr,"label"),optionsField:Ds(Fr,"options")})),"formOptions"),Wm=L({mixins:[Ef],props:Gm,methods:{normalizeOption:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(Zt(t)){var i=ve(t,this.valueField),n=ve(t,this.textField),r=ve(t,this.optionsField,null);return Nt(r)?{value:zt(i)?e||n:i,text:String(zt(n)?e:n),html:ve(t,this.htmlField),disabled:Boolean(ve(t,this.disabledField))}:{label:String(ve(t,this.labelField)||n),options:this.normalizeOptions(r)}}return{value:e||t,text:String(t),disabled:!1}}}}),Um=Is({disabled:Ds(kr,!1),value:Ds(Cr,void 0,!0)},_i),Ym=L({name:_i,functional:!0,props:Um,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.value;return t("option",I(n,{attrs:{disabled:i.disabled},domProps:{value:o}}),r)}}),qm=Is(he(r(r({},Of),{},{label:Ds(Fr,void 0,!0)})),Fi),Km=L({name:Fi,mixins:[So,Ef],props:qm,render:function(t){var e=this.label,i=this.formOptions.map((function(e,i){var n=e.value,r=e.text,o=e.html,s=e.disabled;return t(Ym,{attrs:{value:n,disabled:s},domProps:Zl(o,r),key:"option_".concat(i)})}));return t("optgroup",{attrs:{label:e}},[this.normalizeSlot(Jr),i,this.normalizeSlot()])}}),Xm=Is(he(r(r(r(r(r(r(r({},Vc),zm),qf),Xf),Jf),tp),{},{ariaInvalid:Ds(Rr,!1),multiple:Ds(kr,!1),selectSize:Ds(Br,0)})),Di),Zm=L({name:Di,mixins:[Lc,Hm,Kf,Qf,ep,Zf,Wm,So],props:Xm,data:function(){return{localValue:this[Nm]}},computed:{computedSelectSize:function(){return this.plain||0!==this.selectSize?this.selectSize:null},inputClass:function(){return[this.plain?"form-control":"custom-select",this.size&&this.plain?"form-control-".concat(this.size):null,this.size&&!this.plain?"custom-select-".concat(this.size):null,this.stateClass]}},watch:{value:function(t){this.localValue=t},localValue:function(){this.$emit(jm,this.localValue)}},methods:{focus:function(){vs(this.$refs.input)},blur:function(){gs(this.$refs.input)},onChange:function(t){var e=this,i=t.target,n=mo(i.options).filter((function(t){return t.selected})).map((function(t){return"_value"in t?t._value:t.value}));this.localValue=i.multiple?n:n[0],this.$nextTick((function(){e.$emit(Mn,e.localValue)}))}},render:function(t){var e=this.name,i=this.disabled,n=this.required,r=this.computedSelectSize,o=this.localValue,s=this.formOptions.map((function(e,i){var n=e.value,r=e.label,o=e.options,s=e.disabled,a="option_".concat(i);return Kt(o)?t(Km,{props:{label:r,options:o},key:a}):t(Ym,{props:{value:n,disabled:s},domProps:Zl(e.html,e.text),key:a})}));return t("select",{class:this.inputClass,attrs:{id:this.safeId(),name:e,form:this.form||null,multiple:this.multiple||null,size:r,disabled:i,required:n,"aria-required":n?"true":null,"aria-invalid":this.computedAriaInvalid},on:{change:this.onChange},directives:[{name:"model",value:o}],ref:"input"},[this.normalizeSlot(Jr),s,this.normalizeSlot()])}}),Jm=$e({components:{BFormSelect:Zm,BFormSelectOption:Ym,BFormSelectOptionGroup:Km,BSelect:Zm,BSelectOption:Ym,BSelectOptionGroup:Km}}),Qm=Vs("value",{type:Vr}),tv=Qm.mixin,ev=Qm.props,iv=Qm.prop,nv=Qm.event,rv=[sl,Qa,il,tl,33,34],ov=Is(he(r(r(r(r(r(r({},Vc),ev),ue(qf,["required","autofocus"])),Jf),tp),{},{ariaControls:Ds(Fr),ariaLabel:Ds(Fr),formatterFn:Ds($r),inline:Ds(kr,!1),labelDecrement:Ds(Fr,"Decrement"),labelIncrement:Ds(Fr,"Increment"),locale:Ds(Er),max:Ds(Hr,100),min:Ds(Hr,1),placeholder:Ds(Fr),readonly:Ds(kr,!1),repeatDelay:Ds(Hr,500),repeatInterval:Ds(Hr,100),repeatStepMultiplier:Ds(Hr,4),repeatThreshold:Ds(Hr,10),step:Ds(Hr,1),vertical:Ds(kr,!1),wrap:Ds(kr,!1)})),Pi),sv=L({name:Pi,mixins:[pl,Lc,tv,Qf,ep,So],inheritAttrs:!1,props:ov,data:function(){return{localValue:xo(this[iv],null),hasFocus:!1}},computed:{required:function(){return!1},spinId:function(){return this.safeId()},computedInline:function(){return this.inline&&!this.vertical},computedReadonly:function(){return this.readonly&&!this.disabled},computedRequired:function(){return this.required&&!this.computedReadonly&&!this.disabled},computedStep:function(){return xo(this.step,1)},computedMin:function(){return xo(this.min,1)},computedMax:function(){var t=xo(this.max,100),e=this.computedStep,i=this.computedMin;return ca((t-i)/e)*e+i},computedDelay:function(){var t=ko(this.repeatDelay,0);return t>0?t:500},computedInterval:function(){var t=ko(this.repeatInterval,0);return t>0?t:100},computedThreshold:function(){return sa(ko(this.repeatThreshold,10),1)},computedStepMultiplier:function(){return sa(ko(this.repeatStepMultiplier,4),1)},computedPrecision:function(){var t=this.computedStep;return ca(t)===t?0:(t.toString().split(".")[1]||"").length},computedMultiplier:function(){return ua(10,this.computedPrecision||0)},valueAsFixed:function(){var t=this.localValue;return Nt(t)?"":t.toFixed(this.computedPrecision)},computedLocale:function(){var t=go(this.locale).filter(pe);return new Intl.NumberFormat(t).resolvedOptions().locale},computedRTL:function(){return Ec(this.computedLocale)},defaultFormatter:function(){var t=this.computedPrecision;return new Intl.NumberFormat(this.computedLocale,{style:"decimal",useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:t,maximumFractionDigits:t,notation:"standard"}).format},computedFormatter:function(){var t=this.formatterFn;return Es(t)?t:this.defaultFormatter},computedAttrs:function(){return r(r({},this.bvAttrs),{},{role:"group",lang:this.computedLocale,tabindex:this.disabled?null:"-1",title:this.ariaLabel})},computedSpinAttrs:function(){var t=this.spinId,e=this.localValue,i=this.computedRequired,n=this.disabled,o=this.state,s=this.computedFormatter,a=!Nt(e);return r(r({dir:this.computedRTL?"rtl":"ltr"},this.bvAttrs),{},{id:t,role:"spinbutton",tabindex:n?null:"0","aria-live":"off","aria-label":this.ariaLabel||null,"aria-controls":this.ariaControls||null,"aria-invalid":!1===o||!a&&i?"true":null,"aria-required":i?"true":null,"aria-valuemin":Io(this.computedMin),"aria-valuemax":Io(this.computedMax),"aria-valuenow":a?e:null,"aria-valuetext":a?s(e):null})}},watch:(Qh={},c(Qh,iv,(function(t){this.localValue=xo(t,null)})),c(Qh,"localValue",(function(t){this.$emit(nv,t)})),c(Qh,"disabled",(function(t){t&&this.clearRepeat()})),c(Qh,"readonly",(function(t){t&&this.clearRepeat()})),Qh),created:function(){this.$_autoDelayTimer=null,this.$_autoRepeatTimer=null,this.$_keyIsDown=!1},beforeDestroy:function(){this.clearRepeat()},deactivated:function(){this.clearRepeat()},methods:{focus:function(){this.disabled||vs(this.$refs.spinner)},blur:function(){this.disabled||gs(this.$refs.spinner)},emitChange:function(){this.$emit(Mn,this.localValue)},stepValue:function(t){var e=this.localValue;if(!this.disabled&&!Nt(e)){var i=this.computedStep*t,n=this.computedMin,r=this.computedMax,o=this.computedMultiplier,s=this.wrap;e=da((e-n)/i)*i+n+i,e=da(e*o)/o,this.localValue=e>r?s?n:r:e<n?s?r:n:e}},onFocusBlur:function(t){this.hasFocus=!this.disabled&&"focus"===t.type},stepUp:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=this.localValue;Nt(e)?this.localValue=this.computedMin:this.stepValue(1*t)},stepDown:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=this.localValue;Nt(e)?this.localValue=this.wrap?this.computedMax:this.computedMin:this.stepValue(-1*t)},onKeydown:function(t){var e=t.keyCode,i=t.altKey,n=t.ctrlKey,r=t.metaKey;if(!(this.disabled||this.readonly||i||n||r)&&vo(rv,e)){if(Hs(t,{propagation:!1}),this.$_keyIsDown)return;this.resetTimers(),vo([sl,Qa],e)?(this.$_keyIsDown=!0,e===sl?this.handleStepRepeat(t,this.stepUp):e===Qa&&this.handleStepRepeat(t,this.stepDown)):33===e?this.stepUp(this.computedStepMultiplier):34===e?this.stepDown(this.computedStepMultiplier):e===il?this.localValue=this.computedMin:e===tl&&(this.localValue=this.computedMax)}},onKeyup:function(t){var e=t.keyCode,i=t.altKey,n=t.ctrlKey,r=t.metaKey;this.disabled||this.readonly||i||n||r||vo(rv,e)&&(Hs(t,{propagation:!1}),this.resetTimers(),this.$_keyIsDown=!1,this.emitChange())},handleStepRepeat:function(t,e){var i=this,n=t||{},r=n.type,o=n.button;if(!this.disabled&&!this.readonly){if("mousedown"===r&&o)return;this.resetTimers(),e(1);var s=this.computedThreshold,a=this.computedStepMultiplier,l=this.computedDelay,c=this.computedInterval;this.$_autoDelayTimer=setTimeout((function(){var t=0;i.$_autoRepeatTimer=setInterval((function(){e(t<s?1:a),t++}),c)}),l)}},onMouseup:function(t){var e=t||{},i=e.type,n=e.button;"mouseup"===i&&n||(Hs(t,{propagation:!1}),this.resetTimers(),this.setMouseup(!1),this.emitChange())},setMouseup:function(t){try{Ms(t,document.body,"mouseup",this.onMouseup,!1),Ms(t,document.body,"touchend",this.onMouseup,!1)}catch(t){}},resetTimers:function(){clearTimeout(this.$_autoDelayTimer),clearInterval(this.$_autoRepeatTimer),this.$_autoDelayTimer=null,this.$_autoRepeatTimer=null},clearRepeat:function(){this.resetTimers(),this.setMouseup(!1),this.$_keyIsDown=!1}},render:function(t){var e=this,i=this.spinId,n=this.localValue,r=this.computedInline,o=this.computedReadonly,s=this.vertical,a=this.disabled,l=this.computedFormatter,c=!Nt(n),u=function(n,r,l,c,u,d,h){var f=t(l,{props:{scale:e.hasFocus?1.5:1.25},attrs:{"aria-hidden":"true"}}),p={hasFocus:e.hasFocus},m=function(t){a||o||(Hs(t,{propagation:!1}),e.setMouseup(!0),vs(t.currentTarget),e.handleStepRepeat(t,n))};return t("button",{staticClass:"btn btn-sm border-0 rounded-0",class:{"py-0":!s},attrs:{tabindex:"-1",type:"button",disabled:a||o||d,"aria-disabled":a||o||d?"true":null,"aria-controls":i,"aria-label":r||null,"aria-keyshortcuts":u||null},on:{mousedown:m,touchstart:m},key:c||null,ref:c},[e.normalizeSlot(h,p)||f])},d=u(this.stepUp,this.labelIncrement,Ga,"inc","ArrowUp",!1,"increment"),h=u(this.stepDown,this.labelDecrement,Na,"dec","ArrowDown",!1,"decrement"),f=t();this.name&&!a&&(f=t("input",{attrs:{type:"hidden",name:this.name,form:this.form||null,value:this.valueAsFixed},key:"hidden"}));var p=t("output",{staticClass:"flex-grow-1",class:{"d-flex":s,"align-self-center":!s,"align-items-center":s,"border-top":s,"border-bottom":s,"border-left":!s,"border-right":!s},attrs:this.computedSpinAttrs,key:"output",ref:"spinner"},[t("bdi",c?l(n):this.placeholder||"")]);return t("div",{staticClass:"b-form-spinbutton form-control",class:[{disabled:a,readonly:o,focus:this.hasFocus,"d-inline-flex":r||s,"d-flex":!r&&!s,"align-items-stretch":!s,"flex-column":s},this.sizeFormClass,this.stateClass],attrs:this.computedAttrs,on:{keydown:this.onKeydown,keyup:this.onKeyup,"!focus":this.onFocusBlur,"!blur":this.onFocusBlur}},s?[d,f,p,h]:[h,f,p,d])}}),av=$e({components:{BFormSpinbutton:sv,BSpinbutton:sv}}),lv=Is(he(r(r({},Vc),{},{disabled:Ds(kr,!1),noRemove:Ds(kr,!1),pill:Ds(kr,!1),removeLabel:Ds(Fr,"Remove tag"),tag:Ds(Fr,"span"),title:Ds(Fr),variant:Ds(Fr,"secondary")})),Ii),cv=L({name:Ii,mixins:[Lc,So],props:lv,methods:{onRemove:function(t){var e=t.type,i=t.keyCode;this.disabled||"click"!==e&&("keydown"!==e||46!==i)||this.$emit("remove")}},render:function(t){var e=this.title,i=this.tag,n=this.variant,r=this.pill,o=this.disabled,s=this.safeId(),a=this.safeId("_taglabel_"),l=t();this.noRemove||o||(l=t(Ws,{staticClass:"b-form-tag-remove",props:{ariaLabel:this.removeLabel},attrs:{"aria-controls":s,"aria-describedby":a,"aria-keyshortcuts":"Delete"},on:{click:this.onRemove,keydown:this.onRemove}}));var c=t("span",{staticClass:"b-form-tag-content flex-grow-1 text-truncate",attrs:{id:a}},this.normalizeSlot()||e);return t(ql,{staticClass:"b-form-tag d-inline-flex align-items-baseline mw-100",class:{disabled:o},props:{tag:i,variant:n,pill:r},attrs:{id:s,title:e||null,"aria-labelledby":a}},[c,l])}}),uv=Vs("value",{type:Sr,defaultValue:[]}),dv=uv.mixin,hv=uv.props,fv=uv.prop,pv=uv.event,mv=["text","email","tel","url","number"],vv=[".b-form-tag","button","input","select"].join(" "),gv=function(t){return go(t).map((function(t){return Oo(Io(t))})).filter((function(t,e,i){return t.length>0&&i.indexOf(t)===e}))},bv=function(t){return Ut(t)?t:Qt(t)&&t.target.value||""},yv=Is(he(r(r(r(r(r(r({},Vc),hv),qf),Jf),tp),{},{addButtonText:Ds(Fr,"Add"),addButtonVariant:Ds(Fr,"outline-secondary"),addOnChange:Ds(kr,!1),duplicateTagText:Ds(Fr,"Duplicate tag(s)"),feedbackAriaLive:Ds(Fr,"assertive"),ignoreInputFocusSelector:Ds(Er,vv),inputAttrs:Ds(Dr,{}),inputClass:Ds(Or),inputId:Ds(Fr),inputType:Ds(Fr,"text",(function(t){return vo(mv,t)})),invalidTagText:Ds(Fr,"Invalid tag(s)"),limit:Ds(Br),limitTagsText:Ds(Fr,"Tag limit reached"),noAddOnEnter:Ds(kr,!1),noOuterFocus:Ds(kr,!1),noTagRemove:Ds(kr,!1),placeholder:Ds(Fr,"Add tag..."),removeOnDelete:Ds(kr,!1),separator:Ds(Er),tagClass:Ds(Or),tagPills:Ds(kr,!1),tagRemoveLabel:Ds(Fr,"Remove tag"),tagRemovedLabel:Ds(Fr,"Tag removed"),tagValidator:Ds($r),tagVariant:Ds(Fr,"secondary")})),Oi),Tv=L({name:Oi,mixins:[Tl,Lc,dv,Kf,Qf,ep,So],props:yv,data:function(){return{hasFocus:!1,newTag:"",tags:[],removedTags:[],tagsState:{all:[],valid:[],invalid:[],duplicate:[]},focusState:null}},computed:{computedInputId:function(){return this.inputId||this.safeId("__input__")},computedInputType:function(){return vo(mv,this.inputType)?this.inputType:"text"},computedInputAttrs:function(){var t=this.disabled,e=this.form;return r(r({},this.inputAttrs),{},{id:this.computedInputId,value:this.newTag,disabled:t,form:e})},computedInputHandlers:function(){return r(r({},ue(this.bvListeners,[Zn,Jn])),{},{blur:this.onInputBlur,change:this.onInputChange,focus:this.onInputFocus,input:this.onInputInput,keydown:this.onInputKeydown,reset:this.reset})},computedSeparator:function(){return go(this.separator).filter(Ut).filter(pe).join("")},computedSeparatorRegExp:function(){var t=this.computedSeparator;return t?new RegExp("[".concat(Po(t).replace(Tt,"\\s"),"]+")):null},computedJoiner:function(){var t=this.computedSeparator.charAt(0);return" "!==t?"".concat(t," "):t},computeIgnoreInputFocusSelector:function(){return go(this.ignoreInputFocusSelector).filter(pe).join(",").trim()},disableAddButton:function(){var t=this,e=Oo(this.newTag);return""===e||!this.splitTags(e).some((function(e){return!vo(t.tags,e)&&t.validateTag(e)}))},duplicateTags:function(){return this.tagsState.duplicate},hasDuplicateTags:function(){return this.duplicateTags.length>0},invalidTags:function(){return this.tagsState.invalid},hasInvalidTags:function(){return this.invalidTags.length>0},isLimitReached:function(){var t=this.limit;return Yt(t)&&t>=0&&this.tags.length>=t}},watch:(tf={},c(tf,fv,(function(t){this.tags=gv(t)})),c(tf,"tags",(function(t,e){ll(t,this[fv])||this.$emit(pv,t),ll(t,e)||(t=go(t).filter(pe),e=go(e).filter(pe),this.removedTags=e.filter((function(e){return!vo(t,e)})))})),c(tf,"tagsState",(function(t,e){ll(t,e)||this.$emit("tag-state",t.valid,t.invalid,t.duplicate)})),tf),created:function(){this.tags=gv(this[fv])},mounted:function(){var t=Zo("form",this.$el);t&&Rs(t,"reset",this.reset,Tr)},beforeDestroy:function(){var t=Zo("form",this.$el);t&&As(t,"reset",this.reset,Tr)},methods:{addTag:function(t){if(t=Ut(t)?t:this.newTag,!this.disabled&&""!==Oo(t)&&!this.isLimitReached){var e=this.parseTags(t);if(e.valid.length>0||0===e.all.length)if(Xo(this.getInput(),"select"))this.newTag="";else{var i=[].concat(S(e.invalid),S(e.duplicate));this.newTag=e.all.filter((function(t){return vo(i,t)})).join(this.computedJoiner).concat(i.length>0?this.computedJoiner.charAt(0):"")}e.valid.length>0&&(this.tags=go(this.tags,e.valid)),this.tagsState=e,this.focus()}},removeTag:function(t){this.disabled||(this.tags=this.tags.filter((function(e){return e!==t})))},reset:function(){var t=this;this.newTag="",this.tags=[],this.$nextTick((function(){t.removedTags=[],t.tagsState={all:[],valid:[],invalid:[],duplicate:[]}}))},onInputInput:function(t){if(!(this.disabled||Qt(t)&&t.target.composing)){var e=bv(t),i=this.computedSeparatorRegExp;this.newTag!==e&&(this.newTag=e),e=Io(e).replace(kt,""),i&&i.test(e.slice(-1))?this.addTag():this.tagsState=""===e?{all:[],valid:[],invalid:[],duplicate:[]}:this.parseTags(e)}},onInputChange:function(t){if(!this.disabled&&this.addOnChange){var e=bv(t);this.newTag!==e&&(this.newTag=e),this.addTag()}},onInputKeydown:function(t){if(!this.disabled&&Qt(t)){var e=t.keyCode,i=t.target.value||"";this.noAddOnEnter||e!==el?!this.removeOnDelete||8!==e&&46!==e||""!==i||(Hs(t,{propagation:!1}),this.tags=this.tags.slice(0,-1)):(Hs(t,{propagation:!1}),this.addTag())}},onClick:function(t){var e=this,i=this.computeIgnoreInputFocusSelector;i&&Zo(i,t.target,!0)||this.$nextTick((function(){e.focus()}))},onInputFocus:function(t){var e=this;"out"!==this.focusState&&(this.focusState="in",this.$nextTick((function(){Mo((function(){e.hasFocus&&(e.$emit("focus",t),e.focusState=null)}))})))},onInputBlur:function(t){var e=this;"in"!==this.focusState&&(this.focusState="out",this.$nextTick((function(){Mo((function(){e.hasFocus||(e.$emit(Rn,t),e.focusState=null)}))})))},onFocusin:function(t){this.hasFocus=!0,this.$emit(Zn,t)},onFocusout:function(t){this.hasFocus=!1,this.$emit(Jn,t)},handleAutofocus:function(){var t=this;this.$nextTick((function(){Mo((function(){t.autofocus&&t.focus()}))}))},focus:function(){this.disabled||vs(this.getInput())},blur:function(){this.disabled||gs(this.getInput())},splitTags:function(t){t=Io(t);var e=this.computedSeparatorRegExp;return(e?t.split(e):[t]).map(Oo).filter(pe)},parseTags:function(t){var e=this,i=this.splitTags(t),n={all:i,valid:[],invalid:[],duplicate:[]};return i.forEach((function(t){vo(e.tags,t)||vo(n.valid,t)?vo(n.duplicate,t)||n.duplicate.push(t):e.validateTag(t)?n.valid.push(t):vo(n.invalid,t)||n.invalid.push(t)})),n},validateTag:function(t){var e=this.tagValidator;return!Es(e)||e(t)},getInput:function(){return Ko("#".concat(im(this.computedInputId)),this.$el)},defaultRender:function(t){var e=t.addButtonText,i=t.addButtonVariant,n=t.addTag,o=t.disableAddButton,s=t.disabled,a=t.duplicateTagText,l=t.inputAttrs,c=t.inputClass,u=t.inputHandlers,d=t.inputType,h=t.invalidTagText,f=t.isDuplicate,p=t.isInvalid,m=t.isLimitReached,v=t.limitTagsText,g=t.noTagRemove,b=t.placeholder,y=t.removeTag,T=t.tagClass,w=t.tagPills,C=t.tagRemoveLabel,S=t.tagVariant,k=t.tags,x=this.$createElement,$=k.map((function(t){return t=Io(t),x(cv,{class:T,props:{disabled:s,noRemove:g,pill:w,removeLabel:C,tag:"li",title:t,variant:S},on:{remove:function(){return y(t)}},key:"tags_".concat(t)},t)})),B=h&&p?this.safeId("__invalid_feedback__"):null,D=a&&f?this.safeId("__duplicate_feedback__"):null,_=v&&m?this.safeId("__limit_feedback__"):null,F=[l["aria-describedby"],B,D,_].filter(pe).join(" "),P=x("input",{staticClass:"b-form-tags-input w-100 flex-grow-1 p-0 m-0 bg-transparent border-0",class:c,style:{outline:0,minWidth:"5rem"},attrs:r(r({},l),{},{"aria-describedby":F||null,type:d,placeholder:b||null}),domProps:{value:l.value},on:u,directives:[{name:"model",value:l.value}],ref:"input"}),I=x(Ll,{staticClass:"b-form-tags-button py-0",class:{invisible:o},style:{fontSize:"90%"},props:{disabled:o||m,variant:i},on:{click:function(){return n()}},ref:"button"},[this.normalizeSlot("add-button-text")||e]),O=this.safeId("__tag_list__"),E=x("li",{staticClass:"b-form-tags-field flex-grow-1",attrs:{role:"none","aria-live":"off","aria-controls":O},key:"tags_field"},[x("div",{staticClass:"d-flex",attrs:{role:"group"}},[P,I])]),V=x("ul",{staticClass:"b-form-tags-list list-unstyled mb-0 d-flex flex-wrap align-items-center",attrs:{id:O},key:"tags_list"},[$,E]),L=x();if(h||a||v){var R=this.feedbackAriaLive,A=this.computedJoiner,M=x();B&&(M=x(Hf,{props:{id:B,ariaLive:R,forceShow:!0},key:"tags_invalid_feedback"},[this.invalidTagText,": ",this.invalidTags.join(A)]));var H=x();D&&(H=x(Af,{props:{id:D,ariaLive:R},key:"tags_duplicate_feedback"},[this.duplicateTagText,": ",this.duplicateTags.join(A)]));var z=x();_&&(z=x(Af,{props:{id:_,ariaLive:R},key:"tags_limit_feedback"},[v])),L=x("div",{attrs:{"aria-live":"polite","aria-atomic":"true"},key:"tags_feedback"},[M,H,z])}return[V,L]}},render:function(t){var e=this.name,i=this.disabled,n=this.required,o=this.form,s=this.tags,a=this.computedInputId,l=this.hasFocus,c=this.noOuterFocus,u=r({tags:s.slice(),inputAttrs:this.computedInputAttrs,inputType:this.computedInputType,inputHandlers:this.computedInputHandlers,removeTag:this.removeTag,addTag:this.addTag,reset:this.reset,inputId:a,isInvalid:this.hasInvalidTags,invalidTags:this.invalidTags.slice(),isDuplicate:this.hasDuplicateTags,duplicateTags:this.duplicateTags.slice(),isLimitReached:this.isLimitReached,disableAddButton:this.disableAddButton},ce(this.$props,["addButtonText","addButtonVariant","disabled","duplicateTagText","form","inputClass","invalidTagText","limit","limitTagsText","noTagRemove","placeholder","required","separator","size","state","tagClass","tagPills","tagRemoveLabel","tagVariant"])),d=this.normalizeSlot(Kr,u)||this.defaultRender(u),h=t("output",{staticClass:"sr-only",attrs:{id:this.safeId("__selected_tags__"),role:"status",for:a,"aria-live":l?"polite":"off","aria-atomic":"true","aria-relevant":"additions text"}},this.tags.join(", ")),f=t("div",{staticClass:"sr-only",attrs:{id:this.safeId("__removed_tags__"),role:"status","aria-live":l?"assertive":"off","aria-atomic":"true"}},this.removedTags.length>0?"(".concat(this.tagRemovedLabel,") ").concat(this.removedTags.join(", ")):""),p=t();if(e&&!i){var m=s.length>0;p=(m?s:[""]).map((function(i){return t("input",{class:{"sr-only":!m},attrs:{type:m?"hidden":"text",value:i,required:n,name:e,form:o},key:"tag_input_".concat(i)})}))}return t("div",{staticClass:"b-form-tags form-control h-auto",class:[{focus:l&&!c&&!i,disabled:i},this.sizeFormClass,this.stateClass],attrs:{id:this.safeId(),role:"group",tabindex:i||c?null:"-1","aria-describedby":this.safeId("__selected_tags__")},on:{click:this.onClick,focusin:this.onFocusin,focusout:this.onFocusout}},[h,f,d,p])}}),wv=$e({components:{BFormTags:Tv,BTags:Tv,BFormTag:cv,BTag:cv}}),Cv=Is(he(r(r(r(r(r(r({},Vc),qf),Jf),tp),bm),{},{maxRows:Ds(Hr),noAutoShrink:Ds(kr,!1),noResize:Ds(kr,!1),rows:Ds(Hr,2),wrap:Ds(Fr,"soft")})),Vi),Sv=L({name:Vi,directives:{"b-visible":gu},mixins:[Tl,Lc,gl,Kf,Qf,ep,ym,hm,Tm],props:Cv,data:function(){return{heightInPx:null}},computed:{type:function(){return null},computedStyle:function(){var t={resize:!this.computedRows||this.noResize?"none":null};return this.computedRows||(t.height=this.heightInPx,t.overflowY="scroll"),t},computedMinRows:function(){return sa(ko(this.rows,2),2)},computedMaxRows:function(){return sa(this.computedMinRows,ko(this.maxRows,0))},computedRows:function(){return this.computedMinRows===this.computedMaxRows?this.computedMinRows:null},computedAttrs:function(){var t=this.disabled,e=this.required;return{id:this.safeId(),name:this.name||null,form:this.form||null,disabled:t,placeholder:this.placeholder||null,required:e,autocomplete:this.autocomplete||null,readonly:this.readonly||this.plaintext,rows:this.computedRows,wrap:this.wrap||null,"aria-required":this.required?"true":null,"aria-invalid":this.computedAriaInvalid}},computedListeners:function(){return r(r({},this.bvListeners),{},{input:this.onInput,change:this.onChange,blur:this.onBlur})}},watch:{localValue:function(){this.setHeight()}},mounted:function(){this.setHeight()},methods:{visibleCallback:function(t){t&&this.$nextTick(this.setHeight)},setHeight:function(){var t=this;this.$nextTick((function(){Mo((function(){t.heightInPx=t.computeHeight()}))}))},computeHeight:function(){if(this.$isServer||!Nt(this.computedRows))return null;var t=this.$el;if(!Wo(t))return null;var e=ds(t),i=xo(e.lineHeight,1),n=xo(e.borderTopWidth,0)+xo(e.borderBottomWidth,0),r=xo(e.paddingTop,0)+xo(e.paddingBottom,0),o=n+r,s=i*this.computedMinRows+o,a=cs(t,"height")||e.height;as(t,"height","auto");var l=t.scrollHeight;as(t,"height",a);var c=sa((l-r)/i,2),u=oa(sa(c,this.computedMinRows),this.computedMaxRows),d=sa(la(u*i+o),s);return this.noAutoShrink&&xo(a,0)>d?a:"".concat(d,"px")}},render:function(t){return t("textarea",{class:this.computedClass,style:this.computedStyle,directives:[{name:"b-visible",value:this.visibleCallback,modifiers:{640:!0}}],attrs:this.computedAttrs,domProps:{value:this.localValue},on:this.computedListeners,ref:"input"})}}),kv=$e({components:{BFormTextarea:Sv,BTextarea:Sv}}),xv=Vs("value",{type:Fr,defaultValue:""}),$v=xv.mixin,Bv=xv.props,Dv=xv.prop,_v=xv.event,Fv="numeric",Pv=function(t){return"00".concat(t||"").slice(-2)},Iv=function(t){t=Io(t);var e=null,i=null,n=null;if(_t.test(t)){var r=C(t.split(":").map((function(t){return ko(t,null)})),3);e=r[0],i=r[1],n=r[2]}return{hours:jt(e)?null:e,minutes:jt(i)?null:i,seconds:jt(n)?null:n,ampm:jt(e)||e<12?0:1}},Ov=Is(he(r(r(r(r({},Vc),Bv),ce(ov,["labelIncrement","labelDecrement"])),{},{ariaLabelledby:Ds(Fr),disabled:Ds(kr,!1),footerTag:Ds(Fr,"footer"),headerTag:Ds(Fr,"header"),hidden:Ds(kr,!1),hideHeader:Ds(kr,!1),hour12:Ds(kr,null),labelAm:Ds(Fr,"AM"),labelAmpm:Ds(Fr,"AM/PM"),labelHours:Ds(Fr,"Hours"),labelMinutes:Ds(Fr,"Minutes"),labelNoTimeSelected:Ds(Fr,"No time selected"),labelPm:Ds(Fr,"PM"),labelSeconds:Ds(Fr,"Seconds"),labelSelected:Ds(Fr,"Selected time"),locale:Ds(Er),minutesStep:Ds(Hr,1),readonly:Ds(kr,!1),secondsStep:Ds(Hr,1),showSeconds:Ds(kr,!1)})),In),Ev=L({name:In,mixins:[Lc,$v,So],props:Ov,data:function(){var t=Iv(this[Dv]||"");return{modelHours:t.hours,modelMinutes:t.minutes,modelSeconds:t.seconds,modelAmpm:t.ampm,isLive:!1}},computed:{computedHMS:function(){return function(t){var e=t.hours,i=t.minutes,n=t.seconds,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Nt(e)||Nt(i)||r&&Nt(n)?"":[e,i,r?n:0].map(Pv).join(":")}({hours:this.modelHours,minutes:this.modelMinutes,seconds:this.modelSeconds},this.showSeconds)},resolvedOptions:function(){var t=go(this.locale).filter(pe),e={hour:Fv,minute:Fv,second:Fv};jt(this.hour12)||(e.hour12=!!this.hour12);var i=new Intl.DateTimeFormat(t,e).resolvedOptions(),n=i.hour12||!1,r=i.hourCycle||(n?"h12":"h23");return{locale:i.locale,hour12:n,hourCycle:r}},computedLocale:function(){return this.resolvedOptions.locale},computedLang:function(){return(this.computedLocale||"").replace(/-u-.*$/,"")},computedRTL:function(){return Ec(this.computedLang)},computedHourCycle:function(){return this.resolvedOptions.hourCycle},is12Hour:function(){return!!this.resolvedOptions.hour12},context:function(){return{locale:this.computedLocale,isRTL:this.computedRTL,hourCycle:this.computedHourCycle,hour12:this.is12Hour,hours:this.modelHours,minutes:this.modelMinutes,seconds:this.showSeconds?this.modelSeconds:0,value:this.computedHMS,formatted:this.formattedTimeString}},valueId:function(){return this.safeId()||null},computedAriaLabelledby:function(){return[this.ariaLabelledby,this.valueId].filter(pe).join(" ")||null},timeFormatter:function(){var t={hour12:this.is12Hour,hourCycle:this.computedHourCycle,hour:Fv,minute:Fv,timeZone:"UTC"};return this.showSeconds&&(t.second=Fv),wc(this.computedLocale,t)},numberFormatter:function(){return new Intl.NumberFormat(this.computedLocale,{style:"decimal",minimumIntegerDigits:2,minimumFractionDigits:0,maximumFractionDigits:0,notation:"standard"}).format},formattedTimeString:function(){var t=this.modelHours,e=this.modelMinutes,i=this.showSeconds&&this.modelSeconds||0;return this.computedHMS?this.timeFormatter(bc(Date.UTC(0,0,1,t,e,i))):this.labelNoTimeSelected||" "},spinScopedSlots:function(){var t=this.$createElement;return{increment:function(e){var i=e.hasFocus;return t(Aa,{props:{scale:i?1.5:1.25},attrs:{"aria-hidden":"true"}})},decrement:function(e){var i=e.hasFocus;return t(Aa,{props:{flipV:!0,scale:i?1.5:1.25},attrs:{"aria-hidden":"true"}})}}}},watch:(ef={},c(ef,Dv,(function(t,e){if(t!==e&&!ll(Iv(t),Iv(this.computedHMS))){var i=Iv(t),n=i.hours,r=i.minutes,o=i.seconds,s=i.ampm;this.modelHours=n,this.modelMinutes=r,this.modelSeconds=o,this.modelAmpm=s}})),c(ef,"computedHMS",(function(t,e){t!==e&&this.$emit(_v,t)})),c(ef,"context",(function(t,e){ll(t,e)||this.$emit(Nn,t)})),c(ef,"modelAmpm",(function(t,e){var i=this;if(t!==e){var n=Nt(this.modelHours)?0:this.modelHours;this.$nextTick((function(){0===t&&n>11?i.modelHours=n-12:1===t&&n<12&&(i.modelHours=n+12)}))}})),c(ef,"modelHours",(function(t,e){t!==e&&(this.modelAmpm=t>11?1:0)})),ef),created:function(){var t=this;this.$nextTick((function(){t.$emit(Nn,t.context)}))},mounted:function(){this.setLive(!0)},activated:function(){this.setLive(!0)},deactivated:function(){this.setLive(!1)},beforeDestroy:function(){this.setLive(!1)},methods:{focus:function(){this.disabled||vs(this.$refs.spinners[0])},blur:function(){if(!this.disabled){var t=No();Jo(this.$el,t)&&gs(t)}},formatHours:function(t){var e=this.computedHourCycle;return t=0===(t=this.is12Hour&&t>12?t-12:t)&&"h12"===e?12:0===t&&"h24"===e?24:12===t&&"h11"===e?0:t,this.numberFormatter(t)},formatMinutes:function(t){return this.numberFormatter(t)},formatSeconds:function(t){return this.numberFormatter(t)},formatAmpm:function(t){return 0===t?this.labelAm:1===t?this.labelPm:""},setHours:function(t){this.modelHours=t},setMinutes:function(t){this.modelMinutes=t},setSeconds:function(t){this.modelSeconds=t},setAmpm:function(t){this.modelAmpm=t},onSpinLeftRight:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.type,i=t.keyCode;if(!this.disabled&&"keydown"===e&&(i===nl||i===rl)){Hs(t);var n=this.$refs.spinners||[],r=n.map((function(t){return!!t.hasFocus})).indexOf(!0);r=(r+=i===nl?-1:1)>=n.length?0:r<0?n.length-1:r,vs(n[r])}},setLive:function(t){var e=this;t?this.$nextTick((function(){Mo((function(){e.isLive=!0}))})):this.isLive=!1}},render:function(t){var e=this;if(this.hidden)return t();var i=this.disabled,n=this.readonly,o=this.computedLocale,s=this.computedAriaLabelledby,a=this.labelIncrement,l=this.labelDecrement,u=this.valueId,d=this.focus,h=[],f=function(s,d,f){var p=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},m=e.safeId("_spinbutton_".concat(d,"_"))||null;return h.push(m),t(sv,c({class:f,props:r({id:m,placeholder:"--",vertical:!0,required:!0,disabled:i,readonly:n,locale:o,labelIncrement:a,labelDecrement:l,wrap:!0,ariaControls:u,min:0},p),scopedSlots:e.spinScopedSlots,on:{change:s},key:d,ref:"spinners"},E,!0))},p=function(){return t("div",{staticClass:"d-flex flex-column",class:{"text-muted":i||n},attrs:{"aria-hidden":"true"}},[t(Ma,{props:{shiftV:4,scale:.5}}),t(Ma,{props:{shiftV:-4,scale:.5}})])},m=[];m.push(f(this.setHours,"hours","b-time-hours",{value:this.modelHours,max:23,step:1,formatterFn:this.formatHours,ariaLabel:this.labelHours})),m.push(p()),m.push(f(this.setMinutes,"minutes","b-time-minutes",{value:this.modelMinutes,max:59,step:this.minutesStep||1,formatterFn:this.formatMinutes,ariaLabel:this.labelMinutes})),this.showSeconds&&(m.push(p()),m.push(f(this.setSeconds,"seconds","b-time-seconds",{value:this.modelSeconds,max:59,step:this.secondsStep||1,formatterFn:this.formatSeconds,ariaLabel:this.labelSeconds}))),this.isLive&&this.is12Hour&&m.push(f(this.setAmpm,"ampm","b-time-ampm",{value:this.modelAmpm,max:1,formatterFn:this.formatAmpm,ariaLabel:this.labelAmpm,required:!1})),m=t("div",{staticClass:"d-flex align-items-center justify-content-center mx-auto",attrs:{role:"group",tabindex:i||n?null:"-1","aria-labelledby":s},on:{keydown:this.onSpinLeftRight,click:function(t){t.target===t.currentTarget&&d()}}},m);var v=t("output",{staticClass:"form-control form-control-sm text-center",class:{disabled:i||n},attrs:{id:u,role:"status",for:h.filter(pe).join(" ")||null,tabindex:i?null:"-1","aria-live":this.isLive?"polite":"off","aria-atomic":"true"},on:{click:d,focus:d}},[t("bdi",this.formattedTimeString),this.computedHMS?t("span",{staticClass:"sr-only"}," (".concat(this.labelSelected,") ")):""]),g=t(this.headerTag,{staticClass:"b-time-header",class:{"sr-only":this.hideHeader}},[v]),b=this.normalizeSlot(),y=b?t(this.footerTag,{staticClass:"b-time-footer"},b):t();return t("div",{staticClass:"b-time d-inline-flex flex-column text-center",attrs:{role:"group",lang:this.computedLang||null,"aria-labelledby":s||null,"aria-disabled":i?"true":null,"aria-readonly":n&&!i?"true":null}},[g,m,y])}}),Vv=Vs("value",{type:Fr,defaultValue:""}),Lv=Vv.mixin,Rv=Vv.props,Av=Vv.prop,Mv=Vv.event,Hv=ue(Ov,["hidden","id","value"]),zv=ue(Pp,["formattedValue","id","lang","rtl","value"]),Nv=Is(he(r(r(r(r(r({},Vc),Rv),Hv),zv),{},{closeButtonVariant:Ds(Fr,"outline-secondary"),labelCloseButton:Ds(Fr,"Close"),labelNowButton:Ds(Fr,"Select now"),labelResetButton:Ds(Fr,"Reset"),noCloseButton:Ds(kr,!1),nowButton:Ds(kr,!1),nowButtonVariant:Ds(Fr,"outline-primary"),resetButton:Ds(kr,!1),resetButtonVariant:Ds(Fr,"outline-danger"),resetValue:Ds(Ar)})),Li),jv=L({name:Li,mixins:[Lc,Lv],props:Nv,data:function(){return{localHMS:this[Av]||"",localLocale:null,isRTL:!1,formattedValue:"",isVisible:!1}},computed:{computedLang:function(){return(this.localLocale||"").replace(/-u-.*$/i,"")||null}},watch:(nf={},c(nf,Av,(function(t){this.localHMS=t||""})),c(nf,"localHMS",(function(t){this.isVisible&&this.$emit(Mv,t||"")})),nf),methods:{focus:function(){this.disabled||vs(this.$refs.control)},blur:function(){this.disabled||gs(this.$refs.control)},setAndClose:function(t){var e=this;this.localHMS=t,this.$nextTick((function(){e.$refs.control.hide(!0)}))},onInput:function(t){this.localHMS!==t&&(this.localHMS=t)},onContext:function(t){var e=t.isRTL,i=t.locale,n=t.value,r=t.formatted;this.isRTL=e,this.localLocale=i,this.formattedValue=r,this.localHMS=n||"",this.$emit(Nn,t)},onNowButton:function(){var t=new Date,e=[t.getHours(),t.getMinutes(),this.showSeconds?t.getSeconds():0].map((function(t){return"00".concat(t||"").slice(-2)})).join(":");this.setAndClose(e)},onResetButton:function(){this.setAndClose(this.resetValue)},onCloseButton:function(){this.$refs.control.hide(!0)},onShow:function(){this.isVisible=!0},onShown:function(){var t=this;this.$nextTick((function(){vs(t.$refs.time),t.$emit(pr)}))},onHidden:function(){this.isVisible=!1,this.$emit(tr)},defaultButtonFn:function(t){var e=t.isHovered,i=t.hasFocus;return this.$createElement(e||i?za:Ha,{attrs:{"aria-hidden":"true"}})}},render:function(t){var e=this.localHMS,i=this.disabled,n=this.readonly,o=this.$props,s=jt(this.placeholder)?this.labelNoTimeSelected:this.placeholder,a=[];if(this.nowButton){var l=this.labelNowButton;a.push(t(Ll,{props:{size:"sm",disabled:i||n,variant:this.nowButtonVariant},attrs:{"aria-label":l||null},on:{click:this.onNowButton},key:"now-btn"},l))}if(this.resetButton){a.length>0&&a.push(t("span"," "));var u=this.labelResetButton;a.push(t(Ll,{props:{size:"sm",disabled:i||n,variant:this.resetButtonVariant},attrs:{"aria-label":u||null},on:{click:this.onResetButton},key:"reset-btn"},u))}if(!this.noCloseButton){a.length>0&&a.push(t("span"," "));var d=this.labelCloseButton;a.push(t(Ll,{props:{size:"sm",disabled:i,variant:this.closeButtonVariant},attrs:{"aria-label":d||null},on:{click:this.onCloseButton},key:"close-btn"},d))}a.length>0&&(a=[t("div",{staticClass:"b-form-date-controls d-flex flex-wrap",class:{"justify-content-between":a.length>1,"justify-content-end":a.length<2}},a)]);var h=t(Ev,{staticClass:"b-form-time-control",props:r(r({},Fs(Hv,o)),{},{value:e,hidden:!this.isVisible}),on:{input:this.onInput,context:this.onContext},ref:"time"},a);return t(Ip,{staticClass:"b-form-timepicker",props:r(r({},Fs(zv,o)),{},{id:this.safeId(),value:e,formattedValue:e?this.formattedValue:"",placeholder:s,rtl:this.isRTL,lang:this.computedLang}),on:{show:this.onShow,shown:this.onShown,hidden:this.onHidden},scopedSlots:c({},Yr,this.$scopedSlots["button-content"]||this.defaultButtonFn),ref:"control"},[h])}}),Gv=$e({components:{BFormTimepicker:jv,BTimepicker:jv}}),Wv=$e({components:{BImg:ru,BImgLazy:wu}}),Uv=Is({tag:Ds(Fr,"div")},Wi),Yv=L({name:Wi,functional:!0,props:Uv,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{staticClass:"input-group-text"}),r)}}),qv=Is({append:Ds(kr,!1),id:Ds(Fr),isText:Ds(kr,!1),tag:Ds(Fr,"div")},Ni),Kv=L({name:Ni,functional:!0,props:qv,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.append;return t(i.tag,I(n,{class:{"input-group-append":o,"input-group-prepend":!o},attrs:{id:i.id}}),i.isText?[t(Yv,r)]:r)}}),Xv=Is(ue(qv,["append"]),ji),Zv=L({name:ji,functional:!0,props:Xv,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(Kv,I(n,{props:r(r({},i),{},{append:!0})}),o)}}),Jv=Is(ue(qv,["append"]),Gi),Qv=L({name:Gi,functional:!0,props:Jv,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(Kv,I(n,{props:r(r({},i),{},{append:!1})}),o)}}),tg=Is({append:Ds(Fr),appendHtml:Ds(Fr),id:Ds(Fr),prepend:Ds(Fr),prependHtml:Ds(Fr),size:Ds(Fr),tag:Ds(Fr,"div")},zi),eg=L({name:zi,functional:!0,props:tg,render:function(t,e){var i=e.props,n=e.data,r=e.slots,o=e.scopedSlots,s=i.prepend,a=i.prependHtml,l=i.append,u=i.appendHtml,d=i.size,h=o||{},f=r(),p={},m=t(),v=wo(so,h,f);(v||s||a)&&(m=t(Qv,[v?Co(so,p,h,f):t(Yv,{domProps:Zl(a,s)})]));var g=t(),b=wo(Gr,h,f);return(b||l||u)&&(g=t(Zv,[b?Co(Gr,p,h,f):t(Yv,{domProps:Zl(u,l)})])),t(i.tag,I(n,{staticClass:"input-group",class:c({},"input-group-".concat(d),d),attrs:{id:i.id||null,role:"group"}}),[m,Co(Kr,p,h,f),g])}}),ig=$e({components:{BInputGroup:eg,BInputGroupAddon:Kv,BInputGroupPrepend:Qv,BInputGroupAppend:Zv,BInputGroupText:Yv}}),ng=Is({fluid:Ds(Rr,!1),tag:Ds(Fr,"div")},oi),rg=L({name:oi,functional:!0,props:ng,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.fluid;return t(i.tag,I(n,{class:c({container:!(o||""===o),"container-fluid":!0===o||""===o},"container-".concat(o),o&&!0!==o)}),r)}}),og=Is({bgVariant:Ds(Fr),borderVariant:Ds(Fr),containerFluid:Ds(Rr,!1),fluid:Ds(kr,!1),header:Ds(Fr),headerHtml:Ds(Fr),headerLevel:Ds(Hr,3),headerTag:Ds(Fr,"h1"),lead:Ds(Fr),leadHtml:Ds(Fr),leadTag:Ds(Fr,"p"),tag:Ds(Fr,"div"),textVariant:Ds(Fr)},Ui),sg=L({name:Ui,functional:!0,props:og,render:function(t,e){var i,n=e.props,r=e.data,o=e.slots,s=e.scopedSlots,a=n.header,l=n.headerHtml,u=n.lead,d=n.leadHtml,h=n.textVariant,f=n.bgVariant,p=n.borderVariant,m=s||{},v=o(),g={},b=t(),y=wo(to,m,v);if(y||a||l){var T=n.headerLevel;b=t(n.headerTag,{class:c({},"display-".concat(T),T),domProps:y?{}:Zl(l,a)},Co(to,g,m,v))}var w=t(),C=wo(io,m,v);(C||u||d)&&(w=t(n.leadTag,{staticClass:"lead",domProps:C?{}:Zl(d,u)},Co(io,g,m,v)));var S=[b,w,Co(Kr,g,m,v)];return n.fluid&&(S=[t(rg,{props:{fluid:n.containerFluid}},S)]),t(n.tag,I(r,{staticClass:"jumbotron",class:(i={"jumbotron-fluid":n.fluid},c(i,"text-".concat(h),h),c(i,"bg-".concat(f),f),c(i,"border-".concat(p),p),c(i,"border",p),i)}),S)}}),ag=$e({components:{BJumbotron:sg}}),lg=["start","end","center"],cg=bs((function(t,e){return(e=Oo(Io(e)))?Eo(["row-cols",t,e].filter(pe).join("-")):null})),ug=bs((function(t){return Eo(t.replace("cols",""))})),dg=[],hg={name:mn,functional:!0,get props(){var t;return delete this.props,this.props=(t=ks().reduce((function(t,e){return t[Bs(e,"cols")]=Ds(Hr),t}),ie(null)),dg=se(t),Is(he(r(r({},t),{},{alignContent:Ds(Fr,null,(function(t){return vo(go(lg,"between","around","stretch"),t)})),alignH:Ds(Fr,null,(function(t){return vo(go(lg,"between","around"),t)})),alignV:Ds(Fr,null,(function(t){return vo(go(lg,"baseline","stretch"),t)})),noGutters:Ds(kr,!1),tag:Ds(Fr,"div")})),mn)),this.props},render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.alignV,a=n.alignH,l=n.alignContent,u=[];return dg.forEach((function(t){var e=cg(ug(t),n[t]);e&&u.push(e)})),u.push((c(i={"no-gutters":n.noGutters},"align-items-".concat(s),s),c(i,"justify-content-".concat(a),a),c(i,"align-content-".concat(l),l),i)),t(n.tag,I(r,{staticClass:"row",class:u}),o)}},fg=$e({components:{BContainer:rg,BRow:hg,BCol:sm,BFormRow:Gf}}),pg=$e({components:{BLink:xl}}),mg=Is({flush:Ds(kr,!1),horizontal:Ds(Rr,!1),tag:Ds(Fr,"div")},qi),vg=L({name:qi,functional:!0,props:mg,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=""===i.horizontal||i.horizontal;o=!i.flush&&o;var s={staticClass:"list-group",class:c({"list-group-flush":i.flush,"list-group-horizontal":!0===o},"list-group-horizontal-".concat(o),Ut(o))};return t(i.tag,I(n,s),r)}}),gg=["a","router-link","button","b-link"],bg=ue(kl,["event","routerTag"]);delete bg.href.default,delete bg.to.default;var yg=Is(he(r(r({},bg),{},{action:Ds(kr,!1),button:Ds(kr,!1),tag:Ds(Fr,"div"),variant:Ds(Fr)})),Ki),Tg=L({name:Ki,functional:!0,props:yg,render:function(t,e){var i,n=e.props,r=e.data,o=e.children,s=n.button,a=n.variant,l=n.active,u=n.disabled,d=Ca(n),h=s?"button":d?xl:n.tag,f=!!(n.action||d||s||vo(gg,n.tag)),p={},m={};return jo(h,"button")?(r.attrs&&r.attrs.type||(p.type="button"),n.disabled&&(p.disabled=!0)):m=Fs(bg,n),t(h,I(r,{attrs:p,props:m,staticClass:"list-group-item",class:(i={},c(i,"list-group-item-".concat(a),a),c(i,"list-group-item-action",f),c(i,"active",l),c(i,"disabled",u),i)}),o)}}),wg=$e({components:{BListGroup:vg,BListGroupItem:Tg}}),Cg=Is({right:Ds(kr,!1),tag:Ds(Fr,"div"),verticalAlign:Ds(Fr,"top")},Zi),Sg=L({name:Zi,functional:!0,props:Cg,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.verticalAlign,s="top"===o?"start":"bottom"===o?"end":o;return t(i.tag,I(n,{staticClass:"media-aside",class:c({"media-aside-right":i.right},"align-self-".concat(s),s)}),r)}}),kg=Is({tag:Ds(Fr,"div")},Ji),xg=L({name:Ji,functional:!0,props:kg,render:function(t,e){var i=e.props,n=e.data,r=e.children;return t(i.tag,I(n,{staticClass:"media-body"}),r)}}),$g=Is({noBody:Ds(kr,!1),rightAlign:Ds(kr,!1),tag:Ds(Fr,"div"),verticalAlign:Ds(Fr,"top")},Xi),Bg=L({name:Xi,functional:!0,props:$g,render:function(t,e){var i=e.props,n=e.data,r=e.slots,o=e.scopedSlots,s=e.children,a=i.noBody,l=i.rightAlign,c=i.verticalAlign,u=a?s:[];if(!a){var d={},h=r(),f=o||{};u.push(t(xg,Co(Kr,d,f,h)));var p=Co("aside",d,f,h);p&&u[l?"push":"unshift"](t(Sg,{props:{right:l,verticalAlign:c}},p))}return t(i.tag,I(n,{staticClass:"media"}),u)}}),Dg=$e({components:{BMedia:Bg,BMediaAside:Sg,BMediaBody:xg}}),_g="$_documentListeners",Fg=L({created:function(){this[_g]={}},beforeDestroy:function(){var t=this;se(this[_g]||{}).forEach((function(e){t[_g][e].forEach((function(i){t.listenOffDocument(e,i)}))})),this[_g]=null},methods:{registerDocumentListener:function(t,e){this[_g]&&(this[_g][t]=this[_g][t]||[],vo(this[_g][t],e)||this[_g][t].push(e))},unregisterDocumentListener:function(t,e){this[_g]&&this[_g][t]&&(this[_g][t]=this[_g][t].filter((function(t){return t!==e})))},listenDocument:function(t,e,i){t?this.listenOnDocument(e,i):this.listenOffDocument(e,i)},listenOnDocument:function(t,e){Y&&(Rs(document,t,e,wr),this.registerDocumentListener(t,e))},listenOffDocument:function(t,e){Y&&As(document,t,e,wr),this.unregisterDocumentListener(t,e)}}}),Pg="$_windowListeners",Ig=L({created:function(){this[Pg]={}},beforeDestroy:function(){var t=this;se(this[Pg]||{}).forEach((function(e){t[Pg][e].forEach((function(i){t.listenOffWindow(e,i)}))})),this[Pg]=null},methods:{registerWindowListener:function(t,e){this[Pg]&&(this[Pg][t]=this[Pg][t]||[],vo(this[Pg][t],e)||this[Pg][t].push(e))},unregisterWindowListener:function(t,e){this[Pg]&&this[Pg][t]&&(this[Pg][t]=this[Pg][t].filter((function(t){return t!==e})))},listenWindow:function(t,e,i){t?this.listenOnWindow(e,i):this.listenOffWindow(e,i)},listenOnWindow:function(t,e){Y&&(Rs(window,t,e,wr),this.registerWindowListener(t,e))},listenOffWindow:function(t,e){Y&&As(window,t,e,wr),this.unregisterWindowListener(t,e)}}}),Og=L({computed:{bvParent:function(){return this.$parent||this.$root===this&&this.$options.bvParent}}}),Eg=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return t&&t.$options._scopeId||e},Vg=L({mixins:[Og],computed:{scopedStyleAttrs:function(){var t=Eg(this.bvParent);return t?c({},t,""):{}}}}),Lg=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t.$root?t.$root.$options.bvEventRoot||t.$root:null;return new e(r(r({},i),{},{parent:t,bvParent:t,bvEventRoot:n}))},Rg=L({abstract:!0,name:"BVTransporterTarget",props:{nodes:Ds(Pr)},data:function(t){return{updatedNodes:t.nodes}},destroyed:function(){var t;(t=this.$el)&&t.parentNode&&t.parentNode.removeChild(t)},render:function(t){var e=this.updatedNodes,i=Gt(e)?e({}):e;return(i=go(i).filter(pe))&&i.length>0&&!i[0].text?i[0]:t()}}),Ag={container:Ds([HTMLElement,Fr],"body"),disabled:Ds(kr,!1),tag:Ds(Fr,"div")},Mg=L({name:Ln,mixins:[So],props:Ag,watch:{disabled:{immediate:!0,handler:function(t){t?this.unmountTarget():this.$nextTick(this.mountTarget)}}},created:function(){this.$_defaultFn=null,this.$_target=null},beforeMount:function(){this.mountTarget()},updated:function(){this.updateTarget()},beforeDestroy:function(){this.unmountTarget(),this.$_defaultFn=null},methods:{getContainer:function(){if(Y){var t=this.container;return Ut(t)?Ko(t):t}return null},mountTarget:function(){if(!this.$_target){var t=this.getContainer();if(t){var e=document.createElement("div");t.appendChild(e),this.$_target=Lg(this,Rg,{el:e,propsData:{nodes:go(this.normalizeSlot())}})}}},updateTarget:function(){if(Y&&this.$_target){var t=this.$scopedSlots.default;this.disabled||(t&&this.$_defaultFn!==t?this.$_target.updatedNodes=t:t||(this.$_target.updatedNodes=this.$slots.default)),this.$_defaultFn=t}},unmountTarget:function(){this.$_target&&this.$_target.$destroy(),this.$_target=null}},render:function(t){if(this.disabled){var e=go(this.normalizeSlot()).filter(pe);if(e.length>0&&!e[0].text)return e[0]}return t()}}),Hg=O?L({name:Ln,mixins:[So],props:Ag,render:function(t){if(this.disabled){var e=go(this.normalizeSlot()).filter(pe);if(e.length>0)return e[0]}return t(i.default.Teleport,{to:this.container},this.normalizeSlot())}}):Mg,BvModalEvent=function(t){u(BvModalEvent,t);var e=y(BvModalEvent);function BvModalEvent(t){var i,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s(this,BvModalEvent),i=e.call(this,t,n),ne(g(i),{trigger:{enumerable:!0,configurable:!1,writable:!1}}),i}return l(BvModalEvent,null,[{key:"Defaults",get:function(){return r(r({},w(d(BvModalEvent),"Defaults",this)),{},{trigger:null})}}]),BvModalEvent}(BvEvent),zg=new(L({data:function(){return{modals:[],baseZIndex:null,scrollbarWidth:null,isBodyOverflowing:!1}},computed:{modalCount:function(){return this.modals.length},modalsAreOpen:function(){return this.modalCount>0}},watch:{modalCount:function(t,e){Y&&(this.getScrollbarWidth(),t>0&&0===e?(this.checkScrollbar(),this.setScrollbar(),ts(document.body,"modal-open")):0===t&&e>0&&(this.resetScrollbar(),es(document.body,"modal-open")),ns(document.body,"data-modal-open-count",String(t)))},modals:function(t){var e=this;this.checkScrollbar(),Mo((function(){e.updateModals(t||[])}))}},methods:{registerModal:function(t){t&&-1===this.modals.indexOf(t)&&this.modals.push(t)},unregisterModal:function(t){var e=this.modals.indexOf(t);e>-1&&(this.modals.splice(e,1),t._isBeingDestroyed||t._isDestroyed||this.resetModal(t))},getBaseZIndex:function(){if(Y&&Nt(this.baseZIndex)){var t=document.createElement("div");ts(t,"modal-backdrop"),ts(t,"d-none"),as(t,"display","none"),document.body.appendChild(t),this.baseZIndex=ko(ds(t).zIndex,1040),document.body.removeChild(t)}return this.baseZIndex||1040},getScrollbarWidth:function(){if(Y&&Nt(this.scrollbarWidth)){var t=document.createElement("div");ts(t,"modal-scrollbar-measure"),document.body.appendChild(t),this.scrollbarWidth=us(t).width-t.clientWidth,document.body.removeChild(t)}return this.scrollbarWidth||0},updateModals:function(t){var e=this,i=this.getBaseZIndex(),n=this.getScrollbarWidth();t.forEach((function(t,r){t.zIndex=i+r,t.scrollbarWidth=n,t.isTop=r===e.modals.length-1,t.isBodyOverflowing=e.isBodyOverflowing}))},resetModal:function(t){t&&(t.zIndex=this.getBaseZIndex(),t.isTop=!0,t.isBodyOverflowing=!1)},checkScrollbar:function(){var t=us(document.body),e=t.left,i=t.right;this.isBodyOverflowing=e+i<window.innerWidth},setScrollbar:function(){var t=document.body;if(t._paddingChangedForModal=t._paddingChangedForModal||[],t._marginChangedForModal=t._marginChangedForModal||[],this.isBodyOverflowing){var e=this.scrollbarWidth;qo(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top").forEach((function(i){var n=cs(i,"paddingRight")||"";ns(i,"data-padding-right",n),as(i,"paddingRight","".concat(xo(ds(i).paddingRight,0)+e,"px")),t._paddingChangedForModal.push(i)})),qo(".sticky-top").forEach((function(i){var n=cs(i,"marginRight")||"";ns(i,"data-margin-right",n),as(i,"marginRight","".concat(xo(ds(i).marginRight,0)-e,"px")),t._marginChangedForModal.push(i)})),qo(".navbar-toggler").forEach((function(i){var n=cs(i,"marginRight")||"";ns(i,"data-margin-right",n),as(i,"marginRight","".concat(xo(ds(i).marginRight,0)+e,"px")),t._marginChangedForModal.push(i)}));var i=cs(t,"paddingRight")||"";ns(t,"data-padding-right",i),as(t,"paddingRight","".concat(xo(ds(t).paddingRight,0)+e,"px"))}},resetScrollbar:function(){var t=document.body;t._paddingChangedForModal&&t._paddingChangedForModal.forEach((function(t){ss(t,"data-padding-right")&&(as(t,"paddingRight",os(t,"data-padding-right")||""),rs(t,"data-padding-right"))})),t._marginChangedForModal&&t._marginChangedForModal.forEach((function(t){ss(t,"data-margin-right")&&(as(t,"marginRight",os(t,"data-margin-right")||""),rs(t,"data-margin-right"))})),t._paddingChangedForModal=null,t._marginChangedForModal=null,ss(t,"data-padding-right")&&(as(t,"paddingRight",os(t,"data-padding-right")||""),rs(t,"data-padding-right"))}}})),Ng=Vs("visible",{type:kr,defaultValue:!1,event:Mn}),jg=Ng.mixin,Gg=Ng.props,Wg=Ng.prop,Ug=Ng.event,Yg="FORCE",qg="cancel",Kg="headerclose",Xg="ok",Zg=[qg,Kg,Xg],Jg={subtree:!0,childList:!0,characterData:!0,attributes:!0,attributeFilter:["style","class"]},Qg=Is(he(r(r(r({},Vc),Gg),{},{ariaLabel:Ds(Fr),autoFocusButton:Ds(Fr,null,(function(t){return jt(t)||vo(Zg,t)})),bodyBgVariant:Ds(Fr),bodyClass:Ds(Or),bodyTextVariant:Ds(Fr),busy:Ds(kr,!1),buttonSize:Ds(Fr),cancelDisabled:Ds(kr,!1),cancelTitle:Ds(Fr,"Cancel"),cancelTitleHtml:Ds(Fr),cancelVariant:Ds(Fr,"secondary"),centered:Ds(kr,!1),contentClass:Ds(Or),dialogClass:Ds(Or),footerBgVariant:Ds(Fr),footerBorderVariant:Ds(Fr),footerClass:Ds(Or),footerTag:Ds(Fr,"footer"),footerTextVariant:Ds(Fr),headerBgVariant:Ds(Fr),headerBorderVariant:Ds(Fr),headerClass:Ds(Or),headerCloseContent:Ds(Fr,"&times;"),headerCloseLabel:Ds(Fr,"Close"),headerCloseVariant:Ds(Fr),headerTag:Ds(Fr,"header"),headerTextVariant:Ds(Fr),hideBackdrop:Ds(kr,!1),hideFooter:Ds(kr,!1),hideHeader:Ds(kr,!1),hideHeaderClose:Ds(kr,!1),ignoreEnforceFocusSelector:Ds(Er),lazy:Ds(kr,!1),modalClass:Ds(Or),noCloseOnBackdrop:Ds(kr,!1),noCloseOnEsc:Ds(kr,!1),noEnforceFocus:Ds(kr,!1),noFade:Ds(kr,!1),noStacking:Ds(kr,!1),okDisabled:Ds(kr,!1),okOnly:Ds(kr,!1),okTitle:Ds(Fr,"OK"),okTitleHtml:Ds(Fr),okVariant:Ds(Fr,"primary"),returnFocus:Ds([HTMLElement,Dr,Fr]),scrollable:Ds(kr,!1),size:Ds(Fr,"md"),static:Ds(kr,!1),title:Ds(Fr),titleClass:Ds(Or),titleHtml:Ds(Fr),titleSrOnly:Ds(kr,!1),titleTag:Ds(Fr,"h5")})),Qi),tb=L({name:Qi,mixins:[pl,Lc,jg,Fg,gl,Ig,So,Vg],inheritAttrs:!1,props:Qg,data:function(){return{isHidden:!0,isVisible:!1,isTransitioning:!1,isShow:!1,isBlock:!1,isOpening:!1,isClosing:!1,ignoreBackdropClick:!1,isModalOverflowing:!1,scrollbarWidth:0,zIndex:zg.getBaseZIndex(),isTop:!0,isBodyOverflowing:!1}},computed:{modalId:function(){return this.safeId()},modalOuterId:function(){return this.safeId("__BV_modal_outer_")},modalHeaderId:function(){return this.safeId("__BV_modal_header_")},modalBodyId:function(){return this.safeId("__BV_modal_body_")},modalTitleId:function(){return this.safeId("__BV_modal_title_")},modalContentId:function(){return this.safeId("__BV_modal_content_")},modalFooterId:function(){return this.safeId("__BV_modal_footer_")},modalBackdropId:function(){return this.safeId("__BV_modal_backdrop_")},modalClasses:function(){return[{fade:!this.noFade,show:this.isShow},this.modalClass]},modalStyles:function(){var t="".concat(this.scrollbarWidth,"px");return{paddingLeft:!this.isBodyOverflowing&&this.isModalOverflowing?t:"",paddingRight:this.isBodyOverflowing&&!this.isModalOverflowing?t:"",display:this.isBlock?"block":"none"}},dialogClasses:function(){var t;return[(t={},c(t,"modal-".concat(this.size),this.size),c(t,"modal-dialog-centered",this.centered),c(t,"modal-dialog-scrollable",this.scrollable),t),this.dialogClass]},headerClasses:function(){var t;return[(t={},c(t,"bg-".concat(this.headerBgVariant),this.headerBgVariant),c(t,"text-".concat(this.headerTextVariant),this.headerTextVariant),c(t,"border-".concat(this.headerBorderVariant),this.headerBorderVariant),t),this.headerClass]},titleClasses:function(){return[{"sr-only":this.titleSrOnly},this.titleClass]},bodyClasses:function(){var t;return[(t={},c(t,"bg-".concat(this.bodyBgVariant),this.bodyBgVariant),c(t,"text-".concat(this.bodyTextVariant),this.bodyTextVariant),t),this.bodyClass]},footerClasses:function(){var t;return[(t={},c(t,"bg-".concat(this.footerBgVariant),this.footerBgVariant),c(t,"text-".concat(this.footerTextVariant),this.footerTextVariant),c(t,"border-".concat(this.footerBorderVariant),this.footerBorderVariant),t),this.footerClass]},modalOuterStyle:function(){return{position:"absolute",zIndex:this.zIndex}},slotScope:function(){return{cancel:this.onCancel,close:this.onClose,hide:this.hide,ok:this.onOk,visible:this.isVisible}},computeIgnoreEnforceFocusSelector:function(){return go(this.ignoreEnforceFocusSelector).filter(pe).join(",").trim()},computedAttrs:function(){return r(r(r({},this.static?{}:this.scopedStyleAttrs),this.bvAttrs),{},{id:this.modalOuterId})},computedModalAttrs:function(){var t=this.isVisible,e=this.ariaLabel;return{id:this.modalId,role:"dialog","aria-hidden":t?null:"true","aria-modal":t?"true":null,"aria-label":e,"aria-labelledby":this.hideHeader||e||!(this.hasNormalizedSlot(oo)||this.titleHtml||this.title)?null:this.modalTitleId,"aria-describedby":this.modalBodyId}}},watch:c({},Wg,(function(t,e){t!==e&&this[t?"show":"hide"]()})),created:function(){this.$_observer=null,this.$_returnFocus=this.returnFocus||null},mounted:function(){this.zIndex=zg.getBaseZIndex(),this.listenOnRoot(js(Qi,fr),this.showHandler),this.listenOnRoot(js(Qi,er),this.hideHandler),this.listenOnRoot(js(Qi,vr),this.toggleHandler),this.listenOnRoot(Ns(Qi,fr),this.modalListener),!0===this[Wg]&&this.$nextTick(this.show)},beforeDestroy:function(){zg.unregisterModal(this),this.setObserver(!1),this.isVisible&&(this.isVisible=!1,this.isShow=!1,this.isTransitioning=!1)},methods:{setObserver:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,t&&(this.$_observer=Fu(this.$refs.content,this.checkModalOverflow.bind(this),Jg))},updateModel:function(t){t!==this[Wg]&&this.$emit(Ug,t)},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvModalEvent(t,r(r({cancelable:!1,target:this.$refs.modal||this.$el||null,relatedTarget:null,trigger:null},e),{},{vueTarget:this,componentId:this.modalId}))},show:function(){if(!this.isVisible&&!this.isOpening)if(this.isClosing)this.$once(tr,this.show);else{this.isOpening=!0,this.$_returnFocus=this.$_returnFocus||this.getActiveElement();var t=this.buildEvent(fr,{cancelable:!0});if(this.emitEvent(t),t.defaultPrevented||this.isVisible)return this.isOpening=!1,void this.updateModel(!1);this.doShow()}},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.isVisible&&!this.isClosing){this.isClosing=!0;var e=this.buildEvent(er,{cancelable:t!==Yg,trigger:t||null});if(t===Xg?this.$emit("ok",e):t===qg?this.$emit(An,e):t===Kg&&this.$emit(zn,e),this.emitEvent(e),e.defaultPrevented||!this.isVisible)return this.isClosing=!1,void this.updateModel(!0);this.setObserver(!1),this.isVisible=!1,this.updateModel(!1)}},toggle:function(t){t&&(this.$_returnFocus=t),this.isVisible?this.hide("toggle"):this.show()},getActiveElement:function(){var t=No(Y?[document.body]:[]);return t&&t.focus?t:null},doShow:function(){var t=this;zg.modalsAreOpen&&this.noStacking?this.listenOnRootOnce(Ns(Qi,tr),this.doShow):(zg.registerModal(this),this.isHidden=!1,this.$nextTick((function(){t.isVisible=!0,t.isOpening=!1,t.updateModel(!0),t.$nextTick((function(){t.setObserver(!0)}))})))},onBeforeEnter:function(){this.isTransitioning=!0,this.setResizeEvent(!0)},onEnter:function(){var t=this;this.isBlock=!0,Mo((function(){Mo((function(){t.isShow=!0}))}))},onAfterEnter:function(){var t=this;this.checkModalOverflow(),this.isTransitioning=!1,Mo((function(){t.emitEvent(t.buildEvent(pr)),t.setEnforceFocus(!0),t.$nextTick((function(){t.focusFirst()}))}))},onBeforeLeave:function(){this.isTransitioning=!0,this.setResizeEvent(!1),this.setEnforceFocus(!1)},onLeave:function(){this.isShow=!1},onAfterLeave:function(){var t=this;this.isBlock=!1,this.isTransitioning=!1,this.isModalOverflowing=!1,this.isHidden=!0,this.$nextTick((function(){t.isClosing=!1,zg.unregisterModal(t),t.returnFocusTo(),t.emitEvent(t.buildEvent(tr))}))},emitEvent:function(t){var e=t.type;this.emitOnRoot(Ns(Qi,e),t,t.componentId),this.$emit(e,t)},onDialogMousedown:function(){var t=this,e=this.$refs.modal;Rs(e,"mouseup",(function i(n){As(e,"mouseup",i,wr),n.target===e&&(t.ignoreBackdropClick=!0)}),wr)},onClickOut:function(t){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:this.isVisible&&!this.noCloseOnBackdrop&&Jo(document.body,t.target)&&(Jo(this.$refs.content,t.target)||this.hide("backdrop"))},onOk:function(){this.hide(Xg)},onCancel:function(){this.hide(qg)},onClose:function(){this.hide(Kg)},onEsc:function(t){27===t.keyCode&&this.isVisible&&!this.noCloseOnEsc&&this.hide("esc")},focusHandler:function(t){var e=this.$refs.content,i=t.target;if(!(this.noEnforceFocus||!this.isTop||!this.isVisible||!e||document===i||Jo(e,i)||this.computeIgnoreEnforceFocusSelector&&Zo(this.computeIgnoreEnforceFocusSelector,i,!0))){var n=ms(this.$refs.content),r=this.$refs["bottom-trap"],o=this.$refs["top-trap"];if(r&&i===r){if(vs(n[0]))return}else if(o&&i===o&&vs(n[n.length-1]))return;vs(e,{preventScroll:!0})}},setEnforceFocus:function(t){this.listenDocument(t,"focusin",this.focusHandler)},setResizeEvent:function(t){this.listenWindow(t,"resize",this.checkModalOverflow),this.listenWindow(t,"orientationchange",this.checkModalOverflow)},showHandler:function(t,e){t===this.modalId&&(this.$_returnFocus=e||this.getActiveElement(),this.show())},hideHandler:function(t){t===this.modalId&&this.hide("event")},toggleHandler:function(t,e){t===this.modalId&&this.toggle(e)},modalListener:function(t){this.noStacking&&t.vueTarget!==this&&this.hide()},focusFirst:function(){var t=this;Y&&Mo((function(){var e=t.$refs.modal,i=t.$refs.content,n=t.getActiveElement();if(e&&i&&(!n||!Jo(i,n))){var r=t.$refs["ok-button"],o=t.$refs["cancel-button"],s=t.$refs["close-button"],a=t.autoFocusButton,l=a===Xg&&r?r.$el||r:a===qg&&o?o.$el||o:a===Kg&&s?s.$el||s:i;vs(l),l===i&&t.$nextTick((function(){e.scrollTop=0}))}}))},returnFocusTo:function(){var t=this.returnFocus||this.$_returnFocus||null;this.$_returnFocus=null,this.$nextTick((function(){(t=Ut(t)?Ko(t):t)&&(t=t.$el||t,vs(t))}))},checkModalOverflow:function(){if(this.isVisible){var t=this.$refs.modal;this.isModalOverflowing=t.scrollHeight>document.documentElement.clientHeight}},makeModal:function(t){var e=t();if(!this.hideHeader){var i=this.normalizeSlot("modal-header",this.slotScope);if(!i){var n=t();this.hideHeaderClose||(n=t(Ws,{props:{content:this.headerCloseContent,disabled:this.isTransitioning,ariaLabel:this.headerCloseLabel,textVariant:this.headerCloseVariant||this.headerTextVariant},on:{click:this.onClose},ref:"close-button"},[this.normalizeSlot("modal-header-close")])),i=[t(this.titleTag,{staticClass:"modal-title",class:this.titleClasses,attrs:{id:this.modalTitleId},domProps:this.hasNormalizedSlot(oo)?{}:Zl(this.titleHtml,this.title)},this.normalizeSlot(oo,this.slotScope)),n]}e=t(this.headerTag,{staticClass:"modal-header",class:this.headerClasses,attrs:{id:this.modalHeaderId},ref:"header"},[i])}var r=t("div",{staticClass:"modal-body",class:this.bodyClasses,attrs:{id:this.modalBodyId},ref:"body"},this.normalizeSlot(Kr,this.slotScope)),o=t();if(!this.hideFooter){var s=this.normalizeSlot("modal-footer",this.slotScope);if(!s){var a=t();this.okOnly||(a=t(Ll,{props:{variant:this.cancelVariant,size:this.buttonSize,disabled:this.cancelDisabled||this.busy||this.isTransitioning},domProps:this.hasNormalizedSlot(no)?{}:Zl(this.cancelTitleHtml,this.cancelTitle),on:{click:this.onCancel},ref:"cancel-button"},this.normalizeSlot(no))),s=[a,t(Ll,{props:{variant:this.okVariant,size:this.buttonSize,disabled:this.okDisabled||this.busy||this.isTransitioning},domProps:this.hasNormalizedSlot(ro)?{}:Zl(this.okTitleHtml,this.okTitle),on:{click:this.onOk},ref:"ok-button"},this.normalizeSlot(ro))]}o=t(this.footerTag,{staticClass:"modal-footer",class:this.footerClasses,attrs:{id:this.modalFooterId},ref:"footer"},[s])}var l=t("div",{staticClass:"modal-content",class:this.contentClass,attrs:{id:this.modalContentId,tabindex:"-1"},ref:"content"},[e,r,o]),c=t(),u=t();this.isVisible&&!this.noEnforceFocus&&(c=t("span",{attrs:{tabindex:"0"},ref:"top-trap"}),u=t("span",{attrs:{tabindex:"0"},ref:"bottom-trap"}));var d=t("div",{staticClass:"modal-dialog",class:this.dialogClasses,on:{mousedown:this.onDialogMousedown},ref:"dialog"},[c,l,u]),h=t("div",{staticClass:"modal",class:this.modalClasses,style:this.modalStyles,attrs:this.computedModalAttrs,on:{keydown:this.onEsc,click:this.onClickOut},directives:[{name:"show",value:this.isVisible}],ref:"modal"},[d]);h=t("transition",{props:{enterClass:"",enterToClass:"",enterActiveClass:"",leaveClass:"",leaveActiveClass:"",leaveToClass:""},on:{beforeEnter:this.onBeforeEnter,enter:this.onEnter,afterEnter:this.onAfterEnter,beforeLeave:this.onBeforeLeave,leave:this.onLeave,afterLeave:this.onAfterLeave}},[h]);var f=t();return!this.hideBackdrop&&this.isVisible&&(f=t("div",{staticClass:"modal-backdrop",attrs:{id:this.modalBackdropId}},this.normalizeSlot("modal-backdrop"))),f=t(Ks,{props:{noFade:this.noFade}},[f]),t("div",{style:this.modalOuterStyle,attrs:this.computedAttrs,key:"modal-outer-".concat(this._uid)},[h,f])}},render:function(t){return this.static?this.lazy&&this.isHidden?t():this.makeModal(t):this.isHidden?t():t(Hg,[this.makeModal(t)])}}),eb=js(Qi,fr),ib="__bv_modal_directive__",nb=function(t){var e=t.modifiers,i=void 0===e?{}:e,n=t.arg,r=t.value;return Ut(r)?r:Ut(n)?n:se(i).reverse()[0]},rb=function(t){return t&&Xo(t,".dropdown-menu > li, li.nav-item")&&Ko("a, button",t)||t},ob=function(t){t&&"BUTTON"!==t.tagName&&(ss(t,"role")||ns(t,"role","button"),"A"===t.tagName||ss(t,"tabindex")||ns(t,"tabindex","0"))},sb=function(t){var e=t[ib]||{},i=e.trigger,n=e.handler;i&&n&&(As(i,"click",n,Tr),As(i,"keydown",n,Tr),As(t,"click",n,Tr),As(t,"keydown",n,Tr)),delete t[ib]},ab=function(t,e,i){var n=t[ib]||{},r=nb(e),o=rb(t);r===n.target&&o===n.trigger||(sb(t),function(t,e,i){var n=nb(e),r=rb(t);if(n&&r){var o=function(t){var r=t.currentTarget;if(!Uo(r)){var o=t.type,s=t.keyCode;"click"!==o&&("keydown"!==o||s!==el&&s!==ol)||ml(ld(i,e)).$emit(eb,n,r)}};t[ib]={handler:o,target:n,trigger:r},ob(r),Rs(r,"click",o,Tr),"BUTTON"!==r.tagName&&"button"===os(r,"role")&&Rs(r,"keydown",o,Tr)}}(t,e,i)),ob(o)},lb={inserted:ab,updated:function(){},componentUpdated:ab,unbind:sb},cb="$bvModal",ub=["id"].concat(S(se(ue(Qg,["busy","lazy","noStacking","static","visible"])))),db=function(){},hb={msgBoxContent:"default",title:"modal-title",okTitle:"modal-ok",cancelTitle:"modal-cancel"},fb=function(t){return ub.reduce((function(e,i){return zt(t[i])||(e[i]=t[i]),e}),{})},pb=$e({plugins:{plugin:function(t){var e=t.extend({name:"BMsgBox",extends:tb,mixins:[Og],destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},mounted:function(){var t=this,e=function(){t.$nextTick((function(){Mo((function(){t.$destroy()}))}))};this.bvParent.$once(br,e),this.$once(tr,e),this.$router&&this.$route&&this.$once(gr,this.$watch("$router",e)),this.show()}}),i=function(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:db;if(!Te(cb)&&!we(cb)){var o=Lg(t,e,{propsData:r(r(r({},fb(ws(Qi))),{},{hideHeaderClose:!0,hideHeader:!(i.title||i.titleHtml)},ue(i,se(hb))),{},{lazy:!1,busy:!1,visible:!1,noStacking:!1,noEnforceFocus:!1})});return se(hb).forEach((function(t){zt(i[t])||(o.$slots[hb[t]]=go(i[t]))})),new Promise((function(t,e){var i=!1;o.$once(br,(function(){i||e(new Error("BootstrapVue MsgBox destroyed before resolve"))})),o.$on(er,(function(e){if(!e.defaultPrevented){var r=n(e);e.defaultPrevented||(i=!0,t(r))}}));var r=document.createElement("div");document.body.appendChild(r),o.$mount(r)}))}},n=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(e&&!we(cb)&&!Te(cb)&&Gt(o))return i(t,r(r({},fb(n)),{},{msgBoxContent:e}),o)},o=function(){function t(e){s(this,t),ee(this,{_vm:e,_root:ml(e)}),ne(this,{_vm:{enumerable:!0,configurable:!1,writable:!1},_root:{enumerable:!0,configurable:!1,writable:!1}})}return l(t,[{key:"show",value:function(t){if(t&&this._root){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];(e=this._root).$emit.apply(e,[js(Qi,"show"),t].concat(n))}}},{key:"hide",value:function(t){if(t&&this._root){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];(e=this._root).$emit.apply(e,[js(Qi,"hide"),t].concat(n))}}},{key:"msgBoxOk",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r(r({},e),{},{okOnly:!0,okDisabled:!1,hideFooter:!1,msgBoxContent:t});return n(this._vm,t,i,(function(){return!0}))}},{key:"msgBoxConfirm",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r(r({},e),{},{okOnly:!1,okDisabled:!1,cancelDisabled:!1,hideFooter:!1});return n(this._vm,t,i,(function(t){var e=t.trigger;return"ok"===e||"cancel"!==e&&null}))}}]),t}();t.mixin({beforeCreate:function(){this._bv__modal=new o(this)}}),ae(t.prototype,cb)||re(t.prototype,cb,{get:function(){return this&&this._bv__modal||ye('"'.concat(cb,'" must be accessed from a Vue instance "this" context.'),Qi),this._bv__modal}})}}}),mb=$e({components:{BModal:tb},directives:{VBModal:lb},plugins:{BVModalPlugin:pb}}),vb=Is({align:Ds(Fr),cardHeader:Ds(kr,!1),fill:Ds(kr,!1),justified:Ds(kr,!1),pills:Ds(kr,!1),small:Ds(kr,!1),tabs:Ds(kr,!1),tag:Ds(Fr,"ul"),vertical:Ds(kr,!1)},tn),gb=L({name:tn,functional:!0,props:vb,render:function(t,e){var i,n,r=e.props,o=e.data,s=e.children,a=r.tabs,l=r.pills,u=r.vertical,d=r.align,h=r.cardHeader;return t(r.tag,I(o,{staticClass:"nav",class:(i={"nav-tabs":a,"nav-pills":l&&!a,"card-header-tabs":!u&&h&&a,"card-header-pills":!u&&h&&l&&!a,"flex-column":u,"nav-fill":!u&&r.fill,"nav-justified":!u&&r.justified},c(i,(n=d,"justify-content-".concat(n="left"===n?"start":"right"===n?"end":n)),!u&&d),c(i,"small",r.small),i)}),s)}}),bb=ue(kl,["event","routerTag"]),yb=Is(he(r(r({},bb),{},{linkAttrs:Ds(Dr,{}),linkClasses:Ds(Or)})),an),Tb=L({name:an,functional:!0,props:yb,render:function(t,e){var i=e.props,n=e.data,r=e.listeners,o=e.children;return t("li",I(ue(n,["on"]),{staticClass:"nav-item"}),[t(xl,{staticClass:"nav-link",class:i.linkClasses,attrs:i.linkAttrs,props:Fs(bb,i),on:r},o)])}}),wb=L({name:"BNavText",functional:!0,props:{},render:function(t,e){var i=e.data,n=e.children;return t("li",I(i,{staticClass:"navbar-text"}),n)}}),Cb=ue(Tf,["inline"]),Sb=Is(he(r(r({},Cb),{},{formClass:Ds(Or)})),sn),kb=L({name:sn,functional:!0,props:Sb,render:function(t,e){var i=e.props,n=e.data,o=e.children,s=e.listeners,a=t(wf,{class:i.formClass,props:r(r({},Fs(Cb,i)),{},{inline:!0}),attrs:n.attrs,on:s},o);return t("li",I(ue(n,["attrs","on"]),{staticClass:"form-inline"}),[a])}}),xb=Is(he(r(r({},Vc),ce(cf,[].concat(S(se(af)),["html","lazy","menuClass","noCaret","role","text","toggleClass"])))),ln),$b=L({name:ln,mixins:[Lc,lf,So],props:xb,computed:{toggleId:function(){return this.safeId("_BV_toggle_")},menuId:function(){return this.safeId("_BV_toggle_menu_")},dropdownClasses:function(){return[this.directionClass,this.boundaryClass,{show:this.visible}]},menuClasses:function(){return[this.menuClass,{"dropdown-menu-right":this.right,show:this.visible}]},toggleClasses:function(){return[this.toggleClass,{"dropdown-toggle-no-caret":this.noCaret}]}},render:function(t){var e=this.toggleId,i=this.menuId,n=this.visible,r=this.hide,o=t(xl,{staticClass:"nav-link dropdown-toggle",class:this.toggleClasses,props:{href:"#".concat(this.id||""),disabled:this.disabled},attrs:{id:e,role:"button","aria-haspopup":"true","aria-expanded":n?"true":"false","aria-controls":i},on:{mousedown:this.onMousedown,click:this.toggle,keydown:this.toggle},ref:"toggle"},[this.normalizeSlot([Yr,"text"])||t("span",{domProps:Zl(this.html,this.text)})]),s=t("ul",{staticClass:"dropdown-menu",class:this.menuClasses,attrs:{tabindex:"-1","aria-labelledby":e,id:i},on:{keydown:this.onKeydown},ref:"menu"},!this.lazy||n?this.normalizeSlot(Kr,{hide:r}):[t()]);return t("li",{staticClass:"nav-item b-nav-dropdown dropdown",class:this.dropdownClasses,attrs:{id:this.safeId()}},[o,s])}}),Bb=$e({components:{BNav:gb,BNavItem:Tb,BNavText:wb,BNavForm:kb,BNavItemDropdown:$b,BNavItemDd:$b,BNavDropdown:$b,BNavDd:$b},plugins:{DropdownPlugin:Df}}),Db=Is({fixed:Ds(Fr),print:Ds(kr,!1),sticky:Ds(kr,!1),tag:Ds(Fr,"nav"),toggleable:Ds(Rr,!1),type:Ds(Fr,"light"),variant:Ds(Fr)},en),_b=L({name:en,mixins:[So],provide:function(){var t=this;return{getBvNavbar:function(){return t}}},props:Db,computed:{breakpointClass:function(){var t=this.toggleable,e=Cs()[0],i=null;return t&&Ut(t)&&t!==e?i="navbar-expand-".concat(t):!1===t&&(i="navbar-expand"),i}},render:function(t){var e,i=this.tag,n=this.type,r=this.variant,o=this.fixed;return t(i,{staticClass:"navbar",class:[(e={"d-print":this.print,"sticky-top":this.sticky},c(e,"navbar-".concat(n),n),c(e,"bg-".concat(r),r),c(e,"fixed-".concat(o),o),e),this.breakpointClass],attrs:{role:jo(i,"nav")?null:"navigation"}},[this.normalizeSlot()])}}),Fb=Is(ce(vb,["tag","fill","justified","align","small"]),rn),Pb=L({name:rn,functional:!0,props:Fb,render:function(t,e){var i,n,r=e.props,o=e.data,s=e.children,a=r.align;return t(r.tag,I(o,{staticClass:"navbar-nav",class:(i={"nav-fill":r.fill,"nav-justified":r.justified},c(i,(n=a,"justify-content-".concat(n="left"===n?"start":"right"===n?"end":n)),a),c(i,"small",r.small),i)}),s)}}),Ib=ue(kl,["event","routerTag"]);Ib.href.default=void 0,Ib.to.default=void 0;var Ob,Eb,Vb,Lb,Rb=Is(he(r(r({},Ib),{},{tag:Ds(Fr,"div")})),nn),Ab=L({name:nn,functional:!0,props:Rb,render:function(t,e){var i=e.props,n=e.data,r=e.children,o=i.to||i.href;return t(o?xl:i.tag,I(n,{staticClass:"navbar-brand",props:o?Fs(Ib,i):{}}),r)}}),Mb="navbar-toggler",Hb=Ns(ri,"state"),zb=Ns(ri,"sync-state"),Nb=Is({disabled:Ds(kr,!1),label:Ds(Fr,"Toggle navigation"),target:Ds(Er,void 0,!0)},on),jb=L({name:on,directives:{VBToggle:Id},mixins:[gl,So],props:Nb,data:function(){return{toggleState:!1}},created:function(){this.listenOnRoot(Hb,this.handleStateEvent),this.listenOnRoot(zb,this.handleStateEvent)},methods:{onClick:function(t){this.disabled||this.$emit(Hn,t)},handleStateEvent:function(t,e){t===this.target&&(this.toggleState=e)}},render:function(t){var e=this.disabled;return t("button",{staticClass:Mb,class:{disabled:e},directives:[{name:"VBToggle",value:this.target}],attrs:{type:"button",disabled:e,"aria-label":this.label},on:{click:this.onClick}},[this.normalizeSlot(Kr,{expanded:this.toggleState})||t("span",{staticClass:"".concat(Mb,"-icon")})])}}),Gb=$e({components:{BNavbar:_b,BNavbarNav:Pb,BNavbarBrand:Ab,BNavbarToggle:jb,BNavToggle:jb},plugins:{NavPlugin:Bb,CollapsePlugin:Ed,DropdownPlugin:Df}}),Wb=Is({label:Ds(Fr),role:Ds(Fr,"status"),small:Ds(kr,!1),tag:Ds(Fr,"span"),type:Ds(Fr,"border"),variant:Ds(Fr)},Cn),Ub=L({name:Cn,functional:!0,props:Wb,render:function(t,e){var i,n=e.props,r=e.data,o=e.slots,s=e.scopedSlots,a=o(),l=Co(eo,{},s||{},a)||n.label;return l&&(l=t("span",{staticClass:"sr-only"},l)),t(n.tag,I(r,{attrs:{role:l?n.role||"status":null,"aria-hidden":l?null:"true"},class:(i={},c(i,"spinner-".concat(n.type),n.type),c(i,"spinner-".concat(n.type,"-sm"),n.small),c(i,"text-".concat(n.variant),n.variant),i)}),[l||t()])}}),Yb={top:0,left:0,bottom:0,right:0},qb=Is({bgColor:Ds(Fr),blur:Ds(Fr,"2px"),fixed:Ds(kr,!1),noCenter:Ds(kr,!1),noFade:Ds(kr,!1),noWrap:Ds(kr,!1),opacity:Ds(Hr,.85,(function(t){var e=xo(t,0);return e>=0&&e<=1})),overlayTag:Ds(Fr,"div"),rounded:Ds(Rr,!1),show:Ds(kr,!1),spinnerSmall:Ds(kr,!1),spinnerType:Ds(Fr,"border"),spinnerVariant:Ds(Fr),variant:Ds(Fr,"light"),wrapTag:Ds(Fr,"div"),zIndex:Ds(Hr,10)},cn),Kb=L({name:cn,mixins:[So],props:qb,computed:{computedRounded:function(){var t=this.rounded;return!0===t||""===t?"rounded":t?"rounded-".concat(t):""},computedVariant:function(){var t=this.variant;return t&&!this.bgColor?"bg-".concat(t):""},slotScope:function(){return{spinnerType:this.spinnerType||null,spinnerVariant:this.spinnerVariant||null,spinnerSmall:this.spinnerSmall}}},methods:{defaultOverlayFn:function(t){var e=t.spinnerType,i=t.spinnerVariant,n=t.spinnerSmall;return this.$createElement(Ub,{props:{type:e,variant:i,small:n}})}},render:function(t){var e=this,i=this.show,n=this.fixed,o=this.noFade,s=this.noWrap,a=this.slotScope,l=t();if(i){var c=t("div",{staticClass:"position-absolute",class:[this.computedVariant,this.computedRounded],style:r(r({},Yb),{},{opacity:this.opacity,backgroundColor:this.bgColor||null,backdropFilter:this.blur?"blur(".concat(this.blur,")"):null})}),u=t("div",{staticClass:"position-absolute",style:this.noCenter?r({},Yb):{top:"50%",left:"50%",transform:"translateX(-50%) translateY(-50%)"}},[this.normalizeSlot("overlay",a)||this.defaultOverlayFn(a)]);l=t(this.overlayTag,{staticClass:"b-overlay",class:{"position-absolute":!s||s&&!n,"position-fixed":s&&n},style:r(r({},Yb),{},{zIndex:this.zIndex||10}),on:{click:function(t){return e.$emit(Hn,t)}},key:"overlay"},[c,u])}return l=t(Ks,{props:{noFade:o,appear:!0},on:{"after-enter":function(){return e.$emit(pr)},"after-leave":function(){return e.$emit(tr)}}},[l]),s?l:t(this.wrapTag,{staticClass:"b-overlay-wrap position-relative",attrs:{"aria-busy":i?"true":null}},s?[l]:[this.normalizeSlot(),l])}}),Xb=$e({components:{BOverlay:Kb}}),Zb=Vs("value",{type:Lr,defaultValue:null,validator:function(t){return!(!Nt(t)&&ko(t,0)<1)||(ye('"v-model" value must be a number greater than "0"',un),!1)}}),Jb=Zb.mixin,Qb=Zb.props,ty=Zb.prop,ey=Zb.event,iy=function(t){var e=ko(t)||1;return e<1?5:e},ny=function(t,e){var i=ko(t)||1;return i>e?e:i<1?1:i},ry=function(t){if(t.keyCode===ol)return Hs(t,{immediatePropagation:!0}),t.currentTarget.click(),!1},oy=Is(he(r(r({},Qb),{},{align:Ds(Fr,"left"),ariaLabel:Ds(Fr,"Pagination"),disabled:Ds(kr,!1),ellipsisClass:Ds(Or),ellipsisText:Ds(Fr,"…"),firstClass:Ds(Or),firstNumber:Ds(kr,!1),firstText:Ds(Fr,"«"),hideEllipsis:Ds(kr,!1),hideGotoEndButtons:Ds(kr,!1),labelFirstPage:Ds(Fr,"Go to first page"),labelLastPage:Ds(Fr,"Go to last page"),labelNextPage:Ds(Fr,"Go to next page"),labelPage:Ds(Mr,"Go to page"),labelPrevPage:Ds(Fr,"Go to previous page"),lastClass:Ds(Or),lastNumber:Ds(kr,!1),lastText:Ds(Fr,"»"),limit:Ds(Hr,5,(function(t){return!(ko(t,0)<1)||(ye('Prop "limit" must be a number greater than "0"',un),!1)})),nextClass:Ds(Or),nextText:Ds(Fr,"›"),pageClass:Ds(Or),pills:Ds(kr,!1),prevClass:Ds(Or),prevText:Ds(Fr,"‹"),size:Ds(Fr)})),"pagination"),sy=L({mixins:[Jb,So],props:oy,data:function(){var t=ko(this[ty],0);return{currentPage:t=t>0?t:-1,localNumberOfPages:1,localLimit:5}},computed:{btnSize:function(){var t=this.size;return t?"pagination-".concat(t):""},alignment:function(){var t=this.align;return"center"===t?"justify-content-center":"end"===t||"right"===t?"justify-content-end":"fill"===t?"text-center":""},styleClass:function(){return this.pills?"b-pagination-pills":""},computedCurrentPage:function(){return ny(this.currentPage,this.localNumberOfPages)},paginationParams:function(){var t=this.localLimit,e=this.localNumberOfPages,i=this.computedCurrentPage,n=this.hideEllipsis,r=this.firstNumber,o=this.lastNumber,s=!1,a=!1,l=t,c=1;e<=t?l=e:i<t-1&&t>3?(n&&!o||(a=!0,l=t-(r?0:1)),l=oa(l,t)):e-i+2<t&&t>3?(n&&!r||(s=!0,l=t-(o?0:1)),c=e-l+1):(t>3&&(l=t-(n?0:2),s=!(n&&!r),a=!(n&&!o)),c=i-ca(l/2)),c<1?(c=1,s=!1):c>e-l&&(c=e-l+1,a=!1),s&&r&&c<4&&(l+=2,c=1,s=!1);var u=c+l-1;return a&&o&&u>e-3&&(l+=u===e-2?2:3,a=!1),t<=3&&(r&&1===c?l=oa(l+1,e,t+1):o&&e===c+l-1&&(c=sa(c-1,1),l=oa(e-c+1,e,t+1))),{showFirstDots:s,showLastDots:a,numberOfLinks:l=oa(l,e-c+1),startNumber:c}},pageList:function(){var t=this.paginationParams,e=t.numberOfLinks,i=t.startNumber,n=this.computedCurrentPage,r=function(t,e){return bo(e,(function(e,i){return{number:t+i,classes:null}}))}(i,e);if(r.length>3){var o=n-i,s="bv-d-xs-down-none";if(0===o)for(var a=3;a<r.length;a++)r[a].classes=s;else if(o===r.length-1)for(var l=0;l<r.length-3;l++)r[l].classes=s;else{for(var c=0;c<o-1;c++)r[c].classes=s;for(var u=r.length-1;u>o+1;u--)r[u].classes=s}}return r}},watch:(Ob={},c(Ob,ty,(function(t,e){t!==e&&(this.currentPage=ny(t,this.localNumberOfPages))})),c(Ob,"currentPage",(function(t,e){t!==e&&this.$emit(ey,t>0?t:null)})),c(Ob,"limit",(function(t,e){t!==e&&(this.localLimit=iy(t))})),Ob),created:function(){var t=this;this.localLimit=iy(this.limit),this.$nextTick((function(){t.currentPage=t.currentPage>t.localNumberOfPages?t.localNumberOfPages:t.currentPage}))},methods:{handleKeyNav:function(t){var e=t.keyCode,i=t.shiftKey;this.isNav||(e===nl||e===sl?(Hs(t,{propagation:!1}),i?this.focusFirst():this.focusPrev()):e!==rl&&e!==Qa||(Hs(t,{propagation:!1}),i?this.focusLast():this.focusNext()))},getButtons:function(){return qo("button.page-link, a.page-link",this.$el).filter((function(t){return Wo(t)}))},focusCurrent:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().find((function(e){return ko(os(e,"aria-posinset"),0)===t.computedCurrentPage}));vs(e)||t.focusFirst()}))},focusFirst:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().find((function(t){return!Uo(t)}));vs(e)}))},focusLast:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().reverse().find((function(t){return!Uo(t)}));vs(e)}))},focusPrev:function(){var t=this;this.$nextTick((function(){var e=t.getButtons(),i=e.indexOf(No());i>0&&!Uo(e[i-1])&&vs(e[i-1])}))},focusNext:function(){var t=this;this.$nextTick((function(){var e=t.getButtons(),i=e.indexOf(No());i<e.length-1&&!Uo(e[i+1])&&vs(e[i+1])}))}},render:function(t){var e=this,i=va(this),n=i.disabled,r=i.labelPage,o=i.ariaLabel,s=i.isNav,a=i.localNumberOfPages,l=i.computedCurrentPage,c=this.pageList.map((function(t){return t.number})),u=this.paginationParams,d=u.showFirstDots,h=u.showLastDots,f="fill"===this.align,p=[],m=function(t){return t===l},v=this.currentPage<1,g=function(i,r,o,l,c,u,d){var h=n||m(u)||v||i<1||i>a,p=i<1?1:i>a?a:i,g={disabled:h,page:p,index:p-1},b=e.normalizeSlot(o,g)||Io(l)||t(),y=t(h?"span":s?xl:"button",{staticClass:"page-link",class:{"flex-grow-1":!s&&!h&&f},props:h||!s?{}:e.linkProps(i),attrs:{role:s?null:"menuitem",type:s||h?null:"button",tabindex:h||s?null:"-1","aria-label":r,"aria-controls":va(e).ariaControls||null,"aria-disabled":h?"true":null},on:h?{}:{"!click":function(t){e.onClick(t,i)},keydown:ry}},[b]);return t("li",{key:d,staticClass:"page-item",class:[{disabled:h,"flex-fill":f,"d-flex":f&&!s&&!h},c],attrs:{role:s?null:"presentation","aria-hidden":h?"true":null}},[y])},b=function(i){return t("li",{staticClass:"page-item",class:["disabled","bv-d-xs-down-none",f?"flex-fill":"",e.ellipsisClass],attrs:{role:"separator"},key:"ellipsis-".concat(i?"last":"first")},[t("span",{staticClass:"page-link"},[e.normalizeSlot("ellipsis-text")||Io(e.ellipsisText)||t()])])},y=function(i,o){var l=i.number,c=m(l)&&!v,u=n?null:c||v&&0===o?"0":"-1",d={role:s?null:"menuitemradio",type:s||n?null:"button","aria-disabled":n?"true":null,"aria-controls":va(e).ariaControls||null,"aria-label":Es(r)?r(l):"".concat(Gt(r)?r():r," ").concat(l),"aria-checked":s?null:c?"true":"false","aria-current":s&&c?"page":null,"aria-posinset":s?null:l,"aria-setsize":s?null:a,tabindex:s?null:u},h=Io(e.makePage(l)),p={page:l,index:l-1,content:h,active:c,disabled:n},g=t(n?"span":s?xl:"button",{props:n||!s?{}:e.linkProps(l),staticClass:"page-link",class:{"flex-grow-1":!s&&!n&&f},attrs:d,on:n?{}:{"!click":function(t){e.onClick(t,l)},keydown:ry}},[e.normalizeSlot("page",p)||h]);return t("li",{staticClass:"page-item",class:[{disabled:n,active:c,"flex-fill":f,"d-flex":f&&!s&&!n},i.classes,e.pageClass],attrs:{role:s?null:"presentation"},key:"page-".concat(l)},[g])},T=t();this.firstNumber||this.hideGotoEndButtons||(T=g(1,this.labelFirstPage,"first-text",this.firstText,this.firstClass,1,"pagination-goto-first")),p.push(T),p.push(g(l-1,this.labelPrevPage,"prev-text",this.prevText,this.prevClass,1,"pagination-goto-prev")),p.push(this.firstNumber&&1!==c[0]?y({number:1},0):t()),p.push(d?b(!1):t()),this.pageList.forEach((function(t,i){var n=d&&e.firstNumber&&1!==c[0]?1:0;p.push(y(t,i+n))})),p.push(h?b(!0):t()),p.push(this.lastNumber&&c[c.length-1]!==a?y({number:a},-1):t()),p.push(g(l+1,this.labelNextPage,"next-text",this.nextText,this.nextClass,a,"pagination-goto-next"));var w=t();this.lastNumber||this.hideGotoEndButtons||(w=g(a,this.labelLastPage,"last-text",this.lastText,this.lastClass,a,"pagination-goto-last")),p.push(w);var C=t("ul",{staticClass:"pagination",class:["b-pagination",this.btnSize,this.alignment,this.styleClass],attrs:{role:s?null:"menubar","aria-disabled":n?"true":"false","aria-label":s?null:o||null},on:s?{}:{keydown:this.handleKeyNav},ref:"ul"},p);return s?t("nav",{attrs:{"aria-disabled":n?"true":null,"aria-hidden":n?"true":"false","aria-label":s&&o||null}},[C]):C}}),ay=function(t){return sa(ko(t)||20,1)},ly=function(t){return sa(ko(t)||0,0)},cy=Is(he(r(r({},oy),{},{ariaControls:Ds(Fr),perPage:Ds(Hr,20),totalRows:Ds(Hr,0)})),un),uy=L({name:un,mixins:[sy],props:cy,computed:{numberOfPages:function(){var t=la(ly(this.totalRows)/ay(this.perPage));return t<1?1:t},pageSizeNumberOfPages:function(){return{perPage:ay(this.perPage),totalRows:ly(this.totalRows),numberOfPages:this.numberOfPages}}},watch:{pageSizeNumberOfPages:function(t,e){jt(e)||(t.perPage!==e.perPage&&t.totalRows===e.totalRows||t.numberOfPages!==e.numberOfPages&&this.currentPage>t.numberOfPages)&&(this.currentPage=1),this.localNumberOfPages=t.numberOfPages}},created:function(){var t=this;this.localNumberOfPages=this.numberOfPages;var e=ko(this[ty],0);e>0?this.currentPage=e:this.$nextTick((function(){t.currentPage=0}))},methods:{onClick:function(t,e){var i=this;if(e!==this.currentPage){var n=t.target,r=new BvEvent(lr,{cancelable:!0,vueTarget:this,target:n});this.$emit(r.type,r,e),r.defaultPrevented||(this.currentPage=e,this.$emit(Mn,this.currentPage),this.$nextTick((function(){Wo(n)&&i.$el.contains(n)?vs(n):i.focusCurrent()})))}},makePage:function(t){return t},linkProps:function(){return{}}}}),dy=$e({components:{BPagination:uy}}),hy=ue(kl,["event","routerTag"]),fy=Is(he(r(r(r({},oy),hy),{},{baseUrl:Ds(Fr,"/"),linkGen:Ds($r),noPageDetect:Ds(kr,!1),numberOfPages:Ds(Hr,1,(function(t){return!(ko(t,0)<1)||(ye('Prop "number-of-pages" must be a number greater than "0"',dn),!1)})),pageGen:Ds($r),pages:Ds(Sr),useRouter:Ds(kr,!1)})),dn),py=L({name:dn,mixins:[sy],props:fy,computed:{isNav:function(){return!0},computedValue:function(){var t=ko(this.value,0);return t<1?null:t}},watch:{numberOfPages:function(){var t=this;this.$nextTick((function(){t.setNumberOfPages()}))},pages:function(){var t=this;this.$nextTick((function(){t.setNumberOfPages()}))}},created:function(){this.setNumberOfPages()},mounted:function(){var t=this;this.$router&&this.$watch("$route",(function(){t.$nextTick((function(){Mo((function(){t.guessCurrentPage()}))}))}))},methods:{setNumberOfPages:function(){var t,e=this;Kt(this.pages)&&this.pages.length>0?this.localNumberOfPages=this.pages.length:this.localNumberOfPages=(t=this.numberOfPages,sa(ko(t,0),1)),this.$nextTick((function(){e.guessCurrentPage()}))},onClick:function(t,e){var i=this;if(e!==this.currentPage){var n=t.currentTarget||t.target,r=new BvEvent(lr,{cancelable:!0,vueTarget:this,target:n});this.$emit(r.type,r,e),r.defaultPrevented||(Mo((function(){i.currentPage=e,i.$emit(Mn,e)})),this.$nextTick((function(){gs(n)})))}},getPageInfo:function(t){if(!Kt(this.pages)||0===this.pages.length||zt(this.pages[t-1])){var e="".concat(this.baseUrl).concat(t);return{link:this.useRouter?{path:e}:e,text:Io(t)}}var i=this.pages[t-1];if(Xt(i)){var n=i.link;return{link:Xt(n)?n:this.useRouter?{path:n}:n,text:Io(i.text||t)}}return{link:Io(i),text:Io(t)}},makePage:function(t){var e=this.pageGen,i=this.getPageInfo(t);return Es(e)?e(t,i):i.text},makeLink:function(t){var e=this.linkGen,i=this.getPageInfo(t);return Es(e)?e(t,i):i.link},linkProps:function(t){var e=Fs(hy,this),i=this.makeLink(t);return this.useRouter||Xt(i)?e.to=i:e.href=i,e},resolveLink:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{(t=document.createElement("a")).href=ka({to:e},"a","/","/"),document.body.appendChild(t);var i=t,n=i.pathname,r=i.hash,o=i.search;return document.body.removeChild(t),{path:n,hash:r,query:wa(o)}}catch(e){try{t&&t.parentNode&&t.parentNode.removeChild(t)}catch(t){}return{}}},resolveRoute:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{var e=this.$router.resolve(t,this.$route).route;return{path:e.path,hash:e.hash,query:e.query}}catch(t){return{}}},guessCurrentPage:function(){var t=this.$router,e=this.$route,i=this.computedValue;if(!this.noPageDetect&&!i&&(Y||!Y&&t))for(var n=t&&e?{path:e.path,hash:e.hash,query:e.query}:{},r=Y?window.location||document.location:null,o=r?{path:r.pathname,hash:r.hash,query:wa(r.search)}:{},s=1;!i&&s<=this.localNumberOfPages;s++){var a=this.makeLink(s);i=t&&(Xt(a)||this.useRouter)?ll(this.resolveRoute(a),n)?s:null:Y?ll(this.resolveLink(a),o)?s:null:-1}this.currentPage=i>0?i:0}}}),my=$e({components:{BPaginationNav:py}}),vy={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left",TOPLEFT:"top",TOPRIGHT:"top",RIGHTTOP:"right",RIGHTBOTTOM:"right",BOTTOMLEFT:"bottom",BOTTOMRIGHT:"bottom",LEFTTOP:"left",LEFTBOTTOM:"left"},gy={AUTO:0,TOPLEFT:-1,TOP:0,TOPRIGHT:1,RIGHTTOP:-1,RIGHT:0,RIGHTBOTTOM:1,BOTTOMLEFT:-1,BOTTOM:0,BOTTOMRIGHT:1,LEFTTOP:-1,LEFT:0,LEFTBOTTOM:1},by={arrowPadding:Ds(Hr,6),boundary:Ds([HTMLElement,Fr],"scrollParent"),boundaryPadding:Ds(Hr,5),fallbackPlacement:Ds(Er,"flip"),offset:Ds(Hr,0),placement:Ds(Fr,"top"),target:Ds([HTMLElement,SVGElement])},yy=L({name:"BVPopper",mixins:[Og],props:by,data:function(){return{noFade:!1,localShow:!0,attachment:this.getAttachment(this.placement)}},computed:{templateType:function(){return"unknown"},popperConfig:function(){var t=this,e=this.placement;return{placement:this.getAttachment(e),modifiers:{offset:{offset:this.getOffset(e)},flip:{behavior:this.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{padding:this.boundaryPadding,boundariesElement:this.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t.popperPlacementChange(e)},onUpdate:function(e){t.popperPlacementChange(e)}}}},created:function(){var t=this;this.$_popper=null,this.localShow=!0,this.$on(fr,(function(e){t.popperCreate(e)}));var e=function(){t.$nextTick((function(){Mo((function(){t.$destroy()}))}))};this.bvParent.$once(br,e),this.$once(tr,e)},beforeMount:function(){this.attachment=this.getAttachment(this.placement)},updated:function(){this.updatePopper()},beforeDestroy:function(){this.destroyPopper()},destroyed:function(){var t=this.$el;t&&t.parentNode&&t.parentNode.removeChild(t)},methods:{hide:function(){this.localShow=!1},getAttachment:function(t){return vy[String(t).toUpperCase()]||"auto"},getOffset:function(t){if(!this.offset){var e=this.$refs.arrow||Ko(".arrow",this.$el),i=xo(ds(e).width,0)+xo(this.arrowPadding,0);switch(gy[String(t).toUpperCase()]||0){case 1:return"+50%p - ".concat(i,"px");case-1:return"-50%p + ".concat(i,"px");default:return 0}}return this.offset},popperCreate:function(t){this.destroyPopper(),this.$_popper=new zh(this.target,t,this.popperConfig)},destroyPopper:function(){this.$_popper&&this.$_popper.destroy(),this.$_popper=null},updatePopper:function(){this.$_popper&&this.$_popper.scheduleUpdate()},popperPlacementChange:function(t){this.attachment=this.getAttachment(t.placement)},renderTemplate:function(t){return t("div")}},render:function(t){var e=this,i=this.noFade;return t(Ks,{props:{appear:!0,noFade:i},on:{beforeEnter:function(t){return e.$emit(fr,t)},afterEnter:function(t){return e.$emit(pr,t)},beforeLeave:function(t){return e.$emit(er,t)},afterLeave:function(t){return e.$emit(tr,t)}}},[this.localShow?this.renderTemplate(t):t()])}}),Ty={html:Ds(kr,!1),id:Ds(Fr)},wy=L({name:"BVTooltipTemplate",extends:yy,mixins:[Vg],props:Ty,data:function(){return{title:"",content:"",variant:null,customClass:null,interactive:!0}},computed:{templateType:function(){return"tooltip"},templateClasses:function(){var t,e=this.variant,i=this.attachment,n=this.templateType;return[(t={noninteractive:!this.interactive},c(t,"b-".concat(n,"-").concat(e),e),c(t,"bs-".concat(n,"-").concat(i),i),t),this.customClass]},templateAttributes:function(){var t=this.id;return r(r({},this.bvParent.bvParent.$attrs),{},{id:t,role:"tooltip",tabindex:"-1"},this.scopedStyleAttrs)},templateListeners:function(){var t=this;return{mouseenter:function(e){t.$emit(rr,e)},mouseleave:function(e){t.$emit(or,e)},focusin:function(e){t.$emit(Zn,e)},focusout:function(e){t.$emit(Jn,e)}}}},methods:{renderTemplate:function(t){var e=this.title,i=Gt(e)?e({}):e,n=this.html&&!Gt(e)?{innerHTML:e}:{};return t("div",{staticClass:"tooltip b-tooltip",class:this.templateClasses,attrs:this.templateAttributes,on:this.templateListeners},[t("div",{staticClass:"arrow",ref:"arrow"}),t("div",{staticClass:"tooltip-inner",domProps:n},[i])])}}}),Cy=".modal-content",Sy=Ns(Qi,tr),ky=[Cy,".b-sidebar"].join(", "),xy="data-original-title",$y={title:"",content:"",variant:null,customClass:null,triggers:"",placement:"auto",fallbackPlacement:"flip",target:null,container:null,noFade:!1,boundary:"scrollParent",boundaryPadding:5,offset:0,delay:0,arrowPadding:6,interactive:!0,disabled:!1,id:null,html:!1},By=L({name:"BVTooltip",mixins:[gl,Og],data:function(){return r(r({},$y),{},{activeTrigger:{hover:!1,click:!1,focus:!1},localShow:!1})},computed:{templateType:function(){return"tooltip"},computedId:function(){return this.id||"__bv_".concat(this.templateType,"_").concat(this._uid,"__")},computedDelay:function(){var t={show:0,hide:0};return Zt(this.delay)?(t.show=sa(ko(this.delay.show,0),0),t.hide=sa(ko(this.delay.hide,0),0)):(Yt(this.delay)||Ut(this.delay))&&(t.show=t.hide=sa(ko(this.delay,0),0)),t},computedTriggers:function(){return go(this.triggers).filter(pe).join(" ").trim().toLowerCase().split(/\s+/).sort()},isWithActiveTrigger:function(){for(var t in this.activeTrigger)if(this.activeTrigger[t])return!0;return!1},computedTemplateData:function(){return{title:this.title,content:this.content,variant:this.variant,customClass:this.customClass,noFade:this.noFade,interactive:this.interactive}}},watch:{computedTriggers:function(t,e){var i=this;ll(t,e)||this.$nextTick((function(){i.unListen(),e.forEach((function(e){vo(t,e)||i.activeTrigger[e]&&(i.activeTrigger[e]=!1)})),i.listen()}))},computedTemplateData:function(){this.handleTemplateUpdate()},title:function(t,e){t===e||t||this.hide()},disabled:function(t){t?this.disable():this.enable()}},created:function(){var t=this;this.$_tip=null,this.$_hoverTimeout=null,this.$_hoverState="",this.$_visibleInterval=null,this.$_enabled=!this.disabled,this.$_noop=_u.bind(this),this.bvParent&&this.bvParent.$once(gr,(function(){t.$nextTick((function(){Mo((function(){t.$destroy()}))}))})),this.$nextTick((function(){var e=t.getTarget();e&&Jo(document.body,e)?(t.scopeId=Eg(t.bvParent),t.listen()):ye(Ut(t.target)?'Unable to find target element by ID "#'.concat(t.target,'" in document.'):"The provided target is no valid HTML element.",t.templateType)}))},updated:function(){this.$nextTick(this.handleTemplateUpdate)},deactivated:function(){this.forceHide()},beforeDestroy:function(){this.unListen(),this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.clearVisibilityInterval(),this.destroyTemplate(),this.$_noop=null},methods:{getTemplate:function(){return wy},updateData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=!1;se($y).forEach((function(n){zt(e[n])||t[n]===e[n]||(t[n]=e[n],"title"===n&&(i=!0))})),i&&this.localShow&&this.fixTitle()},createTemplateAndShow:function(){var t=this.getContainer(),e=this.getTemplate(),i=this.$_tip=Lg(this,e,{propsData:{id:this.computedId,html:this.html,placement:this.placement,fallbackPlacement:this.fallbackPlacement,target:this.getPlacementTarget(),boundary:this.getBoundary(),offset:ko(this.offset,0),arrowPadding:ko(this.arrowPadding,0),boundaryPadding:ko(this.boundaryPadding,0)}});this.handleTemplateUpdate(),i.$once(fr,this.onTemplateShow),i.$once(pr,this.onTemplateShown),i.$once(er,this.onTemplateHide),i.$once(tr,this.onTemplateHidden),i.$once(br,this.destroyTemplate),i.$on(Zn,this.handleEvent),i.$on(Jn,this.handleEvent),i.$on(rr,this.handleEvent),i.$on(or,this.handleEvent),i.$mount(t.appendChild(document.createElement("div")))},hideTemplate:function(){this.$_tip&&this.$_tip.hide(),this.clearActiveTriggers(),this.$_hoverState=""},destroyTemplate:function(){this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.$_hoverState="",this.clearActiveTriggers(),this.localPlacementTarget=null;try{this.$_tip.$destroy()}catch(t){}this.$_tip=null,this.removeAriaDescribedby(),this.restoreTitle(),this.localShow=!1},getTemplateElement:function(){return this.$_tip?this.$_tip.$el:null},handleTemplateUpdate:function(){var t=this,e=this.$_tip;if(e){["title","content","variant","customClass","noFade","interactive"].forEach((function(i){e[i]!==t[i]&&(e[i]=t[i])}))}},show:function(){var t=this.getTarget();if(t&&Jo(document.body,t)&&Wo(t)&&!this.dropdownOpen()&&(!jt(this.title)&&""!==this.title||!jt(this.content)&&""!==this.content)&&!this.$_tip&&!this.localShow){this.localShow=!0;var e=this.buildEvent(fr,{cancelable:!0});this.emitEvent(e),e.defaultPrevented?this.destroyTemplate():(this.fixTitle(),this.addAriaDescribedby(),this.createTemplateAndShow())}},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.getTemplateElement();if(e&&this.localShow){var i=this.buildEvent(er,{cancelable:!t});this.emitEvent(i),i.defaultPrevented||this.hideTemplate()}else this.restoreTitle()},forceHide:function(){this.getTemplateElement()&&this.localShow&&(this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.$_hoverState="",this.clearActiveTriggers(),this.$_tip&&(this.$_tip.noFade=!0),this.hide(!0))},enable:function(){this.$_enabled=!0,this.emitEvent(this.buildEvent(qn))},disable:function(){this.$_enabled=!1,this.emitEvent(this.buildEvent(Un))},onTemplateShow:function(){this.setWhileOpenListeners(!0)},onTemplateShown:function(){var t=this.$_hoverState;this.$_hoverState="","out"===t&&this.leave(null),this.emitEvent(this.buildEvent(pr))},onTemplateHide:function(){this.setWhileOpenListeners(!1)},onTemplateHidden:function(){this.destroyTemplate(),this.emitEvent(this.buildEvent(tr))},getTarget:function(){var t=this.target;return Ut(t)?t=Qo(t.replace(/^#/,"")):Gt(t)?t=t():t&&(t=t.$el||t),zo(t)?t:null},getPlacementTarget:function(){return this.getTarget()},getTargetId:function(){var t=this.getTarget();return t&&t.id?t.id:null},getContainer:function(){var t=!!this.container&&(this.container.$el||this.container),e=document.body,i=this.getTarget();return!1===t?Zo(ky,i)||e:Ut(t)&&Qo(t.replace(/^#/,""))||e},getBoundary:function(){return this.boundary?this.boundary.$el||this.boundary:"scrollParent"},isInModal:function(){var t=this.getTarget();return t&&Zo(Cy,t)},isDropdown:function(){var t=this.getTarget();return t&&is(t,"dropdown")},dropdownOpen:function(){var t=this.getTarget();return this.isDropdown()&&t&&Ko(".dropdown-menu.show",t)},clearHoverTimeout:function(){clearTimeout(this.$_hoverTimeout),this.$_hoverTimeout=null},clearVisibilityInterval:function(){clearInterval(this.$_visibleInterval),this.$_visibleInterval=null},clearActiveTriggers:function(){for(var t in this.activeTrigger)this.activeTrigger[t]=!1},addAriaDescribedby:function(){var t=this.getTarget(),e=os(t,"aria-describedby")||"";e=e.split(/\s+/).concat(this.computedId).join(" ").trim(),ns(t,"aria-describedby",e)},removeAriaDescribedby:function(){var t=this,e=this.getTarget(),i=os(e,"aria-describedby")||"";(i=i.split(/\s+/).filter((function(e){return e!==t.computedId})).join(" ").trim())?ns(e,"aria-describedby",i):rs(e,"aria-describedby")},fixTitle:function(){var t=this.getTarget();if(ss(t,"title")){var e=os(t,"title");ns(t,"title",""),e&&ns(t,xy,e)}},restoreTitle:function(){var t=this.getTarget();if(ss(t,xy)){var e=os(t,xy);rs(t,xy),e&&ns(t,"title",e)}},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvEvent(t,r({cancelable:!1,target:this.getTarget(),relatedTarget:this.getTemplateElement()||null,componentId:this.computedId,vueTarget:this},e))},emitEvent:function(t){var e=t.type;this.emitOnRoot(Ns(this.templateType,e),t),this.$emit(e,t)},listen:function(){var t=this,e=this.getTarget();e&&(this.setRootListener(!0),this.computedTriggers.forEach((function(i){"click"===i?Rs(e,"click",t.handleEvent,wr):"focus"===i?(Rs(e,"focusin",t.handleEvent,wr),Rs(e,"focusout",t.handleEvent,wr)):"blur"===i?Rs(e,"focusout",t.handleEvent,wr):"hover"===i&&(Rs(e,"mouseenter",t.handleEvent,wr),Rs(e,"mouseleave",t.handleEvent,wr))}),this))},unListen:function(){var t=this,e=this.getTarget();this.setRootListener(!1),["click","focusin","focusout","mouseenter","mouseleave"].forEach((function(i){e&&As(e,i,t.handleEvent,wr)}),this)},setRootListener:function(t){var e=t?"listenOnRoot":"listenOffRoot",i=this.templateType;this[e](js(i,er),this.doHide),this[e](js(i,fr),this.doShow),this[e](js(i,Wn),this.doDisable),this[e](js(i,Yn),this.doEnable)},setWhileOpenListeners:function(t){this.setModalListener(t),this.setDropdownListener(t),this.visibleCheck(t),this.setOnTouchStartListener(t)},visibleCheck:function(t){var e=this;this.clearVisibilityInterval();var i=this.getTarget();t&&(this.$_visibleInterval=setInterval((function(){!e.getTemplateElement()||!e.localShow||i.parentNode&&Wo(i)||e.forceHide()}),100))},setModalListener:function(t){this.isInModal()&&this[t?"listenOnRoot":"listenOffRoot"](Sy,this.forceHide)},setOnTouchStartListener:function(t){var e=this;"ontouchstart"in document.documentElement&&mo(document.body.children).forEach((function(i){Ms(t,i,"mouseover",e.$_noop)}))},setDropdownListener:function(t){var e=this.getTarget();if(e&&this.bvEventRoot&&this.isDropdown){var i=function(t){if(!O)return t.__vue__;for(var e=t;e;){if(Gh.has(e))return Gh.get(e);e=e.parentNode}return null}(e);i&&i[t?"$on":"$off"](pr,this.forceHide)}},handleEvent:function(t){var e=this.getTarget();if(e&&!Uo(e)&&this.$_enabled&&!this.dropdownOpen()){var i=t.type,n=this.computedTriggers;if("click"===i&&vo(n,"click"))this.click(t);else if("mouseenter"===i&&vo(n,"hover"))this.enter(t);else if("focusin"===i&&vo(n,"focus"))this.enter(t);else if("focusout"===i&&(vo(n,"focus")||vo(n,"blur"))||"mouseleave"===i&&vo(n,"hover")){var r=this.getTemplateElement(),o=t.target,s=t.relatedTarget;if(r&&Jo(r,o)&&Jo(e,s)||r&&Jo(e,o)&&Jo(r,s)||r&&Jo(r,o)&&Jo(r,s)||Jo(e,o)&&Jo(e,s))return;this.leave(t)}}},doHide:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.forceHide()},doShow:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.show()},doDisable:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.disable()},doEnable:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.enable()},click:function(t){this.$_enabled&&!this.dropdownOpen()&&(vs(t.currentTarget),this.activeTrigger.click=!this.activeTrigger.click,this.isWithActiveTrigger?this.enter(null):this.leave(null))},toggle:function(){this.$_enabled&&!this.dropdownOpen()&&(this.localShow?this.leave(null):this.enter(null))},enter:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&(this.activeTrigger["focusin"===e.type?"focus":"hover"]=!0),this.localShow||"in"===this.$_hoverState?this.$_hoverState="in":(this.clearHoverTimeout(),this.$_hoverState="in",this.computedDelay.show?(this.fixTitle(),this.$_hoverTimeout=setTimeout((function(){"in"===t.$_hoverState?t.show():t.localShow||t.restoreTitle()}),this.computedDelay.show)):this.show())},leave:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&(this.activeTrigger["focusout"===e.type?"focus":"hover"]=!1,"focusout"===e.type&&vo(this.computedTriggers,"blur")&&(this.activeTrigger.click=!1,this.activeTrigger.hover=!1)),this.isWithActiveTrigger||(this.clearHoverTimeout(),this.$_hoverState="out",this.computedDelay.hide?this.$_hoverTimeout=setTimeout((function(){"out"===t.$_hoverState&&t.hide()}),this.computedDelay.hide):this.hide())}}}),Dy="disabled",_y="update:disabled",Fy="show",Py=Is((c(Eb={boundary:Ds([HTMLElement,Dr,Fr],"scrollParent"),boundaryPadding:Ds(Hr,50),container:Ds([HTMLElement,Dr,Fr]),customClass:Ds(Fr),delay:Ds(zr,50)},Dy,Ds(kr,!1)),c(Eb,"fallbackPlacement",Ds(Er,"flip")),c(Eb,"id",Ds(Fr)),c(Eb,"noFade",Ds(kr,!1)),c(Eb,"noninteractive",Ds(kr,!1)),c(Eb,"offset",Ds(Hr,0)),c(Eb,"placement",Ds(Fr,"top")),c(Eb,Fy,Ds(kr,!1)),c(Eb,"target",Ds([HTMLElement,SVGElement,$r,Dr,Fr],void 0,!0)),c(Eb,"title",Ds(Fr)),c(Eb,"triggers",Ds(Er,"hover focus")),c(Eb,"variant",Ds(Fr)),Eb),Vn),Iy=L({name:Vn,mixins:[So,Og],inheritAttrs:!1,props:Py,data:function(){return{localShow:this.show,localTitle:"",localContent:""}},computed:{templateData:function(){return r({title:this.localTitle,content:this.localContent,interactive:!this.noninteractive},ce(this.$props,["boundary","boundaryPadding","container","customClass","delay","fallbackPlacement","id","noFade","offset","placement","target","target","triggers","variant",Dy]))},templateTitleContent:function(){return{title:this.title,content:this.content}}},watch:(Vb={},c(Vb,Fy,(function(t,e){t!==e&&t!==this.localShow&&this.$_toolpop&&(t?this.$_toolpop.show():this.$_toolpop.forceHide())})),c(Vb,Dy,(function(t){t?this.doDisable():this.doEnable()})),c(Vb,"localShow",(function(t){this.$emit("update:show",t)})),c(Vb,"templateData",(function(){var t=this;this.$nextTick((function(){t.$_toolpop&&t.$_toolpop.updateData(t.templateData)}))})),c(Vb,"templateTitleContent",(function(){this.$nextTick(this.updateContent)})),Vb),created:function(){this.$_toolpop=null},updated:function(){this.$nextTick(this.updateContent)},beforeDestroy:function(){this.$off(ar,this.doOpen),this.$off(zn,this.doClose),this.$off(Wn,this.doDisable),this.$off(Yn,this.doEnable),this.$_toolpop&&(this.$_toolpop.$destroy(),this.$_toolpop=null)},mounted:function(){var t=this;this.$nextTick((function(){var e=t.getComponent();t.updateContent();var i=Eg(t)||Eg(t.bvParent),n=t.$_toolpop=Lg(t,e,{_scopeId:i||void 0});n.updateData(t.templateData),n.$on(fr,t.onShow),n.$on(pr,t.onShown),n.$on(er,t.onHide),n.$on(tr,t.onHidden),n.$on(Un,t.onDisabled),n.$on(qn,t.onEnabled),t.disabled&&t.doDisable(),t.$on(ar,t.doOpen),t.$on(zn,t.doClose),t.$on(Wn,t.doDisable),t.$on(Yn,t.doEnable),t.localShow&&n.show()}))},methods:{getComponent:function(){return By},updateContent:function(){this.setTitle(this.normalizeSlot()||this.title)},setTitle:function(t){t=jt(t)?"":t,this.localTitle!==t&&(this.localTitle=t)},setContent:function(t){t=jt(t)?"":t,this.localContent!==t&&(this.localContent=t)},onShow:function(t){this.$emit(fr,t),t&&(this.localShow=!t.defaultPrevented)},onShown:function(t){this.localShow=!0,this.$emit(pr,t)},onHide:function(t){this.$emit(er,t)},onHidden:function(t){this.$emit(tr,t),this.localShow=!1},onDisabled:function(t){t&&t.type===Un&&(this.$emit(_y,!0),this.$emit(Un,t))},onEnabled:function(t){t&&t.type===qn&&(this.$emit(_y,!1),this.$emit(qn,t))},doOpen:function(){!this.localShow&&this.$_toolpop&&this.$_toolpop.show()},doClose:function(){this.localShow&&this.$_toolpop&&this.$_toolpop.hide()},doDisable:function(){this.$_toolpop&&this.$_toolpop.disable()},doEnable:function(){this.$_toolpop&&this.$_toolpop.enable()}},render:function(t){return t()}}),Oy=L({name:"BVPopoverTemplate",extends:wy,computed:{templateType:function(){return"popover"}},methods:{renderTemplate:function(t){var e=this.title,i=this.content,n=Gt(e)?e({}):e,r=Gt(i)?i({}):i,o=this.html&&!Gt(e)?{innerHTML:e}:{},s=this.html&&!Gt(i)?{innerHTML:i}:{};return t("div",{staticClass:"popover b-popover",class:this.templateClasses,attrs:this.templateAttributes,on:this.templateListeners},[t("div",{staticClass:"arrow",ref:"arrow"}),jt(n)||""===n?t():t("h3",{staticClass:"popover-header",domProps:o},[n]),jt(r)||""===r?t():t("div",{staticClass:"popover-body",domProps:s},[r])])}}}),Ey=L({name:"BVPopover",extends:By,computed:{templateType:function(){return"popover"}},methods:{getTemplate:function(){return Oy}}}),Vy=Is(he(r(r({},Py),{},{content:Ds(Fr),placement:Ds(Fr,"right"),triggers:Ds(Er,Hn)})),hn),Ly=L({name:hn,extends:Iy,inheritAttrs:!1,props:Vy,methods:{getComponent:function(){return Ey},updateContent:function(){this.setContent(this.normalizeSlot()||this.content),this.setTitle(this.normalizeSlot(fo)||this.title)}}}),Ry="__BV_Popover__",Ay={focus:!0,hover:!0,click:!0,blur:!0,manual:!0},My=/^html$/i,Hy=/^nofade$/i,zy=/^(auto|top(left|right)?|bottom(left|right)?|left(top|bottom)?|right(top|bottom)?)$/i,Ny=/^(window|viewport|scrollParent)$/i,jy=/^d\d+$/i,Gy=/^ds\d+$/i,Wy=/^dh\d+$/i,Uy=/^o-?\d+$/i,Yy=/^v-.+$/i,qy=/\s+/,Ky=function(t,e,i){if(Y){var n=function(t,e){var i={title:void 0,content:void 0,trigger:"",placement:"right",fallbackPlacement:"flip",container:!1,animation:!0,offset:0,disabled:!1,id:null,html:!1,delay:ws(hn,"delay",50),boundary:String(ws(hn,"boundary","scrollParent")),boundaryPadding:ko(ws(hn,"boundaryPadding",5),0),variant:ws(hn,"variant"),customClass:ws(hn,"customClass")};if(Ut(t.value)||Yt(t.value)||Gt(t.value)?i.content=t.value:Zt(t.value)&&(i=r(r({},i),t.value)),t.arg&&(i.container="#".concat(t.arg)),zt(i.title)){var n=e.data||{};i.title=n.attrs&&!jt(n.attrs.title)?n.attrs.title:void 0}Zt(i.delay)||(i.delay={show:ko(i.delay,0),hide:ko(i.delay,0)}),se(t.modifiers).forEach((function(t){if(My.test(t))i.html=!0;else if(Hy.test(t))i.animation=!1;else if(zy.test(t))i.placement=t;else if(Ny.test(t))t="scrollparent"===t?"scrollParent":t,i.boundary=t;else if(jy.test(t)){var e=ko(t.slice(1),0);i.delay.show=e,i.delay.hide=e}else Gy.test(t)?i.delay.show=ko(t.slice(2),0):Wy.test(t)?i.delay.hide=ko(t.slice(2),0):Uy.test(t)?i.offset=ko(t.slice(1),0):Yy.test(t)&&(i.variant=t.slice(2)||null)}));var o={};return go(i.trigger||"").filter(pe).join(" ").trim().toLowerCase().split(qy).forEach((function(t){Ay[t]&&(o[t]=!0)})),se(t.modifiers).forEach((function(t){t=t.toLowerCase(),Ay[t]&&(o[t]=!0)})),i.trigger=se(o).join(" "),"blur"===i.trigger&&(i.trigger="focus"),i.trigger||(i.trigger="click"),i}(e,i);if(!t[Ry]){var o=ld(i,e);t[Ry]=Lg(o,Ey,{_scopeId:Eg(o,void 0)}),t[Ry].__bv_prev_data__={},t[Ry].$on(fr,(function(){var e={};Gt(n.title)&&(e.title=n.title(t)),Gt(n.content)&&(e.content=n.content(t)),se(e).length>0&&t[Ry].updateData(e)}))}var s={title:n.title,content:n.content,triggers:n.trigger,placement:n.placement,fallbackPlacement:n.fallbackPlacement,variant:n.variant,customClass:n.customClass,container:n.container,boundary:n.boundary,delay:n.delay,offset:n.offset,noFade:!n.animation,id:n.id,disabled:n.disabled,html:n.html},a=t[Ry].__bv_prev_data__;if(t[Ry].__bv_prev_data__=s,!ll(s,a)){var l={target:t};se(s).forEach((function(e){s[e]!==a[e]&&(l[e]="title"!==e&&"content"!==e||!Gt(s[e])?s[e]:s[e](t))})),t[Ry].updateData(l)}}},Xy=$e({directives:{VBPopover:{bind:function(t,e,i){Ky(t,e,i)},componentUpdated:function(t,e,i){z((function(){Ky(t,e,i)}))},unbind:function(t){!function(t){t[Ry]&&(t[Ry].$destroy(),t[Ry]=null),delete t[Ry]}(t)}}}}),Zy=$e({components:{BPopover:Ly},plugins:{VBPopoverPlugin:Xy}}),Jy=Is({animated:Ds(kr,null),label:Ds(Fr),labelHtml:Ds(Fr),max:Ds(Hr,null),precision:Ds(Hr,null),showProgress:Ds(kr,null),showValue:Ds(kr,null),striped:Ds(kr,null),value:Ds(Hr,0),variant:Ds(Fr)},pn),Qy=L({name:pn,mixins:[So],inject:{getBvProgress:{default:function(){return function(){return{}}}}},props:Jy,computed:{bvProgress:function(){return this.getBvProgress()},progressBarClasses:function(){var t=this.computedAnimated,e=this.computedVariant;return[e?"bg-".concat(e):"",this.computedStriped||t?"progress-bar-striped":"",t?"progress-bar-animated":""]},progressBarStyles:function(){return{width:this.computedValue/this.computedMax*100+"%"}},computedValue:function(){return xo(this.value,0)},computedMax:function(){var t=xo(this.max)||xo(this.bvProgress.max,0);return t>0?t:100},computedPrecision:function(){return sa(ko(this.precision,ko(this.bvProgress.precision,0)),0)},computedProgress:function(){var t=this.computedPrecision,e=ua(10,t);return $o(100*e*this.computedValue/this.computedMax/e,t)},computedVariant:function(){return this.variant||this.bvProgress.variant},computedStriped:function(){return Wt(this.striped)?this.striped:this.bvProgress.striped||!1},computedAnimated:function(){return Wt(this.animated)?this.animated:this.bvProgress.animated||!1},computedShowProgress:function(){return Wt(this.showProgress)?this.showProgress:this.bvProgress.showProgress||!1},computedShowValue:function(){return Wt(this.showValue)?this.showValue:this.bvProgress.showValue||!1}},render:function(t){var e,i=this.label,n=this.labelHtml,r=this.computedValue,o=this.computedPrecision,s={};return this.hasNormalizedSlot()?e=this.normalizeSlot():i||n?s=Zl(n,i):this.computedShowProgress?e=this.computedProgress:this.computedShowValue&&(e=$o(r,o)),t("div",{staticClass:"progress-bar",class:this.progressBarClasses,style:this.progressBarStyles,attrs:{role:"progressbar","aria-valuemin":"0","aria-valuemax":Io(this.computedMax),"aria-valuenow":$o(r,o)},domProps:s},e)}}),tT=ue(Jy,["label","labelHtml"]),eT=Is(he(r(r({},tT),{},{animated:Ds(kr,!1),height:Ds(Fr),max:Ds(Hr,100),precision:Ds(Hr,0),showProgress:Ds(kr,!1),showValue:Ds(kr,!1),striped:Ds(kr,!1)})),fn),iT=$e({components:{BProgress:L({name:fn,mixins:[So],provide:function(){var t=this;return{getBvProgress:function(){return t}}},props:eT,computed:{progressHeight:function(){return{height:this.height||null}}},render:function(t){var e=this.normalizeSlot();return e||(e=t(Qy,{props:Fs(tT,this.$props)})),t("div",{staticClass:"progress",style:this.progressHeight},[e])}}),BProgressBar:Qy}}),nT="b-sidebar",rT=js(ri,"request-state"),oT=js(ri,"toggle"),sT=Ns(ri,"state"),aT=Ns(ri,"sync-state"),lT=Vs("visible",{type:kr,defaultValue:!1,event:Mn}),cT=lT.mixin,uT=lT.props,dT=lT.prop,hT=lT.event,fT=Is(he(r(r(r({},Vc),uT),{},{ariaLabel:Ds(Fr),ariaLabelledby:Ds(Fr),backdrop:Ds(kr,!1),backdropVariant:Ds(Fr,"dark"),bgVariant:Ds(Fr,"light"),bodyClass:Ds(Or),closeLabel:Ds(Fr),footerClass:Ds(Or),footerTag:Ds(Fr,"footer"),headerClass:Ds(Or),headerTag:Ds(Fr,"header"),lazy:Ds(kr,!1),noCloseOnBackdrop:Ds(kr,!1),noCloseOnEsc:Ds(kr,!1),noCloseOnRouteChange:Ds(kr,!1),noEnforceFocus:Ds(kr,!1),noHeader:Ds(kr,!1),noHeaderClose:Ds(kr,!1),noSlide:Ds(kr,!1),right:Ds(kr,!1),shadow:Ds(Rr,!1),sidebarClass:Ds(Or),tag:Ds(Fr,"div"),textVariant:Ds(Fr,"dark"),title:Ds(Fr),width:Ds(Fr),zIndex:Ds(Hr)})),vn),pT=function(t,e){if(e.noHeader)return t();var i=e.normalizeSlot(to,e.slotScope);if(!i){var n=function(t,e){var i=e.normalizeSlot(fo,e.slotScope)||e.title;return i?t("strong",{attrs:{id:e.safeId("__title__")}},[i]):t("span")}(t,e),r=function(t,e){if(e.noHeaderClose)return t();var i=e.closeLabel,n=e.textVariant,r=e.hide;return t(Ws,{props:{ariaLabel:i,textVariant:n},on:{click:r},ref:"close-button"},[e.normalizeSlot("header-close")||t(qa)])}(t,e);i=e.right?[r,n]:[n,r]}return t(e.headerTag,{staticClass:"".concat(nT,"-header"),class:e.headerClass,key:"header"},i)},mT=function(t,e){return t("div",{staticClass:"".concat(nT,"-body"),class:e.bodyClass,key:"body"},[e.normalizeSlot(Kr,e.slotScope)])},vT=function(t,e){var i=e.normalizeSlot(Qr,e.slotScope);return i?t(e.footerTag,{staticClass:"".concat(nT,"-footer"),class:e.footerClass,key:"footer"},[i]):t()},gT=function(t,e){var i=pT(t,e);return e.lazy&&!e.isOpen?i:[i,mT(t,e),vT(t,e)]},bT=function(t,e){if(!e.backdrop)return t();var i=e.backdropVariant;return t("div",{directives:[{name:"show",value:e.localShow}],staticClass:"b-sidebar-backdrop",class:c({},"bg-".concat(i),i),on:{click:e.onBackdropClick}})},yT=L({name:vn,mixins:[pl,Lc,cT,gl,So],inheritAttrs:!1,props:fT,data:function(){var t=!!this[dT];return{localShow:t,isOpen:t}},computed:{transitionProps:function(){return this.noSlide?{css:!0}:{css:!0,enterClass:"",enterActiveClass:"slide",enterToClass:"show",leaveClass:"show",leaveActiveClass:"slide",leaveToClass:""}},slotScope:function(){return{hide:this.hide,right:this.right,visible:this.localShow}},hasTitle:function(){var t=this.$scopedSlots,e=this.$slots;return!(this.noHeader||this.hasNormalizedSlot(to)||!this.normalizeSlot(fo,this.slotScope,t,e)&&!this.title)},titleId:function(){return this.hasTitle?this.safeId("__title__"):null},computedAttrs:function(){return r(r({},this.bvAttrs),{},{id:this.safeId(),tabindex:"-1",role:"dialog","aria-modal":this.backdrop?"true":"false","aria-hidden":this.localShow?null:"true","aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||this.titleId||null})}},watch:(Lb={},c(Lb,dT,(function(t,e){t!==e&&(this.localShow=t)})),c(Lb,"localShow",(function(t,e){t!==e&&(this.emitState(t),this.$emit(hT,t))})),c(Lb,"$route",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.noCloseOnRouteChange||t.fullPath===e.fullPath||this.hide()})),Lb),created:function(){this.$_returnFocusEl=null},mounted:function(){var t=this;this.listenOnRoot(oT,this.handleToggle),this.listenOnRoot(rT,this.handleSync),this.$nextTick((function(){t.emitState(t.localShow)}))},activated:function(){this.emitSync()},beforeDestroy:function(){this.localShow=!1,this.$_returnFocusEl=null},methods:{hide:function(){this.localShow=!1},emitState:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(sT,this.safeId(),t)},emitSync:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(aT,this.safeId(),t)},handleToggle:function(t){t&&t===this.safeId()&&(this.localShow=!this.localShow)},handleSync:function(t){var e=this;t&&t===this.safeId()&&this.$nextTick((function(){e.emitSync(e.localShow)}))},onKeydown:function(t){var e=t.keyCode;!this.noCloseOnEsc&&27===e&&this.localShow&&this.hide()},onBackdropClick:function(){this.localShow&&!this.noCloseOnBackdrop&&this.hide()},onTopTrapFocus:function(){var t=ms(this.$refs.content);this.enforceFocus(t.reverse()[0])},onBottomTrapFocus:function(){var t=ms(this.$refs.content);this.enforceFocus(t[0])},onBeforeEnter:function(){this.$_returnFocusEl=No(Y?[document.body]:[]),this.isOpen=!0},onAfterEnter:function(t){Jo(t,No())||this.enforceFocus(t),this.$emit(pr)},onAfterLeave:function(){this.enforceFocus(this.$_returnFocusEl),this.$_returnFocusEl=null,this.isOpen=!1,this.$emit(tr)},enforceFocus:function(t){this.noEnforceFocus||vs(t)}},render:function(t){var e,i=this.bgVariant,n=this.width,r=this.textVariant,o=this.localShow,s=""===this.shadow||this.shadow,a=t(this.tag,{staticClass:nT,class:[(e={shadow:!0===s},c(e,"shadow-".concat(s),s&&!0!==s),c(e,"".concat(nT,"-right"),this.right),c(e,"bg-".concat(i),i),c(e,"text-".concat(r),r),e),this.sidebarClass],style:{width:n},attrs:this.computedAttrs,directives:[{name:"show",value:o}],ref:"content"},[gT(t,this)]);a=t("transition",{props:this.transitionProps,on:{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,afterLeave:this.onAfterLeave}},[a]);var l=t(Ks,{props:{noFade:this.noSlide}},[bT(t,this)]),u=t(),d=t();return this.backdrop&&o&&(u=t("div",{attrs:{tabindex:"0"},on:{focus:this.onTopTrapFocus}}),d=t("div",{attrs:{tabindex:"0"},on:{focus:this.onBottomTrapFocus}})),t("div",{staticClass:"b-sidebar-outer",style:{zIndex:this.zIndex},attrs:{tabindex:"-1"},on:{keydown:this.onKeydown}},[u,a,d,l])}}),TT=$e({components:{BSidebar:yT},plugins:{VBTogglePlugin:Od}}),wT=Is({animation:Ds(Fr,"wave"),height:Ds(Fr),size:Ds(Fr),type:Ds(Fr,"text"),variant:Ds(Fr),width:Ds(Fr)},gn),CT=L({name:gn,functional:!0,props:wT,render:function(t,e){var i,n=e.data,r=e.props,o=r.size,s=r.animation,a=r.variant;return t("div",I(n,{staticClass:"b-skeleton",style:{width:o||r.width,height:o||r.height},class:(i={},c(i,"b-skeleton-".concat(r.type),!0),c(i,"b-skeleton-animate-".concat(s),s),c(i,"bg-".concat(a),a),i)}))}});Is(ue(Ba,["content","stacked"]),"BIconstack");var ST,kT,xT,$T,BT,DT=Is({animation:Ds(Fr,"wave"),icon:Ds(Fr),iconProps:Ds(Dr,{})},bn),_T=L({name:bn,functional:!0,props:DT,render:function(t,e){var i=e.data,n=e.props,o=n.icon,s=n.animation,a=t(Ja,{staticClass:"b-skeleton-icon",props:r(r({},n.iconProps),{},{icon:o})});return t("div",I(i,{staticClass:"b-skeleton-icon-wrapper position-relative d-inline-block overflow-hidden",class:c({},"b-skeleton-animate-".concat(s),s)}),[a])}}),FT=Is({animation:Ds(Fr),aspect:Ds(Fr,"16:9"),cardImg:Ds(Fr),height:Ds(Fr),noAspect:Ds(kr,!1),variant:Ds(Fr),width:Ds(Fr)},yn),PT=L({name:yn,functional:!0,props:FT,render:function(t,e){var i=e.data,n=e.props,r=n.aspect,o=n.width,s=n.height,a=n.animation,l=n.variant,u=n.cardImg,d=t(CT,I(i,{props:{type:"img",width:o,height:s,animation:a,variant:l},class:c({},"card-img-".concat(u),u)}));return n.noAspect?d:t(pa,{props:{aspect:r}},[d])}}),IT=L({methods:{hasListener:function(t){if(O)return!0;var e=this.$listeners||{},i=this._events||{};return!zt(e[t])||Kt(i[t])&&i[t].length>0}}}),OT="light",ET="dark",VT=Is({variant:Ds(Fr)},"BTr"),LT=L({name:"BTr",mixins:[pl,Tl,So],provide:function(){var t=this;return{getBvTableTr:function(){return t}}},inject:{getBvTableRowGroup:{default:function(){return function(){return{}}}}},inheritAttrs:!1,props:VT,computed:{bvTableRowGroup:function(){return this.getBvTableRowGroup()},inTbody:function(){return this.bvTableRowGroup.isTbody},inThead:function(){return this.bvTableRowGroup.isThead},inTfoot:function(){return this.bvTableRowGroup.isTfoot},isDark:function(){return this.bvTableRowGroup.isDark},isStacked:function(){return this.bvTableRowGroup.isStacked},isResponsive:function(){return this.bvTableRowGroup.isResponsive},isStickyHeader:function(){return this.bvTableRowGroup.isStickyHeader},hasStickyHeader:function(){return!this.isStacked&&this.bvTableRowGroup.hasStickyHeader},tableVariant:function(){return this.bvTableRowGroup.tableVariant},headVariant:function(){return this.inThead?this.bvTableRowGroup.headVariant:null},footVariant:function(){return this.inTfoot?this.bvTableRowGroup.footVariant:null},isRowDark:function(){return this.headVariant!==OT&&this.footVariant!==OT&&(this.headVariant===ET||this.footVariant===ET||this.isDark)},trClasses:function(){var t=this.variant;return[t?"".concat(this.isRowDark?"bg":"table","-").concat(t):null]},trAttrs:function(){return r({role:"row"},this.bvAttrs)}},render:function(t){return t("tr",{class:this.trClasses,attrs:this.trAttrs,on:this.bvListeners},this.normalizeSlot())}}),RT={},AT=L({props:RT,methods:{renderBottomRow:function(){var t=this.computedFields,e=this.stacked,i=this.tbodyTrClass,n=this.tbodyTrAttr,r=this.$createElement;return this.hasNormalizedSlot(Ur)&&!0!==e&&""!==e?r(LT,{staticClass:"b-table-bottom-row",class:[Gt(i)?i(null,"row-bottom"):i],attrs:Gt(n)?n(null,"row-bottom"):n,key:"b-bottom-row"},this.normalizeSlot(Ur,{columns:t.length,fields:t})):r()}}}),MT=function(t){return(t=ko(t,0))>0?t:null},HT=function(t){return jt(t)||MT(t)>0},zT=Is({colspan:Ds(Hr,null,HT),rowspan:Ds(Hr,null,HT),stackedHeading:Ds(Fr),stickyColumn:Ds(kr,!1),variant:Ds(Fr)},xn),NT=L({name:xn,mixins:[pl,Tl,So],inject:{getBvTableTr:{default:function(){return function(){return{}}}}},inheritAttrs:!1,props:zT,computed:{bvTableTr:function(){return this.getBvTableTr()},tag:function(){return"td"},inTbody:function(){return this.bvTableTr.inTbody},inThead:function(){return this.bvTableTr.inThead},inTfoot:function(){return this.bvTableTr.inTfoot},isDark:function(){return this.bvTableTr.isDark},isStacked:function(){return this.bvTableTr.isStacked},isStackedCell:function(){return this.inTbody&&this.isStacked},isResponsive:function(){return this.bvTableTr.isResponsive},isStickyHeader:function(){return this.bvTableTr.isStickyHeader},hasStickyHeader:function(){return this.bvTableTr.hasStickyHeader},isStickyColumn:function(){return!this.isStacked&&(this.isResponsive||this.hasStickyHeader)&&this.stickyColumn},rowVariant:function(){return this.bvTableTr.variant},headVariant:function(){return this.bvTableTr.headVariant},footVariant:function(){return this.bvTableTr.footVariant},tableVariant:function(){return this.bvTableTr.tableVariant},computedColspan:function(){return MT(this.colspan)},computedRowspan:function(){return MT(this.rowspan)},cellClasses:function(){var t=this.variant,e=this.headVariant,i=this.isStickyColumn;return(!t&&this.isStickyHeader&&!e||!t&&i&&this.inTfoot&&!this.footVariant||!t&&i&&this.inThead&&!e||!t&&i&&this.inTbody)&&(t=this.rowVariant||this.tableVariant||"b-table-default"),[t?"".concat(this.isDark?"bg":"table","-").concat(t):null,i?"b-table-sticky-column":null]},cellAttrs:function(){var t=this.stackedHeading,e=this.inThead||this.inTfoot,i=this.computedColspan,n=this.computedRowspan,o="cell",s=null;return e?(o="columnheader",s=i>0?"colspan":"col"):jo(this.tag,"th")&&(o="rowheader",s=n>0?"rowgroup":"row"),r(r({colspan:i,rowspan:n,role:o,scope:s},this.bvAttrs),{},{"data-label":this.isStackedCell&&!jt(t)?Io(t):null})}},render:function(t){var e=[this.normalizeSlot()];return t(this.tag,{class:this.cellClasses,attrs:this.cellAttrs,on:this.bvListeners},[this.isStackedCell?t("div",[e]):e])}}),jT="busy",GT=c({},jT,Ds(kr,!1)),WT=L({props:GT,data:function(){return{localBusy:!1}},computed:{computedBusy:function(){return this.busy||this.localBusy}},watch:{localBusy:function(t,e){t!==e&&this.$emit("update:busy",t)}},methods:{stopIfBusy:function(t){return!!this.computedBusy&&(Hs(t),!0)},renderBusy:function(){var t=this.tbodyTrClass,e=this.tbodyTrAttr,i=this.$createElement;return this.computedBusy&&this.hasNormalizedSlot(lo)?i(LT,{staticClass:"b-table-busy-slot",class:[Gt(t)?t(null,lo):t],attrs:Gt(e)?e(null,lo):e,key:"table-busy-slot"},[i(NT,{props:{colspan:this.computedFields.length||null}},[this.normalizeSlot(lo)])]):null}}}),UT={caption:Ds(Fr),captionHtml:Ds(Fr)},YT=L({props:UT,computed:{captionId:function(){return this.isStacked?this.safeId("_caption_"):null}},methods:{renderCaption:function(){var t=this.caption,e=this.captionHtml,i=this.$createElement,n=i(),r=this.hasNormalizedSlot(co);return(r||t||e)&&(n=i("caption",{attrs:{id:this.captionId},domProps:r?{}:Zl(e,t),key:"caption",ref:"caption"},this.normalizeSlot(co))),n}}}),qT={},KT=L({methods:{renderColgroup:function(){var t=this.computedFields,e=this.$createElement,i=e();return this.hasNormalizedSlot(uo)&&(i=e("colgroup",{key:"colgroup"},[this.normalizeSlot(uo,{columns:t.length,fields:t})])),i}}}),XT={emptyFilteredHtml:Ds(Fr),emptyFilteredText:Ds(Fr,"There are no records matching your request"),emptyHtml:Ds(Fr),emptyText:Ds(Fr,"There are no records to show"),showEmpty:Ds(kr,!1)},ZT=L({props:XT,methods:{renderEmpty:function(){var t=va(this),e=t.computedItems,i=t.computedBusy,n=this.$createElement,r=n();if(this.showEmpty&&(!e||0===e.length)&&(!i||!this.hasNormalizedSlot(lo))){var o=this.computedFields,s=this.isFiltered,a=this.emptyText,l=this.emptyHtml,c=this.emptyFilteredText,u=this.emptyFilteredHtml,d=this.tbodyTrClass,h=this.tbodyTrAttr;(r=this.normalizeSlot(s?"emptyfiltered":Xr,{emptyFilteredHtml:u,emptyFilteredText:c,emptyHtml:l,emptyText:a,fields:o,items:e}))||(r=n("div",{class:["text-center","my-2"],domProps:s?Zl(u,c):Zl(l,a)})),r=n(NT,{props:{colspan:o.length||null}},[n("div",{attrs:{role:"alert","aria-live":"polite"}},[r])]),r=n(LT,{staticClass:"b-table-empty-row",class:[Gt(d)?d(null,"row-empty"):d],attrs:Gt(h)?h(null,"row-empty"):h,key:s?"b-empty-filtered-row":"b-empty-row"},[r])}return r}}}),JT=function t(e){return jt(e)?"":Xt(e)&&!Jt(e)?se(e).sort().map((function(i){return t(e[i])})).filter((function(t){return!!t})).join(" "):Io(e)},QT="_cellVariants",tw="_rowVariant",ew="_showDetails",iw=[QT,tw,ew].reduce((function(t,e){return r(r({},t),{},c({},e,!0))}),{}),nw=["a","a *","button","button *","input:not(.disabled):not([disabled])","select:not(.disabled):not([disabled])","textarea:not(.disabled):not([disabled])",'[role="link"]','[role="link"] *','[role="button"]','[role="button"] *',"[tabindex]:not(.disabled):not([disabled])"].join(","),rw=function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=se(n).reduce((function(e,i){var r=n[i],o=r.filterByFormatted,s=Gt(o)?o:o?r.formatter:null;return Gt(s)&&(e[i]=s(t[i],i,t)),e}),le(t)),o=se(r).filter((function(t){return!(iw[t]||Kt(e)&&e.length>0&&vo(e,t)||Kt(i)&&i.length>0&&!vo(i,t))}));return ce(r,o)},ow={filter:Ds([].concat(S(Or),[_r])),filterDebounce:Ds(Hr,0,(function(t){return ut.test(String(t))})),filterFunction:Ds($r),filterIgnoredFields:Ds(Sr,[]),filterIncludedFields:Ds(Sr,[])},sw=L({props:ow,data:function(){return{isFiltered:!1,localFilter:this.filterSanitize(this.filter)}},computed:{computedFilterIgnored:function(){return go(this.filterIgnoredFields||[]).filter(pe)},computedFilterIncluded:function(){return go(this.filterIncludedFields||[]).filter(pe)},computedFilterDebounce:function(){var t=ko(this.filterDebounce,0);return t>0&&ye('Prop "filter-debounce" is deprecated. Use the debounce feature of "<b-form-input>" instead.',kn),t},localFiltering:function(){return!this.hasProvider||!!this.noProviderFiltering},filteredCheck:function(){return{filteredItems:this.filteredItems,localItems:this.localItems,localFilter:this.localFilter}},localFilterFn:function(){var t=this.filterFunction;return Es(t)?t:null},filteredItems:function(){var t=this.localItems,e=this.localFilter,i=this.localFiltering?this.filterFnFactory(this.localFilterFn,e)||this.defaultFilterFnFactory(e):null;return i&&t.length>0?t.filter(i):t}},watch:{computedFilterDebounce:function(t){!t&&this.$_filterTimer&&(this.clearFilterTimer(),this.localFilter=this.filterSanitize(this.filter))},filter:{deep:!0,handler:function(t){var e=this,i=this.computedFilterDebounce;this.clearFilterTimer(),i&&i>0?this.$_filterTimer=setTimeout((function(){e.localFilter=e.filterSanitize(t)}),i):this.localFilter=this.filterSanitize(t)}},filteredCheck:function(t){var e=t.filteredItems,i=t.localFilter,n=!1;i?ll(i,[])||ll(i,{})?n=!1:i&&(n=!0):n=!1,n&&this.$emit(Kn,e,e.length),this.isFiltered=n},isFiltered:function(t,e){if(!1===t&&!0===e){var i=this.localItems;this.$emit(Kn,i,i.length)}}},created:function(){var t=this;this.$_filterTimer=null,this.$nextTick((function(){t.isFiltered=Boolean(t.localFilter)}))},beforeDestroy:function(){this.clearFilterTimer()},methods:{clearFilterTimer:function(){clearTimeout(this.$_filterTimer),this.$_filterTimer=null},filterSanitize:function(t){return!this.localFiltering||this.localFilterFn||Ut(t)||te(t)?fe(t):""},filterFnFactory:function(t,e){if(!t||!Gt(t)||!e||ll(e,[])||ll(e,{}))return null;return function(i){return t(i,e)}},defaultFilterFnFactory:function(t){var e=this;if(!t||!Ut(t)&&!te(t))return null;var i=t;if(Ut(i)){var n=Po(t).replace(Tt,"\\s+");i=new RegExp(".*".concat(n,".*"),"i")}return function(t){return i.lastIndex=0,i.test((n=t,r=e.computedFilterIgnored,o=e.computedFilterIncluded,s=e.computedFieldsObj,Xt(n)?JT(rw(n,r,o,s)):""));var n,r,o,s}}}}),aw=function(t,e){var i=[];if(Kt(t)&&t.filter(pe).forEach((function(t){if(Ut(t))i.push({key:t,label:_o(t)});else if(Xt(t)&&t.key&&Ut(t.key))i.push(le(t));else if(Xt(t)&&1===se(t).length){var e=se(t)[0],n=function(t,e){var i=null;return Ut(e)?i={key:t,label:e}:Gt(e)?i={key:t,formatter:e}:Xt(e)?(i=le(e)).key=i.key||t:!1!==e&&(i={key:t}),i}(e,t[e]);n&&i.push(n)}})),0===i.length&&Kt(e)&&e.length>0){var n=e[0];se(n).forEach((function(t){iw[t]||i.push({key:t,label:_o(t)})}))}var r={};return i.filter((function(t){return!r[t.key]&&(r[t.key]=!0,t.label=Ut(t.label)?t.label:_o(t.key),!0)}))},lw=Vs("value",{type:Sr,defaultValue:[]}),cw=lw.mixin,uw=lw.props,dw=lw.prop,hw=lw.event,fw=he(r(r({},uw),{},c({fields:Ds(Sr,null),items:Ds(Sr,[]),primaryKey:Ds(Fr)},dw,Ds(Sr,[])))),pw=L({mixins:[cw,Og],props:fw,data:function(){var t=this.items;return{localItems:Kt(t)?t.slice():[]}},computed:{computedFields:function(){return aw(this.fields,this.localItems)},computedFieldsObj:function(){var t=this.bvParent;return this.computedFields.reduce((function(e,i){if(e[i.key]=le(i),i.formatter){var n=i.formatter;Ut(n)&&Gt(t[n])?n=t[n]:Gt(n)||(n=void 0),e[i.key].formatter=n}return e}),{})},computedItems:function(){var t=va(this),e=t.paginatedItems,i=t.sortedItems,n=t.filteredItems,r=t.localItems;return(e||i||n||r||[]).slice()},context:function(){var t=va(this),e=t.perPage,i=t.currentPage;return{filter:this.localFilter,sortBy:this.localSortBy,sortDesc:this.localSortDesc,perPage:sa(ko(e,0),0),currentPage:sa(ko(i,0),1),apiUrl:this.apiUrl}}},watch:{items:function(t){this.localItems=Kt(t)?t.slice():[]},computedItems:function(t,e){ll(t,e)||this.$emit(hw,t)},context:function(t,e){ll(t,e)||this.$emit(jn,t)}},mounted:function(){this.$emit(hw,this.computedItems)},methods:{getFieldFormatter:function(t){var e=this.computedFieldsObj[t];return e?e.formatter:void 0}}}),mw={currentPage:Ds(Hr,1),perPage:Ds(Hr,0)},vw=L({props:mw,computed:{localPaging:function(){return!this.hasProvider||!!this.noProviderPaging},paginatedItems:function(){var t=va(this),e=t.sortedItems,i=t.filteredItems,n=t.localItems,r=e||i||n||[],o=sa(ko(this.currentPage,1),1),s=sa(ko(this.perPage,0),0);return this.localPaging&&s&&(r=r.slice((o-1)*s,o*s)),r}}}),gw=Ns(kn,ur),bw=js(kn,"refresh"),yw={apiUrl:Ds(Fr),items:Ds(Pr,[]),noProviderFiltering:Ds(kr,!1),noProviderPaging:Ds(kr,!1),noProviderSorting:Ds(kr,!1)},Tw=L({mixins:[gl],props:yw,computed:{hasProvider:function(){return Gt(this.items)},providerTriggerContext:function(){var t={apiUrl:this.apiUrl,filter:null,sortBy:null,sortDesc:null,perPage:null,currentPage:null};return this.noProviderFiltering||(t.filter=this.localFilter),this.noProviderSorting||(t.sortBy=this.localSortBy,t.sortDesc=this.localSortDesc),this.noProviderPaging||(t.perPage=this.perPage,t.currentPage=this.currentPage),le(t)}},watch:{items:function(t){(this.hasProvider||Gt(t))&&this.$nextTick(this._providerUpdate)},providerTriggerContext:function(t,e){ll(t,e)||this.$nextTick(this._providerUpdate)}},mounted:function(){var t=this;!this.hasProvider||this.localItems&&0!==this.localItems.length||this._providerUpdate(),this.listenOnRoot(bw,(function(e){e!==t.id&&e!==t||t.refresh()}))},methods:{refresh:function(){var t=va(this),e=t.items,i=t.refresh,n=t.computedBusy;this.$off(ur,i),n?this.localBusy&&this.hasProvider&&this.$on(ur,i):(this.clearSelected(),this.hasProvider?this.$nextTick(this._providerUpdate):this.localItems=Kt(e)?e.slice():[])},_providerSetLocal:function(t){this.localItems=Kt(t)?t.slice():[],this.localBusy=!1,this.$emit(ur),this.id&&this.emitOnRoot(gw,this.id)},_providerUpdate:function(){var t=this;this.hasProvider&&(va(this).computedBusy?this.$nextTick(this.refresh):(this.localBusy=!0,this.$nextTick((function(){try{var e=t.items(t.context,t._providerSetLocal);!jt(i=e)&&Gt(i.then)&&Gt(i.catch)?e.then((function(e){t._providerSetLocal(e)})):Kt(e)?t._providerSetLocal(e):2!==t.items.length&&(ye("Provider function didn't request callback and did not return a promise or data.",kn),t.localBusy=!1)}catch(e){ye("Provider function error [".concat(e.name,"] ").concat(e.message,"."),kn),t.localBusy=!1,t.$off(ur,t.refresh)}var i}))))}}}),ww=["range","multi","single"],Cw="grid",Sw={noSelectOnClick:Ds(kr,!1),selectMode:Ds(Fr,"multi",(function(t){return vo(ww,t)})),selectable:Ds(kr,!1),selectedVariant:Ds(Fr,"active")},kw=L({props:Sw,data:function(){return{selectedRows:[],selectedLastRow:-1}},computed:{isSelectable:function(){return this.selectable&&this.selectMode},hasSelectableRowClick:function(){return this.isSelectable&&!this.noSelectOnClick},supportsSelectableRows:function(){return!0},selectableHasSelection:function(){var t=this.selectedRows;return this.isSelectable&&t&&t.length>0&&t.some(pe)},selectableIsMultiSelect:function(){return this.isSelectable&&vo(["range","multi"],this.selectMode)},selectableTableClasses:function(){var t,e=this.isSelectable;return c(t={"b-table-selectable":e},"b-table-select-".concat(this.selectMode),e),c(t,"b-table-selecting",this.selectableHasSelection),c(t,"b-table-selectable-no-click",e&&!this.hasSelectableRowClick),t},selectableTableAttrs:function(){if(!this.isSelectable)return{};var t=this.bvAttrs.role||Cw;return{role:t,"aria-multiselectable":t===Cw?Io(this.selectableIsMultiSelect):null}}},watch:{computedItems:function(t,e){var i=!1;if(this.isSelectable&&this.selectedRows.length>0){i=Kt(t)&&Kt(e)&&t.length===e.length;for(var n=0;i&&n<t.length;n++)i=ll(rw(t[n]),rw(e[n]))}i||this.clearSelected()},selectable:function(t){this.clearSelected(),this.setSelectionHandlers(t)},selectMode:function(){this.clearSelected()},hasSelectableRowClick:function(t){this.clearSelected(),this.setSelectionHandlers(!t)},selectedRows:function(t,e){var i=this;if(this.isSelectable&&!ll(t,e)){var n=[];t.forEach((function(t,e){t&&n.push(i.computedItems[e])})),this.$emit("row-selected",n)}}},beforeMount:function(){this.isSelectable&&this.setSelectionHandlers(!0)},methods:{selectRow:function(t){if(this.isSelectable&&Yt(t)&&t>=0&&t<this.computedItems.length&&!this.isRowSelected(t)){var e=this.selectableIsMultiSelect?this.selectedRows.slice():[];e[t]=!0,this.selectedLastClicked=-1,this.selectedRows=e}},unselectRow:function(t){if(this.isSelectable&&Yt(t)&&this.isRowSelected(t)){var e=this.selectedRows.slice();e[t]=!1,this.selectedLastClicked=-1,this.selectedRows=e}},selectAllRows:function(){var t=this.computedItems.length;this.isSelectable&&t>0&&(this.selectedLastClicked=-1,this.selectedRows=this.selectableIsMultiSelect?bo(t,!0):[!0])},isRowSelected:function(t){return!(!Yt(t)||!this.selectedRows[t])},clearSelected:function(){this.selectedLastClicked=-1,this.selectedRows=[]},selectableRowClasses:function(t){if(this.isSelectable&&this.isRowSelected(t)){var e=this.selectedVariant;return c({"b-table-row-selected":!0},"".concat(this.dark?"bg":"table","-").concat(e),e)}return{}},selectableRowAttrs:function(t){return{"aria-selected":this.isSelectable?this.isRowSelected(t)?"true":"false":null}},setSelectionHandlers:function(t){var e=t&&!this.noSelectOnClick?"$on":"$off";this[e](dr,this.selectionHandler),this[e](Kn,this.clearSelected),this[e](jn,this.clearSelected)},selectionHandler:function(t,e,i){if(this.isSelectable&&!this.noSelectOnClick){var n=this.selectMode,r=this.selectedLastRow,o=this.selectedRows.slice(),s=!o[e];if("single"===n)o=[];else if("range"===n)if(r>-1&&i.shiftKey){for(var a=oa(r,e);a<=sa(r,e);a++)o[a]=!0;s=!0}else i.ctrlKey||i.metaKey||(o=[],s=!0),s&&(this.selectedLastRow=e);o[e]=s,this.selectedRows=o}else this.clearSelected()}}}),xw=function(t,e){return t.map((function(t,e){return[e,t]})).sort(function(t,e){return this(t[1],e[1])||t[0]-e[0]}.bind(e)).map((function(t){return t[1]}))},$w=function(t){return jt(t)?"":qt(t)?xo(t,t):t},Bw="sortBy",Dw="sortDesc",_w="asc",Fw="desc",Pw=[_w,Fw,"last"],Iw=(c(ST={labelSortAsc:Ds(Fr,"Click to sort ascending"),labelSortClear:Ds(Fr,"Click to clear sorting"),labelSortDesc:Ds(Fr,"Click to sort descending"),noFooterSorting:Ds(kr,!1),noLocalSorting:Ds(kr,!1),noSortReset:Ds(kr,!1)},Bw,Ds(Fr)),c(ST,"sortCompare",Ds($r)),c(ST,"sortCompareLocale",Ds(Er)),c(ST,"sortCompareOptions",Ds(Dr,{numeric:!0})),c(ST,Dw,Ds(kr,!1)),c(ST,"sortDirection",Ds(Fr,_w,(function(t){return vo(Pw,t)}))),c(ST,"sortIconLeft",Ds(kr,!1)),c(ST,"sortNullLast",Ds(kr,!1)),ST),Ow=L({props:Iw,data:function(){return{localSortBy:this.sortBy||"",localSortDesc:this.sortDesc||!1}},computed:{localSorting:function(){return this.hasProvider?!!this.noProviderSorting:!this.noLocalSorting},isSortable:function(){return this.computedFields.some((function(t){return t.sortable}))},sortedItems:function(){var t=va(this),e=t.localSortBy,i=t.localSortDesc,n=t.sortCompareLocale,o=t.sortNullLast,s=t.sortCompare,a=t.localSorting,l=t.filteredItems,c=t.localItems,u=(l||c||[]).slice(),d=r(r({},this.sortCompareOptions),{},{usage:"sort"});if(e&&a){var h=(this.computedFieldsObj[e]||{}).sortByFormatted,f=Gt(h)?h:h?this.getFieldFormatter(e):void 0;return xw(u,(function(t,r){var a=null;return Gt(s)&&(a=s(t,r,e,i,f,d,n)),(jt(a)||!1===a)&&(a=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=i.sortBy,r=void 0===n?null:n,o=i.formatter,s=void 0===o?null:o,a=i.locale,l=void 0===a?void 0:a,c=i.localeOptions,u=void 0===c?{}:c,d=i.nullLast,h=void 0!==d&&d,f=ve(t,r,null),p=ve(e,r,null);return Gt(s)&&(f=s(f,r,t),p=s(p,r,e)),f=$w(f),p=$w(p),Jt(f)&&Jt(p)||Yt(f)&&Yt(p)?f<p?-1:f>p?1:0:h&&""===f&&""!==p?1:h&&""!==f&&""===p?-1:JT(f).localeCompare(JT(p),l,u)}(t,r,{sortBy:e,formatter:f,locale:n,localeOptions:d,nullLast:o})),(a||0)*(i?-1:1)}))}return u}},watch:(kT={isSortable:function(t){t?this.isSortable&&this.$on(Qn,this.handleSort):this.$off(Qn,this.handleSort)}},c(kT,Dw,(function(t){t!==this.localSortDesc&&(this.localSortDesc=t||!1)})),c(kT,Bw,(function(t){t!==this.localSortBy&&(this.localSortBy=t||"")})),c(kT,"localSortDesc",(function(t,e){t!==e&&this.$emit("update:sortDesc",t)})),c(kT,"localSortBy",(function(t,e){t!==e&&this.$emit("update:sortBy",t)})),kT),created:function(){this.isSortable&&this.$on(Qn,this.handleSort)},methods:{handleSort:function(t,e,i,n){var r=this;if(this.isSortable&&(!n||!this.noFooterSorting)){var o=!1,s=function(){var t=e.sortDirection||r.sortDirection;t===_w?r.localSortDesc=!1:t===Fw&&(r.localSortDesc=!0)};if(e.sortable){var a=!this.localSorting&&e.sortKey?e.sortKey:t;this.localSortBy===a?this.localSortDesc=!this.localSortDesc:(this.localSortBy=a,s()),o=!0}else this.localSortBy&&!this.noSortReset&&(this.localSortBy="",s(),o=!0);o&&this.$emit("sort-changed",this.context)}},sortTheadThClasses:function(t,e,i){return{"b-table-sort-icon-left":e.sortable&&this.sortIconLeft&&!(i&&this.noFooterSorting)}},sortTheadThAttrs:function(t,e,i){var n,r=this.isSortable,o=this.noFooterSorting,s=this.localSortDesc,a=this.localSortBy,l=this.localSorting;if(!r||i&&o)return{};var c=e.sortable,u=l?t:null!==(n=e.sortKey)&&void 0!==n?n:t;return{"aria-sort":c&&a===u?s?"descending":"ascending":c?"none":null}},sortTheadThLabel:function(t,e,i){if(!this.isSortable||i&&this.noFooterSorting)return null;var n=this.localSortBy,r=this.localSortDesc,o=this.labelSortAsc,s=this.labelSortDesc,a="";if(e.sortable)if(n===t)a=r?o:s;else{a=r?s:o;var l=this.sortDirection||e.sortDirection;l===_w?a=o:l===Fw&&(a=s)}else this.noSortReset||(a=n?this.labelSortClear:"");return Oo(a)||null}}}),Ew={stacked:Ds(Rr,!1)},Vw=L({props:Ew,computed:{isStacked:function(){var t=this.stacked;return""===t||t},isStackedAlways:function(){return!0===this.isStacked},stackedTableClasses:function(){var t=this.isStackedAlways;return c({"b-table-stacked":t},"b-table-stacked-".concat(this.stacked),!t&&this.isStacked)}}}),Lw={bordered:Ds(kr,!1),borderless:Ds(kr,!1),captionTop:Ds(kr,!1),dark:Ds(kr,!1),fixed:Ds(kr,!1),hover:Ds(kr,!1),noBorderCollapse:Ds(kr,!1),outlined:Ds(kr,!1),responsive:Ds(Rr,!1),small:Ds(kr,!1),stickyHeader:Ds(Rr,!1),striped:Ds(kr,!1),tableClass:Ds(Or),tableVariant:Ds(Fr)},Rw=L({mixins:[pl],provide:function(){var t=this;return{getBvTable:function(){return t}}},inheritAttrs:!1,props:Lw,computed:{isTableSimple:function(){return!1},isResponsive:function(){var t=this.responsive;return""===t||t},isStickyHeader:function(){var t=this.stickyHeader;return t=""===t||t,!this.isStacked&&t},wrapperClasses:function(){var t=this.isResponsive;return[this.isStickyHeader?"b-table-sticky-header":"",!0===t?"table-responsive":t?"table-responsive-".concat(this.responsive):""].filter(pe)},wrapperStyles:function(){var t=this.isStickyHeader;return t&&!Wt(t)?{maxHeight:t}:{}},tableClasses:function(){var t=va(this),e=t.hover,i=t.tableVariant,n=t.selectableTableClasses,r=t.stackedTableClasses,o=t.tableClass,s=t.computedBusy;return e=this.isTableSimple?e:e&&this.computedItems.length>0&&!s,[o,{"table-striped":this.striped,"table-hover":e,"table-dark":this.dark,"table-bordered":this.bordered,"table-borderless":this.borderless,"table-sm":this.small,border:this.outlined,"b-table-fixed":this.fixed,"b-table-caption-top":this.captionTop,"b-table-no-border-collapse":this.noBorderCollapse},i?"".concat(this.dark?"bg":"table","-").concat(i):"",r,n]},tableAttrs:function(){var t=va(this),e=t.computedItems,i=t.filteredItems,n=t.computedFields,o=t.selectableTableAttrs,s=t.computedBusy,a=this.isTableSimple?{}:{"aria-busy":Io(s),"aria-colcount":Io(n.length),"aria-describedby":this.bvAttrs["aria-describedby"]||this.$refs.caption?this.captionId:null};return r(r(r({"aria-rowcount":e&&i&&i.length>e.length?Io(i.length):null},this.bvAttrs),{},{id:this.safeId(),role:this.bvAttrs.role||"table"},a),o)}},render:function(t){var e=va(this),i=e.wrapperClasses,n=e.renderCaption,r=e.renderColgroup,o=e.renderThead,s=e.renderTbody,a=e.renderTfoot,l=[];this.isTableSimple?l.push(this.normalizeSlot()):(l.push(n?n():null),l.push(r?r():null),l.push(o?o():null),l.push(s?s():null),l.push(a?a():null));var c=t("table",{staticClass:"table b-table",class:this.tableClasses,attrs:this.tableAttrs,key:"b-table"},l.filter(pe));return i.length>0?t("div",{class:i,style:this.wrapperStyles,key:"wrap"},[c]):c}}),Aw=Is({tbodyTransitionHandlers:Ds(Dr),tbodyTransitionProps:Ds(Dr)},_n),Mw=L({name:_n,mixins:[pl,Tl,So],provide:function(){var t=this;return{getBvTableRowGroup:function(){return t}}},inject:{getBvTable:{default:function(){return function(){return{}}}}},inheritAttrs:!1,props:Aw,computed:{bvTable:function(){return this.getBvTable()},isTbody:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!1},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},isTransitionGroup:function(){return this.tbodyTransitionProps||this.tbodyTransitionHandlers},tbodyAttrs:function(){return r({role:"rowgroup"},this.bvAttrs)},tbodyProps:function(){var t=this.tbodyTransitionProps;return t?r(r({},t),{},{tag:"tbody"}):{}}},render:function(t){var e={props:this.tbodyProps,attrs:this.tbodyAttrs};return this.isTransitionGroup?(e.on=this.tbodyTransitionHandlers||{},e.nativeOn=this.bvListeners):e.on=this.bvListeners,t(this.isTransitionGroup?"transition-group":"tbody",e,this.normalizeSlot())}}),Hw=["TD","TH","TR"],zw=function(t){if(!t||!t.target)return!1;var e=t.target;if(e.disabled||-1!==Hw.indexOf(e.tagName))return!1;if(Zo(".dropdown-menu",e))return!0;var i="LABEL"===e.tagName?e:Zo("label",e);if(i){var n=os(i,"for"),r=n?Qo(n):Ko("input, select, textarea",i);if(r&&!r.disabled)return!0}return Xo(e,nw)},Nw=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,e=hs();return!!(e&&""!==e.toString().trim()&&e.containsNode&&zo(t))&&e.containsNode(t,!0)},jw=Is(zT,"BTh"),Gw=L({name:"BTh",extends:NT,props:jw,computed:{tag:function(){return"th"}}}),Ww={detailsTdClass:Ds(Or),tbodyTrAttr:Ds(Nr),tbodyTrClass:Ds([].concat(S(Or),[$r]))},Uw=L({mixins:[Og],props:Ww,methods:{getTdValues:function(t,e,i,n){var r=this.bvParent;if(i){var o=ve(t,e,"");return Gt(i)?i(o,e,t):Ut(i)&&Gt(r[i])?r[i](o,e,t):i}return n},getThValues:function(t,e,i,n,r){var o=this.bvParent;if(i){var s=ve(t,e,"");return Gt(i)?i(s,e,t,n):Ut(i)&&Gt(o[i])?o[i](s,e,t,n):i}return r},getFormattedValue:function(t,e){var i=e.key,n=this.getFieldFormatter(i),r=ve(t,i,null);return Gt(n)&&(r=n(r,i,t)),jt(r)?"":r},toggleDetailsFactory:function(t,e){var i=this;return function(){t&&i.$set(e,ew,!e[ew])}},rowHovered:function(t){this.tbodyRowEventStopped(t)||this.emitTbodyRowEvent("row-hovered",t)},rowUnhovered:function(t){this.tbodyRowEventStopped(t)||this.emitTbodyRowEvent("row-unhovered",t)},renderTbodyRowCell:function(t,e,i,n){var o=this,s=this.isStacked,a=t.key,l=t.label,c=t.isRowHeader,u=this.$createElement,d=this.hasNormalizedSlot(ao),h=this.getFormattedValue(i,t),f=!s&&(this.isResponsive||this.stickyHeader)&&t.stickyColumn,p=f?c?Gw:NT:c?"th":"td",m=i[QT]&&i[QT][a]?i[QT][a]:t.variant||null,v={class:[t.class?t.class:"",this.getTdValues(i,a,t.tdClass,"")],props:{},attrs:r({"aria-colindex":String(e+1)},c?this.getThValues(i,a,t.thAttr,"row",{}):this.getTdValues(i,a,t.tdAttr,{})),key:"row-".concat(n,"-cell-").concat(e,"-").concat(a)};f?v.props={stackedHeading:s?l:null,stickyColumn:!0,variant:m}:(v.attrs["data-label"]=s&&!jt(l)?Io(l):null,v.attrs.role=c?"rowheader":"cell",v.attrs.scope=c?"row":null,m&&v.class.push("".concat(this.dark?"bg":"table","-").concat(m)));var g={item:i,index:n,field:t,unformatted:ve(i,a,""),value:h,toggleDetails:this.toggleDetailsFactory(d,i),detailsShowing:Boolean(i[ew])};va(this).supportsSelectableRows&&(g.rowSelected=this.isRowSelected(n),g.selectRow=function(){return o.selectRow(n)},g.unselectRow=function(){return o.unselectRow(n)});var b=this.$_bodyFieldSlotNameCache[a],y=b?this.normalizeSlot(b,g):Io(h);return this.isStacked&&(y=[u("div",[y])]),u(p,v,[y])},renderTbodyRow:function(t,e){var i=this,n=va(this),o=n.computedFields,s=n.striped,a=n.primaryKey,l=n.currentPage,u=n.perPage,d=n.tbodyTrClass,h=n.tbodyTrAttr,f=n.hasSelectableRowClick,p=this.$createElement,m=this.hasNormalizedSlot(ao),v=t[ew]&&m,g=this.$listeners["row-clicked"]||f,b=[],y=v?this.safeId("_details_".concat(e,"_")):null,T=o.map((function(n,r){return i.renderTbodyRowCell(n,r,t,e)})),w=null;l&&u&&u>0&&(w=String((l-1)*u+e+1));var C=Io(ve(t,a))||null,S=C||Io(e),k=C?this.safeId("_row_".concat(C)):null,x=va(this).selectableRowClasses?this.selectableRowClasses(e):{},$=va(this).selectableRowAttrs?this.selectableRowAttrs(e):{},B=Gt(d)?d(t,"row"):d,D=Gt(h)?h(t,"row"):h;if(b.push(p(LT,c({class:[B,x,v?"b-table-has-details":""],props:{variant:t[tw]||null},attrs:r(r({id:k},D),{},{tabindex:g?"0":null,"data-pk":C||null,"aria-details":y,"aria-owns":y,"aria-rowindex":w},$),on:{mouseenter:this.rowHovered,mouseleave:this.rowUnhovered},key:"__b-table-row-".concat(S,"__"),ref:"item-rows"},E,!0),T)),v){var _={item:t,index:e,fields:o,toggleDetails:this.toggleDetailsFactory(m,t)};va(this).supportsSelectableRows&&(_.rowSelected=this.isRowSelected(e),_.selectRow=function(){return i.selectRow(e)},_.unselectRow=function(){return i.unselectRow(e)});var F=p(NT,{props:{colspan:o.length},class:this.detailsTdClass},[this.normalizeSlot(ao,_)]);s&&b.push(p("tr",{staticClass:"d-none",attrs:{"aria-hidden":"true",role:"presentation"},key:"__b-table-details-stripe__".concat(S)}));var P=Gt(this.tbodyTrClass)?this.tbodyTrClass(t,ao):this.tbodyTrClass,I=Gt(this.tbodyTrAttr)?this.tbodyTrAttr(t,ao):this.tbodyTrAttr;b.push(p(LT,{staticClass:"b-table-details",class:[P],props:{variant:t[tw]||null},attrs:r(r({},I),{},{id:y,tabindex:"-1"}),key:"__b-table-details__".concat(S)},[F]))}else m&&(b.push(p()),s&&b.push(p()));return b}}}),Yw=function(t){return"cell(".concat(t||"",")")},qw=he(r(r(r({},Aw),Ww),{},{tbodyClass:Ds(Or)})),Kw=L({mixins:[Uw],props:qw,beforeDestroy:function(){this.$_bodyFieldSlotNameCache=null},methods:{getTbodyTrs:function(){var t=this.$refs,e=t.tbody?t.tbody.$el||t.tbody:null,i=(t["item-rows"]||[]).map((function(t){return t.$el||t}));return e&&e.children&&e.children.length>0&&i&&i.length>0?mo(e.children).filter((function(t){return vo(i,t)})):[]},getTbodyTrIndex:function(t){if(!zo(t))return-1;var e="TR"===t.tagName?t:Zo("tr",t,!0);return e?this.getTbodyTrs().indexOf(e):-1},emitTbodyRowEvent:function(t,e){if(t&&this.hasListener(t)&&e&&e.target){var i=this.getTbodyTrIndex(e.target);if(i>-1){var n=this.computedItems[i];this.$emit(t,n,i,e)}}},tbodyRowEventStopped:function(t){return this.stopIfBusy&&this.stopIfBusy(t)},onTbodyRowKeydown:function(t){var e=t.target,i=t.keyCode;if(!this.tbodyRowEventStopped(t)&&"TR"===e.tagName&&Go(e)&&0===e.tabIndex)if(vo([el,ol],i))Hs(t),this.onTBodyRowClicked(t);else if(vo([sl,Qa,il,tl],i)){var n=this.getTbodyTrIndex(e);if(n>-1){Hs(t);var r=this.getTbodyTrs(),o=t.shiftKey;i===il||o&&i===sl?vs(r[0]):i===tl||o&&i===Qa?vs(r[r.length-1]):i===sl&&n>0?vs(r[n-1]):i===Qa&&n<r.length-1&&vs(r[n+1])}}},onTBodyRowClicked:function(t){var e=this.$refs,i=e.tbody?e.tbody.$el||e.tbody:null;this.tbodyRowEventStopped(t)||zw(t)||Nw(i||this.$el)||this.emitTbodyRowEvent(dr,t)},onTbodyRowMiddleMouseRowClicked:function(t){this.tbodyRowEventStopped(t)||2!==t.which||this.emitTbodyRowEvent("row-middle-clicked",t)},onTbodyRowContextmenu:function(t){this.tbodyRowEventStopped(t)||this.emitTbodyRowEvent("row-contextmenu",t)},onTbodyRowDblClicked:function(t){this.tbodyRowEventStopped(t)||zw(t)||this.emitTbodyRowEvent("row-dblclicked",t)},renderTbody:function(){var t=this,e=va(this),i=e.computedItems,n=e.renderBusy,r=e.renderTopRow,o=e.renderEmpty,s=e.renderBottomRow,a=e.hasSelectableRowClick,l=this.$createElement,c=this.hasListener(dr)||a,u=[],d=n?n():null;if(d)u.push(d);else{var h={},f=Yw();f=this.hasNormalizedSlot(f)?f:null,this.computedFields.forEach((function(e){var i=e.key,n=Yw(i),r=Yw(i.toLowerCase());h[i]=t.hasNormalizedSlot(n)?n:t.hasNormalizedSlot(r)?r:f})),this.$_bodyFieldSlotNameCache=h,u.push(r?r():l()),i.forEach((function(e,i){u.push(t.renderTbodyRow(e,i))})),u.push(o?o():l()),u.push(s?s():l())}var p={auxclick:this.onTbodyRowMiddleMouseRowClicked,contextmenu:this.onTbodyRowContextmenu,dblclick:this.onTbodyRowDblClicked};return c&&(p.click=this.onTBodyRowClicked,p.keydown=this.onTbodyRowKeydown),l(Mw,{class:this.tbodyClass||null,props:Fs(Aw,this.$props),on:p,ref:"tbody"},u)}}}),Xw=Is({footVariant:Ds(Fr)},Fn),Zw=L({name:Fn,mixins:[pl,Tl,So],provide:function(){var t=this;return{getBvTableRowGroup:function(){return t}}},inject:{getBvTable:{default:function(){return function(){return{}}}}},inheritAttrs:!1,props:Xw,computed:{bvTable:function(){return this.getBvTable()},isTfoot:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!1},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},tfootClasses:function(){return[this.footVariant?"thead-".concat(this.footVariant):null]},tfootAttrs:function(){return r(r({},this.bvAttrs),{},{role:"rowgroup"})}},render:function(t){return t("tfoot",{class:this.tfootClasses,attrs:this.tfootAttrs,on:this.bvListeners},this.normalizeSlot())}}),Jw={footClone:Ds(kr,!1),footRowVariant:Ds(Fr),footVariant:Ds(Fr),tfootClass:Ds(Or),tfootTrClass:Ds(Or)},Qw=L({props:Jw,methods:{renderTFootCustom:function(){var t=this.$createElement;return this.hasNormalizedSlot(qr)?t(Zw,{class:this.tfootClass||null,props:{footVariant:this.footVariant||this.headVariant||null},key:"bv-tfoot-custom"},this.normalizeSlot(qr,{items:this.computedItems.slice(),fields:this.computedFields.slice(),columns:this.computedFields.length})):t()},renderTfoot:function(){return this.footClone?this.renderThead(!0):this.renderTFootCustom()}}}),tC=Is({headVariant:Ds(Fr)},Pn),eC=L({name:Pn,mixins:[pl,Tl,So],provide:function(){var t=this;return{getBvTableRowGroup:function(){return t}}},inject:{getBvTable:{default:function(){return function(){return{}}}}},inheritAttrs:!1,props:tC,computed:{bvTable:function(){return this.getBvTable()},isThead:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},theadClasses:function(){return[this.headVariant?"thead-".concat(this.headVariant):null]},theadAttrs:function(){return r({role:"rowgroup"},this.bvAttrs)}},render:function(t){return t("thead",{class:this.theadClasses,attrs:this.theadAttrs,on:this.bvListeners},this.normalizeSlot())}}),iC=function(t){return"head(".concat(t||"",")")},nC=function(t){return"foot(".concat(t||"",")")},rC={headRowVariant:Ds(Fr),headVariant:Ds(Fr),theadClass:Ds(Or),theadTrClass:Ds(Or)},oC=L({props:rC,methods:{fieldClasses:function(t){return[t.class?t.class:"",t.thClass?t.thClass:""]},headClicked:function(t,e,i){this.stopIfBusy&&this.stopIfBusy(t)||zw(t)||Nw(this.$el)||(Hs(t),this.$emit(Qn,e.key,e,t,i))},renderThead:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=va(this),n=i.computedFields,o=i.isSortable,s=i.isSelectable,a=i.headVariant,l=i.footVariant,c=i.headRowVariant,u=i.footRowVariant,d=this.$createElement;if(this.isStackedAlways||0===n.length)return d();var h=o||this.hasListener(Qn),f=s?this.selectAllRows:_u,p=s?this.clearSelected:_u,m=function(i,n){var s=i.label,a=i.labelHtml,l=i.variant,c=i.stickyColumn,u=i.key,m=null;i.label.trim()||i.headerTitle||(m=_o(i.key));var v={};h&&(v.click=function(n){t.headClicked(n,i,e)},v.keydown=function(n){var r=n.keyCode;r!==el&&r!==ol||t.headClicked(n,i,e)});var g=o?t.sortTheadThAttrs(u,i,e):{},b=o?t.sortTheadThClasses(u,i,e):null,y=o?t.sortTheadThLabel(u,i,e):null,T={class:[{"position-relative":y},t.fieldClasses(i),b],props:{variant:l,stickyColumn:c},style:i.thStyle||{},attrs:r(r({tabindex:h&&i.sortable?"0":null,abbr:i.headerAbbr||null,title:i.headerTitle||null,"aria-colindex":n+1,"aria-label":m},t.getThValues(null,u,i.thAttr,e?"foot":"head",{})),g),on:v,key:u},w=[iC(u),iC(u.toLowerCase()),iC()];e&&(w=[nC(u),nC(u.toLowerCase()),nC()].concat(S(w)));var C={label:s,column:u,field:i,isFoot:e,selectAllRows:f,clearSelected:p},k=t.normalizeSlot(w,C)||d("div",{domProps:Zl(a,s)}),x=y?d("span",{staticClass:"sr-only"}," (".concat(y,")")):null;return d(Gw,T,[k,x].filter(pe))},v=n.map(m).filter(pe),g=[];if(e)g.push(d(LT,{class:this.tfootTrClass,props:{variant:jt(u)?c:u}},v));else{var b={columns:n.length,fields:n,selectAllRows:f,clearSelected:p};g.push(this.normalizeSlot(ho,b)||d()),g.push(d(LT,{class:this.theadTrClass,props:{variant:c}},v))}return d(e?Zw:eC,{class:(e?this.tfootClass:this.theadClass)||null,props:e?{footVariant:l||a||null}:{headVariant:a||null},key:e?"bv-tfoot":"bv-thead"},g)}}}),sC=L({methods:{renderTopRow:function(){var t=this.computedFields,e=this.stacked,i=this.tbodyTrClass,n=this.tbodyTrAttr,r=this.$createElement;return this.hasNormalizedSlot(po)&&!0!==e&&""!==e?r(LT,{staticClass:"b-table-top-row",class:[Gt(i)?i(null,"row-top"):i],attrs:Gt(n)?n(null,"row-top"):n,key:"b-top-row"},[this.normalizeSlot(po,{columns:t.length,fields:t})]):r()}}}),aC=Is(he(r(r(r(r(r(r(r(r(r(r(r(r(r(r(r(r(r(r({},Vc),RT),GT),UT),qT),XT),ow),fw),mw),yw),Sw),Iw),Ew),Lw),qw),Jw),rC),{})),kn),lC=L({name:kn,mixins:[pl,IT,Lc,So,pw,Rw,Vw,oC,Qw,Kw,Vw,sw,Ow,vw,YT,KT,kw,ZT,sC,AT,WT,Tw],props:aC}),cC=Is(he(r(r(r(r(r(r(r(r(r({},Vc),UT),qT),fw),Ew),Lw),qw),Jw),rC)),$n),uC=L({name:$n,mixins:[pl,IT,Lc,So,pw,Rw,Vw,oC,Qw,Kw,YT,KT],props:cC}),dC=Is(he(r(r(r({},Vc),Ew),Lw)),Bn),hC=L({name:Bn,mixins:[pl,IT,Lc,So,Rw,Vw],props:dC,computed:{isTableSimple:function(){return!0}}}),fC=$e({components:{BTable:lC},plugins:{TableLitePlugin:$e({components:{BTableLite:uC}}),TableSimplePlugin:$e({components:{BTableSimple:hC,BTbody:Mw,BThead:eC,BTfoot:Zw,BTr:LT,BTd:NT,BTh:Gw}})}}),pC=function(t){return t>0},mC=Is({animation:Ds(Fr),columns:Ds(Br,5,pC),hideHeader:Ds(kr,!1),rows:Ds(Br,3,pC),showFooter:Ds(kr,!1),tableProps:Ds(Dr,{})},Tn),vC=L({name:Tn,functional:!0,props:mC,render:function(t,e){var i=e.data,n=e.props,o=n.animation,s=n.columns,a=t("th",[t(CT,{props:{animation:o}})]),l=t("tr",bo(s,a)),c=t("td",[t(CT,{props:{width:"75%",animation:o}})]),u=t("tr",bo(s,c)),d=t("tbody",bo(n.rows,u)),h=n.hideHeader?t():t("thead",[l]),f=n.showFooter?t("tfoot",[l]):t();return t(hC,I(i,{props:r({},n.tableProps)}),[h,d,f])}}),gC=Is({loading:Ds(kr,!1)},wn),bC=L({name:wn,functional:!0,props:gC,render:function(t,e){var i=e.data,n=e.props,r=e.slots,o=e.scopedSlots,s=r(),a=o||{},l={};return n.loading?t("div",I(i,{attrs:{role:"alert","aria-live":"polite","aria-busy":!0},staticClass:"b-skeleton-wrapper",key:"loading"}),Co("loading",l,a,s)):Co(Kr,l,a,s)}}),yC=$e({components:{BSkeleton:CT,BSkeletonIcon:_T,BSkeletonImg:PT,BSkeletonTable:vC,BSkeletonWrapper:bC}}),TC=$e({components:{BSpinner:Ub}}),wC=Vs("value",{type:Br}),CC=wC.mixin,SC=wC.props,kC=wC.prop,xC=wC.event,$C=function(t){return!t.disabled},BC=L({name:"BVTabButton",inject:{getBvTabs:{default:function(){return function(){return{}}}}},props:{controls:Ds(Fr),id:Ds(Fr),noKeyNav:Ds(kr,!1),posInSet:Ds(Br),setSize:Ds(Br),tab:Ds(),tabIndex:Ds(Br)},computed:{bvTabs:function(){return this.getBvTabs()}},methods:{focus:function(){vs(this.$refs.link)},handleEvent:function(t){if(!this.tab.disabled){var e=t.type,i=t.keyCode,n=t.shiftKey;"click"===e||"keydown"===e&&i===ol?(Hs(t),this.$emit(Hn,t)):"keydown"!==e||this.noKeyNav||(-1!==[sl,nl,il].indexOf(i)?(Hs(t),n||i===il?this.$emit(Xn,t):this.$emit(cr,t)):-1!==[Qa,rl,tl].indexOf(i)&&(Hs(t),n||i===tl?this.$emit(nr,t):this.$emit(sr,t)))}}},render:function(t){var e=this.id,i=this.tabIndex,n=this.setSize,o=this.posInSet,s=this.controls,a=this.handleEvent,l=this.tab,c=l.title,u=l.localActive,d=l.disabled,h=l.titleItemClass,f=l.titleLinkClass,p=l.titleLinkAttributes,m=t(xl,{staticClass:"nav-link",class:[{active:u&&!d,disabled:d},f,u?this.bvTabs.activeNavItemClass:null],props:{disabled:d},attrs:r(r({},p),{},{id:e,role:"tab",tabindex:i,"aria-selected":u&&!d?"true":"false","aria-setsize":n,"aria-posinset":o,"aria-controls":s}),on:{click:a,keydown:a},ref:"link"},[this.tab.normalizeSlot(fo)||c]);return t("li",{staticClass:"nav-item",class:[h],attrs:{role:"presentation"}},[m])}}),DC=ue(vb,["tabs","isNavBar","cardHeader"]),_C=Is(he(r(r(r(r({},Vc),SC),DC),{},{activeNavItemClass:Ds(Or),activeTabClass:Ds(Or),card:Ds(kr,!1),contentClass:Ds(Or),end:Ds(kr,!1),lazy:Ds(kr,!1),navClass:Ds(Or),navWrapperClass:Ds(Or),noFade:Ds(kr,!1),noKeyNav:Ds(kr,!1),noNavStyle:Ds(kr,!1),tag:Ds(Fr,"div")})),Dn),FC=L({name:Dn,mixins:[Lc,CC,So],provide:function(){var t=this;return{getBvTabs:function(){return t}}},props:_C,data:function(){return{currentTab:ko(this[kC],-1),tabs:[],registeredTabs:[]}},computed:{fade:function(){return!this.noFade},localNavClass:function(){var t=[];return this.card&&this.vertical&&t.push("card-header","h-100","border-bottom-0","rounded-0"),[].concat(t,[this.navClass])}},watch:(xT={},c(xT,kC,(function(t,e){if(t!==e){t=ko(t,-1),e=ko(e,0);var i=this.tabs[t];i&&!i.disabled?this.activateTab(i):t<e?this.previousTab():this.nextTab()}})),c(xT,"currentTab",(function(t){var e=-1;this.tabs.forEach((function(i,n){n!==t||i.disabled?i.localActive=!1:(i.localActive=!0,e=n)})),this.$emit(xC,e)})),c(xT,"tabs",(function(t,e){var i=this;ll(t.map((function(t){return t._uid})),e.map((function(t){return t._uid})))||this.$nextTick((function(){i.$emit("changed",t.slice(),e.slice())}))})),c(xT,"registeredTabs",(function(){this.updateTabs()})),xT),created:function(){this.$_observer=null},mounted:function(){this.setObserver(!0)},beforeDestroy:function(){this.setObserver(!1),this.tabs=[]},methods:{registerTab:function(t){vo(this.registeredTabs,t)||this.registeredTabs.push(t)},unregisterTab:function(t){this.registeredTabs=this.registeredTabs.slice().filter((function(e){return e!==t}))},setObserver:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,e){var i=function(){t.$nextTick((function(){Mo((function(){t.updateTabs()}))}))};this.$_observer=Fu(this.$refs.content,i,{childList:!0,subtree:!1,attributes:!0,attributeFilter:["id"]})}},getTabs:function(){var t=this.registeredTabs,e=[];if(Y&&t.length>0){var i=t.map((function(t){return"#".concat(t.safeId())})).join(", ");e=qo(i,this.$el).map((function(t){return t.id})).filter(pe)}return xw(t,(function(t,i){return e.indexOf(t.safeId())-e.indexOf(i.safeId())}))},updateTabs:function(){var t=this.getTabs(),e=t.indexOf(t.slice().reverse().find((function(t){return t.localActive&&!t.disabled})));if(e<0){var i=this.currentTab;i>=t.length?e=t.indexOf(t.slice().reverse().find($C)):t[i]&&!t[i].disabled&&(e=i)}e<0&&(e=t.indexOf(t.find($C))),t.forEach((function(t,i){t.localActive=i===e})),this.tabs=t,this.currentTab=e},getButtonForTab:function(t){return(this.$refs.buttons||[]).find((function(e){return e.tab===t}))},updateButton:function(t){var e=this.getButtonForTab(t);e&&e.$forceUpdate&&e.$forceUpdate()},activateTab:function(t){var e=this.currentTab,i=this.tabs,n=!1;if(t){var r=i.indexOf(t);if(r!==e&&r>-1&&!t.disabled){var o=new BvEvent("activate-tab",{cancelable:!0,vueTarget:this,componentId:this.safeId()});this.$emit(o.type,r,e,o),o.defaultPrevented||(this.currentTab=r,n=!0)}}return n||this[kC]===e||this.$emit(xC,e),n},deactivateTab:function(t){return!!t&&this.activateTab(this.tabs.filter((function(e){return e!==t})).find($C))},focusButton:function(t){var e=this;this.$nextTick((function(){vs(e.getButtonForTab(t))}))},emitTabClick:function(t,e){Qt(e)&&t&&t.$emit&&!t.disabled&&t.$emit(Hn,e)},clickTab:function(t,e){this.activateTab(t),this.emitTabClick(t,e)},firstTab:function(t){var e=this.tabs.find($C);this.activateTab(e)&&t&&(this.focusButton(e),this.emitTabClick(e,t))},previousTab:function(t){var e=sa(this.currentTab,0),i=this.tabs.slice(0,e).reverse().find($C);this.activateTab(i)&&t&&(this.focusButton(i),this.emitTabClick(i,t))},nextTab:function(t){var e=sa(this.currentTab,-1),i=this.tabs.slice(e+1).find($C);this.activateTab(i)&&t&&(this.focusButton(i),this.emitTabClick(i,t))},lastTab:function(t){var e=this.tabs.slice().reverse().find($C);this.activateTab(e)&&t&&(this.focusButton(e),this.emitTabClick(e,t))}},render:function(t){var e=this,i=this.align,n=this.card,r=this.end,o=this.fill,s=this.firstTab,a=this.justified,l=this.lastTab,u=this.nextTab,d=this.noKeyNav,h=this.noNavStyle,f=this.pills,p=this.previousTab,m=this.small,v=this.tabs,g=this.vertical,b=v.find((function(t){return t.localActive&&!t.disabled})),y=v.find((function(t){return!t.disabled})),T=v.map((function(i,n){var r,o=i.safeId,a=null;return d||(a=-1,(i===b||!b&&i===y)&&(a=null)),t(BC,c({props:{controls:o?o():null,id:i.controlledBy||(o?o("_BV_tab_button_"):null),noKeyNav:d,posInSet:n+1,setSize:v.length,tab:i,tabIndex:a},on:(r={},c(r,Hn,(function(t){e.clickTab(i,t)})),c(r,Xn,s),c(r,cr,p),c(r,sr,u),c(r,nr,l),r),key:i._uid||n,ref:"buttons"},E,!0))})),w=t(gb,{class:this.localNavClass,attrs:{role:"tablist",id:this.safeId("_BV_tab_controls_")},props:{fill:o,justified:a,align:i,tabs:!h&&!f,pills:!h&&f,vertical:g,small:m,cardHeader:n&&!g},ref:"nav"},[this.normalizeSlot("tabs-start")||t(),T,this.normalizeSlot("tabs-end")||t()]);w=t("div",{class:[{"card-header":n&&!g&&!r,"card-footer":n&&!g&&r,"col-auto":g},this.navWrapperClass],key:"bv-tabs-nav"},[w]);var C=this.normalizeSlot()||[],S=t();0===C.length&&(S=t("div",{class:["tab-pane","active",{"card-body":n}],key:"bv-empty-tab"},this.normalizeSlot(Xr)));var k=t("div",{staticClass:"tab-content",class:[{col:g},this.contentClass],attrs:{id:this.safeId("_BV_tab_container_")},key:"bv-content",ref:"content"},[C,S]);return t(this.tag,{staticClass:"tabs",class:{row:g,"no-gutters":g&&n},attrs:{id:this.safeId()}},[r?k:t(),w,r?t():k])}}),PC="active",IC="update:active",OC=Is(he(r(r({},Vc),{},(c($T={},PC,Ds(kr,!1)),c($T,"buttonId",Ds(Fr)),c($T,"disabled",Ds(kr,!1)),c($T,"lazy",Ds(kr,!1)),c($T,"noBody",Ds(kr,!1)),c($T,"tag",Ds(Fr,"div")),c($T,"title",Ds(Fr)),c($T,"titleItemClass",Ds(Or)),c($T,"titleLinkAttributes",Ds(Dr)),c($T,"titleLinkClass",Ds(Or)),$T))),Sn),EC=$e({components:{BTabs:FC,BTab:L({name:Sn,mixins:[Lc,So],inject:{getBvTabs:{default:function(){return function(){return{}}}}},props:OC,data:function(){return{localActive:this.active&&!this.disabled}},computed:{bvTabs:function(){return this.getBvTabs()},_isTab:function(){return!0},tabClasses:function(){var t=this.localActive;return[{active:t,disabled:this.disabled,"card-body":this.bvTabs.card&&!this.noBody},t?this.bvTabs.activeTabClass:null]},controlledBy:function(){return this.buttonId||this.safeId("__BV_tab_button__")},computedNoFade:function(){return!this.bvTabs.fade},computedLazy:function(){return this.bvTabs.lazy||this.lazy}},watch:(BT={},c(BT,PC,(function(t,e){t!==e&&(t?this.activate():this.deactivate()||this.$emit(IC,this.localActive))})),c(BT,"disabled",(function(t,e){if(t!==e){var i=this.bvTabs.firstTab;t&&this.localActive&&i&&(this.localActive=!1,i())}})),c(BT,"localActive",(function(t){this.$emit(IC,t)})),BT),mounted:function(){this.registerTab()},updated:function(){var t=this.bvTabs.updateButton;t&&this.hasNormalizedSlot(fo)&&t(this)},beforeDestroy:function(){this.unregisterTab()},methods:{registerTab:function(){var t=this.bvTabs.registerTab;t&&t(this)},unregisterTab:function(){var t=this.bvTabs.unregisterTab;t&&t(this)},activate:function(){var t=this.bvTabs.activateTab;return!(!t||this.disabled)&&t(this)},deactivate:function(){var t=this.bvTabs.deactivateTab;return!(!t||!this.localActive)&&t(this)}},render:function(t){var e=this.localActive,i=t(this.tag,{staticClass:"tab-pane",class:this.tabClasses,directives:[{name:"show",value:e}],attrs:{role:"tabpanel",id:this.safeId(),"aria-hidden":e?"false":"true","aria-labelledby":this.controlledBy||null},ref:"panel"},[e||!this.computedLazy?this.normalizeSlot():t()]);return t(Ks,{props:{mode:"out-in",noFade:this.computedNoFade}},[i])}})}}),VC=$e({components:{BTime:Ev}});function LC(t){return LC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},LC(t)}function RC(t){return function(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var AC="undefined"!=typeof window;function MC(t,e){return e.reduce((function(e,i){return t.hasOwnProperty(i)&&(e[i]=t[i]),e}),{})}var HC={},zC={},NC={},jC=i.default.extend({data:function(){return{transports:HC,targets:zC,sources:NC,trackInstances:AC}},methods:{open:function(t){if(AC){var e=t.to,n=t.from,r=t.passengers,o=t.order,s=void 0===o?1/0:o;if(e&&n&&r){var a,l={to:e,from:n,passengers:(a=r,Array.isArray(a)||"object"===LC(a)?Object.freeze(a):a),order:s};-1===Object.keys(this.transports).indexOf(e)&&i.default.set(this.transports,e,[]);var c,u=this.$_getTransportIndex(l),d=this.transports[e].slice(0);-1===u?d.push(l):d[u]=l,this.transports[e]=(c=function(t,e){return t.order-e.order},d.map((function(t,e){return[e,t]})).sort((function(t,e){return c(t[1],e[1])||t[0]-e[0]})).map((function(t){return t[1]})))}}},close:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=t.to,n=t.from;if(i&&(n||!1!==e)&&this.transports[i])if(e)this.transports[i]=[];else{var r=this.$_getTransportIndex(t);if(r>=0){var o=this.transports[i].slice(0);o.splice(r,1),this.transports[i]=o}}},registerTarget:function(t,e,i){AC&&(this.trackInstances&&!i&&this.targets[t]&&console.warn("[portal-vue]: Target ".concat(t," already exists")),this.$set(this.targets,t,Object.freeze([e])))},unregisterTarget:function(t){this.$delete(this.targets,t)},registerSource:function(t,e,i){AC&&(this.trackInstances&&!i&&this.sources[t]&&console.warn("[portal-vue]: source ".concat(t," already exists")),this.$set(this.sources,t,Object.freeze([e])))},unregisterSource:function(t){this.$delete(this.sources,t)},hasTarget:function(t){return!(!this.targets[t]||!this.targets[t][0])},hasSource:function(t){return!(!this.sources[t]||!this.sources[t][0])},hasContentFor:function(t){return!!this.transports[t]&&!!this.transports[t].length},$_getTransportIndex:function(t){var e=t.to,i=t.from;for(var n in this.transports[e])if(this.transports[e][n].from===i)return+n;return-1}}}),GC=new jC(HC),WC=1,UC=i.default.extend({name:"portal",props:{disabled:{type:Boolean},name:{type:String,default:function(){return String(WC++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}}},created:function(){var t=this;this.$nextTick((function(){GC.registerSource(t.name,t)}))},mounted:function(){this.disabled||this.sendUpdate()},updated:function(){this.disabled?this.clear():this.sendUpdate()},beforeDestroy:function(){GC.unregisterSource(this.name),this.clear()},watch:{to:function(t,e){e&&e!==t&&this.clear(e),this.sendUpdate()}},methods:{clear:function(t){var e={from:this.name,to:t||this.to};GC.close(e)},normalizeSlots:function(){return this.$scopedSlots.default?[this.$scopedSlots.default]:this.$slots.default},normalizeOwnChildren:function(t){return"function"==typeof t?t(this.slotProps):t},sendUpdate:function(){var t=this.normalizeSlots();if(t){var e={from:this.name,to:this.to,passengers:RC(t),order:this.order};GC.open(e)}else this.clear()}},render:function(t){var e=this.$slots.default||this.$scopedSlots.default||[],i=this.tag;return e&&this.disabled?e.length<=1&&this.slim?this.normalizeOwnChildren(e)[0]:t(i,[this.normalizeOwnChildren(e)]):this.slim?t():t(i,{class:{"v-portal":!0},style:{display:"none"},key:"v-portal-placeholder"})}}),YC=i.default.extend({name:"portalTarget",props:{multiple:{type:Boolean,default:!1},name:{type:String,required:!0},slim:{type:Boolean,default:!1},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},data:function(){return{transports:GC.transports,firstRender:!0}},created:function(){var t=this;this.$nextTick((function(){GC.registerTarget(t.name,t)}))},watch:{ownTransports:function(){this.$emit("change",this.children().length>0)},name:function(t,e){GC.unregisterTarget(e),GC.registerTarget(t,this)}},mounted:function(){var t=this;this.transition&&this.$nextTick((function(){t.firstRender=!1}))},beforeDestroy:function(){GC.unregisterTarget(this.name)},computed:{ownTransports:function(){var t=this.transports[this.name]||[];return this.multiple?t:0===t.length?[]:[t[t.length-1]]},passengers:function(){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.reduce((function(t,i){var n=i.passengers[0],r="function"==typeof n?n(e):i.passengers;return t.concat(r)}),[])}(this.ownTransports,this.slotProps)}},methods:{children:function(){return 0!==this.passengers.length?this.passengers:this.$scopedSlots.default?this.$scopedSlots.default(this.slotProps):this.$slots.default||[]},noWrapper:function(){var t=this.slim&&!this.transition;return t&&this.children().length>1&&console.warn("[portal-vue]: PortalTarget with `slim` option received more than one child element."),t}},render:function(t){var e=this.noWrapper(),i=this.children(),n=this.transition||this.tag;return e?i[0]:this.slim&&!n?t():t(n,{props:{tag:this.transition&&this.tag?this.tag:void 0},class:{"vue-portal-target":!0}},i)}}),qC=0,KC=["disabled","name","order","slim","slotProps","tag","to"],XC=["multiple","transition"];i.default.extend({name:"MountingPortal",inheritAttrs:!1,props:{append:{type:[Boolean,String]},bail:{type:Boolean},mountTo:{type:String,required:!0},disabled:{type:Boolean},name:{type:String,default:function(){return"mounted_"+String(qC++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}},multiple:{type:Boolean,default:!1},targetSlim:{type:Boolean},targetSlotProps:{type:Object,default:function(){return{}}},targetTag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},created:function(){if("undefined"!=typeof document){var t=document.querySelector(this.mountTo);if(t){var e=this.$props;if(GC.targets[e.name])e.bail?console.warn("[portal-vue]: Target ".concat(e.name," is already mounted.\n        Aborting because 'bail: true' is set")):this.portalTarget=GC.targets[e.name];else{var i=e.append;if(i){var n="string"==typeof i?i:"DIV",r=document.createElement(n);t.appendChild(r),t=r}var o=MC(this.$props,XC);o.slim=this.targetSlim,o.tag=this.targetTag,o.slotProps=this.targetSlotProps,o.name=this.to,this.portalTarget=new YC({el:t,parent:this.$parent||this,propsData:o})}}else console.error("[portal-vue]: Mount Point '".concat(this.mountTo,"' not found in document"))}},beforeDestroy:function(){var t=this.portalTarget;if(this.append){var e=t.$el;e.parentNode.removeChild(e)}t.$destroy()},render:function(t){if(!this.portalTarget)return console.warn("[portal-vue] Target wasn't mounted"),t();if(!this.$scopedSlots.manual){var e=MC(this.$props,KC);return t(UC,{props:e,attrs:this.$attrs,on:this.$listeners,scopedSlots:this.$scopedSlots},this.$slots.default)}var i=this.$scopedSlots.manual({to:this.to});return Array.isArray(i)&&(i=i[0]),i||t()}});var ZC,JC,QC=L({mixins:[So],data:function(){return{name:"b-toaster"}},methods:{onAfterEnter:function(t){var e=this;Mo((function(){es(t,"".concat(e.name,"-enter-to"))}))}},render:function(t){return t("transition-group",{props:{tag:"div",name:this.name},on:{afterEnter:this.onAfterEnter}},this.normalizeSlot())}}),tS=Is({ariaAtomic:Ds(Fr),ariaLive:Ds(Fr),name:Ds(Fr,void 0,!0),role:Ds(Fr)},En),eS=L({name:En,mixins:[gl],props:tS,data:function(){return{doRender:!1,dead:!1,staticName:this.name}},beforeMount:function(){var t=this.name;this.staticName=t,GC.hasTarget(t)?(ye('A "<portal-target>" with name "'.concat(t,'" already exists in the document.'),En),this.dead=!0):this.doRender=!0},beforeDestroy:function(){this.doRender&&this.emitOnRoot(Ns(En,Gn),this.name)},destroyed:function(){var t=this.$el;t&&t.parentNode&&t.parentNode.removeChild(t)},render:function(t){var e=t("div",{class:["d-none",{"b-dead-toaster":this.dead}]});if(this.doRender){var i=t(YC,{staticClass:"b-toaster-slot",props:{name:this.staticName,multiple:!0,tag:"div",slim:!1,transition:QC}});e=t("div",{staticClass:"b-toaster",class:[this.staticName],attrs:{id:this.staticName,role:this.role||null,"aria-live":this.ariaLive,"aria-atomic":this.ariaAtomic}},[i])}return e}}),iS=Vs("visible",{type:kr,defaultValue:!1,event:Mn}),nS=iS.mixin,rS=iS.props,oS=iS.prop,sS=iS.event,aS=ce(kl,["href","to"]),lS=Is(he(r(r(r(r({},Vc),rS),aS),{},{appendToast:Ds(kr,!1),autoHideDelay:Ds(Hr,5e3),bodyClass:Ds(Or),headerClass:Ds(Or),headerTag:Ds(Fr,"header"),isStatus:Ds(kr,!1),noAutoHide:Ds(kr,!1),noCloseButton:Ds(kr,!1),noFade:Ds(kr,!1),noHoverPause:Ds(kr,!1),solid:Ds(kr,!1),static:Ds(kr,!1),title:Ds(Fr),toastClass:Ds(Or),toaster:Ds(Fr,"b-toaster-top-right"),variant:Ds(Fr)})),On),cS=L({name:On,mixins:[pl,Lc,nS,gl,So,Vg],inheritAttrs:!1,props:lS,data:function(){return{isMounted:!1,doRender:!1,localShow:!1,isTransitioning:!1,isHiding:!1,order:0,dismissStarted:0,resumeDismiss:0}},computed:{toastClasses:function(){var t=this.appendToast,e=this.variant;return c({"b-toast-solid":this.solid,"b-toast-append":t,"b-toast-prepend":!t},"b-toast-".concat(e),e)},slotScope:function(){return{hide:this.hide}},computedDuration:function(){return sa(ko(this.autoHideDelay,0),1e3)},computedToaster:function(){return String(this.toaster)},transitionHandlers:function(){return{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,beforeLeave:this.onBeforeLeave,afterLeave:this.onAfterLeave}},computedAttrs:function(){return r(r({},this.bvAttrs),{},{id:this.safeId(),tabindex:"0"})}},watch:(ZC={},c(ZC,oS,(function(t){this[t?"show":"hide"]()})),c(ZC,"localShow",(function(t){t!==this[oS]&&this.$emit(sS,t)})),c(ZC,"toaster",(function(){this.$nextTick(this.ensureToaster)})),c(ZC,"static",(function(t){t&&this.localShow&&this.ensureToaster()})),ZC),created:function(){this.$_dismissTimer=null},mounted:function(){var t=this;this.isMounted=!0,this.$nextTick((function(){t[oS]&&Mo((function(){t.show()}))})),this.listenOnRoot(js(On,fr),(function(e){e===t.safeId()&&t.show()})),this.listenOnRoot(js(On,er),(function(e){e&&e!==t.safeId()||t.hide()})),this.listenOnRoot(Ns(En,Gn),(function(e){e===t.computedToaster&&t.hide()}))},beforeDestroy:function(){this.clearDismissTimer()},methods:{show:function(){var t=this;if(!this.localShow){this.ensureToaster();var e=this.buildEvent(fr);this.emitEvent(e),this.dismissStarted=this.resumeDismiss=0,this.order=Date.now()*(this.appendToast?1:-1),this.isHiding=!1,this.doRender=!0,this.$nextTick((function(){Mo((function(){t.localShow=!0}))}))}},hide:function(){var t=this;if(this.localShow){var e=this.buildEvent(er);this.emitEvent(e),this.setHoverHandler(!1),this.dismissStarted=this.resumeDismiss=0,this.clearDismissTimer(),this.isHiding=!0,Mo((function(){t.localShow=!1}))}},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvEvent(t,r(r({cancelable:!1,target:this.$el||null,relatedTarget:null},e),{},{vueTarget:this,componentId:this.safeId()}))},emitEvent:function(t){var e=t.type;this.emitOnRoot(Ns(On,e),t),this.$emit(e,t)},ensureToaster:function(){if(!this.static){var t=this.computedToaster;if(!GC.hasTarget(t)){var e=document.createElement("div");document.body.appendChild(e),Lg(this.bvEventRoot,eS,{propsData:{name:t}}).$mount(e)}}},startDismissTimer:function(){this.clearDismissTimer(),this.noAutoHide||(this.$_dismissTimer=setTimeout(this.hide,this.resumeDismiss||this.computedDuration),this.dismissStarted=Date.now(),this.resumeDismiss=0)},clearDismissTimer:function(){clearTimeout(this.$_dismissTimer),this.$_dismissTimer=null},setHoverHandler:function(t){var e=this.$refs["b-toast"];Ms(t,e,"mouseenter",this.onPause,wr),Ms(t,e,"mouseleave",this.onUnPause,wr)},onPause:function(){if(!this.noAutoHide&&!this.noHoverPause&&this.$_dismissTimer&&!this.resumeDismiss){var t=Date.now()-this.dismissStarted;t>0&&(this.clearDismissTimer(),this.resumeDismiss=sa(this.computedDuration-t,1e3))}},onUnPause:function(){this.noAutoHide||this.noHoverPause||!this.resumeDismiss?this.resumeDismiss=this.dismissStarted=0:this.startDismissTimer()},onLinkClick:function(){var t=this;this.$nextTick((function(){Mo((function(){t.hide()}))}))},onBeforeEnter:function(){this.isTransitioning=!0},onAfterEnter:function(){this.isTransitioning=!1;var t=this.buildEvent(pr);this.emitEvent(t),this.startDismissTimer(),this.setHoverHandler(!0)},onBeforeLeave:function(){this.isTransitioning=!0},onAfterLeave:function(){this.isTransitioning=!1,this.order=0,this.resumeDismiss=this.dismissStarted=0;var t=this.buildEvent(tr);this.emitEvent(t),this.doRender=!1},makeToast:function(t){var e=this,i=this.title,n=this.slotScope,r=Ca(this),o=[],s=this.normalizeSlot("toast-title",n);s?o.push(s):i&&o.push(t("strong",{staticClass:"mr-2"},i)),this.noCloseButton||o.push(t(Ws,{staticClass:"ml-auto mb-1",on:{click:function(){e.hide()}}}));var a=t();o.length>0&&(a=t(this.headerTag,{staticClass:"toast-header",class:this.headerClass},o));var l=t(r?xl:"div",{staticClass:"toast-body",class:this.bodyClass,props:r?Fs(aS,this):{},on:r?{click:this.onLinkClick}:{}},this.normalizeSlot(Kr,n));return t("div",{staticClass:"toast",class:this.toastClass,attrs:this.computedAttrs,key:"toast-".concat(this._uid),ref:"toast"},[a,l])}},render:function(t){if(!this.doRender||!this.isMounted)return t();var e=this.order,i=this.static,n=this.isHiding,o=this.isStatus,s="b-toast-".concat(this._uid),a=t("div",{staticClass:"b-toast",class:this.toastClasses,attrs:r(r({},i?{}:this.scopedStyleAttrs),{},{id:this.safeId("_toast_outer"),role:n?null:o?"status":"alert","aria-live":n?null:o?"polite":"assertive","aria-atomic":n?null:"true"}),key:s,ref:"b-toast"},[t(Ks,{props:{noFade:this.noFade},on:this.transitionHandlers},[this.localShow?this.makeToast(t):t()])]);return t(UC,{props:{name:s,to:this.computedToaster,order:e,slim:!0,disabled:i}},[a])}}),uS="$bvToast",dS=["id"].concat(S(se(ue(lS,["static","visible"])))),hS={toastContent:"default",title:"toast-title"},fS=function(t){return dS.reduce((function(e,i){return zt(t[i])||(e[i]=t[i]),e}),{})},pS=$e({plugins:{plugin:function(t){var e=t.extend({name:"BVToastPop",extends:cS,mixins:[Og],destroyed:function(){var t=this.$el;t&&t.parentNode&&t.parentNode.removeChild(t)},mounted:function(){var t=this,e=function(){t.localShow=!1,t.doRender=!1,t.$nextTick((function(){t.$nextTick((function(){Mo((function(){t.$destroy()}))}))}))};this.bvParent.$once(br,e),this.$once(tr,e),this.listenOnRoot(Ns(En,Gn),(function(i){i===t.toaster&&e()}))}}),i=function(t,i){if(!Te(uS)){var n=Lg(i,e,{propsData:r(r(r({},fS(ws(On))),ue(t,se(hS))),{},{static:!1,visible:!0})});se(hS).forEach((function(e){var r=t[e];zt(r)||("title"===e&&Ut(r)&&(r=[i.$createElement("strong",{class:"mr-2"},r)]),n.$slots[hS[e]]=go(r))}));var o=document.createElement("div");document.body.appendChild(o),n.$mount(o)}},n=function(){function t(e){s(this,t),ee(this,{_vm:e,_root:ml(e)}),ne(this,{_vm:{enumerable:!0,configurable:!1,writable:!1},_root:{enumerable:!0,configurable:!1,writable:!1}})}return l(t,[{key:"toast",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&!Te(uS)&&i(r(r({},fS(e)),{},{toastContent:t}),this._vm)}},{key:"show",value:function(t){t&&this._root.$emit(js(On,fr),t)}},{key:"hide",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this._root.$emit(js(On,er),t)}}]),t}();t.mixin({beforeCreate:function(){this._bv__toast=new n(this)}}),ae(t.prototype,uS)||re(t.prototype,uS,{get:function(){return this&&this._bv__toast||ye('"'.concat(uS,'" must be accessed from a Vue instance "this" context.'),On),this._bv__toast}})}}}),mS=$e({components:{BToast:cS,BToaster:eS},plugins:{BVToastPlugin:pS}}),vS="__BV_Tooltip__",gS={focus:!0,hover:!0,click:!0,blur:!0,manual:!0},bS=/^html$/i,yS=/^noninteractive$/i,TS=/^nofade$/i,wS=/^(auto|top(left|right)?|bottom(left|right)?|left(top|bottom)?|right(top|bottom)?)$/i,CS=/^(window|viewport|scrollParent)$/i,SS=/^d\d+$/i,kS=/^ds\d+$/i,xS=/^dh\d+$/i,$S=/^o-?\d+$/i,BS=/^v-.+$/i,DS=/\s+/,_S=function(t,e,i){if(Y){var n=function(t,e){var i={title:void 0,trigger:"",placement:"top",fallbackPlacement:"flip",container:!1,animation:!0,offset:0,id:null,html:!1,interactive:!0,disabled:!1,delay:ws(Vn,"delay",50),boundary:String(ws(Vn,"boundary","scrollParent")),boundaryPadding:ko(ws(Vn,"boundaryPadding",5),0),variant:ws(Vn,"variant"),customClass:ws(Vn,"customClass")};if(Ut(t.value)||Yt(t.value)||Gt(t.value)?i.title=t.value:Zt(t.value)&&(i=r(r({},i),t.value)),zt(i.title)){var n=O?e.props:(e.data||{}).attrs;i.title=n&&!jt(n.title)?n.title:void 0}Zt(i.delay)||(i.delay={show:ko(i.delay,0),hide:ko(i.delay,0)}),t.arg&&(i.container="#".concat(t.arg)),se(t.modifiers).forEach((function(t){if(bS.test(t))i.html=!0;else if(yS.test(t))i.interactive=!1;else if(TS.test(t))i.animation=!1;else if(wS.test(t))i.placement=t;else if(CS.test(t))t="scrollparent"===t?"scrollParent":t,i.boundary=t;else if(SS.test(t)){var e=ko(t.slice(1),0);i.delay.show=e,i.delay.hide=e}else kS.test(t)?i.delay.show=ko(t.slice(2),0):xS.test(t)?i.delay.hide=ko(t.slice(2),0):$S.test(t)?i.offset=ko(t.slice(1),0):BS.test(t)&&(i.variant=t.slice(2)||null)}));var o={};return go(i.trigger||"").filter(pe).join(" ").trim().toLowerCase().split(DS).forEach((function(t){gS[t]&&(o[t]=!0)})),se(t.modifiers).forEach((function(t){t=t.toLowerCase(),gS[t]&&(o[t]=!0)})),i.trigger=se(o).join(" "),"blur"===i.trigger&&(i.trigger="focus"),i.trigger||(i.trigger="hover focus"),i}(e,i);if(!t[vS]){var o=ld(i,e);t[vS]=Lg(o,By,{_scopeId:Eg(o,void 0)}),t[vS].__bv_prev_data__={},t[vS].$on(fr,(function(){Gt(n.title)&&t[vS].updateData({title:n.title(t)})}))}var s={title:n.title,triggers:n.trigger,placement:n.placement,fallbackPlacement:n.fallbackPlacement,variant:n.variant,customClass:n.customClass,container:n.container,boundary:n.boundary,delay:n.delay,offset:n.offset,noFade:!n.animation,id:n.id,interactive:n.interactive,disabled:n.disabled,html:n.html},a=t[vS].__bv_prev_data__;if(t[vS].__bv_prev_data__=s,!ll(s,a)){var l={target:t};se(s).forEach((function(e){s[e]!==a[e]&&(l[e]="title"===e&&Gt(s[e])?s[e](t):s[e])})),t[vS].updateData(l)}}},FS=$e({directives:{VBTooltip:{bind:function(t,e,i){_S(t,e,i)},componentUpdated:function(t,e,i){z((function(){_S(t,e,i)}))},unbind:function(t){!function(t){t[vS]&&(t[vS].$destroy(),t[vS]=null),delete t[vS]}(t)}}}}),PS=$e({plugins:{AlertPlugin:ra,AspectPlugin:ma,AvatarPlugin:Gl,BadgePlugin:Kl,BreadcrumbPlugin:rc,ButtonPlugin:oc,ButtonGroupPlugin:lc,ButtonToolbarPlugin:hc,CalendarPlugin:Gc,CardPlugin:Du,CarouselPlugin:Gu,CollapsePlugin:Ed,DropdownPlugin:Df,EmbedPlugin:If,FormPlugin:Wf,FormCheckboxPlugin:xp,FormDatepickerPlugin:Np,FormFilePlugin:tm,FormGroupPlugin:dm,FormInputPlugin:km,FormRadioPlugin:Bm,FormRatingPlugin:Am,FormSelectPlugin:Jm,FormSpinbuttonPlugin:av,FormTagsPlugin:wv,FormTextareaPlugin:kv,FormTimepickerPlugin:Gv,ImagePlugin:Wv,InputGroupPlugin:ig,JumbotronPlugin:ag,LayoutPlugin:fg,LinkPlugin:pg,ListGroupPlugin:wg,MediaPlugin:Dg,ModalPlugin:mb,NavPlugin:Bb,NavbarPlugin:Gb,OverlayPlugin:Xb,PaginationPlugin:dy,PaginationNavPlugin:my,PopoverPlugin:Zy,ProgressPlugin:iT,SidebarPlugin:TT,SkeletonPlugin:yC,SpinnerPlugin:TC,TablePlugin:fC,TabsPlugin:EC,TimePlugin:VC,ToastPlugin:mS,TooltipPlugin:$e({components:{BTooltip:Iy},plugins:{VBTooltipPlugin:FS}})}}),IS=$e({directives:{VBHover:Fp}}),OS=$e({directives:{VBModal:lb}}),ES="active",VS=".nav-link",LS=".nav-item",RS=".list-group-item",AS=".dropdown-item",MS=Ns("BVScrollspy","activate"),HS="position",zS={element:"body",offset:10,method:"auto",throttle:75},NS={element:"(string|element|component)",offset:"number",method:"string",throttle:"number"},jS=["webkitTransitionEnd","transitionend","otransitionend","oTransitionEnd"],GS=function(t){return function(t){return Object.prototype.toString.call(t)}(t).match(/\s([a-zA-Z]+)/)[1].toLowerCase()},WS=function(){function t(e,i,n){s(this,t),this.$el=e,this.$scroller=null,this.$selector=[VS,RS,AS].join(","),this.$offsets=[],this.$targets=[],this.$activeTarget=null,this.$scrollHeight=0,this.$resizeTimeout=null,this.$scrollerObserver=null,this.$targetsObserver=null,this.$root=n||null,this.$config=null,this.updateConfig(i)}return l(t,[{key:"updateConfig",value:function(t,e){this.$scroller&&(this.unlisten(),this.$scroller=null);var i=r(r({},this.constructor.Default),t);if(e&&(this.$root=e),function(t,e,i){for(var n in i)if(ae(i,n)){var r=i[n],o=e[n],s=o&&zo(o)?"element":GS(o);s=o&&o._isVue?"component":s,new RegExp(r).test(s)||ye("".concat(t,': Option "').concat(n,'" provided type "').concat(s,'" but expected type "').concat(r,'"'))}}(this.constructor.Name,i,this.constructor.DefaultType),this.$config=i,this.$root){var n=this;this.$root.$nextTick((function(){n.listen()}))}else this.listen()}},{key:"dispose",value:function(){this.unlisten(),clearTimeout(this.$resizeTimeout),this.$resizeTimeout=null,this.$el=null,this.$config=null,this.$scroller=null,this.$selector=null,this.$offsets=null,this.$targets=null,this.$activeTarget=null,this.$scrollHeight=null}},{key:"listen",value:function(){var t=this,e=this.getScroller();e&&"BODY"!==e.tagName&&Rs(e,"scroll",this,wr),Rs(window,"scroll",this,wr),Rs(window,"resize",this,wr),Rs(window,"orientationchange",this,wr),jS.forEach((function(e){Rs(window,e,t,wr)})),this.setObservers(!0),this.handleEvent("refresh")}},{key:"unlisten",value:function(){var t=this,e=this.getScroller();this.setObservers(!1),e&&"BODY"!==e.tagName&&As(e,"scroll",this,wr),As(window,"scroll",this,wr),As(window,"resize",this,wr),As(window,"orientationchange",this,wr),jS.forEach((function(e){As(window,e,t,wr)}))}},{key:"setObservers",value:function(t){var e=this;this.$scrollerObserver&&this.$scrollerObserver.disconnect(),this.$targetsObserver&&this.$targetsObserver.disconnect(),this.$scrollerObserver=null,this.$targetsObserver=null,t&&(this.$targetsObserver=Fu(this.$el,(function(){e.handleEvent("mutation")}),{subtree:!0,childList:!0,attributes:!0,attributeFilter:["href"]}),this.$scrollerObserver=Fu(this.getScroller(),(function(){e.handleEvent("mutation")}),{subtree:!0,childList:!0,characterData:!0,attributes:!0,attributeFilter:["id","style","class"]}))}},{key:"handleEvent",value:function(t){var e=Ut(t)?t:t.type,i=this;"scroll"===e?(this.$scrollerObserver||this.listen(),this.process()):/(resize|orientationchange|mutation|refresh)/.test(e)&&(i.$resizeTimeout||(i.$resizeTimeout=setTimeout((function(){i.refresh(),i.process(),i.$resizeTimeout=null}),i.$config.throttle)))}},{key:"refresh",value:function(){var t=this,e=this.getScroller();if(e){var i=e!==e.window?HS:"offset",n="auto"===this.$config.method?i:this.$config.method,r=n===HS?ps:fs,o=n===HS?this.getScrollTop():0;return this.$offsets=[],this.$targets=[],this.$scrollHeight=this.getScrollHeight(),qo(this.$selector,this.$el).map((function(t){return os(t,"href")})).filter((function(t){return t&&Ft.test(t||"")})).map((function(t){var i=t.replace(Ft,"$1").trim();if(!i)return null;var n=Ko(i,e);return n&&Wo(n)?{offset:ko(r(n).top,0)+o,target:i}:null})).filter(pe).sort((function(t,e){return t.offset-e.offset})).reduce((function(e,i){return e[i.target]||(t.$offsets.push(i.offset),t.$targets.push(i.target),e[i.target]=!0),e}),{}),this}}},{key:"process",value:function(){var t=this.getScrollTop()+this.$config.offset,e=this.getScrollHeight(),i=this.$config.offset+e-this.getOffsetHeight();if(this.$scrollHeight!==e&&this.refresh(),t>=i){var n=this.$targets[this.$targets.length-1];this.$activeTarget!==n&&this.activate(n)}else{if(this.$activeTarget&&t<this.$offsets[0]&&this.$offsets[0]>0)return this.$activeTarget=null,void this.clear();for(var r=this.$offsets.length;r--;){this.$activeTarget!==this.$targets[r]&&t>=this.$offsets[r]&&(zt(this.$offsets[r+1])||t<this.$offsets[r+1])&&this.activate(this.$targets[r])}}}},{key:"getScroller",value:function(){if(this.$scroller)return this.$scroller;var t=this.$config.element;return t?(zo(t.$el)?t=t.$el:Ut(t)&&(t=Ko(t)),t?(this.$scroller="BODY"===t.tagName?window:t,this.$scroller):null):null}},{key:"getScrollTop",value:function(){var t=this.getScroller();return t===window?t.pageYOffset:t.scrollTop}},{key:"getScrollHeight",value:function(){return this.getScroller().scrollHeight||sa(document.body.scrollHeight,document.documentElement.scrollHeight)}},{key:"getOffsetHeight",value:function(){var t=this.getScroller();return t===window?window.innerHeight:us(t).height}},{key:"activate",value:function(t){var e=this;this.$activeTarget=t,this.clear();var i=qo(this.$selector.split(",").map((function(e){return"".concat(e,'[href$="').concat(t,'"]')})).join(","),this.$el);i.forEach((function(t){if(is(t,"dropdown-item")){var i=Zo(".dropdown, .dropup",t);i&&e.setActiveState(Ko(".dropdown-toggle",i),!0),e.setActiveState(t,!0)}else{e.setActiveState(t,!0),Xo(t.parentElement,LS)&&e.setActiveState(t.parentElement,!0);for(var n=t;n;){var r=(n=Zo(".nav, .list-group",n))?n.previousElementSibling:null;r&&Xo(r,"".concat(VS,", ").concat(RS))&&e.setActiveState(r,!0),r&&Xo(r,LS)&&(e.setActiveState(Ko(VS,r),!0),e.setActiveState(r,!0))}}})),i&&i.length>0&&this.$root&&this.$root.$emit(MS,t,i)}},{key:"clear",value:function(){var t=this;qo("".concat(this.$selector,", ").concat(LS),this.$el).filter((function(t){return is(t,ES)})).forEach((function(e){return t.setActiveState(e,!1)}))}},{key:"setActiveState",value:function(t,e){t&&(e?ts(t,ES):es(t,ES))}}],[{key:"Name",get:function(){return"v-b-scrollspy"}},{key:"Default",get:function(){return zS}},{key:"DefaultType",get:function(){return NS}}]),t}(),US="__BV_Scrollspy__",YS=/^\d+$/,qS=/^(auto|position|offset)$/,KS=function(t,e,i){if(Y){var n=function(t){var e={};return t.arg&&(e.element="#".concat(t.arg)),se(t.modifiers).forEach((function(t){YS.test(t)?e.offset=ko(t,0):qS.test(t)&&(e.method=t)})),Ut(t.value)?e.element=t.value:Yt(t.value)?e.offset=da(t.value):Xt(t.value)&&se(t.value).filter((function(t){return!!WS.DefaultType[t]})).forEach((function(i){e[i]=t.value[i]})),e}(e);t[US]?t[US].updateConfig(n,ml(ld(i,e))):t[US]=new WS(t,n,ml(ld(i,e)))}},XS={install:xe({plugins:{componentsPlugin:PS,directivesPlugin:$e({plugins:{VBHoverPlugin:IS,VBModalPlugin:OS,VBPopoverPlugin:Xy,VBScrollspyPlugin:$e({directives:{VBScrollspy:{bind:function(t,e,i){KS(t,e,i)},inserted:function(t,e,i){KS(t,e,i)},update:function(t,e,i){e.value!==e.oldValue&&KS(t,e,i)},componentUpdated:function(t,e,i){e.value!==e.oldValue&&KS(t,e,i)},unbind:function(t){!function(t){t[US]&&(t[US].dispose(),t[US]=null,delete t[US])}(t)}}}}),VBTogglePlugin:Od,VBTooltipPlugin:FS,VBVisiblePlugin:$e({directives:{VBVisible:gu}})}})}}),NAME:"BootstrapVue"};return JC=XS,N&&window.Vue&&window.Vue.use(JC),N&&JC.NAME&&(window[JC.NAME]=JC),XS}));
//# sourceMappingURL=bootstrap-vue.min.js.map