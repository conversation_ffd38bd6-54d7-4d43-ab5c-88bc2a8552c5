<?php
// This file was auto-generated from sdk-root/src/data/iotwireless/2020-11-22/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-22', 'endpointPrefix' => 'api.iotwireless', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT Wireless', 'serviceId' => 'IoT Wireless', 'signatureVersion' => 'v4', 'signingName' => 'iotwireless', 'uid' => 'iotwireless-2020-11-22', ], 'operations' => [ 'AssociateAwsAccountWithPartnerAccount' => [ 'name' => 'AssociateAwsAccountWithPartnerAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/partner-accounts', ], 'input' => [ 'shape' => 'AssociateAwsAccountWithPartnerAccountRequest', ], 'output' => [ 'shape' => 'AssociateAwsAccountWithPartnerAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'AssociateMulticastGroupWithFuotaTask' => [ 'name' => 'AssociateMulticastGroupWithFuotaTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/fuota-tasks/{Id}/multicast-group', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AssociateMulticastGroupWithFuotaTaskRequest', ], 'output' => [ 'shape' => 'AssociateMulticastGroupWithFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateWirelessDeviceWithFuotaTask' => [ 'name' => 'AssociateWirelessDeviceWithFuotaTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/fuota-tasks/{Id}/wireless-device', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AssociateWirelessDeviceWithFuotaTaskRequest', ], 'output' => [ 'shape' => 'AssociateWirelessDeviceWithFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateWirelessDeviceWithMulticastGroup' => [ 'name' => 'AssociateWirelessDeviceWithMulticastGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/multicast-groups/{Id}/wireless-device', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AssociateWirelessDeviceWithMulticastGroupRequest', ], 'output' => [ 'shape' => 'AssociateWirelessDeviceWithMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateWirelessDeviceWithThing' => [ 'name' => 'AssociateWirelessDeviceWithThing', 'http' => [ 'method' => 'PUT', 'requestUri' => '/wireless-devices/{Id}/thing', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AssociateWirelessDeviceWithThingRequest', ], 'output' => [ 'shape' => 'AssociateWirelessDeviceWithThingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateWirelessGatewayWithCertificate' => [ 'name' => 'AssociateWirelessGatewayWithCertificate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/wireless-gateways/{Id}/certificate', ], 'input' => [ 'shape' => 'AssociateWirelessGatewayWithCertificateRequest', ], 'output' => [ 'shape' => 'AssociateWirelessGatewayWithCertificateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateWirelessGatewayWithThing' => [ 'name' => 'AssociateWirelessGatewayWithThing', 'http' => [ 'method' => 'PUT', 'requestUri' => '/wireless-gateways/{Id}/thing', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AssociateWirelessGatewayWithThingRequest', ], 'output' => [ 'shape' => 'AssociateWirelessGatewayWithThingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelMulticastGroupSession' => [ 'name' => 'CancelMulticastGroupSession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/multicast-groups/{Id}/session', 'responseCode' => 204, ], 'input' => [ 'shape' => 'CancelMulticastGroupSessionRequest', ], 'output' => [ 'shape' => 'CancelMulticastGroupSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDestination' => [ 'name' => 'CreateDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/destinations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDestinationRequest', ], 'output' => [ 'shape' => 'CreateDestinationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDeviceProfile' => [ 'name' => 'CreateDeviceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/device-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDeviceProfileRequest', ], 'output' => [ 'shape' => 'CreateDeviceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateFuotaTask' => [ 'name' => 'CreateFuotaTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/fuota-tasks', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFuotaTaskRequest', ], 'output' => [ 'shape' => 'CreateFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateMulticastGroup' => [ 'name' => 'CreateMulticastGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/multicast-groups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMulticastGroupRequest', ], 'output' => [ 'shape' => 'CreateMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateNetworkAnalyzerConfiguration' => [ 'name' => 'CreateNetworkAnalyzerConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/network-analyzer-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateNetworkAnalyzerConfigurationRequest', ], 'output' => [ 'shape' => 'CreateNetworkAnalyzerConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateServiceProfile' => [ 'name' => 'CreateServiceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/service-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateServiceProfileRequest', ], 'output' => [ 'shape' => 'CreateServiceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWirelessDevice' => [ 'name' => 'CreateWirelessDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-devices', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateWirelessDeviceRequest', ], 'output' => [ 'shape' => 'CreateWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWirelessGateway' => [ 'name' => 'CreateWirelessGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-gateways', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateWirelessGatewayRequest', ], 'output' => [ 'shape' => 'CreateWirelessGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWirelessGatewayTask' => [ 'name' => 'CreateWirelessGatewayTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-gateways/{Id}/tasks', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateWirelessGatewayTaskRequest', ], 'output' => [ 'shape' => 'CreateWirelessGatewayTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWirelessGatewayTaskDefinition' => [ 'name' => 'CreateWirelessGatewayTaskDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-gateway-task-definitions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateWirelessGatewayTaskDefinitionRequest', ], 'output' => [ 'shape' => 'CreateWirelessGatewayTaskDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDestination' => [ 'name' => 'DeleteDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/destinations/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDestinationRequest', ], 'output' => [ 'shape' => 'DeleteDestinationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDeviceProfile' => [ 'name' => 'DeleteDeviceProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/device-profiles/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDeviceProfileRequest', ], 'output' => [ 'shape' => 'DeleteDeviceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteFuotaTask' => [ 'name' => 'DeleteFuotaTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/fuota-tasks/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFuotaTaskRequest', ], 'output' => [ 'shape' => 'DeleteFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteMulticastGroup' => [ 'name' => 'DeleteMulticastGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/multicast-groups/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMulticastGroupRequest', ], 'output' => [ 'shape' => 'DeleteMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteNetworkAnalyzerConfiguration' => [ 'name' => 'DeleteNetworkAnalyzerConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/network-analyzer-configurations/{ConfigurationName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteNetworkAnalyzerConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteNetworkAnalyzerConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteQueuedMessages' => [ 'name' => 'DeleteQueuedMessages', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-devices/{Id}/data', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteQueuedMessagesRequest', ], 'output' => [ 'shape' => 'DeleteQueuedMessagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteServiceProfile' => [ 'name' => 'DeleteServiceProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/service-profiles/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteServiceProfileRequest', ], 'output' => [ 'shape' => 'DeleteServiceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWirelessDevice' => [ 'name' => 'DeleteWirelessDevice', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-devices/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteWirelessDeviceRequest', ], 'output' => [ 'shape' => 'DeleteWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWirelessDeviceImportTask' => [ 'name' => 'DeleteWirelessDeviceImportTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless_device_import_task/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'DeleteWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWirelessGateway' => [ 'name' => 'DeleteWirelessGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-gateways/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteWirelessGatewayRequest', ], 'output' => [ 'shape' => 'DeleteWirelessGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWirelessGatewayTask' => [ 'name' => 'DeleteWirelessGatewayTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-gateways/{Id}/tasks', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteWirelessGatewayTaskRequest', ], 'output' => [ 'shape' => 'DeleteWirelessGatewayTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWirelessGatewayTaskDefinition' => [ 'name' => 'DeleteWirelessGatewayTaskDefinition', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-gateway-task-definitions/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteWirelessGatewayTaskDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteWirelessGatewayTaskDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeregisterWirelessDevice' => [ 'name' => 'DeregisterWirelessDevice', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/wireless-devices/{Identifier}/deregister', ], 'input' => [ 'shape' => 'DeregisterWirelessDeviceRequest', ], 'output' => [ 'shape' => 'DeregisterWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateAwsAccountFromPartnerAccount' => [ 'name' => 'DisassociateAwsAccountFromPartnerAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/partner-accounts/{PartnerAccountId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateAwsAccountFromPartnerAccountRequest', ], 'output' => [ 'shape' => 'DisassociateAwsAccountFromPartnerAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateMulticastGroupFromFuotaTask' => [ 'name' => 'DisassociateMulticastGroupFromFuotaTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/fuota-tasks/{Id}/multicast-groups/{MulticastGroupId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateMulticastGroupFromFuotaTaskRequest', ], 'output' => [ 'shape' => 'DisassociateMulticastGroupFromFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateWirelessDeviceFromFuotaTask' => [ 'name' => 'DisassociateWirelessDeviceFromFuotaTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/fuota-tasks/{Id}/wireless-devices/{WirelessDeviceId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateWirelessDeviceFromFuotaTaskRequest', ], 'output' => [ 'shape' => 'DisassociateWirelessDeviceFromFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateWirelessDeviceFromMulticastGroup' => [ 'name' => 'DisassociateWirelessDeviceFromMulticastGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/multicast-groups/{Id}/wireless-devices/{WirelessDeviceId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateWirelessDeviceFromMulticastGroupRequest', ], 'output' => [ 'shape' => 'DisassociateWirelessDeviceFromMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateWirelessDeviceFromThing' => [ 'name' => 'DisassociateWirelessDeviceFromThing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-devices/{Id}/thing', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateWirelessDeviceFromThingRequest', ], 'output' => [ 'shape' => 'DisassociateWirelessDeviceFromThingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateWirelessGatewayFromCertificate' => [ 'name' => 'DisassociateWirelessGatewayFromCertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-gateways/{Id}/certificate', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateWirelessGatewayFromCertificateRequest', ], 'output' => [ 'shape' => 'DisassociateWirelessGatewayFromCertificateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateWirelessGatewayFromThing' => [ 'name' => 'DisassociateWirelessGatewayFromThing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/wireless-gateways/{Id}/thing', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateWirelessGatewayFromThingRequest', ], 'output' => [ 'shape' => 'DisassociateWirelessGatewayFromThingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDestination' => [ 'name' => 'GetDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations/{Name}', ], 'input' => [ 'shape' => 'GetDestinationRequest', ], 'output' => [ 'shape' => 'GetDestinationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDeviceProfile' => [ 'name' => 'GetDeviceProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/device-profiles/{Id}', ], 'input' => [ 'shape' => 'GetDeviceProfileRequest', ], 'output' => [ 'shape' => 'GetDeviceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEventConfigurationByResourceTypes' => [ 'name' => 'GetEventConfigurationByResourceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-configurations-resource-types', ], 'input' => [ 'shape' => 'GetEventConfigurationByResourceTypesRequest', ], 'output' => [ 'shape' => 'GetEventConfigurationByResourceTypesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetFuotaTask' => [ 'name' => 'GetFuotaTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/fuota-tasks/{Id}', ], 'input' => [ 'shape' => 'GetFuotaTaskRequest', ], 'output' => [ 'shape' => 'GetFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLogLevelsByResourceTypes' => [ 'name' => 'GetLogLevelsByResourceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/log-levels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLogLevelsByResourceTypesRequest', ], 'output' => [ 'shape' => 'GetLogLevelsByResourceTypesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMetricConfiguration' => [ 'name' => 'GetMetricConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/metric-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMetricConfigurationRequest', ], 'output' => [ 'shape' => 'GetMetricConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetMetrics' => [ 'name' => 'GetMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics', ], 'input' => [ 'shape' => 'GetMetricsRequest', ], 'output' => [ 'shape' => 'GetMetricsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetMulticastGroup' => [ 'name' => 'GetMulticastGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/multicast-groups/{Id}', ], 'input' => [ 'shape' => 'GetMulticastGroupRequest', ], 'output' => [ 'shape' => 'GetMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetMulticastGroupSession' => [ 'name' => 'GetMulticastGroupSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/multicast-groups/{Id}/session', ], 'input' => [ 'shape' => 'GetMulticastGroupSessionRequest', ], 'output' => [ 'shape' => 'GetMulticastGroupSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetNetworkAnalyzerConfiguration' => [ 'name' => 'GetNetworkAnalyzerConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/network-analyzer-configurations/{ConfigurationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNetworkAnalyzerConfigurationRequest', ], 'output' => [ 'shape' => 'GetNetworkAnalyzerConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetPartnerAccount' => [ 'name' => 'GetPartnerAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/partner-accounts/{PartnerAccountId}', ], 'input' => [ 'shape' => 'GetPartnerAccountRequest', ], 'output' => [ 'shape' => 'GetPartnerAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetPosition' => [ 'name' => 'GetPosition', 'http' => [ 'method' => 'GET', 'requestUri' => '/positions/{ResourceIdentifier}', ], 'input' => [ 'shape' => 'GetPositionRequest', ], 'output' => [ 'shape' => 'GetPositionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetPositionConfiguration' => [ 'name' => 'GetPositionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/position-configurations/{ResourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPositionConfigurationRequest', ], 'output' => [ 'shape' => 'GetPositionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetPositionEstimate' => [ 'name' => 'GetPositionEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/position-estimate', ], 'input' => [ 'shape' => 'GetPositionEstimateRequest', ], 'output' => [ 'shape' => 'GetPositionEstimateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourceEventConfiguration' => [ 'name' => 'GetResourceEventConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-configurations/{Identifier}', ], 'input' => [ 'shape' => 'GetResourceEventConfigurationRequest', ], 'output' => [ 'shape' => 'GetResourceEventConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourceLogLevel' => [ 'name' => 'GetResourceLogLevel', 'http' => [ 'method' => 'GET', 'requestUri' => '/log-levels/{ResourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourceLogLevelRequest', ], 'output' => [ 'shape' => 'GetResourceLogLevelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetResourcePosition' => [ 'name' => 'GetResourcePosition', 'http' => [ 'method' => 'GET', 'requestUri' => '/resource-positions/{ResourceIdentifier}', ], 'input' => [ 'shape' => 'GetResourcePositionRequest', ], 'output' => [ 'shape' => 'GetResourcePositionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceEndpoint' => [ 'name' => 'GetServiceEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/service-endpoint', ], 'input' => [ 'shape' => 'GetServiceEndpointRequest', ], 'output' => [ 'shape' => 'GetServiceEndpointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetServiceProfile' => [ 'name' => 'GetServiceProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/service-profiles/{Id}', ], 'input' => [ 'shape' => 'GetServiceProfileRequest', ], 'output' => [ 'shape' => 'GetServiceProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessDevice' => [ 'name' => 'GetWirelessDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-devices/{Identifier}', ], 'input' => [ 'shape' => 'GetWirelessDeviceRequest', ], 'output' => [ 'shape' => 'GetWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessDeviceImportTask' => [ 'name' => 'GetWirelessDeviceImportTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless_device_import_task/{Id}', ], 'input' => [ 'shape' => 'GetWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'GetWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessDeviceStatistics' => [ 'name' => 'GetWirelessDeviceStatistics', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-devices/{Id}/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWirelessDeviceStatisticsRequest', ], 'output' => [ 'shape' => 'GetWirelessDeviceStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGateway' => [ 'name' => 'GetWirelessGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways/{Identifier}', ], 'input' => [ 'shape' => 'GetWirelessGatewayRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGatewayCertificate' => [ 'name' => 'GetWirelessGatewayCertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways/{Id}/certificate', ], 'input' => [ 'shape' => 'GetWirelessGatewayCertificateRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayCertificateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGatewayFirmwareInformation' => [ 'name' => 'GetWirelessGatewayFirmwareInformation', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways/{Id}/firmware-information', ], 'input' => [ 'shape' => 'GetWirelessGatewayFirmwareInformationRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayFirmwareInformationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGatewayStatistics' => [ 'name' => 'GetWirelessGatewayStatistics', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways/{Id}/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWirelessGatewayStatisticsRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGatewayTask' => [ 'name' => 'GetWirelessGatewayTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways/{Id}/tasks', ], 'input' => [ 'shape' => 'GetWirelessGatewayTaskRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWirelessGatewayTaskDefinition' => [ 'name' => 'GetWirelessGatewayTaskDefinition', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateway-task-definitions/{Id}', ], 'input' => [ 'shape' => 'GetWirelessGatewayTaskDefinitionRequest', ], 'output' => [ 'shape' => 'GetWirelessGatewayTaskDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDestinations' => [ 'name' => 'ListDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations', ], 'input' => [ 'shape' => 'ListDestinationsRequest', ], 'output' => [ 'shape' => 'ListDestinationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDeviceProfiles' => [ 'name' => 'ListDeviceProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/device-profiles', ], 'input' => [ 'shape' => 'ListDeviceProfilesRequest', ], 'output' => [ 'shape' => 'ListDeviceProfilesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDevicesForWirelessDeviceImportTask' => [ 'name' => 'ListDevicesForWirelessDeviceImportTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless_device_import_task', ], 'input' => [ 'shape' => 'ListDevicesForWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'ListDevicesForWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEventConfigurations' => [ 'name' => 'ListEventConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-configurations', ], 'input' => [ 'shape' => 'ListEventConfigurationsRequest', ], 'output' => [ 'shape' => 'ListEventConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListFuotaTasks' => [ 'name' => 'ListFuotaTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/fuota-tasks', ], 'input' => [ 'shape' => 'ListFuotaTasksRequest', ], 'output' => [ 'shape' => 'ListFuotaTasksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListMulticastGroups' => [ 'name' => 'ListMulticastGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/multicast-groups', ], 'input' => [ 'shape' => 'ListMulticastGroupsRequest', ], 'output' => [ 'shape' => 'ListMulticastGroupsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListMulticastGroupsByFuotaTask' => [ 'name' => 'ListMulticastGroupsByFuotaTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/fuota-tasks/{Id}/multicast-groups', ], 'input' => [ 'shape' => 'ListMulticastGroupsByFuotaTaskRequest', ], 'output' => [ 'shape' => 'ListMulticastGroupsByFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNetworkAnalyzerConfigurations' => [ 'name' => 'ListNetworkAnalyzerConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/network-analyzer-configurations', ], 'input' => [ 'shape' => 'ListNetworkAnalyzerConfigurationsRequest', ], 'output' => [ 'shape' => 'ListNetworkAnalyzerConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListPartnerAccounts' => [ 'name' => 'ListPartnerAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/partner-accounts', ], 'input' => [ 'shape' => 'ListPartnerAccountsRequest', ], 'output' => [ 'shape' => 'ListPartnerAccountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListPositionConfigurations' => [ 'name' => 'ListPositionConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/position-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPositionConfigurationsRequest', ], 'output' => [ 'shape' => 'ListPositionConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'ListQueuedMessages' => [ 'name' => 'ListQueuedMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-devices/{Id}/data', ], 'input' => [ 'shape' => 'ListQueuedMessagesRequest', ], 'output' => [ 'shape' => 'ListQueuedMessagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListServiceProfiles' => [ 'name' => 'ListServiceProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/service-profiles', ], 'input' => [ 'shape' => 'ListServiceProfilesRequest', ], 'output' => [ 'shape' => 'ListServiceProfilesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWirelessDeviceImportTasks' => [ 'name' => 'ListWirelessDeviceImportTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless_device_import_tasks', ], 'input' => [ 'shape' => 'ListWirelessDeviceImportTasksRequest', ], 'output' => [ 'shape' => 'ListWirelessDeviceImportTasksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWirelessDevices' => [ 'name' => 'ListWirelessDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-devices', ], 'input' => [ 'shape' => 'ListWirelessDevicesRequest', ], 'output' => [ 'shape' => 'ListWirelessDevicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListWirelessGatewayTaskDefinitions' => [ 'name' => 'ListWirelessGatewayTaskDefinitions', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateway-task-definitions', ], 'input' => [ 'shape' => 'ListWirelessGatewayTaskDefinitionsRequest', ], 'output' => [ 'shape' => 'ListWirelessGatewayTaskDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWirelessGateways' => [ 'name' => 'ListWirelessGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/wireless-gateways', ], 'input' => [ 'shape' => 'ListWirelessGatewaysRequest', ], 'output' => [ 'shape' => 'ListWirelessGatewaysResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutPositionConfiguration' => [ 'name' => 'PutPositionConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/position-configurations/{ResourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPositionConfigurationRequest', ], 'output' => [ 'shape' => 'PutPositionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'PutResourceLogLevel' => [ 'name' => 'PutResourceLogLevel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/log-levels/{ResourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutResourceLogLevelRequest', ], 'output' => [ 'shape' => 'PutResourceLogLevelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ResetAllResourceLogLevels' => [ 'name' => 'ResetAllResourceLogLevels', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/log-levels', 'responseCode' => 204, ], 'input' => [ 'shape' => 'ResetAllResourceLogLevelsRequest', ], 'output' => [ 'shape' => 'ResetAllResourceLogLevelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ResetResourceLogLevel' => [ 'name' => 'ResetResourceLogLevel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/log-levels/{ResourceIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'ResetResourceLogLevelRequest', ], 'output' => [ 'shape' => 'ResetResourceLogLevelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'SendDataToMulticastGroup' => [ 'name' => 'SendDataToMulticastGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/multicast-groups/{Id}/data', 'responseCode' => 201, ], 'input' => [ 'shape' => 'SendDataToMulticastGroupRequest', ], 'output' => [ 'shape' => 'SendDataToMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SendDataToWirelessDevice' => [ 'name' => 'SendDataToWirelessDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-devices/{Id}/data', 'responseCode' => 202, ], 'input' => [ 'shape' => 'SendDataToWirelessDeviceRequest', ], 'output' => [ 'shape' => 'SendDataToWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartBulkAssociateWirelessDeviceWithMulticastGroup' => [ 'name' => 'StartBulkAssociateWirelessDeviceWithMulticastGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/multicast-groups/{Id}/bulk', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StartBulkAssociateWirelessDeviceWithMulticastGroupRequest', ], 'output' => [ 'shape' => 'StartBulkAssociateWirelessDeviceWithMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartBulkDisassociateWirelessDeviceFromMulticastGroup' => [ 'name' => 'StartBulkDisassociateWirelessDeviceFromMulticastGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/multicast-groups/{Id}/bulk', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StartBulkDisassociateWirelessDeviceFromMulticastGroupRequest', ], 'output' => [ 'shape' => 'StartBulkDisassociateWirelessDeviceFromMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartFuotaTask' => [ 'name' => 'StartFuotaTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/fuota-tasks/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StartFuotaTaskRequest', ], 'output' => [ 'shape' => 'StartFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartMulticastGroupSession' => [ 'name' => 'StartMulticastGroupSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/multicast-groups/{Id}/session', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StartMulticastGroupSessionRequest', ], 'output' => [ 'shape' => 'StartMulticastGroupSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartSingleWirelessDeviceImportTask' => [ 'name' => 'StartSingleWirelessDeviceImportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless_single_device_import_task', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartSingleWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'StartSingleWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartWirelessDeviceImportTask' => [ 'name' => 'StartWirelessDeviceImportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless_device_import_task', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'StartWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'TestWirelessDevice' => [ 'name' => 'TestWirelessDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/wireless-devices/{Id}/test', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TestWirelessDeviceRequest', ], 'output' => [ 'shape' => 'TestWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateDestination' => [ 'name' => 'UpdateDestination', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/destinations/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateDestinationRequest', ], 'output' => [ 'shape' => 'UpdateDestinationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateEventConfigurationByResourceTypes' => [ 'name' => 'UpdateEventConfigurationByResourceTypes', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/event-configurations-resource-types', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateEventConfigurationByResourceTypesRequest', ], 'output' => [ 'shape' => 'UpdateEventConfigurationByResourceTypesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateFuotaTask' => [ 'name' => 'UpdateFuotaTask', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/fuota-tasks/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateFuotaTaskRequest', ], 'output' => [ 'shape' => 'UpdateFuotaTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateLogLevelsByResourceTypes' => [ 'name' => 'UpdateLogLevelsByResourceTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/log-levels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLogLevelsByResourceTypesRequest', ], 'output' => [ 'shape' => 'UpdateLogLevelsByResourceTypesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateMetricConfiguration' => [ 'name' => 'UpdateMetricConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/metric-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateMetricConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateMetricConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateMulticastGroup' => [ 'name' => 'UpdateMulticastGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/multicast-groups/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateMulticastGroupRequest', ], 'output' => [ 'shape' => 'UpdateMulticastGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateNetworkAnalyzerConfiguration' => [ 'name' => 'UpdateNetworkAnalyzerConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/network-analyzer-configurations/{ConfigurationName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateNetworkAnalyzerConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateNetworkAnalyzerConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdatePartnerAccount' => [ 'name' => 'UpdatePartnerAccount', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/partner-accounts/{PartnerAccountId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdatePartnerAccountRequest', ], 'output' => [ 'shape' => 'UpdatePartnerAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdatePosition' => [ 'name' => 'UpdatePosition', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/positions/{ResourceIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdatePositionRequest', ], 'output' => [ 'shape' => 'UpdatePositionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'UpdateResourceEventConfiguration' => [ 'name' => 'UpdateResourceEventConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/event-configurations/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateResourceEventConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateResourceEventConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateResourcePosition' => [ 'name' => 'UpdateResourcePosition', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resource-positions/{ResourceIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateResourcePositionRequest', ], 'output' => [ 'shape' => 'UpdateResourcePositionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateWirelessDevice' => [ 'name' => 'UpdateWirelessDevice', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/wireless-devices/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateWirelessDeviceRequest', ], 'output' => [ 'shape' => 'UpdateWirelessDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateWirelessDeviceImportTask' => [ 'name' => 'UpdateWirelessDeviceImportTask', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/wireless_device_import_task/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateWirelessDeviceImportTaskRequest', ], 'output' => [ 'shape' => 'UpdateWirelessDeviceImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateWirelessGateway' => [ 'name' => 'UpdateWirelessGateway', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/wireless-gateways/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateWirelessGatewayRequest', ], 'output' => [ 'shape' => 'UpdateWirelessGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AbpV1_0_x' => [ 'type' => 'structure', 'members' => [ 'DevAddr' => [ 'shape' => 'DevAddr', ], 'SessionKeys' => [ 'shape' => 'SessionKeysAbpV1_0_x', ], 'FCntStart' => [ 'shape' => 'FCntStart', ], ], ], 'AbpV1_1' => [ 'type' => 'structure', 'members' => [ 'DevAddr' => [ 'shape' => 'DevAddr', ], 'SessionKeys' => [ 'shape' => 'SessionKeysAbpV1_1', ], 'FCntStart' => [ 'shape' => 'FCntStart', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountLinked' => [ 'type' => 'boolean', ], 'Accuracy' => [ 'type' => 'structure', 'members' => [ 'HorizontalAccuracy' => [ 'shape' => 'HorizontalAccuracy', ], 'VerticalAccuracy' => [ 'shape' => 'VerticalAccuracy', ], ], ], 'AckModeRetryDurationSecs' => [ 'type' => 'integer', 'max' => 604800, 'min' => 0, ], 'AddGwMetadata' => [ 'type' => 'boolean', ], 'AggregationPeriod' => [ 'type' => 'string', 'enum' => [ 'OneHour', 'OneDay', 'OneWeek', ], ], 'AmazonId' => [ 'type' => 'string', 'max' => 2048, ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'ApId' => [ 'type' => 'string', 'max' => 256, ], 'AppEui' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{16}', ], 'AppKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'AppSKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'AppServerPrivateKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[a-fA-F0-9]{64}', 'sensitive' => true, ], 'ApplicationConfig' => [ 'type' => 'structure', 'members' => [ 'FPort' => [ 'shape' => 'FPort', ], 'Type' => [ 'shape' => 'ApplicationConfigType', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], ], ], 'ApplicationConfigType' => [ 'type' => 'string', 'enum' => [ 'SemtechGeolocation', ], ], 'ApplicationServerPublicKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[a-fA-F0-9]{64}', 'sensitive' => true, ], 'Applications' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationConfig', ], ], 'AssistPosition' => [ 'type' => 'list', 'member' => [ 'shape' => 'Coordinate', ], 'max' => 2, 'min' => 2, ], 'AssociateAwsAccountWithPartnerAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Sidewalk', ], 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkAccountInfo', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AssociateAwsAccountWithPartnerAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkAccountInfo', ], 'Arn' => [ 'shape' => 'PartnerAccountArn', ], ], ], 'AssociateMulticastGroupWithFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'MulticastGroupId', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'MulticastGroupId' => [ 'shape' => 'MulticastGroupId', ], ], ], 'AssociateMulticastGroupWithFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateWirelessDeviceWithFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WirelessDeviceId', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', ], ], ], 'AssociateWirelessDeviceWithFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateWirelessDeviceWithMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WirelessDeviceId', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', ], ], ], 'AssociateWirelessDeviceWithMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateWirelessDeviceWithThingRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'ThingArn', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], 'ThingArn' => [ 'shape' => 'ThingArn', ], ], ], 'AssociateWirelessDeviceWithThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateWirelessGatewayWithCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IotCertificateId', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], 'IotCertificateId' => [ 'shape' => 'IotCertificateId', ], ], ], 'AssociateWirelessGatewayWithCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'IotCertificateId' => [ 'shape' => 'IotCertificateId', ], ], ], 'AssociateWirelessGatewayWithThingRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'ThingArn', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], 'ThingArn' => [ 'shape' => 'ThingArn', ], ], ], 'AssociateWirelessGatewayWithThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'AutoCreateTasks' => [ 'type' => 'boolean', ], 'Avg' => [ 'type' => 'double', ], 'BCCH' => [ 'type' => 'integer', 'max' => 1023, 'min' => 0, ], 'BSIC' => [ 'type' => 'integer', 'max' => 63, 'min' => 0, ], 'BaseLat' => [ 'type' => 'float', 'max' => 90, 'min' => -90, ], 'BaseLng' => [ 'type' => 'float', 'max' => 180, 'min' => -180, ], 'BaseStationId' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'BatteryLevel' => [ 'type' => 'string', 'enum' => [ 'normal', 'low', 'critical', ], ], 'Beaconing' => [ 'type' => 'structure', 'members' => [ 'DataRate' => [ 'shape' => 'BeaconingDataRate', ], 'Frequencies' => [ 'shape' => 'BeaconingFrequencies', ], ], ], 'BeaconingDataRate' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'BeaconingFrequencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'BeaconingFrequency', ], 'max' => 10, 'min' => 0, ], 'BeaconingFrequency' => [ 'type' => 'integer', 'max' => 1000000000, 'min' => 100000000, ], 'CancelMulticastGroupSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'CancelMulticastGroupSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CaptureTimeAccuracy' => [ 'type' => 'float', ], 'CdmaChannel' => [ 'type' => 'integer', 'max' => 4095, 'min' => 0, ], 'CdmaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CdmaObj', ], 'max' => 16, 'min' => 1, ], 'CdmaLocalId' => [ 'type' => 'structure', 'required' => [ 'PnOffset', 'CdmaChannel', ], 'members' => [ 'PnOffset' => [ 'shape' => 'PnOffset', ], 'CdmaChannel' => [ 'shape' => 'CdmaChannel', ], ], ], 'CdmaNmrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CdmaNmrObj', ], 'max' => 32, 'min' => 1, ], 'CdmaNmrObj' => [ 'type' => 'structure', 'required' => [ 'PnOffset', 'CdmaChannel', ], 'members' => [ 'PnOffset' => [ 'shape' => 'PnOffset', ], 'CdmaChannel' => [ 'shape' => 'CdmaChannel', ], 'PilotPower' => [ 'shape' => 'PilotPower', ], 'BaseStationId' => [ 'shape' => 'BaseStationId', ], ], ], 'CdmaObj' => [ 'type' => 'structure', 'required' => [ 'SystemId', 'NetworkId', 'BaseStationId', ], 'members' => [ 'SystemId' => [ 'shape' => 'SystemId', ], 'NetworkId' => [ 'shape' => 'NetworkId', ], 'BaseStationId' => [ 'shape' => 'BaseStationId', ], 'RegistrationZone' => [ 'shape' => 'RegistrationZone', ], 'CdmaLocalId' => [ 'shape' => 'CdmaLocalId', ], 'PilotPower' => [ 'shape' => 'PilotPower', ], 'BaseLat' => [ 'shape' => 'BaseLat', ], 'BaseLng' => [ 'shape' => 'BaseLng', ], 'CdmaNmr' => [ 'shape' => 'CdmaNmrList', ], ], ], 'CellParams' => [ 'type' => 'integer', 'max' => 127, 'min' => 0, ], 'CellTowers' => [ 'type' => 'structure', 'members' => [ 'Gsm' => [ 'shape' => 'GsmList', ], 'Wcdma' => [ 'shape' => 'WcdmaList', ], 'Tdscdma' => [ 'shape' => 'TdscdmaList', ], 'Lte' => [ 'shape' => 'LteList', ], 'Cdma' => [ 'shape' => 'CdmaList', ], ], ], 'CertificateList' => [ 'type' => 'structure', 'required' => [ 'SigningAlg', 'Value', ], 'members' => [ 'SigningAlg' => [ 'shape' => 'SigningAlg', ], 'Value' => [ 'shape' => 'CertificateValue', ], ], ], 'CertificatePEM' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[^-A-Za-z0-9+/=]|=[^=]|={3,}${1,4096}', ], 'CertificateValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ChannelMask' => [ 'type' => 'string', 'max' => 2048, ], 'ClassBTimeout' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'ClassCTimeout' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'Connected', 'Disconnected', ], ], 'ConnectionStatusEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANConnectionStatusEventNotificationConfigurations', ], 'WirelessGatewayIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'ConnectionStatusResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANConnectionStatusResourceTypeEventConfiguration', ], ], ], 'Coordinate' => [ 'type' => 'float', ], 'Crc' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 1, ], 'CreateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ExpressionType', 'Expression', 'RoleArn', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', ], 'ExpressionType' => [ 'shape' => 'ExpressionType', ], 'Expression' => [ 'shape' => 'Expression', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DestinationArn', ], 'Name' => [ 'shape' => 'DestinationName', ], ], ], 'CreateDeviceProfileRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DeviceProfileName', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANDeviceProfile', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Sidewalk' => [ 'shape' => 'SidewalkCreateDeviceProfile', ], ], ], 'CreateDeviceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DeviceProfileArn', ], 'Id' => [ 'shape' => 'DeviceProfileId', ], ], ], 'CreateFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'FirmwareUpdateImage', 'FirmwareUpdateRole', ], 'members' => [ 'Name' => [ 'shape' => 'FuotaTaskName', ], 'Description' => [ 'shape' => 'Description', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'LoRaWAN' => [ 'shape' => 'LoRaWANFuotaTask', ], 'FirmwareUpdateImage' => [ 'shape' => 'FirmwareUpdateImage', ], 'FirmwareUpdateRole' => [ 'shape' => 'FirmwareUpdateRole', ], 'Tags' => [ 'shape' => 'TagList', ], 'RedundancyPercent' => [ 'shape' => 'RedundancyPercent', ], 'FragmentSizeBytes' => [ 'shape' => 'FragmentSizeBytes', ], 'FragmentIntervalMS' => [ 'shape' => 'FragmentIntervalMS', ], ], ], 'CreateFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'FuotaTaskArn', ], 'Id' => [ 'shape' => 'FuotaTaskId', ], ], ], 'CreateMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'LoRaWAN', ], 'members' => [ 'Name' => [ 'shape' => 'MulticastGroupName', ], 'Description' => [ 'shape' => 'Description', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticast', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'MulticastGroupArn', ], 'Id' => [ 'shape' => 'MulticastGroupId', ], ], ], 'CreateNetworkAnalyzerConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NetworkAnalyzerConfigurationName', ], 'TraceContent' => [ 'shape' => 'TraceContent', ], 'WirelessDevices' => [ 'shape' => 'WirelessDeviceList', ], 'WirelessGateways' => [ 'shape' => 'WirelessGatewayList', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'MulticastGroups' => [ 'shape' => 'NetworkAnalyzerMulticastGroupList', ], ], ], 'CreateNetworkAnalyzerConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NetworkAnalyzerConfigurationArn', ], 'Name' => [ 'shape' => 'NetworkAnalyzerConfigurationName', ], ], ], 'CreateServiceProfileRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ServiceProfileName', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANServiceProfile', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateServiceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ServiceProfileArn', ], 'Id' => [ 'shape' => 'ServiceProfileId', ], ], ], 'CreateWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Type', 'DestinationName', ], 'members' => [ 'Type' => [ 'shape' => 'WirelessDeviceType', ], 'Name' => [ 'shape' => 'WirelessDeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'LoRaWAN' => [ 'shape' => 'LoRaWANDevice', ], 'Tags' => [ 'shape' => 'TagList', ], 'Positioning' => [ 'shape' => 'PositioningConfigStatus', ], 'Sidewalk' => [ 'shape' => 'SidewalkCreateWirelessDevice', ], ], ], 'CreateWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'WirelessDeviceArn', ], 'Id' => [ 'shape' => 'WirelessDeviceId', ], ], ], 'CreateWirelessGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'LoRaWAN', ], 'members' => [ 'Name' => [ 'shape' => 'WirelessGatewayName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANGateway', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateWirelessGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'WirelessGatewayArn', ], 'Id' => [ 'shape' => 'WirelessDeviceId', ], ], ], 'CreateWirelessGatewayTaskDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'AutoCreateTasks', ], 'members' => [ 'AutoCreateTasks' => [ 'shape' => 'AutoCreateTasks', ], 'Name' => [ 'shape' => 'WirelessGatewayTaskName', ], 'Update' => [ 'shape' => 'UpdateWirelessGatewayTaskCreate', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWirelessGatewayTaskDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', ], 'Arn' => [ 'shape' => 'WirelessGatewayTaskDefinitionArn', ], ], ], 'CreateWirelessGatewayTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WirelessGatewayTaskDefinitionId', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], 'WirelessGatewayTaskDefinitionId' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', ], ], ], 'CreateWirelessGatewayTaskResponse' => [ 'type' => 'structure', 'members' => [ 'WirelessGatewayTaskDefinitionId' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', ], 'Status' => [ 'shape' => 'WirelessGatewayTaskStatus', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'CreationDate' => [ 'type' => 'timestamp', ], 'CreationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DakCertificateId' => [ 'type' => 'string', 'max' => 256, ], 'DakCertificateMetadata' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'DakCertificateId', ], 'MaxAllowedSignature' => [ 'shape' => 'MaxAllowedSignature', ], 'FactorySupport' => [ 'shape' => 'FactorySupport', ], 'ApId' => [ 'shape' => 'ApId', ], 'DeviceTypeId' => [ 'shape' => 'DeviceTypeId', ], ], ], 'DakCertificateMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DakCertificateMetadata', ], ], 'DeleteDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeleteDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDeviceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'DeviceProfileId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteDeviceProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNetworkAnalyzerConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationName', ], 'members' => [ 'ConfigurationName' => [ 'shape' => 'NetworkAnalyzerConfigurationName', 'location' => 'uri', 'locationName' => 'ConfigurationName', ], ], ], 'DeleteNetworkAnalyzerConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueuedMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'MessageId', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], 'MessageId' => [ 'shape' => 'MessageId', 'location' => 'querystring', 'locationName' => 'messageId', ], 'WirelessDeviceType' => [ 'shape' => 'WirelessDeviceType', 'location' => 'querystring', 'locationName' => 'WirelessDeviceType', ], ], ], 'DeleteQueuedMessagesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteServiceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ServiceProfileId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteServiceProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWirelessGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteWirelessGatewayResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWirelessGatewayTaskDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteWirelessGatewayTaskDefinitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWirelessGatewayTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteWirelessGatewayTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'WirelessDeviceType' => [ 'shape' => 'WirelessDeviceType', 'location' => 'querystring', 'locationName' => 'WirelessDeviceType', ], ], ], 'DeregisterWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 2048, ], 'DestinationArn' => [ 'type' => 'string', ], 'DestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destinations', ], ], 'DestinationName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9-_]+', ], 'Destinations' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DestinationArn', ], 'Name' => [ 'shape' => 'DestinationName', ], 'ExpressionType' => [ 'shape' => 'ExpressionType', ], 'Expression' => [ 'shape' => 'Expression', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'DevAddr' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{8}', ], 'DevEui' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{16}', ], 'DevStatusReqFreq' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'DeviceCertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateList', ], ], 'DeviceCreationFile' => [ 'type' => 'string', 'max' => 1024, ], 'DeviceCreationFileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceCreationFile', ], ], 'DeviceName' => [ 'type' => 'string', ], 'DeviceProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DeviceProfileArn', ], 'Name' => [ 'shape' => 'DeviceProfileName', ], 'Id' => [ 'shape' => 'DeviceProfileId', ], ], ], 'DeviceProfileArn' => [ 'type' => 'string', ], 'DeviceProfileId' => [ 'type' => 'string', 'max' => 256, ], 'DeviceProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceProfile', ], ], 'DeviceProfileName' => [ 'type' => 'string', 'max' => 256, ], 'DeviceProfileType' => [ 'type' => 'string', 'enum' => [ 'Sidewalk', 'LoRaWAN', ], ], 'DeviceRegistrationStateEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkEventNotificationConfigurations', ], 'WirelessDeviceIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'DeviceRegistrationStateResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkResourceTypeEventConfiguration', ], ], ], 'DeviceState' => [ 'type' => 'string', 'enum' => [ 'Provisioned', 'RegisteredNotSeen', 'RegisteredReachable', 'RegisteredUnreachable', ], ], 'DeviceTypeId' => [ 'type' => 'string', 'max' => 2048, ], 'Dimension' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DimensionName', ], 'value' => [ 'shape' => 'DimensionValue', ], ], ], 'DimensionName' => [ 'type' => 'string', 'enum' => [ 'DeviceId', 'GatewayId', ], 'max' => 256, ], 'DimensionValue' => [ 'type' => 'string', 'max' => 256, ], 'Dimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dimension', ], ], 'DisassociateAwsAccountFromPartnerAccountRequest' => [ 'type' => 'structure', 'required' => [ 'PartnerAccountId', 'PartnerType', ], 'members' => [ 'PartnerAccountId' => [ 'shape' => 'PartnerAccountId', 'location' => 'uri', 'locationName' => 'PartnerAccountId', ], 'PartnerType' => [ 'shape' => 'PartnerType', 'location' => 'querystring', 'locationName' => 'partnerType', ], ], ], 'DisassociateAwsAccountFromPartnerAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMulticastGroupFromFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'MulticastGroupId', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'MulticastGroupId' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'MulticastGroupId', ], ], ], 'DisassociateMulticastGroupFromFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWirelessDeviceFromFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WirelessDeviceId', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'WirelessDeviceId', ], ], ], 'DisassociateWirelessDeviceFromFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWirelessDeviceFromMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WirelessDeviceId', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'WirelessDeviceId', ], ], ], 'DisassociateWirelessDeviceFromMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWirelessDeviceFromThingRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DisassociateWirelessDeviceFromThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWirelessGatewayFromCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DisassociateWirelessGatewayFromCertificateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWirelessGatewayFromThingRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DisassociateWirelessGatewayFromThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'DlBucketSize' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'DlClass' => [ 'type' => 'string', 'enum' => [ 'ClassB', 'ClassC', ], 'max' => 256, ], 'DlDr' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'DlFreq' => [ 'type' => 'integer', 'max' => 1000000000, 'min' => 100000000, ], 'DlRate' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'DlRatePolicy' => [ 'type' => 'string', 'max' => 256, ], 'Double' => [ 'type' => 'double', ], 'DownlinkFrequency' => [ 'type' => 'integer', 'max' => 1000000000, 'min' => 100000000, ], 'DownlinkMode' => [ 'type' => 'string', 'enum' => [ 'SEQUENTIAL', 'CONCURRENT', 'USING_UPLINK_GATEWAY', ], ], 'DownlinkQueueMessage' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'MessageId', ], 'TransmitMode' => [ 'shape' => 'TransmitMode', ], 'ReceivedAt' => [ 'shape' => 'ISODateTimeString', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANSendDataToDevice', ], ], ], 'DownlinkQueueMessagesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DownlinkQueueMessage', ], ], 'DrMax' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'DrMaxBox' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'DrMin' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'DrMinBox' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'EARFCN' => [ 'type' => 'integer', 'max' => 262143, 'min' => 0, ], 'EndPoint' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'EutranCid' => [ 'type' => 'integer', 'max' => 268435455, 'min' => 0, ], 'Event' => [ 'type' => 'string', 'enum' => [ 'discovered', 'lost', 'ack', 'nack', 'passthrough', ], ], 'EventConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', ], 'IdentifierType' => [ 'shape' => 'IdentifierType', ], 'PartnerType' => [ 'shape' => 'EventNotificationPartnerType', ], 'Events' => [ 'shape' => 'EventNotificationItemConfigurations', ], ], ], 'EventConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventConfigurationItem', ], ], 'EventNotificationItemConfigurations' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistrationState' => [ 'shape' => 'DeviceRegistrationStateEventConfiguration', ], 'Proximity' => [ 'shape' => 'ProximityEventConfiguration', ], 'Join' => [ 'shape' => 'JoinEventConfiguration', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatusEventConfiguration', ], 'MessageDeliveryStatus' => [ 'shape' => 'MessageDeliveryStatusEventConfiguration', ], ], ], 'EventNotificationPartnerType' => [ 'type' => 'string', 'enum' => [ 'Sidewalk', ], ], 'EventNotificationResourceType' => [ 'type' => 'string', 'enum' => [ 'SidewalkAccount', 'WirelessDevice', 'WirelessGateway', ], ], 'EventNotificationTopicStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'Expression' => [ 'type' => 'string', 'max' => 2048, ], 'ExpressionType' => [ 'type' => 'string', 'enum' => [ 'RuleName', 'MqttTopic', ], ], 'FCntStart' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'FNwkSIntKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'FPort' => [ 'type' => 'integer', 'max' => 223, 'min' => 1, ], 'FPorts' => [ 'type' => 'structure', 'members' => [ 'Fuota' => [ 'shape' => 'FPort', ], 'Multicast' => [ 'shape' => 'FPort', ], 'ClockSync' => [ 'shape' => 'FPort', ], 'Positioning' => [ 'shape' => 'Positioning', ], 'Applications' => [ 'shape' => 'Applications', ], ], ], 'FactoryPresetFreqsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PresetFreq', ], 'max' => 20, 'min' => 0, ], 'FactorySupport' => [ 'type' => 'boolean', ], 'Fingerprint' => [ 'type' => 'string', 'max' => 64, 'min' => 64, 'pattern' => '[a-fA-F0-9]{64}', 'sensitive' => true, ], 'FirmwareUpdateImage' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'FirmwareUpdateRole' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'FragmentIntervalMS' => [ 'type' => 'integer', 'min' => 1, ], 'FragmentSizeBytes' => [ 'type' => 'integer', 'min' => 1, ], 'FuotaDeviceStatus' => [ 'type' => 'string', 'enum' => [ 'Initial', 'Package_Not_Supported', 'FragAlgo_unsupported', 'Not_enough_memory', 'FragIndex_unsupported', 'Wrong_descriptor', 'SessionCnt_replay', 'MissingFrag', 'MemoryError', 'MICError', 'Successful', ], ], 'FuotaTask' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', ], 'Arn' => [ 'shape' => 'FuotaTaskArn', ], 'Name' => [ 'shape' => 'FuotaTaskName', ], ], ], 'FuotaTaskArn' => [ 'type' => 'string', 'max' => 128, ], 'FuotaTaskId' => [ 'type' => 'string', 'max' => 256, ], 'FuotaTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FuotaTask', ], ], 'FuotaTaskName' => [ 'type' => 'string', 'max' => 256, ], 'FuotaTaskStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'FuotaSession_Waiting', 'In_FuotaSession', 'FuotaDone', 'Delete_Waiting', ], ], 'GPST' => [ 'type' => 'float', ], 'GatewayEui' => [ 'type' => 'string', 'pattern' => '^(([0-9A-Fa-f]{2}-){7}|([0-9A-Fa-f]{2}:){7}|([0-9A-Fa-f]{2}\\s){7}|([0-9A-Fa-f]{2}){7})([0-9A-Fa-f]{2})$', ], 'GatewayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayListItem', ], ], 'GatewayListItem' => [ 'type' => 'structure', 'required' => [ 'GatewayId', 'DownlinkFrequency', ], 'members' => [ 'GatewayId' => [ 'shape' => 'WirelessGatewayId', ], 'DownlinkFrequency' => [ 'shape' => 'DownlinkFrequency', ], ], ], 'GatewayMaxEirp' => [ 'type' => 'float', 'max' => 30, 'min' => 0, ], 'GenAppKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'GeoJsonPayload' => [ 'type' => 'blob', ], 'GeranCid' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'GetDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DestinationArn', ], 'Name' => [ 'shape' => 'DestinationName', ], 'Expression' => [ 'shape' => 'Expression', ], 'ExpressionType' => [ 'shape' => 'ExpressionType', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'GetDeviceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'DeviceProfileId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetDeviceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DeviceProfileArn', ], 'Name' => [ 'shape' => 'DeviceProfileName', ], 'Id' => [ 'shape' => 'DeviceProfileId', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANDeviceProfile', ], 'Sidewalk' => [ 'shape' => 'SidewalkGetDeviceProfile', ], ], ], 'GetEventConfigurationByResourceTypesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetEventConfigurationByResourceTypesResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistrationState' => [ 'shape' => 'DeviceRegistrationStateResourceTypeEventConfiguration', ], 'Proximity' => [ 'shape' => 'ProximityResourceTypeEventConfiguration', ], 'Join' => [ 'shape' => 'JoinResourceTypeEventConfiguration', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatusResourceTypeEventConfiguration', ], 'MessageDeliveryStatus' => [ 'shape' => 'MessageDeliveryStatusResourceTypeEventConfiguration', ], ], ], 'GetFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'FuotaTaskArn', ], 'Id' => [ 'shape' => 'FuotaTaskId', ], 'Status' => [ 'shape' => 'FuotaTaskStatus', ], 'Name' => [ 'shape' => 'FuotaTaskName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANFuotaTaskGetInfo', ], 'FirmwareUpdateImage' => [ 'shape' => 'FirmwareUpdateImage', ], 'FirmwareUpdateRole' => [ 'shape' => 'FirmwareUpdateRole', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'RedundancyPercent' => [ 'shape' => 'RedundancyPercent', ], 'FragmentSizeBytes' => [ 'shape' => 'FragmentSizeBytes', ], 'FragmentIntervalMS' => [ 'shape' => 'FragmentIntervalMS', ], ], ], 'GetLogLevelsByResourceTypesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetLogLevelsByResourceTypesResponse' => [ 'type' => 'structure', 'members' => [ 'DefaultLogLevel' => [ 'shape' => 'LogLevel', ], 'WirelessGatewayLogOptions' => [ 'shape' => 'WirelessGatewayLogOptionList', ], 'WirelessDeviceLogOptions' => [ 'shape' => 'WirelessDeviceLogOptionList', ], ], ], 'GetMetricConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMetricConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryMetric' => [ 'shape' => 'SummaryMetricConfiguration', ], ], ], 'GetMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'SummaryMetricQueries' => [ 'shape' => 'SummaryMetricQueries', ], ], ], 'GetMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'SummaryMetricQueryResults' => [ 'shape' => 'SummaryMetricQueryResults', ], ], ], 'GetMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'MulticastGroupArn', ], 'Id' => [ 'shape' => 'MulticastGroupId', ], 'Name' => [ 'shape' => 'MulticastGroupName', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'MulticastGroupStatus', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticastGet', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], ], ], 'GetMulticastGroupSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetMulticastGroupSessionResponse' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticastSession', ], ], ], 'GetNetworkAnalyzerConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationName', ], 'members' => [ 'ConfigurationName' => [ 'shape' => 'NetworkAnalyzerConfigurationName', 'location' => 'uri', 'locationName' => 'ConfigurationName', ], ], ], 'GetNetworkAnalyzerConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'TraceContent' => [ 'shape' => 'TraceContent', ], 'WirelessDevices' => [ 'shape' => 'WirelessDeviceList', ], 'WirelessGateways' => [ 'shape' => 'WirelessGatewayList', ], 'Description' => [ 'shape' => 'Description', ], 'Arn' => [ 'shape' => 'NetworkAnalyzerConfigurationArn', ], 'Name' => [ 'shape' => 'NetworkAnalyzerConfigurationName', ], 'MulticastGroups' => [ 'shape' => 'NetworkAnalyzerMulticastGroupList', ], ], ], 'GetPartnerAccountRequest' => [ 'type' => 'structure', 'required' => [ 'PartnerAccountId', 'PartnerType', ], 'members' => [ 'PartnerAccountId' => [ 'shape' => 'PartnerAccountId', 'location' => 'uri', 'locationName' => 'PartnerAccountId', ], 'PartnerType' => [ 'shape' => 'PartnerType', 'location' => 'querystring', 'locationName' => 'partnerType', ], ], ], 'GetPartnerAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkAccountInfoWithFingerprint', ], 'AccountLinked' => [ 'shape' => 'AccountLinked', ], ], ], 'GetPositionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetPositionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Solvers' => [ 'shape' => 'PositionSolverDetails', ], 'Destination' => [ 'shape' => 'DestinationName', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetPositionEstimateRequest' => [ 'type' => 'structure', 'members' => [ 'WiFiAccessPoints' => [ 'shape' => 'WiFiAccessPoints', ], 'CellTowers' => [ 'shape' => 'CellTowers', ], 'Ip' => [ 'shape' => 'Ip', ], 'Gnss' => [ 'shape' => 'Gnss', ], 'Timestamp' => [ 'shape' => 'CreationDate', ], ], ], 'GetPositionEstimateResponse' => [ 'type' => 'structure', 'members' => [ 'GeoJsonPayload' => [ 'shape' => 'GeoJsonPayload', ], ], 'payload' => 'GeoJsonPayload', ], 'GetPositionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetPositionResponse' => [ 'type' => 'structure', 'members' => [ 'Position' => [ 'shape' => 'PositionCoordinate', ], 'Accuracy' => [ 'shape' => 'Accuracy', ], 'SolverType' => [ 'shape' => 'PositionSolverType', ], 'SolverProvider' => [ 'shape' => 'PositionSolverProvider', ], 'SolverVersion' => [ 'shape' => 'PositionSolverVersion', ], 'Timestamp' => [ 'shape' => 'ISODateTimeString', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'GetResourceEventConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'IdentifierType', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'IdentifierType' => [ 'shape' => 'IdentifierType', 'location' => 'querystring', 'locationName' => 'identifierType', ], 'PartnerType' => [ 'shape' => 'EventNotificationPartnerType', 'location' => 'querystring', 'locationName' => 'partnerType', ], ], ], 'GetResourceEventConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistrationState' => [ 'shape' => 'DeviceRegistrationStateEventConfiguration', ], 'Proximity' => [ 'shape' => 'ProximityEventConfiguration', ], 'Join' => [ 'shape' => 'JoinEventConfiguration', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatusEventConfiguration', ], 'MessageDeliveryStatus' => [ 'shape' => 'MessageDeliveryStatusEventConfiguration', ], ], ], 'GetResourceLogLevelRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'GetResourceLogLevelResponse' => [ 'type' => 'structure', 'members' => [ 'LogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'GetResourcePositionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'GetResourcePositionResponse' => [ 'type' => 'structure', 'members' => [ 'GeoJsonPayload' => [ 'shape' => 'GeoJsonPayload', ], ], 'payload' => 'GeoJsonPayload', ], 'GetServiceEndpointRequest' => [ 'type' => 'structure', 'members' => [ 'ServiceType' => [ 'shape' => 'WirelessGatewayServiceType', 'location' => 'querystring', 'locationName' => 'serviceType', ], ], ], 'GetServiceEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceType' => [ 'shape' => 'WirelessGatewayServiceType', ], 'ServiceEndpoint' => [ 'shape' => 'EndPoint', ], 'ServerTrust' => [ 'shape' => 'CertificatePEM', ], ], ], 'GetServiceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ServiceProfileId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetServiceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ServiceProfileArn', ], 'Name' => [ 'shape' => 'ServiceProfileName', ], 'Id' => [ 'shape' => 'ServiceProfileId', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANGetServiceProfileInfo', ], ], ], 'GetWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', ], 'Arn' => [ 'shape' => 'ImportTaskArn', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'Sidewalk' => [ 'shape' => 'SidewalkGetStartImportInfo', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Status' => [ 'shape' => 'ImportTaskStatus', ], 'StatusReason' => [ 'shape' => 'StatusReason', ], 'InitializedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'PendingImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'OnboardedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'FailedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], ], ], 'GetWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'IdentifierType', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'IdentifierType' => [ 'shape' => 'WirelessDeviceIdType', 'location' => 'querystring', 'locationName' => 'identifierType', ], ], ], 'GetWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'WirelessDeviceType', ], 'Name' => [ 'shape' => 'WirelessDeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'Id' => [ 'shape' => 'WirelessDeviceId', ], 'Arn' => [ 'shape' => 'WirelessDeviceArn', ], 'ThingName' => [ 'shape' => 'ThingName', ], 'ThingArn' => [ 'shape' => 'ThingArn', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANDevice', ], 'Sidewalk' => [ 'shape' => 'SidewalkDevice', ], 'Positioning' => [ 'shape' => 'PositioningConfigStatus', ], ], ], 'GetWirelessDeviceStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'WirelessDeviceId', ], 'members' => [ 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessDeviceStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'WirelessDeviceId' => [ 'shape' => 'WirelessDeviceId', ], 'LastUplinkReceivedAt' => [ 'shape' => 'ISODateTimeString', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANDeviceMetadata', ], 'Sidewalk' => [ 'shape' => 'SidewalkDeviceMetadata', ], ], ], 'GetWirelessGatewayCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessGatewayCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'IotCertificateId' => [ 'shape' => 'IotCertificateId', ], 'LoRaWANNetworkServerCertificateId' => [ 'shape' => 'IotCertificateId', ], ], ], 'GetWirelessGatewayFirmwareInformationRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessGatewayFirmwareInformationResponse' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANGatewayCurrentVersion', ], ], ], 'GetWirelessGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'IdentifierType', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'IdentifierType' => [ 'shape' => 'WirelessGatewayIdType', 'location' => 'querystring', 'locationName' => 'identifierType', ], ], ], 'GetWirelessGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'WirelessGatewayName', ], 'Id' => [ 'shape' => 'WirelessGatewayId', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANGateway', ], 'Arn' => [ 'shape' => 'WirelessGatewayArn', ], 'ThingName' => [ 'shape' => 'ThingName', ], 'ThingArn' => [ 'shape' => 'ThingArn', ], ], ], 'GetWirelessGatewayStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'WirelessGatewayId', ], 'members' => [ 'WirelessGatewayId' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessGatewayStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'WirelessGatewayId' => [ 'shape' => 'WirelessGatewayId', ], 'LastUplinkReceivedAt' => [ 'shape' => 'ISODateTimeString', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatus', ], ], ], 'GetWirelessGatewayTaskDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessGatewayTaskDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'AutoCreateTasks' => [ 'shape' => 'AutoCreateTasks', ], 'Name' => [ 'shape' => 'WirelessGatewayTaskName', ], 'Update' => [ 'shape' => 'UpdateWirelessGatewayTaskCreate', ], 'Arn' => [ 'shape' => 'WirelessGatewayTaskDefinitionArn', ], ], ], 'GetWirelessGatewayTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetWirelessGatewayTaskResponse' => [ 'type' => 'structure', 'members' => [ 'WirelessGatewayId' => [ 'shape' => 'WirelessGatewayId', ], 'WirelessGatewayTaskDefinitionId' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', ], 'LastUplinkReceivedAt' => [ 'shape' => 'ISODateTimeString', ], 'TaskCreatedAt' => [ 'shape' => 'ISODateTimeString', ], 'Status' => [ 'shape' => 'WirelessGatewayTaskStatus', ], ], ], 'GlobalIdentity' => [ 'type' => 'structure', 'required' => [ 'Lac', 'GeranCid', ], 'members' => [ 'Lac' => [ 'shape' => 'LAC', ], 'GeranCid' => [ 'shape' => 'GeranCid', ], ], ], 'Gnss' => [ 'type' => 'structure', 'required' => [ 'Payload', ], 'members' => [ 'Payload' => [ 'shape' => 'GnssNav', ], 'CaptureTime' => [ 'shape' => 'GPST', ], 'CaptureTimeAccuracy' => [ 'shape' => 'CaptureTimeAccuracy', ], 'AssistPosition' => [ 'shape' => 'AssistPosition', ], 'AssistAltitude' => [ 'shape' => 'Coordinate', ], 'Use2DSolver' => [ 'shape' => 'Use2DSolver', ], ], ], 'GnssNav' => [ 'type' => 'string', 'max' => 2048, ], 'GsmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GsmObj', ], 'max' => 16, 'min' => 1, ], 'GsmLocalId' => [ 'type' => 'structure', 'required' => [ 'Bsic', 'Bcch', ], 'members' => [ 'Bsic' => [ 'shape' => 'BSIC', ], 'Bcch' => [ 'shape' => 'BCCH', ], ], ], 'GsmNmrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GsmNmrObj', ], 'max' => 32, 'min' => 1, ], 'GsmNmrObj' => [ 'type' => 'structure', 'required' => [ 'Bsic', 'Bcch', ], 'members' => [ 'Bsic' => [ 'shape' => 'BSIC', ], 'Bcch' => [ 'shape' => 'BCCH', ], 'RxLevel' => [ 'shape' => 'RxLevel', ], 'GlobalIdentity' => [ 'shape' => 'GlobalIdentity', ], ], ], 'GsmObj' => [ 'type' => 'structure', 'required' => [ 'Mcc', 'Mnc', 'Lac', 'GeranCid', ], 'members' => [ 'Mcc' => [ 'shape' => 'MCC', ], 'Mnc' => [ 'shape' => 'MNC', ], 'Lac' => [ 'shape' => 'LAC', ], 'GeranCid' => [ 'shape' => 'GeranCid', ], 'GsmLocalId' => [ 'shape' => 'GsmLocalId', ], 'GsmTimingAdvance' => [ 'shape' => 'GsmTimingAdvance', ], 'RxLevel' => [ 'shape' => 'RxLevel', ], 'GsmNmr' => [ 'shape' => 'GsmNmrList', ], ], ], 'GsmTimingAdvance' => [ 'type' => 'integer', 'max' => 63, 'min' => 0, ], 'HorizontalAccuracy' => [ 'type' => 'float', 'min' => 0, ], 'HrAllowed' => [ 'type' => 'boolean', ], 'IPAddress' => [ 'type' => 'string', ], 'ISODateTimeString' => [ 'type' => 'string', 'pattern' => '^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))([T\\s]((([01]\\d|2[0-3])((:?)[0-5]\\d)?|24\\:?00)([\\.,]\\d+(?!:))?)?(\\17[0-5]\\d([\\.,]\\d+)?)?([zZ]|([\\+-])([01]\\d|2[0-3]):?([0-5]\\d)?)?)?)?$', ], 'Identifier' => [ 'type' => 'string', 'max' => 256, ], 'IdentifierType' => [ 'type' => 'string', 'enum' => [ 'PartnerAccountId', 'DevEui', 'GatewayEui', 'WirelessDeviceId', 'WirelessGatewayId', ], ], 'ImportTaskArn' => [ 'type' => 'string', 'max' => 128, ], 'ImportTaskId' => [ 'type' => 'string', 'max' => 256, ], 'ImportTaskStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'INITIALIZED', 'PENDING', 'COMPLETE', 'FAILED', 'DELETING', ], ], 'ImportedSidewalkDevice' => [ 'type' => 'structure', 'members' => [ 'SidewalkManufacturingSn' => [ 'shape' => 'SidewalkManufacturingSn', ], 'OnboardingStatus' => [ 'shape' => 'OnboardStatus', ], 'OnboardingStatusReason' => [ 'shape' => 'OnboardStatusReason', ], 'LastUpdateTime' => [ 'shape' => 'LastUpdateTime', ], ], ], 'ImportedWirelessDevice' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'ImportedSidewalkDevice', ], ], ], 'ImportedWirelessDeviceCount' => [ 'type' => 'long', ], 'ImportedWirelessDeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportedWirelessDevice', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'IotCertificateId' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Ip' => [ 'type' => 'structure', 'required' => [ 'IpAddress', ], 'members' => [ 'IpAddress' => [ 'shape' => 'IPAddress', ], ], ], 'JoinEui' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{16}', ], 'JoinEuiFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'JoinEuiRange', ], 'max' => 3, 'min' => 0, ], 'JoinEuiRange' => [ 'type' => 'list', 'member' => [ 'shape' => 'JoinEui', ], 'max' => 2, 'min' => 2, ], 'JoinEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANJoinEventNotificationConfigurations', ], 'WirelessDeviceIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'JoinResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANJoinResourceTypeEventConfiguration', ], ], ], 'LAC' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'LastUpdateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ListDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DestinationList' => [ 'shape' => 'DestinationList', ], ], ], 'ListDeviceProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'DeviceProfileType' => [ 'shape' => 'DeviceProfileType', 'location' => 'querystring', 'locationName' => 'deviceProfileType', ], ], ], 'ListDeviceProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DeviceProfileList' => [ 'shape' => 'DeviceProfileList', ], ], ], 'ListDevicesForWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', 'location' => 'querystring', 'locationName' => 'id', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Status' => [ 'shape' => 'OnboardStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListDevicesForWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'ImportedWirelessDeviceList' => [ 'shape' => 'ImportedWirelessDeviceList', ], ], ], 'ListEventConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', ], 'members' => [ 'ResourceType' => [ 'shape' => 'EventNotificationResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEventConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'EventConfigurationsList' => [ 'shape' => 'EventConfigurationsList', ], ], ], 'ListFuotaTasksRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFuotaTasksResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FuotaTaskList' => [ 'shape' => 'FuotaTaskList', ], ], ], 'ListMulticastGroupsByFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMulticastGroupsByFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MulticastGroupList' => [ 'shape' => 'MulticastGroupListByFuotaTask', ], ], ], 'ListMulticastGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMulticastGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MulticastGroupList' => [ 'shape' => 'MulticastGroupList', ], ], ], 'ListNetworkAnalyzerConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNetworkAnalyzerConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'NetworkAnalyzerConfigurationList' => [ 'shape' => 'NetworkAnalyzerConfigurationList', ], ], ], 'ListPartnerAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPartnerAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Sidewalk' => [ 'shape' => 'SidewalkAccountList', ], ], ], 'ListPositionConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'ListPositionConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'PositionConfigurationList' => [ 'shape' => 'PositionConfigurationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'ListQueuedMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'WirelessDeviceType' => [ 'shape' => 'WirelessDeviceType', 'location' => 'querystring', 'locationName' => 'WirelessDeviceType', ], ], ], 'ListQueuedMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DownlinkQueueMessagesList' => [ 'shape' => 'DownlinkQueueMessagesList', ], ], ], 'ListServiceProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListServiceProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServiceProfileList' => [ 'shape' => 'ServiceProfileList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListWirelessDeviceImportTasksRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListWirelessDeviceImportTasksResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'WirelessDeviceImportTaskList' => [ 'shape' => 'WirelessDeviceImportTaskList', ], ], ], 'ListWirelessDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'DestinationName' => [ 'shape' => 'DestinationName', 'location' => 'querystring', 'locationName' => 'destinationName', ], 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', 'location' => 'querystring', 'locationName' => 'deviceProfileId', ], 'ServiceProfileId' => [ 'shape' => 'ServiceProfileId', 'location' => 'querystring', 'locationName' => 'serviceProfileId', ], 'WirelessDeviceType' => [ 'shape' => 'WirelessDeviceType', 'location' => 'querystring', 'locationName' => 'wirelessDeviceType', ], 'FuotaTaskId' => [ 'shape' => 'FuotaTaskId', 'location' => 'querystring', 'locationName' => 'fuotaTaskId', ], 'MulticastGroupId' => [ 'shape' => 'MulticastGroupId', 'location' => 'querystring', 'locationName' => 'multicastGroupId', ], ], ], 'ListWirelessDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'WirelessDeviceList' => [ 'shape' => 'WirelessDeviceStatisticsList', ], ], ], 'ListWirelessGatewayTaskDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'TaskDefinitionType' => [ 'shape' => 'WirelessGatewayTaskDefinitionType', 'location' => 'querystring', 'locationName' => 'taskDefinitionType', ], ], ], 'ListWirelessGatewayTaskDefinitionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'TaskDefinitions' => [ 'shape' => 'WirelessGatewayTaskDefinitionList', ], ], ], 'ListWirelessGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListWirelessGatewaysResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'WirelessGatewayList' => [ 'shape' => 'WirelessGatewayStatisticsList', ], ], ], 'LoRaWANConnectionStatusEventNotificationConfigurations' => [ 'type' => 'structure', 'members' => [ 'GatewayEuiEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'LoRaWANConnectionStatusResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'WirelessGatewayEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'LoRaWANDevice' => [ 'type' => 'structure', 'members' => [ 'DevEui' => [ 'shape' => 'DevEui', ], 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', ], 'ServiceProfileId' => [ 'shape' => 'ServiceProfileId', ], 'OtaaV1_1' => [ 'shape' => 'OtaaV1_1', ], 'OtaaV1_0_x' => [ 'shape' => 'OtaaV1_0_x', ], 'AbpV1_1' => [ 'shape' => 'AbpV1_1', ], 'AbpV1_0_x' => [ 'shape' => 'AbpV1_0_x', ], 'FPorts' => [ 'shape' => 'FPorts', ], ], ], 'LoRaWANDeviceMetadata' => [ 'type' => 'structure', 'members' => [ 'DevEui' => [ 'shape' => 'DevEui', ], 'FPort' => [ 'shape' => 'Integer', ], 'DataRate' => [ 'shape' => 'Integer', ], 'Frequency' => [ 'shape' => 'Integer', ], 'Timestamp' => [ 'shape' => 'ISODateTimeString', ], 'Gateways' => [ 'shape' => 'LoRaWANGatewayMetadataList', ], ], ], 'LoRaWANDeviceProfile' => [ 'type' => 'structure', 'members' => [ 'SupportsClassB' => [ 'shape' => 'SupportsClassB', ], 'ClassBTimeout' => [ 'shape' => 'ClassBTimeout', ], 'PingSlotPeriod' => [ 'shape' => 'PingSlotPeriod', ], 'PingSlotDr' => [ 'shape' => 'PingSlotDr', ], 'PingSlotFreq' => [ 'shape' => 'PingSlotFreq', ], 'SupportsClassC' => [ 'shape' => 'SupportsClassC', ], 'ClassCTimeout' => [ 'shape' => 'ClassCTimeout', ], 'MacVersion' => [ 'shape' => 'MacVersion', ], 'RegParamsRevision' => [ 'shape' => 'RegParamsRevision', ], 'RxDelay1' => [ 'shape' => 'RxDelay1', ], 'RxDrOffset1' => [ 'shape' => 'RxDrOffset1', ], 'RxDataRate2' => [ 'shape' => 'RxDataRate2', ], 'RxFreq2' => [ 'shape' => 'RxFreq2', ], 'FactoryPresetFreqsList' => [ 'shape' => 'FactoryPresetFreqsList', ], 'MaxEirp' => [ 'shape' => 'MaxEirp', ], 'MaxDutyCycle' => [ 'shape' => 'MaxDutyCycle', ], 'RfRegion' => [ 'shape' => 'RfRegion', ], 'SupportsJoin' => [ 'shape' => 'SupportsJoin', ], 'Supports32BitFCnt' => [ 'shape' => 'Supports32BitFCnt', ], ], ], 'LoRaWANFuotaTask' => [ 'type' => 'structure', 'members' => [ 'RfRegion' => [ 'shape' => 'SupportedRfRegion', ], ], ], 'LoRaWANFuotaTaskGetInfo' => [ 'type' => 'structure', 'members' => [ 'RfRegion' => [ 'shape' => 'RfRegion', ], 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'LoRaWANGateway' => [ 'type' => 'structure', 'members' => [ 'GatewayEui' => [ 'shape' => 'GatewayEui', ], 'RfRegion' => [ 'shape' => 'RfRegion', ], 'JoinEuiFilters' => [ 'shape' => 'JoinEuiFilters', ], 'NetIdFilters' => [ 'shape' => 'NetIdFilters', ], 'SubBands' => [ 'shape' => 'SubBands', ], 'Beaconing' => [ 'shape' => 'Beaconing', ], 'MaxEirp' => [ 'shape' => 'GatewayMaxEirp', ], ], ], 'LoRaWANGatewayCurrentVersion' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => 'LoRaWANGatewayVersion', ], ], ], 'LoRaWANGatewayMetadata' => [ 'type' => 'structure', 'members' => [ 'GatewayEui' => [ 'shape' => 'GatewayEui', ], 'Snr' => [ 'shape' => 'Double', ], 'Rssi' => [ 'shape' => 'Double', ], ], ], 'LoRaWANGatewayMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoRaWANGatewayMetadata', ], ], 'LoRaWANGatewayVersion' => [ 'type' => 'structure', 'members' => [ 'PackageVersion' => [ 'shape' => 'PackageVersion', ], 'Model' => [ 'shape' => 'Model', ], 'Station' => [ 'shape' => 'Station', ], ], ], 'LoRaWANGetServiceProfileInfo' => [ 'type' => 'structure', 'members' => [ 'UlRate' => [ 'shape' => 'UlRate', ], 'UlBucketSize' => [ 'shape' => 'UlBucketSize', ], 'UlRatePolicy' => [ 'shape' => 'UlRatePolicy', ], 'DlRate' => [ 'shape' => 'DlRate', ], 'DlBucketSize' => [ 'shape' => 'DlBucketSize', ], 'DlRatePolicy' => [ 'shape' => 'DlRatePolicy', ], 'AddGwMetadata' => [ 'shape' => 'AddGwMetadata', ], 'DevStatusReqFreq' => [ 'shape' => 'DevStatusReqFreq', ], 'ReportDevStatusBattery' => [ 'shape' => 'ReportDevStatusBattery', ], 'ReportDevStatusMargin' => [ 'shape' => 'ReportDevStatusMargin', ], 'DrMin' => [ 'shape' => 'DrMin', ], 'DrMax' => [ 'shape' => 'DrMax', ], 'ChannelMask' => [ 'shape' => 'ChannelMask', ], 'PrAllowed' => [ 'shape' => 'PrAllowed', ], 'HrAllowed' => [ 'shape' => 'HrAllowed', ], 'RaAllowed' => [ 'shape' => 'RaAllowed', ], 'NwkGeoLoc' => [ 'shape' => 'NwkGeoLoc', ], 'TargetPer' => [ 'shape' => 'TargetPer', ], 'MinGwDiversity' => [ 'shape' => 'MinGwDiversity', ], ], ], 'LoRaWANJoinEventNotificationConfigurations' => [ 'type' => 'structure', 'members' => [ 'DevEuiEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'LoRaWANJoinResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'WirelessDeviceEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'LoRaWANListDevice' => [ 'type' => 'structure', 'members' => [ 'DevEui' => [ 'shape' => 'DevEui', ], ], ], 'LoRaWANMulticast' => [ 'type' => 'structure', 'members' => [ 'RfRegion' => [ 'shape' => 'SupportedRfRegion', ], 'DlClass' => [ 'shape' => 'DlClass', ], ], ], 'LoRaWANMulticastGet' => [ 'type' => 'structure', 'members' => [ 'RfRegion' => [ 'shape' => 'SupportedRfRegion', ], 'DlClass' => [ 'shape' => 'DlClass', ], 'NumberOfDevicesRequested' => [ 'shape' => 'NumberOfDevicesRequested', ], 'NumberOfDevicesInGroup' => [ 'shape' => 'NumberOfDevicesInGroup', ], ], ], 'LoRaWANMulticastMetadata' => [ 'type' => 'structure', 'members' => [ 'FPort' => [ 'shape' => 'FPort', ], ], ], 'LoRaWANMulticastSession' => [ 'type' => 'structure', 'members' => [ 'DlDr' => [ 'shape' => 'DlDr', ], 'DlFreq' => [ 'shape' => 'DlFreq', ], 'SessionStartTime' => [ 'shape' => 'SessionStartTimeTimestamp', ], 'SessionTimeout' => [ 'shape' => 'SessionTimeout', ], 'PingSlotPeriod' => [ 'shape' => 'PingSlotPeriod', ], ], ], 'LoRaWANSendDataToDevice' => [ 'type' => 'structure', 'members' => [ 'FPort' => [ 'shape' => 'FPort', ], 'ParticipatingGateways' => [ 'shape' => 'ParticipatingGateways', ], ], ], 'LoRaWANServiceProfile' => [ 'type' => 'structure', 'members' => [ 'AddGwMetadata' => [ 'shape' => 'AddGwMetadata', ], 'DrMin' => [ 'shape' => 'DrMinBox', ], 'DrMax' => [ 'shape' => 'DrMaxBox', ], 'PrAllowed' => [ 'shape' => 'PrAllowed', ], 'RaAllowed' => [ 'shape' => 'RaAllowed', ], ], ], 'LoRaWANStartFuotaTask' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'LoRaWANUpdateDevice' => [ 'type' => 'structure', 'members' => [ 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', ], 'ServiceProfileId' => [ 'shape' => 'ServiceProfileId', ], 'AbpV1_1' => [ 'shape' => 'UpdateAbpV1_1', ], 'AbpV1_0_x' => [ 'shape' => 'UpdateAbpV1_0_x', ], 'FPorts' => [ 'shape' => 'UpdateFPorts', ], ], ], 'LoRaWANUpdateGatewayTaskCreate' => [ 'type' => 'structure', 'members' => [ 'UpdateSignature' => [ 'shape' => 'UpdateSignature', ], 'SigKeyCrc' => [ 'shape' => 'Crc', ], 'CurrentVersion' => [ 'shape' => 'LoRaWANGatewayVersion', ], 'UpdateVersion' => [ 'shape' => 'LoRaWANGatewayVersion', ], ], ], 'LoRaWANUpdateGatewayTaskEntry' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => 'LoRaWANGatewayVersion', ], 'UpdateVersion' => [ 'shape' => 'LoRaWANGatewayVersion', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'INFO', 'ERROR', 'DISABLED', ], ], 'LteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LteObj', ], 'max' => 16, 'min' => 1, ], 'LteLocalId' => [ 'type' => 'structure', 'required' => [ 'Pci', 'Earfcn', ], 'members' => [ 'Pci' => [ 'shape' => 'PCI', ], 'Earfcn' => [ 'shape' => 'EARFCN', ], ], ], 'LteNmrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LteNmrObj', ], 'max' => 32, 'min' => 1, ], 'LteNmrObj' => [ 'type' => 'structure', 'required' => [ 'Pci', 'Earfcn', 'EutranCid', ], 'members' => [ 'Pci' => [ 'shape' => 'PCI', ], 'Earfcn' => [ 'shape' => 'EARFCN', ], 'EutranCid' => [ 'shape' => 'EutranCid', ], 'Rsrp' => [ 'shape' => 'RSRP', ], 'Rsrq' => [ 'shape' => 'RSRQ', ], ], ], 'LteObj' => [ 'type' => 'structure', 'required' => [ 'Mcc', 'Mnc', 'EutranCid', ], 'members' => [ 'Mcc' => [ 'shape' => 'MCC', ], 'Mnc' => [ 'shape' => 'MNC', ], 'EutranCid' => [ 'shape' => 'EutranCid', ], 'Tac' => [ 'shape' => 'TAC', ], 'LteLocalId' => [ 'shape' => 'LteLocalId', ], 'LteTimingAdvance' => [ 'shape' => 'LteTimingAdvance', ], 'Rsrp' => [ 'shape' => 'RSRP', ], 'Rsrq' => [ 'shape' => 'RSRQ', ], 'NrCapable' => [ 'shape' => 'NRCapable', ], 'LteNmr' => [ 'shape' => 'LteNmrList', ], ], ], 'LteTimingAdvance' => [ 'type' => 'integer', 'max' => 1282, 'min' => 0, ], 'MCC' => [ 'type' => 'integer', 'max' => 999, 'min' => 200, ], 'MNC' => [ 'type' => 'integer', 'max' => 999, 'min' => 0, ], 'MacAddress' => [ 'type' => 'string', 'max' => 17, 'min' => 12, 'pattern' => '^([0-9A-Fa-f]{2}[:-]?){5}([0-9A-Fa-f]{2})$', ], 'MacVersion' => [ 'type' => 'string', 'max' => 64, ], 'Max' => [ 'type' => 'double', ], 'MaxAllowedSignature' => [ 'type' => 'integer', ], 'MaxDutyCycle' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'MaxEirp' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 0, ], 'McGroupId' => [ 'type' => 'integer', 'max' => 256, 'min' => 1, ], 'Message' => [ 'type' => 'string', 'max' => 2048, ], 'MessageDeliveryStatusEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkEventNotificationConfigurations', ], 'WirelessDeviceIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'MessageDeliveryStatusResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkResourceTypeEventConfiguration', ], ], ], 'MessageId' => [ 'type' => 'string', ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM_COMMAND_ID_NOTIFY', 'CUSTOM_COMMAND_ID_GET', 'CUSTOM_COMMAND_ID_SET', 'CUSTOM_COMMAND_ID_RESP', ], ], 'MetricName' => [ 'type' => 'string', 'enum' => [ 'DeviceRSSI', 'DeviceSNR', 'DeviceUplinkCount', 'DeviceDownlinkCount', 'DeviceUplinkLostCount', 'DeviceUplinkLostRate', 'DeviceJoinRequestCount', 'DeviceJoinAcceptCount', 'DeviceRoamingUplinkCount', 'DeviceRoamingDownlinkCount', 'GatewayUpTime', 'GatewayDownTime', 'GatewayRSSI', 'GatewaySNR', 'GatewayUplinkCount', 'GatewayDownlinkCount', 'GatewayJoinRequestCount', 'GatewayJoinAcceptCount', 'AwsAccountUplinkCount', 'AwsAccountDownlinkCount', 'AwsAccountUplinkLostCount', 'AwsAccountUplinkLostRate', 'AwsAccountJoinRequestCount', 'AwsAccountJoinAcceptCount', 'AwsAccountRoamingUplinkCount', 'AwsAccountRoamingDownlinkCount', 'AwsAccountDeviceCount', 'AwsAccountGatewayCount', 'AwsAccountActiveDeviceCount', 'AwsAccountActiveGatewayCount', ], 'max' => 256, ], 'MetricQueryEndTimestamp' => [ 'type' => 'timestamp', ], 'MetricQueryError' => [ 'type' => 'string', 'max' => 256, ], 'MetricQueryId' => [ 'type' => 'string', 'max' => 256, ], 'MetricQueryStartTimestamp' => [ 'type' => 'timestamp', ], 'MetricQueryStatus' => [ 'type' => 'string', 'enum' => [ 'Succeeded', 'Failed', ], ], 'MetricQueryTimestamp' => [ 'type' => 'timestamp', ], 'MetricQueryTimestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricQueryTimestamp', ], ], 'MetricQueryValue' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'Min', ], 'Max' => [ 'shape' => 'Max', ], 'Sum' => [ 'shape' => 'Sum', ], 'Avg' => [ 'shape' => 'Avg', ], 'Std' => [ 'shape' => 'Std', ], 'P90' => [ 'shape' => 'P90', ], ], ], 'MetricQueryValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricQueryValue', ], ], 'MetricUnit' => [ 'type' => 'string', 'max' => 256, ], 'Min' => [ 'type' => 'double', ], 'MinGwDiversity' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Model' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'MulticastDeviceStatus' => [ 'type' => 'string', 'max' => 256, ], 'MulticastFrameInfo' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'MulticastGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', ], 'Arn' => [ 'shape' => 'MulticastGroupArn', ], 'Name' => [ 'shape' => 'MulticastGroupName', ], ], ], 'MulticastGroupArn' => [ 'type' => 'string', 'max' => 128, ], 'MulticastGroupByFuotaTask' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', ], ], ], 'MulticastGroupId' => [ 'type' => 'string', 'max' => 256, ], 'MulticastGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastGroup', ], ], 'MulticastGroupListByFuotaTask' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastGroupByFuotaTask', ], ], 'MulticastGroupMessageId' => [ 'type' => 'string', 'max' => 256, ], 'MulticastGroupName' => [ 'type' => 'string', 'max' => 256, ], 'MulticastGroupStatus' => [ 'type' => 'string', 'max' => 256, ], 'MulticastWirelessMetadata' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticastMetadata', ], ], ], 'NRCapable' => [ 'type' => 'boolean', ], 'NetId' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{6}', ], 'NetIdFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetId', ], 'max' => 10, 'min' => 0, ], 'NetworkAnalyzerConfigurationArn' => [ 'type' => 'string', 'max' => 1124, ], 'NetworkAnalyzerConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkAnalyzerConfigurations', ], ], 'NetworkAnalyzerConfigurationName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]+', ], 'NetworkAnalyzerConfigurations' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NetworkAnalyzerConfigurationArn', ], 'Name' => [ 'shape' => 'NetworkAnalyzerConfigurationName', ], ], ], 'NetworkAnalyzerMulticastGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastGroupId', ], 'max' => 10, 'min' => 0, ], 'NetworkId' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, ], 'NumberOfDevicesInGroup' => [ 'type' => 'integer', ], 'NumberOfDevicesRequested' => [ 'type' => 'integer', ], 'NwkGeoLoc' => [ 'type' => 'boolean', ], 'NwkKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'NwkSEncKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'NwkSKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'OnboardStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PENDING', 'ONBOARDED', 'FAILED', ], ], 'OnboardStatusReason' => [ 'type' => 'string', ], 'OtaaV1_0_x' => [ 'type' => 'structure', 'members' => [ 'AppKey' => [ 'shape' => 'AppKey', ], 'AppEui' => [ 'shape' => 'AppEui', ], 'JoinEui' => [ 'shape' => 'JoinEui', ], 'GenAppKey' => [ 'shape' => 'GenAppKey', ], ], ], 'OtaaV1_1' => [ 'type' => 'structure', 'members' => [ 'AppKey' => [ 'shape' => 'AppKey', ], 'NwkKey' => [ 'shape' => 'NwkKey', ], 'JoinEui' => [ 'shape' => 'JoinEui', ], ], ], 'P90' => [ 'type' => 'double', ], 'PCI' => [ 'type' => 'integer', 'max' => 503, 'min' => 0, ], 'PSC' => [ 'type' => 'integer', 'max' => 511, 'min' => 0, ], 'PackageVersion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'ParticipatingGateways' => [ 'type' => 'structure', 'required' => [ 'DownlinkMode', 'GatewayList', 'TransmissionInterval', ], 'members' => [ 'DownlinkMode' => [ 'shape' => 'DownlinkMode', ], 'GatewayList' => [ 'shape' => 'GatewayList', ], 'TransmissionInterval' => [ 'shape' => 'TransmissionInterval', ], ], ], 'PartnerAccountArn' => [ 'type' => 'string', ], 'PartnerAccountId' => [ 'type' => 'string', 'max' => 256, ], 'PartnerType' => [ 'type' => 'string', 'enum' => [ 'Sidewalk', ], ], 'PathLoss' => [ 'type' => 'integer', 'max' => 158, 'min' => 46, ], 'PayloadData' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$', ], 'PilotPower' => [ 'type' => 'integer', 'max' => -49, 'min' => -142, ], 'PingSlotDr' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'PingSlotFreq' => [ 'type' => 'integer', 'max' => ********, 'min' => 1000000, ], 'PingSlotPeriod' => [ 'type' => 'integer', 'max' => 4096, 'min' => 32, ], 'PnOffset' => [ 'type' => 'integer', 'max' => 511, 'min' => 0, ], 'PositionConfigurationFec' => [ 'type' => 'string', 'enum' => [ 'ROSE', 'NONE', ], ], 'PositionConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', ], 'Solvers' => [ 'shape' => 'PositionSolverDetails', ], 'Destination' => [ 'shape' => 'DestinationName', ], ], ], 'PositionConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PositionConfigurationItem', ], ], 'PositionConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'PositionCoordinate' => [ 'type' => 'list', 'member' => [ 'shape' => 'PositionCoordinateValue', ], ], 'PositionCoordinateValue' => [ 'type' => 'float', ], 'PositionResourceIdentifier' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}', ], 'PositionResourceType' => [ 'type' => 'string', 'enum' => [ 'WirelessDevice', 'WirelessGateway', ], ], 'PositionSolverConfigurations' => [ 'type' => 'structure', 'members' => [ 'SemtechGnss' => [ 'shape' => 'SemtechGnssConfiguration', ], ], ], 'PositionSolverDetails' => [ 'type' => 'structure', 'members' => [ 'SemtechGnss' => [ 'shape' => 'SemtechGnssDetail', ], ], ], 'PositionSolverProvider' => [ 'type' => 'string', 'enum' => [ 'Semtech', ], ], 'PositionSolverType' => [ 'type' => 'string', 'enum' => [ 'GNSS', ], ], 'PositionSolverVersion' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'Positioning' => [ 'type' => 'structure', 'members' => [ 'ClockSync' => [ 'shape' => 'FPort', ], 'Stream' => [ 'shape' => 'FPort', ], 'Gnss' => [ 'shape' => 'FPort', ], ], ], 'PositioningConfigStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'PrAllowed' => [ 'type' => 'boolean', ], 'PresetFreq' => [ 'type' => 'integer', 'max' => ********, 'min' => 1000000, ], 'PrivateKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateList', ], ], 'ProximityEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkEventNotificationConfigurations', ], 'WirelessDeviceIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'ProximityResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkResourceTypeEventConfiguration', ], ], ], 'PutPositionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'Solvers' => [ 'shape' => 'PositionSolverConfigurations', ], 'Destination' => [ 'shape' => 'DestinationName', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'PutPositionConfigurationResponse' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'PutResourceLogLevelRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', 'LogLevel', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'PutResourceLogLevelResponse' => [ 'type' => 'structure', 'members' => [], ], 'QualificationStatus' => [ 'type' => 'boolean', ], 'QueryString' => [ 'type' => 'string', 'max' => 4096, ], 'RSCP' => [ 'type' => 'integer', 'max' => -25, 'min' => -120, ], 'RSRP' => [ 'type' => 'integer', 'max' => -44, 'min' => -140, ], 'RSRQ' => [ 'type' => 'float', 'max' => -3, 'min' => -19.5, ], 'RSS' => [ 'type' => 'integer', 'max' => 0, 'min' => -128, ], 'RaAllowed' => [ 'type' => 'boolean', ], 'RedundancyPercent' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'RegParamsRevision' => [ 'type' => 'string', 'max' => 64, ], 'RegistrationZone' => [ 'type' => 'integer', 'max' => 4095, 'min' => 0, ], 'ReportDevStatusBattery' => [ 'type' => 'boolean', ], 'ReportDevStatusMargin' => [ 'type' => 'boolean', ], 'ResetAllResourceLogLevelsRequest' => [ 'type' => 'structure', 'members' => [], ], 'ResetAllResourceLogLevelsResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResetResourceLogLevelRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'ResetResourceLogLevelResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 256, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'Result' => [ 'type' => 'string', 'max' => 2048, ], 'RfRegion' => [ 'type' => 'string', 'max' => 64, ], 'Role' => [ 'type' => 'string', 'max' => 2048, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'RxDataRate2' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'RxDelay1' => [ 'type' => 'integer', 'max' => 15, 'min' => 0, ], 'RxDrOffset1' => [ 'type' => 'integer', 'max' => 7, 'min' => 0, ], 'RxFreq2' => [ 'type' => 'integer', 'max' => ********, 'min' => 1000000, ], 'RxLevel' => [ 'type' => 'integer', 'max' => -25, 'min' => -110, ], 'SNwkSIntKey' => [ 'type' => 'string', 'pattern' => '[a-fA-F0-9]{32}', ], 'SemtechGnssConfiguration' => [ 'type' => 'structure', 'required' => [ 'Status', 'Fec', ], 'members' => [ 'Status' => [ 'shape' => 'PositionConfigurationStatus', ], 'Fec' => [ 'shape' => 'PositionConfigurationFec', ], ], ], 'SemtechGnssDetail' => [ 'type' => 'structure', 'members' => [ 'Provider' => [ 'shape' => 'PositionSolverProvider', ], 'Type' => [ 'shape' => 'PositionSolverType', ], 'Status' => [ 'shape' => 'PositionConfigurationStatus', ], 'Fec' => [ 'shape' => 'PositionConfigurationFec', ], ], ], 'SendDataToMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'PayloadData', 'WirelessMetadata', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'PayloadData' => [ 'shape' => 'PayloadData', ], 'WirelessMetadata' => [ 'shape' => 'MulticastWirelessMetadata', ], ], ], 'SendDataToMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'MulticastGroupMessageId', ], ], ], 'SendDataToWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'TransmitMode', 'PayloadData', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], 'TransmitMode' => [ 'shape' => 'TransmitMode', ], 'PayloadData' => [ 'shape' => 'PayloadData', ], 'WirelessMetadata' => [ 'shape' => 'WirelessMetadata', ], ], ], 'SendDataToWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'MessageId', ], ], ], 'Seq' => [ 'type' => 'integer', 'max' => 16383, 'min' => 0, ], 'ServiceProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ServiceProfileArn', ], 'Name' => [ 'shape' => 'ServiceProfileName', ], 'Id' => [ 'shape' => 'ServiceProfileId', ], ], ], 'ServiceProfileArn' => [ 'type' => 'string', ], 'ServiceProfileId' => [ 'type' => 'string', 'max' => 256, ], 'ServiceProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceProfile', ], ], 'ServiceProfileName' => [ 'type' => 'string', 'max' => 256, ], 'SessionKeysAbpV1_0_x' => [ 'type' => 'structure', 'members' => [ 'NwkSKey' => [ 'shape' => 'NwkSKey', ], 'AppSKey' => [ 'shape' => 'AppSKey', ], ], ], 'SessionKeysAbpV1_1' => [ 'type' => 'structure', 'members' => [ 'FNwkSIntKey' => [ 'shape' => 'FNwkSIntKey', ], 'SNwkSIntKey' => [ 'shape' => 'SNwkSIntKey', ], 'NwkSEncKey' => [ 'shape' => 'NwkSEncKey', ], 'AppSKey' => [ 'shape' => 'AppSKey', ], ], ], 'SessionStartTimeTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'SessionTimeout' => [ 'type' => 'integer', 'max' => 172800, 'min' => 60, ], 'SidewalkAccountInfo' => [ 'type' => 'structure', 'members' => [ 'AmazonId' => [ 'shape' => 'AmazonId', ], 'AppServerPrivateKey' => [ 'shape' => 'AppServerPrivateKey', ], ], ], 'SidewalkAccountInfoWithFingerprint' => [ 'type' => 'structure', 'members' => [ 'AmazonId' => [ 'shape' => 'AmazonId', ], 'Fingerprint' => [ 'shape' => 'Fingerprint', ], 'Arn' => [ 'shape' => 'PartnerAccountArn', ], ], ], 'SidewalkAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SidewalkAccountInfoWithFingerprint', ], ], 'SidewalkCreateDeviceProfile' => [ 'type' => 'structure', 'members' => [], ], 'SidewalkCreateWirelessDevice' => [ 'type' => 'structure', 'members' => [ 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', ], ], ], 'SidewalkDevice' => [ 'type' => 'structure', 'members' => [ 'AmazonId' => [ 'shape' => 'AmazonId', ], 'SidewalkId' => [ 'shape' => 'SidewalkId', ], 'SidewalkManufacturingSn' => [ 'shape' => 'SidewalkManufacturingSn', ], 'DeviceCertificates' => [ 'shape' => 'DeviceCertificateList', ], 'PrivateKeys' => [ 'shape' => 'PrivateKeysList', ], 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', ], 'CertificateId' => [ 'shape' => 'DakCertificateId', ], 'Status' => [ 'shape' => 'WirelessDeviceSidewalkStatus', ], ], ], 'SidewalkDeviceMetadata' => [ 'type' => 'structure', 'members' => [ 'Rssi' => [ 'shape' => 'Integer', ], 'BatteryLevel' => [ 'shape' => 'BatteryLevel', ], 'Event' => [ 'shape' => 'Event', ], 'DeviceState' => [ 'shape' => 'DeviceState', ], ], ], 'SidewalkEventNotificationConfigurations' => [ 'type' => 'structure', 'members' => [ 'AmazonIdEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'SidewalkGetDeviceProfile' => [ 'type' => 'structure', 'members' => [ 'ApplicationServerPublicKey' => [ 'shape' => 'ApplicationServerPublicKey', ], 'QualificationStatus' => [ 'shape' => 'QualificationStatus', ], 'DakCertificateMetadata' => [ 'shape' => 'DakCertificateMetadataList', ], ], ], 'SidewalkGetStartImportInfo' => [ 'type' => 'structure', 'members' => [ 'DeviceCreationFileList' => [ 'shape' => 'DeviceCreationFileList', ], 'Role' => [ 'shape' => 'Role', ], ], ], 'SidewalkId' => [ 'type' => 'string', 'max' => 256, ], 'SidewalkListDevice' => [ 'type' => 'structure', 'members' => [ 'AmazonId' => [ 'shape' => 'AmazonId', ], 'SidewalkId' => [ 'shape' => 'SidewalkId', ], 'SidewalkManufacturingSn' => [ 'shape' => 'SidewalkManufacturingSn', ], 'DeviceCertificates' => [ 'shape' => 'DeviceCertificateList', ], 'DeviceProfileId' => [ 'shape' => 'DeviceProfileId', ], 'Status' => [ 'shape' => 'WirelessDeviceSidewalkStatus', ], ], ], 'SidewalkManufacturingSn' => [ 'type' => 'string', 'max' => 64, ], 'SidewalkResourceTypeEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'WirelessDeviceEventTopic' => [ 'shape' => 'EventNotificationTopicStatus', ], ], ], 'SidewalkSendDataToDevice' => [ 'type' => 'structure', 'members' => [ 'Seq' => [ 'shape' => 'Seq', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'AckModeRetryDurationSecs' => [ 'shape' => 'AckModeRetryDurationSecs', ], ], ], 'SidewalkSingleStartImportInfo' => [ 'type' => 'structure', 'members' => [ 'SidewalkManufacturingSn' => [ 'shape' => 'SidewalkManufacturingSn', ], ], ], 'SidewalkStartImportInfo' => [ 'type' => 'structure', 'members' => [ 'DeviceCreationFile' => [ 'shape' => 'DeviceCreationFile', ], 'Role' => [ 'shape' => 'Role', ], ], ], 'SidewalkUpdateAccount' => [ 'type' => 'structure', 'members' => [ 'AppServerPrivateKey' => [ 'shape' => 'AppServerPrivateKey', ], ], ], 'SidewalkUpdateImportInfo' => [ 'type' => 'structure', 'members' => [ 'DeviceCreationFile' => [ 'shape' => 'DeviceCreationFile', ], ], ], 'SigningAlg' => [ 'type' => 'string', 'enum' => [ 'Ed25519', 'P256r1', ], ], 'StartBulkAssociateWirelessDeviceWithMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'QueryString' => [ 'shape' => 'QueryString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartBulkAssociateWirelessDeviceWithMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartBulkDisassociateWirelessDeviceFromMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'QueryString' => [ 'shape' => 'QueryString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartBulkDisassociateWirelessDeviceFromMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANStartFuotaTask', ], ], ], 'StartFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartMulticastGroupSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'LoRaWAN', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticastSession', ], ], ], 'StartMulticastGroupSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartSingleWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationName', 'Sidewalk', ], 'members' => [ 'DestinationName' => [ 'shape' => 'DestinationName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'Tags' => [ 'shape' => 'TagList', ], 'Sidewalk' => [ 'shape' => 'SidewalkSingleStartImportInfo', ], ], ], 'StartSingleWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', ], 'Arn' => [ 'shape' => 'ImportTaskArn', ], ], ], 'StartTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'StartWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationName', 'Sidewalk', ], 'members' => [ 'DestinationName' => [ 'shape' => 'DestinationName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'Sidewalk' => [ 'shape' => 'SidewalkStartImportInfo', ], ], ], 'StartWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', ], 'Arn' => [ 'shape' => 'ImportTaskArn', ], ], ], 'Station' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'StatusReason' => [ 'type' => 'string', ], 'Std' => [ 'type' => 'double', ], 'SubBand' => [ 'type' => 'integer', 'max' => 8, 'min' => 1, ], 'SubBands' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubBand', ], 'max' => 8, 'min' => 0, ], 'Sum' => [ 'type' => 'double', ], 'SummaryMetricConfiguration' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'SummaryMetricConfigurationStatus', ], ], ], 'SummaryMetricConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'SummaryMetricQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SummaryMetricQuery', ], ], 'SummaryMetricQuery' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'MetricQueryId', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'AggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'StartTimestamp' => [ 'shape' => 'MetricQueryStartTimestamp', ], 'EndTimestamp' => [ 'shape' => 'MetricQueryEndTimestamp', ], ], ], 'SummaryMetricQueryResult' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'MetricQueryId', ], 'QueryStatus' => [ 'shape' => 'MetricQueryStatus', ], 'Error' => [ 'shape' => 'MetricQueryError', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'AggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'StartTimestamp' => [ 'shape' => 'MetricQueryStartTimestamp', ], 'EndTimestamp' => [ 'shape' => 'MetricQueryEndTimestamp', ], 'Timestamps' => [ 'shape' => 'MetricQueryTimestamps', ], 'Values' => [ 'shape' => 'MetricQueryValues', ], 'Unit' => [ 'shape' => 'MetricUnit', ], ], ], 'SummaryMetricQueryResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'SummaryMetricQueryResult', ], ], 'SupportedRfRegion' => [ 'type' => 'string', 'enum' => [ 'EU868', 'US915', 'AU915', 'AS923-1', 'AS923-2', 'AS923-3', 'AS923-4', 'EU433', 'CN470', 'CN779', 'RU864', 'KR920', 'IN865', ], ], 'Supports32BitFCnt' => [ 'type' => 'boolean', ], 'SupportsClassB' => [ 'type' => 'boolean', ], 'SupportsClassC' => [ 'type' => 'boolean', ], 'SupportsJoin' => [ 'type' => 'boolean', ], 'SystemId' => [ 'type' => 'integer', 'max' => 32767, 'min' => 1, ], 'TAC' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetPer' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'TdscdmaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TdscdmaObj', ], 'max' => 16, 'min' => 1, ], 'TdscdmaLocalId' => [ 'type' => 'structure', 'required' => [ 'Uarfcn', 'CellParams', ], 'members' => [ 'Uarfcn' => [ 'shape' => 'UARFCN', ], 'CellParams' => [ 'shape' => 'CellParams', ], ], ], 'TdscdmaNmrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TdscdmaNmrObj', ], 'max' => 32, 'min' => 1, ], 'TdscdmaNmrObj' => [ 'type' => 'structure', 'required' => [ 'Uarfcn', 'CellParams', ], 'members' => [ 'Uarfcn' => [ 'shape' => 'UARFCN', ], 'CellParams' => [ 'shape' => 'CellParams', ], 'UtranCid' => [ 'shape' => 'UtranCid', ], 'Rscp' => [ 'shape' => 'RSCP', ], 'PathLoss' => [ 'shape' => 'PathLoss', ], ], ], 'TdscdmaObj' => [ 'type' => 'structure', 'required' => [ 'Mcc', 'Mnc', 'UtranCid', ], 'members' => [ 'Mcc' => [ 'shape' => 'MCC', ], 'Mnc' => [ 'shape' => 'MNC', ], 'Lac' => [ 'shape' => 'LAC', ], 'UtranCid' => [ 'shape' => 'UtranCid', ], 'TdscdmaLocalId' => [ 'shape' => 'TdscdmaLocalId', ], 'TdscdmaTimingAdvance' => [ 'shape' => 'TdscdmaTimingAdvance', ], 'Rscp' => [ 'shape' => 'RSCP', ], 'PathLoss' => [ 'shape' => 'PathLoss', ], 'TdscdmaNmr' => [ 'shape' => 'TdscdmaNmrList', ], ], ], 'TdscdmaTimingAdvance' => [ 'type' => 'integer', 'max' => 1530, 'min' => 0, ], 'TestWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'TestWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'Result', ], ], ], 'ThingArn' => [ 'type' => 'string', ], 'ThingName' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TraceContent' => [ 'type' => 'structure', 'members' => [ 'WirelessDeviceFrameInfo' => [ 'shape' => 'WirelessDeviceFrameInfo', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], 'MulticastFrameInfo' => [ 'shape' => 'MulticastFrameInfo', ], ], ], 'TransmissionInterval' => [ 'type' => 'integer', 'max' => 604800, 'min' => 1, ], 'TransmitMode' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'UARFCN' => [ 'type' => 'integer', 'max' => 16383, 'min' => 0, ], 'UARFCNDL' => [ 'type' => 'integer', 'max' => 16383, 'min' => 0, ], 'UlBucketSize' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'UlRate' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 0, ], 'UlRatePolicy' => [ 'type' => 'string', 'max' => 256, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAbpV1_0_x' => [ 'type' => 'structure', 'members' => [ 'FCntStart' => [ 'shape' => 'FCntStart', ], ], ], 'UpdateAbpV1_1' => [ 'type' => 'structure', 'members' => [ 'FCntStart' => [ 'shape' => 'FCntStart', ], ], ], 'UpdateDataSource' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'UpdateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], 'ExpressionType' => [ 'shape' => 'ExpressionType', ], 'Expression' => [ 'shape' => 'Expression', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEventConfigurationByResourceTypesRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistrationState' => [ 'shape' => 'DeviceRegistrationStateResourceTypeEventConfiguration', ], 'Proximity' => [ 'shape' => 'ProximityResourceTypeEventConfiguration', ], 'Join' => [ 'shape' => 'JoinResourceTypeEventConfiguration', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatusResourceTypeEventConfiguration', ], 'MessageDeliveryStatus' => [ 'shape' => 'MessageDeliveryStatusResourceTypeEventConfiguration', ], ], ], 'UpdateEventConfigurationByResourceTypesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFPorts' => [ 'type' => 'structure', 'members' => [ 'Positioning' => [ 'shape' => 'Positioning', ], 'Applications' => [ 'shape' => 'Applications', ], ], ], 'UpdateFuotaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'FuotaTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'Name' => [ 'shape' => 'FuotaTaskName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANFuotaTask', ], 'FirmwareUpdateImage' => [ 'shape' => 'FirmwareUpdateImage', ], 'FirmwareUpdateRole' => [ 'shape' => 'FirmwareUpdateRole', ], 'RedundancyPercent' => [ 'shape' => 'RedundancyPercent', ], 'FragmentSizeBytes' => [ 'shape' => 'FragmentSizeBytes', ], 'FragmentIntervalMS' => [ 'shape' => 'FragmentIntervalMS', ], ], ], 'UpdateFuotaTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLogLevelsByResourceTypesRequest' => [ 'type' => 'structure', 'members' => [ 'DefaultLogLevel' => [ 'shape' => 'LogLevel', ], 'WirelessDeviceLogOptions' => [ 'shape' => 'WirelessDeviceLogOptionList', ], 'WirelessGatewayLogOptions' => [ 'shape' => 'WirelessGatewayLogOptionList', ], ], ], 'UpdateLogLevelsByResourceTypesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMetricConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'SummaryMetric' => [ 'shape' => 'SummaryMetricConfiguration', ], ], ], 'UpdateMetricConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMulticastGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MulticastGroupId', 'location' => 'uri', 'locationName' => 'Id', ], 'Name' => [ 'shape' => 'MulticastGroupName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANMulticast', ], ], ], 'UpdateMulticastGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateNetworkAnalyzerConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationName', ], 'members' => [ 'ConfigurationName' => [ 'shape' => 'NetworkAnalyzerConfigurationName', 'location' => 'uri', 'locationName' => 'ConfigurationName', ], 'TraceContent' => [ 'shape' => 'TraceContent', ], 'WirelessDevicesToAdd' => [ 'shape' => 'WirelessDeviceList', ], 'WirelessDevicesToRemove' => [ 'shape' => 'WirelessDeviceList', ], 'WirelessGatewaysToAdd' => [ 'shape' => 'WirelessGatewayList', ], 'WirelessGatewaysToRemove' => [ 'shape' => 'WirelessGatewayList', ], 'Description' => [ 'shape' => 'Description', ], 'MulticastGroupsToAdd' => [ 'shape' => 'NetworkAnalyzerMulticastGroupList', ], 'MulticastGroupsToRemove' => [ 'shape' => 'NetworkAnalyzerMulticastGroupList', ], ], ], 'UpdateNetworkAnalyzerConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePartnerAccountRequest' => [ 'type' => 'structure', 'required' => [ 'Sidewalk', 'PartnerAccountId', 'PartnerType', ], 'members' => [ 'Sidewalk' => [ 'shape' => 'SidewalkUpdateAccount', ], 'PartnerAccountId' => [ 'shape' => 'PartnerAccountId', 'location' => 'uri', 'locationName' => 'PartnerAccountId', ], 'PartnerType' => [ 'shape' => 'PartnerType', 'location' => 'querystring', 'locationName' => 'partnerType', ], ], ], 'UpdatePartnerAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePositionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', 'Position', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'Position' => [ 'shape' => 'PositionCoordinate', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'UpdatePositionResponse' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This operation is no longer supported.', ], 'UpdateResourceEventConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'IdentifierType', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'IdentifierType' => [ 'shape' => 'IdentifierType', 'location' => 'querystring', 'locationName' => 'identifierType', ], 'PartnerType' => [ 'shape' => 'EventNotificationPartnerType', 'location' => 'querystring', 'locationName' => 'partnerType', ], 'DeviceRegistrationState' => [ 'shape' => 'DeviceRegistrationStateEventConfiguration', ], 'Proximity' => [ 'shape' => 'ProximityEventConfiguration', ], 'Join' => [ 'shape' => 'JoinEventConfiguration', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatusEventConfiguration', ], 'MessageDeliveryStatus' => [ 'shape' => 'MessageDeliveryStatusEventConfiguration', ], ], ], 'UpdateResourceEventConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourcePositionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ResourceType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'PositionResourceIdentifier', 'location' => 'uri', 'locationName' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'PositionResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'GeoJsonPayload' => [ 'shape' => 'GeoJsonPayload', ], ], 'payload' => 'GeoJsonPayload', ], 'UpdateResourcePositionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSignature' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'UpdateWirelessDeviceImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'Sidewalk', ], 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', 'location' => 'uri', 'locationName' => 'Id', ], 'Sidewalk' => [ 'shape' => 'SidewalkUpdateImportInfo', ], ], ], 'UpdateWirelessDeviceImportTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWirelessDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessDeviceId', 'location' => 'uri', 'locationName' => 'Id', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'Name' => [ 'shape' => 'WirelessDeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANUpdateDevice', ], 'Positioning' => [ 'shape' => 'PositioningConfigStatus', ], ], ], 'UpdateWirelessDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWirelessGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayId', 'location' => 'uri', 'locationName' => 'Id', ], 'Name' => [ 'shape' => 'WirelessGatewayName', ], 'Description' => [ 'shape' => 'Description', ], 'JoinEuiFilters' => [ 'shape' => 'JoinEuiFilters', ], 'NetIdFilters' => [ 'shape' => 'NetIdFilters', ], 'MaxEirp' => [ 'shape' => 'GatewayMaxEirp', ], ], ], 'UpdateWirelessGatewayResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWirelessGatewayTaskCreate' => [ 'type' => 'structure', 'members' => [ 'UpdateDataSource' => [ 'shape' => 'UpdateDataSource', ], 'UpdateDataRole' => [ 'shape' => 'UpdateDataSource', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANUpdateGatewayTaskCreate', ], ], ], 'UpdateWirelessGatewayTaskEntry' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WirelessGatewayTaskDefinitionId', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANUpdateGatewayTaskEntry', ], 'Arn' => [ 'shape' => 'WirelessGatewayTaskDefinitionArn', ], ], ], 'Use2DSolver' => [ 'type' => 'boolean', ], 'UtranCid' => [ 'type' => 'integer', 'max' => 268435455, 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'VerticalAccuracy' => [ 'type' => 'float', 'min' => 0, ], 'WcdmaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WcdmaObj', ], 'max' => 16, 'min' => 1, ], 'WcdmaLocalId' => [ 'type' => 'structure', 'required' => [ 'Uarfcndl', 'Psc', ], 'members' => [ 'Uarfcndl' => [ 'shape' => 'UARFCNDL', ], 'Psc' => [ 'shape' => 'PSC', ], ], ], 'WcdmaNmrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WcdmaNmrObj', ], 'max' => 32, 'min' => 1, ], 'WcdmaNmrObj' => [ 'type' => 'structure', 'required' => [ 'Uarfcndl', 'Psc', 'UtranCid', ], 'members' => [ 'Uarfcndl' => [ 'shape' => 'UARFCNDL', ], 'Psc' => [ 'shape' => 'PSC', ], 'UtranCid' => [ 'shape' => 'UtranCid', ], 'Rscp' => [ 'shape' => 'RSCP', ], 'PathLoss' => [ 'shape' => 'PathLoss', ], ], ], 'WcdmaObj' => [ 'type' => 'structure', 'required' => [ 'Mcc', 'Mnc', 'UtranCid', ], 'members' => [ 'Mcc' => [ 'shape' => 'MCC', ], 'Mnc' => [ 'shape' => 'MNC', ], 'Lac' => [ 'shape' => 'LAC', ], 'UtranCid' => [ 'shape' => 'UtranCid', ], 'WcdmaLocalId' => [ 'shape' => 'WcdmaLocalId', ], 'Rscp' => [ 'shape' => 'RSCP', ], 'PathLoss' => [ 'shape' => 'PathLoss', ], 'WcdmaNmr' => [ 'shape' => 'WcdmaNmrList', ], ], ], 'WiFiAccessPoint' => [ 'type' => 'structure', 'required' => [ 'MacAddress', 'Rss', ], 'members' => [ 'MacAddress' => [ 'shape' => 'MacAddress', ], 'Rss' => [ 'shape' => 'RSS', ], ], ], 'WiFiAccessPoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'WiFiAccessPoint', ], ], 'WirelessDeviceArn' => [ 'type' => 'string', ], 'WirelessDeviceEvent' => [ 'type' => 'string', 'enum' => [ 'Join', 'Rejoin', 'Uplink_Data', 'Downlink_Data', 'Registration', ], ], 'WirelessDeviceEventLogOption' => [ 'type' => 'structure', 'required' => [ 'Event', 'LogLevel', ], 'members' => [ 'Event' => [ 'shape' => 'WirelessDeviceEvent', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'WirelessDeviceEventLogOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessDeviceEventLogOption', ], ], 'WirelessDeviceFrameInfo' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'WirelessDeviceId' => [ 'type' => 'string', 'max' => 256, ], 'WirelessDeviceIdType' => [ 'type' => 'string', 'enum' => [ 'WirelessDeviceId', 'DevEui', 'ThingName', 'SidewalkManufacturingSn', ], ], 'WirelessDeviceImportTask' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ImportTaskId', ], 'Arn' => [ 'shape' => 'ImportTaskArn', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'Sidewalk' => [ 'shape' => 'SidewalkGetStartImportInfo', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Status' => [ 'shape' => 'ImportTaskStatus', ], 'StatusReason' => [ 'shape' => 'StatusReason', ], 'InitializedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'PendingImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'OnboardedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], 'FailedImportedDeviceCount' => [ 'shape' => 'ImportedWirelessDeviceCount', ], ], ], 'WirelessDeviceImportTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessDeviceImportTask', ], ], 'WirelessDeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessDeviceId', ], 'max' => 250, 'min' => 0, ], 'WirelessDeviceLogOption' => [ 'type' => 'structure', 'required' => [ 'Type', 'LogLevel', ], 'members' => [ 'Type' => [ 'shape' => 'WirelessDeviceType', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], 'Events' => [ 'shape' => 'WirelessDeviceEventLogOptionList', ], ], ], 'WirelessDeviceLogOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessDeviceLogOption', ], ], 'WirelessDeviceName' => [ 'type' => 'string', 'max' => 256, ], 'WirelessDeviceSidewalkStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONED', 'REGISTERED', 'ACTIVATED', 'UNKNOWN', ], ], 'WirelessDeviceStatistics' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'WirelessDeviceArn', ], 'Id' => [ 'shape' => 'WirelessDeviceId', ], 'Type' => [ 'shape' => 'WirelessDeviceType', ], 'Name' => [ 'shape' => 'WirelessDeviceName', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'LastUplinkReceivedAt' => [ 'shape' => 'ISODateTimeString', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANListDevice', ], 'Sidewalk' => [ 'shape' => 'SidewalkListDevice', ], 'FuotaDeviceStatus' => [ 'shape' => 'FuotaDeviceStatus', ], 'MulticastDeviceStatus' => [ 'shape' => 'MulticastDeviceStatus', ], 'McGroupId' => [ 'shape' => 'McGroupId', ], ], ], 'WirelessDeviceStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessDeviceStatistics', ], ], 'WirelessDeviceType' => [ 'type' => 'string', 'enum' => [ 'Sidewalk', 'LoRaWAN', ], ], 'WirelessGatewayArn' => [ 'type' => 'string', ], 'WirelessGatewayEvent' => [ 'type' => 'string', 'enum' => [ 'CUPS_Request', 'Certificate', ], ], 'WirelessGatewayEventLogOption' => [ 'type' => 'structure', 'required' => [ 'Event', 'LogLevel', ], 'members' => [ 'Event' => [ 'shape' => 'WirelessGatewayEvent', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'WirelessGatewayEventLogOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessGatewayEventLogOption', ], ], 'WirelessGatewayId' => [ 'type' => 'string', 'max' => 256, ], 'WirelessGatewayIdType' => [ 'type' => 'string', 'enum' => [ 'GatewayEui', 'WirelessGatewayId', 'ThingName', ], ], 'WirelessGatewayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessGatewayId', ], ], 'WirelessGatewayLogOption' => [ 'type' => 'structure', 'required' => [ 'Type', 'LogLevel', ], 'members' => [ 'Type' => [ 'shape' => 'WirelessGatewayType', ], 'LogLevel' => [ 'shape' => 'LogLevel', ], 'Events' => [ 'shape' => 'WirelessGatewayEventLogOptionList', ], ], ], 'WirelessGatewayLogOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessGatewayLogOption', ], ], 'WirelessGatewayName' => [ 'type' => 'string', 'max' => 256, ], 'WirelessGatewayServiceType' => [ 'type' => 'string', 'enum' => [ 'CUPS', 'LNS', ], ], 'WirelessGatewayStatistics' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'WirelessGatewayArn', ], 'Id' => [ 'shape' => 'WirelessGatewayId', ], 'Name' => [ 'shape' => 'WirelessGatewayName', ], 'Description' => [ 'shape' => 'Description', ], 'LoRaWAN' => [ 'shape' => 'LoRaWANGateway', ], 'LastUplinkReceivedAt' => [ 'shape' => 'ISODateTimeString', ], ], ], 'WirelessGatewayStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WirelessGatewayStatistics', ], ], 'WirelessGatewayTaskDefinitionArn' => [ 'type' => 'string', ], 'WirelessGatewayTaskDefinitionId' => [ 'type' => 'string', 'max' => 36, 'pattern' => '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}', ], 'WirelessGatewayTaskDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateWirelessGatewayTaskEntry', ], ], 'WirelessGatewayTaskDefinitionType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', ], ], 'WirelessGatewayTaskName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'WirelessGatewayTaskStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'FIRST_RETRY', 'SECOND_RETRY', 'COMPLETED', 'FAILED', ], ], 'WirelessGatewayType' => [ 'type' => 'string', 'enum' => [ 'LoRaWAN', ], ], 'WirelessMetadata' => [ 'type' => 'structure', 'members' => [ 'LoRaWAN' => [ 'shape' => 'LoRaWANSendDataToDevice', ], 'Sidewalk' => [ 'shape' => 'SidewalkSendDataToDevice', ], ], ], ],];
