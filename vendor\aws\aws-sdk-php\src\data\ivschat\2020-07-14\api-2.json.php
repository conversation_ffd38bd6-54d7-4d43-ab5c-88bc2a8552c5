<?php
// This file was auto-generated from sdk-root/src/data/ivschat/2020-07-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-14', 'endpointPrefix' => 'ivschat', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'ivschat', 'serviceFullName' => 'Amazon Interactive Video Service Chat', 'serviceId' => 'ivschat', 'signatureVersion' => 'v4', 'signingName' => 'ivschat', 'uid' => 'ivschat-2020-07-14', ], 'operations' => [ 'CreateChatToken' => [ 'name' => 'CreateChatToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateChatToken', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChatTokenRequest', ], 'output' => [ 'shape' => 'CreateChatTokenResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateLoggingConfiguration' => [ 'name' => 'CreateLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLoggingConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'CreateLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateRoom' => [ 'name' => 'CreateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateRoom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRoomRequest', ], 'output' => [ 'shape' => 'CreateRoomResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteLoggingConfiguration' => [ 'name' => 'DeleteLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLoggingConfiguration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteLoggingConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteMessage' => [ 'name' => 'DeleteMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteMessage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMessageRequest', ], 'output' => [ 'shape' => 'DeleteMessageResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRoom' => [ 'name' => 'DeleteRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteRoom', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRoomRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'DisconnectUser' => [ 'name' => 'DisconnectUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectUser', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectUserRequest', ], 'output' => [ 'shape' => 'DisconnectUserResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'GetLoggingConfiguration' => [ 'name' => 'GetLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLoggingConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRoom' => [ 'name' => 'GetRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetRoom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRoomRequest', ], 'output' => [ 'shape' => 'GetRoomResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListLoggingConfigurations' => [ 'name' => 'ListLoggingConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLoggingConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLoggingConfigurationsRequest', ], 'output' => [ 'shape' => 'ListLoggingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListRooms' => [ 'name' => 'ListRooms', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListRooms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoomsRequest', ], 'output' => [ 'shape' => 'ListRoomsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'SendEvent' => [ 'name' => 'SendEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/SendEvent', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendEventRequest', ], 'output' => [ 'shape' => 'SendEventResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateLoggingConfiguration' => [ 'name' => 'UpdateLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLoggingConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateRoom' => [ 'name' => 'UpdateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateRoom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRoomRequest', ], 'output' => [ 'shape' => 'UpdateRoomResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9-.]+$', ], 'ChatToken' => [ 'type' => 'string', 'sensitive' => true, ], 'ChatTokenAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], 'sensitive' => true, ], 'ChatTokenCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChatTokenCapability', ], ], 'ChatTokenCapability' => [ 'type' => 'string', 'enum' => [ 'SEND_MESSAGE', 'DISCONNECT_USER', 'DELETE_MESSAGE', ], ], 'CloudWatchLogsDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'logGroupName', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateChatTokenRequest' => [ 'type' => 'structure', 'required' => [ 'roomIdentifier', 'userId', ], 'members' => [ 'attributes' => [ 'shape' => 'ChatTokenAttributes', ], 'capabilities' => [ 'shape' => 'ChatTokenCapabilities', ], 'roomIdentifier' => [ 'shape' => 'RoomIdentifier', ], 'sessionDurationInMinutes' => [ 'shape' => 'SessionDurationInMinutes', ], 'userId' => [ 'shape' => 'UserID', ], ], ], 'CreateChatTokenResponse' => [ 'type' => 'structure', 'members' => [ 'sessionExpirationTime' => [ 'shape' => 'Time', ], 'token' => [ 'shape' => 'ChatToken', ], 'tokenExpirationTime' => [ 'shape' => 'Time', ], ], ], 'CreateLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationConfiguration', ], 'members' => [ 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LoggingConfigurationArn', ], 'createTime' => [ 'shape' => 'Time', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'id' => [ 'shape' => 'LoggingConfigurationID', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], 'state' => [ 'shape' => 'CreateLoggingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'CreateLoggingConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'CreateRoomRequest' => [ 'type' => 'structure', 'members' => [ 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'maximumMessageLength' => [ 'shape' => 'RoomMaxMessageLength', ], 'maximumMessageRatePerSecond' => [ 'shape' => 'RoomMaxMessageRatePerSecond', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RoomArn', ], 'createTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'RoomID', ], 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'maximumMessageLength' => [ 'shape' => 'RoomMaxMessageLength', ], 'maximumMessageRatePerSecond' => [ 'shape' => 'RoomMaxMessageRatePerSecond', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'DeleteLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'LoggingConfigurationIdentifier', ], ], ], 'DeleteMessageRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'roomIdentifier', ], 'members' => [ 'id' => [ 'shape' => 'MessageID', ], 'reason' => [ 'shape' => 'Reason', ], 'roomIdentifier' => [ 'shape' => 'RoomIdentifier', ], ], ], 'DeleteMessageResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'DeleteRoomRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'RoomIdentifier', ], ], ], 'DeliveryStreamName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'DestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsDestinationConfiguration', ], 'firehose' => [ 'shape' => 'FirehoseDestinationConfiguration', ], 's3' => [ 'shape' => 'S3DestinationConfiguration', ], ], 'union' => true, ], 'DisconnectUserRequest' => [ 'type' => 'structure', 'required' => [ 'roomIdentifier', 'userId', ], 'members' => [ 'reason' => [ 'shape' => 'Reason', ], 'roomIdentifier' => [ 'shape' => 'RoomIdentifier', ], 'userId' => [ 'shape' => 'UserID', ], ], ], 'DisconnectUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'EventName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'FallbackResult' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'FieldName' => [ 'type' => 'string', ], 'FirehoseDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'deliveryStreamName', ], 'members' => [ 'deliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], ], ], 'GetLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'LoggingConfigurationIdentifier', ], ], ], 'GetLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LoggingConfigurationArn', ], 'createTime' => [ 'shape' => 'Time', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'id' => [ 'shape' => 'LoggingConfigurationID', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], 'state' => [ 'shape' => 'LoggingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'GetRoomRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'RoomIdentifier', ], ], ], 'GetRoomResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RoomArn', ], 'createTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'RoomID', ], 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'maximumMessageLength' => [ 'shape' => 'RoomMaxMessageLength', ], 'maximumMessageRatePerSecond' => [ 'shape' => 'RoomMaxMessageRatePerSecond', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'ID' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[a-zA-Z0-9]+$', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 170, 'min' => 0, 'pattern' => '^$|^arn:aws:lambda:[a-z0-9-]+:[0-9]{12}:function:.+', ], 'Limit' => [ 'type' => 'integer', ], 'ListLoggingConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxLoggingConfigurationResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLoggingConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'loggingConfigurations', ], 'members' => [ 'loggingConfigurations' => [ 'shape' => 'LoggingConfigurationList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRoomsRequest' => [ 'type' => 'structure', 'members' => [ 'loggingConfigurationIdentifier' => [ 'shape' => 'LoggingConfigurationIdentifier', ], 'maxResults' => [ 'shape' => 'MaxRoomResults', ], 'messageReviewHandlerUri' => [ 'shape' => 'LambdaArn', ], 'name' => [ 'shape' => 'RoomName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRoomsResponse' => [ 'type' => 'structure', 'required' => [ 'rooms', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'rooms' => [ 'shape' => 'RoomList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[\\.\\-_/#A-Za-z0-9]+$', ], 'LoggingConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivschat:[a-z0-9-]+:[0-9]+:logging-configuration/[a-zA-Z0-9-]+$', ], 'LoggingConfigurationID' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[a-zA-Z0-9]+$', ], 'LoggingConfigurationIdentifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivschat:[a-z0-9-]+:[0-9]+:logging-configuration/[a-zA-Z0-9-]+$', ], 'LoggingConfigurationIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoggingConfigurationIdentifier', ], 'max' => 3, 'min' => 0, ], 'LoggingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoggingConfigurationSummary', ], ], 'LoggingConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'LoggingConfigurationState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'DELETING', 'DELETE_FAILED', 'UPDATING', 'UPDATE_FAILED', 'ACTIVE', ], ], 'LoggingConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LoggingConfigurationArn', ], 'createTime' => [ 'shape' => 'Time', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'id' => [ 'shape' => 'LoggingConfigurationID', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], 'state' => [ 'shape' => 'LoggingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'MaxLoggingConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxRoomResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MessageID' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[a-zA-Z0-9]+$', ], 'MessageReviewHandler' => [ 'type' => 'structure', 'members' => [ 'fallbackResult' => [ 'shape' => 'FallbackResult', ], 'uri' => [ 'shape' => 'LambdaArn', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'PendingVerification' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Reason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivschat:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+$', ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'ROOM', ], ], 'RoomArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivschat:[a-z0-9-]+:[0-9]+:room/[a-zA-Z0-9-]+$', ], 'RoomID' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[a-zA-Z0-9]+$', ], 'RoomIdentifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivschat:[a-z0-9-]+:[0-9]+:room/[a-zA-Z0-9-]+$', ], 'RoomList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomSummary', ], ], 'RoomMaxMessageLength' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'RoomMaxMessageRatePerSecond' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'RoomName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'RoomSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RoomArn', ], 'createTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'RoomID', ], 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], ], ], 'SendEventRequest' => [ 'type' => 'structure', 'required' => [ 'eventName', 'roomIdentifier', ], 'members' => [ 'attributes' => [ 'shape' => 'EventAttributes', ], 'eventName' => [ 'shape' => 'EventName', ], 'roomIdentifier' => [ 'shape' => 'RoomIdentifier', ], ], ], 'SendEventResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'limit', 'message', 'resourceId', 'resourceType', ], 'members' => [ 'limit' => [ 'shape' => 'Limit', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionDurationInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 180, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'limit', 'message', 'resourceId', 'resourceType', ], 'members' => [ 'limit' => [ 'shape' => 'Limit', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'identifier' => [ 'shape' => 'LoggingConfigurationIdentifier', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], ], ], 'UpdateLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LoggingConfigurationArn', ], 'createTime' => [ 'shape' => 'Time', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'id' => [ 'shape' => 'LoggingConfigurationID', ], 'name' => [ 'shape' => 'LoggingConfigurationName', ], 'state' => [ 'shape' => 'UpdateLoggingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'UpdateLoggingConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'UpdateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'RoomIdentifier', ], 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'maximumMessageLength' => [ 'shape' => 'RoomMaxMessageLength', ], 'maximumMessageRatePerSecond' => [ 'shape' => 'RoomMaxMessageRatePerSecond', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], ], ], 'UpdateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RoomArn', ], 'createTime' => [ 'shape' => 'Time', ], 'id' => [ 'shape' => 'RoomID', ], 'loggingConfigurationIdentifiers' => [ 'shape' => 'LoggingConfigurationIdentifierList', ], 'maximumMessageLength' => [ 'shape' => 'RoomMaxMessageLength', ], 'maximumMessageRatePerSecond' => [ 'shape' => 'RoomMaxMessageRatePerSecond', ], 'messageReviewHandler' => [ 'shape' => 'MessageReviewHandler', ], 'name' => [ 'shape' => 'RoomName', ], 'tags' => [ 'shape' => 'Tags', ], 'updateTime' => [ 'shape' => 'Time', ], ], ], 'UserID' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'name' => [ 'shape' => 'FieldName', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], ],];
