<?php
// This file was auto-generated from sdk-root/src/data/bedrock/2023-04-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-04-20', 'endpointPrefix' => 'bedrock', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Bedrock', 'serviceId' => 'Bedrock', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-2023-04-20', ], 'operations' => [ 'CreateModelCustomizationJob' => [ 'name' => 'CreateModelCustomizationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-customization-jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'CreateModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateProvisionedModelThroughput' => [ 'name' => 'CreateProvisionedModelThroughput', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioned-model-throughput', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'CreateProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteCustomModel' => [ 'name' => 'DeleteCustomModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/custom-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomModelRequest', ], 'output' => [ 'shape' => 'DeleteCustomModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteModelInvocationLoggingConfiguration' => [ 'name' => 'DeleteModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteProvisionedModelThroughput' => [ 'name' => 'DeleteProvisionedModelThroughput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'DeleteProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetCustomModel' => [ 'name' => 'GetCustomModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCustomModelRequest', ], 'output' => [ 'shape' => 'GetCustomModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFoundationModel' => [ 'name' => 'GetFoundationModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/foundation-models/{modelIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFoundationModelRequest', ], 'output' => [ 'shape' => 'GetFoundationModelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelCustomizationJob' => [ 'name' => 'GetModelCustomizationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-customization-jobs/{jobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'GetModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetModelInvocationLoggingConfiguration' => [ 'name' => 'GetModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetProvisionedModelThroughput' => [ 'name' => 'GetProvisionedModelThroughput', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'GetProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCustomModels' => [ 'name' => 'ListCustomModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomModelsRequest', ], 'output' => [ 'shape' => 'ListCustomModelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFoundationModels' => [ 'name' => 'ListFoundationModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/foundation-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFoundationModelsRequest', ], 'output' => [ 'shape' => 'ListFoundationModelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListModelCustomizationJobs' => [ 'name' => 'ListModelCustomizationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/model-customization-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListModelCustomizationJobsRequest', ], 'output' => [ 'shape' => 'ListModelCustomizationJobsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListProvisionedModelThroughputs' => [ 'name' => 'ListProvisionedModelThroughputs', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioned-model-throughputs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProvisionedModelThroughputsRequest', ], 'output' => [ 'shape' => 'ListProvisionedModelThroughputsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/listTagsForResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutModelInvocationLoggingConfiguration' => [ 'name' => 'PutModelInvocationLoggingConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logging/modelinvocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutModelInvocationLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutModelInvocationLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopModelCustomizationJob' => [ 'name' => 'StopModelCustomizationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/model-customization-jobs/{jobIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopModelCustomizationJobRequest', ], 'output' => [ 'shape' => 'StopModelCustomizationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateProvisionedModelThroughput' => [ 'name' => 'UpdateProvisionedModelThroughput', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/provisioned-model-throughput/{provisionedModelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProvisionedModelThroughputRequest', ], 'output' => [ 'shape' => 'UpdateProvisionedModelThroughputResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BaseModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2})))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})|(([0-9a-zA-Z][_-]?)+)', ], 'BedrockModelId' => [ 'type' => 'string', 'max' => 140, 'min' => 0, 'pattern' => '[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}(/[a-z0-9]{12}|)', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BrandedName' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '.*', ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'CloudWatchConfig' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'roleArn', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'largeDataDeliveryS3Config' => [ 'shape' => 'S3Config', ], ], ], 'CommitmentDuration' => [ 'type' => 'string', 'enum' => [ 'OneMonth', 'SixMonths', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'customModelName', 'roleArn', 'baseModelIdentifier', 'trainingDataConfig', 'outputDataConfig', 'hyperParameters', ], 'members' => [ 'jobName' => [ 'shape' => 'JobName', ], 'customModelName' => [ 'shape' => 'CustomModelName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'baseModelIdentifier' => [ 'shape' => 'BaseModelIdentifier', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'customModelKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'jobTags' => [ 'shape' => 'TagList', ], 'customModelTags' => [ 'shape' => 'TagList', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'CreateModelCustomizationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], ], ], 'CreateProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'modelUnits', 'provisionedModelName', 'modelId', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'modelId' => [ 'shape' => 'ModelIdentifier', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'required' => [ 'provisionedModelArn', ], 'members' => [ 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], ], ], 'CustomModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12}', ], 'CustomModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([0-9a-zA-Z][_-]?)+', ], 'CustomModelSummary' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelName', 'creationTime', 'baseModelArn', 'baseModelName', ], 'members' => [ 'modelArn' => [ 'shape' => 'CustomModelArn', ], 'modelName' => [ 'shape' => 'CustomModelName', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'baseModelName' => [ 'shape' => 'ModelName', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], ], ], 'CustomModelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomModelSummary', ], ], 'CustomizationType' => [ 'type' => 'string', 'enum' => [ 'FINE_TUNING', 'CONTINUED_PRE_TRAINING', ], ], 'DeleteCustomModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'DeleteCustomModelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], ], ], 'DeleteProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'FineTuningJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'FoundationModelArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/[a-z0-9-]{1,63}[.]{1}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}', ], 'FoundationModelDetails' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelId', ], 'members' => [ 'modelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelId' => [ 'shape' => 'BedrockModelId', ], 'modelName' => [ 'shape' => 'BrandedName', ], 'providerName' => [ 'shape' => 'BrandedName', ], 'inputModalities' => [ 'shape' => 'ModelModalityList', ], 'outputModalities' => [ 'shape' => 'ModelModalityList', ], 'responseStreamingSupported' => [ 'shape' => 'Boolean', ], 'customizationsSupported' => [ 'shape' => 'ModelCustomizationList', ], 'inferenceTypesSupported' => [ 'shape' => 'InferenceTypeList', ], 'modelLifecycle' => [ 'shape' => 'FoundationModelLifecycle', ], ], ], 'FoundationModelLifecycle' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'FoundationModelLifecycleStatus', ], ], ], 'FoundationModelLifecycleStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'LEGACY', ], ], 'FoundationModelSummary' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelId', ], 'members' => [ 'modelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelId' => [ 'shape' => 'BedrockModelId', ], 'modelName' => [ 'shape' => 'BrandedName', ], 'providerName' => [ 'shape' => 'BrandedName', ], 'inputModalities' => [ 'shape' => 'ModelModalityList', ], 'outputModalities' => [ 'shape' => 'ModelModalityList', ], 'responseStreamingSupported' => [ 'shape' => 'Boolean', ], 'customizationsSupported' => [ 'shape' => 'ModelCustomizationList', ], 'inferenceTypesSupported' => [ 'shape' => 'InferenceTypeList', ], 'modelLifecycle' => [ 'shape' => 'FoundationModelLifecycle', ], ], ], 'FoundationModelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FoundationModelSummary', ], ], 'GetCustomModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'GetCustomModelResponse' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'modelName', 'jobArn', 'baseModelArn', 'trainingDataConfig', 'outputDataConfig', 'creationTime', ], 'members' => [ 'modelArn' => [ 'shape' => 'ModelArn', ], 'modelName' => [ 'shape' => 'CustomModelName', ], 'jobName' => [ 'shape' => 'JobName', ], 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'modelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'trainingMetrics' => [ 'shape' => 'TrainingMetrics', ], 'validationMetrics' => [ 'shape' => 'ValidationMetrics', ], 'creationTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetFoundationModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelIdentifier', ], 'members' => [ 'modelIdentifier' => [ 'shape' => 'ModelIdentifier', 'location' => 'uri', 'locationName' => 'modelIdentifier', ], ], ], 'GetFoundationModelResponse' => [ 'type' => 'structure', 'members' => [ 'modelDetails' => [ 'shape' => 'FoundationModelDetails', ], ], ], 'GetModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelCustomizationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'GetModelCustomizationJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'jobName', 'outputModelName', 'roleArn', 'creationTime', 'baseModelArn', 'hyperParameters', 'trainingDataConfig', 'validationDataConfig', 'outputDataConfig', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'jobName' => [ 'shape' => 'JobName', ], 'outputModelName' => [ 'shape' => 'CustomModelName', ], 'outputModelArn' => [ 'shape' => 'CustomModelArn', ], 'clientRequestToken' => [ 'shape' => 'IdempotencyToken', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'ModelCustomizationJobStatus', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'baseModelArn' => [ 'shape' => 'FoundationModelArn', ], 'hyperParameters' => [ 'shape' => 'ModelCustomizationHyperParameters', ], 'trainingDataConfig' => [ 'shape' => 'TrainingDataConfig', ], 'validationDataConfig' => [ 'shape' => 'ValidationDataConfig', ], 'outputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], 'outputModelKmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'trainingMetrics' => [ 'shape' => 'TrainingMetrics', ], 'validationMetrics' => [ 'shape' => 'ValidationMetrics', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'GetModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'GetProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], ], ], 'GetProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'required' => [ 'modelUnits', 'desiredModelUnits', 'provisionedModelName', 'provisionedModelArn', 'modelArn', 'desiredModelArn', 'foundationModelArn', 'status', 'creationTime', 'lastModifiedTime', ], 'members' => [ 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'desiredModelUnits' => [ 'shape' => 'PositiveInteger', ], 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], 'modelArn' => [ 'shape' => 'ModelArn', ], 'desiredModelArn' => [ 'shape' => 'ModelArn', ], 'foundationModelArn' => [ 'shape' => 'FoundationModelArn', ], 'status' => [ 'shape' => 'ProvisionedModelStatus', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'ErrorMessage', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'commitmentExpirationTime' => [ 'shape' => 'Timestamp', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'InferenceType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'PROVISIONED', ], ], 'InferenceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceType', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9\\+\\-\\.])*', ], 'KeyPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:kms:[a-zA-Z0-9-]*:[0-9]{12}:((key/[a-zA-Z0-9-]{36})|(alias/[a-zA-Z0-9-_/]+))', ], 'ListCustomModelsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'nameContains' => [ 'shape' => 'CustomModelName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'baseModelArnEquals' => [ 'shape' => 'ModelArn', 'location' => 'querystring', 'locationName' => 'baseModelArnEquals', ], 'foundationModelArnEquals' => [ 'shape' => 'FoundationModelArn', 'location' => 'querystring', 'locationName' => 'foundationModelArnEquals', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortModelsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListCustomModelsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'modelSummaries' => [ 'shape' => 'CustomModelSummaryList', ], ], ], 'ListFoundationModelsRequest' => [ 'type' => 'structure', 'members' => [ 'byProvider' => [ 'shape' => 'Provider', 'location' => 'querystring', 'locationName' => 'byProvider', ], 'byCustomizationType' => [ 'shape' => 'ModelCustomization', 'location' => 'querystring', 'locationName' => 'byCustomizationType', ], 'byOutputModality' => [ 'shape' => 'ModelModality', 'location' => 'querystring', 'locationName' => 'byOutputModality', ], 'byInferenceType' => [ 'shape' => 'InferenceType', 'location' => 'querystring', 'locationName' => 'byInferenceType', ], ], ], 'ListFoundationModelsResponse' => [ 'type' => 'structure', 'members' => [ 'modelSummaries' => [ 'shape' => 'FoundationModelSummaryList', ], ], ], 'ListModelCustomizationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'FineTuningJobStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'nameContains' => [ 'shape' => 'JobName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortJobsBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListModelCustomizationJobsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'modelCustomizationJobSummaries' => [ 'shape' => 'ModelCustomizationJobSummaries', ], ], ], 'ListProvisionedModelThroughputsRequest' => [ 'type' => 'structure', 'members' => [ 'creationTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeAfter', ], 'creationTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'creationTimeBefore', ], 'statusEquals' => [ 'shape' => 'ProvisionedModelStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'modelArnEquals' => [ 'shape' => 'ModelArn', 'location' => 'querystring', 'locationName' => 'modelArnEquals', ], 'nameContains' => [ 'shape' => 'ProvisionedModelName', 'location' => 'querystring', 'locationName' => 'nameContains', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortByProvisionedModels', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListProvisionedModelThroughputsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'provisionedModelSummaries' => [ 'shape' => 'ProvisionedModelSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LoggingConfig' => [ 'type' => 'structure', 'members' => [ 'cloudWatchConfig' => [ 'shape' => 'CloudWatchConfig', ], 's3Config' => [ 'shape' => 'S3Config', ], 'textDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], 'imageDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], 'embeddingDataDeliveryEnabled' => [ 'shape' => 'Boolean', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MetricFloat' => [ 'type' => 'float', 'box' => true, ], 'ModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}))', ], 'ModelCustomization' => [ 'type' => 'string', 'enum' => [ 'FINE_TUNING', 'CONTINUED_PRE_TRAINING', ], ], 'ModelCustomizationHyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ModelCustomizationJobArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-customization-job/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12}', ], 'ModelCustomizationJobIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 0, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:model-customization-job/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}/[a-z0-9]{12})|([a-zA-Z0-9](-*[a-zA-Z0-9\\+\\-\\.])*)', ], 'ModelCustomizationJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'ModelCustomizationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelCustomizationJobSummary', ], ], 'ModelCustomizationJobSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'baseModelArn', 'jobName', 'status', 'creationTime', ], 'members' => [ 'jobArn' => [ 'shape' => 'ModelCustomizationJobArn', ], 'baseModelArn' => [ 'shape' => 'ModelArn', ], 'jobName' => [ 'shape' => 'JobName', ], 'status' => [ 'shape' => 'ModelCustomizationJobStatus', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'customModelArn' => [ 'shape' => 'CustomModelArn', ], 'customModelName' => [ 'shape' => 'CustomModelName', ], 'customizationType' => [ 'shape' => 'CustomizationType', ], ], ], 'ModelCustomizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelCustomization', ], ], 'ModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+)', ], 'ModelModality' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'IMAGE', 'EMBEDDING', ], ], 'ModelModalityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelModality', ], ], 'ModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63})', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*', ], 'OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\S*', ], 'PositiveInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Provider' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9- ]{1,63}', ], 'ProvisionedModelArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}', ], 'ProvisionedModelId' => [ 'type' => 'string', 'pattern' => '((([0-9a-zA-Z][_-]?)+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}))', ], 'ProvisionedModelName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '([0-9a-zA-Z][_-]?)+', ], 'ProvisionedModelStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'InService', 'Updating', 'Failed', ], ], 'ProvisionedModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisionedModelSummary', ], ], 'ProvisionedModelSummary' => [ 'type' => 'structure', 'required' => [ 'provisionedModelName', 'provisionedModelArn', 'modelArn', 'desiredModelArn', 'foundationModelArn', 'modelUnits', 'desiredModelUnits', 'status', 'creationTime', 'lastModifiedTime', ], 'members' => [ 'provisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'provisionedModelArn' => [ 'shape' => 'ProvisionedModelArn', ], 'modelArn' => [ 'shape' => 'ModelArn', ], 'desiredModelArn' => [ 'shape' => 'ModelArn', ], 'foundationModelArn' => [ 'shape' => 'FoundationModelArn', ], 'modelUnits' => [ 'shape' => 'PositiveInteger', ], 'desiredModelUnits' => [ 'shape' => 'PositiveInteger', ], 'status' => [ 'shape' => 'ProvisionedModelStatus', ], 'commitmentDuration' => [ 'shape' => 'CommitmentDuration', ], 'commitmentExpirationTime' => [ 'shape' => 'Timestamp', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PutModelInvocationLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'loggingConfig', ], 'members' => [ 'loggingConfig' => [ 'shape' => 'LoggingConfig', ], ], ], 'PutModelInvocationLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+', ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'keyPrefix' => [ 'shape' => 'KeyPrefix', ], ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SortByProvisionedModels' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortJobsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortModelsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'StopModelCustomizationJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobIdentifier', ], 'members' => [ 'jobIdentifier' => [ 'shape' => 'ModelCustomizationJobIdentifier', 'location' => 'uri', 'locationName' => 'jobIdentifier', ], ], ], 'StopModelCustomizationJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-0-9a-zA-Z]+', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TaggableResourcesArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '.*(^[a-zA-Z0-9][a-zA-Z0-9\\-]*$)|(^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:([0-9]{12}|)((:(fine-tuning-job|model-customization-job|custom-model)/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}(/[a-z0-9]{12})$)|(:provisioned-model/[a-z0-9]{12}$))).*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'TaggableResourcesArn', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrainingDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'TrainingMetrics' => [ 'type' => 'structure', 'members' => [ 'trainingLoss' => [ 'shape' => 'MetricFloat', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourcesArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateProvisionedModelThroughputRequest' => [ 'type' => 'structure', 'required' => [ 'provisionedModelId', ], 'members' => [ 'provisionedModelId' => [ 'shape' => 'ProvisionedModelId', 'location' => 'uri', 'locationName' => 'provisionedModelId', ], 'desiredProvisionedModelName' => [ 'shape' => 'ProvisionedModelName', ], 'desiredModelId' => [ 'shape' => 'ModelIdentifier', ], ], ], 'UpdateProvisionedModelThroughputResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationDataConfig' => [ 'type' => 'structure', 'required' => [ 'validators', ], 'members' => [ 'validators' => [ 'shape' => 'Validators', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidatorMetric', ], ], 'Validator' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ValidatorMetric' => [ 'type' => 'structure', 'members' => [ 'validationLoss' => [ 'shape' => 'MetricFloat', ], ], ], 'Validators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Validator', ], 'max' => 10, 'min' => 0, ], 'VpcConfig' => [ 'type' => 'structure', 'required' => [ 'subnetIds', 'securityGroupIds', ], 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIds', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], ], ], ],];
