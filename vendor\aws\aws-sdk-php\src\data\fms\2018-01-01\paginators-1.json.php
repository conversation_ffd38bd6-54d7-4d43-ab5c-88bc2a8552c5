<?php
// This file was auto-generated from sdk-root/src/data/fms/2018-01-01/paginators-1.json
return [ 'pagination' => [ 'ListAdminAccountsForOrganization' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'AdminAccounts', ], 'ListAdminsManagingAccount' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'AdminAccounts', ], 'ListAppsLists' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'AppsLists', ], 'ListComplianceStatus' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'PolicyComplianceStatusList', ], 'ListMemberAccounts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'MemberAccounts', ], 'ListPolicies' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'PolicyList', ], 'ListProtocolsLists' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ProtocolsLists', ], 'ListThirdPartyFirewallFirewallPolicies' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ThirdPartyFirewallFirewallPolicies', ], ],];
