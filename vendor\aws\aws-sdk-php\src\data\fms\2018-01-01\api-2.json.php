<?php
// This file was auto-generated from sdk-root/src/data/fms/2018-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-01-01', 'endpointPrefix' => 'fms', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'FMS', 'serviceFullName' => 'Firewall Management Service', 'serviceId' => 'FMS', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSFMS_20180101', 'uid' => 'fms-2018-01-01', ], 'operations' => [ 'AssociateAdminAccount' => [ 'name' => 'AssociateAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateAdminAccountRequest', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'AssociateThirdPartyFirewall' => [ 'name' => 'AssociateThirdPartyFirewall', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateThirdPartyFirewallRequest', ], 'output' => [ 'shape' => 'AssociateThirdPartyFirewallResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'BatchAssociateResource' => [ 'name' => 'BatchAssociateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchAssociateResourceRequest', ], 'output' => [ 'shape' => 'BatchAssociateResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchDisassociateResource' => [ 'name' => 'BatchDisassociateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDisassociateResourceRequest', ], 'output' => [ 'shape' => 'BatchDisassociateResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteAppsList' => [ 'name' => 'DeleteAppsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppsListRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteNotificationChannel' => [ 'name' => 'DeleteNotificationChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNotificationChannelRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteProtocolsList' => [ 'name' => 'DeleteProtocolsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProtocolsListRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteResourceSet' => [ 'name' => 'DeleteResourceSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceSetRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DisassociateAdminAccount' => [ 'name' => 'DisassociateAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateAdminAccountRequest', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DisassociateThirdPartyFirewall' => [ 'name' => 'DisassociateThirdPartyFirewall', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateThirdPartyFirewallRequest', ], 'output' => [ 'shape' => 'DisassociateThirdPartyFirewallResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetAdminAccount' => [ 'name' => 'GetAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAdminAccountRequest', ], 'output' => [ 'shape' => 'GetAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetAdminScope' => [ 'name' => 'GetAdminScope', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAdminScopeRequest', ], 'output' => [ 'shape' => 'GetAdminScopeResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetAppsList' => [ 'name' => 'GetAppsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAppsListRequest', ], 'output' => [ 'shape' => 'GetAppsListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetComplianceDetail' => [ 'name' => 'GetComplianceDetail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComplianceDetailRequest', ], 'output' => [ 'shape' => 'GetComplianceDetailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'GetNotificationChannel' => [ 'name' => 'GetNotificationChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetNotificationChannelRequest', ], 'output' => [ 'shape' => 'GetNotificationChannelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPolicyRequest', ], 'output' => [ 'shape' => 'GetPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidTypeException', ], ], ], 'GetProtectionStatus' => [ 'name' => 'GetProtectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProtectionStatusRequest', ], 'output' => [ 'shape' => 'GetProtectionStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetProtocolsList' => [ 'name' => 'GetProtocolsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProtocolsListRequest', ], 'output' => [ 'shape' => 'GetProtocolsListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetResourceSet' => [ 'name' => 'GetResourceSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceSetRequest', ], 'output' => [ 'shape' => 'GetResourceSetResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetThirdPartyFirewallAssociationStatus' => [ 'name' => 'GetThirdPartyFirewallAssociationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetThirdPartyFirewallAssociationStatusRequest', ], 'output' => [ 'shape' => 'GetThirdPartyFirewallAssociationStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetViolationDetails' => [ 'name' => 'GetViolationDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetViolationDetailsRequest', ], 'output' => [ 'shape' => 'GetViolationDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListAdminAccountsForOrganization' => [ 'name' => 'ListAdminAccountsForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAdminAccountsForOrganizationRequest', ], 'output' => [ 'shape' => 'ListAdminAccountsForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListAdminsManagingAccount' => [ 'name' => 'ListAdminsManagingAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAdminsManagingAccountRequest', ], 'output' => [ 'shape' => 'ListAdminsManagingAccountResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListAppsLists' => [ 'name' => 'ListAppsLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAppsListsRequest', ], 'output' => [ 'shape' => 'ListAppsListsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListComplianceStatus' => [ 'name' => 'ListComplianceStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComplianceStatusRequest', ], 'output' => [ 'shape' => 'ListComplianceStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListDiscoveredResources' => [ 'name' => 'ListDiscoveredResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDiscoveredResourcesRequest', ], 'output' => [ 'shape' => 'ListDiscoveredResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListMemberAccounts' => [ 'name' => 'ListMemberAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMemberAccountsRequest', ], 'output' => [ 'shape' => 'ListMemberAccountsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPoliciesRequest', ], 'output' => [ 'shape' => 'ListPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListProtocolsLists' => [ 'name' => 'ListProtocolsLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProtocolsListsRequest', ], 'output' => [ 'shape' => 'ListProtocolsListsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListResourceSetResources' => [ 'name' => 'ListResourceSetResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceSetResourcesRequest', ], 'output' => [ 'shape' => 'ListResourceSetResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListResourceSets' => [ 'name' => 'ListResourceSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceSetsRequest', ], 'output' => [ 'shape' => 'ListResourceSetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListThirdPartyFirewallFirewallPolicies' => [ 'name' => 'ListThirdPartyFirewallFirewallPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListThirdPartyFirewallFirewallPoliciesRequest', ], 'output' => [ 'shape' => 'ListThirdPartyFirewallFirewallPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'PutAdminAccount' => [ 'name' => 'PutAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAdminAccountRequest', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'PutAppsList' => [ 'name' => 'PutAppsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAppsListRequest', ], 'output' => [ 'shape' => 'PutAppsListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'PutNotificationChannel' => [ 'name' => 'PutNotificationChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutNotificationChannelRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'PutPolicy' => [ 'name' => 'PutPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPolicyRequest', ], 'output' => [ 'shape' => 'PutPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidTypeException', ], ], ], 'PutProtocolsList' => [ 'name' => 'PutProtocolsList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutProtocolsListRequest', ], 'output' => [ 'shape' => 'PutProtocolsListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'PutResourceSet' => [ 'name' => 'PutResourceSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourceSetRequest', ], 'output' => [ 'shape' => 'PutResourceSetResponse', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidInputException', ], ], ], ], 'shapes' => [ 'AWSAccountId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'AWSAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccountId', ], ], 'AWSRegion' => [ 'type' => 'string', 'max' => 32, 'min' => 6, 'pattern' => '^(af|ap|ca|eu|il|me|mx|sa|us|cn|us-gov)-\\w+-\\d+$', ], 'AWSRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSRegion', ], 'max' => 64, 'min' => 0, ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccountId', ], ], 'AccountRoleStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'CREATING', 'PENDING_DELETION', 'DELETING', 'DELETED', ], ], 'AccountScope' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'AccountIdList', ], 'AllAccountsEnabled' => [ 'shape' => 'Boolean', ], 'ExcludeSpecifiedAccounts' => [ 'shape' => 'Boolean', ], ], ], 'ActionTarget' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Description' => [ 'shape' => 'LengthBoundedString', ], ], ], 'AdminAccountSummary' => [ 'type' => 'structure', 'members' => [ 'AdminAccount' => [ 'shape' => 'AWSAccountId', ], 'DefaultAdmin' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'OrganizationStatus', ], ], ], 'AdminAccountSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdminAccountSummary', ], ], 'AdminScope' => [ 'type' => 'structure', 'members' => [ 'AccountScope' => [ 'shape' => 'AccountScope', ], 'OrganizationalUnitScope' => [ 'shape' => 'OrganizationalUnitScope', ], 'RegionScope' => [ 'shape' => 'RegionScope', ], 'PolicyTypeScope' => [ 'shape' => 'PolicyTypeScope', ], ], ], 'App' => [ 'type' => 'structure', 'required' => [ 'AppName', 'Protocol', 'Port', ], 'members' => [ 'AppName' => [ 'shape' => 'ResourceName', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'Port' => [ 'shape' => 'IPPortNumber', ], ], ], 'AppsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'App', ], ], 'AppsListData' => [ 'type' => 'structure', 'required' => [ 'ListName', 'AppsList', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], 'ListName' => [ 'shape' => 'ResourceName', ], 'ListUpdateToken' => [ 'shape' => 'UpdateToken', ], 'CreateTime' => [ 'shape' => 'TimeStamp', ], 'LastUpdateTime' => [ 'shape' => 'TimeStamp', ], 'AppsList' => [ 'shape' => 'AppsList', ], 'PreviousAppsList' => [ 'shape' => 'PreviousAppsList', ], ], ], 'AppsListDataSummary' => [ 'type' => 'structure', 'members' => [ 'ListArn' => [ 'shape' => 'ResourceArn', ], 'ListId' => [ 'shape' => 'ListId', ], 'ListName' => [ 'shape' => 'ResourceName', ], 'AppsList' => [ 'shape' => 'AppsList', ], ], ], 'AppsListsData' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppsListDataSummary', ], ], 'AssociateAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccount', ], 'members' => [ 'AdminAccount' => [ 'shape' => 'AWSAccountId', ], ], ], 'AssociateThirdPartyFirewallRequest' => [ 'type' => 'structure', 'required' => [ 'ThirdPartyFirewall', ], 'members' => [ 'ThirdPartyFirewall' => [ 'shape' => 'ThirdPartyFirewall', ], ], ], 'AssociateThirdPartyFirewallResponse' => [ 'type' => 'structure', 'members' => [ 'ThirdPartyFirewallStatus' => [ 'shape' => 'ThirdPartyFirewallAssociationStatus', ], ], ], 'AwsEc2InstanceViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'AwsEc2NetworkInterfaceViolations' => [ 'shape' => 'AwsEc2NetworkInterfaceViolations', ], ], ], 'AwsEc2NetworkInterfaceViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ViolatingSecurityGroups' => [ 'shape' => 'ResourceIdList', ], ], ], 'AwsEc2NetworkInterfaceViolations' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfaceViolation', ], ], 'AwsVPCSecurityGroupViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ViolationTargetDescription' => [ 'shape' => 'LengthBoundedString', ], 'PartialMatches' => [ 'shape' => 'PartialMatches', ], 'PossibleSecurityGroupRemediationActions' => [ 'shape' => 'SecurityGroupRemediationActions', ], ], ], 'Base62Id' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => '^[a-z0-9A-Z]{22}$', ], 'BasicInteger' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => -2147483648, ], 'BatchAssociateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceSetIdentifier', 'Items', ], 'members' => [ 'ResourceSetIdentifier' => [ 'shape' => 'Identifier', ], 'Items' => [ 'shape' => 'IdentifierList', ], ], ], 'BatchAssociateResourceResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSetIdentifier', 'FailedItems', ], 'members' => [ 'ResourceSetIdentifier' => [ 'shape' => 'Identifier', ], 'FailedItems' => [ 'shape' => 'FailedItemList', ], ], ], 'BatchDisassociateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceSetIdentifier', 'Items', ], 'members' => [ 'ResourceSetIdentifier' => [ 'shape' => 'Identifier', ], 'Items' => [ 'shape' => 'IdentifierList', ], ], ], 'BatchDisassociateResourceResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSetIdentifier', 'FailedItems', ], 'members' => [ 'ResourceSetIdentifier' => [ 'shape' => 'Identifier', ], 'FailedItems' => [ 'shape' => 'FailedItemList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CIDR' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-f0-9:./]+', ], 'ComplianceViolator' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ViolationReason' => [ 'shape' => 'ViolationReason', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Metadata' => [ 'shape' => 'ComplianceViolatorMetadata', ], ], ], 'ComplianceViolatorMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'LengthBoundedString', ], 'value' => [ 'shape' => 'LengthBoundedString', ], ], 'ComplianceViolators' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceViolator', ], ], 'CustomerPolicyScopeId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'CustomerPolicyScopeIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerPolicyScopeId', ], ], 'CustomerPolicyScopeIdType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORG_UNIT', ], ], 'CustomerPolicyScopeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomerPolicyScopeIdType', ], 'value' => [ 'shape' => 'CustomerPolicyScopeIdList', ], ], 'CustomerPolicyStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'OUT_OF_ADMIN_SCOPE', ], ], 'DeleteAppsListRequest' => [ 'type' => 'structure', 'required' => [ 'ListId', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], ], ], 'DeleteNotificationChannelRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'DeleteAllPolicyResources' => [ 'shape' => 'Boolean', ], ], ], 'DeleteProtocolsListRequest' => [ 'type' => 'structure', 'required' => [ 'ListId', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], ], ], 'DeleteResourceSetRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Base62Id', ], ], ], 'DependentServiceName' => [ 'type' => 'string', 'enum' => [ 'AWSCONFIG', 'AWSWAF', 'AWSSHIELD_ADVANCED', 'AWSVPC', ], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'DestinationType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', 'PREFIX_LIST', ], ], 'DetailedInfo' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=,+\\-@]*)$', ], 'DisassociateAdminAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateThirdPartyFirewallRequest' => [ 'type' => 'structure', 'required' => [ 'ThirdPartyFirewall', ], 'members' => [ 'ThirdPartyFirewall' => [ 'shape' => 'ThirdPartyFirewall', ], ], ], 'DisassociateThirdPartyFirewallResponse' => [ 'type' => 'structure', 'members' => [ 'ThirdPartyFirewallStatus' => [ 'shape' => 'ThirdPartyFirewallAssociationStatus', ], ], ], 'DiscoveredResource' => [ 'type' => 'structure', 'members' => [ 'URI' => [ 'shape' => 'Identifier', ], 'AccountId' => [ 'shape' => 'AWSAccountId', ], 'Type' => [ 'shape' => 'ResourceType', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'DiscoveredResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiscoveredResource', ], ], 'DnsDuplicateRuleGroupViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ViolationTargetDescription' => [ 'shape' => 'LengthBoundedString', ], ], ], 'DnsRuleGroupLimitExceededViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ViolationTargetDescription' => [ 'shape' => 'LengthBoundedString', ], 'NumberOfRuleGroupsAlreadyAssociated' => [ 'shape' => 'BasicInteger', ], ], ], 'DnsRuleGroupPriorities' => [ 'type' => 'list', 'member' => [ 'shape' => 'DnsRuleGroupPriority', ], ], 'DnsRuleGroupPriority' => [ 'type' => 'integer', 'max' => 10000, 'min' => 0, ], 'DnsRuleGroupPriorityConflictViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ViolationTargetDescription' => [ 'shape' => 'LengthBoundedString', ], 'ConflictingPriority' => [ 'shape' => 'DnsRuleGroupPriority', ], 'ConflictingPolicyId' => [ 'shape' => 'PolicyId', ], 'UnavailablePriorities' => [ 'shape' => 'DnsRuleGroupPriorities', ], ], ], 'EC2AssociateRouteTableAction' => [ 'type' => 'structure', 'required' => [ 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], 'SubnetId' => [ 'shape' => 'ActionTarget', ], 'GatewayId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2CopyRouteTableAction' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'VpcId' => [ 'shape' => 'ActionTarget', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2CreateRouteAction' => [ 'type' => 'structure', 'required' => [ 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'DestinationCidrBlock' => [ 'shape' => 'CIDR', ], 'DestinationPrefixListId' => [ 'shape' => 'ResourceId', ], 'DestinationIpv6CidrBlock' => [ 'shape' => 'CIDR', ], 'VpcEndpointId' => [ 'shape' => 'ActionTarget', ], 'GatewayId' => [ 'shape' => 'ActionTarget', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2CreateRouteTableAction' => [ 'type' => 'structure', 'required' => [ 'VpcId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'VpcId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2DeleteRouteAction' => [ 'type' => 'structure', 'required' => [ 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'DestinationCidrBlock' => [ 'shape' => 'CIDR', ], 'DestinationPrefixListId' => [ 'shape' => 'ResourceId', ], 'DestinationIpv6CidrBlock' => [ 'shape' => 'CIDR', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2ReplaceRouteAction' => [ 'type' => 'structure', 'required' => [ 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'DestinationCidrBlock' => [ 'shape' => 'CIDR', ], 'DestinationPrefixListId' => [ 'shape' => 'ResourceId', ], 'DestinationIpv6CidrBlock' => [ 'shape' => 'CIDR', ], 'GatewayId' => [ 'shape' => 'ActionTarget', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], ], ], 'EC2ReplaceRouteTableAssociationAction' => [ 'type' => 'structure', 'required' => [ 'AssociationId', 'RouteTableId', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'AssociationId' => [ 'shape' => 'ActionTarget', ], 'RouteTableId' => [ 'shape' => 'ActionTarget', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EvaluationResult' => [ 'type' => 'structure', 'members' => [ 'ComplianceStatus' => [ 'shape' => 'PolicyComplianceStatusType', ], 'ViolatorCount' => [ 'shape' => 'ResourceCount', ], 'EvaluationLimitExceeded' => [ 'shape' => 'Boolean', ], ], ], 'EvaluationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationResult', ], ], 'ExpectedRoute' => [ 'type' => 'structure', 'members' => [ 'IpV4Cidr' => [ 'shape' => 'CIDR', ], 'PrefixListId' => [ 'shape' => 'CIDR', ], 'IpV6Cidr' => [ 'shape' => 'CIDR', ], 'ContributingSubnets' => [ 'shape' => 'ResourceIdList', ], 'AllowedTargets' => [ 'shape' => 'LengthBoundedStringList', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], ], ], 'ExpectedRoutes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExpectedRoute', ], ], 'FMSPolicyUpdateFirewallCreationConfigAction' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'FirewallCreationConfig' => [ 'shape' => 'ManagedServiceData', ], ], ], 'FailedItem' => [ 'type' => 'structure', 'members' => [ 'URI' => [ 'shape' => 'Identifier', ], 'Reason' => [ 'shape' => 'FailedItemReason', ], ], ], 'FailedItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedItem', ], ], 'FailedItemReason' => [ 'type' => 'string', 'enum' => [ 'NOT_VALID_ARN', 'NOT_VALID_PARTITION', 'NOT_VALID_REGION', 'NOT_VALID_SERVICE', 'NOT_VALID_RESOURCE_TYPE', 'NOT_VALID_ACCOUNT_ID', ], ], 'FirewallDeploymentModel' => [ 'type' => 'string', 'enum' => [ 'CENTRALIZED', 'DISTRIBUTED', ], ], 'FirewallPolicyId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'FirewallPolicyName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'FirewallSubnetIsOutOfScopeViolation' => [ 'type' => 'structure', 'members' => [ 'FirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'SubnetAvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'SubnetAvailabilityZoneId' => [ 'shape' => 'LengthBoundedString', ], 'VpcEndpointId' => [ 'shape' => 'ResourceId', ], ], ], 'FirewallSubnetMissingVPCEndpointViolation' => [ 'type' => 'structure', 'members' => [ 'FirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'SubnetAvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'SubnetAvailabilityZoneId' => [ 'shape' => 'LengthBoundedString', ], ], ], 'GetAdminAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccount' => [ 'shape' => 'AWSAccountId', ], 'RoleStatus' => [ 'shape' => 'AccountRoleStatus', ], ], ], 'GetAdminScopeRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccount', ], 'members' => [ 'AdminAccount' => [ 'shape' => 'AWSAccountId', ], ], ], 'GetAdminScopeResponse' => [ 'type' => 'structure', 'members' => [ 'AdminScope' => [ 'shape' => 'AdminScope', ], 'Status' => [ 'shape' => 'OrganizationStatus', ], ], ], 'GetAppsListRequest' => [ 'type' => 'structure', 'required' => [ 'ListId', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], 'DefaultList' => [ 'shape' => 'Boolean', ], ], ], 'GetAppsListResponse' => [ 'type' => 'structure', 'members' => [ 'AppsList' => [ 'shape' => 'AppsListData', ], 'AppsListArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetComplianceDetailRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', 'MemberAccount', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'MemberAccount' => [ 'shape' => 'AWSAccountId', ], ], ], 'GetComplianceDetailResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyComplianceDetail' => [ 'shape' => 'PolicyComplianceDetail', ], ], ], 'GetNotificationChannelRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetNotificationChannelResponse' => [ 'type' => 'structure', 'members' => [ 'SnsTopicArn' => [ 'shape' => 'ResourceArn', ], 'SnsRoleName' => [ 'shape' => 'ResourceArn', ], ], ], 'GetPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], ], ], 'GetPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], 'PolicyArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetProtectionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'MemberAccountId' => [ 'shape' => 'AWSAccountId', ], 'StartTime' => [ 'shape' => 'TimeStamp', ], 'EndTime' => [ 'shape' => 'TimeStamp', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'GetProtectionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccountId' => [ 'shape' => 'AWSAccountId', ], 'ServiceType' => [ 'shape' => 'SecurityServiceType', ], 'Data' => [ 'shape' => 'ProtectionData', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetProtocolsListRequest' => [ 'type' => 'structure', 'required' => [ 'ListId', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], 'DefaultList' => [ 'shape' => 'Boolean', ], ], ], 'GetProtocolsListResponse' => [ 'type' => 'structure', 'members' => [ 'ProtocolsList' => [ 'shape' => 'ProtocolsListData', ], 'ProtocolsListArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetResourceSetRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Base62Id', ], ], ], 'GetResourceSetResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSet', 'ResourceSetArn', ], 'members' => [ 'ResourceSet' => [ 'shape' => 'ResourceSet', ], 'ResourceSetArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetThirdPartyFirewallAssociationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'ThirdPartyFirewall', ], 'members' => [ 'ThirdPartyFirewall' => [ 'shape' => 'ThirdPartyFirewall', ], ], ], 'GetThirdPartyFirewallAssociationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ThirdPartyFirewallStatus' => [ 'shape' => 'ThirdPartyFirewallAssociationStatus', ], 'MarketplaceOnboardingStatus' => [ 'shape' => 'MarketplaceSubscriptionOnboardingStatus', ], ], ], 'GetViolationDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', 'MemberAccount', 'ResourceId', 'ResourceType', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'MemberAccount' => [ 'shape' => 'AWSAccountId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'GetViolationDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'ViolationDetail' => [ 'shape' => 'ViolationDetail', ], ], ], 'IPPortNumber' => [ 'type' => 'long', 'max' => 65535, 'min' => 0, ], 'Identifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'IdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'InternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidTypeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IssueInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DependentServiceName', ], 'value' => [ 'shape' => 'DetailedInfo', ], ], 'LengthBoundedString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'LengthBoundedStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LengthBoundedString', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListAdminAccountsForOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListAdminAccountsForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccounts' => [ 'shape' => 'AdminAccountSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAdminsManagingAccountRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListAdminsManagingAccountResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccounts' => [ 'shape' => 'AccountIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAppsListsRequest' => [ 'type' => 'structure', 'required' => [ 'MaxResults', ], 'members' => [ 'DefaultLists' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListAppsListsResponse' => [ 'type' => 'structure', 'members' => [ 'AppsLists' => [ 'shape' => 'AppsListsData', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListComplianceStatusRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyId', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListComplianceStatusResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyComplianceStatusList' => [ 'shape' => 'PolicyComplianceStatusList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDiscoveredResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'MemberAccountIds', 'ResourceType', ], 'members' => [ 'MemberAccountIds' => [ 'shape' => 'AWSAccountIdList', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDiscoveredResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'DiscoveredResourceList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-z0-9A-Z-]{36}$', ], 'ListMemberAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListMemberAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'MemberAccounts' => [ 'shape' => 'MemberAccounts', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyList' => [ 'shape' => 'PolicySummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListProtocolsListsRequest' => [ 'type' => 'structure', 'required' => [ 'MaxResults', ], 'members' => [ 'DefaultLists' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListProtocolsListsResponse' => [ 'type' => 'structure', 'members' => [ 'ProtocolsLists' => [ 'shape' => 'ProtocolsListsData', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListResourceSetResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ResourceId', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListResourceSetResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'Items', ], 'members' => [ 'Items' => [ 'shape' => 'ResourceList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListResourceSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListResourceSetsResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceSets' => [ 'shape' => 'ResourceSetSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'ListThirdPartyFirewallFirewallPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'ThirdPartyFirewall', 'MaxResults', ], 'members' => [ 'ThirdPartyFirewall' => [ 'shape' => 'ThirdPartyFirewall', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListThirdPartyFirewallFirewallPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'ThirdPartyFirewallFirewallPolicies' => [ 'shape' => 'ThirdPartyFirewallFirewallPolicies', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ManagedServiceData' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'pattern' => '^((?!\\\\[nr]).)+', ], 'MarketplaceSubscriptionOnboardingStatus' => [ 'type' => 'string', 'enum' => [ 'NO_SUBSCRIPTION', 'NOT_COMPLETE', 'COMPLETE', ], ], 'MemberAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccountId', ], ], 'Name' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'NetworkFirewallAction' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]+$', ], 'NetworkFirewallActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkFirewallAction', ], ], 'NetworkFirewallBlackHoleRouteDetectedViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'ViolatingRoutes' => [ 'shape' => 'Routes', ], ], ], 'NetworkFirewallInternetTrafficNotInspectedViolation' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'ResourceId', ], 'SubnetAvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'ViolatingRoutes' => [ 'shape' => 'Routes', ], 'IsRouteTableUsedInDifferentAZ' => [ 'shape' => 'Boolean', ], 'CurrentFirewallSubnetRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedFirewallEndpoint' => [ 'shape' => 'ResourceId', ], 'FirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'ExpectedFirewallSubnetRoutes' => [ 'shape' => 'ExpectedRoutes', ], 'ActualFirewallSubnetRoutes' => [ 'shape' => 'Routes', ], 'InternetGatewayId' => [ 'shape' => 'ResourceId', ], 'CurrentInternetGatewayRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedInternetGatewayRoutes' => [ 'shape' => 'ExpectedRoutes', ], 'ActualInternetGatewayRoutes' => [ 'shape' => 'Routes', ], 'VpcId' => [ 'shape' => 'ResourceId', ], ], ], 'NetworkFirewallInvalidRouteConfigurationViolation' => [ 'type' => 'structure', 'members' => [ 'AffectedSubnets' => [ 'shape' => 'ResourceIdList', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'IsRouteTableUsedInDifferentAZ' => [ 'shape' => 'Boolean', ], 'ViolatingRoute' => [ 'shape' => 'Route', ], 'CurrentFirewallSubnetRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedFirewallEndpoint' => [ 'shape' => 'ResourceId', ], 'ActualFirewallEndpoint' => [ 'shape' => 'ResourceId', ], 'ExpectedFirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'ActualFirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'ExpectedFirewallSubnetRoutes' => [ 'shape' => 'ExpectedRoutes', ], 'ActualFirewallSubnetRoutes' => [ 'shape' => 'Routes', ], 'InternetGatewayId' => [ 'shape' => 'ResourceId', ], 'CurrentInternetGatewayRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedInternetGatewayRoutes' => [ 'shape' => 'ExpectedRoutes', ], 'ActualInternetGatewayRoutes' => [ 'shape' => 'Routes', ], 'VpcId' => [ 'shape' => 'ResourceId', ], ], ], 'NetworkFirewallMissingExpectedRTViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'CurrentRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedRouteTable' => [ 'shape' => 'ResourceId', ], ], ], 'NetworkFirewallMissingExpectedRoutesViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'ExpectedRoutes' => [ 'shape' => 'ExpectedRoutes', ], 'VpcId' => [ 'shape' => 'ResourceId', ], ], ], 'NetworkFirewallMissingFirewallViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'TargetViolationReason' => [ 'shape' => 'TargetViolationReason', ], ], ], 'NetworkFirewallMissingSubnetViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'TargetViolationReason' => [ 'shape' => 'TargetViolationReason', ], ], ], 'NetworkFirewallOverrideAction' => [ 'type' => 'string', 'enum' => [ 'DROP_TO_ALERT', ], ], 'NetworkFirewallPolicy' => [ 'type' => 'structure', 'members' => [ 'FirewallDeploymentModel' => [ 'shape' => 'FirewallDeploymentModel', ], ], ], 'NetworkFirewallPolicyDescription' => [ 'type' => 'structure', 'members' => [ 'StatelessRuleGroups' => [ 'shape' => 'StatelessRuleGroupList', ], 'StatelessDefaultActions' => [ 'shape' => 'NetworkFirewallActionList', ], 'StatelessFragmentDefaultActions' => [ 'shape' => 'NetworkFirewallActionList', ], 'StatelessCustomActions' => [ 'shape' => 'NetworkFirewallActionList', ], 'StatefulRuleGroups' => [ 'shape' => 'StatefulRuleGroupList', ], 'StatefulDefaultActions' => [ 'shape' => 'NetworkFirewallActionList', ], 'StatefulEngineOptions' => [ 'shape' => 'StatefulEngineOptions', ], ], ], 'NetworkFirewallPolicyModifiedViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'CurrentPolicyDescription' => [ 'shape' => 'NetworkFirewallPolicyDescription', ], 'ExpectedPolicyDescription' => [ 'shape' => 'NetworkFirewallPolicyDescription', ], ], ], 'NetworkFirewallResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'NetworkFirewallStatefulRuleGroupOverride' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'NetworkFirewallOverrideAction', ], ], ], 'NetworkFirewallUnexpectedFirewallRoutesViolation' => [ 'type' => 'structure', 'members' => [ 'FirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'ViolatingRoutes' => [ 'shape' => 'Routes', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'FirewallEndpoint' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], ], ], 'NetworkFirewallUnexpectedGatewayRoutesViolation' => [ 'type' => 'structure', 'members' => [ 'GatewayId' => [ 'shape' => 'ResourceId', ], 'ViolatingRoutes' => [ 'shape' => 'Routes', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], ], ], 'OrderedRemediationActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationActionWithOrder', ], ], 'OrganizationStatus' => [ 'type' => 'string', 'enum' => [ 'ONBOARDING', 'ONBOARDING_COMPLETE', 'OFFBOARDING', 'OFFBOARDING_COMPLETE', ], ], 'OrganizationalUnitId' => [ 'type' => 'string', 'max' => 68, 'min' => 16, 'pattern' => '^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$', ], 'OrganizationalUnitIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitId', ], ], 'OrganizationalUnitScope' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnits' => [ 'shape' => 'OrganizationalUnitIdList', ], 'AllOrganizationalUnitsEnabled' => [ 'shape' => 'Boolean', ], 'ExcludeSpecifiedOrganizationalUnits' => [ 'shape' => 'Boolean', ], ], ], 'PaginationMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'PartialMatch' => [ 'type' => 'structure', 'members' => [ 'Reference' => [ 'shape' => 'ReferenceRule', ], 'TargetViolationReasons' => [ 'shape' => 'TargetViolationReasons', ], ], ], 'PartialMatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartialMatch', ], ], 'Policy' => [ 'type' => 'structure', 'required' => [ 'PolicyName', 'SecurityServicePolicyData', 'ResourceType', 'ExcludeResourceTags', 'RemediationEnabled', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyName' => [ 'shape' => 'ResourceName', ], 'PolicyUpdateToken' => [ 'shape' => 'PolicyUpdateToken', ], 'SecurityServicePolicyData' => [ 'shape' => 'SecurityServicePolicyData', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceTypeList' => [ 'shape' => 'ResourceTypeList', ], 'ResourceTags' => [ 'shape' => 'ResourceTags', ], 'ExcludeResourceTags' => [ 'shape' => 'Boolean', ], 'RemediationEnabled' => [ 'shape' => 'Boolean', ], 'DeleteUnusedFMManagedResources' => [ 'shape' => 'Boolean', ], 'IncludeMap' => [ 'shape' => 'CustomerPolicyScopeMap', ], 'ExcludeMap' => [ 'shape' => 'CustomerPolicyScopeMap', ], 'ResourceSetIds' => [ 'shape' => 'ResourceSetIds', ], 'PolicyDescription' => [ 'shape' => 'ResourceDescription', ], 'PolicyStatus' => [ 'shape' => 'CustomerPolicyStatus', ], ], ], 'PolicyComplianceDetail' => [ 'type' => 'structure', 'members' => [ 'PolicyOwner' => [ 'shape' => 'AWSAccountId', ], 'PolicyId' => [ 'shape' => 'PolicyId', ], 'MemberAccount' => [ 'shape' => 'AWSAccountId', ], 'Violators' => [ 'shape' => 'ComplianceViolators', ], 'EvaluationLimitExceeded' => [ 'shape' => 'Boolean', ], 'ExpiredAt' => [ 'shape' => 'TimeStamp', ], 'IssueInfoMap' => [ 'shape' => 'IssueInfoMap', ], ], ], 'PolicyComplianceStatus' => [ 'type' => 'structure', 'members' => [ 'PolicyOwner' => [ 'shape' => 'AWSAccountId', ], 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyName' => [ 'shape' => 'ResourceName', ], 'MemberAccount' => [ 'shape' => 'AWSAccountId', ], 'EvaluationResults' => [ 'shape' => 'EvaluationResults', ], 'LastUpdated' => [ 'shape' => 'TimeStamp', ], 'IssueInfoMap' => [ 'shape' => 'IssueInfoMap', ], ], ], 'PolicyComplianceStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyComplianceStatus', ], ], 'PolicyComplianceStatusType' => [ 'type' => 'string', 'enum' => [ 'COMPLIANT', 'NON_COMPLIANT', ], ], 'PolicyId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-z0-9A-Z-]{36}$', ], 'PolicyOption' => [ 'type' => 'structure', 'members' => [ 'NetworkFirewallPolicy' => [ 'shape' => 'NetworkFirewallPolicy', ], 'ThirdPartyFirewallPolicy' => [ 'shape' => 'ThirdPartyFirewallPolicy', ], ], ], 'PolicySummary' => [ 'type' => 'structure', 'members' => [ 'PolicyArn' => [ 'shape' => 'ResourceArn', ], 'PolicyId' => [ 'shape' => 'PolicyId', ], 'PolicyName' => [ 'shape' => 'ResourceName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'SecurityServiceType' => [ 'shape' => 'SecurityServiceType', ], 'RemediationEnabled' => [ 'shape' => 'Boolean', ], 'DeleteUnusedFMManagedResources' => [ 'shape' => 'Boolean', ], 'PolicyStatus' => [ 'shape' => 'CustomerPolicyStatus', ], ], ], 'PolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicySummary', ], ], 'PolicyTypeScope' => [ 'type' => 'structure', 'members' => [ 'PolicyTypes' => [ 'shape' => 'SecurityServiceTypeList', ], 'AllPolicyTypesEnabled' => [ 'shape' => 'Boolean', ], ], ], 'PolicyUpdateToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'PossibleRemediationAction' => [ 'type' => 'structure', 'required' => [ 'OrderedRemediationActions', ], 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'OrderedRemediationActions' => [ 'shape' => 'OrderedRemediationActions', ], 'IsDefaultAction' => [ 'shape' => 'Boolean', ], ], ], 'PossibleRemediationActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PossibleRemediationAction', ], ], 'PossibleRemediationActions' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'Actions' => [ 'shape' => 'PossibleRemediationActionList', ], ], ], 'PreviousAppsList' => [ 'type' => 'map', 'key' => [ 'shape' => 'PreviousListVersion', ], 'value' => [ 'shape' => 'AppsList', ], ], 'PreviousListVersion' => [ 'type' => 'string', 'max' => 2, 'min' => 1, 'pattern' => '^\\d{1,2}$', ], 'PreviousProtocolsList' => [ 'type' => 'map', 'key' => [ 'shape' => 'PreviousListVersion', ], 'value' => [ 'shape' => 'ProtocolsList', ], ], 'PriorityNumber' => [ 'type' => 'integer', ], 'ProtectionData' => [ 'type' => 'string', ], 'Protocol' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ProtocolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Protocol', ], ], 'ProtocolsListData' => [ 'type' => 'structure', 'required' => [ 'ListName', 'ProtocolsList', ], 'members' => [ 'ListId' => [ 'shape' => 'ListId', ], 'ListName' => [ 'shape' => 'ResourceName', ], 'ListUpdateToken' => [ 'shape' => 'UpdateToken', ], 'CreateTime' => [ 'shape' => 'TimeStamp', ], 'LastUpdateTime' => [ 'shape' => 'TimeStamp', ], 'ProtocolsList' => [ 'shape' => 'ProtocolsList', ], 'PreviousProtocolsList' => [ 'shape' => 'PreviousProtocolsList', ], ], ], 'ProtocolsListDataSummary' => [ 'type' => 'structure', 'members' => [ 'ListArn' => [ 'shape' => 'ResourceArn', ], 'ListId' => [ 'shape' => 'ListId', ], 'ListName' => [ 'shape' => 'ResourceName', ], 'ProtocolsList' => [ 'shape' => 'ProtocolsList', ], ], ], 'ProtocolsListsData' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtocolsListDataSummary', ], ], 'PutAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccount', ], 'members' => [ 'AdminAccount' => [ 'shape' => 'AWSAccountId', ], 'AdminScope' => [ 'shape' => 'AdminScope', ], ], ], 'PutAppsListRequest' => [ 'type' => 'structure', 'required' => [ 'AppsList', ], 'members' => [ 'AppsList' => [ 'shape' => 'AppsListData', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'PutAppsListResponse' => [ 'type' => 'structure', 'members' => [ 'AppsList' => [ 'shape' => 'AppsListData', ], 'AppsListArn' => [ 'shape' => 'ResourceArn', ], ], ], 'PutNotificationChannelRequest' => [ 'type' => 'structure', 'required' => [ 'SnsTopicArn', 'SnsRoleName', ], 'members' => [ 'SnsTopicArn' => [ 'shape' => 'ResourceArn', ], 'SnsRoleName' => [ 'shape' => 'ResourceArn', ], ], ], 'PutPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Policy', ], 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'PutPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], 'PolicyArn' => [ 'shape' => 'ResourceArn', ], ], ], 'PutProtocolsListRequest' => [ 'type' => 'structure', 'required' => [ 'ProtocolsList', ], 'members' => [ 'ProtocolsList' => [ 'shape' => 'ProtocolsListData', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'PutProtocolsListResponse' => [ 'type' => 'structure', 'members' => [ 'ProtocolsList' => [ 'shape' => 'ProtocolsListData', ], 'ProtocolsListArn' => [ 'shape' => 'ResourceArn', ], ], ], 'PutResourceSetRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceSet', ], 'members' => [ 'ResourceSet' => [ 'shape' => 'ResourceSet', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'PutResourceSetResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSet', 'ResourceSetArn', ], 'members' => [ 'ResourceSet' => [ 'shape' => 'ResourceSet', ], 'ResourceSetArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ReferenceRule' => [ 'type' => 'string', ], 'RegionScope' => [ 'type' => 'structure', 'members' => [ 'Regions' => [ 'shape' => 'AWSRegionList', ], 'AllRegionsEnabled' => [ 'shape' => 'Boolean', ], ], ], 'RemediationAction' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'LengthBoundedString', ], 'EC2CreateRouteAction' => [ 'shape' => 'EC2CreateRouteAction', ], 'EC2ReplaceRouteAction' => [ 'shape' => 'EC2ReplaceRouteAction', ], 'EC2DeleteRouteAction' => [ 'shape' => 'EC2DeleteRouteAction', ], 'EC2CopyRouteTableAction' => [ 'shape' => 'EC2CopyRouteTableAction', ], 'EC2ReplaceRouteTableAssociationAction' => [ 'shape' => 'EC2ReplaceRouteTableAssociationAction', ], 'EC2AssociateRouteTableAction' => [ 'shape' => 'EC2AssociateRouteTableAction', ], 'EC2CreateRouteTableAction' => [ 'shape' => 'EC2CreateRouteTableAction', ], 'FMSPolicyUpdateFirewallCreationConfigAction' => [ 'shape' => 'FMSPolicyUpdateFirewallCreationConfigAction', ], ], ], 'RemediationActionDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'RemediationActionType' => [ 'type' => 'string', 'enum' => [ 'REMOVE', 'MODIFY', ], ], 'RemediationActionWithOrder' => [ 'type' => 'structure', 'members' => [ 'RemediationAction' => [ 'shape' => 'RemediationAction', ], 'Order' => [ 'shape' => 'BasicInteger', ], ], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'URI', ], 'members' => [ 'URI' => [ 'shape' => 'Identifier', ], 'AccountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceCount' => [ 'type' => 'long', 'min' => 0, ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceSet' => [ 'type' => 'structure', 'required' => [ 'Name', 'ResourceTypeList', ], 'members' => [ 'Id' => [ 'shape' => 'Base62Id', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'ResourceTypeList' => [ 'shape' => 'ResourceTypeList', ], 'LastUpdateTime' => [ 'shape' => 'TimeStamp', ], 'ResourceSetStatus' => [ 'shape' => 'ResourceSetStatus', ], ], ], 'ResourceSetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'Base62Id', ], ], 'ResourceSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'OUT_OF_ADMIN_SCOPE', ], ], 'ResourceSetSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'Base62Id', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'LastUpdateTime' => [ 'shape' => 'TimeStamp', ], 'ResourceSetStatus' => [ 'shape' => 'ResourceSetStatus', ], ], ], 'ResourceSetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceSetSummary', ], ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'ResourceTagKey', ], 'Value' => [ 'shape' => 'ResourceTagValue', ], ], ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], 'max' => 8, 'min' => 0, ], 'ResourceType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'ResourceViolation' => [ 'type' => 'structure', 'members' => [ 'AwsVPCSecurityGroupViolation' => [ 'shape' => 'AwsVPCSecurityGroupViolation', ], 'AwsEc2NetworkInterfaceViolation' => [ 'shape' => 'AwsEc2NetworkInterfaceViolation', ], 'AwsEc2InstanceViolation' => [ 'shape' => 'AwsEc2InstanceViolation', ], 'NetworkFirewallMissingFirewallViolation' => [ 'shape' => 'NetworkFirewallMissingFirewallViolation', ], 'NetworkFirewallMissingSubnetViolation' => [ 'shape' => 'NetworkFirewallMissingSubnetViolation', ], 'NetworkFirewallMissingExpectedRTViolation' => [ 'shape' => 'NetworkFirewallMissingExpectedRTViolation', ], 'NetworkFirewallPolicyModifiedViolation' => [ 'shape' => 'NetworkFirewallPolicyModifiedViolation', ], 'NetworkFirewallInternetTrafficNotInspectedViolation' => [ 'shape' => 'NetworkFirewallInternetTrafficNotInspectedViolation', ], 'NetworkFirewallInvalidRouteConfigurationViolation' => [ 'shape' => 'NetworkFirewallInvalidRouteConfigurationViolation', ], 'NetworkFirewallBlackHoleRouteDetectedViolation' => [ 'shape' => 'NetworkFirewallBlackHoleRouteDetectedViolation', ], 'NetworkFirewallUnexpectedFirewallRoutesViolation' => [ 'shape' => 'NetworkFirewallUnexpectedFirewallRoutesViolation', ], 'NetworkFirewallUnexpectedGatewayRoutesViolation' => [ 'shape' => 'NetworkFirewallUnexpectedGatewayRoutesViolation', ], 'NetworkFirewallMissingExpectedRoutesViolation' => [ 'shape' => 'NetworkFirewallMissingExpectedRoutesViolation', ], 'DnsRuleGroupPriorityConflictViolation' => [ 'shape' => 'DnsRuleGroupPriorityConflictViolation', ], 'DnsDuplicateRuleGroupViolation' => [ 'shape' => 'DnsDuplicateRuleGroupViolation', ], 'DnsRuleGroupLimitExceededViolation' => [ 'shape' => 'DnsRuleGroupLimitExceededViolation', ], 'PossibleRemediationActions' => [ 'shape' => 'PossibleRemediationActions', ], 'FirewallSubnetIsOutOfScopeViolation' => [ 'shape' => 'FirewallSubnetIsOutOfScopeViolation', ], 'RouteHasOutOfScopeEndpointViolation' => [ 'shape' => 'RouteHasOutOfScopeEndpointViolation', ], 'ThirdPartyFirewallMissingFirewallViolation' => [ 'shape' => 'ThirdPartyFirewallMissingFirewallViolation', ], 'ThirdPartyFirewallMissingSubnetViolation' => [ 'shape' => 'ThirdPartyFirewallMissingSubnetViolation', ], 'ThirdPartyFirewallMissingExpectedRouteTableViolation' => [ 'shape' => 'ThirdPartyFirewallMissingExpectedRouteTableViolation', ], 'FirewallSubnetMissingVPCEndpointViolation' => [ 'shape' => 'FirewallSubnetMissingVPCEndpointViolation', ], ], ], 'ResourceViolations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceViolation', ], ], 'Route' => [ 'type' => 'structure', 'members' => [ 'DestinationType' => [ 'shape' => 'DestinationType', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'Destination' => [ 'shape' => 'LengthBoundedString', ], 'Target' => [ 'shape' => 'LengthBoundedString', ], ], ], 'RouteHasOutOfScopeEndpointViolation' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'RouteTableId' => [ 'shape' => 'ResourceId', ], 'ViolatingRoutes' => [ 'shape' => 'Routes', ], 'SubnetAvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'SubnetAvailabilityZoneId' => [ 'shape' => 'LengthBoundedString', ], 'CurrentFirewallSubnetRouteTable' => [ 'shape' => 'ResourceId', ], 'FirewallSubnetId' => [ 'shape' => 'ResourceId', ], 'FirewallSubnetRoutes' => [ 'shape' => 'Routes', ], 'InternetGatewayId' => [ 'shape' => 'ResourceId', ], 'CurrentInternetGatewayRouteTable' => [ 'shape' => 'ResourceId', ], 'InternetGatewayRoutes' => [ 'shape' => 'Routes', ], ], ], 'Routes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Route', ], ], 'RuleOrder' => [ 'type' => 'string', 'enum' => [ 'STRICT_ORDER', 'DEFAULT_ACTION_ORDER', ], ], 'SecurityGroupRemediationAction' => [ 'type' => 'structure', 'members' => [ 'RemediationActionType' => [ 'shape' => 'RemediationActionType', ], 'Description' => [ 'shape' => 'RemediationActionDescription', ], 'RemediationResult' => [ 'shape' => 'SecurityGroupRuleDescription', ], 'IsDefaultAction' => [ 'shape' => 'Boolean', ], ], ], 'SecurityGroupRemediationActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupRemediationAction', ], ], 'SecurityGroupRuleDescription' => [ 'type' => 'structure', 'members' => [ 'IPV4Range' => [ 'shape' => 'CIDR', ], 'IPV6Range' => [ 'shape' => 'CIDR', ], 'PrefixListId' => [ 'shape' => 'ResourceId', ], 'Protocol' => [ 'shape' => 'LengthBoundedString', ], 'FromPort' => [ 'shape' => 'IPPortNumber', ], 'ToPort' => [ 'shape' => 'IPPortNumber', ], ], ], 'SecurityServicePolicyData' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'SecurityServiceType', ], 'ManagedServiceData' => [ 'shape' => 'ManagedServiceData', ], 'PolicyOption' => [ 'shape' => 'PolicyOption', ], ], ], 'SecurityServiceType' => [ 'type' => 'string', 'enum' => [ 'WAF', 'WAFV2', 'SHIELD_ADVANCED', 'SECURITY_GROUPS_COMMON', 'SECURITY_GROUPS_CONTENT_AUDIT', 'SECURITY_GROUPS_USAGE_AUDIT', 'NETWORK_FIREWALL', 'DNS_FIREWALL', 'THIRD_PARTY_FIREWALL', 'IMPORT_NETWORK_FIREWALL', ], ], 'SecurityServiceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityServiceType', ], 'max' => 32, 'min' => 0, ], 'StatefulEngineOptions' => [ 'type' => 'structure', 'members' => [ 'RuleOrder' => [ 'shape' => 'RuleOrder', ], ], ], 'StatefulRuleGroup' => [ 'type' => 'structure', 'members' => [ 'RuleGroupName' => [ 'shape' => 'NetworkFirewallResourceName', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'PriorityNumber', ], 'Override' => [ 'shape' => 'NetworkFirewallStatefulRuleGroupOverride', ], ], ], 'StatefulRuleGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatefulRuleGroup', ], ], 'StatelessRuleGroup' => [ 'type' => 'structure', 'members' => [ 'RuleGroupName' => [ 'shape' => 'NetworkFirewallResourceName', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'StatelessRuleGroupPriority', ], ], ], 'StatelessRuleGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatelessRuleGroup', ], ], 'StatelessRuleGroupPriority' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagList', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'GATEWAY', 'CARRIER_GATEWAY', 'INSTANCE', 'LOCAL_GATEWAY', 'NAT_GATEWAY', 'NETWORK_INTERFACE', 'VPC_ENDPOINT', 'VPC_PEERING_CONNECTION', 'EGRESS_ONLY_INTERNET_GATEWAY', 'TRANSIT_GATEWAY', ], ], 'TargetViolationReason' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '\\w+', ], 'TargetViolationReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetViolationReason', ], ], 'ThirdPartyFirewall' => [ 'type' => 'string', 'enum' => [ 'PALO_ALTO_NETWORKS_CLOUD_NGFW', 'FORTIGATE_CLOUD_NATIVE_FIREWALL', ], ], 'ThirdPartyFirewallAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ONBOARDING', 'ONBOARD_COMPLETE', 'OFFBOARDING', 'OFFBOARD_COMPLETE', 'NOT_EXIST', ], ], 'ThirdPartyFirewallFirewallPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThirdPartyFirewallFirewallPolicy', ], ], 'ThirdPartyFirewallFirewallPolicy' => [ 'type' => 'structure', 'members' => [ 'FirewallPolicyId' => [ 'shape' => 'FirewallPolicyId', ], 'FirewallPolicyName' => [ 'shape' => 'FirewallPolicyName', ], ], ], 'ThirdPartyFirewallMissingExpectedRouteTableViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'CurrentRouteTable' => [ 'shape' => 'ResourceId', ], 'ExpectedRouteTable' => [ 'shape' => 'ResourceId', ], ], ], 'ThirdPartyFirewallMissingFirewallViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'TargetViolationReason' => [ 'shape' => 'TargetViolationReason', ], ], ], 'ThirdPartyFirewallMissingSubnetViolation' => [ 'type' => 'structure', 'members' => [ 'ViolationTarget' => [ 'shape' => 'ViolationTarget', ], 'VPC' => [ 'shape' => 'ResourceId', ], 'AvailabilityZone' => [ 'shape' => 'LengthBoundedString', ], 'TargetViolationReason' => [ 'shape' => 'TargetViolationReason', ], ], ], 'ThirdPartyFirewallPolicy' => [ 'type' => 'structure', 'members' => [ 'FirewallDeploymentModel' => [ 'shape' => 'FirewallDeploymentModel', ], ], ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ViolationDetail' => [ 'type' => 'structure', 'required' => [ 'PolicyId', 'MemberAccount', 'ResourceId', 'ResourceType', 'ResourceViolations', ], 'members' => [ 'PolicyId' => [ 'shape' => 'PolicyId', ], 'MemberAccount' => [ 'shape' => 'AWSAccountId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceViolations' => [ 'shape' => 'ResourceViolations', ], 'ResourceTags' => [ 'shape' => 'TagList', ], 'ResourceDescription' => [ 'shape' => 'LengthBoundedString', ], ], ], 'ViolationReason' => [ 'type' => 'string', 'enum' => [ 'WEB_ACL_MISSING_RULE_GROUP', 'RESOURCE_MISSING_WEB_ACL', 'RESOURCE_INCORRECT_WEB_ACL', 'RESOURCE_MISSING_SHIELD_PROTECTION', 'RESOURCE_MISSING_WEB_ACL_OR_SHIELD_PROTECTION', 'RESOURCE_MISSING_SECURITY_GROUP', 'RESOURCE_VIOLATES_AUDIT_SECURITY_GROUP', 'SECURITY_GROUP_UNUSED', 'SECURITY_GROUP_REDUNDANT', 'FMS_CREATED_SECURITY_GROUP_EDITED', 'MISSING_FIREWALL', 'MISSING_FIREWALL_SUBNET_IN_AZ', 'MISSING_EXPECTED_ROUTE_TABLE', 'NETWORK_FIREWALL_POLICY_MODIFIED', 'FIREWALL_SUBNET_IS_OUT_OF_SCOPE', 'INTERNET_GATEWAY_MISSING_EXPECTED_ROUTE', 'FIREWALL_SUBNET_MISSING_EXPECTED_ROUTE', 'UNEXPECTED_FIREWALL_ROUTES', 'UNEXPECTED_TARGET_GATEWAY_ROUTES', 'TRAFFIC_INSPECTION_CROSSES_AZ_BOUNDARY', 'INVALID_ROUTE_CONFIGURATION', 'MISSING_TARGET_GATEWAY', 'INTERNET_TRAFFIC_NOT_INSPECTED', 'BLACK_HOLE_ROUTE_DETECTED', 'BLACK_HOLE_ROUTE_DETECTED_IN_FIREWALL_SUBNET', 'RESOURCE_MISSING_DNS_FIREWALL', 'ROUTE_HAS_OUT_OF_SCOPE_ENDPOINT', 'FIREWALL_SUBNET_MISSING_VPCE_ENDPOINT', ], ], 'ViolationTarget' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], ],];
