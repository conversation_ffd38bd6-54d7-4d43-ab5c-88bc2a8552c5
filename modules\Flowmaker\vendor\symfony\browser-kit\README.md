BrowserKit Component
====================

The BrowserKit component simulates the behavior of a web browser, allowing you
to make requests, click on links and submit forms programmatically.

The component comes with a concrete implementation that uses the HttpClient
component to make real HTTP requests.

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/browser_kit/introduction.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)
