{"name": "mobidonia", "version": "4.1.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "noregister": "node ./.scripts/make_non_register.js", "makeclient": "cp navigation/Screensclient.js navigation/Screens.js", "makedriver": "cp navigation/Screensdriver.js navigation/Screens.js", "maketaxidriver": "cp navigation/Screensdriver.js navigation/Screens.js && npm run noregister", "makechat": "cp navigation/Screenschat.js navigation/Screens.js && npm run noregister", "makevendor": "cp navigation/Screensvendor.js navigation/Screens.js", "prepareforrelase": "cp config_release.js config.js", "dev": "cp config_dev.js config.js && cp navigation/Screensclient.js navigation/Screens.js", "zipclient": "npm run prepareforrelase && npm run makeclient && zip -r /Users/<USER>/OneDrive/Plugins/MobileApps/FoodTigerClientApp/client_app.zip ./ -x \"node_modules/*\"  -x \".scripts/*\"  -x \"*.git*\" -x \"*.DS_Store\"  -x \"ios/Pods/*\" -x \"android/app/build/*\"   -x \"navigation/DriverStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\"  -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\" -x \"navigation/Screenschat.js\" -x \"config_dev.js\" -x \"config_release.js\" -x \"1_docs/update_code_driver.txt\" -x \"1_docs/update_code_vendor.txt\"  -x \"1_docs/update_code_taxidriver.txt\" && npm run dev", "zipdriver": "npm run prepareforrelase && npm run makedriver && zip -r /Users/<USER>/OneDrive/Plugins/MobileApps/FoodTigerDriverApp/driver_app.zip ./ -x \"node_modules/*\"  -x \".scripts/*\"  -x \"*.git*\" -x \"*.DS_Store\"  -x \"ios/Pods/*\" -x \"android/app/build/*\"   -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\"  -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\" -x \"navigation/Screenschat.js\" -x \"config_dev.js\" -x \"config_release.js\"  -x \"1_docs/update_code_client.txt\" -x \"1_docs/update_code_vendor.txt\" -x \"1_docs/update_code_taxidriver.txt\"  && npm run dev", "ziptaxidriver": "npm run prepareforrelase && npm run maketaxidriver && zip -r /Users/<USER>/OneDrive/Plugins/MobileApps/TaxiDriver/taxi_driver_app.zip ./ -x \"node_modules/*\"  -x \".scripts/*\"  -x \"*.git*\" -x \"*.DS_Store\"  -x \"ios/Pods/*\" -x \"android/app/build/*\"   -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\"  -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\" -x \"navigation/Screenschat.js\" -x \"config_dev.js\" -x \"config_release.js\"  -x \"1_docs/update_code_driver.txt\" -x \"1_docs/update_code_client.txt\"  -x \"1_docs/update_code_vendor.txt\"  && npm run dev", "zipvendor": "npm run prepareforrelase && npm run makevendor && zip -r /Users/<USER>/OneDrive/Plugins/MobileApps/VendorApp/vendor_app.zip ./ -x \"node_modules/*\"  -x \".scripts/*\"  -x \"*.git*\" -x \"*.DS_Store\"  -x \"ios/Pods/*\" -x \"android/app/build/*\"   -x \"navigation/ClientStacks/*\"  -x \"navigation/DriverStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\"  -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\" -x \"navigation/Screenschat.js\" -x \"config_dev.js\" -x \"config_release.js\"  -x \"1_docs/update_code_driver.txt\" -x \"1_docs/update_code_client.txt\" -x \"1_docs/update_code_taxidriver.txt\" && npm run dev", "zipchat": "npm run prepareforrelase && npm run makechat && zip -r /Users/<USER>/OneDrive/Plugins/MobileApps/ChatApp/chat_app.zip ./ -x \"node_modules/*\"  -x \".scripts/*\"  -x \"*.git*\"  -x \"*.DS_Store\"  -x \"ios/Pods/*\" -x \"android/app/build/*\"   -x \"navigation/ClientStacks/*\"  -x \"navigation/DriverStacks/*\" -x \"navigation/VendorStacks/*\" -x \"navigation/Screenschat.js\"  -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\"  -x \"navigation/ClientStacks/*\"  -x \"navigation/VendorStacks/*\"   -x \"navigation/Screensclient.js\"  -x \"navigation/Screensdriver.js\" -x \"navigation/Screensvendor.js\" -x \"navigation/Screenschat.js\" -x \"config_dev.js\" -x \"config_release.js\"  -x \"1_docs/update_code_driver.txt\" -x \"1_docs/update_code_client.txt\" -x \"1_docs/update_code_taxidriver.txt\" -x \"1_docs/update_code_vendor.txt\" && npm run dev", "zipall": "npm run zipclient && npm run zipdriver && npm run zipvendor && npm run ziptaxidriver"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "6.0.9", "@react-navigation/drawer": "^6.0.0", "@react-navigation/native": "^6.0.0", "@react-navigation/stack": "6.0.0", "cjs": "^0.0.11", "deprecated-react-native-prop-types": "^2.3.0", "expo": "^52.0.28", "expo-asset": "~11.0.2", "expo-constants": "~17.0.5", "expo-device": "~7.0.2", "expo-font": "~13.0.3", "expo-image-picker": "~16.0.4", "expo-location": "~18.0.5", "expo-notifications": "~0.29.13", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-task-manager": "~12.0.5", "expo-updates": "~0.26.13", "galio-framework": "0.8.0", "md5": "^2.3.0", "moment": "^2.29.1", "node-fetch": "^3.2.4", "prop-types": "^15.7.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.6", "react-native-expo-fancy-alerts": "2.1.1", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-modal-dropdown": "1.0.2", "react-native-modals": "^0.19.9", "react-native-places-input": "^1.1.7", "react-native-popup-dialog": "^0.18.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-toast-message": "^2.1.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}