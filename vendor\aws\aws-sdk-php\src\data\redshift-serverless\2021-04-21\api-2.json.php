<?php
// This file was auto-generated from sdk-root/src/data/redshift-serverless/2021-04-21/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-04-21', 'endpointPrefix' => 'redshift-serverless', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Redshift Serverless', 'serviceId' => 'Redshift Serverless', 'signatureVersion' => 'v4', 'signingName' => 'redshift-serverless', 'targetPrefix' => 'RedshiftServerless', 'uid' => 'redshift-serverless-2021-04-21', ], 'operations' => [ 'ConvertRecoveryPointToSnapshot' => [ 'name' => 'ConvertRecoveryPointToSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConvertRecoveryPointToSnapshotRequest', ], 'output' => [ 'shape' => 'ConvertRecoveryPointToSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateCustomDomainAssociation' => [ 'name' => 'CreateCustomDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomDomainAssociationRequest', ], 'output' => [ 'shape' => 'CreateCustomDomainAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateEndpointAccess' => [ 'name' => 'CreateEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointAccessRequest', ], 'output' => [ 'shape' => 'CreateEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateNamespace' => [ 'name' => 'CreateNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNamespaceRequest', ], 'output' => [ 'shape' => 'CreateNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'CreateScheduledAction' => [ 'name' => 'CreateScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateScheduledActionRequest', ], 'output' => [ 'shape' => 'CreateScheduledActionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateSnapshotCopyConfiguration' => [ 'name' => 'CreateSnapshotCopyConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotCopyConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSnapshotCopyConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateUsageLimit' => [ 'name' => 'CreateUsageLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUsageLimitRequest', ], 'output' => [ 'shape' => 'CreateUsageLimitResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateWorkgroup' => [ 'name' => 'CreateWorkgroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkgroupRequest', ], 'output' => [ 'shape' => 'CreateWorkgroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InsufficientCapacityException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], ], 'idempotent' => true, ], 'DeleteCustomDomainAssociation' => [ 'name' => 'DeleteCustomDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomDomainAssociationRequest', ], 'output' => [ 'shape' => 'DeleteCustomDomainAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteEndpointAccess' => [ 'name' => 'DeleteEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointAccessRequest', ], 'output' => [ 'shape' => 'DeleteEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteNamespace' => [ 'name' => 'DeleteNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNamespaceRequest', ], 'output' => [ 'shape' => 'DeleteNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteScheduledAction' => [ 'name' => 'DeleteScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteScheduledActionRequest', ], 'output' => [ 'shape' => 'DeleteScheduledActionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteSnapshotCopyConfiguration' => [ 'name' => 'DeleteSnapshotCopyConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotCopyConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotCopyConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteUsageLimit' => [ 'name' => 'DeleteUsageLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUsageLimitRequest', ], 'output' => [ 'shape' => 'DeleteUsageLimitResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteWorkgroup' => [ 'name' => 'DeleteWorkgroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkgroupRequest', ], 'output' => [ 'shape' => 'DeleteWorkgroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetCredentials' => [ 'name' => 'GetCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCredentialsRequest', ], 'output' => [ 'shape' => 'GetCredentialsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetCustomDomainAssociation' => [ 'name' => 'GetCustomDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCustomDomainAssociationRequest', ], 'output' => [ 'shape' => 'GetCustomDomainAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEndpointAccess' => [ 'name' => 'GetEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEndpointAccessRequest', ], 'output' => [ 'shape' => 'GetEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetNamespace' => [ 'name' => 'GetNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetNamespaceRequest', ], 'output' => [ 'shape' => 'GetNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRecoveryPoint' => [ 'name' => 'GetRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRecoveryPointRequest', ], 'output' => [ 'shape' => 'GetRecoveryPointResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetScheduledAction' => [ 'name' => 'GetScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetScheduledActionRequest', ], 'output' => [ 'shape' => 'GetScheduledActionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetSnapshot' => [ 'name' => 'GetSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSnapshotRequest', ], 'output' => [ 'shape' => 'GetSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTableRestoreStatus' => [ 'name' => 'GetTableRestoreStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableRestoreStatusRequest', ], 'output' => [ 'shape' => 'GetTableRestoreStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetUsageLimit' => [ 'name' => 'GetUsageLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUsageLimitRequest', ], 'output' => [ 'shape' => 'GetUsageLimitResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetWorkgroup' => [ 'name' => 'GetWorkgroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkgroupRequest', ], 'output' => [ 'shape' => 'GetWorkgroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListCustomDomainAssociations' => [ 'name' => 'ListCustomDomainAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomDomainAssociationsRequest', ], 'output' => [ 'shape' => 'ListCustomDomainAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidPaginationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEndpointAccess' => [ 'name' => 'ListEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointAccessRequest', ], 'output' => [ 'shape' => 'ListEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListNamespaces' => [ 'name' => 'ListNamespaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListNamespacesRequest', ], 'output' => [ 'shape' => 'ListNamespacesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListRecoveryPoints' => [ 'name' => 'ListRecoveryPoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecoveryPointsRequest', ], 'output' => [ 'shape' => 'ListRecoveryPointsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListScheduledActions' => [ 'name' => 'ListScheduledActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListScheduledActionsRequest', ], 'output' => [ 'shape' => 'ListScheduledActionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidPaginationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSnapshotCopyConfigurations' => [ 'name' => 'ListSnapshotCopyConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSnapshotCopyConfigurationsRequest', ], 'output' => [ 'shape' => 'ListSnapshotCopyConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidPaginationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSnapshots' => [ 'name' => 'ListSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSnapshotsRequest', ], 'output' => [ 'shape' => 'ListSnapshotsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTableRestoreStatus' => [ 'name' => 'ListTableRestoreStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTableRestoreStatusRequest', ], 'output' => [ 'shape' => 'ListTableRestoreStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListUsageLimits' => [ 'name' => 'ListUsageLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsageLimitsRequest', ], 'output' => [ 'shape' => 'ListUsageLimitsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidPaginationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListWorkgroups' => [ 'name' => 'ListWorkgroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkgroupsRequest', ], 'output' => [ 'shape' => 'ListWorkgroupsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RestoreFromRecoveryPoint' => [ 'name' => 'RestoreFromRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreFromRecoveryPointRequest', ], 'output' => [ 'shape' => 'RestoreFromRecoveryPointResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'RestoreFromSnapshot' => [ 'name' => 'RestoreFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreFromSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'RestoreTableFromRecoveryPoint' => [ 'name' => 'RestoreTableFromRecoveryPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreTableFromRecoveryPointRequest', ], 'output' => [ 'shape' => 'RestoreTableFromRecoveryPointResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'RestoreTableFromSnapshot' => [ 'name' => 'RestoreTableFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreTableFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreTableFromSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateCustomDomainAssociation' => [ 'name' => 'UpdateCustomDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCustomDomainAssociationRequest', ], 'output' => [ 'shape' => 'UpdateCustomDomainAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateEndpointAccess' => [ 'name' => 'UpdateEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEndpointAccessRequest', ], 'output' => [ 'shape' => 'UpdateEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateNamespace' => [ 'name' => 'UpdateNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNamespaceRequest', ], 'output' => [ 'shape' => 'UpdateNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateScheduledAction' => [ 'name' => 'UpdateScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateScheduledActionRequest', ], 'output' => [ 'shape' => 'UpdateScheduledActionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateSnapshot' => [ 'name' => 'UpdateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSnapshotRequest', ], 'output' => [ 'shape' => 'UpdateSnapshotResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateSnapshotCopyConfiguration' => [ 'name' => 'UpdateSnapshotCopyConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSnapshotCopyConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSnapshotCopyConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateUsageLimit' => [ 'name' => 'UpdateUsageLimit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUsageLimitRequest', ], 'output' => [ 'shape' => 'UpdateUsageLimitResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateWorkgroup' => [ 'name' => 'UpdateWorkgroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkgroupRequest', ], 'output' => [ 'shape' => 'UpdateWorkgroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InsufficientCapacityException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'Association' => [ 'type' => 'structure', 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainCertificateExpiryTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'AssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Association', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConfigParameter' => [ 'type' => 'structure', 'members' => [ 'parameterKey' => [ 'shape' => 'ParameterKey', ], 'parameterValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ConfigParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigParameter', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ConvertRecoveryPointToSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryPointId', 'snapshotName', ], 'members' => [ 'recoveryPointId' => [ 'shape' => 'String', ], 'retentionPeriod' => [ 'shape' => 'Integer', ], 'snapshotName' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'ConvertRecoveryPointToSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateCustomDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'customDomainCertificateArn', 'customDomainName', 'workgroupName', ], 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'CreateCustomDomainAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainCertificateExpiryTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'CreateEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'endpointName', 'subnetIds', 'workgroupName', ], 'members' => [ 'endpointName' => [ 'shape' => 'String', ], 'ownerAccount' => [ 'shape' => 'OwnerAccount', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'vpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'CreateEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'EndpointAccess', ], ], ], 'CreateNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', ], 'members' => [ 'adminPasswordSecretKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'adminUserPassword' => [ 'shape' => 'DbPassword', ], 'adminUsername' => [ 'shape' => 'DbUser', ], 'dbName' => [ 'shape' => 'String', ], 'defaultIamRoleArn' => [ 'shape' => 'String', ], 'iamRoles' => [ 'shape' => 'IamRoleArnList', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'logExports' => [ 'shape' => 'LogExportList', ], 'manageAdminPassword' => [ 'shape' => 'Boolean', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'redshiftIdcApplicationArn' => [ 'shape' => 'RedshiftIdcApplicationArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateNamespaceResponse' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], ], ], 'CreateScheduledActionRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'roleArn', 'schedule', 'scheduledActionName', 'targetAction', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'schedule' => [ 'shape' => 'Schedule', ], 'scheduledActionDescription' => [ 'shape' => 'String', ], 'scheduledActionName' => [ 'shape' => 'ScheduledActionName', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'targetAction' => [ 'shape' => 'TargetAction', ], ], ], 'CreateScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAction' => [ 'shape' => 'ScheduledActionResponse', ], ], ], 'CreateSnapshotCopyConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationRegion', 'namespaceName', ], 'members' => [ 'destinationKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'destinationRegion' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'snapshotRetentionPeriod' => [ 'shape' => 'Integer', ], ], ], 'CreateSnapshotCopyConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfiguration', ], 'members' => [ 'snapshotCopyConfiguration' => [ 'shape' => 'SnapshotCopyConfiguration', ], ], ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'snapshotName', ], 'members' => [ 'namespaceName' => [ 'shape' => 'String', ], 'retentionPeriod' => [ 'shape' => 'Integer', ], 'snapshotName' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateSnapshotScheduleActionParameters' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'snapshotNamePrefix', ], 'members' => [ 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'retentionPeriod' => [ 'shape' => 'Integer', ], 'snapshotNamePrefix' => [ 'shape' => 'SnapshotNamePrefix', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUsageLimitRequest' => [ 'type' => 'structure', 'required' => [ 'amount', 'resourceArn', 'usageType', ], 'members' => [ 'amount' => [ 'shape' => 'Long', ], 'breachAction' => [ 'shape' => 'UsageLimitBreachAction', ], 'period' => [ 'shape' => 'UsageLimitPeriod', ], 'resourceArn' => [ 'shape' => 'String', ], 'usageType' => [ 'shape' => 'UsageLimitUsageType', ], ], ], 'CreateUsageLimitResponse' => [ 'type' => 'structure', 'members' => [ 'usageLimit' => [ 'shape' => 'UsageLimit', ], ], ], 'CreateWorkgroupRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'workgroupName', ], 'members' => [ 'baseCapacity' => [ 'shape' => 'Integer', ], 'configParameters' => [ 'shape' => 'ConfigParameterList', ], 'enhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'maxCapacity' => [ 'shape' => 'Integer', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'port' => [ 'shape' => 'Integer', ], 'publiclyAccessible' => [ 'shape' => 'Boolean', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'tags' => [ 'shape' => 'TagList', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'CreateWorkgroupResponse' => [ 'type' => 'structure', 'members' => [ 'workgroup' => [ 'shape' => 'Workgroup', ], ], ], 'CustomDomainCertificateArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:[\\w+=/,.@-]+:acm:[\\w+=/,.@-]*:[0-9]+:[\\w+=,.@-]+(/[\\w+=,.@-]+)*', ], 'CustomDomainName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])$', ], 'DbName' => [ 'type' => 'string', ], 'DbPassword' => [ 'type' => 'string', 'sensitive' => true, ], 'DbUser' => [ 'type' => 'string', 'sensitive' => true, ], 'DeleteCustomDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'customDomainName', 'workgroupName', ], 'members' => [ 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'DeleteCustomDomainAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'endpointName', ], 'members' => [ 'endpointName' => [ 'shape' => 'String', ], ], ], 'DeleteEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'EndpointAccess', ], ], ], 'DeleteNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', ], 'members' => [ 'finalSnapshotName' => [ 'shape' => 'String', ], 'finalSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], ], ], 'DeleteNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteScheduledActionRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledActionName', ], 'members' => [ 'scheduledActionName' => [ 'shape' => 'ScheduledActionName', ], ], ], 'DeleteScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAction' => [ 'shape' => 'ScheduledActionResponse', ], ], ], 'DeleteSnapshotCopyConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfigurationId', ], 'members' => [ 'snapshotCopyConfigurationId' => [ 'shape' => 'String', ], ], ], 'DeleteSnapshotCopyConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfiguration', ], 'members' => [ 'snapshotCopyConfiguration' => [ 'shape' => 'SnapshotCopyConfiguration', ], ], ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'snapshotName', ], 'members' => [ 'snapshotName' => [ 'shape' => 'String', ], ], ], 'DeleteSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'DeleteUsageLimitRequest' => [ 'type' => 'structure', 'required' => [ 'usageLimitId', ], 'members' => [ 'usageLimitId' => [ 'shape' => 'String', ], ], ], 'DeleteUsageLimitResponse' => [ 'type' => 'structure', 'members' => [ 'usageLimit' => [ 'shape' => 'UsageLimit', ], ], ], 'DeleteWorkgroupRequest' => [ 'type' => 'structure', 'required' => [ 'workgroupName', ], 'members' => [ 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'DeleteWorkgroupResponse' => [ 'type' => 'structure', 'required' => [ 'workgroup', ], 'members' => [ 'workgroup' => [ 'shape' => 'Workgroup', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'address' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Integer', ], 'vpcEndpoints' => [ 'shape' => 'VpcEndpointList', ], ], ], 'EndpointAccess' => [ 'type' => 'structure', 'members' => [ 'address' => [ 'shape' => 'String', ], 'endpointArn' => [ 'shape' => 'String', ], 'endpointCreateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endpointName' => [ 'shape' => 'String', ], 'endpointStatus' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Integer', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'vpcEndpoint' => [ 'shape' => 'VpcEndpoint', ], 'vpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'EndpointAccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointAccess', ], ], 'GetCredentialsRequest' => [ 'type' => 'structure', 'members' => [ 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'dbName' => [ 'shape' => 'DbName', ], 'durationSeconds' => [ 'shape' => 'Integer', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'GetCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'dbPassword' => [ 'shape' => 'DbPassword', ], 'dbUser' => [ 'shape' => 'DbUser', ], 'expiration' => [ 'shape' => 'Timestamp', ], 'nextRefreshTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetCustomDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'customDomainName', 'workgroupName', ], 'members' => [ 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'GetCustomDomainAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainCertificateExpiryTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'GetEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'endpointName', ], 'members' => [ 'endpointName' => [ 'shape' => 'String', ], ], ], 'GetEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'EndpointAccess', ], ], ], 'GetNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', ], 'members' => [ 'namespaceName' => [ 'shape' => 'NamespaceName', ], ], ], 'GetNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], ], ], 'GetRecoveryPointRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryPointId', ], 'members' => [ 'recoveryPointId' => [ 'shape' => 'String', ], ], ], 'GetRecoveryPointResponse' => [ 'type' => 'structure', 'members' => [ 'recoveryPoint' => [ 'shape' => 'RecoveryPoint', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'GetScheduledActionRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledActionName', ], 'members' => [ 'scheduledActionName' => [ 'shape' => 'ScheduledActionName', ], ], ], 'GetScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAction' => [ 'shape' => 'ScheduledActionResponse', ], ], ], 'GetSnapshotRequest' => [ 'type' => 'structure', 'members' => [ 'ownerAccount' => [ 'shape' => 'String', ], 'snapshotArn' => [ 'shape' => 'String', ], 'snapshotName' => [ 'shape' => 'String', ], ], ], 'GetSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'GetTableRestoreStatusRequest' => [ 'type' => 'structure', 'required' => [ 'tableRestoreRequestId', ], 'members' => [ 'tableRestoreRequestId' => [ 'shape' => 'String', ], ], ], 'GetTableRestoreStatusResponse' => [ 'type' => 'structure', 'members' => [ 'tableRestoreStatus' => [ 'shape' => 'TableRestoreStatus', ], ], ], 'GetUsageLimitRequest' => [ 'type' => 'structure', 'required' => [ 'usageLimitId', ], 'members' => [ 'usageLimitId' => [ 'shape' => 'String', ], ], ], 'GetUsageLimitResponse' => [ 'type' => 'structure', 'members' => [ 'usageLimit' => [ 'shape' => 'UsageLimit', ], ], ], 'GetWorkgroupRequest' => [ 'type' => 'structure', 'required' => [ 'workgroupName', ], 'members' => [ 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'GetWorkgroupResponse' => [ 'type' => 'structure', 'required' => [ 'workgroup', ], 'members' => [ 'workgroup' => [ 'shape' => 'Workgroup', ], ], ], 'IamRoleArn' => [ 'type' => 'string', ], 'IamRoleArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IamRoleArn', ], ], 'InsufficientCapacityException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidPaginationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'KmsKeyId' => [ 'type' => 'string', ], 'ListCustomDomainAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'maxResults' => [ 'shape' => 'ListCustomDomainAssociationsRequestMaxResultsInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListCustomDomainAssociationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListCustomDomainAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'associations' => [ 'shape' => 'AssociationList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEndpointAccessRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListEndpointAccessRequestMaxResultsInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'String', ], 'ownerAccount' => [ 'shape' => 'OwnerAccount', ], 'vpcId' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'ListEndpointAccessRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListEndpointAccessResponse' => [ 'type' => 'structure', 'required' => [ 'endpoints', ], 'members' => [ 'endpoints' => [ 'shape' => 'EndpointAccessList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListNamespacesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListNamespacesRequestMaxResultsInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListNamespacesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListNamespacesResponse' => [ 'type' => 'structure', 'required' => [ 'namespaces', ], 'members' => [ 'namespaces' => [ 'shape' => 'NamespaceList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListRecoveryPointsRequest' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'maxResults' => [ 'shape' => 'ListRecoveryPointsRequestMaxResultsInteger', 'box' => true, ], 'namespaceArn' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'nextToken' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListRecoveryPointsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRecoveryPointsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'recoveryPoints' => [ 'shape' => 'RecoveryPointList', ], ], ], 'ListScheduledActionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListScheduledActionsRequestMaxResultsInteger', 'box' => true, ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListScheduledActionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListScheduledActionsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'scheduledActions' => [ 'shape' => 'ScheduledActionsList', ], ], ], 'ListSnapshotCopyConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSnapshotCopyConfigurationsRequestMaxResultsInteger', 'box' => true, ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSnapshotCopyConfigurationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSnapshotCopyConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'snapshotCopyConfigurations' => [ 'shape' => 'SnapshotCopyConfigurations', ], ], ], 'ListSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'maxResults' => [ 'shape' => 'ListSnapshotsRequestMaxResultsInteger', 'box' => true, ], 'namespaceArn' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'String', ], 'ownerAccount' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListSnapshotsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'snapshots' => [ 'shape' => 'SnapshotList', ], ], ], 'ListTableRestoreStatusRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListTableRestoreStatusRequestMaxResultsInteger', 'box' => true, ], 'namespaceName' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'ListTableRestoreStatusRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTableRestoreStatusResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'tableRestoreStatuses' => [ 'shape' => 'TableRestoreStatusList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'ListUsageLimitsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListUsageLimitsRequestMaxResultsInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'resourceArn' => [ 'shape' => 'String', ], 'usageType' => [ 'shape' => 'UsageLimitUsageType', ], ], ], 'ListUsageLimitsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListUsageLimitsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'usageLimits' => [ 'shape' => 'UsageLimits', ], ], ], 'ListWorkgroupsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListWorkgroupsRequestMaxResultsInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'String', ], 'ownerAccount' => [ 'shape' => 'OwnerAccount', ], ], ], 'ListWorkgroupsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListWorkgroupsResponse' => [ 'type' => 'structure', 'required' => [ 'workgroups', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'workgroups' => [ 'shape' => 'WorkgroupList', ], ], ], 'LogExport' => [ 'type' => 'string', 'enum' => [ 'useractivitylog', 'userlog', 'connectionlog', ], ], 'LogExportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogExport', ], 'max' => 16, 'min' => 0, ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'Namespace' => [ 'type' => 'structure', 'members' => [ 'adminPasswordSecretArn' => [ 'shape' => 'String', ], 'adminPasswordSecretKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'adminUsername' => [ 'shape' => 'DbUser', ], 'creationDate' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'dbName' => [ 'shape' => 'String', ], 'defaultIamRoleArn' => [ 'shape' => 'String', ], 'iamRoles' => [ 'shape' => 'IamRoleArnList', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'logExports' => [ 'shape' => 'LogExportList', ], 'namespaceArn' => [ 'shape' => 'String', ], 'namespaceId' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'status' => [ 'shape' => 'NamespaceStatus', ], ], ], 'NamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Namespace', ], ], 'NamespaceName' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '^[a-z0-9-]+$', ], 'NamespaceStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'MODIFYING', 'DELETING', ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'availabilityZone' => [ 'shape' => 'String', ], 'networkInterfaceId' => [ 'shape' => 'String', ], 'privateIpAddress' => [ 'shape' => 'String', ], 'subnetId' => [ 'shape' => 'String', ], ], ], 'NetworkInterfaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], ], 'NextInvocationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], ], 'OwnerAccount' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '(\\d{12})', ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 8, ], 'ParameterKey' => [ 'type' => 'string', ], 'ParameterValue' => [ 'type' => 'string', ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policy', 'resourceArn', ], 'members' => [ 'policy' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'String', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'RecoveryPoint' => [ 'type' => 'structure', 'members' => [ 'namespaceArn' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'recoveryPointCreateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'recoveryPointId' => [ 'shape' => 'String', ], 'totalSizeInMegaBytes' => [ 'shape' => 'Double', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'RecoveryPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryPoint', ], ], 'RedshiftIdcApplicationArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'String', ], ], ], 'RestoreFromRecoveryPointRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'recoveryPointId', 'workgroupName', ], 'members' => [ 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'recoveryPointId' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'RestoreFromRecoveryPointResponse' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], 'recoveryPointId' => [ 'shape' => 'String', ], ], ], 'RestoreFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'workgroupName', ], 'members' => [ 'adminPasswordSecretKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'manageAdminPassword' => [ 'shape' => 'Boolean', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'ownerAccount' => [ 'shape' => 'String', ], 'snapshotArn' => [ 'shape' => 'String', ], 'snapshotName' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'RestoreFromSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], 'ownerAccount' => [ 'shape' => 'String', ], 'snapshotName' => [ 'shape' => 'String', ], ], ], 'RestoreTableFromRecoveryPointRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'newTableName', 'recoveryPointId', 'sourceDatabaseName', 'sourceTableName', 'workgroupName', ], 'members' => [ 'activateCaseSensitiveIdentifier' => [ 'shape' => 'Boolean', ], 'namespaceName' => [ 'shape' => 'String', ], 'newTableName' => [ 'shape' => 'String', ], 'recoveryPointId' => [ 'shape' => 'String', ], 'sourceDatabaseName' => [ 'shape' => 'String', ], 'sourceSchemaName' => [ 'shape' => 'String', ], 'sourceTableName' => [ 'shape' => 'String', ], 'targetDatabaseName' => [ 'shape' => 'String', ], 'targetSchemaName' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'RestoreTableFromRecoveryPointResponse' => [ 'type' => 'structure', 'members' => [ 'tableRestoreStatus' => [ 'shape' => 'TableRestoreStatus', ], ], ], 'RestoreTableFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'newTableName', 'snapshotName', 'sourceDatabaseName', 'sourceTableName', 'workgroupName', ], 'members' => [ 'activateCaseSensitiveIdentifier' => [ 'shape' => 'Boolean', ], 'namespaceName' => [ 'shape' => 'String', ], 'newTableName' => [ 'shape' => 'String', ], 'snapshotName' => [ 'shape' => 'String', ], 'sourceDatabaseName' => [ 'shape' => 'String', ], 'sourceSchemaName' => [ 'shape' => 'String', ], 'sourceTableName' => [ 'shape' => 'String', ], 'targetDatabaseName' => [ 'shape' => 'String', ], 'targetSchemaName' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'RestoreTableFromSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'tableRestoreStatus' => [ 'shape' => 'TableRestoreStatus', ], ], ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'at' => [ 'shape' => 'Timestamp', ], 'cron' => [ 'shape' => 'String', ], ], 'union' => true, ], 'ScheduledActionName' => [ 'type' => 'string', 'max' => 60, 'min' => 3, 'pattern' => '^[a-z0-9-]+$', ], 'ScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'nextInvocations' => [ 'shape' => 'NextInvocationsList', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'schedule' => [ 'shape' => 'Schedule', ], 'scheduledActionDescription' => [ 'shape' => 'String', ], 'scheduledActionName' => [ 'shape' => 'ScheduledActionName', ], 'scheduledActionUuid' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'State', ], 'targetAction' => [ 'shape' => 'TargetAction', ], ], ], 'ScheduledActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledActionName', ], ], 'SecurityGroupId' => [ 'type' => 'string', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'accountsWithProvisionedRestoreAccess' => [ 'shape' => 'AccountIdList', ], 'accountsWithRestoreAccess' => [ 'shape' => 'AccountIdList', ], 'actualIncrementalBackupSizeInMegaBytes' => [ 'shape' => 'Double', ], 'adminPasswordSecretArn' => [ 'shape' => 'String', ], 'adminPasswordSecretKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'adminUsername' => [ 'shape' => 'String', ], 'backupProgressInMegaBytes' => [ 'shape' => 'Double', ], 'currentBackupRateInMegaBytesPerSecond' => [ 'shape' => 'Double', ], 'elapsedTimeInSeconds' => [ 'shape' => 'Long', ], 'estimatedSecondsToCompletion' => [ 'shape' => 'Long', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'namespaceArn' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'String', ], 'ownerAccount' => [ 'shape' => 'String', ], 'snapshotArn' => [ 'shape' => 'String', ], 'snapshotCreateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'snapshotName' => [ 'shape' => 'String', ], 'snapshotRemainingDays' => [ 'shape' => 'Integer', ], 'snapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'snapshotRetentionStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'status' => [ 'shape' => 'SnapshotStatus', ], 'totalBackupSizeInMegaBytes' => [ 'shape' => 'Double', ], ], ], 'SnapshotCopyConfiguration' => [ 'type' => 'structure', 'members' => [ 'destinationKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'destinationRegion' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], 'snapshotCopyConfigurationArn' => [ 'shape' => 'String', ], 'snapshotCopyConfigurationId' => [ 'shape' => 'String', ], 'snapshotRetentionPeriod' => [ 'shape' => 'Integer', ], ], ], 'SnapshotCopyConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotCopyConfiguration', ], 'max' => 100, 'min' => 1, ], 'SnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'SnapshotNamePrefix' => [ 'type' => 'string', 'max' => 235, 'min' => 1, ], 'SnapshotStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'CREATING', 'DELETED', 'CANCELLED', 'FAILED', 'COPYING', ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DISABLED', ], ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TableRestoreStatus' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'namespaceName' => [ 'shape' => 'String', ], 'newTableName' => [ 'shape' => 'String', ], 'progressInMegaBytes' => [ 'shape' => 'Long', ], 'recoveryPointId' => [ 'shape' => 'String', ], 'requestTime' => [ 'shape' => 'Timestamp', ], 'snapshotName' => [ 'shape' => 'String', ], 'sourceDatabaseName' => [ 'shape' => 'String', ], 'sourceSchemaName' => [ 'shape' => 'String', ], 'sourceTableName' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'String', ], 'tableRestoreRequestId' => [ 'shape' => 'String', ], 'targetDatabaseName' => [ 'shape' => 'String', ], 'targetSchemaName' => [ 'shape' => 'String', ], 'totalDataInMegaBytes' => [ 'shape' => 'Long', ], 'workgroupName' => [ 'shape' => 'String', ], ], ], 'TableRestoreStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableRestoreStatus', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetAction' => [ 'type' => 'structure', 'members' => [ 'createSnapshot' => [ 'shape' => 'CreateSnapshotScheduleActionParameters', ], ], 'union' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCustomDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'customDomainCertificateArn', 'customDomainName', 'workgroupName', ], 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'UpdateCustomDomainAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainCertificateExpiryTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'UpdateEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'endpointName', ], 'members' => [ 'endpointName' => [ 'shape' => 'String', ], 'vpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], ], ], 'UpdateEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'EndpointAccess', ], ], ], 'UpdateNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'namespaceName', ], 'members' => [ 'adminPasswordSecretKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'adminUserPassword' => [ 'shape' => 'DbPassword', ], 'adminUsername' => [ 'shape' => 'DbUser', ], 'defaultIamRoleArn' => [ 'shape' => 'String', ], 'iamRoles' => [ 'shape' => 'IamRoleArnList', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'logExports' => [ 'shape' => 'LogExportList', ], 'manageAdminPassword' => [ 'shape' => 'Boolean', ], 'namespaceName' => [ 'shape' => 'NamespaceName', ], ], ], 'UpdateNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'Namespace', ], ], ], 'UpdateScheduledActionRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledActionName', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'schedule' => [ 'shape' => 'Schedule', ], 'scheduledActionDescription' => [ 'shape' => 'String', ], 'scheduledActionName' => [ 'shape' => 'ScheduledActionName', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'targetAction' => [ 'shape' => 'TargetAction', ], ], ], 'UpdateScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAction' => [ 'shape' => 'ScheduledActionResponse', ], ], ], 'UpdateSnapshotCopyConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfigurationId', ], 'members' => [ 'snapshotCopyConfigurationId' => [ 'shape' => 'String', ], 'snapshotRetentionPeriod' => [ 'shape' => 'Integer', ], ], ], 'UpdateSnapshotCopyConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'snapshotCopyConfiguration', ], 'members' => [ 'snapshotCopyConfiguration' => [ 'shape' => 'SnapshotCopyConfiguration', ], ], ], 'UpdateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'snapshotName', ], 'members' => [ 'retentionPeriod' => [ 'shape' => 'Integer', ], 'snapshotName' => [ 'shape' => 'String', ], ], ], 'UpdateSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'UpdateUsageLimitRequest' => [ 'type' => 'structure', 'required' => [ 'usageLimitId', ], 'members' => [ 'amount' => [ 'shape' => 'Long', ], 'breachAction' => [ 'shape' => 'UsageLimitBreachAction', ], 'usageLimitId' => [ 'shape' => 'String', ], ], ], 'UpdateUsageLimitResponse' => [ 'type' => 'structure', 'members' => [ 'usageLimit' => [ 'shape' => 'UsageLimit', ], ], ], 'UpdateWorkgroupRequest' => [ 'type' => 'structure', 'required' => [ 'workgroupName', ], 'members' => [ 'baseCapacity' => [ 'shape' => 'Integer', ], 'configParameters' => [ 'shape' => 'ConfigParameterList', ], 'enhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'maxCapacity' => [ 'shape' => 'Integer', ], 'port' => [ 'shape' => 'Integer', ], 'publiclyAccessible' => [ 'shape' => 'Boolean', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], ], ], 'UpdateWorkgroupResponse' => [ 'type' => 'structure', 'required' => [ 'workgroup', ], 'members' => [ 'workgroup' => [ 'shape' => 'Workgroup', ], ], ], 'UsageLimit' => [ 'type' => 'structure', 'members' => [ 'amount' => [ 'shape' => 'Long', ], 'breachAction' => [ 'shape' => 'UsageLimitBreachAction', ], 'period' => [ 'shape' => 'UsageLimitPeriod', ], 'resourceArn' => [ 'shape' => 'String', ], 'usageLimitArn' => [ 'shape' => 'String', ], 'usageLimitId' => [ 'shape' => 'String', ], 'usageType' => [ 'shape' => 'UsageLimitUsageType', ], ], ], 'UsageLimitBreachAction' => [ 'type' => 'string', 'enum' => [ 'log', 'emit-metric', 'deactivate', ], ], 'UsageLimitPeriod' => [ 'type' => 'string', 'enum' => [ 'daily', 'weekly', 'monthly', ], ], 'UsageLimitUsageType' => [ 'type' => 'string', 'enum' => [ 'serverless-compute', 'cross-region-datasharing', ], ], 'UsageLimits' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageLimit', ], 'max' => 100, 'min' => 1, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'VpcEndpoint' => [ 'type' => 'structure', 'members' => [ 'networkInterfaces' => [ 'shape' => 'NetworkInterfaceList', ], 'vpcEndpointId' => [ 'shape' => 'String', ], 'vpcId' => [ 'shape' => 'String', ], ], ], 'VpcEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpoint', ], ], 'VpcIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'VpcSecurityGroupId' => [ 'type' => 'string', ], 'VpcSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupId', ], ], 'VpcSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'vpcSecurityGroupId' => [ 'shape' => 'VpcSecurityGroupId', ], ], ], 'VpcSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupMembership', ], ], 'Workgroup' => [ 'type' => 'structure', 'members' => [ 'baseCapacity' => [ 'shape' => 'Integer', ], 'configParameters' => [ 'shape' => 'ConfigParameterList', ], 'creationDate' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'crossAccountVpcs' => [ 'shape' => 'VpcIds', ], 'customDomainCertificateArn' => [ 'shape' => 'CustomDomainCertificateArnString', ], 'customDomainCertificateExpiryTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'customDomainName' => [ 'shape' => 'CustomDomainName', ], 'endpoint' => [ 'shape' => 'Endpoint', ], 'enhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'maxCapacity' => [ 'shape' => 'Integer', ], 'namespaceName' => [ 'shape' => 'String', ], 'patchVersion' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Integer', ], 'publiclyAccessible' => [ 'shape' => 'Boolean', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'status' => [ 'shape' => 'WorkgroupStatus', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'workgroupArn' => [ 'shape' => 'String', ], 'workgroupId' => [ 'shape' => 'String', ], 'workgroupName' => [ 'shape' => 'WorkgroupName', ], 'workgroupVersion' => [ 'shape' => 'String', ], ], ], 'WorkgroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workgroup', ], ], 'WorkgroupName' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '^[a-z0-9-]+$', ], 'WorkgroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'AVAILABLE', 'MODIFYING', 'DELETING', ], ], ],];
