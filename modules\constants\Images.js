// local imgs
const ProfileBackground = require("../assets/imgs/profile-screen-bg.png");
const RegisterBackground = require("../assets/imgs/register-bg.png");
const noData = require("../assets/imgs/nodata.png");
import config from './../config';
// internet imgs

const ProfilePicture = 'https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?fit=crop&w=1650&q=80';
const RemoteLogo=config.LOGO;

export default {
  ProfileBackground,
  ProfilePicture,
  RegisterBackground,
  noData,
  RemoteLogo
};