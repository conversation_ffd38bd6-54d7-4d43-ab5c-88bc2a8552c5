<?php
// This file was auto-generated from sdk-root/src/data/autoscaling/2011-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2011-01-01', 'endpointPrefix' => 'autoscaling', 'protocol' => 'query', 'serviceFullName' => 'Auto Scaling', 'serviceId' => 'Auto Scaling', 'signatureVersion' => 'v4', 'uid' => 'autoscaling-2011-01-01', 'xmlNamespace' => 'http://autoscaling.amazonaws.com/doc/2011-01-01/', ], 'operations' => [ 'AttachInstances' => [ 'name' => 'AttachInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachInstancesQuery', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'AttachLoadBalancerTargetGroups' => [ 'name' => 'AttachLoadBalancerTargetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachLoadBalancerTargetGroupsType', ], 'output' => [ 'shape' => 'AttachLoadBalancerTargetGroupsResultType', 'resultWrapper' => 'AttachLoadBalancerTargetGroupsResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'AttachLoadBalancers' => [ 'name' => 'AttachLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachLoadBalancersType', ], 'output' => [ 'shape' => 'AttachLoadBalancersResultType', 'resultWrapper' => 'AttachLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'AttachTrafficSources' => [ 'name' => 'AttachTrafficSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachTrafficSourcesType', ], 'output' => [ 'shape' => 'AttachTrafficSourcesResultType', 'resultWrapper' => 'AttachTrafficSourcesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'BatchDeleteScheduledAction' => [ 'name' => 'BatchDeleteScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteScheduledActionType', ], 'output' => [ 'shape' => 'BatchDeleteScheduledActionAnswer', 'resultWrapper' => 'BatchDeleteScheduledActionResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'BatchPutScheduledUpdateGroupAction' => [ 'name' => 'BatchPutScheduledUpdateGroupAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchPutScheduledUpdateGroupActionType', ], 'output' => [ 'shape' => 'BatchPutScheduledUpdateGroupActionAnswer', 'resultWrapper' => 'BatchPutScheduledUpdateGroupActionResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsFault', ], [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'CancelInstanceRefresh' => [ 'name' => 'CancelInstanceRefresh', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelInstanceRefreshType', ], 'output' => [ 'shape' => 'CancelInstanceRefreshAnswer', 'resultWrapper' => 'CancelInstanceRefreshResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ActiveInstanceRefreshNotFoundFault', ], ], ], 'CompleteLifecycleAction' => [ 'name' => 'CompleteLifecycleAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CompleteLifecycleActionType', ], 'output' => [ 'shape' => 'CompleteLifecycleActionAnswer', 'resultWrapper' => 'CompleteLifecycleActionResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'CreateAutoScalingGroup' => [ 'name' => 'CreateAutoScalingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAutoScalingGroupType', ], 'errors' => [ [ 'shape' => 'AlreadyExistsFault', ], [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'CreateLaunchConfiguration' => [ 'name' => 'CreateLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLaunchConfigurationType', ], 'errors' => [ [ 'shape' => 'AlreadyExistsFault', ], [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'CreateOrUpdateTags' => [ 'name' => 'CreateOrUpdateTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOrUpdateTagsType', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'AlreadyExistsFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ResourceInUseFault', ], ], ], 'DeleteAutoScalingGroup' => [ 'name' => 'DeleteAutoScalingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAutoScalingGroupType', ], 'errors' => [ [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceInUseFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DeleteLaunchConfiguration' => [ 'name' => 'DeleteLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'LaunchConfigurationNameType', ], 'errors' => [ [ 'shape' => 'ResourceInUseFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DeleteLifecycleHook' => [ 'name' => 'DeleteLifecycleHook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLifecycleHookType', ], 'output' => [ 'shape' => 'DeleteLifecycleHookAnswer', 'resultWrapper' => 'DeleteLifecycleHookResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DeleteNotificationConfiguration' => [ 'name' => 'DeleteNotificationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNotificationConfigurationType', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyType', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'DeleteScheduledAction' => [ 'name' => 'DeleteScheduledAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteScheduledActionType', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DeleteTags' => [ 'name' => 'DeleteTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTagsType', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ResourceInUseFault', ], ], ], 'DeleteWarmPool' => [ 'name' => 'DeleteWarmPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWarmPoolType', ], 'output' => [ 'shape' => 'DeleteWarmPoolAnswer', 'resultWrapper' => 'DeleteWarmPoolResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceInUseFault', ], ], ], 'DescribeAccountLimits' => [ 'name' => 'DescribeAccountLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeAccountLimitsAnswer', 'resultWrapper' => 'DescribeAccountLimitsResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeAdjustmentTypes' => [ 'name' => 'DescribeAdjustmentTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeAdjustmentTypesAnswer', 'resultWrapper' => 'DescribeAdjustmentTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeAutoScalingGroups' => [ 'name' => 'DescribeAutoScalingGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AutoScalingGroupNamesType', ], 'output' => [ 'shape' => 'AutoScalingGroupsType', 'resultWrapper' => 'DescribeAutoScalingGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeAutoScalingInstances' => [ 'name' => 'DescribeAutoScalingInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAutoScalingInstancesType', ], 'output' => [ 'shape' => 'AutoScalingInstancesType', 'resultWrapper' => 'DescribeAutoScalingInstancesResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeAutoScalingNotificationTypes' => [ 'name' => 'DescribeAutoScalingNotificationTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeAutoScalingNotificationTypesAnswer', 'resultWrapper' => 'DescribeAutoScalingNotificationTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeInstanceRefreshes' => [ 'name' => 'DescribeInstanceRefreshes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceRefreshesType', ], 'output' => [ 'shape' => 'DescribeInstanceRefreshesAnswer', 'resultWrapper' => 'DescribeInstanceRefreshesResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeLaunchConfigurations' => [ 'name' => 'DescribeLaunchConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'LaunchConfigurationNamesType', ], 'output' => [ 'shape' => 'LaunchConfigurationsType', 'resultWrapper' => 'DescribeLaunchConfigurationsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeLifecycleHookTypes' => [ 'name' => 'DescribeLifecycleHookTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeLifecycleHookTypesAnswer', 'resultWrapper' => 'DescribeLifecycleHookTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeLifecycleHooks' => [ 'name' => 'DescribeLifecycleHooks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLifecycleHooksType', ], 'output' => [ 'shape' => 'DescribeLifecycleHooksAnswer', 'resultWrapper' => 'DescribeLifecycleHooksResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeLoadBalancerTargetGroups' => [ 'name' => 'DescribeLoadBalancerTargetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoadBalancerTargetGroupsRequest', ], 'output' => [ 'shape' => 'DescribeLoadBalancerTargetGroupsResponse', 'resultWrapper' => 'DescribeLoadBalancerTargetGroupsResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeLoadBalancers' => [ 'name' => 'DescribeLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoadBalancersRequest', ], 'output' => [ 'shape' => 'DescribeLoadBalancersResponse', 'resultWrapper' => 'DescribeLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeMetricCollectionTypes' => [ 'name' => 'DescribeMetricCollectionTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeMetricCollectionTypesAnswer', 'resultWrapper' => 'DescribeMetricCollectionTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeNotificationConfigurations' => [ 'name' => 'DescribeNotificationConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeNotificationConfigurationsType', ], 'output' => [ 'shape' => 'DescribeNotificationConfigurationsAnswer', 'resultWrapper' => 'DescribeNotificationConfigurationsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribePolicies' => [ 'name' => 'DescribePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePoliciesType', ], 'output' => [ 'shape' => 'PoliciesType', 'resultWrapper' => 'DescribePoliciesResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'DescribeScalingActivities' => [ 'name' => 'DescribeScalingActivities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeScalingActivitiesType', ], 'output' => [ 'shape' => 'ActivitiesType', 'resultWrapper' => 'DescribeScalingActivitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeScalingProcessTypes' => [ 'name' => 'DescribeScalingProcessTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'ProcessesType', 'resultWrapper' => 'DescribeScalingProcessTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeScheduledActions' => [ 'name' => 'DescribeScheduledActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeScheduledActionsType', ], 'output' => [ 'shape' => 'ScheduledActionsType', 'resultWrapper' => 'DescribeScheduledActionsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeTags' => [ 'name' => 'DescribeTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTagsType', ], 'output' => [ 'shape' => 'TagsType', 'resultWrapper' => 'DescribeTagsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeTerminationPolicyTypes' => [ 'name' => 'DescribeTerminationPolicyTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeTerminationPolicyTypesAnswer', 'resultWrapper' => 'DescribeTerminationPolicyTypesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DescribeTrafficSources' => [ 'name' => 'DescribeTrafficSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrafficSourcesRequest', ], 'output' => [ 'shape' => 'DescribeTrafficSourcesResponse', 'resultWrapper' => 'DescribeTrafficSourcesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeWarmPool' => [ 'name' => 'DescribeWarmPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWarmPoolType', ], 'output' => [ 'shape' => 'DescribeWarmPoolAnswer', 'resultWrapper' => 'DescribeWarmPoolResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'DetachInstances' => [ 'name' => 'DetachInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachInstancesQuery', ], 'output' => [ 'shape' => 'DetachInstancesAnswer', 'resultWrapper' => 'DetachInstancesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DetachLoadBalancerTargetGroups' => [ 'name' => 'DetachLoadBalancerTargetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachLoadBalancerTargetGroupsType', ], 'output' => [ 'shape' => 'DetachLoadBalancerTargetGroupsResultType', 'resultWrapper' => 'DetachLoadBalancerTargetGroupsResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DetachLoadBalancers' => [ 'name' => 'DetachLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachLoadBalancersType', ], 'output' => [ 'shape' => 'DetachLoadBalancersResultType', 'resultWrapper' => 'DetachLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DetachTrafficSources' => [ 'name' => 'DetachTrafficSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachTrafficSourcesType', ], 'output' => [ 'shape' => 'DetachTrafficSourcesResultType', 'resultWrapper' => 'DetachTrafficSourcesResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'DisableMetricsCollection' => [ 'name' => 'DisableMetricsCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableMetricsCollectionQuery', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'EnableMetricsCollection' => [ 'name' => 'EnableMetricsCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableMetricsCollectionQuery', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'EnterStandby' => [ 'name' => 'EnterStandby', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnterStandbyQuery', ], 'output' => [ 'shape' => 'EnterStandbyAnswer', 'resultWrapper' => 'EnterStandbyResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'ExecutePolicy' => [ 'name' => 'ExecutePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecutePolicyType', ], 'errors' => [ [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'ExitStandby' => [ 'name' => 'ExitStandby', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExitStandbyQuery', ], 'output' => [ 'shape' => 'ExitStandbyAnswer', 'resultWrapper' => 'ExitStandbyResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'GetPredictiveScalingForecast' => [ 'name' => 'GetPredictiveScalingForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPredictiveScalingForecastType', ], 'output' => [ 'shape' => 'GetPredictiveScalingForecastAnswer', 'resultWrapper' => 'GetPredictiveScalingForecastResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'PutLifecycleHook' => [ 'name' => 'PutLifecycleHook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLifecycleHookType', ], 'output' => [ 'shape' => 'PutLifecycleHookAnswer', 'resultWrapper' => 'PutLifecycleHookResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'PutNotificationConfiguration' => [ 'name' => 'PutNotificationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutNotificationConfigurationType', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'PutScalingPolicy' => [ 'name' => 'PutScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutScalingPolicyType', ], 'output' => [ 'shape' => 'PolicyARNType', 'resultWrapper' => 'PutScalingPolicyResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], 'PutScheduledUpdateGroupAction' => [ 'name' => 'PutScheduledUpdateGroupAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutScheduledUpdateGroupActionType', ], 'errors' => [ [ 'shape' => 'AlreadyExistsFault', ], [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'PutWarmPool' => [ 'name' => 'PutWarmPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutWarmPoolType', ], 'output' => [ 'shape' => 'PutWarmPoolAnswer', 'resultWrapper' => 'PutWarmPoolResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'RecordLifecycleActionHeartbeat' => [ 'name' => 'RecordLifecycleActionHeartbeat', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RecordLifecycleActionHeartbeatType', ], 'output' => [ 'shape' => 'RecordLifecycleActionHeartbeatAnswer', 'resultWrapper' => 'RecordLifecycleActionHeartbeatResult', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'ResumeProcesses' => [ 'name' => 'ResumeProcesses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ScalingProcessQuery', ], 'errors' => [ [ 'shape' => 'ResourceInUseFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'RollbackInstanceRefresh' => [ 'name' => 'RollbackInstanceRefresh', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RollbackInstanceRefreshType', ], 'output' => [ 'shape' => 'RollbackInstanceRefreshAnswer', 'resultWrapper' => 'RollbackInstanceRefreshResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ActiveInstanceRefreshNotFoundFault', ], [ 'shape' => 'IrreversibleInstanceRefreshFault', ], ], ], 'SetDesiredCapacity' => [ 'name' => 'SetDesiredCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetDesiredCapacityType', ], 'errors' => [ [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'SetInstanceHealth' => [ 'name' => 'SetInstanceHealth', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetInstanceHealthQuery', ], 'errors' => [ [ 'shape' => 'ResourceContentionFault', ], ], ], 'SetInstanceProtection' => [ 'name' => 'SetInstanceProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetInstanceProtectionQuery', ], 'output' => [ 'shape' => 'SetInstanceProtectionAnswer', 'resultWrapper' => 'SetInstanceProtectionResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'StartInstanceRefresh' => [ 'name' => 'StartInstanceRefresh', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartInstanceRefreshType', ], 'output' => [ 'shape' => 'StartInstanceRefreshAnswer', 'resultWrapper' => 'StartInstanceRefreshResult', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'InstanceRefreshInProgressFault', ], ], ], 'SuspendProcesses' => [ 'name' => 'SuspendProcesses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ScalingProcessQuery', ], 'errors' => [ [ 'shape' => 'ResourceInUseFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'TerminateInstanceInAutoScalingGroup' => [ 'name' => 'TerminateInstanceInAutoScalingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateInstanceInAutoScalingGroupType', ], 'output' => [ 'shape' => 'ActivityType', 'resultWrapper' => 'TerminateInstanceInAutoScalingGroupResult', ], 'errors' => [ [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceContentionFault', ], ], ], 'UpdateAutoScalingGroup' => [ 'name' => 'UpdateAutoScalingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAutoScalingGroupType', ], 'errors' => [ [ 'shape' => 'ScalingActivityInProgressFault', ], [ 'shape' => 'ResourceContentionFault', ], [ 'shape' => 'ServiceLinkedRoleFailure', ], ], ], ], 'shapes' => [ 'AcceleratorCountRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'AcceleratorManufacturer' => [ 'type' => 'string', 'enum' => [ 'nvidia', 'amd', 'amazon-web-services', 'xilinx', ], ], 'AcceleratorManufacturers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceleratorManufacturer', ], ], 'AcceleratorName' => [ 'type' => 'string', 'enum' => [ 'a100', 'v100', 'k80', 't4', 'm60', 'radeon-pro-v520', 'vu9p', ], ], 'AcceleratorNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceleratorName', ], ], 'AcceleratorTotalMemoryMiBRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'AcceleratorType' => [ 'type' => 'string', 'enum' => [ 'gpu', 'fpga', 'inference', ], ], 'AcceleratorTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceleratorType', ], ], 'ActiveInstanceRefreshNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'ActiveInstanceRefreshNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Activities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Activity', ], ], 'ActivitiesType' => [ 'type' => 'structure', 'required' => [ 'Activities', ], 'members' => [ 'Activities' => [ 'shape' => 'Activities', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'Activity' => [ 'type' => 'structure', 'required' => [ 'ActivityId', 'AutoScalingGroupName', 'Cause', 'StartTime', 'StatusCode', ], 'members' => [ 'ActivityId' => [ 'shape' => 'XmlString', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Description' => [ 'shape' => 'XmlString', ], 'Cause' => [ 'shape' => 'XmlStringMaxLen1023', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'StatusCode' => [ 'shape' => 'ScalingActivityStatusCode', ], 'StatusMessage' => [ 'shape' => 'XmlStringMaxLen255', ], 'Progress' => [ 'shape' => 'Progress', ], 'Details' => [ 'shape' => 'XmlString', ], 'AutoScalingGroupState' => [ 'shape' => 'AutoScalingGroupState', ], 'AutoScalingGroupARN' => [ 'shape' => 'ResourceName', ], ], ], 'ActivityIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlString', ], ], 'ActivityType' => [ 'type' => 'structure', 'members' => [ 'Activity' => [ 'shape' => 'Activity', ], ], ], 'AdjustmentType' => [ 'type' => 'structure', 'members' => [ 'AdjustmentType' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'AdjustmentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdjustmentType', ], ], 'Alarm' => [ 'type' => 'structure', 'members' => [ 'AlarmName' => [ 'shape' => 'XmlStringMaxLen255', ], 'AlarmARN' => [ 'shape' => 'ResourceName', ], ], ], 'AlarmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'AlarmSpecification' => [ 'type' => 'structure', 'members' => [ 'Alarms' => [ 'shape' => 'AlarmList', ], ], ], 'Alarms' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alarm', ], ], 'AllowedInstanceType' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\.\\*\\-]+', ], 'AllowedInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedInstanceType', ], 'max' => 400, ], 'AlreadyExistsFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'AlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AnyPrintableAsciiStringMaxLen4000' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007e]+', ], 'AsciiStringMaxLen255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z0-9\\-_\\/]+', ], 'AssociatePublicIpAddress' => [ 'type' => 'boolean', ], 'AttachInstancesQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'AttachLoadBalancerTargetGroupsResultType' => [ 'type' => 'structure', 'members' => [], ], 'AttachLoadBalancerTargetGroupsType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TargetGroupARNs', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TargetGroupARNs' => [ 'shape' => 'TargetGroupARNs', ], ], ], 'AttachLoadBalancersResultType' => [ 'type' => 'structure', 'members' => [], ], 'AttachLoadBalancersType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'LoadBalancerNames', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LoadBalancerNames' => [ 'shape' => 'LoadBalancerNames', ], ], ], 'AttachTrafficSourcesResultType' => [ 'type' => 'structure', 'members' => [], ], 'AttachTrafficSourcesType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TrafficSources', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TrafficSources' => [ 'shape' => 'TrafficSources', ], ], ], 'AutoRollback' => [ 'type' => 'boolean', ], 'AutoScalingGroup' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'MinSize', 'MaxSize', 'DesiredCapacity', 'DefaultCooldown', 'AvailabilityZones', 'HealthCheckType', 'CreatedTime', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'AutoScalingGroupARN' => [ 'shape' => 'ResourceName', ], 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'MixedInstancesPolicy' => [ 'shape' => 'MixedInstancesPolicy', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'PredictedCapacity' => [ 'shape' => 'AutoScalingGroupPredictedCapacity', ], 'DefaultCooldown' => [ 'shape' => 'Cooldown', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'LoadBalancerNames' => [ 'shape' => 'LoadBalancerNames', ], 'TargetGroupARNs' => [ 'shape' => 'TargetGroupARNs', ], 'HealthCheckType' => [ 'shape' => 'XmlStringMaxLen32', ], 'HealthCheckGracePeriod' => [ 'shape' => 'HealthCheckGracePeriod', ], 'Instances' => [ 'shape' => 'Instances', ], 'CreatedTime' => [ 'shape' => 'TimestampType', ], 'SuspendedProcesses' => [ 'shape' => 'SuspendedProcesses', ], 'PlacementGroup' => [ 'shape' => 'XmlStringMaxLen255', ], 'VPCZoneIdentifier' => [ 'shape' => 'XmlStringMaxLen2047', ], 'EnabledMetrics' => [ 'shape' => 'EnabledMetrics', ], 'Status' => [ 'shape' => 'XmlStringMaxLen255', ], 'Tags' => [ 'shape' => 'TagDescriptionList', ], 'TerminationPolicies' => [ 'shape' => 'TerminationPolicies', ], 'NewInstancesProtectedFromScaleIn' => [ 'shape' => 'InstanceProtected', ], 'ServiceLinkedRoleARN' => [ 'shape' => 'ResourceName', ], 'MaxInstanceLifetime' => [ 'shape' => 'MaxInstanceLifetime', ], 'CapacityRebalance' => [ 'shape' => 'CapacityRebalanceEnabled', ], 'WarmPoolConfiguration' => [ 'shape' => 'WarmPoolConfiguration', ], 'WarmPoolSize' => [ 'shape' => 'WarmPoolSize', ], 'Context' => [ 'shape' => 'Context', ], 'DesiredCapacityType' => [ 'shape' => 'XmlStringMaxLen255', ], 'DefaultInstanceWarmup' => [ 'shape' => 'DefaultInstanceWarmup', ], 'TrafficSources' => [ 'shape' => 'TrafficSources', ], 'InstanceMaintenancePolicy' => [ 'shape' => 'InstanceMaintenancePolicy', ], ], ], 'AutoScalingGroupDesiredCapacity' => [ 'type' => 'integer', ], 'AutoScalingGroupMaxSize' => [ 'type' => 'integer', ], 'AutoScalingGroupMinSize' => [ 'type' => 'integer', ], 'AutoScalingGroupNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'AutoScalingGroupNamesType' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupNames' => [ 'shape' => 'AutoScalingGroupNames', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'AutoScalingGroupPredictedCapacity' => [ 'type' => 'integer', ], 'AutoScalingGroupState' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'AutoScalingGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroup', ], ], 'AutoScalingGroupsType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroups', ], 'members' => [ 'AutoScalingGroups' => [ 'shape' => 'AutoScalingGroups', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'AutoScalingInstanceDetails' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AutoScalingGroupName', 'AvailabilityZone', 'LifecycleState', 'HealthStatus', 'ProtectedFromScaleIn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'InstanceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'AvailabilityZone' => [ 'shape' => 'XmlStringMaxLen255', ], 'LifecycleState' => [ 'shape' => 'XmlStringMaxLen32', ], 'HealthStatus' => [ 'shape' => 'XmlStringMaxLen32', ], 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'ProtectedFromScaleIn' => [ 'shape' => 'InstanceProtected', ], 'WeightedCapacity' => [ 'shape' => 'XmlStringMaxLen32', ], ], ], 'AutoScalingInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingInstanceDetails', ], ], 'AutoScalingInstancesType' => [ 'type' => 'structure', 'members' => [ 'AutoScalingInstances' => [ 'shape' => 'AutoScalingInstances', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'AutoScalingNotificationTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'BareMetal' => [ 'type' => 'string', 'enum' => [ 'included', 'excluded', 'required', ], ], 'BaselineEbsBandwidthMbpsRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'BatchDeleteScheduledActionAnswer' => [ 'type' => 'structure', 'members' => [ 'FailedScheduledActions' => [ 'shape' => 'FailedScheduledUpdateGroupActionRequests', ], ], ], 'BatchDeleteScheduledActionType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ScheduledActionNames', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionNames' => [ 'shape' => 'ScheduledActionNames', ], ], ], 'BatchPutScheduledUpdateGroupActionAnswer' => [ 'type' => 'structure', 'members' => [ 'FailedScheduledUpdateGroupActions' => [ 'shape' => 'FailedScheduledUpdateGroupActionRequests', ], ], ], 'BatchPutScheduledUpdateGroupActionType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ScheduledUpdateGroupActions', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledUpdateGroupActions' => [ 'shape' => 'ScheduledUpdateGroupActionRequests', ], ], ], 'BlockDeviceEbsDeleteOnTermination' => [ 'type' => 'boolean', ], 'BlockDeviceEbsEncrypted' => [ 'type' => 'boolean', ], 'BlockDeviceEbsIops' => [ 'type' => 'integer', 'max' => 20000, 'min' => 100, ], 'BlockDeviceEbsThroughput' => [ 'type' => 'integer', 'max' => 1000, 'min' => 125, ], 'BlockDeviceEbsVolumeSize' => [ 'type' => 'integer', 'max' => 16384, 'min' => 1, ], 'BlockDeviceEbsVolumeType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'BlockDeviceMapping' => [ 'type' => 'structure', 'required' => [ 'DeviceName', ], 'members' => [ 'VirtualName' => [ 'shape' => 'XmlStringMaxLen255', ], 'DeviceName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Ebs' => [ 'shape' => 'Ebs', ], 'NoDevice' => [ 'shape' => 'NoDevice', ], ], ], 'BlockDeviceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockDeviceMapping', ], ], 'BurstablePerformance' => [ 'type' => 'string', 'enum' => [ 'included', 'excluded', 'required', ], ], 'CancelInstanceRefreshAnswer' => [ 'type' => 'structure', 'members' => [ 'InstanceRefreshId' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'CancelInstanceRefreshType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'CapacityForecast' => [ 'type' => 'structure', 'required' => [ 'Timestamps', 'Values', ], 'members' => [ 'Timestamps' => [ 'shape' => 'PredictiveScalingForecastTimestamps', ], 'Values' => [ 'shape' => 'PredictiveScalingForecastValues', ], ], ], 'CapacityRebalanceEnabled' => [ 'type' => 'boolean', ], 'CheckpointDelay' => [ 'type' => 'integer', 'max' => 172800, 'min' => 0, ], 'CheckpointPercentages' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonZeroIntPercent', ], ], 'ClassicLinkVPCSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'CompleteLifecycleActionAnswer' => [ 'type' => 'structure', 'members' => [], ], 'CompleteLifecycleActionType' => [ 'type' => 'structure', 'required' => [ 'LifecycleHookName', 'AutoScalingGroupName', 'LifecycleActionResult', ], 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'ResourceName', ], 'LifecycleActionToken' => [ 'shape' => 'LifecycleActionToken', ], 'LifecycleActionResult' => [ 'shape' => 'LifecycleActionResult', ], 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], ], ], 'Context' => [ 'type' => 'string', ], 'Cooldown' => [ 'type' => 'integer', ], 'CpuManufacturer' => [ 'type' => 'string', 'enum' => [ 'intel', 'amd', 'amazon-web-services', ], ], 'CpuManufacturers' => [ 'type' => 'list', 'member' => [ 'shape' => 'CpuManufacturer', ], ], 'CreateAutoScalingGroupType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'MinSize', 'MaxSize', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'MixedInstancesPolicy' => [ 'shape' => 'MixedInstancesPolicy', ], 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'DefaultCooldown' => [ 'shape' => 'Cooldown', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'LoadBalancerNames' => [ 'shape' => 'LoadBalancerNames', ], 'TargetGroupARNs' => [ 'shape' => 'TargetGroupARNs', ], 'HealthCheckType' => [ 'shape' => 'XmlStringMaxLen32', ], 'HealthCheckGracePeriod' => [ 'shape' => 'HealthCheckGracePeriod', ], 'PlacementGroup' => [ 'shape' => 'XmlStringMaxLen255', ], 'VPCZoneIdentifier' => [ 'shape' => 'XmlStringMaxLen2047', ], 'TerminationPolicies' => [ 'shape' => 'TerminationPolicies', ], 'NewInstancesProtectedFromScaleIn' => [ 'shape' => 'InstanceProtected', ], 'CapacityRebalance' => [ 'shape' => 'CapacityRebalanceEnabled', ], 'LifecycleHookSpecificationList' => [ 'shape' => 'LifecycleHookSpecifications', ], 'Tags' => [ 'shape' => 'Tags', ], 'ServiceLinkedRoleARN' => [ 'shape' => 'ResourceName', ], 'MaxInstanceLifetime' => [ 'shape' => 'MaxInstanceLifetime', ], 'Context' => [ 'shape' => 'Context', ], 'DesiredCapacityType' => [ 'shape' => 'XmlStringMaxLen255', ], 'DefaultInstanceWarmup' => [ 'shape' => 'DefaultInstanceWarmup', ], 'TrafficSources' => [ 'shape' => 'TrafficSources', ], 'InstanceMaintenancePolicy' => [ 'shape' => 'InstanceMaintenancePolicy', ], ], ], 'CreateLaunchConfigurationType' => [ 'type' => 'structure', 'required' => [ 'LaunchConfigurationName', ], 'members' => [ 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ImageId' => [ 'shape' => 'XmlStringMaxLen255', ], 'KeyName' => [ 'shape' => 'XmlStringMaxLen255', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'ClassicLinkVPCId' => [ 'shape' => 'XmlStringMaxLen255', ], 'ClassicLinkVPCSecurityGroups' => [ 'shape' => 'ClassicLinkVPCSecurityGroups', ], 'UserData' => [ 'shape' => 'XmlStringUserData', ], 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'InstanceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'KernelId' => [ 'shape' => 'XmlStringMaxLen255', ], 'RamdiskId' => [ 'shape' => 'XmlStringMaxLen255', ], 'BlockDeviceMappings' => [ 'shape' => 'BlockDeviceMappings', ], 'InstanceMonitoring' => [ 'shape' => 'InstanceMonitoring', ], 'SpotPrice' => [ 'shape' => 'SpotPrice', ], 'IamInstanceProfile' => [ 'shape' => 'XmlStringMaxLen1600', ], 'EbsOptimized' => [ 'shape' => 'EbsOptimized', ], 'AssociatePublicIpAddress' => [ 'shape' => 'AssociatePublicIpAddress', ], 'PlacementTenancy' => [ 'shape' => 'XmlStringMaxLen64', ], 'MetadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], ], ], 'CreateOrUpdateTagsType' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CustomizedMetricSpecification' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'Namespace' => [ 'shape' => 'MetricNamespace', ], 'Dimensions' => [ 'shape' => 'MetricDimensions', ], 'Statistic' => [ 'shape' => 'MetricStatistic', ], 'Unit' => [ 'shape' => 'MetricUnit', ], 'Metrics' => [ 'shape' => 'TargetTrackingMetricDataQueries', ], ], ], 'DefaultInstanceWarmup' => [ 'type' => 'integer', ], 'DeleteAutoScalingGroupType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ForceDelete' => [ 'shape' => 'ForceDelete', ], ], ], 'DeleteLifecycleHookAnswer' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLifecycleHookType' => [ 'type' => 'structure', 'required' => [ 'LifecycleHookName', 'AutoScalingGroupName', ], 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'DeleteNotificationConfigurationType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TopicARN', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TopicARN' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'DeletePolicyType' => [ 'type' => 'structure', 'required' => [ 'PolicyName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteScheduledActionType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ScheduledActionName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'DeleteTagsType' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DeleteWarmPoolAnswer' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWarmPoolType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ForceDelete' => [ 'shape' => 'ForceDelete', ], ], ], 'DescribeAccountLimitsAnswer' => [ 'type' => 'structure', 'members' => [ 'MaxNumberOfAutoScalingGroups' => [ 'shape' => 'MaxNumberOfAutoScalingGroups', ], 'MaxNumberOfLaunchConfigurations' => [ 'shape' => 'MaxNumberOfLaunchConfigurations', ], 'NumberOfAutoScalingGroups' => [ 'shape' => 'NumberOfAutoScalingGroups', ], 'NumberOfLaunchConfigurations' => [ 'shape' => 'NumberOfLaunchConfigurations', ], ], ], 'DescribeAdjustmentTypesAnswer' => [ 'type' => 'structure', 'members' => [ 'AdjustmentTypes' => [ 'shape' => 'AdjustmentTypes', ], ], ], 'DescribeAutoScalingInstancesType' => [ 'type' => 'structure', 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeAutoScalingNotificationTypesAnswer' => [ 'type' => 'structure', 'members' => [ 'AutoScalingNotificationTypes' => [ 'shape' => 'AutoScalingNotificationTypes', ], ], ], 'DescribeInstanceRefreshesAnswer' => [ 'type' => 'structure', 'members' => [ 'InstanceRefreshes' => [ 'shape' => 'InstanceRefreshes', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeInstanceRefreshesType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'InstanceRefreshIds' => [ 'shape' => 'InstanceRefreshIds', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeLifecycleHookTypesAnswer' => [ 'type' => 'structure', 'members' => [ 'LifecycleHookTypes' => [ 'shape' => 'AutoScalingNotificationTypes', ], ], ], 'DescribeLifecycleHooksAnswer' => [ 'type' => 'structure', 'members' => [ 'LifecycleHooks' => [ 'shape' => 'LifecycleHooks', ], ], ], 'DescribeLifecycleHooksType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LifecycleHookNames' => [ 'shape' => 'LifecycleHookNames', ], ], ], 'DescribeLoadBalancerTargetGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeLoadBalancerTargetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerTargetGroups' => [ 'shape' => 'LoadBalancerTargetGroupStates', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeLoadBalancersRequest' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeLoadBalancersResponse' => [ 'type' => 'structure', 'members' => [ 'LoadBalancers' => [ 'shape' => 'LoadBalancerStates', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeMetricCollectionTypesAnswer' => [ 'type' => 'structure', 'members' => [ 'Metrics' => [ 'shape' => 'MetricCollectionTypes', ], 'Granularities' => [ 'shape' => 'MetricGranularityTypes', ], ], ], 'DescribeNotificationConfigurationsAnswer' => [ 'type' => 'structure', 'required' => [ 'NotificationConfigurations', ], 'members' => [ 'NotificationConfigurations' => [ 'shape' => 'NotificationConfigurations', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeNotificationConfigurationsType' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupNames' => [ 'shape' => 'AutoScalingGroupNames', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribePoliciesType' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyNames' => [ 'shape' => 'PolicyNames', ], 'PolicyTypes' => [ 'shape' => 'PolicyTypes', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeScalingActivitiesType' => [ 'type' => 'structure', 'members' => [ 'ActivityIds' => [ 'shape' => 'ActivityIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'IncludeDeletedGroups' => [ 'shape' => 'IncludeDeletedGroups', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeScheduledActionsType' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionNames' => [ 'shape' => 'ScheduledActionNames', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeTagsType' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'Filters', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeTerminationPolicyTypesAnswer' => [ 'type' => 'structure', 'members' => [ 'TerminationPolicyTypes' => [ 'shape' => 'TerminationPolicies', ], ], ], 'DescribeTrafficSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TrafficSourceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeTrafficSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'TrafficSources' => [ 'shape' => 'TrafficSourceStates', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeWarmPoolAnswer' => [ 'type' => 'structure', 'members' => [ 'WarmPoolConfiguration' => [ 'shape' => 'WarmPoolConfiguration', ], 'Instances' => [ 'shape' => 'Instances', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DescribeWarmPoolType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'DesiredConfiguration' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'MixedInstancesPolicy' => [ 'shape' => 'MixedInstancesPolicy', ], ], ], 'DetachInstancesAnswer' => [ 'type' => 'structure', 'members' => [ 'Activities' => [ 'shape' => 'Activities', ], ], ], 'DetachInstancesQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ShouldDecrementDesiredCapacity', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ShouldDecrementDesiredCapacity' => [ 'shape' => 'ShouldDecrementDesiredCapacity', ], ], ], 'DetachLoadBalancerTargetGroupsResultType' => [ 'type' => 'structure', 'members' => [], ], 'DetachLoadBalancerTargetGroupsType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TargetGroupARNs', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TargetGroupARNs' => [ 'shape' => 'TargetGroupARNs', ], ], ], 'DetachLoadBalancersResultType' => [ 'type' => 'structure', 'members' => [], ], 'DetachLoadBalancersType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'LoadBalancerNames', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LoadBalancerNames' => [ 'shape' => 'LoadBalancerNames', ], ], ], 'DetachTrafficSourcesResultType' => [ 'type' => 'structure', 'members' => [], ], 'DetachTrafficSourcesType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TrafficSources', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TrafficSources' => [ 'shape' => 'TrafficSources', ], ], ], 'DisableMetricsCollectionQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Metrics' => [ 'shape' => 'Metrics', ], ], ], 'DisableScaleIn' => [ 'type' => 'boolean', ], 'Ebs' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'XmlStringMaxLen255', ], 'VolumeSize' => [ 'shape' => 'BlockDeviceEbsVolumeSize', ], 'VolumeType' => [ 'shape' => 'BlockDeviceEbsVolumeType', ], 'DeleteOnTermination' => [ 'shape' => 'BlockDeviceEbsDeleteOnTermination', ], 'Iops' => [ 'shape' => 'BlockDeviceEbsIops', ], 'Encrypted' => [ 'shape' => 'BlockDeviceEbsEncrypted', ], 'Throughput' => [ 'shape' => 'BlockDeviceEbsThroughput', ], ], ], 'EbsOptimized' => [ 'type' => 'boolean', ], 'EnableMetricsCollectionQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'Granularity', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Metrics' => [ 'shape' => 'Metrics', ], 'Granularity' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'EnabledMetric' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'XmlStringMaxLen255', ], 'Granularity' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'EnabledMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnabledMetric', ], ], 'EnterStandbyAnswer' => [ 'type' => 'structure', 'members' => [ 'Activities' => [ 'shape' => 'Activities', ], ], ], 'EnterStandbyQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ShouldDecrementDesiredCapacity', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ShouldDecrementDesiredCapacity' => [ 'shape' => 'ShouldDecrementDesiredCapacity', ], ], ], 'EstimatedInstanceWarmup' => [ 'type' => 'integer', ], 'ExcludedInstance' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\.\\*\\-]+', ], 'ExcludedInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExcludedInstance', ], 'max' => 400, ], 'ExecutePolicyType' => [ 'type' => 'structure', 'required' => [ 'PolicyName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyName' => [ 'shape' => 'ResourceName', ], 'HonorCooldown' => [ 'shape' => 'HonorCooldown', ], 'MetricValue' => [ 'shape' => 'MetricScale', ], 'BreachThreshold' => [ 'shape' => 'MetricScale', ], ], ], 'ExitStandbyAnswer' => [ 'type' => 'structure', 'members' => [ 'Activities' => [ 'shape' => 'Activities', ], ], ], 'ExitStandbyQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'FailedScheduledUpdateGroupActionRequest' => [ 'type' => 'structure', 'required' => [ 'ScheduledActionName', ], 'members' => [ 'ScheduledActionName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ErrorCode' => [ 'shape' => 'XmlStringMaxLen64', ], 'ErrorMessage' => [ 'shape' => 'XmlString', ], ], ], 'FailedScheduledUpdateGroupActionRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedScheduledUpdateGroupActionRequest', ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'XmlString', ], 'Values' => [ 'shape' => 'Values', ], ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'ForceDelete' => [ 'type' => 'boolean', ], 'GetPredictiveScalingForecastAnswer' => [ 'type' => 'structure', 'required' => [ 'LoadForecast', 'CapacityForecast', 'UpdateTime', ], 'members' => [ 'LoadForecast' => [ 'shape' => 'LoadForecasts', ], 'CapacityForecast' => [ 'shape' => 'CapacityForecast', ], 'UpdateTime' => [ 'shape' => 'TimestampType', ], ], ], 'GetPredictiveScalingForecastType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'PolicyName', 'StartTime', 'EndTime', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyName' => [ 'shape' => 'XmlStringMaxLen255', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], ], ], 'GlobalTimeout' => [ 'type' => 'integer', ], 'HealthCheckGracePeriod' => [ 'type' => 'integer', ], 'HeartbeatTimeout' => [ 'type' => 'integer', ], 'HonorCooldown' => [ 'type' => 'boolean', ], 'IncludeDeletedGroups' => [ 'type' => 'boolean', ], 'Instance' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AvailabilityZone', 'LifecycleState', 'HealthStatus', 'ProtectedFromScaleIn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'InstanceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'AvailabilityZone' => [ 'shape' => 'XmlStringMaxLen255', ], 'LifecycleState' => [ 'shape' => 'LifecycleState', ], 'HealthStatus' => [ 'shape' => 'XmlStringMaxLen32', ], 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'ProtectedFromScaleIn' => [ 'shape' => 'InstanceProtected', ], 'WeightedCapacity' => [ 'shape' => 'XmlStringMaxLen32', ], ], ], 'InstanceGeneration' => [ 'type' => 'string', 'enum' => [ 'current', 'previous', ], ], 'InstanceGenerations' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceGeneration', ], ], 'InstanceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen19', ], ], 'InstanceMaintenancePolicy' => [ 'type' => 'structure', 'members' => [ 'MinHealthyPercentage' => [ 'shape' => 'IntPercentResettable', ], 'MaxHealthyPercentage' => [ 'shape' => 'IntPercent100To200Resettable', ], ], ], 'InstanceMetadataEndpointState' => [ 'type' => 'string', 'enum' => [ 'disabled', 'enabled', ], ], 'InstanceMetadataHttpPutResponseHopLimit' => [ 'type' => 'integer', 'max' => 64, 'min' => 1, ], 'InstanceMetadataHttpTokensState' => [ 'type' => 'string', 'enum' => [ 'optional', 'required', ], ], 'InstanceMetadataOptions' => [ 'type' => 'structure', 'members' => [ 'HttpTokens' => [ 'shape' => 'InstanceMetadataHttpTokensState', ], 'HttpPutResponseHopLimit' => [ 'shape' => 'InstanceMetadataHttpPutResponseHopLimit', ], 'HttpEndpoint' => [ 'shape' => 'InstanceMetadataEndpointState', ], ], ], 'InstanceMonitoring' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'MonitoringEnabled', ], ], ], 'InstanceProtected' => [ 'type' => 'boolean', ], 'InstanceRefresh' => [ 'type' => 'structure', 'members' => [ 'InstanceRefreshId' => [ 'shape' => 'XmlStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Status' => [ 'shape' => 'InstanceRefreshStatus', ], 'StatusReason' => [ 'shape' => 'XmlStringMaxLen1023', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'PercentageComplete' => [ 'shape' => 'IntPercent', ], 'InstancesToUpdate' => [ 'shape' => 'InstancesToUpdate', ], 'ProgressDetails' => [ 'shape' => 'InstanceRefreshProgressDetails', ], 'Preferences' => [ 'shape' => 'RefreshPreferences', ], 'DesiredConfiguration' => [ 'shape' => 'DesiredConfiguration', ], 'RollbackDetails' => [ 'shape' => 'RollbackDetails', ], ], ], 'InstanceRefreshIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'InstanceRefreshInProgressFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'InstanceRefreshInProgress', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InstanceRefreshLivePoolProgress' => [ 'type' => 'structure', 'members' => [ 'PercentageComplete' => [ 'shape' => 'IntPercent', ], 'InstancesToUpdate' => [ 'shape' => 'InstancesToUpdate', ], ], ], 'InstanceRefreshProgressDetails' => [ 'type' => 'structure', 'members' => [ 'LivePoolProgress' => [ 'shape' => 'InstanceRefreshLivePoolProgress', ], 'WarmPoolProgress' => [ 'shape' => 'InstanceRefreshWarmPoolProgress', ], ], ], 'InstanceRefreshStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Successful', 'Failed', 'Cancelling', 'Cancelled', 'RollbackInProgress', 'RollbackFailed', 'RollbackSuccessful', ], ], 'InstanceRefreshWarmPoolProgress' => [ 'type' => 'structure', 'members' => [ 'PercentageComplete' => [ 'shape' => 'IntPercent', ], 'InstancesToUpdate' => [ 'shape' => 'InstancesToUpdate', ], ], ], 'InstanceRefreshes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceRefresh', ], ], 'InstanceRequirements' => [ 'type' => 'structure', 'required' => [ 'VCpuCount', 'MemoryMiB', ], 'members' => [ 'VCpuCount' => [ 'shape' => 'VCpuCountRequest', ], 'MemoryMiB' => [ 'shape' => 'MemoryMiBRequest', ], 'CpuManufacturers' => [ 'shape' => 'CpuManufacturers', ], 'MemoryGiBPerVCpu' => [ 'shape' => 'MemoryGiBPerVCpuRequest', ], 'ExcludedInstanceTypes' => [ 'shape' => 'ExcludedInstanceTypes', ], 'InstanceGenerations' => [ 'shape' => 'InstanceGenerations', ], 'SpotMaxPricePercentageOverLowestPrice' => [ 'shape' => 'NullablePositiveInteger', ], 'MaxSpotPriceAsPercentageOfOptimalOnDemandPrice' => [ 'shape' => 'NullablePositiveInteger', ], 'OnDemandMaxPricePercentageOverLowestPrice' => [ 'shape' => 'NullablePositiveInteger', ], 'BareMetal' => [ 'shape' => 'BareMetal', ], 'BurstablePerformance' => [ 'shape' => 'BurstablePerformance', ], 'RequireHibernateSupport' => [ 'shape' => 'NullableBoolean', ], 'NetworkInterfaceCount' => [ 'shape' => 'NetworkInterfaceCountRequest', ], 'LocalStorage' => [ 'shape' => 'LocalStorage', ], 'LocalStorageTypes' => [ 'shape' => 'LocalStorageTypes', ], 'TotalLocalStorageGB' => [ 'shape' => 'TotalLocalStorageGBRequest', ], 'BaselineEbsBandwidthMbps' => [ 'shape' => 'BaselineEbsBandwidthMbpsRequest', ], 'AcceleratorTypes' => [ 'shape' => 'AcceleratorTypes', ], 'AcceleratorCount' => [ 'shape' => 'AcceleratorCountRequest', ], 'AcceleratorManufacturers' => [ 'shape' => 'AcceleratorManufacturers', ], 'AcceleratorNames' => [ 'shape' => 'AcceleratorNames', ], 'AcceleratorTotalMemoryMiB' => [ 'shape' => 'AcceleratorTotalMemoryMiBRequest', ], 'NetworkBandwidthGbps' => [ 'shape' => 'NetworkBandwidthGbpsRequest', ], 'AllowedInstanceTypes' => [ 'shape' => 'AllowedInstanceTypes', ], ], ], 'InstanceReusePolicy' => [ 'type' => 'structure', 'members' => [ 'ReuseOnScaleIn' => [ 'shape' => 'ReuseOnScaleIn', ], ], ], 'Instances' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstancesDistribution' => [ 'type' => 'structure', 'members' => [ 'OnDemandAllocationStrategy' => [ 'shape' => 'XmlString', ], 'OnDemandBaseCapacity' => [ 'shape' => 'OnDemandBaseCapacity', ], 'OnDemandPercentageAboveBaseCapacity' => [ 'shape' => 'OnDemandPercentageAboveBaseCapacity', ], 'SpotAllocationStrategy' => [ 'shape' => 'XmlString', ], 'SpotInstancePools' => [ 'shape' => 'SpotInstancePools', ], 'SpotMaxPrice' => [ 'shape' => 'MixedInstanceSpotPrice', ], ], ], 'InstancesToUpdate' => [ 'type' => 'integer', 'min' => 0, ], 'IntPercent' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'IntPercent100To200' => [ 'type' => 'integer', 'max' => 200, 'min' => 100, ], 'IntPercent100To200Resettable' => [ 'type' => 'integer', 'max' => 200, 'min' => -1, ], 'IntPercentResettable' => [ 'type' => 'integer', 'max' => 100, 'min' => -1, ], 'InvalidNextToken' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'InvalidNextToken', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IrreversibleInstanceRefreshFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'IrreversibleInstanceRefresh', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LaunchConfiguration' => [ 'type' => 'structure', 'required' => [ 'LaunchConfigurationName', 'ImageId', 'InstanceType', 'CreatedTime', ], 'members' => [ 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchConfigurationARN' => [ 'shape' => 'ResourceName', ], 'ImageId' => [ 'shape' => 'XmlStringMaxLen255', ], 'KeyName' => [ 'shape' => 'XmlStringMaxLen255', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'ClassicLinkVPCId' => [ 'shape' => 'XmlStringMaxLen255', ], 'ClassicLinkVPCSecurityGroups' => [ 'shape' => 'ClassicLinkVPCSecurityGroups', ], 'UserData' => [ 'shape' => 'XmlStringUserData', ], 'InstanceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'KernelId' => [ 'shape' => 'XmlStringMaxLen255', ], 'RamdiskId' => [ 'shape' => 'XmlStringMaxLen255', ], 'BlockDeviceMappings' => [ 'shape' => 'BlockDeviceMappings', ], 'InstanceMonitoring' => [ 'shape' => 'InstanceMonitoring', ], 'SpotPrice' => [ 'shape' => 'SpotPrice', ], 'IamInstanceProfile' => [ 'shape' => 'XmlStringMaxLen1600', ], 'CreatedTime' => [ 'shape' => 'TimestampType', ], 'EbsOptimized' => [ 'shape' => 'EbsOptimized', ], 'AssociatePublicIpAddress' => [ 'shape' => 'AssociatePublicIpAddress', ], 'PlacementTenancy' => [ 'shape' => 'XmlStringMaxLen64', ], 'MetadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], ], ], 'LaunchConfigurationNameType' => [ 'type' => 'structure', 'required' => [ 'LaunchConfigurationName', ], 'members' => [ 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'LaunchConfigurationNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'LaunchConfigurationNamesType' => [ 'type' => 'structure', 'members' => [ 'LaunchConfigurationNames' => [ 'shape' => 'LaunchConfigurationNames', ], 'NextToken' => [ 'shape' => 'XmlString', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'LaunchConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchConfiguration', ], ], 'LaunchConfigurationsType' => [ 'type' => 'structure', 'required' => [ 'LaunchConfigurations', ], 'members' => [ 'LaunchConfigurations' => [ 'shape' => 'LaunchConfigurations', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'LaunchTemplate' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateSpecification' => [ 'shape' => 'LaunchTemplateSpecification', ], 'Overrides' => [ 'shape' => 'Overrides', ], ], ], 'LaunchTemplateName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '[a-zA-Z0-9\\(\\)\\.\\-/_]+', ], 'LaunchTemplateOverrides' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'XmlStringMaxLen255', ], 'WeightedCapacity' => [ 'shape' => 'XmlStringMaxLen32', ], 'LaunchTemplateSpecification' => [ 'shape' => 'LaunchTemplateSpecification', ], 'InstanceRequirements' => [ 'shape' => 'InstanceRequirements', ], ], ], 'LaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateId' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplateName' => [ 'shape' => 'LaunchTemplateName', ], 'Version' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'LifecycleActionResult' => [ 'type' => 'string', ], 'LifecycleActionToken' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'LifecycleHook' => [ 'type' => 'structure', 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LifecycleTransition' => [ 'shape' => 'LifecycleTransition', ], 'NotificationTargetARN' => [ 'shape' => 'NotificationTargetResourceName', ], 'RoleARN' => [ 'shape' => 'XmlStringMaxLen255', ], 'NotificationMetadata' => [ 'shape' => 'AnyPrintableAsciiStringMaxLen4000', ], 'HeartbeatTimeout' => [ 'shape' => 'HeartbeatTimeout', ], 'GlobalTimeout' => [ 'shape' => 'GlobalTimeout', ], 'DefaultResult' => [ 'shape' => 'LifecycleActionResult', ], ], ], 'LifecycleHookNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AsciiStringMaxLen255', ], 'max' => 50, ], 'LifecycleHookSpecification' => [ 'type' => 'structure', 'required' => [ 'LifecycleHookName', 'LifecycleTransition', ], 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'LifecycleTransition' => [ 'shape' => 'LifecycleTransition', ], 'NotificationMetadata' => [ 'shape' => 'AnyPrintableAsciiStringMaxLen4000', ], 'HeartbeatTimeout' => [ 'shape' => 'HeartbeatTimeout', ], 'DefaultResult' => [ 'shape' => 'LifecycleActionResult', ], 'NotificationTargetARN' => [ 'shape' => 'NotificationTargetResourceName', ], 'RoleARN' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'LifecycleHookSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleHookSpecification', ], ], 'LifecycleHooks' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleHook', ], ], 'LifecycleState' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Pending:Wait', 'Pending:Proceed', 'Quarantined', 'InService', 'Terminating', 'Terminating:Wait', 'Terminating:Proceed', 'Terminated', 'Detaching', 'Detached', 'EnteringStandby', 'Standby', 'Warmed:Pending', 'Warmed:Pending:Wait', 'Warmed:Pending:Proceed', 'Warmed:Terminating', 'Warmed:Terminating:Wait', 'Warmed:Terminating:Proceed', 'Warmed:Terminated', 'Warmed:Stopped', 'Warmed:Running', 'Warmed:Hibernated', ], ], 'LifecycleTransition' => [ 'type' => 'string', ], 'LimitExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'LimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LoadBalancerNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'LoadBalancerState' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerName' => [ 'shape' => 'XmlStringMaxLen255', ], 'State' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'LoadBalancerStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerState', ], ], 'LoadBalancerTargetGroupState' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerTargetGroupARN' => [ 'shape' => 'XmlStringMaxLen511', ], 'State' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'LoadBalancerTargetGroupStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTargetGroupState', ], ], 'LoadForecast' => [ 'type' => 'structure', 'required' => [ 'Timestamps', 'Values', 'MetricSpecification', ], 'members' => [ 'Timestamps' => [ 'shape' => 'PredictiveScalingForecastTimestamps', ], 'Values' => [ 'shape' => 'PredictiveScalingForecastValues', ], 'MetricSpecification' => [ 'shape' => 'PredictiveScalingMetricSpecification', ], ], ], 'LoadForecasts' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadForecast', ], ], 'LocalStorage' => [ 'type' => 'string', 'enum' => [ 'included', 'excluded', 'required', ], ], 'LocalStorageType' => [ 'type' => 'string', 'enum' => [ 'hdd', 'ssd', ], ], 'LocalStorageTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalStorageType', ], ], 'MaxGroupPreparedCapacity' => [ 'type' => 'integer', 'min' => -1, ], 'MaxInstanceLifetime' => [ 'type' => 'integer', ], 'MaxNumberOfAutoScalingGroups' => [ 'type' => 'integer', ], 'MaxNumberOfLaunchConfigurations' => [ 'type' => 'integer', ], 'MaxRecords' => [ 'type' => 'integer', ], 'MemoryGiBPerVCpuRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveDouble', ], 'Max' => [ 'shape' => 'NullablePositiveDouble', ], ], ], 'MemoryMiBRequest' => [ 'type' => 'structure', 'required' => [ 'Min', ], 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'Metric' => [ 'type' => 'structure', 'required' => [ 'Namespace', 'MetricName', ], 'members' => [ 'Namespace' => [ 'shape' => 'MetricNamespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'MetricDimensions', ], ], ], 'MetricCollectionType' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'MetricCollectionTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricCollectionType', ], ], 'MetricDataQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataQuery', ], ], 'MetricDataQuery' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'XmlStringMaxLen255', ], 'Expression' => [ 'shape' => 'XmlStringMaxLen1023', ], 'MetricStat' => [ 'shape' => 'MetricStat', ], 'Label' => [ 'shape' => 'XmlStringMetricLabel', ], 'ReturnData' => [ 'shape' => 'ReturnData', ], ], ], 'MetricDimension' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'MetricDimensionName', ], 'Value' => [ 'shape' => 'MetricDimensionValue', ], ], ], 'MetricDimensionName' => [ 'type' => 'string', ], 'MetricDimensionValue' => [ 'type' => 'string', ], 'MetricDimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDimension', ], ], 'MetricGranularityType' => [ 'type' => 'structure', 'members' => [ 'Granularity' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'MetricGranularityTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricGranularityType', ], ], 'MetricName' => [ 'type' => 'string', ], 'MetricNamespace' => [ 'type' => 'string', ], 'MetricScale' => [ 'type' => 'double', ], 'MetricStat' => [ 'type' => 'structure', 'required' => [ 'Metric', 'Stat', ], 'members' => [ 'Metric' => [ 'shape' => 'Metric', ], 'Stat' => [ 'shape' => 'XmlStringMetricStat', ], 'Unit' => [ 'shape' => 'MetricUnit', ], ], ], 'MetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Average', 'Minimum', 'Maximum', 'SampleCount', 'Sum', ], ], 'MetricType' => [ 'type' => 'string', 'enum' => [ 'ASGAverageCPUUtilization', 'ASGAverageNetworkIn', 'ASGAverageNetworkOut', 'ALBRequestCountPerTarget', ], ], 'MetricUnit' => [ 'type' => 'string', ], 'Metrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'MinAdjustmentMagnitude' => [ 'type' => 'integer', ], 'MinAdjustmentStep' => [ 'type' => 'integer', 'deprecated' => true, ], 'MixedInstanceSpotPrice' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'MixedInstancesPolicy' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplate' => [ 'shape' => 'LaunchTemplate', ], 'InstancesDistribution' => [ 'shape' => 'InstancesDistribution', ], ], ], 'MonitoringEnabled' => [ 'type' => 'boolean', ], 'NetworkBandwidthGbpsRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveDouble', ], 'Max' => [ 'shape' => 'NullablePositiveDouble', ], ], ], 'NetworkInterfaceCountRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'NoDevice' => [ 'type' => 'boolean', ], 'NonZeroIntPercent' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'NotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TopicARN' => [ 'shape' => 'XmlStringMaxLen255', ], 'NotificationType' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'NotificationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationConfiguration', ], ], 'NotificationTargetResourceName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'NullablePositiveDouble' => [ 'type' => 'double', 'min' => 0, ], 'NullablePositiveInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NumberOfAutoScalingGroups' => [ 'type' => 'integer', ], 'NumberOfLaunchConfigurations' => [ 'type' => 'integer', ], 'OnDemandBaseCapacity' => [ 'type' => 'integer', ], 'OnDemandPercentageAboveBaseCapacity' => [ 'type' => 'integer', ], 'Overrides' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchTemplateOverrides', ], ], 'PoliciesType' => [ 'type' => 'structure', 'members' => [ 'ScalingPolicies' => [ 'shape' => 'ScalingPolicies', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'PolicyARNType' => [ 'type' => 'structure', 'members' => [ 'PolicyARN' => [ 'shape' => 'ResourceName', ], 'Alarms' => [ 'shape' => 'Alarms', ], ], ], 'PolicyIncrement' => [ 'type' => 'integer', ], 'PolicyNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceName', ], ], 'PolicyTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen64', ], ], 'PredefinedLoadMetricType' => [ 'type' => 'string', 'enum' => [ 'ASGTotalCPUUtilization', 'ASGTotalNetworkIn', 'ASGTotalNetworkOut', 'ALBTargetGroupRequestCount', ], ], 'PredefinedMetricPairType' => [ 'type' => 'string', 'enum' => [ 'ASGCPUUtilization', 'ASGNetworkIn', 'ASGNetworkOut', 'ALBRequestCount', ], ], 'PredefinedMetricSpecification' => [ 'type' => 'structure', 'required' => [ 'PredefinedMetricType', ], 'members' => [ 'PredefinedMetricType' => [ 'shape' => 'MetricType', ], 'ResourceLabel' => [ 'shape' => 'XmlStringMaxLen1023', ], ], ], 'PredefinedScalingMetricType' => [ 'type' => 'string', 'enum' => [ 'ASGAverageCPUUtilization', 'ASGAverageNetworkIn', 'ASGAverageNetworkOut', 'ALBRequestCountPerTarget', ], ], 'PredictiveScalingConfiguration' => [ 'type' => 'structure', 'required' => [ 'MetricSpecifications', ], 'members' => [ 'MetricSpecifications' => [ 'shape' => 'PredictiveScalingMetricSpecifications', ], 'Mode' => [ 'shape' => 'PredictiveScalingMode', ], 'SchedulingBufferTime' => [ 'shape' => 'PredictiveScalingSchedulingBufferTime', ], 'MaxCapacityBreachBehavior' => [ 'shape' => 'PredictiveScalingMaxCapacityBreachBehavior', ], 'MaxCapacityBuffer' => [ 'shape' => 'PredictiveScalingMaxCapacityBuffer', ], ], ], 'PredictiveScalingCustomizedCapacityMetric' => [ 'type' => 'structure', 'required' => [ 'MetricDataQueries', ], 'members' => [ 'MetricDataQueries' => [ 'shape' => 'MetricDataQueries', ], ], ], 'PredictiveScalingCustomizedLoadMetric' => [ 'type' => 'structure', 'required' => [ 'MetricDataQueries', ], 'members' => [ 'MetricDataQueries' => [ 'shape' => 'MetricDataQueries', ], ], ], 'PredictiveScalingCustomizedScalingMetric' => [ 'type' => 'structure', 'required' => [ 'MetricDataQueries', ], 'members' => [ 'MetricDataQueries' => [ 'shape' => 'MetricDataQueries', ], ], ], 'PredictiveScalingForecastTimestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimestampType', ], ], 'PredictiveScalingForecastValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricScale', ], ], 'PredictiveScalingMaxCapacityBreachBehavior' => [ 'type' => 'string', 'enum' => [ 'HonorMaxCapacity', 'IncreaseMaxCapacity', ], ], 'PredictiveScalingMaxCapacityBuffer' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'PredictiveScalingMetricSpecification' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'TargetValue' => [ 'shape' => 'MetricScale', ], 'PredefinedMetricPairSpecification' => [ 'shape' => 'PredictiveScalingPredefinedMetricPair', ], 'PredefinedScalingMetricSpecification' => [ 'shape' => 'PredictiveScalingPredefinedScalingMetric', ], 'PredefinedLoadMetricSpecification' => [ 'shape' => 'PredictiveScalingPredefinedLoadMetric', ], 'CustomizedScalingMetricSpecification' => [ 'shape' => 'PredictiveScalingCustomizedScalingMetric', ], 'CustomizedLoadMetricSpecification' => [ 'shape' => 'PredictiveScalingCustomizedLoadMetric', ], 'CustomizedCapacityMetricSpecification' => [ 'shape' => 'PredictiveScalingCustomizedCapacityMetric', ], ], ], 'PredictiveScalingMetricSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredictiveScalingMetricSpecification', ], ], 'PredictiveScalingMode' => [ 'type' => 'string', 'enum' => [ 'ForecastAndScale', 'ForecastOnly', ], ], 'PredictiveScalingPredefinedLoadMetric' => [ 'type' => 'structure', 'required' => [ 'PredefinedMetricType', ], 'members' => [ 'PredefinedMetricType' => [ 'shape' => 'PredefinedLoadMetricType', ], 'ResourceLabel' => [ 'shape' => 'XmlStringMaxLen1023', ], ], ], 'PredictiveScalingPredefinedMetricPair' => [ 'type' => 'structure', 'required' => [ 'PredefinedMetricType', ], 'members' => [ 'PredefinedMetricType' => [ 'shape' => 'PredefinedMetricPairType', ], 'ResourceLabel' => [ 'shape' => 'XmlStringMaxLen1023', ], ], ], 'PredictiveScalingPredefinedScalingMetric' => [ 'type' => 'structure', 'required' => [ 'PredefinedMetricType', ], 'members' => [ 'PredefinedMetricType' => [ 'shape' => 'PredefinedScalingMetricType', ], 'ResourceLabel' => [ 'shape' => 'XmlStringMaxLen1023', ], ], ], 'PredictiveScalingSchedulingBufferTime' => [ 'type' => 'integer', 'min' => 0, ], 'ProcessNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'ProcessType' => [ 'type' => 'structure', 'required' => [ 'ProcessName', ], 'members' => [ 'ProcessName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'Processes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessType', ], ], 'ProcessesType' => [ 'type' => 'structure', 'members' => [ 'Processes' => [ 'shape' => 'Processes', ], ], ], 'Progress' => [ 'type' => 'integer', ], 'PropagateAtLaunch' => [ 'type' => 'boolean', ], 'ProtectedFromScaleIn' => [ 'type' => 'boolean', ], 'PutLifecycleHookAnswer' => [ 'type' => 'structure', 'members' => [], ], 'PutLifecycleHookType' => [ 'type' => 'structure', 'required' => [ 'LifecycleHookName', 'AutoScalingGroupName', ], 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LifecycleTransition' => [ 'shape' => 'LifecycleTransition', ], 'RoleARN' => [ 'shape' => 'XmlStringMaxLen255', ], 'NotificationTargetARN' => [ 'shape' => 'NotificationTargetResourceName', ], 'NotificationMetadata' => [ 'shape' => 'AnyPrintableAsciiStringMaxLen4000', ], 'HeartbeatTimeout' => [ 'shape' => 'HeartbeatTimeout', ], 'DefaultResult' => [ 'shape' => 'LifecycleActionResult', ], ], ], 'PutNotificationConfigurationType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'TopicARN', 'NotificationTypes', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'TopicARN' => [ 'shape' => 'XmlStringMaxLen255', ], 'NotificationTypes' => [ 'shape' => 'AutoScalingNotificationTypes', ], ], ], 'PutScalingPolicyType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'PolicyName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyType' => [ 'shape' => 'XmlStringMaxLen64', ], 'AdjustmentType' => [ 'shape' => 'XmlStringMaxLen255', ], 'MinAdjustmentStep' => [ 'shape' => 'MinAdjustmentStep', ], 'MinAdjustmentMagnitude' => [ 'shape' => 'MinAdjustmentMagnitude', ], 'ScalingAdjustment' => [ 'shape' => 'PolicyIncrement', ], 'Cooldown' => [ 'shape' => 'Cooldown', ], 'MetricAggregationType' => [ 'shape' => 'XmlStringMaxLen32', ], 'StepAdjustments' => [ 'shape' => 'StepAdjustments', ], 'EstimatedInstanceWarmup' => [ 'shape' => 'EstimatedInstanceWarmup', ], 'TargetTrackingConfiguration' => [ 'shape' => 'TargetTrackingConfiguration', ], 'Enabled' => [ 'shape' => 'ScalingPolicyEnabled', ], 'PredictiveScalingConfiguration' => [ 'shape' => 'PredictiveScalingConfiguration', ], ], ], 'PutScheduledUpdateGroupActionType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'ScheduledActionName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Time' => [ 'shape' => 'TimestampType', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'Recurrence' => [ 'shape' => 'XmlStringMaxLen255', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'TimeZone' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'PutWarmPoolAnswer' => [ 'type' => 'structure', 'members' => [], ], 'PutWarmPoolType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'MaxGroupPreparedCapacity' => [ 'shape' => 'MaxGroupPreparedCapacity', ], 'MinSize' => [ 'shape' => 'WarmPoolMinSize', ], 'PoolState' => [ 'shape' => 'WarmPoolState', ], 'InstanceReusePolicy' => [ 'shape' => 'InstanceReusePolicy', ], ], ], 'RecordLifecycleActionHeartbeatAnswer' => [ 'type' => 'structure', 'members' => [], ], 'RecordLifecycleActionHeartbeatType' => [ 'type' => 'structure', 'required' => [ 'LifecycleHookName', 'AutoScalingGroupName', ], 'members' => [ 'LifecycleHookName' => [ 'shape' => 'AsciiStringMaxLen255', ], 'AutoScalingGroupName' => [ 'shape' => 'ResourceName', ], 'LifecycleActionToken' => [ 'shape' => 'LifecycleActionToken', ], 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], ], ], 'RefreshInstanceWarmup' => [ 'type' => 'integer', 'min' => 0, ], 'RefreshPreferences' => [ 'type' => 'structure', 'members' => [ 'MinHealthyPercentage' => [ 'shape' => 'IntPercent', ], 'InstanceWarmup' => [ 'shape' => 'RefreshInstanceWarmup', ], 'CheckpointPercentages' => [ 'shape' => 'CheckpointPercentages', ], 'CheckpointDelay' => [ 'shape' => 'CheckpointDelay', ], 'SkipMatching' => [ 'shape' => 'SkipMatching', ], 'AutoRollback' => [ 'shape' => 'AutoRollback', ], 'ScaleInProtectedInstances' => [ 'shape' => 'ScaleInProtectedInstances', ], 'StandbyInstances' => [ 'shape' => 'StandbyInstances', ], 'AlarmSpecification' => [ 'shape' => 'AlarmSpecification', ], 'MaxHealthyPercentage' => [ 'shape' => 'IntPercent100To200', ], ], ], 'RefreshStrategy' => [ 'type' => 'string', 'enum' => [ 'Rolling', ], ], 'ResourceContentionFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'ResourceContention', 'httpStatusCode' => 500, 'senderFault' => true, ], 'exception' => true, ], 'ResourceInUseFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'ResourceInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ResourceName' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'ReturnData' => [ 'type' => 'boolean', ], 'ReuseOnScaleIn' => [ 'type' => 'boolean', ], 'RollbackDetails' => [ 'type' => 'structure', 'members' => [ 'RollbackReason' => [ 'shape' => 'XmlStringMaxLen1023', ], 'RollbackStartTime' => [ 'shape' => 'TimestampType', ], 'PercentageCompleteOnRollback' => [ 'shape' => 'IntPercent', ], 'InstancesToUpdateOnRollback' => [ 'shape' => 'InstancesToUpdate', ], 'ProgressDetailsOnRollback' => [ 'shape' => 'InstanceRefreshProgressDetails', ], ], ], 'RollbackInstanceRefreshAnswer' => [ 'type' => 'structure', 'members' => [ 'InstanceRefreshId' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'RollbackInstanceRefreshType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'ScaleInProtectedInstances' => [ 'type' => 'string', 'enum' => [ 'Refresh', 'Ignore', 'Wait', ], ], 'ScalingActivityInProgressFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'ScalingActivityInProgress', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ScalingActivityStatusCode' => [ 'type' => 'string', 'enum' => [ 'PendingSpotBidPlacement', 'WaitingForSpotInstanceRequestId', 'WaitingForSpotInstanceId', 'WaitingForInstanceId', 'PreInService', 'InProgress', 'WaitingForELBConnectionDraining', 'MidLifecycleAction', 'WaitingForInstanceWarmup', 'Successful', 'Failed', 'Cancelled', 'WaitingForConnectionDraining', ], ], 'ScalingPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScalingPolicy', ], ], 'ScalingPolicy' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyName' => [ 'shape' => 'XmlStringMaxLen255', ], 'PolicyARN' => [ 'shape' => 'ResourceName', ], 'PolicyType' => [ 'shape' => 'XmlStringMaxLen64', ], 'AdjustmentType' => [ 'shape' => 'XmlStringMaxLen255', ], 'MinAdjustmentStep' => [ 'shape' => 'MinAdjustmentStep', ], 'MinAdjustmentMagnitude' => [ 'shape' => 'MinAdjustmentMagnitude', ], 'ScalingAdjustment' => [ 'shape' => 'PolicyIncrement', ], 'Cooldown' => [ 'shape' => 'Cooldown', ], 'StepAdjustments' => [ 'shape' => 'StepAdjustments', ], 'MetricAggregationType' => [ 'shape' => 'XmlStringMaxLen32', ], 'EstimatedInstanceWarmup' => [ 'shape' => 'EstimatedInstanceWarmup', ], 'Alarms' => [ 'shape' => 'Alarms', ], 'TargetTrackingConfiguration' => [ 'shape' => 'TargetTrackingConfiguration', ], 'Enabled' => [ 'shape' => 'ScalingPolicyEnabled', ], 'PredictiveScalingConfiguration' => [ 'shape' => 'PredictiveScalingConfiguration', ], ], ], 'ScalingPolicyEnabled' => [ 'type' => 'boolean', ], 'ScalingProcessQuery' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScalingProcesses' => [ 'shape' => 'ProcessNames', ], ], ], 'ScheduledActionNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'ScheduledActionsType' => [ 'type' => 'structure', 'members' => [ 'ScheduledUpdateGroupActions' => [ 'shape' => 'ScheduledUpdateGroupActions', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'ScheduledUpdateGroupAction' => [ 'type' => 'structure', 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ScheduledActionARN' => [ 'shape' => 'ResourceName', ], 'Time' => [ 'shape' => 'TimestampType', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'Recurrence' => [ 'shape' => 'XmlStringMaxLen255', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'TimeZone' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'ScheduledUpdateGroupActionRequest' => [ 'type' => 'structure', 'required' => [ 'ScheduledActionName', ], 'members' => [ 'ScheduledActionName' => [ 'shape' => 'XmlStringMaxLen255', ], 'StartTime' => [ 'shape' => 'TimestampType', ], 'EndTime' => [ 'shape' => 'TimestampType', ], 'Recurrence' => [ 'shape' => 'XmlStringMaxLen255', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'TimeZone' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'ScheduledUpdateGroupActionRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledUpdateGroupActionRequest', ], ], 'ScheduledUpdateGroupActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledUpdateGroupAction', ], ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlString', ], ], 'ServiceLinkedRoleFailure' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'XmlStringMaxLen255', ], ], 'error' => [ 'code' => 'ServiceLinkedRoleFailure', 'httpStatusCode' => 500, 'senderFault' => true, ], 'exception' => true, ], 'SetDesiredCapacityType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', 'DesiredCapacity', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'HonorCooldown' => [ 'shape' => 'HonorCooldown', ], ], ], 'SetInstanceHealthQuery' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HealthStatus', ], 'members' => [ 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'HealthStatus' => [ 'shape' => 'XmlStringMaxLen32', ], 'ShouldRespectGracePeriod' => [ 'shape' => 'ShouldRespectGracePeriod', ], ], ], 'SetInstanceProtectionAnswer' => [ 'type' => 'structure', 'members' => [], ], 'SetInstanceProtectionQuery' => [ 'type' => 'structure', 'required' => [ 'InstanceIds', 'AutoScalingGroupName', 'ProtectedFromScaleIn', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'InstanceIds', ], 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'ProtectedFromScaleIn' => [ 'shape' => 'ProtectedFromScaleIn', ], ], ], 'ShouldDecrementDesiredCapacity' => [ 'type' => 'boolean', ], 'ShouldRespectGracePeriod' => [ 'type' => 'boolean', ], 'SkipMatching' => [ 'type' => 'boolean', ], 'SpotInstancePools' => [ 'type' => 'integer', ], 'SpotPrice' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'StandbyInstances' => [ 'type' => 'string', 'enum' => [ 'Terminate', 'Ignore', 'Wait', ], ], 'StartInstanceRefreshAnswer' => [ 'type' => 'structure', 'members' => [ 'InstanceRefreshId' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'StartInstanceRefreshType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'Strategy' => [ 'shape' => 'RefreshStrategy', ], 'DesiredConfiguration' => [ 'shape' => 'DesiredConfiguration', ], 'Preferences' => [ 'shape' => 'RefreshPreferences', ], ], ], 'StepAdjustment' => [ 'type' => 'structure', 'required' => [ 'ScalingAdjustment', ], 'members' => [ 'MetricIntervalLowerBound' => [ 'shape' => 'MetricScale', ], 'MetricIntervalUpperBound' => [ 'shape' => 'MetricScale', ], 'ScalingAdjustment' => [ 'shape' => 'PolicyIncrement', ], ], ], 'StepAdjustments' => [ 'type' => 'list', 'member' => [ 'shape' => 'StepAdjustment', ], ], 'SuspendedProcess' => [ 'type' => 'structure', 'members' => [ 'ProcessName' => [ 'shape' => 'XmlStringMaxLen255', ], 'SuspensionReason' => [ 'shape' => 'XmlStringMaxLen255', ], ], ], 'SuspendedProcesses' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuspendedProcess', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'ResourceId' => [ 'shape' => 'XmlString', ], 'ResourceType' => [ 'shape' => 'XmlString', ], 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], 'PropagateAtLaunch' => [ 'shape' => 'PropagateAtLaunch', ], ], ], 'TagDescription' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'XmlString', ], 'ResourceType' => [ 'shape' => 'XmlString', ], 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], 'PropagateAtLaunch' => [ 'shape' => 'PropagateAtLaunch', ], ], ], 'TagDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagDescription', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagsType' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagDescriptionList', ], 'NextToken' => [ 'shape' => 'XmlString', ], ], ], 'TargetGroupARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen511', ], ], 'TargetTrackingConfiguration' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'PredefinedMetricSpecification' => [ 'shape' => 'PredefinedMetricSpecification', ], 'CustomizedMetricSpecification' => [ 'shape' => 'CustomizedMetricSpecification', ], 'TargetValue' => [ 'shape' => 'MetricScale', ], 'DisableScaleIn' => [ 'shape' => 'DisableScaleIn', ], ], ], 'TargetTrackingMetricDataQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetTrackingMetricDataQuery', ], ], 'TargetTrackingMetricDataQuery' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'XmlStringMaxLen255', ], 'Expression' => [ 'shape' => 'XmlStringMaxLen2047', ], 'MetricStat' => [ 'shape' => 'TargetTrackingMetricStat', ], 'Label' => [ 'shape' => 'XmlStringMetricLabel', ], 'ReturnData' => [ 'shape' => 'ReturnData', ], ], ], 'TargetTrackingMetricStat' => [ 'type' => 'structure', 'required' => [ 'Metric', 'Stat', ], 'members' => [ 'Metric' => [ 'shape' => 'Metric', ], 'Stat' => [ 'shape' => 'XmlStringMetricStat', ], 'Unit' => [ 'shape' => 'MetricUnit', ], ], ], 'TerminateInstanceInAutoScalingGroupType' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ShouldDecrementDesiredCapacity', ], 'members' => [ 'InstanceId' => [ 'shape' => 'XmlStringMaxLen19', ], 'ShouldDecrementDesiredCapacity' => [ 'shape' => 'ShouldDecrementDesiredCapacity', ], ], ], 'TerminationPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlStringMaxLen1600', ], ], 'TimestampType' => [ 'type' => 'timestamp', ], 'TotalLocalStorageGBRequest' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveDouble', ], 'Max' => [ 'shape' => 'NullablePositiveDouble', ], ], ], 'TrafficSourceIdentifier' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'XmlStringMaxLen511', ], 'Type' => [ 'shape' => 'XmlStringMaxLen511', ], ], ], 'TrafficSourceState' => [ 'type' => 'structure', 'members' => [ 'TrafficSource' => [ 'shape' => 'XmlStringMaxLen511', 'deprecated' => true, 'deprecatedMessage' => 'TrafficSource has been replaced by Identifier', ], 'State' => [ 'shape' => 'XmlStringMaxLen255', ], 'Identifier' => [ 'shape' => 'XmlStringMaxLen511', ], 'Type' => [ 'shape' => 'XmlStringMaxLen511', ], ], ], 'TrafficSourceStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrafficSourceState', ], ], 'TrafficSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrafficSourceIdentifier', ], ], 'UpdateAutoScalingGroupType' => [ 'type' => 'structure', 'required' => [ 'AutoScalingGroupName', ], 'members' => [ 'AutoScalingGroupName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchConfigurationName' => [ 'shape' => 'XmlStringMaxLen255', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'MixedInstancesPolicy' => [ 'shape' => 'MixedInstancesPolicy', ], 'MinSize' => [ 'shape' => 'AutoScalingGroupMinSize', ], 'MaxSize' => [ 'shape' => 'AutoScalingGroupMaxSize', ], 'DesiredCapacity' => [ 'shape' => 'AutoScalingGroupDesiredCapacity', ], 'DefaultCooldown' => [ 'shape' => 'Cooldown', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'HealthCheckType' => [ 'shape' => 'XmlStringMaxLen32', ], 'HealthCheckGracePeriod' => [ 'shape' => 'HealthCheckGracePeriod', ], 'PlacementGroup' => [ 'shape' => 'XmlStringMaxLen255', ], 'VPCZoneIdentifier' => [ 'shape' => 'XmlStringMaxLen2047', ], 'TerminationPolicies' => [ 'shape' => 'TerminationPolicies', ], 'NewInstancesProtectedFromScaleIn' => [ 'shape' => 'InstanceProtected', ], 'ServiceLinkedRoleARN' => [ 'shape' => 'ResourceName', ], 'MaxInstanceLifetime' => [ 'shape' => 'MaxInstanceLifetime', ], 'CapacityRebalance' => [ 'shape' => 'CapacityRebalanceEnabled', ], 'Context' => [ 'shape' => 'Context', ], 'DesiredCapacityType' => [ 'shape' => 'XmlStringMaxLen255', ], 'DefaultInstanceWarmup' => [ 'shape' => 'DefaultInstanceWarmup', ], 'InstanceMaintenancePolicy' => [ 'shape' => 'InstanceMaintenancePolicy', ], ], ], 'VCpuCountRequest' => [ 'type' => 'structure', 'required' => [ 'Min', ], 'members' => [ 'Min' => [ 'shape' => 'NullablePositiveInteger', ], 'Max' => [ 'shape' => 'NullablePositiveInteger', ], ], ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'XmlString', ], ], 'WarmPoolConfiguration' => [ 'type' => 'structure', 'members' => [ 'MaxGroupPreparedCapacity' => [ 'shape' => 'MaxGroupPreparedCapacity', ], 'MinSize' => [ 'shape' => 'WarmPoolMinSize', ], 'PoolState' => [ 'shape' => 'WarmPoolState', ], 'Status' => [ 'shape' => 'WarmPoolStatus', ], 'InstanceReusePolicy' => [ 'shape' => 'InstanceReusePolicy', ], ], ], 'WarmPoolMinSize' => [ 'type' => 'integer', 'min' => 0, ], 'WarmPoolSize' => [ 'type' => 'integer', ], 'WarmPoolState' => [ 'type' => 'string', 'enum' => [ 'Stopped', 'Running', 'Hibernated', ], ], 'WarmPoolStatus' => [ 'type' => 'string', 'enum' => [ 'PendingDelete', ], ], 'XmlString' => [ 'type' => 'string', 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen1023' => [ 'type' => 'string', 'max' => 1023, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen1600' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen19' => [ 'type' => 'string', 'max' => 19, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen2047' => [ 'type' => 'string', 'max' => 2047, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen32' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen511' => [ 'type' => 'string', 'max' => 511, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMaxLen64' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMetricLabel' => [ 'type' => 'string', 'max' => 2047, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringMetricStat' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'XmlStringUserData' => [ 'type' => 'string', 'max' => 21847, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], ],];
