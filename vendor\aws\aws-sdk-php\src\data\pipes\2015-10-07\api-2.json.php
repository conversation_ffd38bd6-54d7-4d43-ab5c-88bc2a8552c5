<?php
// This file was auto-generated from sdk-root/src/data/pipes/2015-10-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-10-07', 'endpointPrefix' => 'pipes', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon EventBridge Pipes', 'serviceId' => 'Pipes', 'signatureVersion' => 'v4', 'signingName' => 'pipes', 'uid' => 'pipes-2015-10-07', ], 'operations' => [ 'CreatePipe' => [ 'name' => 'CreatePipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePipeRequest', ], 'output' => [ 'shape' => 'CreatePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeletePipe' => [ 'name' => 'DeletePipe', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePipeRequest', ], 'output' => [ 'shape' => 'DeletePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DescribePipe' => [ 'name' => 'DescribePipe', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePipeRequest', ], 'output' => [ 'shape' => 'DescribePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListPipes' => [ 'name' => 'ListPipes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/pipes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPipesRequest', ], 'output' => [ 'shape' => 'ListPipesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'StartPipe' => [ 'name' => 'StartPipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPipeRequest', ], 'output' => [ 'shape' => 'StartPipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopPipe' => [ 'name' => 'StopPipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopPipeRequest', ], 'output' => [ 'shape' => 'StopPipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], 'idempotent' => true, ], 'UpdatePipe' => [ 'name' => 'UpdatePipe', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePipeRequest', ], 'output' => [ 'shape' => 'UpdatePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1})?:(\\d{12})?:(.+)$', ], 'ArnOrJsonPath' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1})?:(\\d{12})?:(.+)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', ], 'ArnOrUrl' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^smk://(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9]):[0-9]{1,5}|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1})?:(\\d{12})?:(.+)$', ], 'AssignPublicIp' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AwsVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'Subnets', ], 'members' => [ 'AssignPublicIp' => [ 'shape' => 'AssignPublicIp', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'Subnets' => [ 'shape' => 'Subnets', ], ], ], 'BatchArrayProperties' => [ 'type' => 'structure', 'members' => [ 'Size' => [ 'shape' => 'BatchArraySize', ], ], ], 'BatchArraySize' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 2, ], 'BatchContainerOverrides' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'StringList', ], 'Environment' => [ 'shape' => 'BatchEnvironmentVariableList', ], 'InstanceType' => [ 'shape' => 'String', ], 'ResourceRequirements' => [ 'shape' => 'BatchResourceRequirementsList', ], ], ], 'BatchDependsOn' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchJobDependency', ], 'max' => 20, 'min' => 0, ], 'BatchEnvironmentVariable' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'BatchEnvironmentVariableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchEnvironmentVariable', ], ], 'BatchJobDependency' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'BatchJobDependencyType', ], ], ], 'BatchJobDependencyType' => [ 'type' => 'string', 'enum' => [ 'N_TO_N', 'SEQUENTIAL', ], ], 'BatchParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'BatchResourceRequirement' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'BatchResourceRequirementType', ], 'Value' => [ 'shape' => 'String', ], ], ], 'BatchResourceRequirementType' => [ 'type' => 'string', 'enum' => [ 'GPU', 'MEMORY', 'VCPU', ], ], 'BatchResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchResourceRequirement', ], ], 'BatchRetryAttempts' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'BatchRetryStrategy' => [ 'type' => 'structure', 'members' => [ 'Attempts' => [ 'shape' => 'BatchRetryAttempts', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CapacityProvider' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'CapacityProviderStrategy' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapacityProviderStrategyItem', ], 'max' => 6, 'min' => 0, ], 'CapacityProviderStrategyItem' => [ 'type' => 'structure', 'required' => [ 'capacityProvider', ], 'members' => [ 'base' => [ 'shape' => 'CapacityProviderStrategyItemBase', ], 'capacityProvider' => [ 'shape' => 'CapacityProvider', ], 'weight' => [ 'shape' => 'CapacityProviderStrategyItemWeight', ], ], ], 'CapacityProviderStrategyItemBase' => [ 'type' => 'integer', 'max' => 100000, 'min' => 0, ], 'CapacityProviderStrategyItemWeight' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'CloudwatchLogGroupArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^(^arn:aws([a-z]|\\-)*:logs:([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}):(\\d{12}):log-group:[\\.\\-_/#A-Za-z0-9]{1,512}(:\\*)?)$', ], 'CloudwatchLogsLogDestination' => [ 'type' => 'structure', 'members' => [ 'LogGroupArn' => [ 'shape' => 'CloudwatchLogGroupArn', ], ], ], 'CloudwatchLogsLogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'LogGroupArn', ], 'members' => [ 'LogGroupArn' => [ 'shape' => 'CloudwatchLogGroupArn', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreatePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', 'Source', 'Target', ], 'members' => [ 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfigurationParameters', ], 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'SourceParameters' => [ 'shape' => 'PipeSourceParameters', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], ], ], 'CreatePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], ], ], 'Database' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'DbUser' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'DeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'DeletePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeletePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeStateDescribeResponse', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], ], ], 'DescribePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DescribePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeStateDescribeResponse', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfiguration', ], 'Name' => [ 'shape' => 'PipeName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'SourceParameters' => [ 'shape' => 'PipeSourceParameters', ], 'StateReason' => [ 'shape' => 'PipeStateReason', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], ], ], 'DynamoDBStreamStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'EcsContainerOverride' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'StringList', ], 'Cpu' => [ 'shape' => 'Integer', ], 'Environment' => [ 'shape' => 'EcsEnvironmentVariableList', ], 'EnvironmentFiles' => [ 'shape' => 'EcsEnvironmentFileList', ], 'Memory' => [ 'shape' => 'Integer', ], 'MemoryReservation' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'String', ], 'ResourceRequirements' => [ 'shape' => 'EcsResourceRequirementsList', ], ], ], 'EcsContainerOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsContainerOverride', ], ], 'EcsEnvironmentFile' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'EcsEnvironmentFileType', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsEnvironmentFileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsEnvironmentFile', ], ], 'EcsEnvironmentFileType' => [ 'type' => 'string', 'enum' => [ 's3', ], ], 'EcsEnvironmentVariable' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsEnvironmentVariableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsEnvironmentVariable', ], ], 'EcsEphemeralStorage' => [ 'type' => 'structure', 'required' => [ 'sizeInGiB', ], 'members' => [ 'sizeInGiB' => [ 'shape' => 'EphemeralStorageSize', ], ], ], 'EcsInferenceAcceleratorOverride' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'String', ], 'deviceType' => [ 'shape' => 'String', ], ], ], 'EcsInferenceAcceleratorOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsInferenceAcceleratorOverride', ], ], 'EcsResourceRequirement' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'EcsResourceRequirementType', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsResourceRequirementType' => [ 'type' => 'string', 'enum' => [ 'GPU', 'InferenceAccelerator', ], ], 'EcsResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsResourceRequirement', ], ], 'EcsTaskOverride' => [ 'type' => 'structure', 'members' => [ 'ContainerOverrides' => [ 'shape' => 'EcsContainerOverrideList', ], 'Cpu' => [ 'shape' => 'String', ], 'EphemeralStorage' => [ 'shape' => 'EcsEphemeralStorage', ], 'ExecutionRoleArn' => [ 'shape' => 'ArnOrJsonPath', ], 'InferenceAcceleratorOverrides' => [ 'shape' => 'EcsInferenceAcceleratorOverrideList', ], 'Memory' => [ 'shape' => 'String', ], 'TaskRoleArn' => [ 'shape' => 'ArnOrJsonPath', ], ], ], 'EndpointString' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9]):[0-9]{1,5}$', 'sensitive' => true, ], 'EphemeralStorageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 21, ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventBridgeDetailType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'EventBridgeEndpointId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[A-Za-z0-9\\-]+[\\.][A-Za-z0-9\\-]+$', 'sensitive' => true, ], 'EventBridgeEventResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArnOrJsonPath', ], 'max' => 10, 'min' => 0, ], 'EventBridgeEventSource' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '(?=[/\\.\\-_A-Za-z0-9]+)((?!aws\\.).*)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'EventPattern' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Pattern' => [ 'shape' => 'EventPattern', ], ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 5, 'min' => 0, ], 'FirehoseArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^(^arn:aws([a-z]|\\-)*:firehose:([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}):(\\d{12}):deliverystream/[a-zA-Z0-9_.-]{1,64})$', ], 'FirehoseLogDestination' => [ 'type' => 'structure', 'members' => [ 'DeliveryStreamArn' => [ 'shape' => 'FirehoseArn', ], ], ], 'FirehoseLogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamArn', ], 'members' => [ 'DeliveryStreamArn' => [ 'shape' => 'FirehoseArn', ], ], ], 'HeaderKey' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^[!#$%&\'*+-.^_`|~0-9a-zA-Z]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', ], 'HeaderParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'HeaderKey', ], 'value' => [ 'shape' => 'HeaderValue', ], ], 'HeaderValue' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'IncludeExecutionData' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncludeExecutionDataOption', ], ], 'IncludeExecutionDataOption' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'InputTemplate' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'sensitive' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JsonPath' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*$', ], 'KafkaBootstrapServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointString', ], 'max' => 2, 'min' => 0, ], 'KafkaTopicName' => [ 'type' => 'string', 'max' => 249, 'min' => 1, 'pattern' => '^[^.]([a-zA-Z0-9\\-_.]+)$', 'sensitive' => true, ], 'KinesisPartitionKey' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'KinesisStreamStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', 'AT_TIMESTAMP', ], ], 'LaunchType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'FARGATE', 'EXTERNAL', ], ], 'LimitMax10' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'LimitMax100' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'LimitMax10000' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'LimitMin1' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ListPipesRequest' => [ 'type' => 'structure', 'members' => [ 'CurrentState' => [ 'shape' => 'PipeState', 'location' => 'querystring', 'locationName' => 'CurrentState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', 'location' => 'querystring', 'locationName' => 'DesiredState', ], 'Limit' => [ 'shape' => 'LimitMax100', 'location' => 'querystring', 'locationName' => 'Limit', ], 'NamePrefix' => [ 'shape' => 'PipeName', 'location' => 'querystring', 'locationName' => 'NamePrefix', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'SourcePrefix' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'SourcePrefix', ], 'TargetPrefix' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'TargetPrefix', ], ], ], 'ListPipesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Pipes' => [ 'shape' => 'PipeList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ERROR', 'INFO', 'TRACE', ], ], 'LogStreamName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MQBrokerAccessCredentials' => [ 'type' => 'structure', 'members' => [ 'BasicAuth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'MQBrokerQueueName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\s\\S]*$', 'sensitive' => true, ], 'MSKAccessCredentials' => [ 'type' => 'structure', 'members' => [ 'ClientCertificateTlsAuth' => [ 'shape' => 'SecretManagerArn', ], 'SaslScram512Auth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'MSKStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'MaximumBatchingWindowInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 0, ], 'MaximumRecordAgeInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 604800, 'min' => -1, ], 'MaximumRetryAttemptsESM' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => -1, ], 'MessageDeduplicationId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'MessageGroupId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'NetworkConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsvpcConfiguration' => [ 'shape' => 'AwsVpcConfiguration', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OnPartialBatchItemFailureStreams' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC_BISECT', ], ], 'OptionalArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '^$|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1})?:(\\d{12})?:(.+)$', ], 'PathParameter' => [ 'type' => 'string', 'pattern' => '^(?!\\s*$).+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'PathParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathParameter', ], ], 'Pipe' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'StateReason' => [ 'shape' => 'PipeStateReason', ], 'Target' => [ 'shape' => 'Arn', ], ], ], 'PipeArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws([a-z]|\\-)*:([a-zA-Z0-9\\-]+):([a-z]|\\d|\\-)*:([0-9]{12})?:(.+)$', ], 'PipeDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^.*$', 'sensitive' => true, ], 'PipeEnrichmentHttpParameters' => [ 'type' => 'structure', 'members' => [ 'HeaderParameters' => [ 'shape' => 'HeaderParametersMap', ], 'PathParameterValues' => [ 'shape' => 'PathParameterList', ], 'QueryStringParameters' => [ 'shape' => 'QueryStringParametersMap', ], ], ], 'PipeEnrichmentParameters' => [ 'type' => 'structure', 'members' => [ 'HttpParameters' => [ 'shape' => 'PipeEnrichmentHttpParameters', ], 'InputTemplate' => [ 'shape' => 'InputTemplate', ], ], ], 'PipeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Pipe', ], ], 'PipeLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'CloudwatchLogsLogDestination' => [ 'shape' => 'CloudwatchLogsLogDestination', ], 'FirehoseLogDestination' => [ 'shape' => 'FirehoseLogDestination', ], 'IncludeExecutionData' => [ 'shape' => 'IncludeExecutionData', ], 'Level' => [ 'shape' => 'LogLevel', ], 'S3LogDestination' => [ 'shape' => 'S3LogDestination', ], ], ], 'PipeLogConfigurationParameters' => [ 'type' => 'structure', 'required' => [ 'Level', ], 'members' => [ 'CloudwatchLogsLogDestination' => [ 'shape' => 'CloudwatchLogsLogDestinationParameters', ], 'FirehoseLogDestination' => [ 'shape' => 'FirehoseLogDestinationParameters', ], 'IncludeExecutionData' => [ 'shape' => 'IncludeExecutionData', ], 'Level' => [ 'shape' => 'LogLevel', ], 'S3LogDestination' => [ 'shape' => 'S3LogDestinationParameters', ], ], ], 'PipeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\.\\-_A-Za-z0-9]+$', ], 'PipeSourceActiveMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', 'QueueName', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'QueueName' => [ 'shape' => 'MQBrokerQueueName', ], ], ], 'PipeSourceDynamoDBStreamParameters' => [ 'type' => 'structure', 'required' => [ 'StartingPosition', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], 'StartingPosition' => [ 'shape' => 'DynamoDBStreamStartPosition', ], ], ], 'PipeSourceKinesisStreamParameters' => [ 'type' => 'structure', 'required' => [ 'StartingPosition', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], 'StartingPosition' => [ 'shape' => 'KinesisStreamStartPosition', ], 'StartingPositionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'PipeSourceManagedStreamingKafkaParameters' => [ 'type' => 'structure', 'required' => [ 'TopicName', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'ConsumerGroupID' => [ 'shape' => 'URI', ], 'Credentials' => [ 'shape' => 'MSKAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'StartingPosition' => [ 'shape' => 'MSKStartPosition', ], 'TopicName' => [ 'shape' => 'KafkaTopicName', ], ], ], 'PipeSourceParameters' => [ 'type' => 'structure', 'members' => [ 'ActiveMQBrokerParameters' => [ 'shape' => 'PipeSourceActiveMQBrokerParameters', ], 'DynamoDBStreamParameters' => [ 'shape' => 'PipeSourceDynamoDBStreamParameters', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'KinesisStreamParameters' => [ 'shape' => 'PipeSourceKinesisStreamParameters', ], 'ManagedStreamingKafkaParameters' => [ 'shape' => 'PipeSourceManagedStreamingKafkaParameters', ], 'RabbitMQBrokerParameters' => [ 'shape' => 'PipeSourceRabbitMQBrokerParameters', ], 'SelfManagedKafkaParameters' => [ 'shape' => 'PipeSourceSelfManagedKafkaParameters', ], 'SqsQueueParameters' => [ 'shape' => 'PipeSourceSqsQueueParameters', ], ], ], 'PipeSourceRabbitMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', 'QueueName', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'QueueName' => [ 'shape' => 'MQBrokerQueueName', ], 'VirtualHost' => [ 'shape' => 'URI', ], ], ], 'PipeSourceSelfManagedKafkaParameters' => [ 'type' => 'structure', 'required' => [ 'TopicName', ], 'members' => [ 'AdditionalBootstrapServers' => [ 'shape' => 'KafkaBootstrapServers', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'ConsumerGroupID' => [ 'shape' => 'URI', ], 'Credentials' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ServerRootCaCertificate' => [ 'shape' => 'SecretManagerArn', ], 'StartingPosition' => [ 'shape' => 'SelfManagedKafkaStartPosition', ], 'TopicName' => [ 'shape' => 'KafkaTopicName', ], 'Vpc' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationVpc', ], ], ], 'PipeSourceSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'PipeState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', 'CREATING', 'UPDATING', 'DELETING', 'STARTING', 'STOPPING', 'CREATE_FAILED', 'UPDATE_FAILED', 'START_FAILED', 'STOP_FAILED', 'DELETE_FAILED', 'CREATE_ROLLBACK_FAILED', 'DELETE_ROLLBACK_FAILED', 'UPDATE_ROLLBACK_FAILED', ], ], 'PipeStateReason' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^.*$', ], 'PipeTargetBatchJobParameters' => [ 'type' => 'structure', 'required' => [ 'JobDefinition', 'JobName', ], 'members' => [ 'ArrayProperties' => [ 'shape' => 'BatchArrayProperties', ], 'ContainerOverrides' => [ 'shape' => 'BatchContainerOverrides', ], 'DependsOn' => [ 'shape' => 'BatchDependsOn', ], 'JobDefinition' => [ 'shape' => 'String', ], 'JobName' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'BatchParametersMap', ], 'RetryStrategy' => [ 'shape' => 'BatchRetryStrategy', ], ], ], 'PipeTargetCloudWatchLogsParameters' => [ 'type' => 'structure', 'members' => [ 'LogStreamName' => [ 'shape' => 'LogStreamName', ], 'Timestamp' => [ 'shape' => 'JsonPath', ], ], ], 'PipeTargetEcsTaskParameters' => [ 'type' => 'structure', 'required' => [ 'TaskDefinitionArn', ], 'members' => [ 'CapacityProviderStrategy' => [ 'shape' => 'CapacityProviderStrategy', ], 'EnableECSManagedTags' => [ 'shape' => 'Boolean', ], 'EnableExecuteCommand' => [ 'shape' => 'Boolean', ], 'Group' => [ 'shape' => 'String', ], 'LaunchType' => [ 'shape' => 'LaunchType', ], 'NetworkConfiguration' => [ 'shape' => 'NetworkConfiguration', ], 'Overrides' => [ 'shape' => 'EcsTaskOverride', ], 'PlacementConstraints' => [ 'shape' => 'PlacementConstraints', ], 'PlacementStrategy' => [ 'shape' => 'PlacementStrategies', ], 'PlatformVersion' => [ 'shape' => 'String', ], 'PropagateTags' => [ 'shape' => 'PropagateTags', ], 'ReferenceId' => [ 'shape' => 'ReferenceId', ], 'Tags' => [ 'shape' => 'TagList', ], 'TaskCount' => [ 'shape' => 'LimitMin1', ], 'TaskDefinitionArn' => [ 'shape' => 'ArnOrJsonPath', ], ], ], 'PipeTargetEventBridgeEventBusParameters' => [ 'type' => 'structure', 'members' => [ 'DetailType' => [ 'shape' => 'EventBridgeDetailType', ], 'EndpointId' => [ 'shape' => 'EventBridgeEndpointId', ], 'Resources' => [ 'shape' => 'EventBridgeEventResourceList', ], 'Source' => [ 'shape' => 'EventBridgeEventSource', ], 'Time' => [ 'shape' => 'JsonPath', ], ], ], 'PipeTargetHttpParameters' => [ 'type' => 'structure', 'members' => [ 'HeaderParameters' => [ 'shape' => 'HeaderParametersMap', ], 'PathParameterValues' => [ 'shape' => 'PathParameterList', ], 'QueryStringParameters' => [ 'shape' => 'QueryStringParametersMap', ], ], ], 'PipeTargetInvocationType' => [ 'type' => 'string', 'enum' => [ 'REQUEST_RESPONSE', 'FIRE_AND_FORGET', ], ], 'PipeTargetKinesisStreamParameters' => [ 'type' => 'structure', 'required' => [ 'PartitionKey', ], 'members' => [ 'PartitionKey' => [ 'shape' => 'KinesisPartitionKey', ], ], ], 'PipeTargetLambdaFunctionParameters' => [ 'type' => 'structure', 'members' => [ 'InvocationType' => [ 'shape' => 'PipeTargetInvocationType', ], ], ], 'PipeTargetParameters' => [ 'type' => 'structure', 'members' => [ 'BatchJobParameters' => [ 'shape' => 'PipeTargetBatchJobParameters', ], 'CloudWatchLogsParameters' => [ 'shape' => 'PipeTargetCloudWatchLogsParameters', ], 'EcsTaskParameters' => [ 'shape' => 'PipeTargetEcsTaskParameters', ], 'EventBridgeEventBusParameters' => [ 'shape' => 'PipeTargetEventBridgeEventBusParameters', ], 'HttpParameters' => [ 'shape' => 'PipeTargetHttpParameters', ], 'InputTemplate' => [ 'shape' => 'InputTemplate', ], 'KinesisStreamParameters' => [ 'shape' => 'PipeTargetKinesisStreamParameters', ], 'LambdaFunctionParameters' => [ 'shape' => 'PipeTargetLambdaFunctionParameters', ], 'RedshiftDataParameters' => [ 'shape' => 'PipeTargetRedshiftDataParameters', ], 'SageMakerPipelineParameters' => [ 'shape' => 'PipeTargetSageMakerPipelineParameters', ], 'SqsQueueParameters' => [ 'shape' => 'PipeTargetSqsQueueParameters', ], 'StepFunctionStateMachineParameters' => [ 'shape' => 'PipeTargetStateMachineParameters', ], ], ], 'PipeTargetRedshiftDataParameters' => [ 'type' => 'structure', 'required' => [ 'Database', 'Sqls', ], 'members' => [ 'Database' => [ 'shape' => 'Database', ], 'DbUser' => [ 'shape' => 'DbUser', ], 'SecretManagerArn' => [ 'shape' => 'SecretManagerArnOrJsonPath', ], 'Sqls' => [ 'shape' => 'Sqls', ], 'StatementName' => [ 'shape' => 'StatementName', ], 'WithEvent' => [ 'shape' => 'Boolean', ], ], ], 'PipeTargetSageMakerPipelineParameters' => [ 'type' => 'structure', 'members' => [ 'PipelineParameterList' => [ 'shape' => 'SageMakerPipelineParameterList', ], ], ], 'PipeTargetSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'MessageDeduplicationId' => [ 'shape' => 'MessageDeduplicationId', ], 'MessageGroupId' => [ 'shape' => 'MessageGroupId', ], ], ], 'PipeTargetStateMachineParameters' => [ 'type' => 'structure', 'members' => [ 'InvocationType' => [ 'shape' => 'PipeTargetInvocationType', ], ], ], 'PlacementConstraint' => [ 'type' => 'structure', 'members' => [ 'expression' => [ 'shape' => 'PlacementConstraintExpression', ], 'type' => [ 'shape' => 'PlacementConstraintType', ], ], ], 'PlacementConstraintExpression' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'sensitive' => true, ], 'PlacementConstraintType' => [ 'type' => 'string', 'enum' => [ 'distinctInstance', 'memberOf', ], ], 'PlacementConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementConstraint', ], 'max' => 10, 'min' => 0, ], 'PlacementStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementStrategy', ], 'max' => 5, 'min' => 0, ], 'PlacementStrategy' => [ 'type' => 'structure', 'members' => [ 'field' => [ 'shape' => 'PlacementStrategyField', ], 'type' => [ 'shape' => 'PlacementStrategyType', ], ], ], 'PlacementStrategyField' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'PlacementStrategyType' => [ 'type' => 'string', 'enum' => [ 'random', 'spread', 'binpack', ], ], 'PropagateTags' => [ 'type' => 'string', 'enum' => [ 'TASK_DEFINITION', ], ], 'QueryStringKey' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^[^\\x00-\\x1F\\x7F]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', ], 'QueryStringParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'QueryStringKey', ], 'value' => [ 'shape' => 'QueryStringValue', ], ], 'QueryStringValue' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'ReferenceId' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'RequestedPipeState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', ], ], 'RequestedPipeStateDescribeResponse' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', 'DELETED', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'RoleArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z0-9+=,.@\\-_/]+$', ], 'S3LogDestination' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => 'String', ], 'BucketOwner' => [ 'shape' => 'String', ], 'OutputFormat' => [ 'shape' => 'S3OutputFormat', ], 'Prefix' => [ 'shape' => 'String', ], ], ], 'S3LogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'BucketOwner', ], 'members' => [ 'BucketName' => [ 'shape' => 'S3LogDestinationParametersBucketNameString', ], 'BucketOwner' => [ 'shape' => 'S3LogDestinationParametersBucketOwnerString', ], 'OutputFormat' => [ 'shape' => 'S3OutputFormat', ], 'Prefix' => [ 'shape' => 'S3LogDestinationParametersPrefixString', ], ], ], 'S3LogDestinationParametersBucketNameString' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'S3LogDestinationParametersBucketOwnerString' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'S3LogDestinationParametersPrefixString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'S3OutputFormat' => [ 'type' => 'string', 'enum' => [ 'json', 'plain', 'w3c', ], ], 'SageMakerPipelineParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'SageMakerPipelineParameterName', ], 'Value' => [ 'shape' => 'SageMakerPipelineParameterValue', ], ], ], 'SageMakerPipelineParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SageMakerPipelineParameter', ], 'max' => 200, 'min' => 0, ], 'SageMakerPipelineParameterName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'SageMakerPipelineParameterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'SecretManagerArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^(^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}):(\\d{12}):secret:.+)$', ], 'SecretManagerArnOrJsonPath' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^(^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}):(\\d{12}):secret:.+)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', ], 'SecurityGroup' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^sg-[0-9a-zA-Z]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^sg-[0-9a-zA-Z]*$', 'sensitive' => true, ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 0, ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroup', ], 'max' => 5, 'min' => 0, ], 'SelfManagedKafkaAccessConfigurationCredentials' => [ 'type' => 'structure', 'members' => [ 'BasicAuth' => [ 'shape' => 'SecretManagerArn', ], 'ClientCertificateTlsAuth' => [ 'shape' => 'SecretManagerArn', ], 'SaslScram256Auth' => [ 'shape' => 'SecretManagerArn', ], 'SaslScram512Auth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'SelfManagedKafkaAccessConfigurationVpc' => [ 'type' => 'structure', 'members' => [ 'SecurityGroup' => [ 'shape' => 'SecurityGroupIds', ], 'Subnets' => [ 'shape' => 'SubnetIds', ], ], ], 'SelfManagedKafkaStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'resourceId', 'resourceType', 'serviceCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Sql' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'Sqls' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sql', ], 'max' => 40, 'min' => 1, ], 'StartPipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'StartPipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], ], ], 'StatementName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'StopPipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'StopPipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^subnet-[0-9a-z]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)$', 'sensitive' => true, ], 'SubnetId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^subnet-[0-9a-z]*$', 'sensitive' => true, ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 0, ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], 'max' => 16, 'min' => 0, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'URI' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-\\/*:_+=.@-]*$', 'sensitive' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', ], 'members' => [ 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfigurationParameters', ], 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SourceParameters' => [ 'shape' => 'UpdatePipeSourceParameters', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], ], ], 'UpdatePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'PipeName', ], ], ], 'UpdatePipeSourceActiveMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceDynamoDBStreamParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], ], ], 'UpdatePipeSourceKinesisStreamParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], ], ], 'UpdatePipeSourceManagedStreamingKafkaParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MSKAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceParameters' => [ 'type' => 'structure', 'members' => [ 'ActiveMQBrokerParameters' => [ 'shape' => 'UpdatePipeSourceActiveMQBrokerParameters', ], 'DynamoDBStreamParameters' => [ 'shape' => 'UpdatePipeSourceDynamoDBStreamParameters', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'KinesisStreamParameters' => [ 'shape' => 'UpdatePipeSourceKinesisStreamParameters', ], 'ManagedStreamingKafkaParameters' => [ 'shape' => 'UpdatePipeSourceManagedStreamingKafkaParameters', ], 'RabbitMQBrokerParameters' => [ 'shape' => 'UpdatePipeSourceRabbitMQBrokerParameters', ], 'SelfManagedKafkaParameters' => [ 'shape' => 'UpdatePipeSourceSelfManagedKafkaParameters', ], 'SqsQueueParameters' => [ 'shape' => 'UpdatePipeSourceSqsQueueParameters', ], ], ], 'UpdatePipeSourceRabbitMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceSelfManagedKafkaParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ServerRootCaCertificate' => [ 'shape' => 'SecretManagerArn', ], 'Vpc' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationVpc', ], ], ], 'UpdatePipeSourceSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], ], 'xmlNamespace' => 'http://events.amazonaws.com/doc/2015-10-07',];
