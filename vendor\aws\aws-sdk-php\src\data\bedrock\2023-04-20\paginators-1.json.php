<?php
// This file was auto-generated from sdk-root/src/data/bedrock/2023-04-20/paginators-1.json
return [ 'pagination' => [ 'ListCustomModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'modelSummaries', ], 'ListModelCustomizationJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'modelCustomizationJobSummaries', ], 'ListProvisionedModelThroughputs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'provisionedModelSummaries', ], ],];
