<?php
// This file was auto-generated from sdk-root/src/data/ivs/2020-07-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-14', 'endpointPrefix' => 'ivs', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Amazon IVS', 'serviceFullName' => 'Amazon Interactive Video Service', 'serviceId' => 'ivs', 'signatureVersion' => 'v4', 'signingName' => 'ivs', 'uid' => 'ivs-2020-07-14', ], 'operations' => [ 'BatchGetChannel' => [ 'name' => 'BatchGetChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchGetChannel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetChannelRequest', ], 'output' => [ 'shape' => 'BatchGetChannelResponse', ], ], 'BatchGetStreamKey' => [ 'name' => 'BatchGetStreamKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchGetStreamKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetStreamKeyRequest', ], 'output' => [ 'shape' => 'BatchGetStreamKeyResponse', ], ], 'BatchStartViewerSessionRevocation' => [ 'name' => 'BatchStartViewerSessionRevocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchStartViewerSessionRevocation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchStartViewerSessionRevocationRequest', ], 'output' => [ 'shape' => 'BatchStartViewerSessionRevocationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateChannel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreatePlaybackRestrictionPolicy' => [ 'name' => 'CreatePlaybackRestrictionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreatePlaybackRestrictionPolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePlaybackRestrictionPolicyRequest', ], 'output' => [ 'shape' => 'CreatePlaybackRestrictionPolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateRecordingConfiguration' => [ 'name' => 'CreateRecordingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateRecordingConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRecordingConfigurationRequest', ], 'output' => [ 'shape' => 'CreateRecordingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamKey' => [ 'name' => 'CreateStreamKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateStreamKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStreamKeyRequest', ], 'output' => [ 'shape' => 'CreateStreamKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteChannel', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePlaybackKeyPair' => [ 'name' => 'DeletePlaybackKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeletePlaybackKeyPair', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePlaybackKeyPairRequest', ], 'output' => [ 'shape' => 'DeletePlaybackKeyPairResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeletePlaybackRestrictionPolicy' => [ 'name' => 'DeletePlaybackRestrictionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeletePlaybackRestrictionPolicy', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePlaybackRestrictionPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteRecordingConfiguration' => [ 'name' => 'DeleteRecordingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteRecordingConfiguration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRecordingConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteStreamKey' => [ 'name' => 'DeleteStreamKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteStreamKey', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteStreamKeyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], ], ], 'GetChannel' => [ 'name' => 'GetChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetChannel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelRequest', ], 'output' => [ 'shape' => 'GetChannelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPlaybackKeyPair' => [ 'name' => 'GetPlaybackKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetPlaybackKeyPair', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaybackKeyPairRequest', ], 'output' => [ 'shape' => 'GetPlaybackKeyPairResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPlaybackRestrictionPolicy' => [ 'name' => 'GetPlaybackRestrictionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetPlaybackRestrictionPolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaybackRestrictionPolicyRequest', ], 'output' => [ 'shape' => 'GetPlaybackRestrictionPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], ], ], 'GetRecordingConfiguration' => [ 'name' => 'GetRecordingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetRecordingConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecordingConfigurationRequest', ], 'output' => [ 'shape' => 'GetRecordingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetStream' => [ 'name' => 'GetStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamRequest', ], 'output' => [ 'shape' => 'GetStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ChannelNotBroadcasting', ], ], ], 'GetStreamKey' => [ 'name' => 'GetStreamKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStreamKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamKeyRequest', ], 'output' => [ 'shape' => 'GetStreamKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetStreamSession' => [ 'name' => 'GetStreamSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStreamSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamSessionRequest', ], 'output' => [ 'shape' => 'GetStreamSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ImportPlaybackKeyPair' => [ 'name' => 'ImportPlaybackKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/ImportPlaybackKeyPair', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportPlaybackKeyPairRequest', ], 'output' => [ 'shape' => 'ImportPlaybackKeyPairResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListChannels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListPlaybackKeyPairs' => [ 'name' => 'ListPlaybackKeyPairs', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListPlaybackKeyPairs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaybackKeyPairsRequest', ], 'output' => [ 'shape' => 'ListPlaybackKeyPairsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPlaybackRestrictionPolicies' => [ 'name' => 'ListPlaybackRestrictionPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListPlaybackRestrictionPolicies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaybackRestrictionPoliciesRequest', ], 'output' => [ 'shape' => 'ListPlaybackRestrictionPoliciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], ], ], 'ListRecordingConfigurations' => [ 'name' => 'ListRecordingConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListRecordingConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecordingConfigurationsRequest', ], 'output' => [ 'shape' => 'ListRecordingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreamKeys' => [ 'name' => 'ListStreamKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStreamKeys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamKeysRequest', ], 'output' => [ 'shape' => 'ListStreamKeysResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreamSessions' => [ 'name' => 'ListStreamSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStreamSessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamSessionsRequest', ], 'output' => [ 'shape' => 'ListStreamSessionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreams' => [ 'name' => 'ListStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStreams', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamsRequest', ], 'output' => [ 'shape' => 'ListStreamsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutMetadata' => [ 'name' => 'PutMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutMetadata', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutMetadataRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ChannelNotBroadcasting', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartViewerSessionRevocation' => [ 'name' => 'StartViewerSessionRevocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartViewerSessionRevocation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartViewerSessionRevocationRequest', ], 'output' => [ 'shape' => 'StartViewerSessionRevocationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StopStream' => [ 'name' => 'StopStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopStream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopStreamRequest', ], 'output' => [ 'shape' => 'StopStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ChannelNotBroadcasting', ], [ 'shape' => 'StreamUnavailable', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateChannel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdatePlaybackRestrictionPolicy' => [ 'name' => 'UpdatePlaybackRestrictionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdatePlaybackRestrictionPolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePlaybackRestrictionPolicyRequest', ], 'output' => [ 'shape' => 'UpdatePlaybackRestrictionPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PendingVerification', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AudioConfiguration' => [ 'type' => 'structure', 'members' => [ 'channels' => [ 'shape' => 'Integer', ], 'codec' => [ 'shape' => 'String', ], 'sampleRate' => [ 'shape' => 'Integer', ], 'targetBitrate' => [ 'shape' => 'Integer', ], ], ], 'BatchError' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'code' => [ 'shape' => 'errorCode', ], 'message' => [ 'shape' => 'errorMessage', ], ], ], 'BatchErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchError', ], ], 'BatchGetChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arns', ], 'members' => [ 'arns' => [ 'shape' => 'ChannelArnList', ], ], ], 'BatchGetChannelResponse' => [ 'type' => 'structure', 'members' => [ 'channels' => [ 'shape' => 'Channels', ], 'errors' => [ 'shape' => 'BatchErrors', ], ], ], 'BatchGetStreamKeyRequest' => [ 'type' => 'structure', 'required' => [ 'arns', ], 'members' => [ 'arns' => [ 'shape' => 'StreamKeyArnList', ], ], ], 'BatchGetStreamKeyResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchErrors', ], 'streamKeys' => [ 'shape' => 'StreamKeys', ], ], ], 'BatchStartViewerSessionRevocationError' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'viewerId', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'code' => [ 'shape' => 'errorCode', ], 'message' => [ 'shape' => 'errorMessage', ], 'viewerId' => [ 'shape' => 'ViewerId', ], ], ], 'BatchStartViewerSessionRevocationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStartViewerSessionRevocationError', ], ], 'BatchStartViewerSessionRevocationRequest' => [ 'type' => 'structure', 'required' => [ 'viewerSessions', ], 'members' => [ 'viewerSessions' => [ 'shape' => 'BatchStartViewerSessionRevocationViewerSessionList', ], ], ], 'BatchStartViewerSessionRevocationResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchStartViewerSessionRevocationErrors', ], ], ], 'BatchStartViewerSessionRevocationViewerSession' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'viewerId', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'viewerId' => [ 'shape' => 'ViewerId', ], 'viewerSessionVersionsLessThanOrEqualTo' => [ 'shape' => 'ViewerSessionVersion', ], ], ], 'BatchStartViewerSessionRevocationViewerSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStartViewerSessionRevocationViewerSession', ], 'max' => 20, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', ], 'Channel' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', ], 'authorized' => [ 'shape' => 'IsAuthorized', ], 'ingestEndpoint' => [ 'shape' => 'IngestEndpoint', ], 'insecureIngest' => [ 'shape' => 'InsecureIngest', ], 'latencyMode' => [ 'shape' => 'ChannelLatencyMode', ], 'name' => [ 'shape' => 'ChannelName', ], 'playbackRestrictionPolicyArn' => [ 'shape' => 'ChannelPlaybackRestrictionPolicyArn', ], 'playbackUrl' => [ 'shape' => 'PlaybackURL', ], 'preset' => [ 'shape' => 'TranscodePreset', ], 'recordingConfigurationArn' => [ 'shape' => 'ChannelRecordingConfigurationArn', ], 'srt' => [ 'shape' => 'Srt', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'ChannelType', ], ], ], 'ChannelArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:channel/[a-zA-Z0-9-]+$', ], 'ChannelArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelArn', ], 'max' => 50, 'min' => 1, ], 'ChannelLatencyMode' => [ 'type' => 'string', 'enum' => [ 'NORMAL', 'LOW', ], ], 'ChannelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSummary', ], ], 'ChannelName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'ChannelNotBroadcasting' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ChannelPlaybackRestrictionPolicyArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:playback-restriction-policy/[a-zA-Z0-9-]+$', ], 'ChannelRecordingConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:recording-configuration/[a-zA-Z0-9-]+$', ], 'ChannelSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', ], 'authorized' => [ 'shape' => 'IsAuthorized', ], 'insecureIngest' => [ 'shape' => 'InsecureIngest', ], 'latencyMode' => [ 'shape' => 'ChannelLatencyMode', ], 'name' => [ 'shape' => 'ChannelName', ], 'playbackRestrictionPolicyArn' => [ 'shape' => 'ChannelPlaybackRestrictionPolicyArn', ], 'preset' => [ 'shape' => 'TranscodePreset', ], 'recordingConfigurationArn' => [ 'shape' => 'ChannelRecordingConfigurationArn', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'ChannelType', ], ], ], 'ChannelType' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'STANDARD', 'ADVANCED_SD', 'ADVANCED_HD', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateChannelRequest' => [ 'type' => 'structure', 'members' => [ 'authorized' => [ 'shape' => 'Boolean', ], 'insecureIngest' => [ 'shape' => 'Boolean', ], 'latencyMode' => [ 'shape' => 'ChannelLatencyMode', ], 'name' => [ 'shape' => 'ChannelName', ], 'playbackRestrictionPolicyArn' => [ 'shape' => 'ChannelPlaybackRestrictionPolicyArn', ], 'preset' => [ 'shape' => 'TranscodePreset', ], 'recordingConfigurationArn' => [ 'shape' => 'ChannelRecordingConfigurationArn', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'ChannelType', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'Channel', ], 'streamKey' => [ 'shape' => 'StreamKey', ], ], ], 'CreatePlaybackRestrictionPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'allowedCountries' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedCountryList', ], 'allowedOrigins' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedOriginList', ], 'enableStrictOriginEnforcement' => [ 'shape' => 'PlaybackRestrictionPolicyEnableStrictOriginEnforcement', ], 'name' => [ 'shape' => 'PlaybackRestrictionPolicyName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreatePlaybackRestrictionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'playbackRestrictionPolicy' => [ 'shape' => 'PlaybackRestrictionPolicy', ], ], ], 'CreateRecordingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationConfiguration', ], 'members' => [ 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'name' => [ 'shape' => 'RecordingConfigurationName', ], 'recordingReconnectWindowSeconds' => [ 'shape' => 'RecordingReconnectWindowSeconds', ], 'renditionConfiguration' => [ 'shape' => 'RenditionConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], 'thumbnailConfiguration' => [ 'shape' => 'ThumbnailConfiguration', ], ], ], 'CreateRecordingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'recordingConfiguration' => [ 'shape' => 'RecordingConfiguration', ], ], ], 'CreateStreamKeyRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStreamKeyResponse' => [ 'type' => 'structure', 'members' => [ 'streamKey' => [ 'shape' => 'StreamKey', ], ], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', ], ], ], 'DeletePlaybackKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PlaybackKeyPairArn', ], ], ], 'DeletePlaybackKeyPairResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaybackRestrictionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PlaybackRestrictionPolicyArn', ], ], ], 'DeleteRecordingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'RecordingConfigurationArn', ], ], ], 'DeleteStreamKeyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StreamKeyArn', ], ], ], 'DestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'S3DestinationConfiguration', ], ], ], 'GetChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', ], ], ], 'GetChannelResponse' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'Channel', ], ], ], 'GetPlaybackKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PlaybackKeyPairArn', ], ], ], 'GetPlaybackKeyPairResponse' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'PlaybackKeyPair', ], ], ], 'GetPlaybackRestrictionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PlaybackRestrictionPolicyArn', ], ], ], 'GetPlaybackRestrictionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'playbackRestrictionPolicy' => [ 'shape' => 'PlaybackRestrictionPolicy', ], ], ], 'GetRecordingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'RecordingConfigurationArn', ], ], ], 'GetRecordingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'recordingConfiguration' => [ 'shape' => 'RecordingConfiguration', ], ], ], 'GetStreamKeyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StreamKeyArn', ], ], ], 'GetStreamKeyResponse' => [ 'type' => 'structure', 'members' => [ 'streamKey' => [ 'shape' => 'StreamKey', ], ], ], 'GetStreamRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], ], ], 'GetStreamResponse' => [ 'type' => 'structure', 'members' => [ 'stream' => [ 'shape' => 'Stream', ], ], ], 'GetStreamSessionRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'streamId' => [ 'shape' => 'StreamId', ], ], ], 'GetStreamSessionResponse' => [ 'type' => 'structure', 'members' => [ 'streamSession' => [ 'shape' => 'StreamSession', ], ], ], 'ImportPlaybackKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'publicKeyMaterial', ], 'members' => [ 'name' => [ 'shape' => 'PlaybackKeyPairName', ], 'publicKeyMaterial' => [ 'shape' => 'PlaybackPublicKeyMaterial', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ImportPlaybackKeyPairResponse' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'PlaybackKeyPair', ], ], ], 'IngestConfiguration' => [ 'type' => 'structure', 'members' => [ 'audio' => [ 'shape' => 'AudioConfiguration', ], 'video' => [ 'shape' => 'VideoConfiguration', ], ], ], 'IngestEndpoint' => [ 'type' => 'string', ], 'InsecureIngest' => [ 'type' => 'boolean', ], 'Integer' => [ 'type' => 'long', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IsAuthorized' => [ 'type' => 'boolean', ], 'ListChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'filterByName' => [ 'shape' => 'ChannelName', ], 'filterByPlaybackRestrictionPolicyArn' => [ 'shape' => 'ChannelPlaybackRestrictionPolicyArn', ], 'filterByRecordingConfigurationArn' => [ 'shape' => 'ChannelRecordingConfigurationArn', ], 'maxResults' => [ 'shape' => 'MaxChannelResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'required' => [ 'channels', ], 'members' => [ 'channels' => [ 'shape' => 'ChannelList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPlaybackKeyPairsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPlaybackKeyPairResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPlaybackKeyPairsResponse' => [ 'type' => 'structure', 'required' => [ 'keyPairs', ], 'members' => [ 'keyPairs' => [ 'shape' => 'PlaybackKeyPairList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPlaybackRestrictionPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPlaybackRestrictionPolicyResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPlaybackRestrictionPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'playbackRestrictionPolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'playbackRestrictionPolicies' => [ 'shape' => 'PlaybackRestrictionPolicyList', ], ], ], 'ListRecordingConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxRecordingConfigurationResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRecordingConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'recordingConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'recordingConfigurations' => [ 'shape' => 'RecordingConfigurationList', ], ], ], 'ListStreamKeysRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'maxResults' => [ 'shape' => 'MaxStreamKeyResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStreamKeysResponse' => [ 'type' => 'structure', 'required' => [ 'streamKeys', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'streamKeys' => [ 'shape' => 'StreamKeyList', ], ], ], 'ListStreamSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'maxResults' => [ 'shape' => 'MaxStreamResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStreamSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'streamSessions', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'streamSessions' => [ 'shape' => 'StreamSessionList', ], ], ], 'ListStreamsRequest' => [ 'type' => 'structure', 'members' => [ 'filterBy' => [ 'shape' => 'StreamFilters', ], 'maxResults' => [ 'shape' => 'MaxStreamResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStreamsResponse' => [ 'type' => 'structure', 'required' => [ 'streams', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'streams' => [ 'shape' => 'StreamList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'MaxChannelResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxPlaybackKeyPairResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxPlaybackRestrictionPolicyResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxRecordingConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStreamKeyResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxStreamResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[a-zA-Z0-9+/=_-]*$', ], 'PendingVerification' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'PlaybackKeyPair' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'PlaybackKeyPairArn', ], 'fingerprint' => [ 'shape' => 'PlaybackKeyPairFingerprint', ], 'name' => [ 'shape' => 'PlaybackKeyPairName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PlaybackKeyPairArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:playback-key/[a-zA-Z0-9-]+$', ], 'PlaybackKeyPairFingerprint' => [ 'type' => 'string', ], 'PlaybackKeyPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackKeyPairSummary', ], ], 'PlaybackKeyPairName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'PlaybackKeyPairSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'PlaybackKeyPairArn', ], 'name' => [ 'shape' => 'PlaybackKeyPairName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PlaybackPublicKeyMaterial' => [ 'type' => 'string', ], 'PlaybackRestrictionPolicy' => [ 'type' => 'structure', 'required' => [ 'allowedCountries', 'allowedOrigins', 'arn', ], 'members' => [ 'allowedCountries' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedCountryList', ], 'allowedOrigins' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedOriginList', ], 'arn' => [ 'shape' => 'PlaybackRestrictionPolicyArn', ], 'enableStrictOriginEnforcement' => [ 'shape' => 'PlaybackRestrictionPolicyEnableStrictOriginEnforcement', ], 'name' => [ 'shape' => 'PlaybackRestrictionPolicyName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PlaybackRestrictionPolicyAllowedCountry' => [ 'type' => 'string', 'max' => 2, 'min' => 2, ], 'PlaybackRestrictionPolicyAllowedCountryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedCountry', ], ], 'PlaybackRestrictionPolicyAllowedOrigin' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PlaybackRestrictionPolicyAllowedOriginList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedOrigin', ], ], 'PlaybackRestrictionPolicyArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:playback-restriction-policy/[a-zA-Z0-9-]+$', ], 'PlaybackRestrictionPolicyEnableStrictOriginEnforcement' => [ 'type' => 'boolean', 'box' => true, ], 'PlaybackRestrictionPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackRestrictionPolicySummary', ], ], 'PlaybackRestrictionPolicyName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'PlaybackRestrictionPolicySummary' => [ 'type' => 'structure', 'required' => [ 'allowedCountries', 'allowedOrigins', 'arn', ], 'members' => [ 'allowedCountries' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedCountryList', ], 'allowedOrigins' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedOriginList', ], 'arn' => [ 'shape' => 'PlaybackRestrictionPolicyArn', ], 'enableStrictOriginEnforcement' => [ 'shape' => 'PlaybackRestrictionPolicyEnableStrictOriginEnforcement', ], 'name' => [ 'shape' => 'PlaybackRestrictionPolicyName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PlaybackURL' => [ 'type' => 'string', ], 'PutMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'metadata', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'metadata' => [ 'shape' => 'StreamMetadata', ], ], ], 'RecordingConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinationConfiguration', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'RecordingConfigurationArn', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'name' => [ 'shape' => 'RecordingConfigurationName', ], 'recordingReconnectWindowSeconds' => [ 'shape' => 'RecordingReconnectWindowSeconds', ], 'renditionConfiguration' => [ 'shape' => 'RenditionConfiguration', ], 'state' => [ 'shape' => 'RecordingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], 'thumbnailConfiguration' => [ 'shape' => 'ThumbnailConfiguration', ], ], ], 'RecordingConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^arn:aws:ivs:[a-z0-9-]+:[0-9]+:recording-configuration/[a-zA-Z0-9-]+$', ], 'RecordingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordingConfigurationSummary', ], ], 'RecordingConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'RecordingConfigurationState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', ], ], 'RecordingConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinationConfiguration', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'RecordingConfigurationArn', ], 'destinationConfiguration' => [ 'shape' => 'DestinationConfiguration', ], 'name' => [ 'shape' => 'RecordingConfigurationName', ], 'state' => [ 'shape' => 'RecordingConfigurationState', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'RecordingMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'INTERVAL', ], ], 'RecordingReconnectWindowSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 0, ], 'RenditionConfiguration' => [ 'type' => 'structure', 'members' => [ 'renditionSelection' => [ 'shape' => 'RenditionConfigurationRenditionSelection', ], 'renditions' => [ 'shape' => 'RenditionConfigurationRenditionList', ], ], ], 'RenditionConfigurationRendition' => [ 'type' => 'string', 'enum' => [ 'FULL_HD', 'HD', 'SD', 'LOWEST_RESOLUTION', ], ], 'RenditionConfigurationRenditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RenditionConfigurationRendition', ], ], 'RenditionConfigurationRenditionSelection' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', 'CUSTOM', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3DestinationBucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9-.]+$', ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3DestinationBucketName', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Srt' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'SrtEndpoint', ], 'passphrase' => [ 'shape' => 'SrtPassphrase', ], ], ], 'SrtEndpoint' => [ 'type' => 'string', ], 'SrtPassphrase' => [ 'type' => 'string', 'sensitive' => true, ], 'StartViewerSessionRevocationRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'viewerId', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'viewerId' => [ 'shape' => 'ViewerId', ], 'viewerSessionVersionsLessThanOrEqualTo' => [ 'shape' => 'ViewerSessionVersion', ], ], ], 'StartViewerSessionRevocationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopStreamRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], ], ], 'StopStreamResponse' => [ 'type' => 'structure', 'members' => [], ], 'Stream' => [ 'type' => 'structure', 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'health' => [ 'shape' => 'StreamHealth', ], 'playbackUrl' => [ 'shape' => 'PlaybackURL', ], 'startTime' => [ 'shape' => 'StreamStartTime', ], 'state' => [ 'shape' => 'StreamState', ], 'streamId' => [ 'shape' => 'StreamId', ], 'viewerCount' => [ 'shape' => 'StreamViewerCount', ], ], ], 'StreamEvent' => [ 'type' => 'structure', 'members' => [ 'eventTime' => [ 'shape' => 'Time', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], ], ], 'StreamEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamEvent', ], 'max' => 500, 'min' => 0, ], 'StreamFilters' => [ 'type' => 'structure', 'members' => [ 'health' => [ 'shape' => 'StreamHealth', ], ], ], 'StreamHealth' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'STARVING', 'UNKNOWN', ], ], 'StreamId' => [ 'type' => 'string', 'max' => 26, 'min' => 26, 'pattern' => '^st-[a-zA-Z0-9]+$', ], 'StreamKey' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'StreamKeyArn', ], 'channelArn' => [ 'shape' => 'ChannelArn', ], 'tags' => [ 'shape' => 'Tags', ], 'value' => [ 'shape' => 'StreamKeyValue', ], ], ], 'StreamKeyArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:[is]vs:[a-z0-9-]+:[0-9]+:stream-key/[a-zA-Z0-9-]+$', ], 'StreamKeyArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamKeyArn', ], 'max' => 50, 'min' => 1, ], 'StreamKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamKeySummary', ], ], 'StreamKeySummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'StreamKeyArn', ], 'channelArn' => [ 'shape' => 'ChannelArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StreamKeyValue' => [ 'type' => 'string', 'sensitive' => true, ], 'StreamKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamKey', ], ], 'StreamList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamSummary', ], ], 'StreamMetadata' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'StreamSession' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'Channel', ], 'endTime' => [ 'shape' => 'Time', ], 'ingestConfiguration' => [ 'shape' => 'IngestConfiguration', ], 'recordingConfiguration' => [ 'shape' => 'RecordingConfiguration', ], 'startTime' => [ 'shape' => 'Time', ], 'streamId' => [ 'shape' => 'StreamId', ], 'truncatedEvents' => [ 'shape' => 'StreamEvents', ], ], ], 'StreamSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamSessionSummary', ], ], 'StreamSessionSummary' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Time', ], 'hasErrorEvent' => [ 'shape' => 'Boolean', ], 'startTime' => [ 'shape' => 'Time', ], 'streamId' => [ 'shape' => 'StreamId', ], ], ], 'StreamStartTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'StreamState' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'OFFLINE', ], ], 'StreamSummary' => [ 'type' => 'structure', 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'health' => [ 'shape' => 'StreamHealth', ], 'startTime' => [ 'shape' => 'StreamStartTime', ], 'state' => [ 'shape' => 'StreamState', ], 'streamId' => [ 'shape' => 'StreamId', ], 'viewerCount' => [ 'shape' => 'StreamViewerCount', ], ], ], 'StreamUnavailable' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'StreamViewerCount' => [ 'type' => 'long', ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TargetIntervalSeconds' => [ 'type' => 'long', 'box' => true, 'max' => 60, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'ThumbnailConfiguration' => [ 'type' => 'structure', 'members' => [ 'recordingMode' => [ 'shape' => 'RecordingMode', ], 'resolution' => [ 'shape' => 'ThumbnailConfigurationResolution', ], 'storage' => [ 'shape' => 'ThumbnailConfigurationStorageList', ], 'targetIntervalSeconds' => [ 'shape' => 'TargetIntervalSeconds', ], ], ], 'ThumbnailConfigurationResolution' => [ 'type' => 'string', 'enum' => [ 'FULL_HD', 'HD', 'SD', 'LOWEST_RESOLUTION', ], ], 'ThumbnailConfigurationStorage' => [ 'type' => 'string', 'enum' => [ 'SEQUENTIAL', 'LATEST', ], ], 'ThumbnailConfigurationStorageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThumbnailConfigurationStorage', ], ], 'Time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TranscodePreset' => [ 'type' => 'string', 'enum' => [ 'HIGHER_BANDWIDTH_DELIVERY', 'CONSTRAINED_BANDWIDTH_DELIVERY', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', ], 'authorized' => [ 'shape' => 'Boolean', ], 'insecureIngest' => [ 'shape' => 'Boolean', ], 'latencyMode' => [ 'shape' => 'ChannelLatencyMode', ], 'name' => [ 'shape' => 'ChannelName', ], 'playbackRestrictionPolicyArn' => [ 'shape' => 'ChannelPlaybackRestrictionPolicyArn', ], 'preset' => [ 'shape' => 'TranscodePreset', ], 'recordingConfigurationArn' => [ 'shape' => 'ChannelRecordingConfigurationArn', ], 'type' => [ 'shape' => 'ChannelType', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'Channel', ], ], ], 'UpdatePlaybackRestrictionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'allowedCountries' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedCountryList', ], 'allowedOrigins' => [ 'shape' => 'PlaybackRestrictionPolicyAllowedOriginList', ], 'arn' => [ 'shape' => 'PlaybackRestrictionPolicyArn', ], 'enableStrictOriginEnforcement' => [ 'shape' => 'PlaybackRestrictionPolicyEnableStrictOriginEnforcement', ], 'name' => [ 'shape' => 'PlaybackRestrictionPolicyName', ], ], ], 'UpdatePlaybackRestrictionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'playbackRestrictionPolicy' => [ 'shape' => 'PlaybackRestrictionPolicy', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VideoConfiguration' => [ 'type' => 'structure', 'members' => [ 'avcLevel' => [ 'shape' => 'String', ], 'avcProfile' => [ 'shape' => 'String', ], 'codec' => [ 'shape' => 'String', ], 'encoder' => [ 'shape' => 'String', ], 'targetBitrate' => [ 'shape' => 'Integer', ], 'targetFramerate' => [ 'shape' => 'Integer', ], 'videoHeight' => [ 'shape' => 'Integer', ], 'videoWidth' => [ 'shape' => 'Integer', ], ], ], 'ViewerId' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'ViewerSessionVersion' => [ 'type' => 'integer', 'min' => 0, ], 'errorCode' => [ 'type' => 'string', ], 'errorMessage' => [ 'type' => 'string', ], ],];
