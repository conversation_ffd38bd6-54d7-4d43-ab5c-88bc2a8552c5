{"alias": "flowmaker", "version": "1.2", "description": "", "keywords": [], "active": 1, "order": 0, "providers": ["Modules\\Flowmaker\\Providers\\Main", "Modules\\Flowmaker\\Providers\\Event"], "aliases": {}, "files": [], "requires": [], "ownermenus": [{"name": "Flows", "icon": "ni ni-controller text-blue", "route": "flows.index", "color": "#2dce89", "priority": 10}], "vendor_fields": [{"separator": "OpenRouter", "title": "OpenRouter API Key", "key": "openrouter_api_key", "ftype": "input", "icon": "🔗", "value": ""}]}