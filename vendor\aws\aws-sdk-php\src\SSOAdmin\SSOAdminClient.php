<?php
namespace Aws\SSOAdmin;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Single Sign-On Admin** service.
 * @method \Aws\Result attachCustomerManagedPolicyReferenceToPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise attachCustomerManagedPolicyReferenceToPermissionSetAsync(array $args = [])
 * @method \Aws\Result attachManagedPolicyToPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise attachManagedPolicyToPermissionSetAsync(array $args = [])
 * @method \Aws\Result createAccountAssignment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAccountAssignmentAsync(array $args = [])
 * @method \Aws\Result createApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApplicationAsync(array $args = [])
 * @method \Aws\Result createApplicationAssignment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApplicationAssignmentAsync(array $args = [])
 * @method \Aws\Result createInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInstanceAsync(array $args = [])
 * @method \Aws\Result createInstanceAccessControlAttributeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInstanceAccessControlAttributeConfigurationAsync(array $args = [])
 * @method \Aws\Result createPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPermissionSetAsync(array $args = [])
 * @method \Aws\Result createTrustedTokenIssuer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTrustedTokenIssuerAsync(array $args = [])
 * @method \Aws\Result deleteAccountAssignment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAccountAssignmentAsync(array $args = [])
 * @method \Aws\Result deleteApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAsync(array $args = [])
 * @method \Aws\Result deleteApplicationAccessScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAccessScopeAsync(array $args = [])
 * @method \Aws\Result deleteApplicationAssignment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAssignmentAsync(array $args = [])
 * @method \Aws\Result deleteApplicationAuthenticationMethod(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAuthenticationMethodAsync(array $args = [])
 * @method \Aws\Result deleteApplicationGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationGrantAsync(array $args = [])
 * @method \Aws\Result deleteInlinePolicyFromPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInlinePolicyFromPermissionSetAsync(array $args = [])
 * @method \Aws\Result deleteInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInstanceAsync(array $args = [])
 * @method \Aws\Result deleteInstanceAccessControlAttributeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInstanceAccessControlAttributeConfigurationAsync(array $args = [])
 * @method \Aws\Result deletePermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePermissionSetAsync(array $args = [])
 * @method \Aws\Result deletePermissionsBoundaryFromPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePermissionsBoundaryFromPermissionSetAsync(array $args = [])
 * @method \Aws\Result deleteTrustedTokenIssuer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTrustedTokenIssuerAsync(array $args = [])
 * @method \Aws\Result describeAccountAssignmentCreationStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccountAssignmentCreationStatusAsync(array $args = [])
 * @method \Aws\Result describeAccountAssignmentDeletionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccountAssignmentDeletionStatusAsync(array $args = [])
 * @method \Aws\Result describeApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeApplicationAsync(array $args = [])
 * @method \Aws\Result describeApplicationAssignment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeApplicationAssignmentAsync(array $args = [])
 * @method \Aws\Result describeApplicationProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeApplicationProviderAsync(array $args = [])
 * @method \Aws\Result describeInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInstanceAsync(array $args = [])
 * @method \Aws\Result describeInstanceAccessControlAttributeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInstanceAccessControlAttributeConfigurationAsync(array $args = [])
 * @method \Aws\Result describePermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePermissionSetAsync(array $args = [])
 * @method \Aws\Result describePermissionSetProvisioningStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePermissionSetProvisioningStatusAsync(array $args = [])
 * @method \Aws\Result describeTrustedTokenIssuer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTrustedTokenIssuerAsync(array $args = [])
 * @method \Aws\Result detachCustomerManagedPolicyReferenceFromPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detachCustomerManagedPolicyReferenceFromPermissionSetAsync(array $args = [])
 * @method \Aws\Result detachManagedPolicyFromPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detachManagedPolicyFromPermissionSetAsync(array $args = [])
 * @method \Aws\Result getApplicationAccessScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationAccessScopeAsync(array $args = [])
 * @method \Aws\Result getApplicationAssignmentConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationAssignmentConfigurationAsync(array $args = [])
 * @method \Aws\Result getApplicationAuthenticationMethod(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationAuthenticationMethodAsync(array $args = [])
 * @method \Aws\Result getApplicationGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationGrantAsync(array $args = [])
 * @method \Aws\Result getInlinePolicyForPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getInlinePolicyForPermissionSetAsync(array $args = [])
 * @method \Aws\Result getPermissionsBoundaryForPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPermissionsBoundaryForPermissionSetAsync(array $args = [])
 * @method \Aws\Result listAccountAssignmentCreationStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccountAssignmentCreationStatusAsync(array $args = [])
 * @method \Aws\Result listAccountAssignmentDeletionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccountAssignmentDeletionStatusAsync(array $args = [])
 * @method \Aws\Result listAccountAssignments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccountAssignmentsAsync(array $args = [])
 * @method \Aws\Result listAccountAssignmentsForPrincipal(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccountAssignmentsForPrincipalAsync(array $args = [])
 * @method \Aws\Result listAccountsForProvisionedPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccountsForProvisionedPermissionSetAsync(array $args = [])
 * @method \Aws\Result listApplicationAccessScopes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationAccessScopesAsync(array $args = [])
 * @method \Aws\Result listApplicationAssignments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationAssignmentsAsync(array $args = [])
 * @method \Aws\Result listApplicationAssignmentsForPrincipal(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationAssignmentsForPrincipalAsync(array $args = [])
 * @method \Aws\Result listApplicationAuthenticationMethods(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationAuthenticationMethodsAsync(array $args = [])
 * @method \Aws\Result listApplicationGrants(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationGrantsAsync(array $args = [])
 * @method \Aws\Result listApplicationProviders(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationProvidersAsync(array $args = [])
 * @method \Aws\Result listApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationsAsync(array $args = [])
 * @method \Aws\Result listCustomerManagedPolicyReferencesInPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCustomerManagedPolicyReferencesInPermissionSetAsync(array $args = [])
 * @method \Aws\Result listInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInstancesAsync(array $args = [])
 * @method \Aws\Result listManagedPoliciesInPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedPoliciesInPermissionSetAsync(array $args = [])
 * @method \Aws\Result listPermissionSetProvisioningStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionSetProvisioningStatusAsync(array $args = [])
 * @method \Aws\Result listPermissionSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionSetsAsync(array $args = [])
 * @method \Aws\Result listPermissionSetsProvisionedToAccount(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionSetsProvisionedToAccountAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTrustedTokenIssuers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTrustedTokenIssuersAsync(array $args = [])
 * @method \Aws\Result provisionPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise provisionPermissionSetAsync(array $args = [])
 * @method \Aws\Result putApplicationAccessScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putApplicationAccessScopeAsync(array $args = [])
 * @method \Aws\Result putApplicationAssignmentConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putApplicationAssignmentConfigurationAsync(array $args = [])
 * @method \Aws\Result putApplicationAuthenticationMethod(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putApplicationAuthenticationMethodAsync(array $args = [])
 * @method \Aws\Result putApplicationGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putApplicationGrantAsync(array $args = [])
 * @method \Aws\Result putInlinePolicyToPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putInlinePolicyToPermissionSetAsync(array $args = [])
 * @method \Aws\Result putPermissionsBoundaryToPermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putPermissionsBoundaryToPermissionSetAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApplicationAsync(array $args = [])
 * @method \Aws\Result updateInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInstanceAsync(array $args = [])
 * @method \Aws\Result updateInstanceAccessControlAttributeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInstanceAccessControlAttributeConfigurationAsync(array $args = [])
 * @method \Aws\Result updatePermissionSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePermissionSetAsync(array $args = [])
 * @method \Aws\Result updateTrustedTokenIssuer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTrustedTokenIssuerAsync(array $args = [])
 */
class SSOAdminClient extends AwsClient {}
