<?php
// This file was auto-generated from sdk-root/src/data/supplychain/2024-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-01-01', 'endpointPrefix' => 'scn', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Supply Chain', 'serviceId' => 'SupplyChain', 'signatureVersion' => 'v4', 'signingName' => 'scn', 'uid' => 'supplychain-2024-01-01', ], 'operations' => [ 'CreateBillOfMaterialsImportJob' => [ 'name' => 'CreateBillOfMaterialsImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/configuration/instances/{instanceId}/bill-of-materials-import-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBillOfMaterialsImportJobRequest', ], 'output' => [ 'shape' => 'CreateBillOfMaterialsImportJobResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetBillOfMaterialsImportJob' => [ 'name' => 'GetBillOfMaterialsImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/configuration/instances/{instanceId}/bill-of-materials-import-jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBillOfMaterialsImportJobRequest', ], 'output' => [ 'shape' => 'GetBillOfMaterialsImportJobResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'SendDataIntegrationEvent' => [ 'name' => 'SendDataIntegrationEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendDataIntegrationEventRequest', ], 'output' => [ 'shape' => 'SendDataIntegrationEventResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BillOfMaterialsImportJob' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'jobId', 'status', 's3uri', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'jobId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'ConfigurationJobStatus', ], 's3uri' => [ 'shape' => 'ConfigurationS3Uri', ], 'message' => [ 'shape' => 'String', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 126, 'min' => 33, ], 'ConfigurationJobStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'FAILED', 'IN_PROGRESS', 'QUEUED', 'SUCCESS', ], ], 'ConfigurationS3Uri' => [ 'type' => 'string', 'min' => 10, 'pattern' => '[sS]3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateBillOfMaterialsImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 's3uri', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 's3uri' => [ 'shape' => 'ConfigurationS3Uri', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateBillOfMaterialsImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'UUID', ], ], ], 'DataIntegrationEventData' => [ 'type' => 'string', 'max' => 1048576, 'min' => 1, 'sensitive' => true, ], 'DataIntegrationEventGroupId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataIntegrationEventType' => [ 'type' => 'string', 'enum' => [ 'scn.data.forecast', 'scn.data.inventorylevel', 'scn.data.inboundorder', 'scn.data.inboundorderline', 'scn.data.inboundorderlineschedule', 'scn.data.outboundorderline', 'scn.data.outboundshipment', 'scn.data.processheader', 'scn.data.processoperation', 'scn.data.processproduct', 'scn.data.reservation', 'scn.data.shipment', 'scn.data.shipmentstop', 'scn.data.shipmentstoporder', 'scn.data.supplyplan', ], ], 'GetBillOfMaterialsImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'jobId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'jobId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetBillOfMaterialsImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'BillOfMaterialsImportJob', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SendDataIntegrationEventRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'eventType', 'data', 'eventGroupId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'eventType' => [ 'shape' => 'DataIntegrationEventType', ], 'data' => [ 'shape' => 'DataIntegrationEventData', ], 'eventGroupId' => [ 'shape' => 'DataIntegrationEventGroupId', ], 'eventTimestamp' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'SendDataIntegrationEventResponse' => [ 'type' => 'structure', 'required' => [ 'eventId', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_epoch_seconds' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
