<?php
// This file was auto-generated from sdk-root/src/data/cloudtrail-data/2021-08-11/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-08-11', 'endpointPrefix' => 'cloudtrail-data', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS CloudTrail Data Service', 'serviceId' => 'CloudTrail Data', 'signatureVersion' => 'v4', 'signingName' => 'cloudtrail-data', 'uid' => 'cloudtrail-data-2021-08-11', ], 'operations' => [ 'PutAuditEvents' => [ 'name' => 'PutAuditEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutAuditEvents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAuditEventsRequest', ], 'output' => [ 'shape' => 'PutAuditEventsResponse', ], 'errors' => [ [ 'shape' => 'ChannelInsufficientPermission', ], [ 'shape' => 'ChannelNotFound', ], [ 'shape' => 'InvalidChannelARN', ], [ 'shape' => 'ChannelUnsupportedSchema', ], [ 'shape' => 'DuplicatedAuditEventId', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], ], 'shapes' => [ 'AuditEvent' => [ 'type' => 'structure', 'required' => [ 'eventData', 'id', ], 'members' => [ 'eventData' => [ 'shape' => 'String', ], 'eventDataChecksum' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], ], ], 'AuditEventResultEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditEventResultEntry', ], 'max' => 100, 'min' => 0, ], 'AuditEventResultEntry' => [ 'type' => 'structure', 'required' => [ 'eventID', 'id', ], 'members' => [ 'eventID' => [ 'shape' => 'Uuid', ], 'id' => [ 'shape' => 'Uuid', ], ], ], 'AuditEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditEvent', ], 'max' => 100, 'min' => 1, ], 'ChannelArn' => [ 'type' => 'string', 'pattern' => '^arn:.*$', ], 'ChannelInsufficientPermission' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ChannelNotFound' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ChannelUnsupportedSchema' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DuplicatedAuditEventId' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ErrorCode' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ExternalId' => [ 'type' => 'string', 'max' => 1224, 'min' => 2, 'pattern' => '^[\\w+=,.@:\\/-]*$', ], 'InvalidChannelARN' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'PutAuditEventsRequest' => [ 'type' => 'structure', 'required' => [ 'auditEvents', 'channelArn', ], 'members' => [ 'auditEvents' => [ 'shape' => 'AuditEvents', ], 'channelArn' => [ 'shape' => 'ChannelArn', 'location' => 'querystring', 'locationName' => 'channelArn', ], 'externalId' => [ 'shape' => 'ExternalId', 'location' => 'querystring', 'locationName' => 'externalId', ], ], ], 'PutAuditEventsResponse' => [ 'type' => 'structure', 'required' => [ 'failed', 'successful', ], 'members' => [ 'failed' => [ 'shape' => 'ResultErrorEntries', ], 'successful' => [ 'shape' => 'AuditEventResultEntries', ], ], ], 'ResultErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultErrorEntry', ], 'max' => 100, 'min' => 0, ], 'ResultErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'id', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'id' => [ 'shape' => 'Uuid', ], ], ], 'String' => [ 'type' => 'string', ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Uuid' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[-_A-Za-z0-9]+$', ], ],];
