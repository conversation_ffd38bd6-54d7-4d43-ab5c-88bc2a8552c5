<?php

return [

    'AED' => [
        'name'                => 'UAE Dirham',
        'code'                => 784,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'د.إ',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'AFN' => [
        'name'                => 'Afghani',
        'code'                => 971,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '؋',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ALL' => [
        'name'                => 'Lek',
        'code'                => 8,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'L',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'AMD' => [
        'name'                => 'Armenian Dram',
        'code'                => 51,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'դր.',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ANG' => [
        'name'                => 'Netherlands Antillean Guilder',
        'code'                => 532,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ƒ',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'AOA' => [
        'name'                => 'Kwanza',
        'code'                => 973,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Kz',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ARS' => [
        'name'                => 'Argentine Peso',
        'code'                => 32,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'AUD' => [
        'name'                => 'Australian Dollar',
        'code'                => 36,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ' ',
    ],

    'AWG' => [
        'name'                => 'Aruban Florin',
        'code'                => 533,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ƒ',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'AZN' => [
        'name'                => 'Azerbaijanian Manat',
        'code'                => 944,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₼',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BAM' => [
        'name'                => 'Convertible Mark',
        'code'                => 977,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'КМ',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BBD' => [
        'name'                => 'Barbados Dollar',
        'code'                => 52,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BDT' => [
        'name'                => 'Taka',
        'code'                => 50,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '৳',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BGN' => [
        'name'                => 'Bulgarian Lev',
        'code'                => 975,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'лв',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => ' ',
    ],

    'BHD' => [
        'name'                => 'Bahraini Dinar',
        'code'                => 48,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'ب.د',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BIF' => [
        'name'                => 'Burundi Franc',
        'code'                => 108,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BMD' => [
        'name'                => 'Bermudian Dollar',
        'code'                => 60,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BND' => [
        'name'                => 'Brunei Dollar',
        'code'                => 96,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BOB' => [
        'name'                => 'Boliviano',
        'code'                => 68,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Bs.',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BOV' => [
        'name'                => 'Mvdol',
        'code'                => 984,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Bs.',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BRL' => [
        'name'                => 'Brazilian Real',
        'code'                => 986,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'R$',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'BSD' => [
        'name'                => 'Bahamian Dollar',
        'code'                => 44,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BTN' => [
        'name'                => 'Ngultrum',
        'code'                => 64,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Nu.',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BWP' => [
        'name'                => 'Pula',
        'code'                => 72,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'P',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'BYN' => [
        'name'                => 'Belarussian Ruble',
        'code'                => 974,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Br',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => ' ',
    ],

    'BZD' => [
        'name'                => 'Belize Dollar',
        'code'                => 84,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CAD' => [
        'name'                => 'Canadian Dollar',
        'code'                => 124,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CDF' => [
        'name'                => 'Congolese Franc',
        'code'                => 976,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CHF' => [
        'name'                => 'Swiss Franc',
        'code'                => 756,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'CHF',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CLF' => [
        'name'                => 'Unidades de fomento',
        'code'                => 990,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'UF',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'CLP' => [
        'name'                => 'Chilean Peso',
        'code'                => 152,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'CNY' => [
        'name'                => 'Yuan Renminbi',
        'code'                => 156,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '¥',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'COP' => [
        'name'                => 'Colombian Peso',
        'code'                => 170,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'CRC' => [
        'name'                => 'Costa Rican Colon',
        'code'                => 188,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₡',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'CUC' => [
        'name'                => 'Peso Convertible',
        'code'                => 931,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CUP' => [
        'name'                => 'Cuban Peso',
        'code'                => 192,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CVE' => [
        'name'                => 'Cape Verde Escudo',
        'code'                => 132,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'CZK' => [
        'name'                => 'Czech Koruna',
        'code'                => 203,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Kč',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'DJF' => [
        'name'                => 'Djibouti Franc',
        'code'                => 262,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fdj',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'DKK' => [
        'name'                => 'Danish Krone',
        'code'                => 208,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'kr',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'DOP' => [
        'name'                => 'Dominican Peso',
        'code'                => 214,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'DZD' => [
        'name'                => 'Algerian Dinar',
        'code'                => 12,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'د.ج',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'EGP' => [
        'name'                => 'Egyptian Pound',
        'code'                => 818,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ج.م',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ERN' => [
        'name'                => 'Nakfa',
        'code'                => 232,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Nfk',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ETB' => [
        'name'                => 'Ethiopian Birr',
        'code'                => 230,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Br',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'EUR' => [
        'name'                => 'Euro',
        'code'                => 978,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '€',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'FJD' => [
        'name'                => 'Fiji Dollar',
        'code'                => 242,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'FKP' => [
        'name'                => 'Falkland Islands Pound',
        'code'                => 238,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GBP' => [
        'name'                => 'Pound Sterling',
        'code'                => 826,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GEL' => [
        'name'                => 'Lari',
        'code'                => 981,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ლ',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GHS' => [
        'name'                => 'Ghana Cedi',
        'code'                => 936,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₵',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GIP' => [
        'name'                => 'Gibraltar Pound',
        'code'                => 292,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GMD' => [
        'name'                => 'Dalasi',
        'code'                => 270,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'D',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GNF' => [
        'name'                => 'Guinea Franc',
        'code'                => 324,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GTQ' => [
        'name'                => 'Quetzal',
        'code'                => 320,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Q',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'GYD' => [
        'name'                => 'Guyana Dollar',
        'code'                => 328,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'HKD' => [
        'name'                => 'Hong Kong Dollar',
        'code'                => 344,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'HNL' => [
        'name'                => 'Lempira',
        'code'                => 340,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'L',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'HRK' => [
        'name'                => 'Croatian Kuna',
        'code'                => 191,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'kn',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'HTG' => [
        'name'                => 'Gourde',
        'code'                => 332,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'G',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'HUF' => [
        'name'                => 'Forint',
        'code'                => 348,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Ft',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'IDR' => [
        'name'                => 'Rupiah',
        'code'                => 360,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Rp',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'ILS' => [
        'name'                => 'New Israeli Sheqel',
        'code'                => 376,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₪',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'INR' => [
        'name'                => 'Indian Rupee',
        'code'                => 356,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₹',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'IQD' => [
        'name'                => 'Iraqi Dinar',
        'code'                => 368,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'ع.د',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'IRR' => [
        'name'                => 'Iranian Rial',
        'code'                => 364,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '﷼',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ISK' => [
        'name'                => 'Iceland Krona',
        'code'                => 352,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'kr',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'JMD' => [
        'name'                => 'Jamaican Dollar',
        'code'                => 388,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'JOD' => [
        'name'                => 'Jordanian Dinar',
        'code'                => 400,
        'precision'           => 3,
        'subunit'             => 100,
        'symbol'              => 'د.ا',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'JPY' => [
        'name'                => 'Yen',
        'code'                => 392,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => '¥',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KES' => [
        'name'                => 'Kenyan Shilling',
        'code'                => 404,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'KSh',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KGS' => [
        'name'                => 'Som',
        'code'                => 417,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'som',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KHR' => [
        'name'                => 'Riel',
        'code'                => 116,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '៛',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KMF' => [
        'name'                => 'Comoro Franc',
        'code'                => 174,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KPW' => [
        'name'                => 'North Korean Won',
        'code'                => 408,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₩',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KRW' => [
        'name'                => 'Won',
        'code'                => 410,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => '₩',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KWD' => [
        'name'                => 'Kuwaiti Dinar',
        'code'                => 414,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'د.ك',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KYD' => [
        'name'                => 'Cayman Islands Dollar',
        'code'                => 136,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'KZT' => [
        'name'                => 'Tenge',
        'code'                => 398,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '〒',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LAK' => [
        'name'                => 'Kip',
        'code'                => 418,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₭',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LBP' => [
        'name'                => 'Lebanese Pound',
        'code'                => 422,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ل.ل',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LKR' => [
        'name'                => 'Sri Lanka Rupee',
        'code'                => 144,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₨',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LRD' => [
        'name'                => 'Liberian Dollar',
        'code'                => 430,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LSL' => [
        'name'                => 'Loti',
        'code'                => 426,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'L',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LTL' => [
        'name'                => 'Lithuanian Litas',
        'code'                => 440,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Lt',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LVL' => [
        'name'                => 'Latvian Lats',
        'code'                => 428,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Ls',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'LYD' => [
        'name'                => 'Libyan Dinar',
        'code'                => 434,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'ل.د',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MAD' => [
        'name'                => 'Moroccan Dirham',
        'code'                => 504,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'د.م.',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MDL' => [
        'name'                => 'Moldovan Leu',
        'code'                => 498,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'L',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MGA' => [
        'name'                => 'Malagasy Ariary',
        'code'                => 969,
        'precision'           => 2,
        'subunit'             => 5,
        'symbol'              => 'Ar',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MKD' => [
        'name'                => 'Denar',
        'code'                => 807,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ден',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MMK' => [
        'name'                => 'Kyat',
        'code'                => 104,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'K',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MNT' => [
        'name'                => 'Tugrik',
        'code'                => 496,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₮',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MOP' => [
        'name'                => 'Pataca',
        'code'                => 446,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'P',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MRO' => [
        'name'                => 'Ouguiya',
        'code'                => 478,
        'precision'           => 2,
        'subunit'             => 5,
        'symbol'              => 'UM',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MUR' => [
        'name'                => 'Mauritius Rupee',
        'code'                => 480,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₨',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MVR' => [
        'name'                => 'Rufiyaa',
        'code'                => 462,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'MVR',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MWK' => [
        'name'                => 'Kwacha',
        'code'                => 454,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'MK',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MXN' => [
        'name'                => 'Mexican Peso',
        'code'                => 484,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MYR' => [
        'name'                => 'Malaysian Ringgit',
        'code'                => 458,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'RM',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'MZN' => [
        'name'                => 'Mozambique Metical',
        'code'                => 943,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'MTn',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'NAD' => [
        'name'                => 'Namibia Dollar',
        'code'                => 516,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'NGN' => [
        'name'                => 'Naira',
        'code'                => 566,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₦',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'NIO' => [
        'name'                => 'Cordoba Oro',
        'code'                => 558,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'C$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'NOK' => [
        'name'                => 'Norwegian Krone',
        'code'                => 578,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'kr',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'NPR' => [
        'name'                => 'Nepalese Rupee',
        'code'                => 524,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₨',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'NZD' => [
        'name'                => 'New Zealand Dollar',
        'code'                => 554,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'OMR' => [
        'name'                => 'Rial Omani',
        'code'                => 512,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'ر.ع.',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PAB' => [
        'name'                => 'Balboa',
        'code'                => 590,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'B/.',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PEN' => [
        'name'                => 'Sol',
        'code'                => 604,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'S/',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PGK' => [
        'name'                => 'Kina',
        'code'                => 598,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'K',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PHP' => [
        'name'                => 'Philippine Peso',
        'code'                => 608,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₱',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PKR' => [
        'name'                => 'Pakistan Rupee',
        'code'                => 586,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₨',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'PLN' => [
        'name'                => 'Zloty',
        'code'                => 985,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'zł',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => ' ',
    ],

    'PYG' => [
        'name'                => 'Guarani',
        'code'                => 600,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => '₲',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'QAR' => [
        'name'                => 'Qatari Rial',
        'code'                => 634,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ر.ق',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'RON' => [
        'name'                => 'New Romanian Leu',
        'code'                => 946,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Lei',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'RSD' => [
        'name'                => 'Serbian Dinar',
        'code'                => 941,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'РСД',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'RUB' => [
        'name'                => 'Russian Ruble',
        'code'                => 643,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₽',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'RWF' => [
        'name'                => 'Rwanda Franc',
        'code'                => 646,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'FRw',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SAR' => [
        'name'                => 'Saudi Riyal',
        'code'                => 682,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ر.س',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SBD' => [
        'name'                => 'Solomon Islands Dollar',
        'code'                => 90,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SCR' => [
        'name'                => 'Seychelles Rupee',
        'code'                => 690,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₨',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SDG' => [
        'name'                => 'Sudanese Pound',
        'code'                => 938,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SEK' => [
        'name'                => 'Swedish Krona',
        'code'                => 752,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'kr',
        'symbol_first'        => false,
        'decimal_mark'        => ',',
        'thousands_separator' => ' ',
    ],

    'SGD' => [
        'name'                => 'Singapore Dollar',
        'code'                => 702,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SHP' => [
        'name'                => 'Saint Helena Pound',
        'code'                => 654,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SLL' => [
        'name'                => 'Leone',
        'code'                => 694,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Le',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SOS' => [
        'name'                => 'Somali Shilling',
        'code'                => 706,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Sh',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SRD' => [
        'name'                => 'Surinam Dollar',
        'code'                => 968,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SSP' => [
        'name'                => 'South Sudanese Pound',
        'code'                => 728,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'STD' => [
        'name'                => 'Dobra',
        'code'                => 678,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Db',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SVC' => [
        'name'                => 'El Salvador Colon',
        'code'                => 222,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₡',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SYP' => [
        'name'                => 'Syrian Pound',
        'code'                => 760,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '£S',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'SZL' => [
        'name'                => 'Lilangeni',
        'code'                => 748,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'E',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'THB' => [
        'name'                => 'Baht',
        'code'                => 764,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '฿',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TJS' => [
        'name'                => 'Somoni',
        'code'                => 972,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ЅМ',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TMT' => [
        'name'                => 'Turkmenistan New Manat',
        'code'                => 934,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'T',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TND' => [
        'name'                => 'Tunisian Dinar',
        'code'                => 788,
        'precision'           => 3,
        'subunit'             => 1000,
        'symbol'              => 'د.ت',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TOP' => [
        'name'                => 'Pa’anga',
        'code'                => 776,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'T$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TRY' => [
        'name'                => 'Turkish Lira',
        'code'                => 949,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₺',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'TTD' => [
        'name'                => 'Trinidad and Tobago Dollar',
        'code'                => 780,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TWD' => [
        'name'                => 'New Taiwan Dollar',
        'code'                => 901,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'TZS' => [
        'name'                => 'Tanzanian Shilling',
        'code'                => 834,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Sh',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'UAH' => [
        'name'                => 'Hryvnia',
        'code'                => 980,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '₴',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'UGX' => [
        'name'                => 'Uganda Shilling',
        'code'                => 800,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'USh',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'USD' => [
        'name'                => 'US Dollar',
        'code'                => 840,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'UYU' => [
        'name'                => 'Peso Uruguayo',
        'code'                => 858,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'UZS' => [
        'name'                => 'Uzbekistan Sum',
        'code'                => 860,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'лв',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'VEF' => [
        'name'                => 'Bolivar',
        'code'                => 937,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'Bs F',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'VND' => [
        'name'                => 'Dong',
        'code'                => 704,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => '₫',
        'symbol_first'        => true,
        'decimal_mark'        => ',',
        'thousands_separator' => '.',
    ],

    'VUV' => [
        'name'                => 'Vatu',
        'code'                => 548,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Vt',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'WST' => [
        'name'                => 'Tala',
        'code'                => 882,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'T',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XAF' => [
        'name'                => 'CFA Franc BEAC',
        'code'                => 950,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XAG' => [
        'name'                => 'Silver',
        'code'                => 961,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'oz t',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XAU' => [
        'name'                => 'Gold',
        'code'                => 959,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'oz t',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XCD' => [
        'name'                => 'East Caribbean Dollar',
        'code'                => 951,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XDR' => [
        'name'                => 'SDR (Special Drawing Right)',
        'code'                => 960,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'SDR',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XOF' => [
        'name'                => 'CFA Franc BCEAO',
        'code'                => 952,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'XPF' => [
        'name'                => 'CFP Franc',
        'code'                => 953,
        'precision'           => 0,
        'subunit'             => 1,
        'symbol'              => 'Fr',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'YER' => [
        'name'                => 'Yemeni Rial',
        'code'                => 886,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '﷼',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ZAR' => [
        'name'                => 'Rand',
        'code'                => 710,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'R',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ZMW' => [
        'name'                => 'Zambian Kwacha',
        'code'                => 967,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => 'ZK',
        'symbol_first'        => false,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],

    'ZWL' => [
        'name'                => 'Zimbabwe Dollar',
        'code'                => 932,
        'precision'           => 2,
        'subunit'             => 100,
        'symbol'              => '$',
        'symbol_first'        => true,
        'decimal_mark'        => '.',
        'thousands_separator' => ',',
    ],
];
