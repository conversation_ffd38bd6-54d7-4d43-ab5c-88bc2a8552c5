<?php

/**
 * This file is part of PHPWord - A pure PHP library for reading and writing
 * word processing documents.
 *
 * PHPWord is free software distributed under the terms of the GNU Lesser
 * General Public License version 3 as published by the Free Software Foundation.
 *
 * For the full copyright and license information, please read the LICENSE
 * file that was distributed with this source code. For the full list of
 * contributors, visit https://github.com/PHPOffice/PHPWord/contributors.
 *
 * @see         https://github.com/PHPOffice/PHPWord
 *
 * @license     http://www.gnu.org/licenses/lgpl.txt LGPL version 3
 */

namespace PhpOffice\PhpWord\Writer;

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Settings;
use PhpOffice\PhpWord\Shared\Validate;
use PhpOffice\PhpWord\Style\Font;

/**
 * HTML writer.
 *
 * Not supported: PreserveText, PageBreak, Object
 *
 * @since 0.10.0
 */
class HTML extends AbstractWriter implements WriterInterface
{
    /**
     * Is the current writer creating PDF?
     *
     * @var bool
     */
    protected $isPdf = false;

    /**
     * Footnotes and endnotes collection.
     *
     * @var array
     */
    protected $notes = [];

    /**
     * Callback for editing generated html.
     *
     * @var null|callable
     */
    private $editCallback;

    /**
     * Default generic name for default font for html.
     *
     * @var string
     */
    private $defaultGenericFont = '';

    /**
     * Default white space style for html.
     *
     * @var string
     */
    private $defaultWhiteSpace = '';

    /**
     * Create new instance.
     */
    public function __construct(?PhpWord $phpWord = null)
    {
        $this->setPhpWord($phpWord);

        $this->parts = ['Head', 'Body'];
        foreach ($this->parts as $partName) {
            $partClass = 'PhpOffice\\PhpWord\\Writer\\HTML\\Part\\' . $partName;
            if (class_exists($partClass)) {
                /** @var HTML\Part\AbstractPart $part Type hint */
                $part = new $partClass();
                $part->setParentWriter($this);
                $this->writerParts[strtolower($partName)] = $part;
            }
        }
    }

    /**
     * Save PhpWord to file.
     */
    public function save(string $filename): void
    {
        $this->writeFile($this->openFile($filename), $this->getContent());
    }

    /**
     * Get content.
     *
     * @return string
     *
     * @since 0.11.0
     */
    public function getContent()
    {
        $content = '';

        $content .= '<!DOCTYPE html>' . PHP_EOL;
        $content .= '<!-- Generated by PHPWord -->' . PHP_EOL;
        $langtext = '';
        $phpWord = $this->getPhpWord();
        $lang = $phpWord->getSettings()->getThemeFontLang();
        if (!empty($lang)) {
            $lang2 = $lang->getLatin();
            if (!$lang2) {
                $lang2 = $lang->getEastAsia();
            }
            if (!$lang2) {
                $lang2 = $lang->getBidirectional();
            }
            if ($lang2) {
                $langtext = " lang='" . $lang2 . "'";
            }
        }
        $content .= "<html$langtext>" . PHP_EOL;
        $content .= $this->getWriterPart('Head')->write();
        $content .= $this->getWriterPart('Body')->write();
        $content .= '</html>' . PHP_EOL;

        // Trigger a callback for editing the entire HTML
        $callback = $this->editCallback;
        if ($callback !== null) {
            $content = $callback($content);
        }

        return $content;
    }

    /**
     * Return the callback to edit the entire HTML.
     */
    public function getEditCallback(): ?callable
    {
        return $this->editCallback;
    }

    /**
     * Set a callback to edit the entire HTML.
     *
     * The callback must accept the HTML as string as first parameter,
     * and it must return the edited HTML as string.
     */
    public function setEditCallback(?callable $callback): self
    {
        $this->editCallback = $callback;

        return $this;
    }

    /**
     * Get is PDF.
     *
     * @return bool
     */
    public function isPdf()
    {
        return $this->isPdf;
    }

    /**
     * Get notes.
     *
     * @return array
     */
    public function getNotes()
    {
        return $this->notes;
    }

    /**
     * Add note.
     *
     * @param int $noteId
     * @param string $noteMark
     */
    public function addNote($noteId, $noteMark): void
    {
        $this->notes[$noteId] = $noteMark;
    }

    /**
     * Get generic name for default font for html.
     */
    public function getDefaultGenericFont(): string
    {
        return $this->defaultGenericFont;
    }

    /**
     * Set generic name for default font for html.
     */
    public function setDefaultGenericFont(string $value): self
    {
        $this->defaultGenericFont = Validate::validateCSSGenericFont($value);

        return $this;
    }

    /**
     * Get default white space style for html.
     */
    public function getDefaultWhiteSpace(): string
    {
        return $this->defaultWhiteSpace;
    }

    /**
     * Set default white space style for html.
     */
    public function setDefaultWhiteSpace(string $value): self
    {
        $this->defaultWhiteSpace = Validate::validateCSSWhiteSpace($value);

        return $this;
    }

    /**
     * Escape string or not depending on setting.
     */
    public function escapeHTML(string $txt): string
    {
        if (Settings::isOutputEscapingEnabled()) {
            return htmlspecialchars($txt, ENT_QUOTES | (defined('ENT_SUBSTITUTE') ? ENT_SUBSTITUTE : 0), 'UTF-8');
        }

        return $txt;
    }
}
