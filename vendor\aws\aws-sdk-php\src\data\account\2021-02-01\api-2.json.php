<?php
// This file was auto-generated from sdk-root/src/data/account/2021-02-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-02-01', 'endpointPrefix' => 'account', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Account', 'serviceId' => 'Account', 'signatureVersion' => 'v4', 'signingName' => 'account', 'uid' => 'account-2021-02-01', ], 'operations' => [ 'DeleteAlternateContact' => [ 'name' => 'DeleteAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAlternateContactRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DisableRegion' => [ 'name' => 'DisableRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/disableRegion', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableRegionRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'EnableRegion' => [ 'name' => 'EnableRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/enableRegion', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableRegionRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAlternateContact' => [ 'name' => 'GetAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/getAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAlternateContactRequest', ], 'output' => [ 'shape' => 'GetAlternateContactResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetContactInformation' => [ 'name' => 'GetContactInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/getContactInformation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContactInformationRequest', ], 'output' => [ 'shape' => 'GetContactInformationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRegionOptStatus' => [ 'name' => 'GetRegionOptStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/getRegionOptStatus', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRegionOptStatusRequest', ], 'output' => [ 'shape' => 'GetRegionOptStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRegions' => [ 'name' => 'ListRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/listRegions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRegionsRequest', ], 'output' => [ 'shape' => 'ListRegionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutAlternateContact' => [ 'name' => 'PutAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/putAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAlternateContactRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'PutContactInformation' => [ 'name' => 'PutContactInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/putContactInformation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutContactInformationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'AddressLine' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'sensitive' => true, ], 'AlternateContact' => [ 'type' => 'structure', 'members' => [ 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'Name', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Title' => [ 'shape' => 'Title', ], ], ], 'AlternateContactType' => [ 'type' => 'string', 'enum' => [ 'BILLING', 'OPERATIONS', 'SECURITY', ], ], 'City' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'CompanyName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContactInformation' => [ 'type' => 'structure', 'required' => [ 'AddressLine1', 'City', 'CountryCode', 'FullName', 'PhoneNumber', 'PostalCode', ], 'members' => [ 'AddressLine1' => [ 'shape' => 'AddressLine', ], 'AddressLine2' => [ 'shape' => 'AddressLine', ], 'AddressLine3' => [ 'shape' => 'AddressLine', ], 'City' => [ 'shape' => 'City', ], 'CompanyName' => [ 'shape' => 'CompanyName', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'DistrictOrCounty' => [ 'shape' => 'DistrictOrCounty', ], 'FullName' => [ 'shape' => 'FullName', ], 'PhoneNumber' => [ 'shape' => 'ContactInformationPhoneNumber', ], 'PostalCode' => [ 'shape' => 'PostalCode', ], 'StateOrRegion' => [ 'shape' => 'StateOrRegion', ], 'WebsiteUrl' => [ 'shape' => 'WebsiteUrl', ], ], ], 'ContactInformationPhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[+][\\s0-9()-]+$', 'sensitive' => true, ], 'CountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'DeleteAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], ], ], 'DisableRegionRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'DistrictOrCounty' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'EmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 1, 'pattern' => '^[\\s]*[\\w+=.#|!&-]+@[\\w.-]+\\.[\\w]+[\\s]*$', 'sensitive' => true, ], 'EnableRegionRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'FullName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'GetAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], ], ], 'GetAlternateContactResponse' => [ 'type' => 'structure', 'members' => [ 'AlternateContact' => [ 'shape' => 'AlternateContact', ], ], ], 'GetContactInformationRequest' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetContactInformationResponse' => [ 'type' => 'structure', 'members' => [ 'ContactInformation' => [ 'shape' => 'ContactInformation', ], ], ], 'GetRegionOptStatusRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'GetRegionOptStatusResponse' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'RegionOptStatus' => [ 'shape' => 'RegionOptStatus', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListRegionsRequest' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'MaxResults' => [ 'shape' => 'ListRegionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListRegionsRequestNextTokenString', ], 'RegionOptStatusContains' => [ 'shape' => 'RegionOptStatusList', ], ], ], 'ListRegionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListRegionsRequestNextTokenString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ListRegionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Regions' => [ 'shape' => 'RegionOptList', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => '^[\\s0-9()+-]+$', 'sensitive' => true, ], 'PostalCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'sensitive' => true, ], 'PutAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', 'EmailAddress', 'Name', 'PhoneNumber', 'Title', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'Name', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Title' => [ 'shape' => 'Title', ], ], ], 'PutContactInformationRequest' => [ 'type' => 'structure', 'required' => [ 'ContactInformation', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ContactInformation' => [ 'shape' => 'ContactInformation', ], ], ], 'Region' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'RegionOptStatus' => [ 'shape' => 'RegionOptStatus', ], ], ], 'RegionName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'RegionOptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegionOptStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'ENABLING', 'DISABLING', 'DISABLED', 'ENABLED_BY_DEFAULT', ], ], 'RegionOptStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionOptStatus', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'StateOrRegion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'Title' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TooManyRequestsException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'SensitiveString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'SensitiveString', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'invalidRegionOptTarget', 'fieldValidationFailed', ], ], 'WebsiteUrl' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], ],];
