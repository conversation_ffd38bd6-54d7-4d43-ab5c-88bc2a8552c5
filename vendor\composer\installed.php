<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '9cf6f65193744dccceb963cfdd0534987d92b607',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'akaunting/laravel-module' => array(
            'pretty_version' => '2.0.12',
            'version' => '2.0.12.0',
            'reference' => 'cfcba1bcae3499b21bd1bac6ebc72aaf4b275cf7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../akaunting/laravel-module',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'akaunting/laravel-money' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'df99d0f5d415490ef7e79362c3b694e8cc8af903',
            'type' => 'library',
            'install_path' => __DIR__ . '/../akaunting/laravel-money',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'anourvalar/eloquent-serialize' => array(
            'pretty_version' => '1.2.22',
            'version' => '1.2.22.0',
            'reference' => '6e91093c10940859c4b0549b6a90f18d8db45998',
            'type' => 'library',
            'install_path' => __DIR__ . '/../anourvalar/eloquent-serialize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-crt-php' => array(
            'pretty_version' => 'v1.2.4',
            'version' => '1.2.4.0',
            'reference' => 'eb0c6e4e142224a10b08f49ebf87f32611d162b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-crt-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-sdk-php' => array(
            'pretty_version' => '3.304.3',
            'version' => '3.304.3.0',
            'reference' => 'd1ecaba720dc5d935d4ff66255f990fce3aa9727',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'berkayk/onesignal-laravel' => array(
            'pretty_version' => 'v2.1',
            'version' => '2.1.0.0',
            'reference' => '292973c63914b7b6a6b788a7849c1060abfe9079',
            'type' => 'library',
            'install_path' => __DIR__ . '/../berkayk/onesignal-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'blade-ui-kit/blade-heroicons' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'reference' => 'a265dbcf2a098121aad05752d0bba9f59022e4ba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../blade-ui-kit/blade-heroicons',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'blade-ui-kit/blade-icons' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => '89660d93f9897d231e9113ba203cd17f4c5efade',
            'type' => 'library',
            'install_path' => __DIR__ . '/../blade-ui-kit/blade-icons',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '99f76ffa36cce3b70a4a6abce41dba15ca2e84cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.0',
            'version' => '*******',
            'reference' => '35e8d0af4486141bc745f23a29cc2091eb624a32',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dacoto/laravel-env-set' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'a0a7ce173c01ba0fac7899e57182c9c226e5efdc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dacoto/laravel-env-set',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dacoto/laravel-wizard-installer' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'c502d4f8d31125469783e0ac4bf8fe7da209e742',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dacoto/laravel-wizard-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'danharrin/date-format-converter' => array(
            'pretty_version' => 'v0.3.0',
            'version' => '0.3.0.0',
            'reference' => '42b6ddc52059d4ba228a67c15adaaa0c039e75f2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../danharrin/date-format-converter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'reference' => '6faf451159fb8ba4126b925ed2d78acfce0dc016',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '*******',
            'reference' => 'f41715465d65213d644d3141a6a93081be5d3549',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1ca8f21980e770095a31456042471a57bc4c68fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '3.8.3',
            'version' => '3.8.3.0',
            'reference' => 'db922ba9436b7b18a23d1653a0b41ff2369ca41c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => '750671534e0241a7c50ea5b43f67e23eb5c96f32',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'c6222283fa3f4ac679f8b9ced9a4e23f163e80d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'adfb1f505deb6384dc8b39804c5065dd3c8c8c0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'ebaaf5be6c0286928352e054f2d5125608e5405e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.17.0',
            'version' => '4.17.0.0',
            'reference' => 'bbc513d79acf6691fa9cf10f192c90dd2957f18c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/actions' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '7f978130358ce997bdb5f7fe7994915cebf9f524',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/actions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/forms' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '5ecbfdfd124ad072d0bb77edba33262494c137eb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/forms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/infolists' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '8945c92a86531bb9793b844b3321028e572c8185',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/infolists',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/notifications' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '067117fb0708dfd04955faafcfc82cd6d182e52f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/notifications',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/support' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '4b629597f5c2130abe0701c82e4da5b266bcbafa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filament/tables' => array(
            'pretty_version' => 'v3.2.65',
            'version' => '3.2.65.0',
            'reference' => '1cdf9ee9ba35cad6e8e41725896e26e397549440',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filament/tables',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.15.4',
            'version' => '2.15.4.0',
            'reference' => 'a139776fa3f5985a50b509f2a02ff0f709d2a546',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => 'fbd48bce38f73f8a4ec8583362e732e4095e5862',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.1',
            'version' => '7.8.1.0',
            'reference' => '41042bc7ab002487b876a0683fc8dce04ddce104',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'bbff78d96034045e58e13dedd6ad91b5d1253223',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'reference' => '45b30f99ac27b5ca93cb4831afe16285f57b8221',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'ecea8feef63bd4fef1f037ecb288386999ecc11c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.48.7',
            ),
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'joedixon/laravel-translation' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'feba4d1e3d12722ca60c05d9180f39b7de227e4e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../joedixon/laravel-translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kirschbaum-development/eloquent-power-joins' => array(
            'pretty_version' => '3.5.6',
            'version' => '3.5.6.0',
            'reference' => '6de51d9ec43af34e77bd1d9908173de1416a0aed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kirschbaum-development/eloquent-power-joins',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/cashier' => array(
            'pretty_version' => 'v14.14.0',
            'version' => '14.14.0.0',
            'reference' => '46d8139cc4c4a53c70a56cbc8605c4503a429f4c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/cashier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/fortify' => array(
            'pretty_version' => 'v1.21.1',
            'version' => '1.21.1.0',
            'reference' => '405388fd399264715573e23ed2f368fbce426da3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/fortify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v10.48.7',
            'version' => '10.48.7.0',
            'reference' => '118c686992f4b90d4da6deaf0901315c337bbaf9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/jetstream' => array(
            'pretty_version' => 'v4.3.1',
            'version' => '4.3.1.0',
            'reference' => '1cb2b30664d818491b3193ccbdd97758fa5b60fc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/jetstream',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '9cf6f65193744dccceb963cfdd0534987d92b607',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/legacy-factories' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '6cb79f668fc36b8b396ada1da3ba45867889c30f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/legacy-factories',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.15.1',
            'version' => '1.15.1.0',
            'reference' => '5f288b5e79938cc72f5c298d384e639de87507c6',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.1.18',
            'version' => '0.1.18.0',
            'reference' => '3b5e6b03f1f1175574b5a32331d99c9819da9848',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.29.1',
            'version' => '1.29.1.0',
            'reference' => '8be4a31150eab3b46af11a2e7b2c4632eefaad7e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => '8c104366459739f3ada0e994bcd3e6fd681ce3d5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.3',
            'version' => '1.3.3.0',
            'reference' => '3dbf8a8e914634c48d389c1234552666b3d43754',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/socialite' => array(
            'pretty_version' => 'v5.12.1',
            'version' => '5.12.1.0',
            'reference' => '7dae1b072573809f32ab6dcf4aebb57c8b3e8acf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/telescope' => array(
            'pretty_version' => 'v4.17.6',
            'version' => '4.17.6.0',
            'reference' => '2d453dc629b27e8cf39fb1217aba062f8c54e690',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/telescope',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.9.0',
            'version' => '*******',
            'reference' => '502e0fe3f0415d06d5db1f83a472f0f3b754bafe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.4.2',
            'version' => '2.4.2.0',
            'reference' => '91c24291965bd6d7c46c46a12ba7492f83b1cadf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.15.0',
            'version' => '9.15.0.0',
            'reference' => 'fa7e2441c0bc9b2360f4314fd6c954f7ff40d435',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.27.0',
            'version' => '3.27.0.0',
            'reference' => '4729745b1ab737908c7d055148c9a6b3e959832f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-aws-s3-v3' => array(
            'pretty_version' => '3.27.0',
            'version' => '3.27.0.0',
            'reference' => '3e6ce2f972f1470db779f04d29c289dcd2c32837',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-aws-s3-v3',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.25.1',
            'version' => '3.25.1.0',
            'reference' => '61a6a90d6e999e4ddd9ce5adb356de0939060b92',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth1-client' => array(
            'pretty_version' => 'v1.10.1',
            'version' => '1.10.1.0',
            'reference' => 'd6365b901b5c287dd41f143033315e2f777e1167',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth1-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri' => array(
            'pretty_version' => '7.4.1',
            'version' => '7.4.1.0',
            'reference' => 'bedb6e55eff0c933668addaa7efa1e1f2c417cc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '7.4.1',
            'version' => '7.4.1.0',
            'reference' => '8d43ef5c841032c87e2de015972c06f3865ef718',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'livewire/livewire' => array(
            'pretty_version' => 'v3.4.10',
            'version' => '3.4.10.0',
            'reference' => '6f90e2d7f8e80a97a7406c22a0fbc61ca1256ed9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../livewire/livewire',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maatwebsite/excel' => array(
            'pretty_version' => '3.1.55',
            'version' => '********',
            'reference' => '6d9d791dcdb01a9b6fd6f48d46f0d5fff86e6260',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maatwebsite/excel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.0',
            'version' => '*******',
            'reference' => 'b8174494eda667f7d13876b4a7bfef0f62a7c0d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '*******',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mckenziearts/laravel-notify' => array(
            'pretty_version' => 'v2.4',
            'version' => '2.4.0.0',
            'reference' => 'fddde15ae2407792f2884e6f9107d49e9e608cbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mckenziearts/laravel-notify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mobiledetect/mobiledetectlib' => array(
            'pretty_version' => '4.8.06',
            'version' => '4.8.06.0',
            'reference' => 'af088b54cecc13b3264edca7da93a89ba7aa2d9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mobiledetect/mobiledetectlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.11',
            'version' => '1.6.11.0',
            'reference' => '81a161d0b135df89951abd52296adf97deb0723d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'moneyphp/money' => array(
            'pretty_version' => 'v4.5.0',
            'version' => '4.5.0.0',
            'reference' => 'a1daa7daf159b4044e3d0c34c41fe2be5860e850',
            'type' => 'library',
            'install_path' => __DIR__ . '/../moneyphp/money',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'c915e2634718dbc8a4a15c61b0e62e7a44e14448',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'bbb69a935c2cbb0c03d7f481a238027430f6440b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.1',
            'version' => '1.11.1.0',
            'reference' => '7284c22080590fb39f2ffa3e9057f10a4ddd0e0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.72.3',
            'version' => '2.72.3.0',
            'reference' => '0c6fd108360c562f6e4fd1dedb8233b423e91c83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'a6d3a6d1f545f01ef38e60f375d1cf1f4de98188',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd3ad0aa3b9f934602cb3e3902ebccf10be34d218',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.0.2',
            'version' => '5.0.2.0',
            'reference' => '139676794dc1e9231bf7bcd123cfc0c99182cb13',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'reference' => 'f05978827b9343cba381ca05b8c7deee346b6015',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v1.15.1',
            'version' => '1.15.1.0',
            'reference' => '8ab0b32c8caa4a2e09700ea32925441385e4a5dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opcodesio/log-viewer' => array(
            'pretty_version' => 'v3.8.0',
            'version' => '3.8.0.0',
            'reference' => 'e7af7313a6d06af03ac2ee1a565ccf57bde4f976',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opcodesio/log-viewer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opcodesio/mail-parser' => array(
            'pretty_version' => 'v0.1.6',
            'version' => '0.1.6.0',
            'reference' => '639ef31cbd146a63416283e75afce152e13233ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opcodesio/mail-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openspout/openspout' => array(
            'pretty_version' => 'v4.23.0',
            'version' => '4.23.0.0',
            'reference' => '28f6a0e45acc3377f34c26cc3866e21f0447e0c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openspout/openspout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '58c3f47f650c94ec05a151692652a868995d2938',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.20.1',
            'version' => '1.20.1.0',
            'reference' => '1840b98d228bdad83869b191d7e51f9bb6624d8d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'fde2ccf55eaef7e86021ff1acce26479160a0fa0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.2',
            'version' => '1.9.2.0',
            'reference' => '80735db690fe4fc5c76dfa7f9b770634285fa820',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '9.2.31',
            'version' => '9.2.31.0',
            'reference' => '48c34b5d8d983006bd2adc2d0de92963b9155965',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'reference' => 'cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '5a10147d0aaf65b58940a0b72f71c9ac0423cc67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '9.6.19',
            'version' => '9.6.19.0',
            'reference' => 'a1a54a473501ef4cdeaae4e06891674114d79db8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.1',
            'version' => '8.0.1.0',
            'reference' => '80c3d801b31fe165f8fe99ea085e0a37834e1be3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'fe5ea303b0887d5caefd3d431c3e61ad47037001',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.3',
            'version' => '0.12.3.0',
            'reference' => 'b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pusher/pusher-php-server' => array(
            'pretty_version' => '7.2.4',
            'version' => '7.2.4.0',
            'reference' => 'de2f72296808f9cafa6a4462b15a768ff130cddb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pusher/pusher-php-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.5',
            'version' => '4.7.5.0',
            'reference' => '5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.5',
            ),
        ),
        'ryangjchandler/blade-capture-directive' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'cb6f58663d97f17bece176295240b740835e14f1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ryangjchandler/blade-capture-directive',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '2b56bea83a09de3ac06bb18b92f068e60cc6f50b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '1.0.8',
            'version' => '1.0.8.0',
            'reference' => '1fc9f64c0927627ef78ba436c9b17d967e68e120',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'fa0f136dd2334583309d32b62544682ee972b51a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '25f207c40d62b8b7aa32f5ab026c53561964053a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '5.1.5',
            'version' => '5.1.5.0',
            'reference' => '830c43a844f1f8d5b7a1f6d6076b784454d8b7ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '78c00df8f170e02473b682df15bfcdacc3d32d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '5.0.7',
            'version' => '5.0.7.0',
            'reference' => 'bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'e1e4a170560925c26d424b6a03aed157e7dcc5c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '5c9eeac41b290a3712d88851518825ad78f45c71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b4f479ebdbf63ac605d183ece17d8d7fe49c15c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '3.0.4',
            'version' => '3.0.4.0',
            'reference' => '05d5692a7993ecccd56a03e40cd7e5b09b1d404e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => 'c6c1022351a901512170118436c764e473f6de8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/backtrace' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '483f76a82964a0431aa836b6ed0edde0c248e3ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/backtrace',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/color' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '49739265900cabce4640cd26c3266fd8d2cca390',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/color',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/eloquent-sortable' => array(
            'pretty_version' => '4.2.0',
            'version' => '4.2.0.0',
            'reference' => 'd7940cc59af939b27422a8e7a4020ac8c5d93a77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/eloquent-sortable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/flare-client-php' => array(
            'pretty_version' => '1.4.4',
            'version' => '1.4.4.0',
            'reference' => '17082e780752d346c2db12ef5d6bee8e835e399c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/ignition' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '889bf1dfa59e161590f677728b47bf4a6893983b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/ignition',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/invade' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => '7b20a25486de69198e402da20dc924d8bcc8024a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/invade',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-ignition' => array(
            'pretty_version' => '2.5.1',
            'version' => '2.5.1.0',
            'reference' => '0c864b3cbd66ce67a2096c5f743e07ce8f1d6ab9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-ignition',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-package-tools' => array(
            'pretty_version' => '1.16.4',
            'version' => '1.16.4.0',
            'reference' => 'ddf678e78d7f8b17e5cdd99c0c3413a4a6592e53',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-package-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-permission' => array(
            'pretty_version' => '5.11.1',
            'version' => '5.11.1.0',
            'reference' => '7090824cca57e693b880ce3aaf7ef78362e28bbd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-permission',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-sluggable' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'a44afe6f317959bcfdadcec3148486859fd5c1f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-sluggable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-translatable' => array(
            'pretty_version' => '6.6.2',
            'version' => '6.6.2.0',
            'reference' => '529b4e89ad0b0982d9c635696260661d1cf2612c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-translatable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-welcome-notification' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'reference' => 'd8b92f0fe177cfb94f0319e46589939a8700a31b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-welcome-notification',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/opening-hours' => array(
            'pretty_version' => '2.41.0',
            'version' => '********',
            'reference' => '2113e289b16340b24ea0743a7179ebc9dcbe5e64',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/opening-hours',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v10.21.0',
            'version' => '*********',
            'reference' => 'b4ab319731958077227fad1874a3671458c5d593',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => 'a2708a5da5c87d1d0d52937bdeac625df659e11f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.0.3',
            'version' => '*******',
            'reference' => 'ec60a4edf94e63b0556b6a0888548bb400a3a3be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '*******',
            'reference' => '7c3aff79d10325257a001fcf92d991f24fc967cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => '64db1c1802e3a4557e37ba33031ac39f452ac5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.0.3',
            'version' => '*******',
            'reference' => '834c28d533dd0636f910909d01b9ff45cc094b5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.4.2',
            'version' => '3.4.2.0',
            'reference' => '4e64b49bf370ade88e567de29465762e316e4224',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'reference' => '11d736e97f116ac375a81f96e662911a34cd50ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/html-sanitizer' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => 'a8543ad56bc5250378ca44bb3988516fcb073c5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/html-sanitizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.4',
            'version' => '6.4.4.0',
            'reference' => 'ebc713bc6e6f4b53f46539fc158be85dfcd77304',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => '060038863743fd0cd982be06acecccf246d35653',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => '677f34a6f4b4559e08acf73ae0aec460479e5859',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => '14762b86918823cb42e3558cdcca62e58b5227fe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '32a9da87d7b3245e09ac426c83d334ae9f06f80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-icu' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '07094a28851a49107f3ab4f9120ca2975a64b6e1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-icu',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'a287ed7475f85bf6f61890146edbc932c0fff919',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '861391a8da9a04cbad2d232ddd9e4893220d6e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '86fcae159633351e5fd145d1c47de6c528f8caff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '3abdd21b0ceaa3000ee950097bc3cf9efc137853',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.4',
            'version' => '6.4.4.0',
            'reference' => '710e27879e9be3395de2b98da3f52a946039f297',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v7.0.6',
            'version' => '7.0.6.0',
            'reference' => 'fbc500cbcb64d3ea7469f019ab7aa717b320ff3f',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => 'f2591fd1f8c6e3734656b5d6b3829e8bf81f507c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.4.2',
            'version' => '3.4.2.0',
            'reference' => '11bbf19a0fb7b36345861e85c5768844c552906e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => 'f5832521b998b0bec40bee688ad5de98d4cf111b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.4',
            'version' => '6.4.4.0',
            'reference' => 'bce6a5a78e94566641b2594d17e48b0da3184a8e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.4.2',
            'version' => '3.4.2.0',
            'reference' => '43810bdb2ddb5400e5c5e778e27b210a0ca83b6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v6.4.3',
            'version' => '6.4.3.0',
            'reference' => '1d31267211cc3a2fff32bcfc7c1818dac41b6fc0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.4.6',
            'version' => '*******',
            'reference' => '95bd2706a97fb875185b51ecaa6112ec184233d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.0.3',
            'version' => '*******',
            'reference' => '2d4fca631c00700597e9442a0b2451ce234513d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.2.7',
            'version' => '2.2.7.0',
            'reference' => '83ee6f38df0a63106a9e4536e3060458b74ccedb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tinymce/tinymce' => array(
            'pretty_version' => '6.8.3',
            'version' => '6.8.3.0',
            'reference' => '01d1959b1200e0b872ea078e59ea5abfb5c54100',
            'type' => 'component',
            'install_path' => __DIR__ . '/../tinymce/tinymce',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.0',
            'version' => '5.6.0.0',
            'reference' => '2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'willvincent/laravel-rateable' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '9ea34cfa63fc2d88957ad98f22dd9201c8bb89e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../willvincent/laravel-rateable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
