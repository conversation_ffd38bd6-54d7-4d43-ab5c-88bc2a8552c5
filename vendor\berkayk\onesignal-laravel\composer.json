{"name": "berka<PERSON>/onesignal-laravel", "description": "OneSignal Push Wrapper <PERSON>", "keywords": ["onesignal", "webpush", "push", "laravel", "laravel 5"], "type": "library", "require": {"php": ">=5.4.0", "guzzlehttp/guzzle": "^6.2|^7.4.1|^7.2", "illuminate/support": "~5.5|~6.0|~7.0|~8.0|~9.0|^10.0|^11.0", "symfony/psr-http-message-bridge": "1.*|2.*|^7.0"}, "require-dev": {"vlucas/phpdotenv": "^2.2|^5.5"}, "autoload": {"psr-4": {"Berkayk\\OneSignal\\": "src/"}}, "extra": {"laravel": {"providers": ["Berkayk\\OneSignal\\OneSignalServiceProvider"], "aliases": {"OneSignal": "Berkayk\\OneSignal\\OneSignalFacade"}}}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://berkaykaya.com"}, {"name": "Maykonn Welington Candido", "email": "<EMAIL>"}], "minimum-stability": "dev", "prefer-stable": true}