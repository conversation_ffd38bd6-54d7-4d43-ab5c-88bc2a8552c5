<?php
// This file was auto-generated from sdk-root/src/data/redshift-serverless/2021-04-21/paginators-1.json
return [ 'pagination' => [ 'ListCustomDomainAssociations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'associations', ], 'ListEndpointAccess' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'endpoints', ], 'ListNamespaces' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'namespaces', ], 'ListRecoveryPoints' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'recoveryPoints', ], 'ListScheduledActions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'scheduledActions', ], 'ListSnapshotCopyConfigurations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'snapshotCopyConfigurations', ], 'ListSnapshots' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'snapshots', ], 'ListTableRestoreStatus' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'tableRestoreStatuses', ], 'ListUsageLimits' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'usageLimits', ], 'ListWorkgroups' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'workgroups', ], ],];
