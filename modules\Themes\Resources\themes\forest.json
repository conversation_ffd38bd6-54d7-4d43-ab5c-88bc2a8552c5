{"name": "Forest", "file": "forest.json", "description": "Forest theme", "colors": {"primary": "#0eb770", "secondary": "#f2ffec", "tertiary": "#ffffff"}, "css_content": "/* Fonts */\n@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');\n\n/* Root Variables */\n:root {\n    --primary: #0eb770;\n}\n\n/* Body Styles */\nbody {\n    font-family: \"Inter\", sans-serif !important;\n    background: #f2ffec !important;\n}\n\n/* Navbar Styles */\n.navbar-nav {\n    font-weight: bold;\n    padding-left: 10px;\n    padding-right: 10px;\n}\n\n.navbar-nav i.ni {\n    color: var(--primary) !important;\n}\n\n.navbar-nav .nav-link {\n    color: rgba(0,0,0,0.8) !important;\n}\n\n.navbar-nav .nav-link.active {\n    background-color: var(--primary) !important;\n    border-radius: 2rem;\n    margin-left: 0px;\n    margin-right: 10px;\n}\n\n.navbar-nav .nav-link.active:before {\n    border-left: 0 !important;\n}\n\n.navbar-nav .nav-link.active,\n.navbar-nav .nav-link.active .ni {\n    color: #ffffff !important; \n}\n\n.navbar-brand {\n    padding-bottom: 0 !important;\n}\n\n.navbar-brand-img {\n    max-height: 4rem !important;\n}\n\n/* Header Styles */\n.header .header-body .btn.btn-outline-primary:hover {\n    color: var(--primary) !important;\n    border-color: var(--primary) !important;\n    background: #ffffff !important;\n    box-shadow: none !important;\n}\n\n.header .header-body .btn.btn-outline-primary {\n    margin-top: 10px;\n    min-width: 180px;\n    border-color: var(--primary) !important;\n    background-color: var(--primary) !important;\n    color: #ffffff !important;\n}\n\n.header .header-body .card-stats {\n    min-height: 142px !important;\n    box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;\n}\n\n/* Card Styles */\n.card {\n    width: unset !important;\n}\n\n.card.shadow-lg,\n.card.shadow {\n    box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px !important;\n}\n\n.shadow {\n    box-shadow: none !important;\n}\n\n/* Navigation Pills */\n.nav-pills .nav-link {\n    border: 1px solid var(--primary);\n    color: var(--primary) !important;\n}\n\n.btn-primary,\n.nav-pills .nav-link.active {\n    background-color: var(--primary) !important;\n    color: #ffffff !important;\n    box-shadow: none !important;\n    border: 0 !important;\n}\n\n/* Form Controls */\n.form-control {\n    box-shadow: none !important;\n    padding: 0 !important;\n    border-radius: 0 !important;\n    border: 0 !important;\n    border-bottom: 1px solid grey !important;\n}\n\ntextarea.form-control {\n    padding: 1rem !important;\n}\n\n/* Preview Element */\n#previewElement,\n#previewElement .card {\n    min-width: unset !important;\n}\n\n/* Gradient Styles */\n.bg-gradient-primary,\n.bg-gradient-info {\n    background: var(--primary) !important;\n}\n\n.bg-gradient-success {\n    background: var(--primary) !important;\n}\n\n.bg-gradient-success .avatar {\n    color: var(--primary) !important;\n    background-color: #ffffff !important;\n}\n\n/* Avatar Styles */\n.avatar.bg-gradient-primary {\n    min-height: 48px;\n    min-width: 48px;\n}\n\n/* Chat List Styles */\n#chatList .card-body .d-block {\n    cursor: pointer;\n}\n\n#chatList .nav-pills .nav-link {\n    box-shadow: none !important;\n}\n\n#chatList .nav-pills .nav-item {\n    padding-right: 0.5rem !important;\n}\n\n/* Nav Wrapper Styles */\n.nav-wrapper .nav-item .nav-link {\n    color: var(--primary) !important;\n    box-shadow: none !important;\n}\n\n.nav-wrapper .nav-item .nav-link.active {\n    color: #ffffff !important;\n}\n\n/* Media Queries */\n@media only screen and (min-width: 768px) {\n    .main-content .navbar-top {\n        background: #ffffff !important;\n    }\n}"}