<?php
// This file was auto-generated from sdk-root/src/data/guardduty/2017-11-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-28', 'endpointPrefix' => 'guardduty', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon GuardDuty', 'serviceId' => 'GuardDuty', 'signatureVersion' => 'v4', 'signingName' => 'guardduty', 'uid' => 'guardduty-2017-11-28', ], 'operations' => [ 'AcceptAdministratorInvitation' => [ 'name' => 'AcceptAdministratorInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/administrator', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptAdministratorInvitationRequest', ], 'output' => [ 'shape' => 'AcceptAdministratorInvitationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'AcceptInvitation' => [ 'name' => 'AcceptInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/master', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptInvitationRequest', ], 'output' => [ 'shape' => 'AcceptInvitationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use AcceptAdministratorInvitation instead', ], 'ArchiveFindings' => [ 'name' => 'ArchiveFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/archive', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ArchiveFindingsRequest', ], 'output' => [ 'shape' => 'ArchiveFindingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateDetector' => [ 'name' => 'CreateDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDetectorRequest', ], 'output' => [ 'shape' => 'CreateDetectorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateFilter' => [ 'name' => 'CreateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/filter', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFilterRequest', ], 'output' => [ 'shape' => 'CreateFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateIPSet' => [ 'name' => 'CreateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/ipset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIPSetRequest', ], 'output' => [ 'shape' => 'CreateIPSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateMembers' => [ 'name' => 'CreateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMembersRequest', ], 'output' => [ 'shape' => 'CreateMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreatePublishingDestination' => [ 'name' => 'CreatePublishingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/publishingDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePublishingDestinationRequest', ], 'output' => [ 'shape' => 'CreatePublishingDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateSampleFindings' => [ 'name' => 'CreateSampleFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSampleFindingsRequest', ], 'output' => [ 'shape' => 'CreateSampleFindingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateThreatIntelSet' => [ 'name' => 'CreateThreatIntelSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/threatintelset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateThreatIntelSetRequest', ], 'output' => [ 'shape' => 'CreateThreatIntelSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeclineInvitations' => [ 'name' => 'DeclineInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitation/decline', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeclineInvitationsRequest', ], 'output' => [ 'shape' => 'DeclineInvitationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteDetector' => [ 'name' => 'DeleteDetector', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector/{detectorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDetectorRequest', ], 'output' => [ 'shape' => 'DeleteDetectorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteFilter' => [ 'name' => 'DeleteFilter', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector/{detectorId}/filter/{filterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFilterRequest', ], 'output' => [ 'shape' => 'DeleteFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteIPSet' => [ 'name' => 'DeleteIPSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector/{detectorId}/ipset/{ipSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIPSetRequest', ], 'output' => [ 'shape' => 'DeleteIPSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteInvitations' => [ 'name' => 'DeleteInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitation/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInvitationsRequest', ], 'output' => [ 'shape' => 'DeleteInvitationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteMembers' => [ 'name' => 'DeleteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMembersRequest', ], 'output' => [ 'shape' => 'DeleteMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeletePublishingDestination' => [ 'name' => 'DeletePublishingDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector/{detectorId}/publishingDestination/{destinationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePublishingDestinationRequest', ], 'output' => [ 'shape' => 'DeletePublishingDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteThreatIntelSet' => [ 'name' => 'DeleteThreatIntelSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/detector/{detectorId}/threatintelset/{threatIntelSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteThreatIntelSetRequest', ], 'output' => [ 'shape' => 'DeleteThreatIntelSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeMalwareScans' => [ 'name' => 'DescribeMalwareScans', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/malware-scans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMalwareScansRequest', ], 'output' => [ 'shape' => 'DescribeMalwareScansResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribePublishingDestination' => [ 'name' => 'DescribePublishingDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/publishingDestination/{destinationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePublishingDestinationRequest', ], 'output' => [ 'shape' => 'DescribePublishingDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DisableOrganizationAdminAccount' => [ 'name' => 'DisableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/admin/disable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DisassociateFromAdministratorAccount' => [ 'name' => 'DisassociateFromAdministratorAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/administrator/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateFromAdministratorAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DisassociateFromMasterAccount' => [ 'name' => 'DisassociateFromMasterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/master/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateFromMasterAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use DisassociateFromAdministratorAccount instead', ], 'DisassociateMembers' => [ 'name' => 'DisassociateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateMembersRequest', ], 'output' => [ 'shape' => 'DisassociateMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'EnableOrganizationAdminAccount' => [ 'name' => 'EnableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/admin/enable', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetAdministratorAccount' => [ 'name' => 'GetAdministratorAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/administrator', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAdministratorAccountRequest', ], 'output' => [ 'shape' => 'GetAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetCoverageStatistics' => [ 'name' => 'GetCoverageStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/coverage/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCoverageStatisticsRequest', ], 'output' => [ 'shape' => 'GetCoverageStatisticsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetDetector' => [ 'name' => 'GetDetector', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDetectorRequest', ], 'output' => [ 'shape' => 'GetDetectorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetFilter' => [ 'name' => 'GetFilter', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/filter/{filterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFilterRequest', ], 'output' => [ 'shape' => 'GetFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetFindings' => [ 'name' => 'GetFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsRequest', ], 'output' => [ 'shape' => 'GetFindingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetFindingsStatistics' => [ 'name' => 'GetFindingsStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsStatisticsRequest', ], 'output' => [ 'shape' => 'GetFindingsStatisticsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetIPSet' => [ 'name' => 'GetIPSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/ipset/{ipSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIPSetRequest', ], 'output' => [ 'shape' => 'GetIPSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetInvitationsCount' => [ 'name' => 'GetInvitationsCount', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitation/count', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvitationsCountRequest', ], 'output' => [ 'shape' => 'GetInvitationsCountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetMalwareScanSettings' => [ 'name' => 'GetMalwareScanSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/malware-scan-settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMalwareScanSettingsRequest', ], 'output' => [ 'shape' => 'GetMalwareScanSettingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetMasterAccount' => [ 'name' => 'GetMasterAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/master', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMasterAccountRequest', ], 'output' => [ 'shape' => 'GetMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This operation is deprecated, use GetAdministratorAccount instead', ], 'GetMemberDetectors' => [ 'name' => 'GetMemberDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/detector/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMemberDetectorsRequest', ], 'output' => [ 'shape' => 'GetMemberDetectorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetMembers' => [ 'name' => 'GetMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMembersRequest', ], 'output' => [ 'shape' => 'GetMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetOrganizationStatistics' => [ 'name' => 'GetOrganizationStatistics', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/statistics', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetOrganizationStatisticsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetRemainingFreeTrialDays' => [ 'name' => 'GetRemainingFreeTrialDays', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/freeTrial/daysRemaining', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRemainingFreeTrialDaysRequest', ], 'output' => [ 'shape' => 'GetRemainingFreeTrialDaysResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetThreatIntelSet' => [ 'name' => 'GetThreatIntelSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/threatintelset/{threatIntelSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetThreatIntelSetRequest', ], 'output' => [ 'shape' => 'GetThreatIntelSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetUsageStatistics' => [ 'name' => 'GetUsageStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/usage/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUsageStatisticsRequest', ], 'output' => [ 'shape' => 'GetUsageStatisticsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'InviteMembers' => [ 'name' => 'InviteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/invite', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InviteMembersRequest', ], 'output' => [ 'shape' => 'InviteMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListCoverage' => [ 'name' => 'ListCoverage', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/coverage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCoverageRequest', ], 'output' => [ 'shape' => 'ListCoverageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListDetectors' => [ 'name' => 'ListDetectors', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDetectorsRequest', ], 'output' => [ 'shape' => 'ListDetectorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListFilters' => [ 'name' => 'ListFilters', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/filter', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFiltersRequest', ], 'output' => [ 'shape' => 'ListFiltersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListIPSets' => [ 'name' => 'ListIPSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/ipset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIPSetsRequest', ], 'output' => [ 'shape' => 'ListIPSetsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListInvitations' => [ 'name' => 'ListInvitations', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvitationsRequest', ], 'output' => [ 'shape' => 'ListInvitationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/member', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListOrganizationAdminAccounts' => [ 'name' => 'ListOrganizationAdminAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOrganizationAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListOrganizationAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListPublishingDestinations' => [ 'name' => 'ListPublishingDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/publishingDestination', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPublishingDestinationsRequest', ], 'output' => [ 'shape' => 'ListPublishingDestinationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListThreatIntelSets' => [ 'name' => 'ListThreatIntelSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/detector/{detectorId}/threatintelset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListThreatIntelSetsRequest', ], 'output' => [ 'shape' => 'ListThreatIntelSetsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'StartMalwareScan' => [ 'name' => 'StartMalwareScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/malware-scan/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMalwareScanRequest', ], 'output' => [ 'shape' => 'StartMalwareScanResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'StartMonitoringMembers' => [ 'name' => 'StartMonitoringMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMonitoringMembersRequest', ], 'output' => [ 'shape' => 'StartMonitoringMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'StopMonitoringMembers' => [ 'name' => 'StopMonitoringMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopMonitoringMembersRequest', ], 'output' => [ 'shape' => 'StopMonitoringMembersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UnarchiveFindings' => [ 'name' => 'UnarchiveFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/unarchive', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UnarchiveFindingsRequest', ], 'output' => [ 'shape' => 'UnarchiveFindingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateDetector' => [ 'name' => 'UpdateDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDetectorRequest', ], 'output' => [ 'shape' => 'UpdateDetectorResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateFilter' => [ 'name' => 'UpdateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/filter/{filterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFilterRequest', ], 'output' => [ 'shape' => 'UpdateFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateFindingsFeedback' => [ 'name' => 'UpdateFindingsFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/findings/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFindingsFeedbackRequest', ], 'output' => [ 'shape' => 'UpdateFindingsFeedbackResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateIPSet' => [ 'name' => 'UpdateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/ipset/{ipSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIPSetRequest', ], 'output' => [ 'shape' => 'UpdateIPSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateMalwareScanSettings' => [ 'name' => 'UpdateMalwareScanSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/malware-scan-settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMalwareScanSettingsRequest', ], 'output' => [ 'shape' => 'UpdateMalwareScanSettingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateMemberDetectors' => [ 'name' => 'UpdateMemberDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/member/detector/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMemberDetectorsRequest', ], 'output' => [ 'shape' => 'UpdateMemberDetectorsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/admin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdatePublishingDestination' => [ 'name' => 'UpdatePublishingDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/publishingDestination/{destinationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePublishingDestinationRequest', ], 'output' => [ 'shape' => 'UpdatePublishingDestinationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateThreatIntelSet' => [ 'name' => 'UpdateThreatIntelSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/detector/{detectorId}/threatintelset/{threatIntelSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateThreatIntelSetRequest', ], 'output' => [ 'shape' => 'UpdateThreatIntelSetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], ], 'shapes' => [ 'AcceptAdministratorInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AdministratorId', 'InvitationId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AdministratorId' => [ 'shape' => 'String', 'locationName' => 'administratorId', ], 'InvitationId' => [ 'shape' => 'String', 'locationName' => 'invitationId', ], ], ], 'AcceptAdministratorInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AcceptInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'MasterId', 'InvitationId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MasterId' => [ 'shape' => 'String', 'locationName' => 'masterId', ], 'InvitationId' => [ 'shape' => 'String', 'locationName' => 'invitationId', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This input is deprecated, use AcceptAdministratorInvitationRequest instead', ], 'AcceptInvitationResponse' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This output is deprecated, use AcceptAdministratorInvitationResponse instead', ], 'AccessControlList' => [ 'type' => 'structure', 'members' => [ 'AllowsPublicReadAccess' => [ 'shape' => 'Boolean', 'locationName' => 'allowsPublicReadAccess', ], 'AllowsPublicWriteAccess' => [ 'shape' => 'Boolean', 'locationName' => 'allowsPublicWriteAccess', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Type' => [ 'shape' => 'String', 'locationName' => '__type', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessKeyDetails' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'String', 'locationName' => 'accessKeyId', ], 'PrincipalId' => [ 'shape' => 'String', 'locationName' => 'principalId', ], 'UserName' => [ 'shape' => 'String', 'locationName' => 'userName', ], 'UserType' => [ 'shape' => 'String', 'locationName' => 'userType', ], ], ], 'AccountDetail' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Email', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'Email' => [ 'shape' => 'Email', 'locationName' => 'email', ], ], ], 'AccountDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountDetail', ], 'max' => 50, 'min' => 1, ], 'AccountFreeTrialInfo' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'String', 'locationName' => 'accountId', ], 'DataSources' => [ 'shape' => 'DataSourcesFreeTrial', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'FreeTrialFeatureConfigurationsResults', 'locationName' => 'features', ], ], ], 'AccountFreeTrialInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountFreeTrialInfo', ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, ], 'AccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 50, 'min' => 1, ], 'AccountLevelPermissions' => [ 'type' => 'structure', 'members' => [ 'BlockPublicAccess' => [ 'shape' => 'BlockPublicAccess', 'locationName' => 'blockPublicAccess', ], ], ], 'Action' => [ 'type' => 'structure', 'members' => [ 'ActionType' => [ 'shape' => 'String', 'locationName' => 'actionType', ], 'AwsApiCallAction' => [ 'shape' => 'AwsApiCallAction', 'locationName' => 'awsApiCallAction', ], 'DnsRequestAction' => [ 'shape' => 'DnsRequestAction', 'locationName' => 'dnsRequestAction', ], 'NetworkConnectionAction' => [ 'shape' => 'NetworkConnectionAction', 'locationName' => 'networkConnectionAction', ], 'PortProbeAction' => [ 'shape' => 'PortProbeAction', 'locationName' => 'portProbeAction', ], 'KubernetesApiCallAction' => [ 'shape' => 'KubernetesApiCallAction', 'locationName' => 'kubernetesApiCallAction', ], 'RdsLoginAttemptAction' => [ 'shape' => 'RdsLoginAttemptAction', 'locationName' => 'rdsLoginAttemptAction', ], 'KubernetesPermissionCheckedDetails' => [ 'shape' => 'KubernetesPermissionCheckedDetails', 'locationName' => 'kubernetesPermissionCheckedDetails', ], 'KubernetesRoleBindingDetails' => [ 'shape' => 'KubernetesRoleBindingDetails', 'locationName' => 'kubernetesRoleBindingDetails', ], 'KubernetesRoleDetails' => [ 'shape' => 'KubernetesRoleDetails', 'locationName' => 'kubernetesRoleDetails', ], ], ], 'AddonDetails' => [ 'type' => 'structure', 'members' => [ 'AddonVersion' => [ 'shape' => 'String', 'locationName' => 'addonVersion', ], 'AddonStatus' => [ 'shape' => 'String', 'locationName' => 'addonStatus', ], ], ], 'AdminAccount' => [ 'type' => 'structure', 'members' => [ 'AdminAccountId' => [ 'shape' => 'String', 'locationName' => 'adminAccountId', ], 'AdminStatus' => [ 'shape' => 'AdminStatus', 'locationName' => 'adminStatus', ], ], ], 'AdminAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdminAccount', ], 'max' => 1, 'min' => 0, ], 'AdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLE_IN_PROGRESS', ], 'max' => 300, 'min' => 1, ], 'Administrator' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'InvitationId' => [ 'shape' => 'String', 'locationName' => 'invitationId', ], 'RelationshipStatus' => [ 'shape' => 'String', 'locationName' => 'relationshipStatus', ], 'InvitedAt' => [ 'shape' => 'String', 'locationName' => 'invitedAt', ], ], ], 'AffectedResources' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'AgentDetails' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'String', 'locationName' => 'version', ], ], ], 'Anomaly' => [ 'type' => 'structure', 'members' => [ 'Profiles' => [ 'shape' => 'AnomalyProfiles', 'locationName' => 'profiles', ], 'Unusual' => [ 'shape' => 'AnomalyUnusual', 'locationName' => 'unusual', ], ], ], 'AnomalyObject' => [ 'type' => 'structure', 'members' => [ 'ProfileType' => [ 'shape' => 'ProfileType', 'locationName' => 'profileType', ], 'ProfileSubtype' => [ 'shape' => 'ProfileSubtype', 'locationName' => 'profileSubtype', ], 'Observations' => [ 'shape' => 'Observations', 'locationName' => 'observations', ], ], ], 'AnomalyProfileFeatureObjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyObject', ], ], 'AnomalyProfileFeatures' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AnomalyProfileFeatureObjects', ], ], 'AnomalyProfiles' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AnomalyProfileFeatures', ], ], 'AnomalyUnusual' => [ 'type' => 'structure', 'members' => [ 'Behavior' => [ 'shape' => 'Behavior', 'locationName' => 'behavior', ], ], ], 'AnomalyUnusualBehaviorFeature' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AnomalyObject', ], ], 'ArchiveFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FindingIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingIds' => [ 'shape' => 'FindingIds', 'locationName' => 'findingIds', ], ], ], 'ArchiveFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'AutoEnableMembers' => [ 'type' => 'string', 'enum' => [ 'NEW', 'ALL', 'NONE', ], ], 'AwsApiCallAction' => [ 'type' => 'structure', 'members' => [ 'Api' => [ 'shape' => 'String', 'locationName' => 'api', ], 'CallerType' => [ 'shape' => 'String', 'locationName' => 'callerType', ], 'DomainDetails' => [ 'shape' => 'DomainDetails', 'locationName' => 'domainDetails', ], 'ErrorCode' => [ 'shape' => 'String', 'locationName' => 'errorCode', ], 'UserAgent' => [ 'shape' => 'String', 'locationName' => 'userAgent', ], 'RemoteIpDetails' => [ 'shape' => 'RemoteIpDetails', 'locationName' => 'remoteIpDetails', ], 'ServiceName' => [ 'shape' => 'String', 'locationName' => 'serviceName', ], 'RemoteAccountDetails' => [ 'shape' => 'RemoteAccountDetails', 'locationName' => 'remoteAccountDetails', ], 'AffectedResources' => [ 'shape' => 'AffectedResources', 'locationName' => 'affectedResources', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Type' => [ 'shape' => 'String', 'locationName' => '__type', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Behavior' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AnomalyUnusualBehaviorFeature', ], ], 'BlockPublicAccess' => [ 'type' => 'structure', 'members' => [ 'IgnorePublicAcls' => [ 'shape' => 'Boolean', 'locationName' => 'ignorePublicAcls', ], 'RestrictPublicBuckets' => [ 'shape' => 'Boolean', 'locationName' => 'restrictPublicBuckets', ], 'BlockPublicAcls' => [ 'shape' => 'Boolean', 'locationName' => 'blockPublicAcls', ], 'BlockPublicPolicy' => [ 'shape' => 'Boolean', 'locationName' => 'blockPublicPolicy', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BucketLevelPermissions' => [ 'type' => 'structure', 'members' => [ 'AccessControlList' => [ 'shape' => 'AccessControlList', 'locationName' => 'accessControlList', ], 'BucketPolicy' => [ 'shape' => 'BucketPolicy', 'locationName' => 'bucketPolicy', ], 'BlockPublicAccess' => [ 'shape' => 'BlockPublicAccess', 'locationName' => 'blockPublicAccess', ], ], ], 'BucketPolicy' => [ 'type' => 'structure', 'members' => [ 'AllowsPublicReadAccess' => [ 'shape' => 'Boolean', 'locationName' => 'allowsPublicReadAccess', ], 'AllowsPublicWriteAccess' => [ 'shape' => 'Boolean', 'locationName' => 'allowsPublicWriteAccess', ], ], ], 'City' => [ 'type' => 'structure', 'members' => [ 'CityName' => [ 'shape' => 'String', 'locationName' => 'cityName', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'CloudTrailConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], ], ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'Eq' => [ 'shape' => 'Eq', 'deprecated' => true, 'locationName' => 'eq', ], 'Neq' => [ 'shape' => 'Neq', 'deprecated' => true, 'locationName' => 'neq', ], 'Gt' => [ 'shape' => 'Integer', 'deprecated' => true, 'locationName' => 'gt', ], 'Gte' => [ 'shape' => 'Integer', 'deprecated' => true, 'locationName' => 'gte', ], 'Lt' => [ 'shape' => 'Integer', 'deprecated' => true, 'locationName' => 'lt', ], 'Lte' => [ 'shape' => 'Integer', 'deprecated' => true, 'locationName' => 'lte', ], 'Equals' => [ 'shape' => 'Equals', 'locationName' => 'equals', ], 'NotEquals' => [ 'shape' => 'NotEquals', 'locationName' => 'notEquals', ], 'GreaterThan' => [ 'shape' => 'Long', 'locationName' => 'greaterThan', ], 'GreaterThanOrEqual' => [ 'shape' => 'Long', 'locationName' => 'greaterThanOrEqual', ], 'LessThan' => [ 'shape' => 'Long', 'locationName' => 'lessThan', ], 'LessThanOrEqual' => [ 'shape' => 'Long', 'locationName' => 'lessThanOrEqual', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Type' => [ 'shape' => 'String', 'locationName' => '__type', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Container' => [ 'type' => 'structure', 'members' => [ 'ContainerRuntime' => [ 'shape' => 'String', 'locationName' => 'containerRuntime', ], 'Id' => [ 'shape' => 'String', 'locationName' => 'id', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Image' => [ 'shape' => 'String', 'locationName' => 'image', ], 'ImagePrefix' => [ 'shape' => 'String', 'locationName' => 'imagePrefix', ], 'VolumeMounts' => [ 'shape' => 'VolumeMounts', 'locationName' => 'volumeMounts', ], 'SecurityContext' => [ 'shape' => 'SecurityContext', 'locationName' => 'securityContext', ], ], ], 'ContainerInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'CoveredContainerInstances' => [ 'shape' => 'Long', 'locationName' => 'coveredContainerInstances', ], 'CompatibleContainerInstances' => [ 'shape' => 'Long', 'locationName' => 'compatibleContainerInstances', ], ], ], 'Containers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Container', ], ], 'CountByCoverageStatus' => [ 'type' => 'map', 'key' => [ 'shape' => 'CoverageStatus', ], 'value' => [ 'shape' => 'Long', ], ], 'CountByResourceType' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceType', ], 'value' => [ 'shape' => 'Long', ], ], 'CountBySeverity' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Integer', ], ], 'Country' => [ 'type' => 'structure', 'members' => [ 'CountryCode' => [ 'shape' => 'String', 'locationName' => 'countryCode', ], 'CountryName' => [ 'shape' => 'String', 'locationName' => 'countryName', ], ], ], 'CoverageEc2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'InstanceType' => [ 'shape' => 'String', 'locationName' => 'instanceType', ], 'ClusterArn' => [ 'shape' => 'String', 'locationName' => 'clusterArn', ], 'AgentDetails' => [ 'shape' => 'AgentDetails', 'locationName' => 'agentDetails', ], 'ManagementType' => [ 'shape' => 'ManagementType', 'locationName' => 'managementType', ], ], ], 'CoverageEcsClusterDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', 'locationName' => 'clusterName', ], 'FargateDetails' => [ 'shape' => 'FargateDetails', 'locationName' => 'fargateDetails', ], 'ContainerInstanceDetails' => [ 'shape' => 'ContainerInstanceDetails', 'locationName' => 'containerInstanceDetails', ], ], ], 'CoverageEksClusterDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', 'locationName' => 'clusterName', ], 'CoveredNodes' => [ 'shape' => 'Long', 'locationName' => 'coveredNodes', ], 'CompatibleNodes' => [ 'shape' => 'Long', 'locationName' => 'compatibleNodes', ], 'AddonDetails' => [ 'shape' => 'AddonDetails', 'locationName' => 'addonDetails', ], 'ManagementType' => [ 'shape' => 'ManagementType', 'locationName' => 'managementType', ], ], ], 'CoverageFilterCondition' => [ 'type' => 'structure', 'members' => [ 'Equals' => [ 'shape' => 'Equals', 'locationName' => 'equals', ], 'NotEquals' => [ 'shape' => 'NotEquals', 'locationName' => 'notEquals', ], ], ], 'CoverageFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'FilterCriterion' => [ 'shape' => 'CoverageFilterCriterionList', 'locationName' => 'filterCriterion', ], ], ], 'CoverageFilterCriterion' => [ 'type' => 'structure', 'members' => [ 'CriterionKey' => [ 'shape' => 'CoverageFilterCriterionKey', 'locationName' => 'criterionKey', ], 'FilterCondition' => [ 'shape' => 'CoverageFilterCondition', 'locationName' => 'filterCondition', ], ], ], 'CoverageFilterCriterionKey' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'CLUSTER_NAME', 'RESOURCE_TYPE', 'COVERAGE_STATUS', 'ADDON_VERSION', 'MANAGEMENT_TYPE', 'EKS_CLUSTER_NAME', 'ECS_CLUSTER_NAME', 'AGENT_VERSION', 'INSTANCE_ID', 'CLUSTER_ARN', ], ], 'CoverageFilterCriterionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageFilterCriterion', ], 'max' => 50, 'min' => 0, ], 'CoverageResource' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'String', 'locationName' => 'resourceId', ], 'DetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'detectorId', ], 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'ResourceDetails' => [ 'shape' => 'CoverageResourceDetails', 'locationName' => 'resourceDetails', ], 'CoverageStatus' => [ 'shape' => 'CoverageStatus', 'locationName' => 'coverageStatus', ], 'Issue' => [ 'shape' => 'String', 'locationName' => 'issue', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], ], ], 'CoverageResourceDetails' => [ 'type' => 'structure', 'members' => [ 'EksClusterDetails' => [ 'shape' => 'CoverageEksClusterDetails', 'locationName' => 'eksClusterDetails', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'locationName' => 'resourceType', ], 'EcsClusterDetails' => [ 'shape' => 'CoverageEcsClusterDetails', 'locationName' => 'ecsClusterDetails', ], 'Ec2InstanceDetails' => [ 'shape' => 'CoverageEc2InstanceDetails', 'locationName' => 'ec2InstanceDetails', ], ], ], 'CoverageResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageResource', ], ], 'CoverageSortCriteria' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'CoverageSortKey', 'locationName' => 'attributeName', ], 'OrderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'CoverageSortKey' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'CLUSTER_NAME', 'COVERAGE_STATUS', 'ISSUE', 'ADDON_VERSION', 'UPDATED_AT', 'EKS_CLUSTER_NAME', 'ECS_CLUSTER_NAME', 'INSTANCE_ID', ], ], 'CoverageStatistics' => [ 'type' => 'structure', 'members' => [ 'CountByResourceType' => [ 'shape' => 'CountByResourceType', 'locationName' => 'countByResourceType', ], 'CountByCoverageStatus' => [ 'shape' => 'CountByCoverageStatus', 'locationName' => 'countByCoverageStatus', ], ], ], 'CoverageStatisticsType' => [ 'type' => 'string', 'enum' => [ 'COUNT_BY_RESOURCE_TYPE', 'COUNT_BY_COVERAGE_STATUS', ], ], 'CoverageStatisticsTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageStatisticsType', ], ], 'CoverageStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'CreateDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'Enable', ], 'members' => [ 'Enable' => [ 'shape' => 'Boolean', 'locationName' => 'enable', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'locationName' => 'clientToken', ], 'FindingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'DataSources' => [ 'shape' => 'DataSourceConfigurations', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'Features' => [ 'shape' => 'DetectorFeatureConfigurations', 'locationName' => 'features', ], ], ], 'CreateDetectorResponse' => [ 'type' => 'structure', 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'detectorId', ], 'UnprocessedDataSources' => [ 'shape' => 'UnprocessedDataSourcesResult', 'locationName' => 'unprocessedDataSources', ], ], ], 'CreateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'Name', 'FindingCriteria', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'Name' => [ 'shape' => 'FilterName', 'locationName' => 'name', ], 'Description' => [ 'shape' => 'FilterDescription', 'locationName' => 'description', ], 'Action' => [ 'shape' => 'FilterAction', 'locationName' => 'action', ], 'Rank' => [ 'shape' => 'FilterRank', 'locationName' => 'rank', ], 'FindingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'locationName' => 'clientToken', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'FilterName', 'locationName' => 'name', ], ], ], 'CreateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'Name', 'Format', 'Location', 'Activate', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Format' => [ 'shape' => 'IpSetFormat', 'locationName' => 'format', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Activate' => [ 'shape' => 'Boolean', 'locationName' => 'activate', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'locationName' => 'clientToken', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateIPSetResponse' => [ 'type' => 'structure', 'required' => [ 'IpSetId', ], 'members' => [ 'IpSetId' => [ 'shape' => 'String', 'locationName' => 'ipSetId', ], ], ], 'CreateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountDetails', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountDetails' => [ 'shape' => 'AccountDetails', 'locationName' => 'accountDetails', ], ], ], 'CreateMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'CreatePublishingDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'DestinationType', 'DestinationProperties', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'DestinationType' => [ 'shape' => 'DestinationType', 'locationName' => 'destinationType', ], 'DestinationProperties' => [ 'shape' => 'DestinationProperties', 'locationName' => 'destinationProperties', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'locationName' => 'clientToken', ], ], ], 'CreatePublishingDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'DestinationId', ], 'members' => [ 'DestinationId' => [ 'shape' => 'String', 'locationName' => 'destinationId', ], ], ], 'CreateSampleFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingTypes' => [ 'shape' => 'FindingTypes', 'locationName' => 'findingTypes', ], ], ], 'CreateSampleFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateThreatIntelSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'Name', 'Format', 'Location', 'Activate', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Format' => [ 'shape' => 'ThreatIntelSetFormat', 'locationName' => 'format', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Activate' => [ 'shape' => 'Boolean', 'locationName' => 'activate', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'locationName' => 'clientToken', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateThreatIntelSetResponse' => [ 'type' => 'structure', 'required' => [ 'ThreatIntelSetId', ], 'members' => [ 'ThreatIntelSetId' => [ 'shape' => 'String', 'locationName' => 'threatIntelSetId', ], ], ], 'Criterion' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Condition', ], ], 'CriterionKey' => [ 'type' => 'string', 'enum' => [ 'EC2_INSTANCE_ARN', 'SCAN_ID', 'ACCOUNT_ID', 'GUARDDUTY_FINDING_ID', 'SCAN_START_TIME', 'SCAN_STATUS', 'SCAN_TYPE', ], ], 'DNSLogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], ], ], 'DataSource' => [ 'type' => 'string', 'enum' => [ 'FLOW_LOGS', 'CLOUD_TRAIL', 'DNS_LOGS', 'S3_LOGS', 'KUBERNETES_AUDIT_LOGS', 'EC2_MALWARE_SCAN', ], ], 'DataSourceConfigurations' => [ 'type' => 'structure', 'members' => [ 'S3Logs' => [ 'shape' => 'S3LogsConfiguration', 'locationName' => 's3Logs', ], 'Kubernetes' => [ 'shape' => 'KubernetesConfiguration', 'locationName' => 'kubernetes', ], 'MalwareProtection' => [ 'shape' => 'MalwareProtectionConfiguration', 'locationName' => 'malwareProtection', ], ], ], 'DataSourceConfigurationsResult' => [ 'type' => 'structure', 'required' => [ 'CloudTrail', 'DNSLogs', 'FlowLogs', 'S3Logs', ], 'members' => [ 'CloudTrail' => [ 'shape' => 'CloudTrailConfigurationResult', 'locationName' => 'cloudTrail', ], 'DNSLogs' => [ 'shape' => 'DNSLogsConfigurationResult', 'locationName' => 'dnsLogs', ], 'FlowLogs' => [ 'shape' => 'FlowLogsConfigurationResult', 'locationName' => 'flowLogs', ], 'S3Logs' => [ 'shape' => 'S3LogsConfigurationResult', 'locationName' => 's3Logs', ], 'Kubernetes' => [ 'shape' => 'KubernetesConfigurationResult', 'locationName' => 'kubernetes', ], 'MalwareProtection' => [ 'shape' => 'MalwareProtectionConfigurationResult', 'locationName' => 'malwareProtection', ], ], ], 'DataSourceFreeTrial' => [ 'type' => 'structure', 'members' => [ 'FreeTrialDaysRemaining' => [ 'shape' => 'Integer', 'locationName' => 'freeTrialDaysRemaining', ], ], ], 'DataSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], 'max' => 300, 'min' => 1, ], 'DataSourcesFreeTrial' => [ 'type' => 'structure', 'members' => [ 'CloudTrail' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 'cloudTrail', ], 'DnsLogs' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 'dnsLogs', ], 'FlowLogs' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 'flowLogs', ], 'S3Logs' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 's3Logs', ], 'Kubernetes' => [ 'shape' => 'KubernetesDataSourceFreeTrial', 'locationName' => 'kubernetes', ], 'MalwareProtection' => [ 'shape' => 'MalwareProtectionDataSourceFreeTrial', 'locationName' => 'malwareProtection', ], ], ], 'DeclineInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'DeclineInvitationsResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'DefaultServerSideEncryption' => [ 'type' => 'structure', 'members' => [ 'EncryptionType' => [ 'shape' => 'String', 'locationName' => 'encryptionType', ], 'KmsMasterKeyArn' => [ 'shape' => 'String', 'locationName' => 'kmsMasterKeyArn', ], ], ], 'DeleteDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], ], 'DeleteDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFilterRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FilterName', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FilterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'filterName', ], ], ], 'DeleteFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'IpSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'IpSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ipSetId', ], ], ], 'DeleteIPSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'DeleteInvitationsResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'DeleteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'DeleteMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'DeletePublishingDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'DestinationId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'DestinationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'destinationId', ], ], ], 'DeletePublishingDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteThreatIntelSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'ThreatIntelSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'ThreatIntelSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'threatIntelSetId', ], ], ], 'DeleteThreatIntelSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeMalwareScansRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'IntegerValueWithMax', 'locationName' => 'maxResults', ], 'FilterCriteria' => [ 'shape' => 'FilterCriteria', 'locationName' => 'filterCriteria', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'DescribeMalwareScansResponse' => [ 'type' => 'structure', 'required' => [ 'Scans', ], 'members' => [ 'Scans' => [ 'shape' => 'Scans', 'locationName' => 'scans', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'MemberAccountLimitReached', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use AutoEnableOrganizationMembers instead', 'locationName' => 'autoEnable', ], 'MemberAccountLimitReached' => [ 'shape' => 'Boolean', 'locationName' => 'memberAccountLimitReached', ], 'DataSources' => [ 'shape' => 'OrganizationDataSourceConfigurationsResult', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'OrganizationFeaturesConfigurationsResults', 'locationName' => 'features', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], 'AutoEnableOrganizationMembers' => [ 'shape' => 'AutoEnableMembers', 'locationName' => 'autoEnableOrganizationMembers', ], ], ], 'DescribePublishingDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'DestinationId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'DestinationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'destinationId', ], ], ], 'DescribePublishingDestinationResponse' => [ 'type' => 'structure', 'required' => [ 'DestinationId', 'DestinationType', 'Status', 'PublishingFailureStartTimestamp', 'DestinationProperties', ], 'members' => [ 'DestinationId' => [ 'shape' => 'String', 'locationName' => 'destinationId', ], 'DestinationType' => [ 'shape' => 'DestinationType', 'locationName' => 'destinationType', ], 'Status' => [ 'shape' => 'PublishingStatus', 'locationName' => 'status', ], 'PublishingFailureStartTimestamp' => [ 'shape' => 'Long', 'locationName' => 'publishingFailureStartTimestamp', ], 'DestinationProperties' => [ 'shape' => 'DestinationProperties', 'locationName' => 'destinationProperties', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'DestinationId', 'DestinationType', 'Status', ], 'members' => [ 'DestinationId' => [ 'shape' => 'String', 'locationName' => 'destinationId', ], 'DestinationType' => [ 'shape' => 'DestinationType', 'locationName' => 'destinationType', ], 'Status' => [ 'shape' => 'PublishingStatus', 'locationName' => 'status', ], ], ], 'DestinationProperties' => [ 'type' => 'structure', 'members' => [ 'DestinationArn' => [ 'shape' => 'String', 'locationName' => 'destinationArn', ], 'KmsKeyArn' => [ 'shape' => 'String', 'locationName' => 'kmsKeyArn', ], ], ], 'DestinationType' => [ 'type' => 'string', 'enum' => [ 'S3', ], 'max' => 300, 'min' => 1, ], 'Destinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], ], 'Detection' => [ 'type' => 'structure', 'members' => [ 'Anomaly' => [ 'shape' => 'Anomaly', 'locationName' => 'anomaly', ], ], ], 'DetectorAdditionalConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FeatureAdditionalConfiguration', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], ], ], 'DetectorAdditionalConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FeatureAdditionalConfiguration', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], ], ], 'DetectorAdditionalConfigurationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorAdditionalConfigurationResult', ], ], 'DetectorAdditionalConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorAdditionalConfiguration', ], ], 'DetectorFeature' => [ 'type' => 'string', 'enum' => [ 'S3_DATA_EVENTS', 'EKS_AUDIT_LOGS', 'EBS_MALWARE_PROTECTION', 'RDS_LOGIN_EVENTS', 'EKS_RUNTIME_MONITORING', 'LAMBDA_NETWORK_LOGS', 'RUNTIME_MONITORING', ], ], 'DetectorFeatureConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DetectorFeature', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'AdditionalConfiguration' => [ 'shape' => 'DetectorAdditionalConfigurations', 'locationName' => 'additionalConfiguration', ], ], ], 'DetectorFeatureConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DetectorFeatureResult', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], 'AdditionalConfiguration' => [ 'shape' => 'DetectorAdditionalConfigurationResults', 'locationName' => 'additionalConfiguration', ], ], ], 'DetectorFeatureConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorFeatureConfiguration', ], ], 'DetectorFeatureConfigurationsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorFeatureConfigurationResult', ], ], 'DetectorFeatureResult' => [ 'type' => 'string', 'enum' => [ 'FLOW_LOGS', 'CLOUD_TRAIL', 'DNS_LOGS', 'S3_DATA_EVENTS', 'EKS_AUDIT_LOGS', 'EBS_MALWARE_PROTECTION', 'RDS_LOGIN_EVENTS', 'EKS_RUNTIME_MONITORING', 'LAMBDA_NETWORK_LOGS', 'RUNTIME_MONITORING', ], ], 'DetectorId' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'DetectorIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorId', ], 'max' => 50, 'min' => 0, ], 'DetectorStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], 'max' => 300, 'min' => 1, ], 'DisableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'String', 'locationName' => 'adminAccountId', ], ], ], 'DisableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], ], 'DisassociateFromAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This input is deprecated, use DisassociateFromAdministratorAccountRequest instead', ], 'DisassociateFromMasterAccountResponse' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'This output is deprecated, use DisassociateFromAdministratorAccountResponse instead', ], 'DisassociateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'DisassociateMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'DnsRequestAction' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', 'locationName' => 'domain', ], 'Protocol' => [ 'shape' => 'String', 'locationName' => 'protocol', ], 'Blocked' => [ 'shape' => 'Boolean', 'locationName' => 'blocked', ], 'DomainWithSuffix' => [ 'shape' => 'String', 'locationName' => 'domainWithSuffix', ], ], ], 'DomainDetails' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', 'locationName' => 'domain', ], ], ], 'Double' => [ 'type' => 'double', ], 'EbsSnapshotPreservation' => [ 'type' => 'string', 'enum' => [ 'NO_RETENTION', 'RETENTION_WITH_FINDING', ], ], 'EbsVolumeDetails' => [ 'type' => 'structure', 'members' => [ 'ScannedVolumeDetails' => [ 'shape' => 'VolumeDetails', 'locationName' => 'scannedVolumeDetails', ], 'SkippedVolumeDetails' => [ 'shape' => 'VolumeDetails', 'locationName' => 'skippedVolumeDetails', ], ], ], 'EbsVolumeScanDetails' => [ 'type' => 'structure', 'members' => [ 'ScanId' => [ 'shape' => 'String', 'locationName' => 'scanId', ], 'ScanStartedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'scanStartedAt', ], 'ScanCompletedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'scanCompletedAt', ], 'TriggerFindingId' => [ 'shape' => 'String', 'locationName' => 'triggerFindingId', ], 'Sources' => [ 'shape' => 'Sources', 'locationName' => 'sources', ], 'ScanDetections' => [ 'shape' => 'ScanDetections', 'locationName' => 'scanDetections', ], 'ScanType' => [ 'shape' => 'ScanType', 'locationName' => 'scanType', ], ], ], 'EbsVolumesResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], 'Reason' => [ 'shape' => 'String', 'locationName' => 'reason', ], ], ], 'EcsClusterDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'Status' => [ 'shape' => 'String', 'locationName' => 'status', ], 'ActiveServicesCount' => [ 'shape' => 'Integer', 'locationName' => 'activeServicesCount', ], 'RegisteredContainerInstancesCount' => [ 'shape' => 'Integer', 'locationName' => 'registeredContainerInstancesCount', ], 'RunningTasksCount' => [ 'shape' => 'Integer', 'locationName' => 'runningTasksCount', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'TaskDetails' => [ 'shape' => 'EcsTaskDetails', 'locationName' => 'taskDetails', ], ], ], 'EcsTaskDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'DefinitionArn' => [ 'shape' => 'String', 'locationName' => 'definitionArn', ], 'Version' => [ 'shape' => 'String', 'locationName' => 'version', ], 'TaskCreatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'createdAt', ], 'StartedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'startedAt', ], 'StartedBy' => [ 'shape' => 'String', 'locationName' => 'startedBy', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Volumes' => [ 'shape' => 'Volumes', 'locationName' => 'volumes', ], 'Containers' => [ 'shape' => 'Containers', 'locationName' => 'containers', ], 'Group' => [ 'shape' => 'String', 'locationName' => 'group', ], ], ], 'EksClusterDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'VpcId' => [ 'shape' => 'String', 'locationName' => 'vpcId', ], 'Status' => [ 'shape' => 'String', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'CreatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'createdAt', ], ], ], 'Email' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'EnableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'String', 'locationName' => 'adminAccountId', ], ], ], 'EnableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'Eq' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Equals' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Evidence' => [ 'type' => 'structure', 'members' => [ 'ThreatIntelligenceDetails' => [ 'shape' => 'ThreatIntelligenceDetails', 'locationName' => 'threatIntelligenceDetails', ], ], ], 'FargateDetails' => [ 'type' => 'structure', 'members' => [ 'Issues' => [ 'shape' => 'Issues', 'locationName' => 'issues', ], 'ManagementType' => [ 'shape' => 'ManagementType', 'locationName' => 'managementType', ], ], ], 'FeatureAdditionalConfiguration' => [ 'type' => 'string', 'enum' => [ 'EKS_ADDON_MANAGEMENT', 'ECS_FARGATE_AGENT_MANAGEMENT', 'EC2_AGENT_MANAGEMENT', ], ], 'FeatureStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Feedback' => [ 'type' => 'string', 'enum' => [ 'USEFUL', 'NOT_USEFUL', ], ], 'FilePaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScanFilePath', ], ], 'FilterAction' => [ 'type' => 'string', 'enum' => [ 'NOOP', 'ARCHIVE', ], 'max' => 300, 'min' => 1, ], 'FilterCondition' => [ 'type' => 'structure', 'members' => [ 'EqualsValue' => [ 'shape' => 'NonEmptyString', 'locationName' => 'equalsValue', ], 'GreaterThan' => [ 'shape' => 'LongValue', 'locationName' => 'greaterThan', ], 'LessThan' => [ 'shape' => 'LongValue', 'locationName' => 'lessThan', ], ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'FilterCriterion' => [ 'shape' => 'FilterCriterionList', 'locationName' => 'filterCriterion', ], ], ], 'FilterCriterion' => [ 'type' => 'structure', 'members' => [ 'CriterionKey' => [ 'shape' => 'CriterionKey', 'locationName' => 'criterionKey', ], 'FilterCondition' => [ 'shape' => 'FilterCondition', 'locationName' => 'filterCondition', ], ], ], 'FilterCriterionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterCriterion', ], 'max' => 1, 'min' => 0, ], 'FilterDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 0, ], 'FilterName' => [ 'type' => 'string', 'max' => 64, 'min' => 3, ], 'FilterNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterName', ], 'max' => 50, 'min' => 0, ], 'FilterRank' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Arn', 'CreatedAt', 'Id', 'Region', 'Resource', 'SchemaVersion', 'Severity', 'Type', 'UpdatedAt', ], 'members' => [ 'AccountId' => [ 'shape' => 'String', 'locationName' => 'accountId', ], 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'Confidence' => [ 'shape' => 'Double', 'locationName' => 'confidence', ], 'CreatedAt' => [ 'shape' => 'String', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Id' => [ 'shape' => 'String', 'locationName' => 'id', ], 'Partition' => [ 'shape' => 'String', 'locationName' => 'partition', ], 'Region' => [ 'shape' => 'String', 'locationName' => 'region', ], 'Resource' => [ 'shape' => 'Resource', 'locationName' => 'resource', ], 'SchemaVersion' => [ 'shape' => 'String', 'locationName' => 'schemaVersion', ], 'Service' => [ 'shape' => 'Service', 'locationName' => 'service', ], 'Severity' => [ 'shape' => 'Double', 'locationName' => 'severity', ], 'Title' => [ 'shape' => 'String', 'locationName' => 'title', ], 'Type' => [ 'shape' => 'FindingType', 'locationName' => 'type', ], 'UpdatedAt' => [ 'shape' => 'String', 'locationName' => 'updatedAt', ], ], ], 'FindingCriteria' => [ 'type' => 'structure', 'members' => [ 'Criterion' => [ 'shape' => 'Criterion', 'locationName' => 'criterion', ], ], ], 'FindingId' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'FindingIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingId', ], 'max' => 50, 'min' => 0, ], 'FindingPublishingFrequency' => [ 'type' => 'string', 'enum' => [ 'FIFTEEN_MINUTES', 'ONE_HOUR', 'SIX_HOURS', ], ], 'FindingStatisticType' => [ 'type' => 'string', 'enum' => [ 'COUNT_BY_SEVERITY', ], ], 'FindingStatisticTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingStatisticType', ], 'max' => 10, 'min' => 0, ], 'FindingStatistics' => [ 'type' => 'structure', 'members' => [ 'CountBySeverity' => [ 'shape' => 'CountBySeverity', 'locationName' => 'countBySeverity', ], ], ], 'FindingType' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'FindingTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingType', ], 'max' => 50, 'min' => 0, ], 'Findings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], 'max' => 50, 'min' => 0, ], 'FlagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FlowLogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], ], ], 'FreeTrialFeatureConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FreeTrialFeatureResult', 'locationName' => 'name', ], 'FreeTrialDaysRemaining' => [ 'shape' => 'Integer', 'locationName' => 'freeTrialDaysRemaining', ], ], ], 'FreeTrialFeatureConfigurationsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'FreeTrialFeatureConfigurationResult', ], ], 'FreeTrialFeatureResult' => [ 'type' => 'string', 'enum' => [ 'FLOW_LOGS', 'CLOUD_TRAIL', 'DNS_LOGS', 'S3_DATA_EVENTS', 'EKS_AUDIT_LOGS', 'EBS_MALWARE_PROTECTION', 'RDS_LOGIN_EVENTS', 'EKS_RUNTIME_MONITORING', 'LAMBDA_NETWORK_LOGS', 'FARGATE_RUNTIME_MONITORING', 'EC2_RUNTIME_MONITORING', ], ], 'GeoLocation' => [ 'type' => 'structure', 'members' => [ 'Lat' => [ 'shape' => 'Double', 'locationName' => 'lat', ], 'Lon' => [ 'shape' => 'Double', 'locationName' => 'lon', ], ], ], 'GetAdministratorAccountRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], ], 'GetAdministratorAccountResponse' => [ 'type' => 'structure', 'required' => [ 'Administrator', ], 'members' => [ 'Administrator' => [ 'shape' => 'Administrator', 'locationName' => 'administrator', ], ], ], 'GetCoverageStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'StatisticsType', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FilterCriteria' => [ 'shape' => 'CoverageFilterCriteria', 'locationName' => 'filterCriteria', ], 'StatisticsType' => [ 'shape' => 'CoverageStatisticsTypeList', 'locationName' => 'statisticsType', ], ], ], 'GetCoverageStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'CoverageStatistics' => [ 'shape' => 'CoverageStatistics', 'locationName' => 'coverageStatistics', ], ], ], 'GetDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], ], 'GetDetectorResponse' => [ 'type' => 'structure', 'required' => [ 'ServiceRole', 'Status', ], 'members' => [ 'CreatedAt' => [ 'shape' => 'String', 'locationName' => 'createdAt', ], 'FindingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'ServiceRole' => [ 'shape' => 'String', 'locationName' => 'serviceRole', ], 'Status' => [ 'shape' => 'DetectorStatus', 'locationName' => 'status', ], 'UpdatedAt' => [ 'shape' => 'String', 'locationName' => 'updatedAt', ], 'DataSources' => [ 'shape' => 'DataSourceConfigurationsResult', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'Features' => [ 'shape' => 'DetectorFeatureConfigurationsResults', 'locationName' => 'features', ], ], ], 'GetFilterRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FilterName', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FilterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'filterName', ], ], ], 'GetFilterResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'Action', 'FindingCriteria', ], 'members' => [ 'Name' => [ 'shape' => 'FilterName', 'locationName' => 'name', ], 'Description' => [ 'shape' => 'FilterDescription', 'locationName' => 'description', ], 'Action' => [ 'shape' => 'FilterAction', 'locationName' => 'action', ], 'Rank' => [ 'shape' => 'FilterRank', 'locationName' => 'rank', ], 'FindingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FindingIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingIds' => [ 'shape' => 'FindingIds', 'locationName' => 'findingIds', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'GetFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'Findings', ], 'members' => [ 'Findings' => [ 'shape' => 'Findings', 'locationName' => 'findings', ], ], ], 'GetFindingsStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FindingStatisticTypes', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingStatisticTypes' => [ 'shape' => 'FindingStatisticTypes', 'locationName' => 'findingStatisticTypes', ], 'FindingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], ], ], 'GetFindingsStatisticsResponse' => [ 'type' => 'structure', 'required' => [ 'FindingStatistics', ], 'members' => [ 'FindingStatistics' => [ 'shape' => 'FindingStatistics', 'locationName' => 'findingStatistics', ], ], ], 'GetIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'IpSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'IpSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ipSetId', ], ], ], 'GetIPSetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'Format', 'Location', 'Status', ], 'members' => [ 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Format' => [ 'shape' => 'IpSetFormat', 'locationName' => 'format', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Status' => [ 'shape' => 'IpSetStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetInvitationsCountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInvitationsCountResponse' => [ 'type' => 'structure', 'members' => [ 'InvitationsCount' => [ 'shape' => 'Integer', 'locationName' => 'invitationsCount', ], ], ], 'GetMalwareScanSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], ], 'GetMalwareScanSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ScanResourceCriteria' => [ 'shape' => 'ScanResourceCriteria', 'locationName' => 'scanResourceCriteria', ], 'EbsSnapshotPreservation' => [ 'shape' => 'EbsSnapshotPreservation', 'locationName' => 'ebsSnapshotPreservation', ], ], ], 'GetMasterAccountRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This input is deprecated, use GetAdministratorAccountRequest instead', ], 'GetMasterAccountResponse' => [ 'type' => 'structure', 'required' => [ 'Master', ], 'members' => [ 'Master' => [ 'shape' => 'Master', 'locationName' => 'master', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This output is deprecated, use GetAdministratorAccountResponse instead', ], 'GetMemberDetectorsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'GetMemberDetectorsResponse' => [ 'type' => 'structure', 'required' => [ 'MemberDataSourceConfigurations', 'UnprocessedAccounts', ], 'members' => [ 'MemberDataSourceConfigurations' => [ 'shape' => 'MemberDataSourceConfigurations', 'locationName' => 'members', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'GetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'GetMembersResponse' => [ 'type' => 'structure', 'required' => [ 'Members', 'UnprocessedAccounts', ], 'members' => [ 'Members' => [ 'shape' => 'Members', 'locationName' => 'members', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'GetOrganizationStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationDetails' => [ 'shape' => 'OrganizationDetails', 'locationName' => 'organizationDetails', ], ], ], 'GetRemainingFreeTrialDaysRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'GetRemainingFreeTrialDaysResponse' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'AccountFreeTrialInfos', 'locationName' => 'accounts', ], 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'GetThreatIntelSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'ThreatIntelSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'ThreatIntelSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'threatIntelSetId', ], ], ], 'GetThreatIntelSetResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'Format', 'Location', 'Status', ], 'members' => [ 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Format' => [ 'shape' => 'ThreatIntelSetFormat', 'locationName' => 'format', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Status' => [ 'shape' => 'ThreatIntelSetStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetUsageStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'UsageStatisticType', 'UsageCriteria', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'UsageStatisticType' => [ 'shape' => 'UsageStatisticType', 'locationName' => 'usageStatisticsType', ], 'UsageCriteria' => [ 'shape' => 'UsageCriteria', 'locationName' => 'usageCriteria', ], 'Unit' => [ 'shape' => 'String', 'locationName' => 'unit', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'GetUsageStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'UsageStatistics' => [ 'shape' => 'UsageStatistics', 'locationName' => 'usageStatistics', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'Groups' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'GuardDutyArn' => [ 'type' => 'string', 'pattern' => '^arn:[A-Za-z_.-]{1,20}:guardduty:[A-Za-z0-9_/.-]{0,63}:\\d+:detector/[A-Za-z0-9_/.-]{32,264}$', ], 'HighestSeverityThreatDetails' => [ 'type' => 'structure', 'members' => [ 'Severity' => [ 'shape' => 'String', 'locationName' => 'severity', ], 'ThreatName' => [ 'shape' => 'String', 'locationName' => 'threatName', ], 'Count' => [ 'shape' => 'Integer', 'locationName' => 'count', ], ], ], 'HostPath' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'String', 'locationName' => 'path', ], ], ], 'IamInstanceProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'Id' => [ 'shape' => 'String', 'locationName' => 'id', ], ], ], 'ImpersonatedUser' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', 'locationName' => 'username', ], 'Groups' => [ 'shape' => 'Groups', 'locationName' => 'groups', ], ], ], 'InstanceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):[a-z]+:[a-z]+(-[0-9]+|-[a-z]+)+:([0-9]{12}):[a-z\\-]+\\/[a-zA-Z0-9]*$', ], 'InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', 'locationName' => 'availabilityZone', ], 'IamInstanceProfile' => [ 'shape' => 'IamInstanceProfile', 'locationName' => 'iamInstanceProfile', ], 'ImageDescription' => [ 'shape' => 'String', 'locationName' => 'imageDescription', ], 'ImageId' => [ 'shape' => 'String', 'locationName' => 'imageId', ], 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'InstanceState' => [ 'shape' => 'String', 'locationName' => 'instanceState', ], 'InstanceType' => [ 'shape' => 'String', 'locationName' => 'instanceType', ], 'OutpostArn' => [ 'shape' => 'String', 'locationName' => 'outpostArn', ], 'LaunchTime' => [ 'shape' => 'String', 'locationName' => 'launchTime', ], 'NetworkInterfaces' => [ 'shape' => 'NetworkInterfaces', 'locationName' => 'networkInterfaces', ], 'Platform' => [ 'shape' => 'String', 'locationName' => 'platform', ], 'ProductCodes' => [ 'shape' => 'ProductCodes', 'locationName' => 'productCodes', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerValueWithMax' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Type' => [ 'shape' => 'String', 'locationName' => '__type', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'Invitation' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'InvitationId' => [ 'shape' => 'String', 'locationName' => 'invitationId', ], 'RelationshipStatus' => [ 'shape' => 'String', 'locationName' => 'relationshipStatus', ], 'InvitedAt' => [ 'shape' => 'String', 'locationName' => 'invitedAt', ], ], ], 'Invitations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invitation', ], 'max' => 50, 'min' => 0, ], 'InviteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], 'DisableEmailNotification' => [ 'shape' => 'Boolean', 'locationName' => 'disableEmailNotification', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], ], 'InviteMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'IpSetFormat' => [ 'type' => 'string', 'enum' => [ 'TXT', 'STIX', 'OTX_CSV', 'ALIEN_VAULT', 'PROOF_POINT', 'FIRE_EYE', ], 'max' => 300, 'min' => 1, ], 'IpSetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, ], 'IpSetStatus' => [ 'type' => 'string', 'enum' => [ 'INACTIVE', 'ACTIVATING', 'ACTIVE', 'DEACTIVATING', 'ERROR', 'DELETE_PENDING', 'DELETED', ], 'max' => 300, 'min' => 1, ], 'Ipv6Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Issues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, ], 'KubernetesApiCallAction' => [ 'type' => 'structure', 'members' => [ 'RequestUri' => [ 'shape' => 'String', 'locationName' => 'requestUri', ], 'Verb' => [ 'shape' => 'String', 'locationName' => 'verb', ], 'SourceIps' => [ 'shape' => 'SourceIps', 'locationName' => 'sourceIps', ], 'UserAgent' => [ 'shape' => 'String', 'locationName' => 'userAgent', ], 'RemoteIpDetails' => [ 'shape' => 'RemoteIpDetails', 'locationName' => 'remoteIpDetails', ], 'StatusCode' => [ 'shape' => 'Integer', 'locationName' => 'statusCode', ], 'Parameters' => [ 'shape' => 'String', 'locationName' => 'parameters', ], 'Resource' => [ 'shape' => 'String', 'locationName' => 'resource', ], 'Subresource' => [ 'shape' => 'String', 'locationName' => 'subresource', ], 'Namespace' => [ 'shape' => 'String', 'locationName' => 'namespace', ], 'ResourceName' => [ 'shape' => 'String', 'locationName' => 'resourceName', ], ], ], 'KubernetesAuditLogsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Enable', ], 'members' => [ 'Enable' => [ 'shape' => 'Boolean', 'locationName' => 'enable', ], ], ], 'KubernetesAuditLogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], ], ], 'KubernetesConfiguration' => [ 'type' => 'structure', 'required' => [ 'AuditLogs', ], 'members' => [ 'AuditLogs' => [ 'shape' => 'KubernetesAuditLogsConfiguration', 'locationName' => 'auditLogs', ], ], ], 'KubernetesConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'AuditLogs', ], 'members' => [ 'AuditLogs' => [ 'shape' => 'KubernetesAuditLogsConfigurationResult', 'locationName' => 'auditLogs', ], ], ], 'KubernetesDataSourceFreeTrial' => [ 'type' => 'structure', 'members' => [ 'AuditLogs' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 'auditLogs', ], ], ], 'KubernetesDetails' => [ 'type' => 'structure', 'members' => [ 'KubernetesUserDetails' => [ 'shape' => 'KubernetesUserDetails', 'locationName' => 'kubernetesUserDetails', ], 'KubernetesWorkloadDetails' => [ 'shape' => 'KubernetesWorkloadDetails', 'locationName' => 'kubernetesWorkloadDetails', ], ], ], 'KubernetesPermissionCheckedDetails' => [ 'type' => 'structure', 'members' => [ 'Verb' => [ 'shape' => 'String', 'locationName' => 'verb', ], 'Resource' => [ 'shape' => 'String', 'locationName' => 'resource', ], 'Namespace' => [ 'shape' => 'String', 'locationName' => 'namespace', ], 'Allowed' => [ 'shape' => 'Boolean', 'locationName' => 'allowed', ], ], ], 'KubernetesRoleBindingDetails' => [ 'type' => 'structure', 'members' => [ 'Kind' => [ 'shape' => 'String', 'locationName' => 'kind', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Uid' => [ 'shape' => 'String', 'locationName' => 'uid', ], 'RoleRefName' => [ 'shape' => 'String', 'locationName' => 'roleRefName', ], 'RoleRefKind' => [ 'shape' => 'String', 'locationName' => 'roleRefKind', ], ], ], 'KubernetesRoleDetails' => [ 'type' => 'structure', 'members' => [ 'Kind' => [ 'shape' => 'String', 'locationName' => 'kind', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Uid' => [ 'shape' => 'String', 'locationName' => 'uid', ], ], ], 'KubernetesUserDetails' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', 'locationName' => 'username', ], 'Uid' => [ 'shape' => 'String', 'locationName' => 'uid', ], 'Groups' => [ 'shape' => 'Groups', 'locationName' => 'groups', ], 'SessionName' => [ 'shape' => 'SessionNameList', 'locationName' => 'sessionName', ], 'ImpersonatedUser' => [ 'shape' => 'ImpersonatedUser', 'locationName' => 'impersonatedUser', ], ], ], 'KubernetesWorkloadDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Type' => [ 'shape' => 'String', 'locationName' => 'type', ], 'Uid' => [ 'shape' => 'String', 'locationName' => 'uid', ], 'Namespace' => [ 'shape' => 'String', 'locationName' => 'namespace', ], 'HostNetwork' => [ 'shape' => 'Boolean', 'locationName' => 'hostNetwork', ], 'Containers' => [ 'shape' => 'Containers', 'locationName' => 'containers', ], 'Volumes' => [ 'shape' => 'Volumes', 'locationName' => 'volumes', ], 'ServiceAccountName' => [ 'shape' => 'String', 'locationName' => 'serviceAccountName', ], 'HostIPC' => [ 'shape' => 'Boolean', 'locationName' => 'hostIPC', ], 'HostPID' => [ 'shape' => 'Boolean', 'locationName' => 'hostPID', ], ], ], 'LambdaDetails' => [ 'type' => 'structure', 'members' => [ 'FunctionArn' => [ 'shape' => 'String', 'locationName' => 'functionArn', ], 'FunctionName' => [ 'shape' => 'String', 'locationName' => 'functionName', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'LastModifiedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'lastModifiedAt', ], 'RevisionId' => [ 'shape' => 'String', 'locationName' => 'revisionId', ], 'FunctionVersion' => [ 'shape' => 'String', 'locationName' => 'functionVersion', ], 'Role' => [ 'shape' => 'String', 'locationName' => 'role', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', 'locationName' => 'vpcConfig', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'Lineage' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineageObject', ], ], 'LineageObject' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', 'locationName' => 'startTime', ], 'NamespacePid' => [ 'shape' => 'Integer', 'locationName' => 'namespacePid', ], 'UserId' => [ 'shape' => 'Integer', 'locationName' => 'userId', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Pid' => [ 'shape' => 'Integer', 'locationName' => 'pid', ], 'Uuid' => [ 'shape' => 'String', 'locationName' => 'uuid', ], 'ExecutablePath' => [ 'shape' => 'String', 'locationName' => 'executablePath', ], 'Euid' => [ 'shape' => 'Integer', 'locationName' => 'euid', ], 'ParentUuid' => [ 'shape' => 'String', 'locationName' => 'parentUuid', ], ], ], 'ListCoverageRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'locationName' => 'maxResults', ], 'FilterCriteria' => [ 'shape' => 'CoverageFilterCriteria', 'locationName' => 'filterCriteria', ], 'SortCriteria' => [ 'shape' => 'CoverageSortCriteria', 'locationName' => 'sortCriteria', ], ], ], 'ListCoverageResponse' => [ 'type' => 'structure', 'required' => [ 'Resources', ], 'members' => [ 'Resources' => [ 'shape' => 'CoverageResources', 'locationName' => 'resources', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListDetectorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDetectorsResponse' => [ 'type' => 'structure', 'required' => [ 'DetectorIds', ], 'members' => [ 'DetectorIds' => [ 'shape' => 'DetectorIds', 'locationName' => 'detectorIds', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListFiltersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFiltersResponse' => [ 'type' => 'structure', 'required' => [ 'FilterNames', ], 'members' => [ 'FilterNames' => [ 'shape' => 'FilterNames', 'locationName' => 'filterNames', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', 'locationName' => 'sortCriteria', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'FindingIds', ], 'members' => [ 'FindingIds' => [ 'shape' => 'FindingIds', 'locationName' => 'findingIds', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListIPSetsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIPSetsResponse' => [ 'type' => 'structure', 'required' => [ 'IpSetIds', ], 'members' => [ 'IpSetIds' => [ 'shape' => 'IpSetIds', 'locationName' => 'ipSetIds', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'Invitations' => [ 'shape' => 'Invitations', 'locationName' => 'invitations', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'OnlyAssociated' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'onlyAssociated', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'Members', 'locationName' => 'members', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListOrganizationAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListOrganizationAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccounts' => [ 'shape' => 'AdminAccounts', 'locationName' => 'adminAccounts', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListPublishingDestinationsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPublishingDestinationsResponse' => [ 'type' => 'structure', 'required' => [ 'Destinations', ], 'members' => [ 'Destinations' => [ 'shape' => 'Destinations', 'locationName' => 'destinations', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GuardDutyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'ListThreatIntelSetsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListThreatIntelSetsResponse' => [ 'type' => 'structure', 'required' => [ 'ThreatIntelSetIds', ], 'members' => [ 'ThreatIntelSetIds' => [ 'shape' => 'ThreatIntelSetIds', 'locationName' => 'threatIntelSetIds', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'LocalIpDetails' => [ 'type' => 'structure', 'members' => [ 'IpAddressV4' => [ 'shape' => 'SensitiveString', 'locationName' => 'ipAddressV4', ], ], ], 'LocalPortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'PortName' => [ 'shape' => 'String', 'locationName' => 'portName', ], ], ], 'Location' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'LoginAttribute' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'String', 'locationName' => 'user', ], 'Application' => [ 'shape' => 'String', 'locationName' => 'application', ], 'FailedLoginAttempts' => [ 'shape' => 'Integer', 'locationName' => 'failedLoginAttempts', ], 'SuccessfulLoginAttempts' => [ 'shape' => 'Integer', 'locationName' => 'successfulLoginAttempts', ], ], ], 'LoginAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoginAttribute', ], ], 'Long' => [ 'type' => 'long', ], 'LongValue' => [ 'type' => 'long', ], 'MalwareProtectionConfiguration' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'ScanEc2InstanceWithFindings', 'locationName' => 'scanEc2InstanceWithFindings', ], ], ], 'MalwareProtectionConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'ScanEc2InstanceWithFindingsResult', 'locationName' => 'scanEc2InstanceWithFindings', ], 'ServiceRole' => [ 'shape' => 'String', 'locationName' => 'serviceRole', ], ], ], 'MalwareProtectionDataSourceFreeTrial' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'DataSourceFreeTrial', 'locationName' => 'scanEc2InstanceWithFindings', ], ], ], 'ManagementType' => [ 'type' => 'string', 'enum' => [ 'AUTO_MANAGED', 'MANUAL', 'DISABLED', ], ], 'MapEquals' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScanConditionPair', ], ], 'Master' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'InvitationId' => [ 'shape' => 'String', 'locationName' => 'invitationId', ], 'RelationshipStatus' => [ 'shape' => 'String', 'locationName' => 'relationshipStatus', ], 'InvitedAt' => [ 'shape' => 'String', 'locationName' => 'invitedAt', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'MasterId', 'Email', 'RelationshipStatus', 'UpdatedAt', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'DetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'detectorId', ], 'MasterId' => [ 'shape' => 'String', 'locationName' => 'masterId', ], 'Email' => [ 'shape' => 'Email', 'locationName' => 'email', ], 'RelationshipStatus' => [ 'shape' => 'String', 'locationName' => 'relationshipStatus', ], 'InvitedAt' => [ 'shape' => 'String', 'locationName' => 'invitedAt', ], 'UpdatedAt' => [ 'shape' => 'String', 'locationName' => 'updatedAt', ], 'AdministratorId' => [ 'shape' => 'String', 'locationName' => 'administratorId', ], ], ], 'MemberAdditionalConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeatureAdditionalConfiguration', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], ], ], 'MemberAdditionalConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeatureAdditionalConfiguration', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], ], ], 'MemberAdditionalConfigurationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAdditionalConfigurationResult', ], ], 'MemberAdditionalConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAdditionalConfiguration', ], ], 'MemberDataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'DataSources' => [ 'shape' => 'DataSourceConfigurationsResult', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'MemberFeaturesConfigurationsResults', 'locationName' => 'features', ], ], ], 'MemberDataSourceConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberDataSourceConfiguration', ], 'max' => 50, 'min' => 1, ], 'MemberFeaturesConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeature', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'AdditionalConfiguration' => [ 'shape' => 'MemberAdditionalConfigurations', 'locationName' => 'additionalConfiguration', ], ], ], 'MemberFeaturesConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeature', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'FeatureStatus', 'locationName' => 'status', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], 'AdditionalConfiguration' => [ 'shape' => 'MemberAdditionalConfigurationResults', 'locationName' => 'additionalConfiguration', ], ], ], 'MemberFeaturesConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberFeaturesConfiguration', ], ], 'MemberFeaturesConfigurationsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberFeaturesConfigurationResult', ], ], 'Members' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], 'max' => 50, 'min' => 0, ], 'MemoryRegionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Name' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'Neq' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'NetworkConnectionAction' => [ 'type' => 'structure', 'members' => [ 'Blocked' => [ 'shape' => 'Boolean', 'locationName' => 'blocked', ], 'ConnectionDirection' => [ 'shape' => 'String', 'locationName' => 'connectionDirection', ], 'LocalPortDetails' => [ 'shape' => 'LocalPortDetails', 'locationName' => 'localPortDetails', ], 'Protocol' => [ 'shape' => 'String', 'locationName' => 'protocol', ], 'LocalIpDetails' => [ 'shape' => 'LocalIpDetails', 'locationName' => 'localIpDetails', ], 'RemoteIpDetails' => [ 'shape' => 'RemoteIpDetails', 'locationName' => 'remoteIpDetails', ], 'RemotePortDetails' => [ 'shape' => 'RemotePortDetails', 'locationName' => 'remotePortDetails', ], ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'Ipv6Addresses' => [ 'shape' => 'Ipv6Addresses', 'locationName' => 'ipv6Addresses', ], 'NetworkInterfaceId' => [ 'shape' => 'String', 'locationName' => 'networkInterfaceId', ], 'PrivateDnsName' => [ 'shape' => 'String', 'locationName' => 'privateDnsName', ], 'PrivateIpAddress' => [ 'shape' => 'SensitiveString', 'locationName' => 'privateIpAddress', ], 'PrivateIpAddresses' => [ 'shape' => 'PrivateIpAddresses', 'locationName' => 'privateIpAddresses', ], 'PublicDnsName' => [ 'shape' => 'String', 'locationName' => 'publicDnsName', ], 'PublicIp' => [ 'shape' => 'String', 'locationName' => 'publicIp', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', 'locationName' => 'securityGroups', ], 'SubnetId' => [ 'shape' => 'String', 'locationName' => 'subnetId', ], 'VpcId' => [ 'shape' => 'String', 'locationName' => 'vpcId', ], ], ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'NotEquals' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ObservationTexts' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Observations' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'ObservationTexts', 'locationName' => 'text', ], ], ], 'OrderBy' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'OrgFeature' => [ 'type' => 'string', 'enum' => [ 'S3_DATA_EVENTS', 'EKS_AUDIT_LOGS', 'EBS_MALWARE_PROTECTION', 'RDS_LOGIN_EVENTS', 'EKS_RUNTIME_MONITORING', 'LAMBDA_NETWORK_LOGS', 'RUNTIME_MONITORING', ], ], 'OrgFeatureAdditionalConfiguration' => [ 'type' => 'string', 'enum' => [ 'EKS_ADDON_MANAGEMENT', 'ECS_FARGATE_AGENT_MANAGEMENT', 'EC2_AGENT_MANAGEMENT', ], ], 'OrgFeatureStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'NONE', 'ALL', ], ], 'Organization' => [ 'type' => 'structure', 'members' => [ 'Asn' => [ 'shape' => 'String', 'locationName' => 'asn', ], 'AsnOrg' => [ 'shape' => 'String', 'locationName' => 'asnOrg', ], 'Isp' => [ 'shape' => 'String', 'locationName' => 'isp', ], 'Org' => [ 'shape' => 'String', 'locationName' => 'org', ], ], ], 'OrganizationAdditionalConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeatureAdditionalConfiguration', 'locationName' => 'name', ], 'AutoEnable' => [ 'shape' => 'OrgFeatureStatus', 'locationName' => 'autoEnable', ], ], ], 'OrganizationAdditionalConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeatureAdditionalConfiguration', 'locationName' => 'name', ], 'AutoEnable' => [ 'shape' => 'OrgFeatureStatus', 'locationName' => 'autoEnable', ], ], ], 'OrganizationAdditionalConfigurationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationAdditionalConfigurationResult', ], ], 'OrganizationAdditionalConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationAdditionalConfiguration', ], ], 'OrganizationDataSourceConfigurations' => [ 'type' => 'structure', 'members' => [ 'S3Logs' => [ 'shape' => 'OrganizationS3LogsConfiguration', 'locationName' => 's3Logs', ], 'Kubernetes' => [ 'shape' => 'OrganizationKubernetesConfiguration', 'locationName' => 'kubernetes', ], 'MalwareProtection' => [ 'shape' => 'OrganizationMalwareProtectionConfiguration', 'locationName' => 'malwareProtection', ], ], ], 'OrganizationDataSourceConfigurationsResult' => [ 'type' => 'structure', 'required' => [ 'S3Logs', ], 'members' => [ 'S3Logs' => [ 'shape' => 'OrganizationS3LogsConfigurationResult', 'locationName' => 's3Logs', ], 'Kubernetes' => [ 'shape' => 'OrganizationKubernetesConfigurationResult', 'locationName' => 'kubernetes', ], 'MalwareProtection' => [ 'shape' => 'OrganizationMalwareProtectionConfigurationResult', 'locationName' => 'malwareProtection', ], ], ], 'OrganizationDetails' => [ 'type' => 'structure', 'members' => [ 'UpdatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'updatedAt', ], 'OrganizationStatistics' => [ 'shape' => 'OrganizationStatistics', 'locationName' => 'organizationStatistics', ], ], ], 'OrganizationEbsVolumes' => [ 'type' => 'structure', 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationEbsVolumesResult' => [ 'type' => 'structure', 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationFeatureConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeature', 'locationName' => 'name', ], 'AutoEnable' => [ 'shape' => 'OrgFeatureStatus', 'locationName' => 'autoEnable', ], 'AdditionalConfiguration' => [ 'shape' => 'OrganizationAdditionalConfigurations', 'locationName' => 'additionalConfiguration', ], ], ], 'OrganizationFeatureConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeature', 'locationName' => 'name', ], 'AutoEnable' => [ 'shape' => 'OrgFeatureStatus', 'locationName' => 'autoEnable', ], 'AdditionalConfiguration' => [ 'shape' => 'OrganizationAdditionalConfigurationResults', 'locationName' => 'additionalConfiguration', ], ], ], 'OrganizationFeatureStatistics' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeature', 'locationName' => 'name', ], 'EnabledAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'enabledAccountsCount', ], 'AdditionalConfiguration' => [ 'shape' => 'OrganizationFeatureStatisticsAdditionalConfigurations', 'locationName' => 'additionalConfiguration', ], ], ], 'OrganizationFeatureStatisticsAdditionalConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrgFeatureAdditionalConfiguration', 'locationName' => 'name', ], 'EnabledAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'enabledAccountsCount', ], ], ], 'OrganizationFeatureStatisticsAdditionalConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationFeatureStatisticsAdditionalConfiguration', ], ], 'OrganizationFeatureStatisticsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationFeatureStatistics', ], ], 'OrganizationFeaturesConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationFeatureConfiguration', ], ], 'OrganizationFeaturesConfigurationsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationFeatureConfigurationResult', ], ], 'OrganizationKubernetesAuditLogsConfiguration' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationKubernetesAuditLogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationKubernetesConfiguration' => [ 'type' => 'structure', 'required' => [ 'AuditLogs', ], 'members' => [ 'AuditLogs' => [ 'shape' => 'OrganizationKubernetesAuditLogsConfiguration', 'locationName' => 'auditLogs', ], ], ], 'OrganizationKubernetesConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'AuditLogs', ], 'members' => [ 'AuditLogs' => [ 'shape' => 'OrganizationKubernetesAuditLogsConfigurationResult', 'locationName' => 'auditLogs', ], ], ], 'OrganizationMalwareProtectionConfiguration' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'OrganizationScanEc2InstanceWithFindings', 'locationName' => 'scanEc2InstanceWithFindings', ], ], ], 'OrganizationMalwareProtectionConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'OrganizationScanEc2InstanceWithFindingsResult', 'locationName' => 'scanEc2InstanceWithFindings', ], ], ], 'OrganizationS3LogsConfiguration' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationS3LogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', 'locationName' => 'autoEnable', ], ], ], 'OrganizationScanEc2InstanceWithFindings' => [ 'type' => 'structure', 'members' => [ 'EbsVolumes' => [ 'shape' => 'OrganizationEbsVolumes', 'locationName' => 'ebsVolumes', ], ], ], 'OrganizationScanEc2InstanceWithFindingsResult' => [ 'type' => 'structure', 'members' => [ 'EbsVolumes' => [ 'shape' => 'OrganizationEbsVolumesResult', 'locationName' => 'ebsVolumes', ], ], ], 'OrganizationStatistics' => [ 'type' => 'structure', 'members' => [ 'TotalAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'totalAccountsCount', ], 'MemberAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'memberAccountsCount', ], 'ActiveAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'activeAccountsCount', ], 'EnabledAccountsCount' => [ 'shape' => 'Integer', 'locationName' => 'enabledAccountsCount', ], 'CountByFeature' => [ 'shape' => 'OrganizationFeatureStatisticsResults', 'locationName' => 'countByFeature', ], ], ], 'Owner' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', 'locationName' => 'id', ], ], ], 'PermissionConfiguration' => [ 'type' => 'structure', 'members' => [ 'BucketLevelPermissions' => [ 'shape' => 'BucketLevelPermissions', 'locationName' => 'bucketLevelPermissions', ], 'AccountLevelPermissions' => [ 'shape' => 'AccountLevelPermissions', 'locationName' => 'accountLevelPermissions', ], ], ], 'PortProbeAction' => [ 'type' => 'structure', 'members' => [ 'Blocked' => [ 'shape' => 'Boolean', 'locationName' => 'blocked', ], 'PortProbeDetails' => [ 'shape' => 'PortProbeDetails', 'locationName' => 'portProbeDetails', ], ], ], 'PortProbeDetail' => [ 'type' => 'structure', 'members' => [ 'LocalPortDetails' => [ 'shape' => 'LocalPortDetails', 'locationName' => 'localPortDetails', ], 'LocalIpDetails' => [ 'shape' => 'LocalIpDetails', 'locationName' => 'localIpDetails', ], 'RemoteIpDetails' => [ 'shape' => 'RemoteIpDetails', 'locationName' => 'remoteIpDetails', ], ], ], 'PortProbeDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortProbeDetail', ], ], 'PositiveLong' => [ 'type' => 'long', 'min' => 0, ], 'PrivateIpAddressDetails' => [ 'type' => 'structure', 'members' => [ 'PrivateDnsName' => [ 'shape' => 'String', 'locationName' => 'privateDnsName', ], 'PrivateIpAddress' => [ 'shape' => 'SensitiveString', 'locationName' => 'privateIpAddress', ], ], ], 'PrivateIpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrivateIpAddressDetails', ], ], 'ProcessDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'ExecutablePath' => [ 'shape' => 'String', 'locationName' => 'executablePath', ], 'ExecutableSha256' => [ 'shape' => 'String', 'locationName' => 'executableSha256', ], 'NamespacePid' => [ 'shape' => 'Integer', 'locationName' => 'namespacePid', ], 'Pwd' => [ 'shape' => 'String', 'locationName' => 'pwd', ], 'Pid' => [ 'shape' => 'Integer', 'locationName' => 'pid', ], 'StartTime' => [ 'shape' => 'Timestamp', 'locationName' => 'startTime', ], 'Uuid' => [ 'shape' => 'String', 'locationName' => 'uuid', ], 'ParentUuid' => [ 'shape' => 'String', 'locationName' => 'parentUuid', ], 'User' => [ 'shape' => 'String', 'locationName' => 'user', ], 'UserId' => [ 'shape' => 'Integer', 'locationName' => 'userId', ], 'Euid' => [ 'shape' => 'Integer', 'locationName' => 'euid', ], 'Lineage' => [ 'shape' => 'Lineage', 'locationName' => 'lineage', ], ], ], 'ProductCode' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'String', 'locationName' => 'productCodeId', ], 'ProductType' => [ 'shape' => 'String', 'locationName' => 'productCodeType', ], ], ], 'ProductCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductCode', ], ], 'ProfileSubtype' => [ 'type' => 'string', 'enum' => [ 'FREQUENT', 'INFREQUENT', 'UNSEEN', 'RARE', ], ], 'ProfileType' => [ 'type' => 'string', 'enum' => [ 'FREQUENCY', ], ], 'PublicAccess' => [ 'type' => 'structure', 'members' => [ 'PermissionConfiguration' => [ 'shape' => 'PermissionConfiguration', 'locationName' => 'permissionConfiguration', ], 'EffectivePermission' => [ 'shape' => 'String', 'locationName' => 'effectivePermission', ], ], ], 'PublishingStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VERIFICATION', 'PUBLISHING', 'UNABLE_TO_PUBLISH_FIX_DESTINATION_PROPERTY', 'STOPPED', ], 'max' => 300, 'min' => 1, ], 'RdsDbInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'DbInstanceIdentifier' => [ 'shape' => 'String', 'locationName' => 'dbInstanceIdentifier', ], 'Engine' => [ 'shape' => 'String', 'locationName' => 'engine', ], 'EngineVersion' => [ 'shape' => 'String', 'locationName' => 'engineVersion', ], 'DbClusterIdentifier' => [ 'shape' => 'String', 'locationName' => 'dbClusterIdentifier', ], 'DbInstanceArn' => [ 'shape' => 'String', 'locationName' => 'dbInstanceArn', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'RdsDbUserDetails' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'String', 'locationName' => 'user', ], 'Application' => [ 'shape' => 'String', 'locationName' => 'application', ], 'Database' => [ 'shape' => 'String', 'locationName' => 'database', ], 'Ssl' => [ 'shape' => 'String', 'locationName' => 'ssl', ], 'AuthMethod' => [ 'shape' => 'String', 'locationName' => 'authMethod', ], ], ], 'RdsLoginAttemptAction' => [ 'type' => 'structure', 'members' => [ 'RemoteIpDetails' => [ 'shape' => 'RemoteIpDetails', 'locationName' => 'remoteIpDetails', ], 'LoginAttributes' => [ 'shape' => 'LoginAttributes', ], ], ], 'RemoteAccountDetails' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'String', 'locationName' => 'accountId', ], 'Affiliated' => [ 'shape' => 'Boolean', 'locationName' => 'affiliated', ], ], ], 'RemoteIpDetails' => [ 'type' => 'structure', 'members' => [ 'City' => [ 'shape' => 'City', 'locationName' => 'city', ], 'Country' => [ 'shape' => 'Country', 'locationName' => 'country', ], 'GeoLocation' => [ 'shape' => 'GeoLocation', 'locationName' => 'geoLocation', ], 'IpAddressV4' => [ 'shape' => 'SensitiveString', 'locationName' => 'ipAddressV4', ], 'Organization' => [ 'shape' => 'Organization', 'locationName' => 'organization', ], ], ], 'RemotePortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'PortName' => [ 'shape' => 'String', 'locationName' => 'portName', ], ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'AccessKeyDetails' => [ 'shape' => 'AccessKeyDetails', 'locationName' => 'accessKeyDetails', ], 'S3BucketDetails' => [ 'shape' => 'S3BucketDetails', 'locationName' => 's3BucketDetails', ], 'InstanceDetails' => [ 'shape' => 'InstanceDetails', 'locationName' => 'instanceDetails', ], 'EksClusterDetails' => [ 'shape' => 'EksClusterDetails', 'locationName' => 'eksClusterDetails', ], 'KubernetesDetails' => [ 'shape' => 'KubernetesDetails', 'locationName' => 'kubernetesDetails', ], 'ResourceType' => [ 'shape' => 'String', 'locationName' => 'resourceType', ], 'EbsVolumeDetails' => [ 'shape' => 'EbsVolumeDetails', 'locationName' => 'ebsVolumeDetails', ], 'EcsClusterDetails' => [ 'shape' => 'EcsClusterDetails', 'locationName' => 'ecsClusterDetails', ], 'ContainerDetails' => [ 'shape' => 'Container', 'locationName' => 'containerDetails', ], 'RdsDbInstanceDetails' => [ 'shape' => 'RdsDbInstanceDetails', 'locationName' => 'rdsDbInstanceDetails', ], 'RdsDbUserDetails' => [ 'shape' => 'RdsDbUserDetails', 'locationName' => 'rdsDbUserDetails', ], 'LambdaDetails' => [ 'shape' => 'LambdaDetails', 'locationName' => 'lambdaDetails', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:[A-Za-z-]+:[A-Za-z0-9]+:[A-Za-z0-9-]+:\\d+:(([A-Za-z0-9-]+)[:\\/])?[A-Za-z0-9-]*$', ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', 'locationName' => 'instanceArn', ], ], ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'EKS', 'ECS', 'EC2', ], ], 'RuntimeContext' => [ 'type' => 'structure', 'members' => [ 'ModifyingProcess' => [ 'shape' => 'ProcessDetails', 'locationName' => 'modifyingProcess', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'modifiedAt', ], 'ScriptPath' => [ 'shape' => 'String', 'locationName' => 'scriptPath', ], 'LibraryPath' => [ 'shape' => 'String', 'locationName' => 'libraryPath', ], 'LdPreloadValue' => [ 'shape' => 'String', 'locationName' => 'ldPreloadValue', ], 'SocketPath' => [ 'shape' => 'String', 'locationName' => 'socketPath', ], 'RuncBinaryPath' => [ 'shape' => 'String', 'locationName' => 'runcBinaryPath', ], 'ReleaseAgentPath' => [ 'shape' => 'String', 'locationName' => 'releaseAgentPath', ], 'MountSource' => [ 'shape' => 'String', 'locationName' => 'mountSource', ], 'MountTarget' => [ 'shape' => 'String', 'locationName' => 'mountTarget', ], 'FileSystemType' => [ 'shape' => 'String', 'locationName' => 'fileSystemType', ], 'Flags' => [ 'shape' => 'FlagsList', 'locationName' => 'flags', ], 'ModuleName' => [ 'shape' => 'String', 'locationName' => 'moduleName', ], 'ModuleFilePath' => [ 'shape' => 'String', 'locationName' => 'moduleFilePath', ], 'ModuleSha256' => [ 'shape' => 'String', 'locationName' => 'moduleSha256', ], 'ShellHistoryFilePath' => [ 'shape' => 'String', 'locationName' => 'shellHistoryFilePath', ], 'TargetProcess' => [ 'shape' => 'ProcessDetails', 'locationName' => 'targetProcess', ], 'AddressFamily' => [ 'shape' => 'String', 'locationName' => 'addressFamily', ], 'IanaProtocolNumber' => [ 'shape' => 'Integer', 'locationName' => 'ianaProtocolNumber', ], 'MemoryRegions' => [ 'shape' => 'MemoryRegionsList', 'locationName' => 'memoryRegions', ], 'ToolName' => [ 'shape' => 'String', 'locationName' => 'toolName', ], 'ToolCategory' => [ 'shape' => 'String', 'locationName' => 'toolCategory', ], 'ServiceName' => [ 'shape' => 'String', 'locationName' => 'serviceName', ], 'CommandLineExample' => [ 'shape' => 'String', 'locationName' => 'commandLineExample', ], 'ThreatFilePath' => [ 'shape' => 'String', 'locationName' => 'threatFilePath', ], ], ], 'RuntimeDetails' => [ 'type' => 'structure', 'members' => [ 'Process' => [ 'shape' => 'ProcessDetails', 'locationName' => 'process', ], 'Context' => [ 'shape' => 'RuntimeContext', 'locationName' => 'context', ], ], ], 'S3BucketDetail' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', 'locationName' => 'arn', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Type' => [ 'shape' => 'String', 'locationName' => 'type', ], 'CreatedAt' => [ 'shape' => 'Timestamp', 'locationName' => 'createdAt', ], 'Owner' => [ 'shape' => 'Owner', 'locationName' => 'owner', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'DefaultServerSideEncryption' => [ 'shape' => 'DefaultServerSideEncryption', 'locationName' => 'defaultServerSideEncryption', ], 'PublicAccess' => [ 'shape' => 'PublicAccess', 'locationName' => 'publicAccess', ], ], ], 'S3BucketDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketDetail', ], ], 'S3LogsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Enable', ], 'members' => [ 'Enable' => [ 'shape' => 'Boolean', 'locationName' => 'enable', ], ], ], 'S3LogsConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'DataSourceStatus', 'locationName' => 'status', ], ], ], 'Scan' => [ 'type' => 'structure', 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'detectorId', ], 'AdminDetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'adminDetectorId', ], 'ScanId' => [ 'shape' => 'NonEmptyString', 'locationName' => 'scanId', ], 'ScanStatus' => [ 'shape' => 'ScanStatus', 'locationName' => 'scanStatus', ], 'FailureReason' => [ 'shape' => 'NonEmptyString', 'locationName' => 'failureReason', ], 'ScanStartTime' => [ 'shape' => 'Timestamp', 'locationName' => 'scanStartTime', ], 'ScanEndTime' => [ 'shape' => 'Timestamp', 'locationName' => 'scanEndTime', ], 'TriggerDetails' => [ 'shape' => 'TriggerDetails', 'locationName' => 'triggerDetails', ], 'ResourceDetails' => [ 'shape' => 'ResourceDetails', 'locationName' => 'resourceDetails', ], 'ScanResultDetails' => [ 'shape' => 'ScanResultDetails', 'locationName' => 'scanResultDetails', ], 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'TotalBytes' => [ 'shape' => 'PositiveLong', 'locationName' => 'totalBytes', ], 'FileCount' => [ 'shape' => 'PositiveLong', 'locationName' => 'fileCount', ], 'AttachedVolumes' => [ 'shape' => 'VolumeDetails', 'locationName' => 'attachedVolumes', ], 'ScanType' => [ 'shape' => 'ScanType', 'locationName' => 'scanType', ], ], ], 'ScanCondition' => [ 'type' => 'structure', 'required' => [ 'MapEquals', ], 'members' => [ 'MapEquals' => [ 'shape' => 'MapEquals', 'locationName' => 'mapEquals', ], ], ], 'ScanConditionPair' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', 'locationName' => 'key', ], 'Value' => [ 'shape' => 'TagValue', 'locationName' => 'value', ], ], ], 'ScanCriterion' => [ 'type' => 'map', 'key' => [ 'shape' => 'ScanCriterionKey', ], 'value' => [ 'shape' => 'ScanCondition', ], ], 'ScanCriterionKey' => [ 'type' => 'string', 'enum' => [ 'EC2_INSTANCE_TAG', ], ], 'ScanDetections' => [ 'type' => 'structure', 'members' => [ 'ScannedItemCount' => [ 'shape' => 'ScannedItemCount', 'locationName' => 'scannedItemCount', ], 'ThreatsDetectedItemCount' => [ 'shape' => 'ThreatsDetectedItemCount', 'locationName' => 'threatsDetectedItemCount', ], 'HighestSeverityThreatDetails' => [ 'shape' => 'HighestSeverityThreatDetails', 'locationName' => 'highestSeverityThreatDetails', ], 'ThreatDetectedByName' => [ 'shape' => 'ThreatDetectedByName', 'locationName' => 'threatDetectedByName', ], ], ], 'ScanEc2InstanceWithFindings' => [ 'type' => 'structure', 'members' => [ 'EbsVolumes' => [ 'shape' => 'Boolean', 'locationName' => 'ebsVolumes', ], ], ], 'ScanEc2InstanceWithFindingsResult' => [ 'type' => 'structure', 'members' => [ 'EbsVolumes' => [ 'shape' => 'EbsVolumesResult', 'locationName' => 'ebsVolumes', ], ], ], 'ScanFilePath' => [ 'type' => 'structure', 'members' => [ 'FilePath' => [ 'shape' => 'String', 'locationName' => 'filePath', ], 'VolumeArn' => [ 'shape' => 'String', 'locationName' => 'volumeArn', ], 'Hash' => [ 'shape' => 'String', 'locationName' => 'hash', ], 'FileName' => [ 'shape' => 'String', 'locationName' => 'fileName', ], ], ], 'ScanResourceCriteria' => [ 'type' => 'structure', 'members' => [ 'Include' => [ 'shape' => 'ScanCriterion', 'locationName' => 'include', ], 'Exclude' => [ 'shape' => 'ScanCriterion', 'locationName' => 'exclude', ], ], ], 'ScanResult' => [ 'type' => 'string', 'enum' => [ 'CLEAN', 'INFECTED', ], ], 'ScanResultDetails' => [ 'type' => 'structure', 'members' => [ 'ScanResult' => [ 'shape' => 'ScanResult', 'locationName' => 'scanResult', ], ], ], 'ScanStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'FAILED', 'SKIPPED', ], ], 'ScanThreatName' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Severity' => [ 'shape' => 'String', 'locationName' => 'severity', ], 'ItemCount' => [ 'shape' => 'Integer', 'locationName' => 'itemCount', ], 'FilePaths' => [ 'shape' => 'FilePaths', 'locationName' => 'filePaths', ], ], ], 'ScanThreatNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScanThreatName', ], ], 'ScanType' => [ 'type' => 'string', 'enum' => [ 'GUARDDUTY_INITIATED', 'ON_DEMAND', ], ], 'ScannedItemCount' => [ 'type' => 'structure', 'members' => [ 'TotalGb' => [ 'shape' => 'Integer', 'locationName' => 'totalGb', ], 'Files' => [ 'shape' => 'Integer', 'locationName' => 'files', ], 'Volumes' => [ 'shape' => 'Integer', 'locationName' => 'volumes', ], ], ], 'Scans' => [ 'type' => 'list', 'member' => [ 'shape' => 'Scan', ], ], 'SecurityContext' => [ 'type' => 'structure', 'members' => [ 'Privileged' => [ 'shape' => 'Boolean', 'locationName' => 'privileged', ], 'AllowPrivilegeEscalation' => [ 'shape' => 'Boolean', 'locationName' => 'allowPrivilegeEscalation', ], ], ], 'SecurityGroup' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'String', 'locationName' => 'groupId', ], 'GroupName' => [ 'shape' => 'String', 'locationName' => 'groupName', ], ], ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroup', ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'Service' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', 'locationName' => 'action', ], 'Evidence' => [ 'shape' => 'Evidence', 'locationName' => 'evidence', ], 'Archived' => [ 'shape' => 'Boolean', 'locationName' => 'archived', ], 'Count' => [ 'shape' => 'Integer', 'locationName' => 'count', ], 'DetectorId' => [ 'shape' => 'DetectorId', 'locationName' => 'detectorId', ], 'EventFirstSeen' => [ 'shape' => 'String', 'locationName' => 'eventFirstSeen', ], 'EventLastSeen' => [ 'shape' => 'String', 'locationName' => 'eventLastSeen', ], 'ResourceRole' => [ 'shape' => 'String', 'locationName' => 'resourceRole', ], 'ServiceName' => [ 'shape' => 'String', 'locationName' => 'serviceName', ], 'UserFeedback' => [ 'shape' => 'String', 'locationName' => 'userFeedback', ], 'AdditionalInfo' => [ 'shape' => 'ServiceAdditionalInfo', 'locationName' => 'additionalInfo', ], 'FeatureName' => [ 'shape' => 'String', 'locationName' => 'featureName', ], 'EbsVolumeScanDetails' => [ 'shape' => 'EbsVolumeScanDetails', 'locationName' => 'ebsVolumeScanDetails', ], 'RuntimeDetails' => [ 'shape' => 'RuntimeDetails', 'locationName' => 'runtimeDetails', ], 'Detection' => [ 'shape' => 'Detection', 'locationName' => 'detection', ], ], ], 'ServiceAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', 'locationName' => 'value', ], 'Type' => [ 'shape' => 'String', 'locationName' => 'type', ], ], ], 'SessionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SortCriteria' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'String', 'locationName' => 'attributeName', ], 'OrderBy' => [ 'shape' => 'OrderBy', 'locationName' => 'orderBy', ], ], ], 'SourceIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Sources' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StartMalwareScanRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'locationName' => 'resourceArn', ], ], ], 'StartMalwareScanResponse' => [ 'type' => 'structure', 'members' => [ 'ScanId' => [ 'shape' => 'NonEmptyString', 'locationName' => 'scanId', ], ], ], 'StartMonitoringMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'StartMonitoringMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'StopMonitoringMembersRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], ], ], 'StopMonitoringMembersResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'String' => [ 'type' => 'string', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', 'locationName' => 'key', ], 'Value' => [ 'shape' => 'String', 'locationName' => 'value', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GuardDutyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'ThreatDetectedByName' => [ 'type' => 'structure', 'members' => [ 'ItemCount' => [ 'shape' => 'Integer', 'locationName' => 'itemCount', ], 'UniqueThreatNameCount' => [ 'shape' => 'Integer', 'locationName' => 'uniqueThreatNameCount', ], 'Shortened' => [ 'shape' => 'Boolean', 'locationName' => 'shortened', ], 'ThreatNames' => [ 'shape' => 'ScanThreatNames', 'locationName' => 'threatNames', ], ], ], 'ThreatIntelSetFormat' => [ 'type' => 'string', 'enum' => [ 'TXT', 'STIX', 'OTX_CSV', 'ALIEN_VAULT', 'PROOF_POINT', 'FIRE_EYE', ], 'max' => 300, 'min' => 1, ], 'ThreatIntelSetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, ], 'ThreatIntelSetStatus' => [ 'type' => 'string', 'enum' => [ 'INACTIVE', 'ACTIVATING', 'ACTIVE', 'DEACTIVATING', 'ERROR', 'DELETE_PENDING', 'DELETED', ], 'max' => 300, 'min' => 1, ], 'ThreatIntelligenceDetail' => [ 'type' => 'structure', 'members' => [ 'ThreatListName' => [ 'shape' => 'String', 'locationName' => 'threatListName', ], 'ThreatNames' => [ 'shape' => 'ThreatNames', 'locationName' => 'threatNames', ], 'ThreatFileSha256' => [ 'shape' => 'String', 'locationName' => 'threatFileSha256', ], ], ], 'ThreatIntelligenceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThreatIntelligenceDetail', ], ], 'ThreatNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ThreatsDetectedItemCount' => [ 'type' => 'structure', 'members' => [ 'Files' => [ 'shape' => 'Integer', 'locationName' => 'files', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Total' => [ 'type' => 'structure', 'members' => [ 'Amount' => [ 'shape' => 'String', 'locationName' => 'amount', ], 'Unit' => [ 'shape' => 'String', 'locationName' => 'unit', ], ], ], 'TriggerDetails' => [ 'type' => 'structure', 'members' => [ 'GuardDutyFindingId' => [ 'shape' => 'NonEmptyString', 'locationName' => 'guardDutyFindingId', ], 'Description' => [ 'shape' => 'NonEmptyString', 'locationName' => 'description', ], ], ], 'UnarchiveFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FindingIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingIds' => [ 'shape' => 'FindingIds', 'locationName' => 'findingIds', ], ], ], 'UnarchiveFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UnprocessedAccount' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Result', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'Result' => [ 'shape' => 'String', 'locationName' => 'result', ], ], ], 'UnprocessedAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedAccount', ], 'max' => 50, 'min' => 0, ], 'UnprocessedDataSourcesResult' => [ 'type' => 'structure', 'members' => [ 'MalwareProtection' => [ 'shape' => 'MalwareProtectionConfigurationResult', 'locationName' => 'malwareProtection', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GuardDutyArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'Enable' => [ 'shape' => 'Boolean', 'locationName' => 'enable', ], 'FindingPublishingFrequency' => [ 'shape' => 'FindingPublishingFrequency', 'locationName' => 'findingPublishingFrequency', ], 'DataSources' => [ 'shape' => 'DataSourceConfigurations', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'DetectorFeatureConfigurations', 'locationName' => 'features', ], ], ], 'UpdateDetectorResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FilterName', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FilterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'filterName', ], 'Description' => [ 'shape' => 'FilterDescription', 'locationName' => 'description', ], 'Action' => [ 'shape' => 'FilterAction', 'locationName' => 'action', ], 'Rank' => [ 'shape' => 'FilterRank', 'locationName' => 'rank', ], 'FindingCriteria' => [ 'shape' => 'FindingCriteria', 'locationName' => 'findingCriteria', ], ], ], 'UpdateFilterResponse' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'FilterName', 'locationName' => 'name', ], ], ], 'UpdateFindingsFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'FindingIds', 'Feedback', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'FindingIds' => [ 'shape' => 'FindingIds', 'locationName' => 'findingIds', ], 'Feedback' => [ 'shape' => 'Feedback', 'locationName' => 'feedback', ], 'Comments' => [ 'shape' => 'String', 'locationName' => 'comments', ], ], ], 'UpdateFindingsFeedbackResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'IpSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'IpSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ipSetId', ], 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Activate' => [ 'shape' => 'Boolean', 'locationName' => 'activate', ], ], ], 'UpdateIPSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMalwareScanSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'ScanResourceCriteria' => [ 'shape' => 'ScanResourceCriteria', 'locationName' => 'scanResourceCriteria', ], 'EbsSnapshotPreservation' => [ 'shape' => 'EbsSnapshotPreservation', 'locationName' => 'ebsSnapshotPreservation', ], ], ], 'UpdateMalwareScanSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMemberDetectorsRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'AccountIds', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], 'DataSources' => [ 'shape' => 'DataSourceConfigurations', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'MemberFeaturesConfigurations', 'locationName' => 'features', ], ], ], 'UpdateMemberDetectorsResponse' => [ 'type' => 'structure', 'required' => [ 'UnprocessedAccounts', ], 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'UnprocessedAccounts', 'locationName' => 'unprocessedAccounts', ], ], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'AutoEnable' => [ 'shape' => 'Boolean', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use AutoEnableOrganizationMembers instead', 'locationName' => 'autoEnable', ], 'DataSources' => [ 'shape' => 'OrganizationDataSourceConfigurations', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Features' => [ 'shape' => 'OrganizationFeaturesConfigurations', 'locationName' => 'features', ], 'AutoEnableOrganizationMembers' => [ 'shape' => 'AutoEnableMembers', 'locationName' => 'autoEnableOrganizationMembers', ], ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePublishingDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'DestinationId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'DestinationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'destinationId', ], 'DestinationProperties' => [ 'shape' => 'DestinationProperties', 'locationName' => 'destinationProperties', ], ], ], 'UpdatePublishingDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateThreatIntelSetRequest' => [ 'type' => 'structure', 'required' => [ 'DetectorId', 'ThreatIntelSetId', ], 'members' => [ 'DetectorId' => [ 'shape' => 'DetectorId', 'location' => 'uri', 'locationName' => 'detectorId', ], 'ThreatIntelSetId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'threatIntelSetId', ], 'Name' => [ 'shape' => 'Name', 'locationName' => 'name', ], 'Location' => [ 'shape' => 'Location', 'locationName' => 'location', ], 'Activate' => [ 'shape' => 'Boolean', 'locationName' => 'activate', ], ], ], 'UpdateThreatIntelSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UsageAccountResult' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'Total' => [ 'shape' => 'Total', 'locationName' => 'total', ], ], ], 'UsageAccountResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageAccountResult', ], ], 'UsageCriteria' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIds', 'locationName' => 'accountIds', ], 'DataSources' => [ 'shape' => 'DataSourceList', 'deprecated' => true, 'deprecatedMessage' => 'This parameter is deprecated, use Features instead', 'locationName' => 'dataSources', ], 'Resources' => [ 'shape' => 'ResourceList', 'locationName' => 'resources', ], 'Features' => [ 'shape' => 'UsageFeatureList', 'locationName' => 'features', ], ], ], 'UsageDataSourceResult' => [ 'type' => 'structure', 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', 'locationName' => 'dataSource', ], 'Total' => [ 'shape' => 'Total', 'locationName' => 'total', ], ], ], 'UsageDataSourceResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageDataSourceResult', ], ], 'UsageFeature' => [ 'type' => 'string', 'enum' => [ 'FLOW_LOGS', 'CLOUD_TRAIL', 'DNS_LOGS', 'S3_DATA_EVENTS', 'EKS_AUDIT_LOGS', 'EBS_MALWARE_PROTECTION', 'RDS_LOGIN_EVENTS', 'LAMBDA_NETWORK_LOGS', 'EKS_RUNTIME_MONITORING', 'FARGATE_RUNTIME_MONITORING', 'EC2_RUNTIME_MONITORING', 'RDS_DBI_PROTECTION_PROVISIONED', 'RDS_DBI_PROTECTION_SERVERLESS', ], ], 'UsageFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageFeature', ], ], 'UsageFeatureResult' => [ 'type' => 'structure', 'members' => [ 'Feature' => [ 'shape' => 'UsageFeature', 'locationName' => 'feature', ], 'Total' => [ 'shape' => 'Total', 'locationName' => 'total', ], ], ], 'UsageFeatureResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageFeatureResult', ], ], 'UsageResourceResult' => [ 'type' => 'structure', 'members' => [ 'Resource' => [ 'shape' => 'String', 'locationName' => 'resource', ], 'Total' => [ 'shape' => 'Total', 'locationName' => 'total', ], ], ], 'UsageResourceResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageResourceResult', ], ], 'UsageStatisticType' => [ 'type' => 'string', 'enum' => [ 'SUM_BY_ACCOUNT', 'SUM_BY_DATA_SOURCE', 'SUM_BY_RESOURCE', 'TOP_RESOURCES', 'SUM_BY_FEATURES', 'TOP_ACCOUNTS_BY_FEATURE', ], ], 'UsageStatistics' => [ 'type' => 'structure', 'members' => [ 'SumByAccount' => [ 'shape' => 'UsageAccountResultList', 'locationName' => 'sumByAccount', ], 'TopAccountsByFeature' => [ 'shape' => 'UsageTopAccountsResultList', 'locationName' => 'topAccountsByFeature', ], 'SumByDataSource' => [ 'shape' => 'UsageDataSourceResultList', 'locationName' => 'sumByDataSource', ], 'SumByResource' => [ 'shape' => 'UsageResourceResultList', 'locationName' => 'sumByResource', ], 'TopResources' => [ 'shape' => 'UsageResourceResultList', 'locationName' => 'topResources', ], 'SumByFeature' => [ 'shape' => 'UsageFeatureResultList', 'locationName' => 'sumByFeature', ], ], ], 'UsageTopAccountResult' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', 'locationName' => 'accountId', ], 'Total' => [ 'shape' => 'Total', 'locationName' => 'total', ], ], ], 'UsageTopAccountsByFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageTopAccountResult', ], ], 'UsageTopAccountsResult' => [ 'type' => 'structure', 'members' => [ 'Feature' => [ 'shape' => 'UsageFeature', 'locationName' => 'feature', ], 'Accounts' => [ 'shape' => 'UsageTopAccountsByFeatureList', 'locationName' => 'accounts', ], ], ], 'UsageTopAccountsResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageTopAccountsResult', ], ], 'Volume' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'HostPath' => [ 'shape' => 'HostPath', 'locationName' => 'hostPath', ], ], ], 'VolumeDetail' => [ 'type' => 'structure', 'members' => [ 'VolumeArn' => [ 'shape' => 'String', 'locationName' => 'volumeArn', ], 'VolumeType' => [ 'shape' => 'String', 'locationName' => 'volumeType', ], 'DeviceName' => [ 'shape' => 'String', 'locationName' => 'deviceName', ], 'VolumeSizeInGB' => [ 'shape' => 'Integer', 'locationName' => 'volumeSizeInGB', ], 'EncryptionType' => [ 'shape' => 'String', 'locationName' => 'encryptionType', ], 'SnapshotArn' => [ 'shape' => 'String', 'locationName' => 'snapshotArn', ], 'KmsKeyArn' => [ 'shape' => 'String', 'locationName' => 'kmsKeyArn', ], ], ], 'VolumeDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeDetail', ], ], 'VolumeMount' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'MountPath' => [ 'shape' => 'String', 'locationName' => 'mountPath', ], ], ], 'VolumeMounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeMount', ], ], 'Volumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Volume', ], ], 'VpcConfig' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIds', 'locationName' => 'subnetIds', ], 'VpcId' => [ 'shape' => 'String', 'locationName' => 'vpcId', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', 'locationName' => 'securityGroups', ], ], ], ],];
