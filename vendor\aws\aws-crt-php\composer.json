{"name": "aws/aws-crt-php", "homepage": "https://github.com/awslabs/aws-crt-php", "description": "AWS Common Runtime for PHP", "keywords": ["aws", "amazon", "sdk", "crt"], "type": "library", "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "minimum-stability": "alpha", "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "autoload": {"classmap": ["src/"]}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "scripts": {"test": "./dev-scripts/run_tests.sh", "test-extension": "@test", "test-win": ".\\dev-scripts\\run_tests.bat"}, "license": "Apache-2.0"}