<?php
namespace Aws\RAM;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Resource Access Manager** service.
 * @method \Aws\Result acceptResourceShareInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptResourceShareInvitationAsync(array $args = [])
 * @method \Aws\Result associateResourceShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateResourceShareAsync(array $args = [])
 * @method \Aws\Result associateResourceSharePermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateResourceSharePermissionAsync(array $args = [])
 * @method \Aws\Result createPermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPermissionAsync(array $args = [])
 * @method \Aws\Result createPermissionVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPermissionVersionAsync(array $args = [])
 * @method \Aws\Result createResourceShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourceShareAsync(array $args = [])
 * @method \Aws\Result deletePermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePermissionAsync(array $args = [])
 * @method \Aws\Result deletePermissionVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePermissionVersionAsync(array $args = [])
 * @method \Aws\Result deleteResourceShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourceShareAsync(array $args = [])
 * @method \Aws\Result disassociateResourceShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateResourceShareAsync(array $args = [])
 * @method \Aws\Result disassociateResourceSharePermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateResourceSharePermissionAsync(array $args = [])
 * @method \Aws\Result enableSharingWithAwsOrganization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableSharingWithAwsOrganizationAsync(array $args = [])
 * @method \Aws\Result getPermission(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPermissionAsync(array $args = [])
 * @method \Aws\Result getResourcePolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourcePoliciesAsync(array $args = [])
 * @method \Aws\Result getResourceShareAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceShareAssociationsAsync(array $args = [])
 * @method \Aws\Result getResourceShareInvitations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceShareInvitationsAsync(array $args = [])
 * @method \Aws\Result getResourceShares(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceSharesAsync(array $args = [])
 * @method \Aws\Result listPendingInvitationResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPendingInvitationResourcesAsync(array $args = [])
 * @method \Aws\Result listPermissionAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionAssociationsAsync(array $args = [])
 * @method \Aws\Result listPermissionVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionVersionsAsync(array $args = [])
 * @method \Aws\Result listPermissions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionsAsync(array $args = [])
 * @method \Aws\Result listPrincipals(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPrincipalsAsync(array $args = [])
 * @method \Aws\Result listReplacePermissionAssociationsWork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReplacePermissionAssociationsWorkAsync(array $args = [])
 * @method \Aws\Result listResourceSharePermissions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourceSharePermissionsAsync(array $args = [])
 * @method \Aws\Result listResourceTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourceTypesAsync(array $args = [])
 * @method \Aws\Result listResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourcesAsync(array $args = [])
 * @method \Aws\Result promotePermissionCreatedFromPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise promotePermissionCreatedFromPolicyAsync(array $args = [])
 * @method \Aws\Result promoteResourceShareCreatedFromPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise promoteResourceShareCreatedFromPolicyAsync(array $args = [])
 * @method \Aws\Result rejectResourceShareInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rejectResourceShareInvitationAsync(array $args = [])
 * @method \Aws\Result replacePermissionAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise replacePermissionAssociationsAsync(array $args = [])
 * @method \Aws\Result setDefaultPermissionVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise setDefaultPermissionVersionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateResourceShare(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateResourceShareAsync(array $args = [])
 */
class RAMClient extends AwsClient {}
