<?php
// This file was auto-generated from sdk-root/src/data/neptunedata/2023-08-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-08-01', 'endpointPrefix' => 'neptune-db', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon NeptuneData', 'serviceId' => 'neptunedata', 'signatureVersion' => 'v4', 'signingName' => 'neptune-db', 'uid' => 'neptunedata-2023-08-01', ], 'operations' => [ 'CancelGremlinQuery' => [ 'name' => 'CancelGremlinQuery', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/gremlin/status/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelGremlinQueryInput', ], 'output' => [ 'shape' => 'CancelGremlinQueryOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'CancelLoaderJob' => [ 'name' => 'CancelLoaderJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/loader/{loadId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelLoaderJobInput', ], 'output' => [ 'shape' => 'CancelLoaderJobOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'BulkLoadIdNotFoundException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'LoadUrlAccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'CancelMLDataProcessingJob' => [ 'name' => 'CancelMLDataProcessingJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ml/dataprocessing/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelMLDataProcessingJobInput', ], 'output' => [ 'shape' => 'CancelMLDataProcessingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], 'idempotent' => true, ], 'CancelMLModelTrainingJob' => [ 'name' => 'CancelMLModelTrainingJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ml/modeltraining/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelMLModelTrainingJobInput', ], 'output' => [ 'shape' => 'CancelMLModelTrainingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], 'idempotent' => true, ], 'CancelMLModelTransformJob' => [ 'name' => 'CancelMLModelTransformJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ml/modeltransform/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelMLModelTransformJobInput', ], 'output' => [ 'shape' => 'CancelMLModelTransformJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], 'idempotent' => true, ], 'CancelOpenCypherQuery' => [ 'name' => 'CancelOpenCypherQuery', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/opencypher/status/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelOpenCypherQueryInput', ], 'output' => [ 'shape' => 'CancelOpenCypherQueryOutput', ], 'errors' => [ [ 'shape' => 'InvalidNumericDataException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'CreateMLEndpoint' => [ 'name' => 'CreateMLEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/ml/endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMLEndpointInput', ], 'output' => [ 'shape' => 'CreateMLEndpointOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteMLEndpoint' => [ 'name' => 'DeleteMLEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ml/endpoints/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMLEndpointInput', ], 'output' => [ 'shape' => 'DeleteMLEndpointOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], 'idempotent' => true, ], 'DeletePropertygraphStatistics' => [ 'name' => 'DeletePropertygraphStatistics', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/propertygraph/statistics', 'responseCode' => 200, ], 'output' => [ 'shape' => 'DeletePropertygraphStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'DeleteSparqlStatistics' => [ 'name' => 'DeleteSparqlStatistics', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sparql/statistics', 'responseCode' => 200, ], 'output' => [ 'shape' => 'DeleteSparqlStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'ExecuteFastReset' => [ 'name' => 'ExecuteFastReset', 'http' => [ 'method' => 'POST', 'requestUri' => '/system', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteFastResetInput', ], 'output' => [ 'shape' => 'ExecuteFastResetOutput', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ServerShutdownException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'ExecuteGremlinExplainQuery' => [ 'name' => 'ExecuteGremlinExplainQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/gremlin/explain', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteGremlinExplainQueryInput', ], 'output' => [ 'shape' => 'ExecuteGremlinExplainQueryOutput', ], 'errors' => [ [ 'shape' => 'QueryTooLargeException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'QueryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryLimitException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'CancelledByUserException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ExecuteGremlinProfileQuery' => [ 'name' => 'ExecuteGremlinProfileQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/gremlin/profile', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteGremlinProfileQueryInput', ], 'output' => [ 'shape' => 'ExecuteGremlinProfileQueryOutput', ], 'errors' => [ [ 'shape' => 'QueryTooLargeException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'QueryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryLimitException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'CancelledByUserException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ExecuteGremlinQuery' => [ 'name' => 'ExecuteGremlinQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/gremlin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteGremlinQueryInput', ], 'output' => [ 'shape' => 'ExecuteGremlinQueryOutput', ], 'errors' => [ [ 'shape' => 'QueryTooLargeException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'QueryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryLimitException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'CancelledByUserException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ExecuteOpenCypherExplainQuery' => [ 'name' => 'ExecuteOpenCypherExplainQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/opencypher/explain', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteOpenCypherExplainQueryInput', ], 'output' => [ 'shape' => 'ExecuteOpenCypherExplainQueryOutput', ], 'errors' => [ [ 'shape' => 'QueryTooLargeException', ], [ 'shape' => 'InvalidNumericDataException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'QueryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryLimitException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'CancelledByUserException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ExecuteOpenCypherQuery' => [ 'name' => 'ExecuteOpenCypherQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/opencypher', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExecuteOpenCypherQueryInput', ], 'output' => [ 'shape' => 'ExecuteOpenCypherQueryOutput', ], 'errors' => [ [ 'shape' => 'QueryTooLargeException', ], [ 'shape' => 'InvalidNumericDataException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'QueryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'QueryLimitException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'CancelledByUserException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'MalformedQueryException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetEngineStatus' => [ 'name' => 'GetEngineStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/status', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetEngineStatusOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetGremlinQueryStatus' => [ 'name' => 'GetGremlinQueryStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/gremlin/status/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGremlinQueryStatusInput', ], 'output' => [ 'shape' => 'GetGremlinQueryStatusOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetLoaderJobStatus' => [ 'name' => 'GetLoaderJobStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/loader/{loadId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLoaderJobStatusInput', ], 'output' => [ 'shape' => 'GetLoaderJobStatusOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'BulkLoadIdNotFoundException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'LoadUrlAccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetMLDataProcessingJob' => [ 'name' => 'GetMLDataProcessingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/dataprocessing/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLDataProcessingJobInput', ], 'output' => [ 'shape' => 'GetMLDataProcessingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetMLEndpoint' => [ 'name' => 'GetMLEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/endpoints/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLEndpointInput', ], 'output' => [ 'shape' => 'GetMLEndpointOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetMLModelTrainingJob' => [ 'name' => 'GetMLModelTrainingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/modeltraining/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLModelTrainingJobInput', ], 'output' => [ 'shape' => 'GetMLModelTrainingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetMLModelTransformJob' => [ 'name' => 'GetMLModelTransformJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/modeltransform/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLModelTransformJobInput', ], 'output' => [ 'shape' => 'GetMLModelTransformJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetOpenCypherQueryStatus' => [ 'name' => 'GetOpenCypherQueryStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/opencypher/status/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOpenCypherQueryStatusInput', ], 'output' => [ 'shape' => 'GetOpenCypherQueryStatusOutput', ], 'errors' => [ [ 'shape' => 'InvalidNumericDataException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetPropertygraphStatistics' => [ 'name' => 'GetPropertygraphStatistics', 'http' => [ 'method' => 'GET', 'requestUri' => '/propertygraph/statistics', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetPropertygraphStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetPropertygraphStream' => [ 'name' => 'GetPropertygraphStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/propertygraph/stream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertygraphStreamInput', ], 'output' => [ 'shape' => 'GetPropertygraphStreamOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ExpiredStreamException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'StreamRecordsNotFoundException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetPropertygraphSummary' => [ 'name' => 'GetPropertygraphSummary', 'http' => [ 'method' => 'GET', 'requestUri' => '/propertygraph/statistics/summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertygraphSummaryInput', ], 'output' => [ 'shape' => 'GetPropertygraphSummaryOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetRDFGraphSummary' => [ 'name' => 'GetRDFGraphSummary', 'http' => [ 'method' => 'GET', 'requestUri' => '/rdf/statistics/summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRDFGraphSummaryInput', ], 'output' => [ 'shape' => 'GetRDFGraphSummaryOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetSparqlStatistics' => [ 'name' => 'GetSparqlStatistics', 'http' => [ 'method' => 'GET', 'requestUri' => '/sparql/statistics', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetSparqlStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'GetSparqlStream' => [ 'name' => 'GetSparqlStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/sparql/stream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSparqlStreamInput', ], 'output' => [ 'shape' => 'GetSparqlStreamOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ExpiredStreamException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MemoryLimitExceededException', ], [ 'shape' => 'StreamRecordsNotFoundException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListGremlinQueries' => [ 'name' => 'ListGremlinQueries', 'http' => [ 'method' => 'GET', 'requestUri' => '/gremlin/status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGremlinQueriesInput', ], 'output' => [ 'shape' => 'ListGremlinQueriesOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ListLoaderJobs' => [ 'name' => 'ListLoaderJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/loader', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLoaderJobsInput', ], 'output' => [ 'shape' => 'ListLoaderJobsOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'BulkLoadIdNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LoadUrlAccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMLDataProcessingJobs' => [ 'name' => 'ListMLDataProcessingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/dataprocessing', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMLDataProcessingJobsInput', ], 'output' => [ 'shape' => 'ListMLDataProcessingJobsOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMLEndpoints' => [ 'name' => 'ListMLEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMLEndpointsInput', ], 'output' => [ 'shape' => 'ListMLEndpointsOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMLModelTrainingJobs' => [ 'name' => 'ListMLModelTrainingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/modeltraining', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMLModelTrainingJobsInput', ], 'output' => [ 'shape' => 'ListMLModelTrainingJobsOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMLModelTransformJobs' => [ 'name' => 'ListMLModelTransformJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/ml/modeltransform', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMLModelTransformJobsInput', ], 'output' => [ 'shape' => 'ListMLModelTransformJobsOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListOpenCypherQueries' => [ 'name' => 'ListOpenCypherQueries', 'http' => [ 'method' => 'GET', 'requestUri' => '/opencypher/status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOpenCypherQueriesInput', ], 'output' => [ 'shape' => 'ListOpenCypherQueriesOutput', ], 'errors' => [ [ 'shape' => 'InvalidNumericDataException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'FailureByQueryException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ParsingException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'TimeLimitExceededException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'MissingParameterException', ], ], ], 'ManagePropertygraphStatistics' => [ 'name' => 'ManagePropertygraphStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/propertygraph/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ManagePropertygraphStatisticsInput', ], 'output' => [ 'shape' => 'ManagePropertygraphStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'ManageSparqlStatistics' => [ 'name' => 'ManageSparqlStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/sparql/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ManageSparqlStatisticsInput', ], 'output' => [ 'shape' => 'ManageSparqlStatisticsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'StatisticsNotAvailableException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ReadOnlyViolationException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'StartLoaderJob' => [ 'name' => 'StartLoaderJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/loader', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartLoaderJobInput', ], 'output' => [ 'shape' => 'StartLoaderJobOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'BulkLoadIdNotFoundException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'LoadUrlAccessDeniedException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'S3Exception', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], ], 'idempotent' => true, ], 'StartMLDataProcessingJob' => [ 'name' => 'StartMLDataProcessingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/ml/dataprocessing', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMLDataProcessingJobInput', ], 'output' => [ 'shape' => 'StartMLDataProcessingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'StartMLModelTrainingJob' => [ 'name' => 'StartMLModelTrainingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/ml/modeltraining', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMLModelTrainingJobInput', ], 'output' => [ 'shape' => 'StartMLModelTrainingJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'StartMLModelTransformJob' => [ 'name' => 'StartMLModelTransformJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/ml/modeltransform', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMLModelTransformJobInput', ], 'output' => [ 'shape' => 'StartMLModelTransformJobOutput', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'MLResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientTimeoutException', ], [ 'shape' => 'PreconditionsFailedException', ], [ 'shape' => 'ConstraintViolationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'MissingParameterException', ], [ 'shape' => 'IllegalArgumentException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Action' => [ 'type' => 'string', 'enum' => [ 'initiateDatabaseReset', 'performDatabaseReset', ], ], 'BadRequestException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BulkLoadIdNotFoundException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'CancelGremlinQueryInput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'CancelGremlinQueryOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'CancelLoaderJobInput' => [ 'type' => 'structure', 'required' => [ 'loadId', ], 'members' => [ 'loadId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'loadId', ], ], ], 'CancelLoaderJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'CancelMLDataProcessingJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], 'clean' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'clean', ], ], ], 'CancelMLDataProcessingJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'CancelMLModelTrainingJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], 'clean' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'clean', ], ], ], 'CancelMLModelTrainingJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'CancelMLModelTransformJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], 'clean' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'clean', ], ], ], 'CancelMLModelTransformJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'CancelOpenCypherQueryInput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], 'silent' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'silent', ], ], ], 'CancelOpenCypherQueryOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'Boolean', ], ], ], 'CancelledByUserException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Classes' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ClientTimeoutException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ConstraintViolationException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'CreateMLEndpointInput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'mlModelTrainingJobId' => [ 'shape' => 'String', ], 'mlModelTransformJobId' => [ 'shape' => 'String', ], 'update' => [ 'shape' => 'Boolean', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', ], 'modelName' => [ 'shape' => 'String', ], 'instanceType' => [ 'shape' => 'String', ], 'instanceCount' => [ 'shape' => 'Integer', ], 'volumeEncryptionKMSKey' => [ 'shape' => 'String', ], ], ], 'CreateMLEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'creationTimeInMillis' => [ 'shape' => 'Long', ], ], ], 'CustomModelTrainingParameters' => [ 'type' => 'structure', 'required' => [ 'sourceS3DirectoryPath', ], 'members' => [ 'sourceS3DirectoryPath' => [ 'shape' => 'String', ], 'trainingEntryPointScript' => [ 'shape' => 'String', ], 'transformEntryPointScript' => [ 'shape' => 'String', ], ], ], 'CustomModelTransformParameters' => [ 'type' => 'structure', 'required' => [ 'sourceS3DirectoryPath', ], 'members' => [ 'sourceS3DirectoryPath' => [ 'shape' => 'String', ], 'transformEntryPointScript' => [ 'shape' => 'String', ], ], ], 'DeleteMLEndpointInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], 'clean' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'clean', ], ], ], 'DeleteMLEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], ], ], 'DeletePropertygraphStatisticsOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'DeleteStatisticsValueMap', ], ], ], 'DeleteSparqlStatisticsOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'DeleteStatisticsValueMap', ], ], ], 'DeleteStatisticsValueMap' => [ 'type' => 'structure', 'members' => [ 'active' => [ 'shape' => 'Boolean', ], 'statisticsId' => [ 'shape' => 'String', ], ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DocumentValuedMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Document', ], ], 'EdgeLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EdgeProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EdgeStructure' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Long', ], 'edgeProperties' => [ 'shape' => 'EdgeProperties', ], ], ], 'EdgeStructures' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeStructure', ], ], 'Encoding' => [ 'type' => 'string', 'enum' => [ 'gzip', ], ], 'ExecuteFastResetInput' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'Action', ], 'token' => [ 'shape' => 'String', ], ], ], 'ExecuteFastResetOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'FastResetToken', ], ], ], 'ExecuteGremlinExplainQueryInput' => [ 'type' => 'structure', 'required' => [ 'gremlinQuery', ], 'members' => [ 'gremlinQuery' => [ 'shape' => 'String', 'locationName' => 'gremlin', ], ], ], 'ExecuteGremlinExplainQueryOutput' => [ 'type' => 'structure', 'members' => [ 'output' => [ 'shape' => 'ReportAsText', ], ], 'payload' => 'output', ], 'ExecuteGremlinProfileQueryInput' => [ 'type' => 'structure', 'required' => [ 'gremlinQuery', ], 'members' => [ 'gremlinQuery' => [ 'shape' => 'String', 'locationName' => 'gremlin', ], 'results' => [ 'shape' => 'Boolean', 'locationName' => 'profile.results', ], 'chop' => [ 'shape' => 'Integer', 'locationName' => 'profile.chop', ], 'serializer' => [ 'shape' => 'String', 'locationName' => 'profile.serializer', ], 'indexOps' => [ 'shape' => 'Boolean', 'locationName' => 'profile.indexOps', ], ], ], 'ExecuteGremlinProfileQueryOutput' => [ 'type' => 'structure', 'members' => [ 'output' => [ 'shape' => 'ReportAsText', ], ], 'payload' => 'output', ], 'ExecuteGremlinQueryInput' => [ 'type' => 'structure', 'required' => [ 'gremlinQuery', ], 'members' => [ 'gremlinQuery' => [ 'shape' => 'String', 'locationName' => 'gremlin', ], 'serializer' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'accept', ], ], ], 'ExecuteGremlinQueryOutput' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'GremlinQueryStatusAttributes', ], 'result' => [ 'shape' => 'Document', ], 'meta' => [ 'shape' => 'Document', ], ], ], 'ExecuteOpenCypherExplainQueryInput' => [ 'type' => 'structure', 'required' => [ 'openCypherQuery', 'explainMode', ], 'members' => [ 'openCypherQuery' => [ 'shape' => 'String', 'locationName' => 'query', ], 'parameters' => [ 'shape' => 'String', ], 'explainMode' => [ 'shape' => 'OpenCypherExplainMode', 'locationName' => 'explain', ], ], ], 'ExecuteOpenCypherExplainQueryOutput' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'Blob', ], ], 'payload' => 'results', ], 'ExecuteOpenCypherQueryInput' => [ 'type' => 'structure', 'required' => [ 'openCypherQuery', ], 'members' => [ 'openCypherQuery' => [ 'shape' => 'String', 'locationName' => 'query', ], 'parameters' => [ 'shape' => 'String', ], ], ], 'ExecuteOpenCypherQueryOutput' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'Document', ], ], ], 'ExpiredStreamException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'FailureByQueryException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'FastResetToken' => [ 'type' => 'structure', 'members' => [ 'token' => [ 'shape' => 'String', ], ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'csv', 'opencypher', 'ntriples', 'nquads', 'rdfxml', 'turtle', ], ], 'GetEngineStatusOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'String', ], 'dbEngineVersion' => [ 'shape' => 'String', ], 'role' => [ 'shape' => 'String', ], 'dfeQueryEngine' => [ 'shape' => 'String', ], 'gremlin' => [ 'shape' => 'QueryLanguageVersion', ], 'sparql' => [ 'shape' => 'QueryLanguageVersion', ], 'opencypher' => [ 'shape' => 'QueryLanguageVersion', ], 'labMode' => [ 'shape' => 'StringValuedMap', ], 'rollingBackTrxCount' => [ 'shape' => 'Integer', ], 'rollingBackTrxEarliestStartTime' => [ 'shape' => 'String', ], 'features' => [ 'shape' => 'DocumentValuedMap', ], 'settings' => [ 'shape' => 'StringValuedMap', ], ], ], 'GetGremlinQueryStatusInput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'GetGremlinQueryStatusOutput' => [ 'type' => 'structure', 'members' => [ 'queryId' => [ 'shape' => 'String', ], 'queryString' => [ 'shape' => 'String', ], 'queryEvalStats' => [ 'shape' => 'QueryEvalStats', ], ], ], 'GetLoaderJobStatusInput' => [ 'type' => 'structure', 'required' => [ 'loadId', ], 'members' => [ 'loadId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'loadId', ], 'details' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'details', ], 'errors' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'errors', ], 'page' => [ 'shape' => 'PositiveInteger', 'location' => 'querystring', 'locationName' => 'page', ], 'errorsPerPage' => [ 'shape' => 'PositiveInteger', 'location' => 'querystring', 'locationName' => 'errorsPerPage', ], ], ], 'GetLoaderJobStatusOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'payload', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'Document', ], ], ], 'GetMLDataProcessingJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'GetMLDataProcessingJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'processingJob' => [ 'shape' => 'MlResourceDefinition', ], ], ], 'GetMLEndpointInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'GetMLEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'endpoint' => [ 'shape' => 'MlResourceDefinition', ], 'endpointConfig' => [ 'shape' => 'MlConfigDefinition', ], ], ], 'GetMLModelTrainingJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'GetMLModelTrainingJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'processingJob' => [ 'shape' => 'MlResourceDefinition', ], 'hpoJob' => [ 'shape' => 'MlResourceDefinition', ], 'modelTransformJob' => [ 'shape' => 'MlResourceDefinition', ], 'mlModels' => [ 'shape' => 'MlModels', ], ], ], 'GetMLModelTransformJobInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'GetMLModelTransformJobOutput' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'baseProcessingJob' => [ 'shape' => 'MlResourceDefinition', ], 'remoteModelTransformJob' => [ 'shape' => 'MlResourceDefinition', ], 'models' => [ 'shape' => 'Models', ], ], ], 'GetOpenCypherQueryStatusInput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'GetOpenCypherQueryStatusOutput' => [ 'type' => 'structure', 'members' => [ 'queryId' => [ 'shape' => 'String', ], 'queryString' => [ 'shape' => 'String', ], 'queryEvalStats' => [ 'shape' => 'QueryEvalStats', ], ], ], 'GetPropertygraphStatisticsOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'payload', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'Statistics', ], ], ], 'GetPropertygraphStreamInput' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'GetPropertygraphStreamInputLimitLong', 'location' => 'querystring', 'locationName' => 'limit', ], 'iteratorType' => [ 'shape' => 'IteratorType', 'location' => 'querystring', 'locationName' => 'iteratorType', ], 'commitNum' => [ 'shape' => 'Long', 'location' => 'querystring', 'locationName' => 'commitNum', ], 'opNum' => [ 'shape' => 'Long', 'location' => 'querystring', 'locationName' => 'opNum', ], 'encoding' => [ 'shape' => 'Encoding', 'location' => 'header', 'locationName' => 'Accept-Encoding', ], ], ], 'GetPropertygraphStreamInputLimitLong' => [ 'type' => 'long', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetPropertygraphStreamOutput' => [ 'type' => 'structure', 'required' => [ 'lastEventId', 'lastTrxTimestampInMillis', 'format', 'records', 'totalRecords', ], 'members' => [ 'lastEventId' => [ 'shape' => 'StringValuedMap', ], 'lastTrxTimestampInMillis' => [ 'shape' => 'Long', 'locationName' => 'lastTrxTimestamp', ], 'format' => [ 'shape' => 'String', ], 'records' => [ 'shape' => 'PropertygraphRecordsList', ], 'totalRecords' => [ 'shape' => 'Integer', ], ], ], 'GetPropertygraphSummaryInput' => [ 'type' => 'structure', 'members' => [ 'mode' => [ 'shape' => 'GraphSummaryType', 'location' => 'querystring', 'locationName' => 'mode', ], ], ], 'GetPropertygraphSummaryOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'payload' => [ 'shape' => 'PropertygraphSummaryValueMap', ], ], ], 'GetRDFGraphSummaryInput' => [ 'type' => 'structure', 'members' => [ 'mode' => [ 'shape' => 'GraphSummaryType', 'location' => 'querystring', 'locationName' => 'mode', ], ], ], 'GetRDFGraphSummaryOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', 'location' => 'statusCode', ], 'payload' => [ 'shape' => 'RDFGraphSummaryValueMap', ], ], ], 'GetSparqlStatisticsOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'payload', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'Statistics', ], ], ], 'GetSparqlStreamInput' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'GetSparqlStreamInputLimitLong', 'location' => 'querystring', 'locationName' => 'limit', ], 'iteratorType' => [ 'shape' => 'IteratorType', 'location' => 'querystring', 'locationName' => 'iteratorType', ], 'commitNum' => [ 'shape' => 'Long', 'location' => 'querystring', 'locationName' => 'commitNum', ], 'opNum' => [ 'shape' => 'Long', 'location' => 'querystring', 'locationName' => 'opNum', ], 'encoding' => [ 'shape' => 'Encoding', 'location' => 'header', 'locationName' => 'Accept-Encoding', ], ], ], 'GetSparqlStreamInputLimitLong' => [ 'type' => 'long', 'box' => true, 'max' => 100000, 'min' => 1, ], 'GetSparqlStreamOutput' => [ 'type' => 'structure', 'required' => [ 'lastEventId', 'lastTrxTimestampInMillis', 'format', 'records', 'totalRecords', ], 'members' => [ 'lastEventId' => [ 'shape' => 'StringValuedMap', ], 'lastTrxTimestampInMillis' => [ 'shape' => 'Long', 'locationName' => 'lastTrxTimestamp', ], 'format' => [ 'shape' => 'String', ], 'records' => [ 'shape' => 'SparqlRecordsList', ], 'totalRecords' => [ 'shape' => 'Integer', ], ], ], 'GraphSummaryType' => [ 'type' => 'string', 'enum' => [ 'basic', 'detailed', ], ], 'GremlinQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GremlinQueryStatus', ], ], 'GremlinQueryStatus' => [ 'type' => 'structure', 'members' => [ 'queryId' => [ 'shape' => 'String', ], 'queryString' => [ 'shape' => 'String', ], 'queryEvalStats' => [ 'shape' => 'QueryEvalStats', ], ], ], 'GremlinQueryStatusAttributes' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'Integer', ], 'attributes' => [ 'shape' => 'Document', ], ], ], 'IllegalArgumentException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalFailureException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidArgumentException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidNumericDataException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IteratorType' => [ 'type' => 'string', 'enum' => [ 'AT_SEQUENCE_NUMBER', 'AFTER_SEQUENCE_NUMBER', 'TRIM_HORIZON', 'LATEST', ], ], 'ListGremlinQueriesInput' => [ 'type' => 'structure', 'members' => [ 'includeWaiting' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeWaiting', ], ], ], 'ListGremlinQueriesOutput' => [ 'type' => 'structure', 'members' => [ 'acceptedQueryCount' => [ 'shape' => 'Integer', ], 'runningQueryCount' => [ 'shape' => 'Integer', ], 'queries' => [ 'shape' => 'GremlinQueries', ], ], ], 'ListLoaderJobsInput' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'ListLoaderJobsInputLimitInteger', 'location' => 'querystring', 'locationName' => 'limit', ], 'includeQueuedLoads' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeQueuedLoads', ], ], ], 'ListLoaderJobsInputLimitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListLoaderJobsOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'payload', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'LoaderIdResult', ], ], ], 'ListMLDataProcessingJobsInput' => [ 'type' => 'structure', 'members' => [ 'maxItems' => [ 'shape' => 'ListMLDataProcessingJobsInputMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'ListMLDataProcessingJobsInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListMLDataProcessingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'StringList', ], ], ], 'ListMLEndpointsInput' => [ 'type' => 'structure', 'members' => [ 'maxItems' => [ 'shape' => 'ListMLEndpointsInputMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'ListMLEndpointsInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListMLEndpointsOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'StringList', ], ], ], 'ListMLModelTrainingJobsInput' => [ 'type' => 'structure', 'members' => [ 'maxItems' => [ 'shape' => 'ListMLModelTrainingJobsInputMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'ListMLModelTrainingJobsInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListMLModelTrainingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'StringList', ], ], ], 'ListMLModelTransformJobsInput' => [ 'type' => 'structure', 'members' => [ 'maxItems' => [ 'shape' => 'ListMLModelTransformJobsInputMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'neptuneIamRoleArn', ], ], ], 'ListMLModelTransformJobsInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1024, 'min' => 1, ], 'ListMLModelTransformJobsOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'StringList', ], ], ], 'ListOpenCypherQueriesInput' => [ 'type' => 'structure', 'members' => [ 'includeWaiting' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeWaiting', ], ], ], 'ListOpenCypherQueriesOutput' => [ 'type' => 'structure', 'members' => [ 'acceptedQueryCount' => [ 'shape' => 'Integer', ], 'runningQueryCount' => [ 'shape' => 'Integer', ], 'queries' => [ 'shape' => 'OpenCypherQueries', ], ], ], 'LoadUrlAccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LoaderIdResult' => [ 'type' => 'structure', 'members' => [ 'loadIds' => [ 'shape' => 'StringList', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'LongValuedMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Long', ], ], 'LongValuedMapList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LongValuedMap', ], ], 'MLResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'MalformedQueryException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ManagePropertygraphStatisticsInput' => [ 'type' => 'structure', 'members' => [ 'mode' => [ 'shape' => 'StatisticsAutoGenerationMode', ], ], ], 'ManagePropertygraphStatisticsOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'RefreshStatisticsIdMap', ], ], ], 'ManageSparqlStatisticsInput' => [ 'type' => 'structure', 'members' => [ 'mode' => [ 'shape' => 'StatisticsAutoGenerationMode', ], ], ], 'ManageSparqlStatisticsOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'RefreshStatisticsIdMap', ], ], ], 'MemoryLimitExceededException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'MethodNotAllowedException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 405, 'senderFault' => true, ], 'exception' => true, ], 'MissingParameterException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'MlConfigDefinition' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], ], ], 'MlModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'MlConfigDefinition', ], ], 'MlResourceDefinition' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'String', ], 'outputLocation' => [ 'shape' => 'String', ], 'failureReason' => [ 'shape' => 'String', ], 'cloudwatchLogUrl' => [ 'shape' => 'String', ], ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'RESUME', 'NEW', 'AUTO', ], ], 'Models' => [ 'type' => 'list', 'member' => [ 'shape' => 'MlConfigDefinition', ], ], 'NodeLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'NodeProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'NodeStructure' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Long', ], 'nodeProperties' => [ 'shape' => 'NodeProperties', ], 'distinctOutgoingEdgeLabels' => [ 'shape' => 'OutgoingEdgeLabels', ], ], ], 'NodeStructures' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeStructure', ], ], 'OpenCypherExplainMode' => [ 'type' => 'string', 'enum' => [ 'static', 'dynamic', 'details', ], ], 'OpenCypherQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GremlinQueryStatus', ], ], 'OutgoingEdgeLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Parallelism' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'OVERSUBSCRIBE', ], ], 'ParsingException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'PositiveInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PreconditionsFailedException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Predicates' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'PropertygraphData' => [ 'type' => 'structure', 'required' => [ 'id', 'type', 'key', 'value', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Document', ], 'from' => [ 'shape' => 'String', ], 'to' => [ 'shape' => 'String', ], ], ], 'PropertygraphRecord' => [ 'type' => 'structure', 'required' => [ 'commitTimestampInMillis', 'eventId', 'data', 'op', ], 'members' => [ 'commitTimestampInMillis' => [ 'shape' => 'Long', 'locationName' => 'commitTimestamp', ], 'eventId' => [ 'shape' => 'StringValuedMap', ], 'data' => [ 'shape' => 'PropertygraphData', ], 'op' => [ 'shape' => 'String', ], 'isLastOp' => [ 'shape' => 'Boolean', ], ], ], 'PropertygraphRecordsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertygraphRecord', ], ], 'PropertygraphSummary' => [ 'type' => 'structure', 'members' => [ 'numNodes' => [ 'shape' => 'Long', ], 'numEdges' => [ 'shape' => 'Long', ], 'numNodeLabels' => [ 'shape' => 'Long', ], 'numEdgeLabels' => [ 'shape' => 'Long', ], 'nodeLabels' => [ 'shape' => 'NodeLabels', ], 'edgeLabels' => [ 'shape' => 'EdgeLabels', ], 'numNodeProperties' => [ 'shape' => 'Long', ], 'numEdgeProperties' => [ 'shape' => 'Long', ], 'nodeProperties' => [ 'shape' => 'LongValuedMapList', ], 'edgeProperties' => [ 'shape' => 'LongValuedMapList', ], 'totalNodePropertyValues' => [ 'shape' => 'Long', ], 'totalEdgePropertyValues' => [ 'shape' => 'Long', ], 'nodeStructures' => [ 'shape' => 'NodeStructures', ], 'edgeStructures' => [ 'shape' => 'EdgeStructures', ], ], ], 'PropertygraphSummaryValueMap' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'String', ], 'lastStatisticsComputationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'graphSummary' => [ 'shape' => 'PropertygraphSummary', ], ], ], 'QueryEvalStats' => [ 'type' => 'structure', 'members' => [ 'waited' => [ 'shape' => 'Integer', ], 'elapsed' => [ 'shape' => 'Integer', ], 'cancelled' => [ 'shape' => 'Boolean', ], 'subqueries' => [ 'shape' => 'Document', ], ], ], 'QueryLanguageVersion' => [ 'type' => 'structure', 'required' => [ 'version', ], 'members' => [ 'version' => [ 'shape' => 'String', ], ], ], 'QueryLimitExceededException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'QueryLimitException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'QueryTooLargeException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RDFGraphSummary' => [ 'type' => 'structure', 'members' => [ 'numDistinctSubjects' => [ 'shape' => 'Long', ], 'numDistinctPredicates' => [ 'shape' => 'Long', ], 'numQuads' => [ 'shape' => 'Long', ], 'numClasses' => [ 'shape' => 'Long', ], 'classes' => [ 'shape' => 'Classes', ], 'predicates' => [ 'shape' => 'LongValuedMapList', ], 'subjectStructures' => [ 'shape' => 'SubjectStructures', ], ], ], 'RDFGraphSummaryValueMap' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'String', ], 'lastStatisticsComputationTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'graphSummary' => [ 'shape' => 'RDFGraphSummary', ], ], ], 'ReadOnlyViolationException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RefreshStatisticsIdMap' => [ 'type' => 'structure', 'members' => [ 'statisticsId' => [ 'shape' => 'String', ], ], ], 'ReportAsText' => [ 'type' => 'blob', ], 'S3BucketRegion' => [ 'type' => 'string', 'enum' => [ 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2', 'ca-central-1', 'sa-east-1', 'eu-north-1', 'eu-west-1', 'eu-west-2', 'eu-west-3', 'eu-central-1', 'me-south-1', 'af-south-1', 'ap-east-1', 'ap-northeast-1', 'ap-northeast-2', 'ap-southeast-1', 'ap-southeast-2', 'ap-south-1', 'cn-north-1', 'cn-northwest-1', 'us-gov-west-1', 'us-gov-east-1', ], ], 'S3Exception' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ServerShutdownException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'SparqlData' => [ 'type' => 'structure', 'required' => [ 'stmt', ], 'members' => [ 'stmt' => [ 'shape' => 'String', ], ], ], 'SparqlRecord' => [ 'type' => 'structure', 'required' => [ 'commitTimestampInMillis', 'eventId', 'data', 'op', ], 'members' => [ 'commitTimestampInMillis' => [ 'shape' => 'Long', 'locationName' => 'commitTimestamp', ], 'eventId' => [ 'shape' => 'StringValuedMap', ], 'data' => [ 'shape' => 'SparqlData', ], 'op' => [ 'shape' => 'String', ], 'isLastOp' => [ 'shape' => 'Boolean', ], ], ], 'SparqlRecordsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SparqlRecord', ], ], 'StartLoaderJobInput' => [ 'type' => 'structure', 'required' => [ 'source', 'format', 's3BucketRegion', 'iamRoleArn', ], 'members' => [ 'source' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'Format', ], 's3BucketRegion' => [ 'shape' => 'S3BucketRegion', 'locationName' => 'region', ], 'iamRoleArn' => [ 'shape' => 'String', ], 'mode' => [ 'shape' => 'Mode', ], 'failOnError' => [ 'shape' => 'Boolean', ], 'parallelism' => [ 'shape' => 'Parallelism', ], 'parserConfiguration' => [ 'shape' => 'StringValuedMap', ], 'updateSingleCardinalityProperties' => [ 'shape' => 'Boolean', ], 'queueRequest' => [ 'shape' => 'Boolean', ], 'dependencies' => [ 'shape' => 'StringList', ], 'userProvidedEdgeIds' => [ 'shape' => 'Boolean', ], ], ], 'StartLoaderJobOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'payload', ], 'members' => [ 'status' => [ 'shape' => 'String', ], 'payload' => [ 'shape' => 'StringValuedMap', ], ], ], 'StartMLDataProcessingJobInput' => [ 'type' => 'structure', 'required' => [ 'inputDataS3Location', 'processedDataS3Location', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'previousDataProcessingJobId' => [ 'shape' => 'String', ], 'inputDataS3Location' => [ 'shape' => 'String', ], 'processedDataS3Location' => [ 'shape' => 'String', ], 'sagemakerIamRoleArn' => [ 'shape' => 'String', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', ], 'processingInstanceType' => [ 'shape' => 'String', ], 'processingInstanceVolumeSizeInGB' => [ 'shape' => 'Integer', ], 'processingTimeOutInSeconds' => [ 'shape' => 'Integer', ], 'modelType' => [ 'shape' => 'String', ], 'configFileName' => [ 'shape' => 'String', ], 'subnets' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'volumeEncryptionKMSKey' => [ 'shape' => 'String', ], 's3OutputEncryptionKMSKey' => [ 'shape' => 'String', ], ], ], 'StartMLDataProcessingJobOutput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'creationTimeInMillis' => [ 'shape' => 'Long', ], ], ], 'StartMLModelTrainingJobInput' => [ 'type' => 'structure', 'required' => [ 'dataProcessingJobId', 'trainModelS3Location', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'previousModelTrainingJobId' => [ 'shape' => 'String', ], 'dataProcessingJobId' => [ 'shape' => 'String', ], 'trainModelS3Location' => [ 'shape' => 'String', ], 'sagemakerIamRoleArn' => [ 'shape' => 'String', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', ], 'baseProcessingInstanceType' => [ 'shape' => 'String', ], 'trainingInstanceType' => [ 'shape' => 'String', ], 'trainingInstanceVolumeSizeInGB' => [ 'shape' => 'Integer', ], 'trainingTimeOutInSeconds' => [ 'shape' => 'Integer', ], 'maxHPONumberOfTrainingJobs' => [ 'shape' => 'Integer', ], 'maxHPOParallelTrainingJobs' => [ 'shape' => 'Integer', ], 'subnets' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'volumeEncryptionKMSKey' => [ 'shape' => 'String', ], 's3OutputEncryptionKMSKey' => [ 'shape' => 'String', ], 'enableManagedSpotTraining' => [ 'shape' => 'Boolean', ], 'customModelTrainingParameters' => [ 'shape' => 'CustomModelTrainingParameters', ], ], ], 'StartMLModelTrainingJobOutput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'creationTimeInMillis' => [ 'shape' => 'Long', ], ], ], 'StartMLModelTransformJobInput' => [ 'type' => 'structure', 'required' => [ 'modelTransformOutputS3Location', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'dataProcessingJobId' => [ 'shape' => 'String', ], 'mlModelTrainingJobId' => [ 'shape' => 'String', ], 'trainingJobName' => [ 'shape' => 'String', ], 'modelTransformOutputS3Location' => [ 'shape' => 'String', ], 'sagemakerIamRoleArn' => [ 'shape' => 'String', ], 'neptuneIamRoleArn' => [ 'shape' => 'String', ], 'customModelTransformParameters' => [ 'shape' => 'CustomModelTransformParameters', ], 'baseProcessingInstanceType' => [ 'shape' => 'String', ], 'baseProcessingInstanceVolumeSizeInGB' => [ 'shape' => 'Integer', ], 'subnets' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'volumeEncryptionKMSKey' => [ 'shape' => 'String', ], 's3OutputEncryptionKMSKey' => [ 'shape' => 'String', ], ], ], 'StartMLModelTransformJobOutput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'creationTimeInMillis' => [ 'shape' => 'Long', ], ], ], 'Statistics' => [ 'type' => 'structure', 'members' => [ 'autoCompute' => [ 'shape' => 'Boolean', ], 'active' => [ 'shape' => 'Boolean', ], 'statisticsId' => [ 'shape' => 'String', ], 'date' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'note' => [ 'shape' => 'String', ], 'signatureInfo' => [ 'shape' => 'StatisticsSummary', ], ], ], 'StatisticsAutoGenerationMode' => [ 'type' => 'string', 'enum' => [ 'disableAutoCompute', 'enableAutoCompute', 'refresh', ], ], 'StatisticsNotAvailableException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'StatisticsSummary' => [ 'type' => 'structure', 'members' => [ 'signatureCount' => [ 'shape' => 'Integer', ], 'instanceCount' => [ 'shape' => 'Integer', ], 'predicateCount' => [ 'shape' => 'Integer', ], ], ], 'StreamRecordsNotFoundException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StringValuedMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SubjectStructure' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Long', ], 'predicates' => [ 'shape' => 'Predicates', ], ], ], 'SubjectStructures' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubjectStructure', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeLimitExceededException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'required' => [ 'detailedMessage', 'requestId', 'code', ], 'members' => [ 'detailedMessage' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'code' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
