{"name": "filament/infolists", "description": "Easily add beautiful read-only infolists to any Livewire component.", "license": "MIT", "homepage": "https://github.com/filamentphp/filament", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/console": "^10.45|^11.0", "illuminate/contracts": "^10.45|^11.0", "illuminate/database": "^10.45|^11.0", "illuminate/filesystem": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "illuminate/view": "^10.45|^11.0", "spatie/laravel-package-tools": "^1.9"}, "autoload": {"psr-4": {"Filament\\Infolists\\": "src"}}, "extra": {"laravel": {"providers": ["Filament\\Infolists\\InfolistsServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}