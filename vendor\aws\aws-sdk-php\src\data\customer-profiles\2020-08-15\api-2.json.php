<?php
// This file was auto-generated from sdk-root/src/data/customer-profiles/2020-08-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-08-15', 'endpointPrefix' => 'profile', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Customer Profiles', 'serviceFullName' => 'Amazon Connect Customer Profiles', 'serviceId' => 'Customer Profiles', 'signatureVersion' => 'v4', 'signingName' => 'profile', 'uid' => 'customer-profiles-2020-08-15', ], 'operations' => [ 'AddProfileKey' => [ 'name' => 'AddProfileKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/keys', ], 'input' => [ 'shape' => 'AddProfileKeyRequest', ], 'output' => [ 'shape' => 'AddProfileKeyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateCalculatedAttributeDefinition' => [ 'name' => 'CreateCalculatedAttributeDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/calculated-attributes/{CalculatedAttributeName}', ], 'input' => [ 'shape' => 'CreateCalculatedAttributeDefinitionRequest', ], 'output' => [ 'shape' => 'CreateCalculatedAttributeDefinitionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateEventStream' => [ 'name' => 'CreateEventStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/event-streams/{EventStreamName}', ], 'input' => [ 'shape' => 'CreateEventStreamRequest', ], 'output' => [ 'shape' => 'CreateEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateIntegrationWorkflow' => [ 'name' => 'CreateIntegrationWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/workflows/integrations', ], 'input' => [ 'shape' => 'CreateIntegrationWorkflowRequest', ], 'output' => [ 'shape' => 'CreateIntegrationWorkflowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCalculatedAttributeDefinition' => [ 'name' => 'DeleteCalculatedAttributeDefinition', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{DomainName}/calculated-attributes/{CalculatedAttributeName}', ], 'input' => [ 'shape' => 'DeleteCalculatedAttributeDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteCalculatedAttributeDefinitionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{DomainName}', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEventStream' => [ 'name' => 'DeleteEventStream', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{DomainName}/event-streams/{EventStreamName}', ], 'input' => [ 'shape' => 'DeleteEventStreamRequest', ], 'output' => [ 'shape' => 'DeleteEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteIntegration' => [ 'name' => 'DeleteIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/integrations/delete', ], 'input' => [ 'shape' => 'DeleteIntegrationRequest', ], 'output' => [ 'shape' => 'DeleteIntegrationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/delete', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'output' => [ 'shape' => 'DeleteProfileResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteProfileKey' => [ 'name' => 'DeleteProfileKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/keys/delete', ], 'input' => [ 'shape' => 'DeleteProfileKeyRequest', ], 'output' => [ 'shape' => 'DeleteProfileKeyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteProfileObject' => [ 'name' => 'DeleteProfileObject', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/objects/delete', ], 'input' => [ 'shape' => 'DeleteProfileObjectRequest', ], 'output' => [ 'shape' => 'DeleteProfileObjectResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteProfileObjectType' => [ 'name' => 'DeleteProfileObjectType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{DomainName}/object-types/{ObjectTypeName}', ], 'input' => [ 'shape' => 'DeleteProfileObjectTypeRequest', ], 'output' => [ 'shape' => 'DeleteProfileObjectTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{DomainName}/workflows/{WorkflowId}', ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DetectProfileObjectType' => [ 'name' => 'DetectProfileObjectType', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/detect/object-types', ], 'input' => [ 'shape' => 'DetectProfileObjectTypeRequest', ], 'output' => [ 'shape' => 'DetectProfileObjectTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAutoMergingPreview' => [ 'name' => 'GetAutoMergingPreview', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/identity-resolution-jobs/auto-merging-preview', ], 'input' => [ 'shape' => 'GetAutoMergingPreviewRequest', ], 'output' => [ 'shape' => 'GetAutoMergingPreviewResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCalculatedAttributeDefinition' => [ 'name' => 'GetCalculatedAttributeDefinition', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/calculated-attributes/{CalculatedAttributeName}', ], 'input' => [ 'shape' => 'GetCalculatedAttributeDefinitionRequest', ], 'output' => [ 'shape' => 'GetCalculatedAttributeDefinitionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCalculatedAttributeForProfile' => [ 'name' => 'GetCalculatedAttributeForProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/profile/{ProfileId}/calculated-attributes/{CalculatedAttributeName}', ], 'input' => [ 'shape' => 'GetCalculatedAttributeForProfileRequest', ], 'output' => [ 'shape' => 'GetCalculatedAttributeForProfileResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDomain' => [ 'name' => 'GetDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}', ], 'input' => [ 'shape' => 'GetDomainRequest', ], 'output' => [ 'shape' => 'GetDomainResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEventStream' => [ 'name' => 'GetEventStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/event-streams/{EventStreamName}', ], 'input' => [ 'shape' => 'GetEventStreamRequest', ], 'output' => [ 'shape' => 'GetEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetIdentityResolutionJob' => [ 'name' => 'GetIdentityResolutionJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/identity-resolution-jobs/{JobId}', ], 'input' => [ 'shape' => 'GetIdentityResolutionJobRequest', ], 'output' => [ 'shape' => 'GetIdentityResolutionJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetIntegration' => [ 'name' => 'GetIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/integrations', ], 'input' => [ 'shape' => 'GetIntegrationRequest', ], 'output' => [ 'shape' => 'GetIntegrationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetMatches' => [ 'name' => 'GetMatches', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/matches', ], 'input' => [ 'shape' => 'GetMatchesRequest', ], 'output' => [ 'shape' => 'GetMatchesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProfileObjectType' => [ 'name' => 'GetProfileObjectType', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/object-types/{ObjectTypeName}', ], 'input' => [ 'shape' => 'GetProfileObjectTypeRequest', ], 'output' => [ 'shape' => 'GetProfileObjectTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProfileObjectTypeTemplate' => [ 'name' => 'GetProfileObjectTypeTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/{TemplateId}', ], 'input' => [ 'shape' => 'GetProfileObjectTypeTemplateRequest', ], 'output' => [ 'shape' => 'GetProfileObjectTypeTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSimilarProfiles' => [ 'name' => 'GetSimilarProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/matches', ], 'input' => [ 'shape' => 'GetSimilarProfilesRequest', ], 'output' => [ 'shape' => 'GetSimilarProfilesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/workflows/{WorkflowId}', ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetWorkflowSteps' => [ 'name' => 'GetWorkflowSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/workflows/{WorkflowId}/steps', ], 'input' => [ 'shape' => 'GetWorkflowStepsRequest', ], 'output' => [ 'shape' => 'GetWorkflowStepsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAccountIntegrations' => [ 'name' => 'ListAccountIntegrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/integrations', ], 'input' => [ 'shape' => 'ListAccountIntegrationsRequest', ], 'output' => [ 'shape' => 'ListAccountIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCalculatedAttributeDefinitions' => [ 'name' => 'ListCalculatedAttributeDefinitions', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/calculated-attributes', ], 'input' => [ 'shape' => 'ListCalculatedAttributeDefinitionsRequest', ], 'output' => [ 'shape' => 'ListCalculatedAttributeDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCalculatedAttributesForProfile' => [ 'name' => 'ListCalculatedAttributesForProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/profile/{ProfileId}/calculated-attributes', ], 'input' => [ 'shape' => 'ListCalculatedAttributesForProfileRequest', ], 'output' => [ 'shape' => 'ListCalculatedAttributesForProfileResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDomains' => [ 'name' => 'ListDomains', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains', ], 'input' => [ 'shape' => 'ListDomainsRequest', ], 'output' => [ 'shape' => 'ListDomainsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEventStreams' => [ 'name' => 'ListEventStreams', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/event-streams', ], 'input' => [ 'shape' => 'ListEventStreamsRequest', ], 'output' => [ 'shape' => 'ListEventStreamsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIdentityResolutionJobs' => [ 'name' => 'ListIdentityResolutionJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/identity-resolution-jobs', ], 'input' => [ 'shape' => 'ListIdentityResolutionJobsRequest', ], 'output' => [ 'shape' => 'ListIdentityResolutionJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntegrations' => [ 'name' => 'ListIntegrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/integrations', ], 'input' => [ 'shape' => 'ListIntegrationsRequest', ], 'output' => [ 'shape' => 'ListIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListProfileObjectTypeTemplates' => [ 'name' => 'ListProfileObjectTypeTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates', ], 'input' => [ 'shape' => 'ListProfileObjectTypeTemplatesRequest', ], 'output' => [ 'shape' => 'ListProfileObjectTypeTemplatesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListProfileObjectTypes' => [ 'name' => 'ListProfileObjectTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/object-types', ], 'input' => [ 'shape' => 'ListProfileObjectTypesRequest', ], 'output' => [ 'shape' => 'ListProfileObjectTypesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListProfileObjects' => [ 'name' => 'ListProfileObjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/objects', ], 'input' => [ 'shape' => 'ListProfileObjectsRequest', ], 'output' => [ 'shape' => 'ListProfileObjectsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRuleBasedMatches' => [ 'name' => 'ListRuleBasedMatches', 'http' => [ 'method' => 'GET', 'requestUri' => '/domains/{DomainName}/profiles/ruleBasedMatches', ], 'input' => [ 'shape' => 'ListRuleBasedMatchesRequest', ], 'output' => [ 'shape' => 'ListRuleBasedMatchesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/workflows', ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'MergeProfiles' => [ 'name' => 'MergeProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/objects/merge', ], 'input' => [ 'shape' => 'MergeProfilesRequest', ], 'output' => [ 'shape' => 'MergeProfilesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutIntegration' => [ 'name' => 'PutIntegration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}/integrations', ], 'input' => [ 'shape' => 'PutIntegrationRequest', ], 'output' => [ 'shape' => 'PutIntegrationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutProfileObject' => [ 'name' => 'PutProfileObject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}/profiles/objects', ], 'input' => [ 'shape' => 'PutProfileObjectRequest', ], 'output' => [ 'shape' => 'PutProfileObjectResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutProfileObjectType' => [ 'name' => 'PutProfileObjectType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}/object-types/{ObjectTypeName}', ], 'input' => [ 'shape' => 'PutProfileObjectTypeRequest', ], 'output' => [ 'shape' => 'PutProfileObjectTypeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SearchProfiles' => [ 'name' => 'SearchProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{DomainName}/profiles/search', ], 'input' => [ 'shape' => 'SearchProfilesRequest', ], 'output' => [ 'shape' => 'SearchProfilesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateCalculatedAttributeDefinition' => [ 'name' => 'UpdateCalculatedAttributeDefinition', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}/calculated-attributes/{CalculatedAttributeName}', ], 'input' => [ 'shape' => 'UpdateCalculatedAttributeDefinitionRequest', ], 'output' => [ 'shape' => 'UpdateCalculatedAttributeDefinitionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDomain' => [ 'name' => 'UpdateDomain', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}', ], 'input' => [ 'shape' => 'UpdateDomainRequest', ], 'output' => [ 'shape' => 'UpdateDomainResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{DomainName}/profiles', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'name' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AddProfileKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', 'KeyName', 'Values', 'DomainName', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'AddProfileKeyResponse' => [ 'type' => 'structure', 'members' => [ 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], ], ], 'AdditionalSearchKey' => [ 'type' => 'structure', 'required' => [ 'KeyName', 'Values', ], 'members' => [ 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], ], ], 'Address' => [ 'type' => 'structure', 'members' => [ 'Address1' => [ 'shape' => 'string1To255', ], 'Address2' => [ 'shape' => 'string1To255', ], 'Address3' => [ 'shape' => 'string1To255', ], 'Address4' => [ 'shape' => 'string1To255', ], 'City' => [ 'shape' => 'string1To255', ], 'County' => [ 'shape' => 'string1To255', ], 'State' => [ 'shape' => 'string1To255', ], 'Province' => [ 'shape' => 'string1To255', ], 'Country' => [ 'shape' => 'string1To255', ], 'PostalCode' => [ 'shape' => 'string1To255', ], ], 'sensitive' => true, ], 'AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], 'max' => 4, 'min' => 1, ], 'AppflowIntegration' => [ 'type' => 'structure', 'required' => [ 'FlowDefinition', ], 'members' => [ 'FlowDefinition' => [ 'shape' => 'FlowDefinition', ], 'Batches' => [ 'shape' => 'Batches', ], ], ], 'AppflowIntegrationWorkflowAttributes' => [ 'type' => 'structure', 'required' => [ 'SourceConnectorType', 'ConnectorProfileName', ], 'members' => [ 'SourceConnectorType' => [ 'shape' => 'SourceConnectorType', ], 'ConnectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'RoleArn' => [ 'shape' => 'string1To255', ], ], ], 'AppflowIntegrationWorkflowMetrics' => [ 'type' => 'structure', 'required' => [ 'RecordsProcessed', 'StepsCompleted', 'TotalSteps', ], 'members' => [ 'RecordsProcessed' => [ 'shape' => 'long', ], 'StepsCompleted' => [ 'shape' => 'long', ], 'TotalSteps' => [ 'shape' => 'long', ], ], ], 'AppflowIntegrationWorkflowStep' => [ 'type' => 'structure', 'required' => [ 'FlowName', 'Status', 'ExecutionMessage', 'RecordsProcessed', 'BatchRecordsStartTime', 'BatchRecordsEndTime', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'FlowName' => [ 'shape' => 'FlowName', ], 'Status' => [ 'shape' => 'Status', ], 'ExecutionMessage' => [ 'shape' => 'string1To255', ], 'RecordsProcessed' => [ 'shape' => 'long', ], 'BatchRecordsStartTime' => [ 'shape' => 'string1To255', ], 'BatchRecordsEndTime' => [ 'shape' => 'string1To255', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], ], ], 'AttributeDetails' => [ 'type' => 'structure', 'required' => [ 'Attributes', 'Expression', ], 'members' => [ 'Attributes' => [ 'shape' => 'AttributeList', ], 'Expression' => [ 'shape' => 'string1To255', ], ], 'sensitive' => true, ], 'AttributeItem' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'attributeName', ], ], ], 'AttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeItem', ], 'max' => 2, 'min' => 1, ], 'AttributeMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_TO_ONE', 'MANY_TO_MANY', ], ], 'AttributeSourceIdMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string1To255', ], 'value' => [ 'shape' => 'uuid', ], ], 'AttributeTypesSelector' => [ 'type' => 'structure', 'required' => [ 'AttributeMatchingModel', ], 'members' => [ 'AttributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'Address' => [ 'shape' => 'AddressList', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumberList', ], 'EmailAddress' => [ 'shape' => 'EmailList', ], ], ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'string1To255', ], 'value' => [ 'shape' => 'string1To255', ], 'sensitive' => true, ], 'AutoMerging' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'optionalBoolean', ], 'Consolidation' => [ 'shape' => 'Consolidation', ], 'ConflictResolution' => [ 'shape' => 'ConflictResolution', ], 'MinAllowedConfidenceScoreForMerging' => [ 'shape' => 'Double0To1', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Batch' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], ], ], 'Batches' => [ 'type' => 'list', 'member' => [ 'shape' => 'Batch', ], ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '\\S+', ], 'BucketPrefix' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'CalculatedAttributeDefinitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListCalculatedAttributeDefinitionItem', ], 'sensitive' => true, ], 'CalculatedAttributesForProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListCalculatedAttributeForProfileItem', ], ], 'Conditions' => [ 'type' => 'structure', 'members' => [ 'Range' => [ 'shape' => 'Range', ], 'ObjectCount' => [ 'shape' => 'ObjectCount', ], 'Threshold' => [ 'shape' => 'Threshold', ], ], 'sensitive' => true, ], 'ConflictResolution' => [ 'type' => 'structure', 'required' => [ 'ConflictResolvingModel', ], 'members' => [ 'ConflictResolvingModel' => [ 'shape' => 'ConflictResolvingModel', ], 'SourceName' => [ 'shape' => 'string1To255', ], ], ], 'ConflictResolvingModel' => [ 'type' => 'string', 'enum' => [ 'RECENCY', 'SOURCE', ], ], 'ConnectorOperator' => [ 'type' => 'structure', 'members' => [ 'Marketo' => [ 'shape' => 'MarketoConnectorOperator', ], 'S3' => [ 'shape' => 'S3ConnectorOperator', ], 'Salesforce' => [ 'shape' => 'SalesforceConnectorOperator', ], 'ServiceNow' => [ 'shape' => 'ServiceNowConnectorOperator', ], 'Zendesk' => [ 'shape' => 'ZendeskConnectorOperator', ], ], ], 'ConnectorProfileName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\w/!@#+=.-]+', ], 'Consolidation' => [ 'type' => 'structure', 'required' => [ 'MatchingAttributesList', ], 'members' => [ 'MatchingAttributesList' => [ 'shape' => 'MatchingAttributesList', ], ], ], 'CreateCalculatedAttributeDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CalculatedAttributeName', 'AttributeDetails', 'Statistic', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'CalculatedAttributeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'CalculatedAttributeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'AttributeDetails' => [ 'shape' => 'AttributeDetails', ], 'Conditions' => [ 'shape' => 'Conditions', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateCalculatedAttributeDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'AttributeDetails' => [ 'shape' => 'AttributeDetails', ], 'Conditions' => [ 'shape' => 'Conditions', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'DefaultExpirationDays', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'DefaultExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'DefaultEncryptionKey' => [ 'shape' => 'encryptionKey', ], 'DeadLetterQueueUrl' => [ 'shape' => 'sqsQueueUrl', ], 'Matching' => [ 'shape' => 'MatchingRequest', ], 'RuleBasedMatching' => [ 'shape' => 'RuleBasedMatchingRequest', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDomainResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'DefaultExpirationDays', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'DefaultExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'DefaultEncryptionKey' => [ 'shape' => 'encryptionKey', ], 'DeadLetterQueueUrl' => [ 'shape' => 'sqsQueueUrl', ], 'Matching' => [ 'shape' => 'MatchingResponse', ], 'RuleBasedMatching' => [ 'shape' => 'RuleBasedMatchingResponse', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEventStreamRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', 'EventStreamName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Uri' => [ 'shape' => 'string1To255', ], 'EventStreamName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'EventStreamName', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEventStreamResponse' => [ 'type' => 'structure', 'required' => [ 'EventStreamArn', ], 'members' => [ 'EventStreamArn' => [ 'shape' => 'string1To255', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIntegrationWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'WorkflowType', 'IntegrationConfig', 'ObjectTypeName', 'RoleArn', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'WorkflowType' => [ 'shape' => 'WorkflowType', ], 'IntegrationConfig' => [ 'shape' => 'IntegrationConfig', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIntegrationWorkflowResponse' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', 'Message', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'uuid', ], 'Message' => [ 'shape' => 'string1To255', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'AccountNumber' => [ 'shape' => 'sensitiveString1To255', ], 'AdditionalInformation' => [ 'shape' => 'sensitiveString1To1000', ], 'PartyType' => [ 'shape' => 'PartyType', ], 'BusinessName' => [ 'shape' => 'sensitiveString1To255', ], 'FirstName' => [ 'shape' => 'sensitiveString1To255', ], 'MiddleName' => [ 'shape' => 'sensitiveString1To255', ], 'LastName' => [ 'shape' => 'sensitiveString1To255', ], 'BirthDate' => [ 'shape' => 'sensitiveString1To255', ], 'Gender' => [ 'shape' => 'Gender', ], 'PhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'MobilePhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'HomePhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'BusinessPhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'EmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'PersonalEmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'BusinessEmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'Address' => [ 'shape' => 'Address', ], 'ShippingAddress' => [ 'shape' => 'Address', ], 'MailingAddress' => [ 'shape' => 'Address', ], 'BillingAddress' => [ 'shape' => 'Address', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'PartyTypeString' => [ 'shape' => 'sensitiveString1To255', ], 'GenderString' => [ 'shape' => 'sensitiveString1To255', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], ], ], 'DataPullMode' => [ 'type' => 'string', 'enum' => [ 'Incremental', 'Complete', ], ], 'Date' => [ 'type' => 'timestamp', ], 'DatetimeTypeFieldName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'DeleteCalculatedAttributeDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CalculatedAttributeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'CalculatedAttributeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'CalculatedAttributeName', ], ], ], 'DeleteCalculatedAttributeDefinitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteDomainResponse' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteEventStreamRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'EventStreamName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'EventStreamName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'EventStreamName', ], ], ], 'DeleteEventStreamResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Uri' => [ 'shape' => 'string1To255', ], ], ], 'DeleteIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteProfileKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', 'KeyName', 'Values', 'DomainName', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteProfileKeyResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteProfileObjectRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', 'ProfileObjectUniqueKey', 'ObjectTypeName', 'DomainName', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], 'ProfileObjectUniqueKey' => [ 'shape' => 'string1To255', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteProfileObjectResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteProfileObjectTypeRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ObjectTypeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ObjectTypeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'ObjectTypeName', ], ], ], 'DeleteProfileObjectTypeResponse' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', 'DomainName', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'WorkflowId', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'WorkflowId' => [ 'shape' => 'string1To255', 'location' => 'uri', 'locationName' => 'WorkflowId', ], ], ], 'DeleteWorkflowResponse' => [ 'type' => 'structure', 'members' => [], ], 'DestinationField' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'DestinationSummary' => [ 'type' => 'structure', 'required' => [ 'Uri', 'Status', ], 'members' => [ 'Uri' => [ 'shape' => 'string1To255', ], 'Status' => [ 'shape' => 'EventStreamDestinationStatus', ], 'UnhealthySince' => [ 'shape' => 'timestamp', ], ], ], 'DetectProfileObjectTypeRequest' => [ 'type' => 'structure', 'required' => [ 'Objects', 'DomainName', ], 'members' => [ 'Objects' => [ 'shape' => 'Objects', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DetectProfileObjectTypeResponse' => [ 'type' => 'structure', 'members' => [ 'DetectedProfileObjectTypes' => [ 'shape' => 'DetectedProfileObjectTypes', ], ], ], 'DetectedProfileObjectType' => [ 'type' => 'structure', 'members' => [ 'SourceLastUpdatedTimestampFormat' => [ 'shape' => 'string1To255', ], 'Fields' => [ 'shape' => 'FieldMap', ], 'Keys' => [ 'shape' => 'KeyMap', ], ], ], 'DetectedProfileObjectTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectedProfileObjectType', ], ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListDomainItem', ], ], 'DomainStats' => [ 'type' => 'structure', 'members' => [ 'ProfileCount' => [ 'shape' => 'long', ], 'MeteringProfileCount' => [ 'shape' => 'long', ], 'ObjectCount' => [ 'shape' => 'long', ], 'TotalSize' => [ 'shape' => 'long', ], ], ], 'Double' => [ 'type' => 'double', ], 'Double0To1' => [ 'type' => 'double', 'max' => 1.0, 'min' => 0.0, ], 'EmailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], 'max' => 3, 'min' => 1, ], 'EventStreamDestinationDetails' => [ 'type' => 'structure', 'required' => [ 'Uri', 'Status', ], 'members' => [ 'Uri' => [ 'shape' => 'string1To255', ], 'Status' => [ 'shape' => 'EventStreamDestinationStatus', ], 'UnhealthySince' => [ 'shape' => 'timestamp', ], 'Message' => [ 'shape' => 'string1To1000', ], ], ], 'EventStreamDestinationStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'EventStreamState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', ], ], 'EventStreamSummary' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'EventStreamName', 'EventStreamArn', 'State', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'EventStreamName' => [ 'shape' => 'name', ], 'EventStreamArn' => [ 'shape' => 'string1To255', ], 'State' => [ 'shape' => 'EventStreamState', ], 'StoppedSince' => [ 'shape' => 'timestamp', ], 'DestinationSummary' => [ 'shape' => 'DestinationSummary', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'EventStreamSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventStreamSummary', ], ], 'ExportingConfig' => [ 'type' => 'structure', 'members' => [ 'S3Exporting' => [ 'shape' => 'S3ExportingConfig', ], ], ], 'ExportingLocation' => [ 'type' => 'structure', 'members' => [ 'S3Exporting' => [ 'shape' => 'S3ExportingLocation', ], ], ], 'FieldContentType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'NUMBER', 'PHONE_NUMBER', 'EMAIL_ADDRESS', 'NAME', ], ], 'FieldMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'name', ], 'value' => [ 'shape' => 'ObjectTypeField', ], 'sensitive' => true, ], 'FieldNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'name', ], ], 'FieldSourceProfileIds' => [ 'type' => 'structure', 'members' => [ 'AccountNumber' => [ 'shape' => 'uuid', ], 'AdditionalInformation' => [ 'shape' => 'uuid', ], 'PartyType' => [ 'shape' => 'uuid', ], 'BusinessName' => [ 'shape' => 'uuid', ], 'FirstName' => [ 'shape' => 'uuid', ], 'MiddleName' => [ 'shape' => 'uuid', ], 'LastName' => [ 'shape' => 'uuid', ], 'BirthDate' => [ 'shape' => 'uuid', ], 'Gender' => [ 'shape' => 'uuid', ], 'PhoneNumber' => [ 'shape' => 'uuid', ], 'MobilePhoneNumber' => [ 'shape' => 'uuid', ], 'HomePhoneNumber' => [ 'shape' => 'uuid', ], 'BusinessPhoneNumber' => [ 'shape' => 'uuid', ], 'EmailAddress' => [ 'shape' => 'uuid', ], 'PersonalEmailAddress' => [ 'shape' => 'uuid', ], 'BusinessEmailAddress' => [ 'shape' => 'uuid', ], 'Address' => [ 'shape' => 'uuid', ], 'ShippingAddress' => [ 'shape' => 'uuid', ], 'MailingAddress' => [ 'shape' => 'uuid', ], 'BillingAddress' => [ 'shape' => 'uuid', ], 'Attributes' => [ 'shape' => 'AttributeSourceIdMap', ], ], ], 'FlowDefinition' => [ 'type' => 'structure', 'required' => [ 'FlowName', 'KmsArn', 'SourceFlowConfig', 'Tasks', 'TriggerConfig', ], 'members' => [ 'Description' => [ 'shape' => 'FlowDescription', ], 'FlowName' => [ 'shape' => 'FlowName', ], 'KmsArn' => [ 'shape' => 'KmsArn', ], 'SourceFlowConfig' => [ 'shape' => 'SourceFlowConfig', ], 'Tasks' => [ 'shape' => 'Tasks', ], 'TriggerConfig' => [ 'shape' => 'TriggerConfig', ], ], 'sensitive' => true, ], 'FlowDescription' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\w!@#\\-.?,\\s]*', ], 'FlowName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[a-zA-Z0-9][\\w!@#.-]+', ], 'FoundByKeyValue' => [ 'type' => 'structure', 'members' => [ 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], ], ], 'Gender' => [ 'type' => 'string', 'deprecated' => true, 'enum' => [ 'MALE', 'FEMALE', 'UNSPECIFIED', ], 'sensitive' => true, ], 'GetAutoMergingPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Consolidation', 'ConflictResolution', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Consolidation' => [ 'shape' => 'Consolidation', ], 'ConflictResolution' => [ 'shape' => 'ConflictResolution', ], 'MinAllowedConfidenceScoreForMerging' => [ 'shape' => 'Double0To1', ], ], ], 'GetAutoMergingPreviewResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'NumberOfMatchesInSample' => [ 'shape' => 'long', ], 'NumberOfProfilesInSample' => [ 'shape' => 'long', ], 'NumberOfProfilesWillBeMerged' => [ 'shape' => 'long', ], ], ], 'GetCalculatedAttributeDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CalculatedAttributeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'CalculatedAttributeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'CalculatedAttributeName', ], ], ], 'GetCalculatedAttributeDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Conditions' => [ 'shape' => 'Conditions', ], 'AttributeDetails' => [ 'shape' => 'AttributeDetails', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetCalculatedAttributeForProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ProfileId', 'CalculatedAttributeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ProfileId' => [ 'shape' => 'uuid', 'location' => 'uri', 'locationName' => 'ProfileId', ], 'CalculatedAttributeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'CalculatedAttributeName', ], ], ], 'GetCalculatedAttributeForProfileResponse' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'IsDataPartial' => [ 'shape' => 'string1To255', ], 'Value' => [ 'shape' => 'string1To255', ], ], ], 'GetDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'GetDomainResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'DefaultExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'DefaultEncryptionKey' => [ 'shape' => 'encryptionKey', ], 'DeadLetterQueueUrl' => [ 'shape' => 'sqsQueueUrl', ], 'Stats' => [ 'shape' => 'DomainStats', ], 'Matching' => [ 'shape' => 'MatchingResponse', ], 'RuleBasedMatching' => [ 'shape' => 'RuleBasedMatchingResponse', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetEventStreamRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'EventStreamName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'EventStreamName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'EventStreamName', ], ], ], 'GetEventStreamResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'EventStreamArn', 'CreatedAt', 'State', 'DestinationDetails', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'EventStreamArn' => [ 'shape' => 'string1To255', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'State' => [ 'shape' => 'EventStreamState', ], 'StoppedSince' => [ 'shape' => 'timestamp', ], 'DestinationDetails' => [ 'shape' => 'EventStreamDestinationDetails', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetIdentityResolutionJobRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'JobId', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'JobId' => [ 'shape' => 'uuid', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'GetIdentityResolutionJobResponse' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'JobId' => [ 'shape' => 'uuid', ], 'Status' => [ 'shape' => 'IdentityResolutionJobStatus', ], 'Message' => [ 'shape' => 'stringTo2048', ], 'JobStartTime' => [ 'shape' => 'timestamp', ], 'JobEndTime' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'JobExpirationTime' => [ 'shape' => 'timestamp', ], 'AutoMerging' => [ 'shape' => 'AutoMerging', ], 'ExportingLocation' => [ 'shape' => 'ExportingLocation', ], 'JobStats' => [ 'shape' => 'JobStats', ], ], ], 'GetIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Uri' => [ 'shape' => 'string1To255', ], ], ], 'GetIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'Uri' => [ 'shape' => 'string1To255', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ObjectTypeNames' => [ 'shape' => 'ObjectTypeNames', ], 'WorkflowId' => [ 'shape' => 'string1To255', ], 'IsUnstructured' => [ 'shape' => 'optionalBoolean', ], ], ], 'GetMatchesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'GetMatchesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'token', ], 'MatchGenerationDate' => [ 'shape' => 'timestamp', ], 'PotentialMatches' => [ 'shape' => 'matchesNumber', ], 'Matches' => [ 'shape' => 'MatchesList', ], ], ], 'GetProfileObjectTypeRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ObjectTypeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ObjectTypeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'ObjectTypeName', ], ], ], 'GetProfileObjectTypeResponse' => [ 'type' => 'structure', 'required' => [ 'ObjectTypeName', 'Description', ], 'members' => [ 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'TemplateId' => [ 'shape' => 'name', ], 'ExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'EncryptionKey' => [ 'shape' => 'encryptionKey', ], 'AllowProfileCreation' => [ 'shape' => 'boolean', ], 'SourceLastUpdatedTimestampFormat' => [ 'shape' => 'string1To255', ], 'Fields' => [ 'shape' => 'FieldMap', ], 'Keys' => [ 'shape' => 'KeyMap', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetProfileObjectTypeTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateId', ], 'members' => [ 'TemplateId' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'TemplateId', ], ], ], 'GetProfileObjectTypeTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateId' => [ 'shape' => 'name', ], 'SourceName' => [ 'shape' => 'name', ], 'SourceObject' => [ 'shape' => 'name', ], 'AllowProfileCreation' => [ 'shape' => 'boolean', ], 'SourceLastUpdatedTimestampFormat' => [ 'shape' => 'string1To255', ], 'Fields' => [ 'shape' => 'FieldMap', ], 'Keys' => [ 'shape' => 'KeyMap', ], ], ], 'GetSimilarProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'MatchType', 'SearchKey', 'SearchValue', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MatchType' => [ 'shape' => 'MatchType', ], 'SearchKey' => [ 'shape' => 'string1To255', ], 'SearchValue' => [ 'shape' => 'string1To255', ], ], ], 'GetSimilarProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'ProfileIds' => [ 'shape' => 'ProfileIdList', ], 'MatchId' => [ 'shape' => 'string1To255', ], 'MatchType' => [ 'shape' => 'MatchType', ], 'RuleLevel' => [ 'shape' => 'RuleLevel', ], 'ConfidenceScore' => [ 'shape' => 'Double', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'WorkflowId', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'WorkflowId' => [ 'shape' => 'uuid', 'location' => 'uri', 'locationName' => 'WorkflowId', ], ], ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'WorkflowId' => [ 'shape' => 'uuid', ], 'WorkflowType' => [ 'shape' => 'WorkflowType', ], 'Status' => [ 'shape' => 'Status', ], 'ErrorDescription' => [ 'shape' => 'string1To255', ], 'StartDate' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Attributes' => [ 'shape' => 'WorkflowAttributes', ], 'Metrics' => [ 'shape' => 'WorkflowMetrics', ], ], ], 'GetWorkflowStepsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'WorkflowId', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'WorkflowId' => [ 'shape' => 'uuid', 'location' => 'uri', 'locationName' => 'WorkflowId', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'GetWorkflowStepsResponse' => [ 'type' => 'structure', 'members' => [ 'WorkflowId' => [ 'shape' => 'uuid', ], 'WorkflowType' => [ 'shape' => 'WorkflowType', ], 'Items' => [ 'shape' => 'WorkflowStepsList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'IdentityResolutionJob' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'JobId' => [ 'shape' => 'uuid', ], 'Status' => [ 'shape' => 'IdentityResolutionJobStatus', ], 'JobStartTime' => [ 'shape' => 'timestamp', ], 'JobEndTime' => [ 'shape' => 'timestamp', ], 'JobStats' => [ 'shape' => 'JobStats', ], 'ExportingLocation' => [ 'shape' => 'ExportingLocation', ], 'Message' => [ 'shape' => 'stringTo2048', ], ], ], 'IdentityResolutionJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PREPROCESSING', 'FIND_MATCHING', 'MERGING', 'COMPLETED', 'PARTIAL_SUCCESS', 'FAILED', ], ], 'IdentityResolutionJobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityResolutionJob', ], ], 'IncrementalPullConfig' => [ 'type' => 'structure', 'members' => [ 'DatetimeTypeFieldName' => [ 'shape' => 'DatetimeTypeFieldName', ], ], ], 'IntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'AppflowIntegration' => [ 'shape' => 'AppflowIntegration', ], ], ], 'IntegrationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListIntegrationItem', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JobSchedule' => [ 'type' => 'structure', 'required' => [ 'DayOfTheWeek', 'Time', ], 'members' => [ 'DayOfTheWeek' => [ 'shape' => 'JobScheduleDayOfTheWeek', ], 'Time' => [ 'shape' => 'JobScheduleTime', ], ], ], 'JobScheduleDayOfTheWeek' => [ 'type' => 'string', 'enum' => [ 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', ], ], 'JobScheduleTime' => [ 'type' => 'string', 'max' => 5, 'min' => 3, 'pattern' => '^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$', ], 'JobStats' => [ 'type' => 'structure', 'members' => [ 'NumberOfProfilesReviewed' => [ 'shape' => 'long', ], 'NumberOfMatchesFound' => [ 'shape' => 'long', ], 'NumberOfMergesDone' => [ 'shape' => 'long', ], ], ], 'KeyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'name', ], 'value' => [ 'shape' => 'ObjectTypeKeyList', ], 'sensitive' => true, ], 'KmsArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:kms:.*:[0-9]+:.*', ], 'ListAccountIntegrationsRequest' => [ 'type' => 'structure', 'required' => [ 'Uri', ], 'members' => [ 'Uri' => [ 'shape' => 'string1To255', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'IncludeHidden' => [ 'shape' => 'optionalBoolean', 'location' => 'querystring', 'locationName' => 'include-hidden', ], ], ], 'ListAccountIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'IntegrationList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListCalculatedAttributeDefinitionItem' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListCalculatedAttributeDefinitionsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListCalculatedAttributeDefinitionsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'CalculatedAttributeDefinitionsList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListCalculatedAttributeForProfileItem' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'IsDataPartial' => [ 'shape' => 'string1To255', ], 'Value' => [ 'shape' => 'string1To255', ], ], ], 'ListCalculatedAttributesForProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ProfileId', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ProfileId' => [ 'shape' => 'uuid', 'location' => 'uri', 'locationName' => 'ProfileId', ], ], ], 'ListCalculatedAttributesForProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'CalculatedAttributesForProfileList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListDomainItem' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'DomainList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListEventStreamsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListEventStreamsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'EventStreamSummaryList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListIdentityResolutionJobsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListIdentityResolutionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'IdentityResolutionJobsList' => [ 'shape' => 'IdentityResolutionJobsList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListIntegrationItem' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'Uri' => [ 'shape' => 'string1To255', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ObjectTypeNames' => [ 'shape' => 'ObjectTypeNames', ], 'WorkflowId' => [ 'shape' => 'string1To255', ], 'IsUnstructured' => [ 'shape' => 'optionalBoolean', ], ], ], 'ListIntegrationsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'IncludeHidden' => [ 'shape' => 'optionalBoolean', 'location' => 'querystring', 'locationName' => 'include-hidden', ], ], ], 'ListIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'IntegrationList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListProfileObjectTypeItem' => [ 'type' => 'structure', 'required' => [ 'ObjectTypeName', 'Description', ], 'members' => [ 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'Description' => [ 'shape' => 'text', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListProfileObjectTypeTemplateItem' => [ 'type' => 'structure', 'members' => [ 'TemplateId' => [ 'shape' => 'name', ], 'SourceName' => [ 'shape' => 'name', ], 'SourceObject' => [ 'shape' => 'name', ], ], ], 'ListProfileObjectTypeTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListProfileObjectTypeTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ProfileObjectTypeTemplateList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListProfileObjectTypesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListProfileObjectTypesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ProfileObjectTypeList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListProfileObjectsItem' => [ 'type' => 'structure', 'members' => [ 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'ProfileObjectUniqueKey' => [ 'shape' => 'string1To255', ], 'Object' => [ 'shape' => 'stringifiedJson', ], ], ], 'ListProfileObjectsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ObjectTypeName', 'ProfileId', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'ProfileId' => [ 'shape' => 'uuid', ], 'ObjectFilter' => [ 'shape' => 'ObjectFilter', ], ], ], 'ListProfileObjectsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ProfileObjectList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListRuleBasedMatchesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'ListRuleBasedMatchesResponse' => [ 'type' => 'structure', 'members' => [ 'MatchIds' => [ 'shape' => 'MatchIdList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWorkflowsItem' => [ 'type' => 'structure', 'required' => [ 'WorkflowType', 'WorkflowId', 'Status', 'StatusDescription', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'WorkflowType' => [ 'shape' => 'WorkflowType', ], 'WorkflowId' => [ 'shape' => 'string1To255', ], 'Status' => [ 'shape' => 'Status', ], 'StatusDescription' => [ 'shape' => 'string1To255', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'WorkflowType' => [ 'shape' => 'WorkflowType', ], 'Status' => [ 'shape' => 'Status', ], 'QueryStartDate' => [ 'shape' => 'timestamp', ], 'QueryEndDate' => [ 'shape' => 'timestamp', ], 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'WorkflowList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'MarketoConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'MarketoSourceProperties' => [ 'type' => 'structure', 'required' => [ 'Object', ], 'members' => [ 'Object' => [ 'shape' => 'Object', ], ], ], 'MatchIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], ], 'MatchItem' => [ 'type' => 'structure', 'members' => [ 'MatchId' => [ 'shape' => 'string1To255', ], 'ProfileIds' => [ 'shape' => 'ProfileIdList', ], 'ConfidenceScore' => [ 'shape' => 'Double', ], ], ], 'MatchType' => [ 'type' => 'string', 'enum' => [ 'RULE_BASED_MATCHING', 'ML_BASED_MATCHING', ], ], 'MatchesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchItem', ], ], 'MatchingAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], 'max' => 20, 'min' => 1, ], 'MatchingAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingAttributes', ], 'max' => 10, 'min' => 1, ], 'MatchingRequest' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'optionalBoolean', ], 'JobSchedule' => [ 'shape' => 'JobSchedule', ], 'AutoMerging' => [ 'shape' => 'AutoMerging', ], 'ExportingConfig' => [ 'shape' => 'ExportingConfig', ], ], ], 'MatchingResponse' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'optionalBoolean', ], 'JobSchedule' => [ 'shape' => 'JobSchedule', ], 'AutoMerging' => [ 'shape' => 'AutoMerging', ], 'ExportingConfig' => [ 'shape' => 'ExportingConfig', ], ], ], 'MatchingRule' => [ 'type' => 'structure', 'required' => [ 'Rule', ], 'members' => [ 'Rule' => [ 'shape' => 'MatchingRuleAttributeList', ], ], ], 'MatchingRuleAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], 'max' => 15, 'min' => 1, ], 'MatchingRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingRule', ], 'max' => 15, 'min' => 1, ], 'MaxAllowedRuleLevelForMatching' => [ 'type' => 'integer', 'max' => 15, 'min' => 1, ], 'MaxAllowedRuleLevelForMerging' => [ 'type' => 'integer', 'max' => 15, 'min' => 1, ], 'MergeProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'MainProfileId', 'ProfileIdsToBeMerged', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MainProfileId' => [ 'shape' => 'uuid', ], 'ProfileIdsToBeMerged' => [ 'shape' => 'ProfileIdToBeMergedList', ], 'FieldSourceProfileIds' => [ 'shape' => 'FieldSourceProfileIds', ], ], ], 'MergeProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], ], 'Object' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ObjectCount' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ObjectFilter' => [ 'type' => 'structure', 'required' => [ 'KeyName', 'Values', ], 'members' => [ 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], ], ], 'ObjectTypeField' => [ 'type' => 'structure', 'members' => [ 'Source' => [ 'shape' => 'text', ], 'Target' => [ 'shape' => 'text', ], 'ContentType' => [ 'shape' => 'FieldContentType', ], ], ], 'ObjectTypeKey' => [ 'type' => 'structure', 'members' => [ 'StandardIdentifiers' => [ 'shape' => 'StandardIdentifierList', ], 'FieldNames' => [ 'shape' => 'FieldNameList', ], ], ], 'ObjectTypeKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectTypeKey', ], ], 'ObjectTypeNames' => [ 'type' => 'map', 'key' => [ 'shape' => 'string1To255', ], 'value' => [ 'shape' => 'typeName', ], ], 'Objects' => [ 'type' => 'list', 'member' => [ 'shape' => 'stringifiedJson', ], 'max' => 5, 'min' => 1, 'sensitive' => true, ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'EQUAL_TO', 'GREATER_THAN', 'LESS_THAN', 'NOT_EQUAL_TO', ], ], 'OperatorPropertiesKeys' => [ 'type' => 'string', 'enum' => [ 'VALUE', 'VALUES', 'DATA_TYPE', 'UPPER_BOUND', 'LOWER_BOUND', 'SOURCE_DATA_TYPE', 'DESTINATION_DATA_TYPE', 'VALIDATION_ACTION', 'MASK_VALUE', 'MASK_LENGTH', 'TRUNCATE_LENGTH', 'MATH_OPERATION_FIELDS_ORDER', 'CONCAT_FORMAT', 'SUBFIELD_CATEGORY_MAP', ], ], 'PartyType' => [ 'type' => 'string', 'deprecated' => true, 'enum' => [ 'INDIVIDUAL', 'BUSINESS', 'OTHER', ], 'sensitive' => true, ], 'PhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], 'max' => 4, 'min' => 1, ], 'Profile' => [ 'type' => 'structure', 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], 'AccountNumber' => [ 'shape' => 'sensitiveString1To255', ], 'AdditionalInformation' => [ 'shape' => 'sensitiveString1To1000', ], 'PartyType' => [ 'shape' => 'PartyType', ], 'BusinessName' => [ 'shape' => 'sensitiveString1To255', ], 'FirstName' => [ 'shape' => 'sensitiveString1To255', ], 'MiddleName' => [ 'shape' => 'sensitiveString1To255', ], 'LastName' => [ 'shape' => 'sensitiveString1To255', ], 'BirthDate' => [ 'shape' => 'sensitiveString1To255', ], 'Gender' => [ 'shape' => 'Gender', ], 'PhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'MobilePhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'HomePhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'BusinessPhoneNumber' => [ 'shape' => 'sensitiveString1To255', ], 'EmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'PersonalEmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'BusinessEmailAddress' => [ 'shape' => 'sensitiveString1To255', ], 'Address' => [ 'shape' => 'Address', ], 'ShippingAddress' => [ 'shape' => 'Address', ], 'MailingAddress' => [ 'shape' => 'Address', ], 'BillingAddress' => [ 'shape' => 'Address', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'FoundByItems' => [ 'shape' => 'foundByList', ], 'PartyTypeString' => [ 'shape' => 'sensitiveString1To255', ], 'GenderString' => [ 'shape' => 'sensitiveString1To255', ], ], ], 'ProfileIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'uuid', ], ], 'ProfileIdToBeMergedList' => [ 'type' => 'list', 'member' => [ 'shape' => 'uuid', ], 'max' => 20, 'min' => 1, ], 'ProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Profile', ], ], 'ProfileObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListProfileObjectsItem', ], ], 'ProfileObjectTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListProfileObjectTypeItem', ], 'sensitive' => true, ], 'ProfileObjectTypeTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListProfileObjectTypeTemplateItem', ], ], 'Property' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.+', ], 'PutIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Uri' => [ 'shape' => 'string1To255', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'Tags' => [ 'shape' => 'TagMap', ], 'FlowDefinition' => [ 'shape' => 'FlowDefinition', ], 'ObjectTypeNames' => [ 'shape' => 'ObjectTypeNames', ], ], ], 'PutIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Uri', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'Uri' => [ 'shape' => 'string1To255', ], 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ObjectTypeNames' => [ 'shape' => 'ObjectTypeNames', ], 'WorkflowId' => [ 'shape' => 'string1To255', ], 'IsUnstructured' => [ 'shape' => 'optionalBoolean', ], ], ], 'PutProfileObjectRequest' => [ 'type' => 'structure', 'required' => [ 'ObjectTypeName', 'Object', 'DomainName', ], 'members' => [ 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'Object' => [ 'shape' => 'stringifiedJson', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'PutProfileObjectResponse' => [ 'type' => 'structure', 'members' => [ 'ProfileObjectUniqueKey' => [ 'shape' => 'string1To255', ], ], ], 'PutProfileObjectTypeRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ObjectTypeName', 'Description', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ObjectTypeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'ObjectTypeName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'TemplateId' => [ 'shape' => 'name', ], 'ExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'EncryptionKey' => [ 'shape' => 'encryptionKey', ], 'AllowProfileCreation' => [ 'shape' => 'boolean', ], 'SourceLastUpdatedTimestampFormat' => [ 'shape' => 'string1To255', ], 'Fields' => [ 'shape' => 'FieldMap', ], 'Keys' => [ 'shape' => 'KeyMap', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'PutProfileObjectTypeResponse' => [ 'type' => 'structure', 'required' => [ 'ObjectTypeName', 'Description', ], 'members' => [ 'ObjectTypeName' => [ 'shape' => 'typeName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'TemplateId' => [ 'shape' => 'name', ], 'ExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'EncryptionKey' => [ 'shape' => 'encryptionKey', ], 'AllowProfileCreation' => [ 'shape' => 'boolean', ], 'SourceLastUpdatedTimestampFormat' => [ 'shape' => 'string1To255', ], 'Fields' => [ 'shape' => 'FieldMap', ], 'Keys' => [ 'shape' => 'KeyMap', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'Range' => [ 'type' => 'structure', 'required' => [ 'Value', 'Unit', ], 'members' => [ 'Value' => [ 'shape' => 'Value', ], 'Unit' => [ 'shape' => 'Unit', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 512, 'pattern' => 'arn:aws:iam:.*:[0-9]+:.*', ], 'RuleBasedMatchingRequest' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'optionalBoolean', ], 'MatchingRules' => [ 'shape' => 'MatchingRules', ], 'MaxAllowedRuleLevelForMerging' => [ 'shape' => 'MaxAllowedRuleLevelForMerging', ], 'MaxAllowedRuleLevelForMatching' => [ 'shape' => 'MaxAllowedRuleLevelForMatching', ], 'AttributeTypesSelector' => [ 'shape' => 'AttributeTypesSelector', ], 'ConflictResolution' => [ 'shape' => 'ConflictResolution', ], 'ExportingConfig' => [ 'shape' => 'ExportingConfig', ], ], ], 'RuleBasedMatchingResponse' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'optionalBoolean', ], 'MatchingRules' => [ 'shape' => 'MatchingRules', ], 'Status' => [ 'shape' => 'RuleBasedMatchingStatus', ], 'MaxAllowedRuleLevelForMerging' => [ 'shape' => 'MaxAllowedRuleLevelForMerging', ], 'MaxAllowedRuleLevelForMatching' => [ 'shape' => 'MaxAllowedRuleLevelForMatching', ], 'AttributeTypesSelector' => [ 'shape' => 'AttributeTypesSelector', ], 'ConflictResolution' => [ 'shape' => 'ConflictResolution', ], 'ExportingConfig' => [ 'shape' => 'ExportingConfig', ], ], ], 'RuleBasedMatchingStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'ACTIVE', ], ], 'RuleLevel' => [ 'type' => 'integer', 'max' => 15, 'min' => 1, ], 'S3ConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'S3ExportingConfig' => [ 'type' => 'structure', 'required' => [ 'S3BucketName', ], 'members' => [ 'S3BucketName' => [ 'shape' => 's3BucketName', ], 'S3KeyName' => [ 'shape' => 's3KeyNameCustomerOutputConfig', ], ], ], 'S3ExportingLocation' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 's3BucketName', ], 'S3KeyName' => [ 'shape' => 's3KeyName', ], ], ], 'S3SourceProperties' => [ 'type' => 'structure', 'required' => [ 'BucketName', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'BucketPrefix' => [ 'shape' => 'BucketPrefix', ], ], ], 'SalesforceConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'LESS_THAN', 'CONTAINS', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'SalesforceSourceProperties' => [ 'type' => 'structure', 'required' => [ 'Object', ], 'members' => [ 'Object' => [ 'shape' => 'Object', ], 'EnableDynamicFieldUpdate' => [ 'shape' => 'boolean', ], 'IncludeDeletedRecords' => [ 'shape' => 'boolean', ], ], ], 'ScheduleExpression' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ScheduleOffset' => [ 'type' => 'long', 'max' => 36000, 'min' => 0, ], 'ScheduledTriggerProperties' => [ 'type' => 'structure', 'required' => [ 'ScheduleExpression', ], 'members' => [ 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'DataPullMode' => [ 'shape' => 'DataPullMode', ], 'ScheduleStartTime' => [ 'shape' => 'Date', ], 'ScheduleEndTime' => [ 'shape' => 'Date', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'ScheduleOffset' => [ 'shape' => 'ScheduleOffset', 'box' => true, ], 'FirstExecutionFrom' => [ 'shape' => 'Date', ], ], ], 'SearchProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'KeyName', 'Values', ], 'members' => [ 'NextToken' => [ 'shape' => 'token', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'maxSize100', 'location' => 'querystring', 'locationName' => 'max-results', ], 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'KeyName' => [ 'shape' => 'name', ], 'Values' => [ 'shape' => 'requestValueList', ], 'AdditionalSearchKeys' => [ 'shape' => 'additionalSearchKeysList', ], 'LogicalOperator' => [ 'shape' => 'logicalOperator', ], ], ], 'SearchProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ProfileList', ], 'NextToken' => [ 'shape' => 'token', ], ], ], 'ServiceNowConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'CONTAINS', 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'LESS_THAN_OR_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'EQUAL_TO', 'NOT_EQUAL_TO', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'ServiceNowSourceProperties' => [ 'type' => 'structure', 'required' => [ 'Object', ], 'members' => [ 'Object' => [ 'shape' => 'Object', ], ], ], 'SourceConnectorProperties' => [ 'type' => 'structure', 'members' => [ 'Marketo' => [ 'shape' => 'MarketoSourceProperties', ], 'S3' => [ 'shape' => 'S3SourceProperties', ], 'Salesforce' => [ 'shape' => 'SalesforceSourceProperties', ], 'ServiceNow' => [ 'shape' => 'ServiceNowSourceProperties', ], 'Zendesk' => [ 'shape' => 'ZendeskSourceProperties', ], ], ], 'SourceConnectorType' => [ 'type' => 'string', 'enum' => [ 'Salesforce', 'Marketo', 'Zendesk', 'Servicenow', 'S3', ], ], 'SourceFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'stringTo2048', ], ], 'SourceFlowConfig' => [ 'type' => 'structure', 'required' => [ 'ConnectorType', 'SourceConnectorProperties', ], 'members' => [ 'ConnectorProfileName' => [ 'shape' => 'ConnectorProfileName', ], 'ConnectorType' => [ 'shape' => 'SourceConnectorType', ], 'IncrementalPullConfig' => [ 'shape' => 'IncrementalPullConfig', ], 'SourceConnectorProperties' => [ 'shape' => 'SourceConnectorProperties', ], ], ], 'StandardIdentifier' => [ 'type' => 'string', 'enum' => [ 'PROFILE', 'ASSET', 'CASE', 'UNIQUE', 'SECONDARY', 'LOOKUP_ONLY', 'NEW_ONLY', 'ORDER', ], ], 'StandardIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardIdentifier', ], ], 'Statistic' => [ 'type' => 'string', 'enum' => [ 'FIRST_OCCURRENCE', 'LAST_OCCURRENCE', 'COUNT', 'SUM', 'MINIMUM', 'MAXIMUM', 'AVERAGE', 'MAX_OCCURRENCE', ], 'sensitive' => true, ], 'Status' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'COMPLETE', 'FAILED', 'SPLIT', 'RETRY', 'CANCELLED', ], ], 'TagArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^arn:[a-z0-9]{1,10}:profile', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Task' => [ 'type' => 'structure', 'required' => [ 'SourceFields', 'TaskType', ], 'members' => [ 'ConnectorOperator' => [ 'shape' => 'ConnectorOperator', ], 'DestinationField' => [ 'shape' => 'DestinationField', ], 'SourceFields' => [ 'shape' => 'SourceFields', ], 'TaskProperties' => [ 'shape' => 'TaskPropertiesMap', ], 'TaskType' => [ 'shape' => 'TaskType', ], ], ], 'TaskPropertiesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OperatorPropertiesKeys', ], 'value' => [ 'shape' => 'Property', ], ], 'TaskType' => [ 'type' => 'string', 'enum' => [ 'Arithmetic', 'Filter', 'Map', 'Mask', 'Merge', 'Truncate', 'Validate', ], ], 'Tasks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Task', ], ], 'Threshold' => [ 'type' => 'structure', 'required' => [ 'Value', 'Operator', ], 'members' => [ 'Value' => [ 'shape' => 'string1To255', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timezone' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'TriggerConfig' => [ 'type' => 'structure', 'required' => [ 'TriggerType', ], 'members' => [ 'TriggerType' => [ 'shape' => 'TriggerType', ], 'TriggerProperties' => [ 'shape' => 'TriggerProperties', ], ], ], 'TriggerProperties' => [ 'type' => 'structure', 'members' => [ 'Scheduled' => [ 'shape' => 'ScheduledTriggerProperties', ], ], ], 'TriggerType' => [ 'type' => 'string', 'enum' => [ 'Scheduled', 'Event', 'OnDemand', ], ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'DAYS', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TagArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAddress' => [ 'type' => 'structure', 'members' => [ 'Address1' => [ 'shape' => 'string0To255', ], 'Address2' => [ 'shape' => 'string0To255', ], 'Address3' => [ 'shape' => 'string0To255', ], 'Address4' => [ 'shape' => 'string0To255', ], 'City' => [ 'shape' => 'string0To255', ], 'County' => [ 'shape' => 'string0To255', ], 'State' => [ 'shape' => 'string0To255', ], 'Province' => [ 'shape' => 'string0To255', ], 'Country' => [ 'shape' => 'string0To255', ], 'PostalCode' => [ 'shape' => 'string0To255', ], ], 'sensitive' => true, ], 'UpdateAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'string1To255', ], 'value' => [ 'shape' => 'string0To255', ], 'sensitive' => true, ], 'UpdateCalculatedAttributeDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CalculatedAttributeName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'CalculatedAttributeName' => [ 'shape' => 'typeName', 'location' => 'uri', 'locationName' => 'CalculatedAttributeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'Conditions' => [ 'shape' => 'Conditions', ], ], ], 'UpdateCalculatedAttributeDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CalculatedAttributeName' => [ 'shape' => 'typeName', ], 'DisplayName' => [ 'shape' => 'displayName', ], 'Description' => [ 'shape' => 'sensitiveText', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Conditions' => [ 'shape' => 'Conditions', ], 'AttributeDetails' => [ 'shape' => 'AttributeDetails', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'DefaultExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'DefaultEncryptionKey' => [ 'shape' => 'encryptionKey', ], 'DeadLetterQueueUrl' => [ 'shape' => 'sqsQueueUrl', ], 'Matching' => [ 'shape' => 'MatchingRequest', ], 'RuleBasedMatching' => [ 'shape' => 'RuleBasedMatchingRequest', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateDomainResponse' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'CreatedAt', 'LastUpdatedAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', ], 'DefaultExpirationDays' => [ 'shape' => 'expirationDaysInteger', ], 'DefaultEncryptionKey' => [ 'shape' => 'encryptionKey', ], 'DeadLetterQueueUrl' => [ 'shape' => 'sqsQueueUrl', ], 'Matching' => [ 'shape' => 'MatchingResponse', ], 'RuleBasedMatching' => [ 'shape' => 'RuleBasedMatchingResponse', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ProfileId', ], 'members' => [ 'DomainName' => [ 'shape' => 'name', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ProfileId' => [ 'shape' => 'uuid', ], 'AdditionalInformation' => [ 'shape' => 'sensitiveString0To1000', ], 'AccountNumber' => [ 'shape' => 'sensitiveString0To255', ], 'PartyType' => [ 'shape' => 'PartyType', ], 'BusinessName' => [ 'shape' => 'sensitiveString0To255', ], 'FirstName' => [ 'shape' => 'sensitiveString0To255', ], 'MiddleName' => [ 'shape' => 'sensitiveString0To255', ], 'LastName' => [ 'shape' => 'sensitiveString0To255', ], 'BirthDate' => [ 'shape' => 'sensitiveString0To255', ], 'Gender' => [ 'shape' => 'Gender', ], 'PhoneNumber' => [ 'shape' => 'sensitiveString0To255', ], 'MobilePhoneNumber' => [ 'shape' => 'sensitiveString0To255', ], 'HomePhoneNumber' => [ 'shape' => 'sensitiveString0To255', ], 'BusinessPhoneNumber' => [ 'shape' => 'sensitiveString0To255', ], 'EmailAddress' => [ 'shape' => 'sensitiveString0To255', ], 'PersonalEmailAddress' => [ 'shape' => 'sensitiveString0To255', ], 'BusinessEmailAddress' => [ 'shape' => 'sensitiveString0To255', ], 'Address' => [ 'shape' => 'UpdateAddress', ], 'ShippingAddress' => [ 'shape' => 'UpdateAddress', ], 'MailingAddress' => [ 'shape' => 'UpdateAddress', ], 'BillingAddress' => [ 'shape' => 'UpdateAddress', ], 'Attributes' => [ 'shape' => 'UpdateAttributes', ], 'PartyTypeString' => [ 'shape' => 'sensitiveString0To255', ], 'GenderString' => [ 'shape' => 'sensitiveString0To255', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'uuid', ], ], ], 'Value' => [ 'type' => 'integer', 'max' => 366, 'min' => 1, ], 'WorkflowAttributes' => [ 'type' => 'structure', 'members' => [ 'AppflowIntegration' => [ 'shape' => 'AppflowIntegrationWorkflowAttributes', ], ], ], 'WorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListWorkflowsItem', ], ], 'WorkflowMetrics' => [ 'type' => 'structure', 'members' => [ 'AppflowIntegration' => [ 'shape' => 'AppflowIntegrationWorkflowMetrics', ], ], ], 'WorkflowStepItem' => [ 'type' => 'structure', 'members' => [ 'AppflowIntegration' => [ 'shape' => 'AppflowIntegrationWorkflowStep', ], ], ], 'WorkflowStepsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepItem', ], ], 'WorkflowType' => [ 'type' => 'string', 'enum' => [ 'APPFLOW_INTEGRATION', ], ], 'ZendeskConnectorOperator' => [ 'type' => 'string', 'enum' => [ 'PROJECTION', 'GREATER_THAN', 'ADDITION', 'MULTIPLICATION', 'DIVISION', 'SUBTRACTION', 'MASK_ALL', 'MASK_FIRST_N', 'MASK_LAST_N', 'VALIDATE_NON_NULL', 'VALIDATE_NON_ZERO', 'VALIDATE_NON_NEGATIVE', 'VALIDATE_NUMERIC', 'NO_OP', ], ], 'ZendeskSourceProperties' => [ 'type' => 'structure', 'required' => [ 'Object', ], 'members' => [ 'Object' => [ 'shape' => 'Object', ], ], ], 'additionalSearchKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalSearchKey', ], 'max' => 4, 'min' => 1, ], 'attributeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'boolean' => [ 'type' => 'boolean', ], 'displayName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z_][a-zA-Z_0-9-\\s]*$', ], 'encryptionKey' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'expirationDaysInteger' => [ 'type' => 'integer', 'max' => 1098, 'min' => 1, ], 'foundByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FoundByKeyValue', ], 'max' => 5, 'min' => 1, ], 'logicalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'long' => [ 'type' => 'long', ], 'matchesNumber' => [ 'type' => 'integer', 'min' => 0, ], 'maxSize100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'message' => [ 'type' => 'string', ], 'optionalBoolean' => [ 'type' => 'boolean', ], 'requestValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string1To255', ], ], 's3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9.-]+$', ], 's3KeyName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 's3KeyNameCustomerOutputConfig' => [ 'type' => 'string', 'max' => 800, 'min' => 1, 'pattern' => '.*', ], 'sensitiveString0To1000' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'sensitiveString0To255' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'sensitiveString1To1000' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'sensitiveString1To255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'sensitiveText' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'sqsQueueUrl' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'string0To255' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'string1To1000' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'string1To255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'stringTo2048' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.*', ], 'stringifiedJson' => [ 'type' => 'string', 'max' => 256000, 'min' => 1, 'sensitive' => true, ], 'text' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'timestamp' => [ 'type' => 'timestamp', ], 'token' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'typeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z_][a-zA-Z_0-9-]*$', ], 'uuid' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{32}', ], ],];
