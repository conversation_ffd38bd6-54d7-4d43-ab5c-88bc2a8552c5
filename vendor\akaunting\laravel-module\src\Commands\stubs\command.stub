<?php

namespace $NAMESPACE$;

use Illuminate\Console\Command;

class $CLASS$ extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = '$COMMAND_NAME$';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
    }
}
