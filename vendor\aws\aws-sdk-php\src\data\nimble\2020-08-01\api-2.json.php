<?php
// This file was auto-generated from sdk-root/src/data/nimble/2020-08-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-08-01', 'endpointPrefix' => 'nimble', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AmazonNimbleStudio', 'serviceId' => 'nimble', 'signatureVersion' => 'v4', 'signingName' => 'nimble', 'uid' => 'nimble-2020-08-01', ], 'operations' => [ 'AcceptEulas' => [ 'name' => 'AcceptEulas', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/eula-acceptances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptEulasRequest', ], 'output' => [ 'shape' => 'AcceptEulasResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateLaunchProfile' => [ 'name' => 'CreateLaunchProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLaunchProfileRequest', ], 'output' => [ 'shape' => 'CreateLaunchProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamingImage' => [ 'name' => 'CreateStreamingImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-images', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStreamingImageRequest', ], 'output' => [ 'shape' => 'CreateStreamingImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamingSession' => [ 'name' => 'CreateStreamingSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStreamingSessionRequest', ], 'output' => [ 'shape' => 'CreateStreamingSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamingSessionStream' => [ 'name' => 'CreateStreamingSessionStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/streams', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStreamingSessionStreamRequest', ], 'output' => [ 'shape' => 'CreateStreamingSessionStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateStudio' => [ 'name' => 'CreateStudio', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStudioRequest', ], 'output' => [ 'shape' => 'CreateStudioResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStudioComponent' => [ 'name' => 'CreateStudioComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/studio-components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStudioComponentRequest', ], 'output' => [ 'shape' => 'CreateStudioComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteLaunchProfile' => [ 'name' => 'DeleteLaunchProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLaunchProfileRequest', ], 'output' => [ 'shape' => 'DeleteLaunchProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteLaunchProfileMember' => [ 'name' => 'DeleteLaunchProfileMember', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLaunchProfileMemberRequest', ], 'output' => [ 'shape' => 'DeleteLaunchProfileMemberResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteStreamingImage' => [ 'name' => 'DeleteStreamingImage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStreamingImageRequest', ], 'output' => [ 'shape' => 'DeleteStreamingImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteStreamingSession' => [ 'name' => 'DeleteStreamingSession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStreamingSessionRequest', ], 'output' => [ 'shape' => 'DeleteStreamingSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteStudio' => [ 'name' => 'DeleteStudio', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStudioRequest', ], 'output' => [ 'shape' => 'DeleteStudioResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteStudioComponent' => [ 'name' => 'DeleteStudioComponent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStudioComponentRequest', ], 'output' => [ 'shape' => 'DeleteStudioComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteStudioMember' => [ 'name' => 'DeleteStudioMember', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/studios/{studioId}/membership/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStudioMemberRequest', ], 'output' => [ 'shape' => 'DeleteStudioMemberResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'GetEula' => [ 'name' => 'GetEula', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/eulas/{eulaId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEulaRequest', ], 'output' => [ 'shape' => 'GetEulaResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetLaunchProfile' => [ 'name' => 'GetLaunchProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchProfileRequest', ], 'output' => [ 'shape' => 'GetLaunchProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetLaunchProfileDetails' => [ 'name' => 'GetLaunchProfileDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchProfileDetailsRequest', ], 'output' => [ 'shape' => 'GetLaunchProfileDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetLaunchProfileInitialization' => [ 'name' => 'GetLaunchProfileInitialization', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/init', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchProfileInitializationRequest', ], 'output' => [ 'shape' => 'GetLaunchProfileInitializationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetLaunchProfileMember' => [ 'name' => 'GetLaunchProfileMember', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchProfileMemberRequest', ], 'output' => [ 'shape' => 'GetLaunchProfileMemberResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStreamingImage' => [ 'name' => 'GetStreamingImage', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingImageRequest', ], 'output' => [ 'shape' => 'GetStreamingImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStreamingSession' => [ 'name' => 'GetStreamingSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingSessionRequest', ], 'output' => [ 'shape' => 'GetStreamingSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStreamingSessionBackup' => [ 'name' => 'GetStreamingSessionBackup', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-session-backups/{backupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingSessionBackupRequest', ], 'output' => [ 'shape' => 'GetStreamingSessionBackupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetStreamingSessionStream' => [ 'name' => 'GetStreamingSessionStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/streams/{streamId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingSessionStreamRequest', ], 'output' => [ 'shape' => 'GetStreamingSessionStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStudio' => [ 'name' => 'GetStudio', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStudioRequest', ], 'output' => [ 'shape' => 'GetStudioResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStudioComponent' => [ 'name' => 'GetStudioComponent', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStudioComponentRequest', ], 'output' => [ 'shape' => 'GetStudioComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetStudioMember' => [ 'name' => 'GetStudioMember', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/membership/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStudioMemberRequest', ], 'output' => [ 'shape' => 'GetStudioMemberResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListEulaAcceptances' => [ 'name' => 'ListEulaAcceptances', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/eula-acceptances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEulaAcceptancesRequest', ], 'output' => [ 'shape' => 'ListEulaAcceptancesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListEulas' => [ 'name' => 'ListEulas', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/eulas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEulasRequest', ], 'output' => [ 'shape' => 'ListEulasResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListLaunchProfileMembers' => [ 'name' => 'ListLaunchProfileMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLaunchProfileMembersRequest', ], 'output' => [ 'shape' => 'ListLaunchProfileMembersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListLaunchProfiles' => [ 'name' => 'ListLaunchProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLaunchProfilesRequest', ], 'output' => [ 'shape' => 'ListLaunchProfilesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListStreamingImages' => [ 'name' => 'ListStreamingImages', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-images', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamingImagesRequest', ], 'output' => [ 'shape' => 'ListStreamingImagesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListStreamingSessionBackups' => [ 'name' => 'ListStreamingSessionBackups', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-session-backups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamingSessionBackupsRequest', ], 'output' => [ 'shape' => 'ListStreamingSessionBackupsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListStreamingSessions' => [ 'name' => 'ListStreamingSessions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamingSessionsRequest', ], 'output' => [ 'shape' => 'ListStreamingSessionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListStudioComponents' => [ 'name' => 'ListStudioComponents', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/studio-components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStudioComponentsRequest', ], 'output' => [ 'shape' => 'ListStudioComponentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListStudioMembers' => [ 'name' => 'ListStudioMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios/{studioId}/membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStudioMembersRequest', ], 'output' => [ 'shape' => 'ListStudioMembersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListStudios' => [ 'name' => 'ListStudios', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/studios', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStudiosRequest', ], 'output' => [ 'shape' => 'ListStudiosResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-08-01/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'PutLaunchProfileMembers' => [ 'name' => 'PutLaunchProfileMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutLaunchProfileMembersRequest', ], 'output' => [ 'shape' => 'PutLaunchProfileMembersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'PutStudioMembers' => [ 'name' => 'PutStudioMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutStudioMembersRequest', ], 'output' => [ 'shape' => 'PutStudioMembersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StartStreamingSession' => [ 'name' => 'StartStreamingSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartStreamingSessionRequest', ], 'output' => [ 'shape' => 'StartStreamingSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartStudioSSOConfigurationRepair' => [ 'name' => 'StartStudioSSOConfigurationRepair', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-08-01/studios/{studioId}/sso-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartStudioSSOConfigurationRepairRequest', ], 'output' => [ 'shape' => 'StartStudioSSOConfigurationRepairResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StopStreamingSession' => [ 'name' => 'StopStreamingSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-sessions/{sessionId}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopStreamingSessionRequest', ], 'output' => [ 'shape' => 'StopStreamingSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-08-01/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-08-01/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateLaunchProfile' => [ 'name' => 'UpdateLaunchProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchProfileRequest', ], 'output' => [ 'shape' => 'UpdateLaunchProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateLaunchProfileMember' => [ 'name' => 'UpdateLaunchProfileMember', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2020-08-01/studios/{studioId}/launch-profiles/{launchProfileId}/membership/{principalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchProfileMemberRequest', ], 'output' => [ 'shape' => 'UpdateLaunchProfileMemberResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateStreamingImage' => [ 'name' => 'UpdateStreamingImage', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2020-08-01/studios/{studioId}/streaming-images/{streamingImageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStreamingImageRequest', ], 'output' => [ 'shape' => 'UpdateStreamingImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateStudio' => [ 'name' => 'UpdateStudio', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2020-08-01/studios/{studioId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStudioRequest', ], 'output' => [ 'shape' => 'UpdateStudioResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UpdateStudioComponent' => [ 'name' => 'UpdateStudioComponent', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/2020-08-01/studios/{studioId}/studio-components/{studioComponentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStudioComponentRequest', ], 'output' => [ 'shape' => 'UpdateStudioComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'AcceptEulasRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'eulaIds' => [ 'shape' => 'EulaIdList', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'AcceptEulasResponse' => [ 'type' => 'structure', 'members' => [ 'eulaAcceptances' => [ 'shape' => 'EulaAcceptanceList', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActiveDirectoryComputerAttribute' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ActiveDirectoryComputerAttributeName', ], 'value' => [ 'shape' => 'ActiveDirectoryComputerAttributeValue', ], ], ], 'ActiveDirectoryComputerAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActiveDirectoryComputerAttribute', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'ActiveDirectoryComputerAttributeName' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'ActiveDirectoryComputerAttributeValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ActiveDirectoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'computerAttributes' => [ 'shape' => 'ActiveDirectoryComputerAttributeList', ], 'directoryId' => [ 'shape' => 'DirectoryId', ], 'organizationalUnitDistinguishedName' => [ 'shape' => 'ActiveDirectoryOrganizationalUnitDistinguishedName', ], ], ], 'ActiveDirectoryDnsIpAddress' => [ 'type' => 'string', ], 'ActiveDirectoryDnsIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActiveDirectoryDnsIpAddress', ], 'max' => 10, 'min' => 0, ], 'ActiveDirectoryOrganizationalUnitDistinguishedName' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'AutomaticTerminationMode' => [ 'type' => 'string', 'enum' => [ 'DEACTIVATED', 'ACTIVATED', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ComputeFarmConfiguration' => [ 'type' => 'structure', 'members' => [ 'activeDirectoryUser' => [ 'shape' => 'String', ], 'endpoint' => [ 'shape' => 'SensitiveString', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateLaunchProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ec2SubnetIds', 'launchProfileProtocolVersions', 'name', 'streamConfiguration', 'studioComponentIds', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'LaunchProfileDescription', ], 'ec2SubnetIds' => [ 'shape' => 'EC2SubnetIdList', ], 'launchProfileProtocolVersions' => [ 'shape' => 'LaunchProfileProtocolVersionList', ], 'name' => [ 'shape' => 'LaunchProfileName', ], 'streamConfiguration' => [ 'shape' => 'StreamConfigurationCreate', ], 'studioComponentIds' => [ 'shape' => 'LaunchProfileStudioComponentIdList', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLaunchProfileResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfile' => [ 'shape' => 'LaunchProfile', ], ], ], 'CreateStreamingImageRequest' => [ 'type' => 'structure', 'required' => [ 'ec2ImageId', 'name', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'StreamingImageDescription', ], 'ec2ImageId' => [ 'shape' => 'EC2ImageId', ], 'name' => [ 'shape' => 'StreamingImageName', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStreamingImageResponse' => [ 'type' => 'structure', 'members' => [ 'streamingImage' => [ 'shape' => 'StreamingImage', ], ], ], 'CreateStreamingSessionRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'ec2InstanceType' => [ 'shape' => 'StreamingInstanceType', ], 'launchProfileId' => [ 'shape' => 'String', ], 'ownedBy' => [ 'shape' => 'String', ], 'streamingImageId' => [ 'shape' => 'StreamingImageId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStreamingSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'StreamingSession', ], ], ], 'CreateStreamingSessionStreamRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'expirationInSeconds' => [ 'shape' => 'StreamingSessionStreamExpirationInSeconds', ], 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'CreateStreamingSessionStreamResponse' => [ 'type' => 'structure', 'members' => [ 'stream' => [ 'shape' => 'StreamingSessionStream', ], ], ], 'CreateStudioComponentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'studioId', 'type', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'configuration' => [ 'shape' => 'StudioComponentConfiguration', ], 'description' => [ 'shape' => 'StudioComponentDescription', ], 'ec2SecurityGroupIds' => [ 'shape' => 'StudioComponentSecurityGroupIdList', ], 'initializationScripts' => [ 'shape' => 'StudioComponentInitializationScriptList', ], 'name' => [ 'shape' => 'StudioComponentName', ], 'runtimeRoleArn' => [ 'shape' => 'RoleArn', ], 'scriptParameters' => [ 'shape' => 'StudioComponentScriptParameterKeyValueList', ], 'secureInitializationRoleArn' => [ 'shape' => 'RoleArn', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'subtype' => [ 'shape' => 'StudioComponentSubtype', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'StudioComponentType', ], ], ], 'CreateStudioComponentResponse' => [ 'type' => 'structure', 'members' => [ 'studioComponent' => [ 'shape' => 'StudioComponent', ], ], ], 'CreateStudioRequest' => [ 'type' => 'structure', 'required' => [ 'adminRoleArn', 'displayName', 'studioName', 'userRoleArn', ], 'members' => [ 'adminRoleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'StudioDisplayName', ], 'studioEncryptionConfiguration' => [ 'shape' => 'StudioEncryptionConfiguration', ], 'studioName' => [ 'shape' => 'StudioName', ], 'tags' => [ 'shape' => 'Tags', ], 'userRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CreateStudioResponse' => [ 'type' => 'structure', 'members' => [ 'studio' => [ 'shape' => 'Studio', ], ], ], 'DeleteLaunchProfileMemberRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'principalId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'principalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteLaunchProfileMemberResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLaunchProfileRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteLaunchProfileResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfile' => [ 'shape' => 'LaunchProfile', ], ], ], 'DeleteStreamingImageRequest' => [ 'type' => 'structure', 'required' => [ 'streamingImageId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'streamingImageId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'streamingImageId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteStreamingImageResponse' => [ 'type' => 'structure', 'members' => [ 'streamingImage' => [ 'shape' => 'StreamingImage', ], ], ], 'DeleteStreamingSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteStreamingSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'StreamingSession', ], ], ], 'DeleteStudioComponentRequest' => [ 'type' => 'structure', 'required' => [ 'studioComponentId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'studioComponentId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioComponentId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteStudioComponentResponse' => [ 'type' => 'structure', 'members' => [ 'studioComponent' => [ 'shape' => 'StudioComponent', ], ], ], 'DeleteStudioMemberRequest' => [ 'type' => 'structure', 'required' => [ 'principalId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'principalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteStudioMemberResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStudioRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'DeleteStudioResponse' => [ 'type' => 'structure', 'required' => [ 'studio', ], 'members' => [ 'studio' => [ 'shape' => 'Studio', ], ], ], 'DirectoryId' => [ 'type' => 'string', ], 'EC2ImageId' => [ 'type' => 'string', 'pattern' => '^ami-[0-9A-z]+$', ], 'EC2SubnetId' => [ 'type' => 'string', ], 'EC2SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2SubnetId', ], 'max' => 6, 'min' => 0, ], 'Eula' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'eulaId' => [ 'shape' => 'EulaId', ], 'name' => [ 'shape' => 'EulaName', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'EulaAcceptance' => [ 'type' => 'structure', 'members' => [ 'acceptedAt' => [ 'shape' => 'Timestamp', ], 'acceptedBy' => [ 'shape' => 'String', ], 'accepteeId' => [ 'shape' => 'String', ], 'eulaAcceptanceId' => [ 'shape' => 'EulaAcceptanceId', ], 'eulaId' => [ 'shape' => 'EulaId', ], ], ], 'EulaAcceptanceId' => [ 'type' => 'string', 'max' => 22, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'EulaAcceptanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EulaAcceptance', ], ], 'EulaId' => [ 'type' => 'string', 'max' => 22, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'EulaIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EulaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Eula', ], ], 'EulaName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'ExceptionContext' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'GetEulaRequest' => [ 'type' => 'structure', 'required' => [ 'eulaId', ], 'members' => [ 'eulaId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'eulaId', ], ], ], 'GetEulaResponse' => [ 'type' => 'structure', 'members' => [ 'eula' => [ 'shape' => 'Eula', ], ], ], 'GetLaunchProfileDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetLaunchProfileDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfile' => [ 'shape' => 'LaunchProfile', ], 'streamingImages' => [ 'shape' => 'StreamingImageList', ], 'studioComponentSummaries' => [ 'shape' => 'StudioComponentSummaryList', ], ], ], 'GetLaunchProfileInitializationRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'launchProfileProtocolVersions', 'launchPurpose', 'platform', 'studioId', ], 'members' => [ 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'launchProfileProtocolVersions' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'launchProfileProtocolVersions', ], 'launchPurpose' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'launchPurpose', ], 'platform' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'platform', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetLaunchProfileInitializationResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfileInitialization' => [ 'shape' => 'LaunchProfileInitialization', ], ], ], 'GetLaunchProfileMemberRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'principalId', 'studioId', ], 'members' => [ 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'principalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetLaunchProfileMemberResponse' => [ 'type' => 'structure', 'members' => [ 'member' => [ 'shape' => 'LaunchProfileMembership', ], ], ], 'GetLaunchProfileRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetLaunchProfileResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfile' => [ 'shape' => 'LaunchProfile', ], ], ], 'GetStreamingImageRequest' => [ 'type' => 'structure', 'required' => [ 'streamingImageId', 'studioId', ], 'members' => [ 'streamingImageId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'streamingImageId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStreamingImageResponse' => [ 'type' => 'structure', 'members' => [ 'streamingImage' => [ 'shape' => 'StreamingImage', ], ], ], 'GetStreamingSessionBackupRequest' => [ 'type' => 'structure', 'required' => [ 'backupId', 'studioId', ], 'members' => [ 'backupId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'backupId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStreamingSessionBackupResponse' => [ 'type' => 'structure', 'members' => [ 'streamingSessionBackup' => [ 'shape' => 'StreamingSessionBackup', ], ], ], 'GetStreamingSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'studioId', ], 'members' => [ 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStreamingSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'StreamingSession', ], ], ], 'GetStreamingSessionStreamRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'streamId', 'studioId', ], 'members' => [ 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'streamId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'streamId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStreamingSessionStreamResponse' => [ 'type' => 'structure', 'members' => [ 'stream' => [ 'shape' => 'StreamingSessionStream', ], ], ], 'GetStudioComponentRequest' => [ 'type' => 'structure', 'required' => [ 'studioComponentId', 'studioId', ], 'members' => [ 'studioComponentId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioComponentId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStudioComponentResponse' => [ 'type' => 'structure', 'members' => [ 'studioComponent' => [ 'shape' => 'StudioComponent', ], ], ], 'GetStudioMemberRequest' => [ 'type' => 'structure', 'required' => [ 'principalId', 'studioId', ], 'members' => [ 'principalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStudioMemberResponse' => [ 'type' => 'structure', 'members' => [ 'member' => [ 'shape' => 'StudioMembership', ], ], ], 'GetStudioRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'GetStudioResponse' => [ 'type' => 'structure', 'required' => [ 'studio', ], 'members' => [ 'studio' => [ 'shape' => 'Studio', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'LaunchProfile' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'LaunchProfileDescription', ], 'ec2SubnetIds' => [ 'shape' => 'EC2SubnetIdList', ], 'launchProfileId' => [ 'shape' => 'LaunchProfileId', ], 'launchProfileProtocolVersions' => [ 'shape' => 'LaunchProfileProtocolVersionList', ], 'name' => [ 'shape' => 'LaunchProfileName', ], 'state' => [ 'shape' => 'LaunchProfileState', ], 'statusCode' => [ 'shape' => 'LaunchProfileStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'streamConfiguration' => [ 'shape' => 'StreamConfiguration', ], 'studioComponentIds' => [ 'shape' => 'LaunchProfileStudioComponentIdList', ], 'tags' => [ 'shape' => 'Tags', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'validationResults' => [ 'shape' => 'ValidationResults', ], ], ], 'LaunchProfileDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'LaunchProfileId' => [ 'type' => 'string', 'max' => 22, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'LaunchProfileInitialization' => [ 'type' => 'structure', 'members' => [ 'activeDirectory' => [ 'shape' => 'LaunchProfileInitializationActiveDirectory', ], 'ec2SecurityGroupIds' => [ 'shape' => 'LaunchProfileSecurityGroupIdList', ], 'launchProfileId' => [ 'shape' => 'LaunchProfileId', ], 'launchProfileProtocolVersion' => [ 'shape' => 'LaunchProfileProtocolVersion', ], 'launchPurpose' => [ 'shape' => 'LaunchPurpose', ], 'name' => [ 'shape' => 'LaunchProfileName', ], 'platform' => [ 'shape' => 'LaunchProfilePlatform', ], 'systemInitializationScripts' => [ 'shape' => 'LaunchProfileInitializationScriptList', ], 'userInitializationScripts' => [ 'shape' => 'LaunchProfileInitializationScriptList', ], ], ], 'LaunchProfileInitializationActiveDirectory' => [ 'type' => 'structure', 'members' => [ 'computerAttributes' => [ 'shape' => 'ActiveDirectoryComputerAttributeList', ], 'directoryId' => [ 'shape' => 'DirectoryId', ], 'directoryName' => [ 'shape' => 'String', ], 'dnsIpAddresses' => [ 'shape' => 'ActiveDirectoryDnsIpAddressList', ], 'organizationalUnitDistinguishedName' => [ 'shape' => 'ActiveDirectoryOrganizationalUnitDistinguishedName', ], 'studioComponentId' => [ 'shape' => 'StudioComponentId', ], 'studioComponentName' => [ 'shape' => 'StudioComponentName', ], ], ], 'LaunchProfileInitializationScript' => [ 'type' => 'structure', 'members' => [ 'runtimeRoleArn' => [ 'shape' => 'RoleArn', ], 'script' => [ 'shape' => 'StudioComponentInitializationScriptContent', ], 'secureInitializationRoleArn' => [ 'shape' => 'RoleArn', ], 'studioComponentId' => [ 'shape' => 'StudioComponentId', ], 'studioComponentName' => [ 'shape' => 'StudioComponentName', ], ], ], 'LaunchProfileInitializationScriptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchProfileInitializationScript', ], ], 'LaunchProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchProfile', ], ], 'LaunchProfileMembership' => [ 'type' => 'structure', 'members' => [ 'identityStoreId' => [ 'shape' => 'String', ], 'persona' => [ 'shape' => 'LaunchProfilePersona', ], 'principalId' => [ 'shape' => 'String', ], 'sid' => [ 'shape' => 'String', ], ], ], 'LaunchProfileMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchProfileMembership', ], 'max' => 20, 'min' => 0, ], 'LaunchProfileName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'LaunchProfilePersona' => [ 'type' => 'string', 'enum' => [ 'USER', ], ], 'LaunchProfilePlatform' => [ 'type' => 'string', 'enum' => [ 'LINUX', 'WINDOWS', ], ], 'LaunchProfileProtocolVersion' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^2021\\-03\\-31$', ], 'LaunchProfileProtocolVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchProfileProtocolVersion', ], ], 'LaunchProfileSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'min' => 1, ], 'LaunchProfileState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'READY', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'DELETED', 'DELETE_FAILED', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'LaunchProfileStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchProfileState', ], ], 'LaunchProfileStatusCode' => [ 'type' => 'string', 'enum' => [ 'LAUNCH_PROFILE_CREATED', 'LAUNCH_PROFILE_UPDATED', 'LAUNCH_PROFILE_DELETED', 'LAUNCH_PROFILE_CREATE_IN_PROGRESS', 'LAUNCH_PROFILE_UPDATE_IN_PROGRESS', 'LAUNCH_PROFILE_DELETE_IN_PROGRESS', 'INTERNAL_ERROR', 'STREAMING_IMAGE_NOT_FOUND', 'STREAMING_IMAGE_NOT_READY', 'LAUNCH_PROFILE_WITH_STREAM_SESSIONS_NOT_DELETED', 'ENCRYPTION_KEY_ACCESS_DENIED', 'ENCRYPTION_KEY_NOT_FOUND', 'INVALID_SUBNETS_PROVIDED', 'INVALID_INSTANCE_TYPES_PROVIDED', 'INVALID_SUBNETS_COMBINATION', ], ], 'LaunchProfileStudioComponentIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'LaunchProfileValidationState' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_NOT_STARTED', 'VALIDATION_IN_PROGRESS', 'VALIDATION_SUCCESS', 'VALIDATION_FAILED', 'VALIDATION_FAILED_INTERNAL_SERVER_ERROR', ], ], 'LaunchProfileValidationStatusCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_NOT_STARTED', 'VALIDATION_IN_PROGRESS', 'VALIDATION_SUCCESS', 'VALIDATION_FAILED_INVALID_SUBNET_ROUTE_TABLE_ASSOCIATION', 'VALIDATION_FAILED_SUBNET_NOT_FOUND', 'VALIDATION_FAILED_INVALID_SECURITY_GROUP_ASSOCIATION', 'VALIDATION_FAILED_INVALID_ACTIVE_DIRECTORY', 'VALIDATION_FAILED_UNAUTHORIZED', 'VALIDATION_FAILED_INTERNAL_SERVER_ERROR', ], ], 'LaunchProfileValidationStatusMessage' => [ 'type' => 'string', ], 'LaunchProfileValidationType' => [ 'type' => 'string', 'enum' => [ 'VALIDATE_ACTIVE_DIRECTORY_STUDIO_COMPONENT', 'VALIDATE_SUBNET_ASSOCIATION', 'VALIDATE_NETWORK_ACL_ASSOCIATION', 'VALIDATE_SECURITY_GROUP_ASSOCIATION', ], ], 'LaunchPurpose' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[A-Z0-9_]+$', ], 'LicenseServiceConfiguration' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'SensitiveString', ], ], ], 'LinuxMountPoint' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^(/?|(\\$HOME)?(/[^/\\n\\s\\\\]+)*)$', 'sensitive' => true, ], 'ListEulaAcceptancesRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'eulaIds' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'eulaIds', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListEulaAcceptancesResponse' => [ 'type' => 'structure', 'members' => [ 'eulaAcceptances' => [ 'shape' => 'EulaAcceptanceList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListEulasRequest' => [ 'type' => 'structure', 'members' => [ 'eulaIds' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'eulaIds', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEulasResponse' => [ 'type' => 'structure', 'members' => [ 'eulas' => [ 'shape' => 'EulaList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListLaunchProfileMembersRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListLaunchProfileMembersResponse' => [ 'type' => 'structure', 'members' => [ 'members' => [ 'shape' => 'LaunchProfileMembershipList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListLaunchProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalId' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'principalId', ], 'states' => [ 'shape' => 'LaunchProfileStateList', 'location' => 'querystring', 'locationName' => 'states', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListLaunchProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfiles' => [ 'shape' => 'LaunchProfileList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStreamingImagesRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'owner' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'owner', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListStreamingImagesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'streamingImages' => [ 'shape' => 'StreamingImageList', ], ], ], 'ListStreamingSessionBackupsRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ownedBy' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'ownedBy', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListStreamingSessionBackupsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'streamingSessionBackups' => [ 'shape' => 'StreamingSessionBackupList', ], ], ], 'ListStreamingSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'createdBy' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'createdBy', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ownedBy' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'ownedBy', ], 'sessionIds' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'sessionIds', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListStreamingSessionsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'sessions' => [ 'shape' => 'StreamingSessionList', ], ], ], 'ListStudioComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'states' => [ 'shape' => 'StudioComponentStateList', 'location' => 'querystring', 'locationName' => 'states', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'types' => [ 'shape' => 'StudioComponentTypeList', 'location' => 'querystring', 'locationName' => 'types', ], ], ], 'ListStudioComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'studioComponents' => [ 'shape' => 'StudioComponentList', ], ], ], 'ListStudioMembersRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'ListStudioMembersResponse' => [ 'type' => 'structure', 'members' => [ 'members' => [ 'shape' => 'StudioMembershipList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListStudiosRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListStudiosResponse' => [ 'type' => 'structure', 'required' => [ 'studios', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'studios' => [ 'shape' => 'StudioList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NewLaunchProfileMember' => [ 'type' => 'structure', 'required' => [ 'persona', 'principalId', ], 'members' => [ 'persona' => [ 'shape' => 'LaunchProfilePersona', ], 'principalId' => [ 'shape' => 'String', ], ], ], 'NewLaunchProfileMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NewLaunchProfileMember', ], 'max' => 20, 'min' => 1, ], 'NewStudioMember' => [ 'type' => 'structure', 'required' => [ 'persona', 'principalId', ], 'members' => [ 'persona' => [ 'shape' => 'StudioPersona', ], 'principalId' => [ 'shape' => 'String', ], ], ], 'NewStudioMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NewStudioMember', ], 'max' => 20, 'min' => 1, ], 'PutLaunchProfileMembersRequest' => [ 'type' => 'structure', 'required' => [ 'identityStoreId', 'launchProfileId', 'members', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'identityStoreId' => [ 'shape' => 'String', ], 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'members' => [ 'shape' => 'NewLaunchProfileMemberList', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'PutLaunchProfileMembersResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutStudioMembersRequest' => [ 'type' => 'structure', 'required' => [ 'identityStoreId', 'members', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'identityStoreId' => [ 'shape' => 'String', ], 'members' => [ 'shape' => 'NewStudioMemberList', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'PutStudioMembersResponse' => [ 'type' => 'structure', 'members' => [], ], 'Region' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[a-z]{2}-?(iso|gov)?-{1}[a-z]*-{1}[0-9]', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ScriptParameterKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z_][a-zA-Z0-9_]+$', ], 'ScriptParameterKeyValue' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'ScriptParameterKey', ], 'value' => [ 'shape' => 'ScriptParameterValue', ], ], ], 'ScriptParameterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SecurityGroupId' => [ 'type' => 'string', ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionBackupMode' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'DEACTIVATED', ], ], 'SessionPersistenceMode' => [ 'type' => 'string', 'enum' => [ 'DEACTIVATED', 'ACTIVATED', ], ], 'SharedFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'endpoint' => [ 'shape' => 'SensitiveString', ], 'fileSystemId' => [ 'shape' => 'String', ], 'linuxMountPoint' => [ 'shape' => 'LinuxMountPoint', ], 'shareName' => [ 'shape' => 'SensitiveString', ], 'windowsMountDrive' => [ 'shape' => 'WindowsMountDrive', ], ], ], 'StartStreamingSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'studioId', ], 'members' => [ 'backupId' => [ 'shape' => 'String', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'StartStreamingSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'StreamingSession', ], ], ], 'StartStudioSSOConfigurationRepairRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'StartStudioSSOConfigurationRepairResponse' => [ 'type' => 'structure', 'required' => [ 'studio', ], 'members' => [ 'studio' => [ 'shape' => 'Studio', ], ], ], 'StopStreamingSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'sessionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'sessionId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'volumeRetentionMode' => [ 'shape' => 'VolumeRetentionMode', ], ], ], 'StopStreamingSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'StreamingSession', ], ], ], 'StreamConfiguration' => [ 'type' => 'structure', 'required' => [ 'clipboardMode', 'ec2InstanceTypes', 'streamingImageIds', ], 'members' => [ 'automaticTerminationMode' => [ 'shape' => 'AutomaticTerminationMode', ], 'clipboardMode' => [ 'shape' => 'StreamingClipboardMode', ], 'ec2InstanceTypes' => [ 'shape' => 'StreamingInstanceTypeList', ], 'maxSessionLengthInMinutes' => [ 'shape' => 'StreamConfigurationMaxSessionLengthInMinutes', ], 'maxStoppedSessionLengthInMinutes' => [ 'shape' => 'StreamConfigurationMaxStoppedSessionLengthInMinutes', ], 'sessionBackup' => [ 'shape' => 'StreamConfigurationSessionBackup', ], 'sessionPersistenceMode' => [ 'shape' => 'SessionPersistenceMode', ], 'sessionStorage' => [ 'shape' => 'StreamConfigurationSessionStorage', ], 'streamingImageIds' => [ 'shape' => 'StreamingImageIdList', ], 'volumeConfiguration' => [ 'shape' => 'VolumeConfiguration', ], ], ], 'StreamConfigurationCreate' => [ 'type' => 'structure', 'required' => [ 'clipboardMode', 'ec2InstanceTypes', 'streamingImageIds', ], 'members' => [ 'automaticTerminationMode' => [ 'shape' => 'AutomaticTerminationMode', ], 'clipboardMode' => [ 'shape' => 'StreamingClipboardMode', ], 'ec2InstanceTypes' => [ 'shape' => 'StreamingInstanceTypeList', ], 'maxSessionLengthInMinutes' => [ 'shape' => 'StreamConfigurationMaxSessionLengthInMinutes', ], 'maxStoppedSessionLengthInMinutes' => [ 'shape' => 'StreamConfigurationMaxStoppedSessionLengthInMinutes', ], 'sessionBackup' => [ 'shape' => 'StreamConfigurationSessionBackup', ], 'sessionPersistenceMode' => [ 'shape' => 'SessionPersistenceMode', ], 'sessionStorage' => [ 'shape' => 'StreamConfigurationSessionStorage', ], 'streamingImageIds' => [ 'shape' => 'StreamingImageIdList', ], 'volumeConfiguration' => [ 'shape' => 'VolumeConfiguration', ], ], ], 'StreamConfigurationMaxBackupsToRetain' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'StreamConfigurationMaxSessionLengthInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 43200, 'min' => 1, ], 'StreamConfigurationMaxStoppedSessionLengthInMinutes' => [ 'type' => 'integer', 'max' => 5760, 'min' => 0, ], 'StreamConfigurationSessionBackup' => [ 'type' => 'structure', 'members' => [ 'maxBackupsToRetain' => [ 'shape' => 'StreamConfigurationMaxBackupsToRetain', ], 'mode' => [ 'shape' => 'SessionBackupMode', ], ], ], 'StreamConfigurationSessionStorage' => [ 'type' => 'structure', 'required' => [ 'mode', ], 'members' => [ 'mode' => [ 'shape' => 'StreamingSessionStorageModeList', ], 'root' => [ 'shape' => 'StreamingSessionStorageRoot', ], ], ], 'StreamingClipboardMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'StreamingImage' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'StreamingImageDescription', ], 'ec2ImageId' => [ 'shape' => 'EC2ImageId', ], 'encryptionConfiguration' => [ 'shape' => 'StreamingImageEncryptionConfiguration', ], 'eulaIds' => [ 'shape' => 'EulaIdList', ], 'name' => [ 'shape' => 'StreamingImageName', ], 'owner' => [ 'shape' => 'StreamingImageOwner', ], 'platform' => [ 'shape' => 'StreamingImagePlatform', ], 'state' => [ 'shape' => 'StreamingImageState', ], 'statusCode' => [ 'shape' => 'StreamingImageStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'streamingImageId' => [ 'shape' => 'StreamingImageId', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StreamingImageDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'StreamingImageEncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'keyType', ], 'members' => [ 'keyArn' => [ 'shape' => 'StreamingImageEncryptionConfigurationKeyArn', ], 'keyType' => [ 'shape' => 'StreamingImageEncryptionConfigurationKeyType', ], ], ], 'StreamingImageEncryptionConfigurationKeyArn' => [ 'type' => 'string', 'min' => 4, 'pattern' => '^arn:.*', ], 'StreamingImageEncryptionConfigurationKeyType' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_MANAGED_KEY', ], ], 'StreamingImageId' => [ 'type' => 'string', 'max' => 22, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'StreamingImageIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingImageId', ], 'max' => 20, 'min' => 1, ], 'StreamingImageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingImage', ], ], 'StreamingImageName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'sensitive' => true, ], 'StreamingImageOwner' => [ 'type' => 'string', ], 'StreamingImagePlatform' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]*$', ], 'StreamingImageState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'READY', 'DELETE_IN_PROGRESS', 'DELETED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'StreamingImageStatusCode' => [ 'type' => 'string', 'enum' => [ 'STREAMING_IMAGE_CREATE_IN_PROGRESS', 'STREAMING_IMAGE_READY', 'STREAMING_IMAGE_DELETE_IN_PROGRESS', 'STREAMING_IMAGE_DELETED', 'STREAMING_IMAGE_UPDATE_IN_PROGRESS', 'INTERNAL_ERROR', 'ACCESS_DENIED', ], ], 'StreamingInstanceType' => [ 'type' => 'string', 'enum' => [ 'g4dn.xlarge', 'g4dn.2xlarge', 'g4dn.4xlarge', 'g4dn.8xlarge', 'g4dn.12xlarge', 'g4dn.16xlarge', 'g3.4xlarge', 'g3s.xlarge', 'g5.xlarge', 'g5.2xlarge', 'g5.4xlarge', 'g5.8xlarge', 'g5.16xlarge', ], ], 'StreamingInstanceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingInstanceType', ], 'max' => 30, 'min' => 1, ], 'StreamingSession' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'automaticTerminationMode' => [ 'shape' => 'AutomaticTerminationMode', ], 'backupMode' => [ 'shape' => 'SessionBackupMode', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'ec2InstanceType' => [ 'shape' => 'String', ], 'launchProfileId' => [ 'shape' => 'String', ], 'maxBackupsToRetain' => [ 'shape' => 'StreamConfigurationMaxBackupsToRetain', ], 'ownedBy' => [ 'shape' => 'String', ], 'sessionId' => [ 'shape' => 'StreamingSessionId', ], 'sessionPersistenceMode' => [ 'shape' => 'SessionPersistenceMode', ], 'startedAt' => [ 'shape' => 'Timestamp', ], 'startedBy' => [ 'shape' => 'String', ], 'startedFromBackupId' => [ 'shape' => 'String', ], 'state' => [ 'shape' => 'StreamingSessionState', ], 'statusCode' => [ 'shape' => 'StreamingSessionStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'stopAt' => [ 'shape' => 'Timestamp', ], 'stoppedAt' => [ 'shape' => 'Timestamp', ], 'stoppedBy' => [ 'shape' => 'String', ], 'streamingImageId' => [ 'shape' => 'StreamingImageId', ], 'tags' => [ 'shape' => 'Tags', ], 'terminateAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'volumeConfiguration' => [ 'shape' => 'VolumeConfiguration', ], 'volumeRetentionMode' => [ 'shape' => 'VolumeRetentionMode', ], ], ], 'StreamingSessionBackup' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'backupId' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'launchProfileId' => [ 'shape' => 'String', ], 'ownedBy' => [ 'shape' => 'String', ], 'sessionId' => [ 'shape' => 'StreamingSessionId', ], 'state' => [ 'shape' => 'StreamingSessionState', ], 'statusCode' => [ 'shape' => 'StreamingSessionStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StreamingSessionBackupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingSessionBackup', ], ], 'StreamingSessionId' => [ 'type' => 'string', ], 'StreamingSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingSession', ], ], 'StreamingSessionState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'READY', 'DELETED', 'CREATE_FAILED', 'DELETE_FAILED', 'STOP_IN_PROGRESS', 'START_IN_PROGRESS', 'STOPPED', 'STOP_FAILED', 'START_FAILED', ], ], 'StreamingSessionStatusCode' => [ 'type' => 'string', 'enum' => [ 'STREAMING_SESSION_READY', 'STREAMING_SESSION_DELETED', 'STREAMING_SESSION_CREATE_IN_PROGRESS', 'STREAMING_SESSION_DELETE_IN_PROGRESS', 'INTERNAL_ERROR', 'INSUFFICIENT_CAPACITY', 'ACTIVE_DIRECTORY_DOMAIN_JOIN_ERROR', 'NETWORK_CONNECTION_ERROR', 'INITIALIZATION_SCRIPT_ERROR', 'DECRYPT_STREAMING_IMAGE_ERROR', 'NETWORK_INTERFACE_ERROR', 'STREAMING_SESSION_STOPPED', 'STREAMING_SESSION_STARTED', 'STREAMING_SESSION_STOP_IN_PROGRESS', 'STREAMING_SESSION_START_IN_PROGRESS', 'AMI_VALIDATION_ERROR', ], ], 'StreamingSessionStorageMode' => [ 'type' => 'string', 'enum' => [ 'UPLOAD', ], ], 'StreamingSessionStorageModeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingSessionStorageMode', ], 'min' => 1, ], 'StreamingSessionStorageRoot' => [ 'type' => 'structure', 'members' => [ 'linux' => [ 'shape' => 'StreamingSessionStorageRootPathLinux', ], 'windows' => [ 'shape' => 'StreamingSessionStorageRootPathWindows', ], ], ], 'StreamingSessionStorageRootPathLinux' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(\\$HOME|/)[/]?([A-Za-z0-9-_]+/)*([A-Za-z0-9_-]+)$', 'sensitive' => true, ], 'StreamingSessionStorageRootPathWindows' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^((\\%HOMEPATH\\%)|[a-zA-Z]:)[\\\\/](?:[a-zA-Z0-9_-]+[\\\\/])*[a-zA-Z0-9_-]+$', 'sensitive' => true, ], 'StreamingSessionStream' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'ownedBy' => [ 'shape' => 'String', ], 'state' => [ 'shape' => 'StreamingSessionStreamState', ], 'statusCode' => [ 'shape' => 'StreamingSessionStreamStatusCode', ], 'streamId' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'SensitiveString', ], ], ], 'StreamingSessionStreamExpirationInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 3600, 'min' => 60, ], 'StreamingSessionStreamState' => [ 'type' => 'string', 'enum' => [ 'READY', 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'DELETED', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'StreamingSessionStreamStatusCode' => [ 'type' => 'string', 'enum' => [ 'STREAM_CREATE_IN_PROGRESS', 'STREAM_READY', 'STREAM_DELETE_IN_PROGRESS', 'STREAM_DELETED', 'INTERNAL_ERROR', 'NETWORK_CONNECTION_ERROR', ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Studio' => [ 'type' => 'structure', 'members' => [ 'adminRoleArn' => [ 'shape' => 'RoleArn', ], 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'StudioDisplayName', ], 'homeRegion' => [ 'shape' => 'Region', ], 'ssoClientId' => [ 'shape' => 'String', ], 'state' => [ 'shape' => 'StudioState', ], 'statusCode' => [ 'shape' => 'StudioStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'studioEncryptionConfiguration' => [ 'shape' => 'StudioEncryptionConfiguration', ], 'studioId' => [ 'shape' => 'String', ], 'studioName' => [ 'shape' => 'StudioName', ], 'studioUrl' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'Tags', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'userRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'StudioComponent' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'configuration' => [ 'shape' => 'StudioComponentConfiguration', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'StudioComponentDescription', ], 'ec2SecurityGroupIds' => [ 'shape' => 'StudioComponentSecurityGroupIdList', ], 'initializationScripts' => [ 'shape' => 'StudioComponentInitializationScriptList', ], 'name' => [ 'shape' => 'StudioComponentName', ], 'runtimeRoleArn' => [ 'shape' => 'RoleArn', ], 'scriptParameters' => [ 'shape' => 'StudioComponentScriptParameterKeyValueList', ], 'secureInitializationRoleArn' => [ 'shape' => 'RoleArn', ], 'state' => [ 'shape' => 'StudioComponentState', ], 'statusCode' => [ 'shape' => 'StudioComponentStatusCode', ], 'statusMessage' => [ 'shape' => 'String', ], 'studioComponentId' => [ 'shape' => 'StudioComponentId', ], 'subtype' => [ 'shape' => 'StudioComponentSubtype', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'StudioComponentType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'updatedBy' => [ 'shape' => 'String', ], ], ], 'StudioComponentConfiguration' => [ 'type' => 'structure', 'members' => [ 'activeDirectoryConfiguration' => [ 'shape' => 'ActiveDirectoryConfiguration', ], 'computeFarmConfiguration' => [ 'shape' => 'ComputeFarmConfiguration', ], 'licenseServiceConfiguration' => [ 'shape' => 'LicenseServiceConfiguration', ], 'sharedFileSystemConfiguration' => [ 'shape' => 'SharedFileSystemConfiguration', ], ], 'union' => true, ], 'StudioComponentDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'StudioComponentId' => [ 'type' => 'string', 'max' => 22, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'StudioComponentInitializationScript' => [ 'type' => 'structure', 'members' => [ 'launchProfileProtocolVersion' => [ 'shape' => 'LaunchProfileProtocolVersion', ], 'platform' => [ 'shape' => 'LaunchProfilePlatform', ], 'runContext' => [ 'shape' => 'StudioComponentInitializationScriptRunContext', ], 'script' => [ 'shape' => 'StudioComponentInitializationScriptContent', ], ], ], 'StudioComponentInitializationScriptContent' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, 'sensitive' => true, ], 'StudioComponentInitializationScriptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioComponentInitializationScript', ], ], 'StudioComponentInitializationScriptRunContext' => [ 'type' => 'string', 'enum' => [ 'SYSTEM_INITIALIZATION', 'USER_INITIALIZATION', ], ], 'StudioComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioComponent', ], 'max' => 50, 'min' => 0, ], 'StudioComponentName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'sensitive' => true, ], 'StudioComponentScriptParameterKeyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScriptParameterKeyValue', ], 'max' => 30, 'min' => 0, 'sensitive' => true, ], 'StudioComponentSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 30, 'min' => 0, ], 'StudioComponentState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'READY', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'DELETED', 'DELETE_FAILED', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'StudioComponentStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioComponentState', ], ], 'StudioComponentStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE_DIRECTORY_ALREADY_EXISTS', 'STUDIO_COMPONENT_CREATED', 'STUDIO_COMPONENT_UPDATED', 'STUDIO_COMPONENT_DELETED', 'ENCRYPTION_KEY_ACCESS_DENIED', 'ENCRYPTION_KEY_NOT_FOUND', 'STUDIO_COMPONENT_CREATE_IN_PROGRESS', 'STUDIO_COMPONENT_UPDATE_IN_PROGRESS', 'STUDIO_COMPONENT_DELETE_IN_PROGRESS', 'INTERNAL_ERROR', ], ], 'StudioComponentSubtype' => [ 'type' => 'string', 'enum' => [ 'AWS_MANAGED_MICROSOFT_AD', 'AMAZON_FSX_FOR_WINDOWS', 'AMAZON_FSX_FOR_LUSTRE', 'CUSTOM', ], ], 'StudioComponentSummary' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'StudioComponentDescription', ], 'name' => [ 'shape' => 'StudioComponentName', ], 'studioComponentId' => [ 'shape' => 'StudioComponentId', ], 'subtype' => [ 'shape' => 'StudioComponentSubtype', ], 'type' => [ 'shape' => 'StudioComponentType', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'updatedBy' => [ 'shape' => 'String', ], ], ], 'StudioComponentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioComponentSummary', ], ], 'StudioComponentType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE_DIRECTORY', 'SHARED_FILE_SYSTEM', 'COMPUTE_FARM', 'LICENSE_SERVICE', 'CUSTOM', ], ], 'StudioComponentTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioComponentType', ], ], 'StudioDisplayName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'sensitive' => true, ], 'StudioEncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'keyType', ], 'members' => [ 'keyArn' => [ 'shape' => 'StudioEncryptionConfigurationKeyArn', ], 'keyType' => [ 'shape' => 'StudioEncryptionConfigurationKeyType', ], ], ], 'StudioEncryptionConfigurationKeyArn' => [ 'type' => 'string', 'min' => 4, 'pattern' => '^arn:.*', ], 'StudioEncryptionConfigurationKeyType' => [ 'type' => 'string', 'enum' => [ 'AWS_OWNED_KEY', 'CUSTOMER_MANAGED_KEY', ], ], 'StudioList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Studio', ], ], 'StudioMembership' => [ 'type' => 'structure', 'members' => [ 'identityStoreId' => [ 'shape' => 'String', ], 'persona' => [ 'shape' => 'StudioPersona', ], 'principalId' => [ 'shape' => 'String', ], 'sid' => [ 'shape' => 'String', ], ], ], 'StudioMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StudioMembership', ], 'max' => 20, 'min' => 0, ], 'StudioName' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '^[a-z0-9]*$', ], 'StudioPersona' => [ 'type' => 'string', 'enum' => [ 'ADMINISTRATOR', ], ], 'StudioState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'READY', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'DELETED', 'DELETE_FAILED', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'StudioStatusCode' => [ 'type' => 'string', 'enum' => [ 'STUDIO_CREATED', 'STUDIO_DELETED', 'STUDIO_UPDATED', 'STUDIO_CREATE_IN_PROGRESS', 'STUDIO_UPDATE_IN_PROGRESS', 'STUDIO_DELETE_IN_PROGRESS', 'STUDIO_WITH_LAUNCH_PROFILES_NOT_DELETED', 'STUDIO_WITH_STUDIO_COMPONENTS_NOT_DELETED', 'STUDIO_WITH_STREAMING_IMAGES_NOT_DELETED', 'AWS_SSO_NOT_ENABLED', 'AWS_SSO_ACCESS_DENIED', 'ROLE_NOT_OWNED_BY_STUDIO_OWNER', 'ROLE_COULD_NOT_BE_ASSUMED', 'INTERNAL_ERROR', 'ENCRYPTION_KEY_NOT_FOUND', 'ENCRYPTION_KEY_ACCESS_DENIED', 'AWS_SSO_CONFIGURATION_REPAIRED', 'AWS_SSO_CONFIGURATION_REPAIR_IN_PROGRESS', 'AWS_STS_REGION_DISABLED', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLaunchProfileMemberRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'persona', 'principalId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'persona' => [ 'shape' => 'LaunchProfilePersona', ], 'principalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'UpdateLaunchProfileMemberResponse' => [ 'type' => 'structure', 'members' => [ 'member' => [ 'shape' => 'LaunchProfileMembership', ], ], ], 'UpdateLaunchProfileRequest' => [ 'type' => 'structure', 'required' => [ 'launchProfileId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'LaunchProfileDescription', ], 'launchProfileId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'launchProfileId', ], 'launchProfileProtocolVersions' => [ 'shape' => 'LaunchProfileProtocolVersionList', ], 'name' => [ 'shape' => 'LaunchProfileName', ], 'streamConfiguration' => [ 'shape' => 'StreamConfigurationCreate', ], 'studioComponentIds' => [ 'shape' => 'LaunchProfileStudioComponentIdList', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'UpdateLaunchProfileResponse' => [ 'type' => 'structure', 'members' => [ 'launchProfile' => [ 'shape' => 'LaunchProfile', ], ], ], 'UpdateStreamingImageRequest' => [ 'type' => 'structure', 'required' => [ 'streamingImageId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'description' => [ 'shape' => 'StreamingImageDescription', ], 'name' => [ 'shape' => 'StreamingImageName', ], 'streamingImageId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'streamingImageId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], ], ], 'UpdateStreamingImageResponse' => [ 'type' => 'structure', 'members' => [ 'streamingImage' => [ 'shape' => 'StreamingImage', ], ], ], 'UpdateStudioComponentRequest' => [ 'type' => 'structure', 'required' => [ 'studioComponentId', 'studioId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'configuration' => [ 'shape' => 'StudioComponentConfiguration', ], 'description' => [ 'shape' => 'StudioComponentDescription', ], 'ec2SecurityGroupIds' => [ 'shape' => 'StudioComponentSecurityGroupIdList', ], 'initializationScripts' => [ 'shape' => 'StudioComponentInitializationScriptList', ], 'name' => [ 'shape' => 'StudioComponentName', ], 'runtimeRoleArn' => [ 'shape' => 'RoleArn', ], 'scriptParameters' => [ 'shape' => 'StudioComponentScriptParameterKeyValueList', ], 'secureInitializationRoleArn' => [ 'shape' => 'RoleArn', ], 'studioComponentId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioComponentId', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'subtype' => [ 'shape' => 'StudioComponentSubtype', ], 'type' => [ 'shape' => 'StudioComponentType', ], ], ], 'UpdateStudioComponentResponse' => [ 'type' => 'structure', 'members' => [ 'studioComponent' => [ 'shape' => 'StudioComponent', ], ], ], 'UpdateStudioRequest' => [ 'type' => 'structure', 'required' => [ 'studioId', ], 'members' => [ 'adminRoleArn' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'header', 'locationName' => 'X-Amz-Client-Token', ], 'displayName' => [ 'shape' => 'StudioDisplayName', ], 'studioId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'studioId', ], 'userRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateStudioResponse' => [ 'type' => 'structure', 'required' => [ 'studio', ], 'members' => [ 'studio' => [ 'shape' => 'Studio', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'context' => [ 'shape' => 'ExceptionContext', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationResult' => [ 'type' => 'structure', 'required' => [ 'state', 'statusCode', 'statusMessage', 'type', ], 'members' => [ 'state' => [ 'shape' => 'LaunchProfileValidationState', ], 'statusCode' => [ 'shape' => 'LaunchProfileValidationStatusCode', ], 'statusMessage' => [ 'shape' => 'LaunchProfileValidationStatusMessage', ], 'type' => [ 'shape' => 'LaunchProfileValidationType', ], ], ], 'ValidationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationResult', ], ], 'VolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'iops' => [ 'shape' => 'VolumeIops', ], 'size' => [ 'shape' => 'VolumeSizeInGiB', ], 'throughput' => [ 'shape' => 'VolumeThroughputInMiBs', ], ], ], 'VolumeIops' => [ 'type' => 'integer', 'box' => true, 'max' => 16000, 'min' => 3000, ], 'VolumeRetentionMode' => [ 'type' => 'string', 'enum' => [ 'RETAIN', 'DELETE', ], ], 'VolumeSizeInGiB' => [ 'type' => 'integer', 'box' => true, 'max' => 16000, 'min' => 100, ], 'VolumeThroughputInMiBs' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 125, ], 'WindowsMountDrive' => [ 'type' => 'string', 'pattern' => '^[A-Z]$', ], ],];
