{"name": "aws/aws-sdk-php", "homepage": "http://aws.amazon.com/sdkforphp", "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "keywords": ["aws", "amazon", "sdk", "s3", "ec2", "dynamodb", "cloud", "glacier"], "type": "library", "license": "Apache-2.0", "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues"}, "require": {"php": ">=7.2.5", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "mtdowling/jmespath.php": "^2.6", "ext-pcre": "*", "ext-json": "*", "ext-simplexml": "*", "aws/aws-crt-php": "^1.2.3", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"composer/composer": "^1.10.22", "ext-openssl": "*", "ext-dom": "*", "ext-pcntl": "*", "ext-sockets": "*", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "aws/aws-php-sns-message-validator": "~1.0", "nette/neon": "^2.3", "andrewsville/php-token-reflection": "^1.4", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "paragonie/random_compat": ">= 2", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0", "dms/phpunit-arraysubset-asserts": "^0.4.0"}, "suggest": {"ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-curl": "To send requests using cURL", "ext-sockets": "To use client-side monitoring", "doctrine/cache": "To use the DoctrineCacheAdapter", "aws/aws-php-sns-message-validator": "To validate incoming SNS notifications"}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Aws\\Test\\": "tests/"}, "classmap": ["build/"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}