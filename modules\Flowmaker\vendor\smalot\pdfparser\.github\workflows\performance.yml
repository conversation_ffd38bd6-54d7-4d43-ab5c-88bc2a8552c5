name: "Performance Tests"

on:
  pull_request:
  push:
    branches:
      - "master"

env:
  fail-fast: true

jobs:
  performance-tests:
    name: "Tests for the performance testing the PDF parsing"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Run composer for further autoloading"
        run: "composer update"

      - name: "Run performance tests"
        run: "php tests/Performance/runPerformanceTests.php"
