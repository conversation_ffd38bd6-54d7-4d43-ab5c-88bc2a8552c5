<?php
// This file was auto-generated from sdk-root/src/data/amplify/2017-07-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-25', 'endpointPrefix' => 'amplify', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Amplify', 'serviceFullName' => 'AWS Amplify', 'serviceId' => 'Amplify', 'signatureVersion' => 'v4', 'signingName' => 'amplify', 'uid' => 'amplify-2017-07-25', ], 'operations' => [ 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps', ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'CreateBackendEnvironment' => [ 'name' => 'CreateBackendEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/backendenvironments', ], 'input' => [ 'shape' => 'CreateBackendEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateBackendEnvironmentResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateBranch' => [ 'name' => 'CreateBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/branches', ], 'input' => [ 'shape' => 'CreateBranchRequest', ], 'output' => [ 'shape' => 'CreateBranchResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'CreateDeployment' => [ 'name' => 'CreateDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/branches/{branchName}/deployments', ], 'input' => [ 'shape' => 'CreateDeploymentRequest', ], 'output' => [ 'shape' => 'CreateDeploymentResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDomainAssociation' => [ 'name' => 'CreateDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/domains', ], 'input' => [ 'shape' => 'CreateDomainAssociationRequest', ], 'output' => [ 'shape' => 'CreateDomainAssociationResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'CreateWebhook' => [ 'name' => 'CreateWebhook', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/webhooks', ], 'input' => [ 'shape' => 'CreateWebhookRequest', ], 'output' => [ 'shape' => 'CreateWebhookResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}', ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'output' => [ 'shape' => 'DeleteAppResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'DeleteBackendEnvironment' => [ 'name' => 'DeleteBackendEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}/backendenvironments/{environmentName}', ], 'input' => [ 'shape' => 'DeleteBackendEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteBackendEnvironmentResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'DeleteBranch' => [ 'name' => 'DeleteBranch', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}/branches/{branchName}', ], 'input' => [ 'shape' => 'DeleteBranchRequest', ], 'output' => [ 'shape' => 'DeleteBranchResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'DeleteDomainAssociation' => [ 'name' => 'DeleteDomainAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}/domains/{domainName}', ], 'input' => [ 'shape' => 'DeleteDomainAssociationRequest', ], 'output' => [ 'shape' => 'DeleteDomainAssociationResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs/{jobId}', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteWebhook' => [ 'name' => 'DeleteWebhook', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/webhooks/{webhookId}', ], 'input' => [ 'shape' => 'DeleteWebhookRequest', ], 'output' => [ 'shape' => 'DeleteWebhookResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GenerateAccessLogs' => [ 'name' => 'GenerateAccessLogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/accesslogs', ], 'input' => [ 'shape' => 'GenerateAccessLogsRequest', ], 'output' => [ 'shape' => 'GenerateAccessLogsResult', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetApp' => [ 'name' => 'GetApp', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}', ], 'input' => [ 'shape' => 'GetAppRequest', ], 'output' => [ 'shape' => 'GetAppResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetArtifactUrl' => [ 'name' => 'GetArtifactUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/artifacts/{artifactId}', ], 'input' => [ 'shape' => 'GetArtifactUrlRequest', ], 'output' => [ 'shape' => 'GetArtifactUrlResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetBackendEnvironment' => [ 'name' => 'GetBackendEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/backendenvironments/{environmentName}', ], 'input' => [ 'shape' => 'GetBackendEnvironmentRequest', ], 'output' => [ 'shape' => 'GetBackendEnvironmentResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetBranch' => [ 'name' => 'GetBranch', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/branches/{branchName}', ], 'input' => [ 'shape' => 'GetBranchRequest', ], 'output' => [ 'shape' => 'GetBranchResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetDomainAssociation' => [ 'name' => 'GetDomainAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/domains/{domainName}', ], 'input' => [ 'shape' => 'GetDomainAssociationRequest', ], 'output' => [ 'shape' => 'GetDomainAssociationResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs/{jobId}', ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetWebhook' => [ 'name' => 'GetWebhook', 'http' => [ 'method' => 'GET', 'requestUri' => '/webhooks/{webhookId}', ], 'input' => [ 'shape' => 'GetWebhookRequest', ], 'output' => [ 'shape' => 'GetWebhookResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListApps' => [ 'name' => 'ListApps', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps', ], 'input' => [ 'shape' => 'ListAppsRequest', ], 'output' => [ 'shape' => 'ListAppsResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListArtifacts' => [ 'name' => 'ListArtifacts', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs/{jobId}/artifacts', ], 'input' => [ 'shape' => 'ListArtifactsRequest', ], 'output' => [ 'shape' => 'ListArtifactsResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListBackendEnvironments' => [ 'name' => 'ListBackendEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/backendenvironments', ], 'input' => [ 'shape' => 'ListBackendEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListBackendEnvironmentsResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListBranches' => [ 'name' => 'ListBranches', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/branches', ], 'input' => [ 'shape' => 'ListBranchesRequest', ], 'output' => [ 'shape' => 'ListBranchesResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDomainAssociations' => [ 'name' => 'ListDomainAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/domains', ], 'input' => [ 'shape' => 'ListDomainAssociationsRequest', ], 'output' => [ 'shape' => 'ListDomainAssociationsResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListWebhooks' => [ 'name' => 'ListWebhooks', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps/{appId}/webhooks', ], 'input' => [ 'shape' => 'ListWebhooksRequest', ], 'output' => [ 'shape' => 'ListWebhooksResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartDeployment' => [ 'name' => 'StartDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/branches/{branchName}/deployments/start', ], 'input' => [ 'shape' => 'StartDeploymentRequest', ], 'output' => [ 'shape' => 'StartDeploymentResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartJob' => [ 'name' => 'StartJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs', ], 'input' => [ 'shape' => 'StartJobRequest', ], 'output' => [ 'shape' => 'StartJobResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StopJob' => [ 'name' => 'StopJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/apps/{appId}/branches/{branchName}/jobs/{jobId}/stop', ], 'input' => [ 'shape' => 'StopJobRequest', ], 'output' => [ 'shape' => 'StopJobResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateApp' => [ 'name' => 'UpdateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}', ], 'input' => [ 'shape' => 'UpdateAppRequest', ], 'output' => [ 'shape' => 'UpdateAppResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateBranch' => [ 'name' => 'UpdateBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/branches/{branchName}', ], 'input' => [ 'shape' => 'UpdateBranchRequest', ], 'output' => [ 'shape' => 'UpdateBranchResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'UpdateDomainAssociation' => [ 'name' => 'UpdateDomainAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps/{appId}/domains/{domainName}', ], 'input' => [ 'shape' => 'UpdateDomainAssociationRequest', ], 'output' => [ 'shape' => 'UpdateDomainAssociationResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], 'UpdateWebhook' => [ 'name' => 'UpdateWebhook', 'http' => [ 'method' => 'POST', 'requestUri' => '/webhooks/{webhookId}', ], 'input' => [ 'shape' => 'UpdateWebhookRequest', ], 'output' => [ 'shape' => 'UpdateWebhookResult', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'DependentServiceFailureException', ], ], ], ], 'shapes' => [ 'AccessToken' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?s).+', 'sensitive' => true, ], 'ActiveJobId' => [ 'type' => 'string', 'max' => 1000, ], 'App' => [ 'type' => 'structure', 'required' => [ 'appId', 'appArn', 'name', 'description', 'repository', 'platform', 'createTime', 'updateTime', 'environmentVariables', 'defaultDomain', 'enableBranchAutoBuild', 'enableBasicAuth', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'appArn' => [ 'shape' => 'AppArn', ], 'name' => [ 'shape' => 'Name', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'Description', ], 'repository' => [ 'shape' => 'Repository', ], 'platform' => [ 'shape' => 'Platform', ], 'createTime' => [ 'shape' => 'CreateTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'iamServiceRoleArn' => [ 'shape' => 'ServiceRoleArn', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'defaultDomain' => [ 'shape' => 'DefaultDomain', ], 'enableBranchAutoBuild' => [ 'shape' => 'EnableBranchAutoBuild', ], 'enableBranchAutoDeletion' => [ 'shape' => 'EnableBranchAutoDeletion', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'customRules' => [ 'shape' => 'CustomRules', ], 'productionBranch' => [ 'shape' => 'ProductionBranch', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'customHeaders' => [ 'shape' => 'CustomHeaders', ], 'enableAutoBranchCreation' => [ 'shape' => 'EnableAutoBranchCreation', ], 'autoBranchCreationPatterns' => [ 'shape' => 'AutoBranchCreationPatterns', ], 'autoBranchCreationConfig' => [ 'shape' => 'AutoBranchCreationConfig', ], 'repositoryCloneMethod' => [ 'shape' => 'RepositoryCloneMethod', ], ], ], 'AppArn' => [ 'type' => 'string', 'max' => 1000, ], 'AppId' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => 'd[a-z0-9]+', ], 'Apps' => [ 'type' => 'list', 'member' => [ 'shape' => 'App', ], ], 'Artifact' => [ 'type' => 'structure', 'required' => [ 'artifactFileName', 'artifactId', ], 'members' => [ 'artifactFileName' => [ 'shape' => 'ArtifactFileName', ], 'artifactId' => [ 'shape' => 'ArtifactId', ], ], ], 'ArtifactFileName' => [ 'type' => 'string', 'max' => 1000, ], 'ArtifactId' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'ArtifactUrl' => [ 'type' => 'string', 'max' => 1000, ], 'Artifacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Artifact', ], ], 'ArtifactsUrl' => [ 'type' => 'string', 'max' => 1000, ], 'AssociatedResource' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AssociatedResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedResource', ], ], 'AutoBranchCreationConfig' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], 'framework' => [ 'shape' => 'Framework', ], 'enableAutoBuild' => [ 'shape' => 'EnableAutoBuild', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'enablePerformanceMode' => [ 'shape' => 'EnablePerformanceMode', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'enablePullRequestPreview' => [ 'shape' => 'EnablePullRequestPreview', ], 'pullRequestEnvironmentName' => [ 'shape' => 'PullRequestEnvironmentName', ], ], ], 'AutoBranchCreationPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(?s).+', ], 'AutoBranchCreationPatterns' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoBranchCreationPattern', ], ], 'AutoSubDomainCreationPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(?s).+', ], 'AutoSubDomainCreationPatterns' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoSubDomainCreationPattern', ], ], 'AutoSubDomainIAMRole' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^$|^arn:aws:iam::\\d{12}:role.+', ], 'Backend' => [ 'type' => 'structure', 'members' => [ 'stackArn' => [ 'shape' => 'StackArn', ], ], ], 'BackendEnvironment' => [ 'type' => 'structure', 'required' => [ 'backendEnvironmentArn', 'environmentName', 'createTime', 'updateTime', ], 'members' => [ 'backendEnvironmentArn' => [ 'shape' => 'BackendEnvironmentArn', ], 'environmentName' => [ 'shape' => 'EnvironmentName', ], 'stackName' => [ 'shape' => 'StackName', ], 'deploymentArtifacts' => [ 'shape' => 'DeploymentArtifacts', ], 'createTime' => [ 'shape' => 'CreateTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'BackendEnvironmentArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '(?s).*', ], 'BackendEnvironments' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackendEnvironment', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BasicAuthCredentials' => [ 'type' => 'string', 'max' => 2000, 'pattern' => '(?s).*', 'sensitive' => true, ], 'Branch' => [ 'type' => 'structure', 'required' => [ 'branchArn', 'branchName', 'description', 'stage', 'displayName', 'enableNotification', 'createTime', 'updateTime', 'environmentVariables', 'enableAutoBuild', 'customDomains', 'framework', 'activeJobId', 'totalNumberOfJobs', 'enableBasicAuth', 'ttl', 'enablePullRequestPreview', ], 'members' => [ 'branchArn' => [ 'shape' => 'BranchArn', ], 'branchName' => [ 'shape' => 'BranchName', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'stage' => [ 'shape' => 'Stage', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'enableNotification' => [ 'shape' => 'EnableNotification', ], 'createTime' => [ 'shape' => 'CreateTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'enableAutoBuild' => [ 'shape' => 'EnableAutoBuild', ], 'customDomains' => [ 'shape' => 'CustomDomains', ], 'framework' => [ 'shape' => 'Framework', ], 'activeJobId' => [ 'shape' => 'ActiveJobId', ], 'totalNumberOfJobs' => [ 'shape' => 'TotalNumberOfJobs', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'enablePerformanceMode' => [ 'shape' => 'EnablePerformanceMode', ], 'thumbnailUrl' => [ 'shape' => 'ThumbnailUrl', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'ttl' => [ 'shape' => 'TTL', ], 'associatedResources' => [ 'shape' => 'AssociatedResources', ], 'enablePullRequestPreview' => [ 'shape' => 'EnablePullRequestPreview', ], 'pullRequestEnvironmentName' => [ 'shape' => 'PullRequestEnvironmentName', ], 'destinationBranch' => [ 'shape' => 'BranchName', ], 'sourceBranch' => [ 'shape' => 'BranchName', ], 'backendEnvironmentArn' => [ 'shape' => 'BackendEnvironmentArn', ], 'backend' => [ 'shape' => 'Backend', ], ], ], 'BranchArn' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '(?s).*', ], 'BranchName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?s).+', ], 'Branches' => [ 'type' => 'list', 'member' => [ 'shape' => 'Branch', ], 'max' => 255, ], 'BuildSpec' => [ 'type' => 'string', 'max' => 25000, 'min' => 1, 'pattern' => '(?s).+', 'sensitive' => true, ], 'Certificate' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'CertificateType', ], 'customCertificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateVerificationDNSRecord' => [ 'shape' => 'CertificateVerificationDNSRecord', ], ], ], 'CertificateArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^arn:aws:acm:[a-z0-9-]+:\\d{12}:certificate\\/.+$', ], 'CertificateSettings' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'CertificateType', ], 'customCertificateArn' => [ 'shape' => 'CertificateArn', ], ], ], 'CertificateType' => [ 'type' => 'string', 'enum' => [ 'AMPLIFY_MANAGED', 'CUSTOM', ], ], 'CertificateVerificationDNSRecord' => [ 'type' => 'string', 'max' => 1000, ], 'Code' => [ 'type' => 'string', ], 'CommitId' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'CommitMessage' => [ 'type' => 'string', 'max' => 10000, 'pattern' => '(?s).*', ], 'CommitTime' => [ 'type' => 'timestamp', ], 'Condition' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(?s).*', ], 'Context' => [ 'type' => 'string', ], 'CreateAppRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'repository' => [ 'shape' => 'Repository', ], 'platform' => [ 'shape' => 'Platform', ], 'iamServiceRoleArn' => [ 'shape' => 'ServiceRoleArn', ], 'oauthToken' => [ 'shape' => 'OauthToken', ], 'accessToken' => [ 'shape' => 'AccessToken', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'enableBranchAutoBuild' => [ 'shape' => 'EnableBranchAutoBuild', ], 'enableBranchAutoDeletion' => [ 'shape' => 'EnableBranchAutoDeletion', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'customRules' => [ 'shape' => 'CustomRules', ], 'tags' => [ 'shape' => 'TagMap', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'customHeaders' => [ 'shape' => 'CustomHeaders', ], 'enableAutoBranchCreation' => [ 'shape' => 'EnableAutoBranchCreation', ], 'autoBranchCreationPatterns' => [ 'shape' => 'AutoBranchCreationPatterns', ], 'autoBranchCreationConfig' => [ 'shape' => 'AutoBranchCreationConfig', ], ], ], 'CreateAppResult' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'CreateBackendEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'EnvironmentName', ], 'stackName' => [ 'shape' => 'StackName', ], 'deploymentArtifacts' => [ 'shape' => 'DeploymentArtifacts', ], ], ], 'CreateBackendEnvironmentResult' => [ 'type' => 'structure', 'required' => [ 'backendEnvironment', ], 'members' => [ 'backendEnvironment' => [ 'shape' => 'BackendEnvironment', ], ], ], 'CreateBranchRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', ], 'description' => [ 'shape' => 'Description', ], 'stage' => [ 'shape' => 'Stage', ], 'framework' => [ 'shape' => 'Framework', ], 'enableNotification' => [ 'shape' => 'EnableNotification', ], 'enableAutoBuild' => [ 'shape' => 'EnableAutoBuild', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'enablePerformanceMode' => [ 'shape' => 'EnablePerformanceMode', ], 'tags' => [ 'shape' => 'TagMap', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'ttl' => [ 'shape' => 'TTL', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'enablePullRequestPreview' => [ 'shape' => 'EnablePullRequestPreview', ], 'pullRequestEnvironmentName' => [ 'shape' => 'PullRequestEnvironmentName', ], 'backendEnvironmentArn' => [ 'shape' => 'BackendEnvironmentArn', ], 'backend' => [ 'shape' => 'Backend', ], ], ], 'CreateBranchResult' => [ 'type' => 'structure', 'required' => [ 'branch', ], 'members' => [ 'branch' => [ 'shape' => 'Branch', ], ], ], 'CreateDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'fileMap' => [ 'shape' => 'FileMap', ], ], ], 'CreateDeploymentResult' => [ 'type' => 'structure', 'required' => [ 'fileUploadUrls', 'zipUploadUrl', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'fileUploadUrls' => [ 'shape' => 'FileUploadUrls', ], 'zipUploadUrl' => [ 'shape' => 'UploadUrl', ], ], ], 'CreateDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'domainName', 'subDomainSettings', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'domainName' => [ 'shape' => 'DomainName', ], 'enableAutoSubDomain' => [ 'shape' => 'EnableAutoSubDomain', ], 'subDomainSettings' => [ 'shape' => 'SubDomainSettings', ], 'autoSubDomainCreationPatterns' => [ 'shape' => 'AutoSubDomainCreationPatterns', ], 'autoSubDomainIAMRole' => [ 'shape' => 'AutoSubDomainIAMRole', ], 'certificateSettings' => [ 'shape' => 'CertificateSettings', ], ], ], 'CreateDomainAssociationResult' => [ 'type' => 'structure', 'required' => [ 'domainAssociation', ], 'members' => [ 'domainAssociation' => [ 'shape' => 'DomainAssociation', ], ], ], 'CreateTime' => [ 'type' => 'timestamp', ], 'CreateWebhookRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', ], 'description' => [ 'shape' => 'Description', ], ], ], 'CreateWebhookResult' => [ 'type' => 'structure', 'required' => [ 'webhook', ], 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'CustomDomain' => [ 'type' => 'string', 'max' => 255, ], 'CustomDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDomain', ], 'max' => 255, ], 'CustomHeaders' => [ 'type' => 'string', 'max' => 25000, 'min' => 0, 'pattern' => '(?s).*', ], 'CustomRule' => [ 'type' => 'structure', 'required' => [ 'source', 'target', ], 'members' => [ 'source' => [ 'shape' => 'Source', ], 'target' => [ 'shape' => 'Target', ], 'status' => [ 'shape' => 'Status', ], 'condition' => [ 'shape' => 'Condition', ], ], ], 'CustomRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRule', ], ], 'DNSRecord' => [ 'type' => 'string', 'max' => 1000, ], 'DefaultDomain' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'DeleteAppRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], ], ], 'DeleteAppResult' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'DeleteBackendEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'EnvironmentName', 'location' => 'uri', 'locationName' => 'environmentName', ], ], ], 'DeleteBackendEnvironmentResult' => [ 'type' => 'structure', 'required' => [ 'backendEnvironment', ], 'members' => [ 'backendEnvironment' => [ 'shape' => 'BackendEnvironment', ], ], ], 'DeleteBranchRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], ], ], 'DeleteBranchResult' => [ 'type' => 'structure', 'required' => [ 'branch', ], 'members' => [ 'branch' => [ 'shape' => 'Branch', ], ], ], 'DeleteDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'domainName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'DeleteDomainAssociationResult' => [ 'type' => 'structure', 'required' => [ 'domainAssociation', ], 'members' => [ 'domainAssociation' => [ 'shape' => 'DomainAssociation', ], ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', 'jobId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'DeleteJobResult' => [ 'type' => 'structure', 'required' => [ 'jobSummary', ], 'members' => [ 'jobSummary' => [ 'shape' => 'JobSummary', ], ], ], 'DeleteWebhookRequest' => [ 'type' => 'structure', 'required' => [ 'webhookId', ], 'members' => [ 'webhookId' => [ 'shape' => 'WebhookId', 'location' => 'uri', 'locationName' => 'webhookId', ], ], ], 'DeleteWebhookResult' => [ 'type' => 'structure', 'required' => [ 'webhook', ], 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'DependentServiceFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, ], 'DeploymentArtifacts' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '(?s).+', ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '(?s).*', ], 'DisplayName' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'DomainAssociation' => [ 'type' => 'structure', 'required' => [ 'domainAssociationArn', 'domainName', 'enableAutoSubDomain', 'domainStatus', 'statusReason', 'subDomains', ], 'members' => [ 'domainAssociationArn' => [ 'shape' => 'DomainAssociationArn', ], 'domainName' => [ 'shape' => 'DomainName', ], 'enableAutoSubDomain' => [ 'shape' => 'EnableAutoSubDomain', ], 'autoSubDomainCreationPatterns' => [ 'shape' => 'AutoSubDomainCreationPatterns', ], 'autoSubDomainIAMRole' => [ 'shape' => 'AutoSubDomainIAMRole', ], 'domainStatus' => [ 'shape' => 'DomainStatus', ], 'updateStatus' => [ 'shape' => 'UpdateStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'certificateVerificationDNSRecord' => [ 'shape' => 'CertificateVerificationDNSRecord', ], 'subDomains' => [ 'shape' => 'SubDomains', ], 'certificate' => [ 'shape' => 'Certificate', ], ], ], 'DomainAssociationArn' => [ 'type' => 'string', 'max' => 1000, ], 'DomainAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainAssociation', ], 'max' => 255, ], 'DomainName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])(\\.)?$', ], 'DomainPrefix' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VERIFICATION', 'IN_PROGRESS', 'AVAILABLE', 'IMPORTING_CUSTOM_CERTIFICATE', 'PENDING_DEPLOYMENT', 'AWAITING_APP_CNAME', 'FAILED', 'CREATING', 'REQUESTING_CERTIFICATE', 'UPDATING', ], ], 'EnableAutoBranchCreation' => [ 'type' => 'boolean', ], 'EnableAutoBuild' => [ 'type' => 'boolean', ], 'EnableAutoSubDomain' => [ 'type' => 'boolean', ], 'EnableBasicAuth' => [ 'type' => 'boolean', ], 'EnableBranchAutoBuild' => [ 'type' => 'boolean', ], 'EnableBranchAutoDeletion' => [ 'type' => 'boolean', ], 'EnableNotification' => [ 'type' => 'boolean', ], 'EnablePerformanceMode' => [ 'type' => 'boolean', ], 'EnablePullRequestPreview' => [ 'type' => 'boolean', ], 'EndTime' => [ 'type' => 'timestamp', ], 'EnvKey' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'EnvValue' => [ 'type' => 'string', 'max' => 5500, 'pattern' => '(?s).*', ], 'EnvironmentName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?s).+', ], 'EnvironmentVariables' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvKey', ], 'value' => [ 'shape' => 'EnvValue', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 255, ], 'FileMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FileName', ], 'value' => [ 'shape' => 'MD5Hash', ], ], 'FileName' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'FileUploadUrls' => [ 'type' => 'map', 'key' => [ 'shape' => 'FileName', ], 'value' => [ 'shape' => 'UploadUrl', ], ], 'Framework' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'GenerateAccessLogsRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'appId', ], 'members' => [ 'startTime' => [ 'shape' => 'StartTime', ], 'endTime' => [ 'shape' => 'EndTime', ], 'domainName' => [ 'shape' => 'DomainName', ], 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], ], ], 'GenerateAccessLogsResult' => [ 'type' => 'structure', 'members' => [ 'logUrl' => [ 'shape' => 'LogUrl', ], ], ], 'GetAppRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], ], ], 'GetAppResult' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'GetArtifactUrlRequest' => [ 'type' => 'structure', 'required' => [ 'artifactId', ], 'members' => [ 'artifactId' => [ 'shape' => 'ArtifactId', 'location' => 'uri', 'locationName' => 'artifactId', ], ], ], 'GetArtifactUrlResult' => [ 'type' => 'structure', 'required' => [ 'artifactId', 'artifactUrl', ], 'members' => [ 'artifactId' => [ 'shape' => 'ArtifactId', ], 'artifactUrl' => [ 'shape' => 'ArtifactUrl', ], ], ], 'GetBackendEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'EnvironmentName', 'location' => 'uri', 'locationName' => 'environmentName', ], ], ], 'GetBackendEnvironmentResult' => [ 'type' => 'structure', 'required' => [ 'backendEnvironment', ], 'members' => [ 'backendEnvironment' => [ 'shape' => 'BackendEnvironment', ], ], ], 'GetBranchRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], ], ], 'GetBranchResult' => [ 'type' => 'structure', 'required' => [ 'branch', ], 'members' => [ 'branch' => [ 'shape' => 'Branch', ], ], ], 'GetDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'domainName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], ], ], 'GetDomainAssociationResult' => [ 'type' => 'structure', 'required' => [ 'domainAssociation', ], 'members' => [ 'domainAssociation' => [ 'shape' => 'DomainAssociation', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', 'jobId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetJobResult' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'GetWebhookRequest' => [ 'type' => 'structure', 'required' => [ 'webhookId', ], 'members' => [ 'webhookId' => [ 'shape' => 'WebhookId', 'location' => 'uri', 'locationName' => 'webhookId', ], ], ], 'GetWebhookResult' => [ 'type' => 'structure', 'required' => [ 'webhook', ], 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Job' => [ 'type' => 'structure', 'required' => [ 'summary', 'steps', ], 'members' => [ 'summary' => [ 'shape' => 'JobSummary', ], 'steps' => [ 'shape' => 'Steps', ], ], ], 'JobArn' => [ 'type' => 'string', 'max' => 1000, ], 'JobId' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[0-9]+', ], 'JobReason' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PROVISIONING', 'RUNNING', 'FAILED', 'SUCCEED', 'CANCELLING', 'CANCELLED', ], ], 'JobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'jobArn', 'jobId', 'commitId', 'commitMessage', 'commitTime', 'startTime', 'status', 'jobType', ], 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'commitId' => [ 'shape' => 'CommitId', ], 'commitMessage' => [ 'shape' => 'CommitMessage', ], 'commitTime' => [ 'shape' => 'CommitTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'JobStatus', ], 'endTime' => [ 'shape' => 'EndTime', ], 'jobType' => [ 'shape' => 'JobType', ], ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'RELEASE', 'RETRY', 'MANUAL', 'WEB_HOOK', ], 'max' => 10, ], 'LastDeployTime' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListAppsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAppsResult' => [ 'type' => 'structure', 'required' => [ 'apps', ], 'members' => [ 'apps' => [ 'shape' => 'Apps', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListArtifactsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', 'jobId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListArtifactsResult' => [ 'type' => 'structure', 'required' => [ 'artifacts', ], 'members' => [ 'artifacts' => [ 'shape' => 'Artifacts', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBackendEnvironmentsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'EnvironmentName', 'location' => 'querystring', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBackendEnvironmentsResult' => [ 'type' => 'structure', 'required' => [ 'backendEnvironments', ], 'members' => [ 'backendEnvironments' => [ 'shape' => 'BackendEnvironments', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBranchesRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListBranchesResult' => [ 'type' => 'structure', 'required' => [ 'branches', ], 'members' => [ 'branches' => [ 'shape' => 'Branches', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDomainAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDomainAssociationsResult' => [ 'type' => 'structure', 'required' => [ 'domainAssociations', ], 'members' => [ 'domainAssociations' => [ 'shape' => 'DomainAssociations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListJobsResult' => [ 'type' => 'structure', 'required' => [ 'jobSummaries', ], 'members' => [ 'jobSummaries' => [ 'shape' => 'JobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWebhooksRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListWebhooksResult' => [ 'type' => 'structure', 'required' => [ 'webhooks', ], 'members' => [ 'webhooks' => [ 'shape' => 'Webhooks', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogUrl' => [ 'type' => 'string', 'max' => 1000, ], 'MD5Hash' => [ 'type' => 'string', 'max' => 32, 'pattern' => '(?s).*', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?s).+', ], 'NextToken' => [ 'type' => 'string', 'max' => 2000, 'pattern' => '(?s).*', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OauthToken' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '(?s).*', 'sensitive' => true, ], 'Platform' => [ 'type' => 'string', 'enum' => [ 'WEB', 'WEB_DYNAMIC', 'WEB_COMPUTE', ], ], 'ProductionBranch' => [ 'type' => 'structure', 'members' => [ 'lastDeployTime' => [ 'shape' => 'LastDeployTime', ], 'status' => [ 'shape' => 'Status', ], 'thumbnailUrl' => [ 'shape' => 'ThumbnailUrl', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'PullRequestEnvironmentName' => [ 'type' => 'string', 'max' => 20, 'pattern' => '(?s).*', ], 'Repository' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '(?s).*', ], 'RepositoryCloneMethod' => [ 'type' => 'string', 'enum' => [ 'SSH', 'TOKEN', 'SIGV4', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:amplify:.*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'Code', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Screenshots' => [ 'type' => 'map', 'key' => [ 'shape' => 'ThumbnailName', ], 'value' => [ 'shape' => 'ThumbnailUrl', ], ], 'ServiceRoleArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '(?s).*', ], 'Source' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(?s).+', ], 'SourceUrl' => [ 'type' => 'string', 'max' => 3000, 'pattern' => '(?s).*', ], 'StackArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:cloudformation:[a-z0-9-]+:\\d{12}:stack/.+/.+$', ], 'StackName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(?s).+', ], 'Stage' => [ 'type' => 'string', 'enum' => [ 'PRODUCTION', 'BETA', 'DEVELOPMENT', 'EXPERIMENTAL', 'PULL_REQUEST', ], ], 'StartDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', ], 'sourceUrl' => [ 'shape' => 'SourceUrl', ], ], ], 'StartDeploymentResult' => [ 'type' => 'structure', 'required' => [ 'jobSummary', ], 'members' => [ 'jobSummary' => [ 'shape' => 'JobSummary', ], ], ], 'StartJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', 'jobType', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', ], 'jobType' => [ 'shape' => 'JobType', ], 'jobReason' => [ 'shape' => 'JobReason', ], 'commitId' => [ 'shape' => 'CommitId', ], 'commitMessage' => [ 'shape' => 'CommitMessage', ], 'commitTime' => [ 'shape' => 'CommitTime', ], ], ], 'StartJobResult' => [ 'type' => 'structure', 'required' => [ 'jobSummary', ], 'members' => [ 'jobSummary' => [ 'shape' => 'JobSummary', ], ], ], 'StartTime' => [ 'type' => 'timestamp', ], 'Status' => [ 'type' => 'string', 'max' => 7, 'min' => 3, 'pattern' => '.{3,7}', ], 'StatusReason' => [ 'type' => 'string', 'max' => 1000, ], 'Step' => [ 'type' => 'structure', 'required' => [ 'stepName', 'startTime', 'status', 'endTime', ], 'members' => [ 'stepName' => [ 'shape' => 'StepName', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'JobStatus', ], 'endTime' => [ 'shape' => 'EndTime', ], 'logUrl' => [ 'shape' => 'LogUrl', ], 'artifactsUrl' => [ 'shape' => 'ArtifactsUrl', ], 'testArtifactsUrl' => [ 'shape' => 'TestArtifactsUrl', ], 'testConfigUrl' => [ 'shape' => 'TestConfigUrl', ], 'screenshots' => [ 'shape' => 'Screenshots', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'context' => [ 'shape' => 'Context', ], ], ], 'StepName' => [ 'type' => 'string', 'max' => 255, ], 'Steps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], ], 'StopJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', 'jobId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'StopJobResult' => [ 'type' => 'structure', 'required' => [ 'jobSummary', ], 'members' => [ 'jobSummary' => [ 'shape' => 'JobSummary', ], ], ], 'SubDomain' => [ 'type' => 'structure', 'required' => [ 'subDomainSetting', 'verified', 'dnsRecord', ], 'members' => [ 'subDomainSetting' => [ 'shape' => 'SubDomainSetting', ], 'verified' => [ 'shape' => 'Verified', ], 'dnsRecord' => [ 'shape' => 'DNSRecord', ], ], ], 'SubDomainSetting' => [ 'type' => 'structure', 'required' => [ 'prefix', 'branchName', ], 'members' => [ 'prefix' => [ 'shape' => 'DomainPrefix', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'SubDomainSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubDomainSetting', ], 'max' => 500, ], 'SubDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubDomain', ], 'max' => 500, ], 'TTL' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '\\d*', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Target' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(?s).+', ], 'TestArtifactsUrl' => [ 'type' => 'string', 'max' => 1000, ], 'TestConfigUrl' => [ 'type' => 'string', 'max' => 1000, ], 'ThumbnailName' => [ 'type' => 'string', 'max' => 256, ], 'ThumbnailUrl' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TotalNumberOfJobs' => [ 'type' => 'string', 'max' => 1000, ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppRequest' => [ 'type' => 'structure', 'required' => [ 'appId', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'platform' => [ 'shape' => 'Platform', ], 'iamServiceRoleArn' => [ 'shape' => 'ServiceRoleArn', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'enableBranchAutoBuild' => [ 'shape' => 'EnableAutoBuild', ], 'enableBranchAutoDeletion' => [ 'shape' => 'EnableBranchAutoDeletion', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'customRules' => [ 'shape' => 'CustomRules', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'customHeaders' => [ 'shape' => 'CustomHeaders', ], 'enableAutoBranchCreation' => [ 'shape' => 'EnableAutoBranchCreation', ], 'autoBranchCreationPatterns' => [ 'shape' => 'AutoBranchCreationPatterns', ], 'autoBranchCreationConfig' => [ 'shape' => 'AutoBranchCreationConfig', ], 'repository' => [ 'shape' => 'Repository', ], 'oauthToken' => [ 'shape' => 'OauthToken', ], 'accessToken' => [ 'shape' => 'AccessToken', ], ], ], 'UpdateAppResult' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'UpdateBranchRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'branchName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'branchName' => [ 'shape' => 'BranchName', 'location' => 'uri', 'locationName' => 'branchName', ], 'description' => [ 'shape' => 'Description', ], 'framework' => [ 'shape' => 'Framework', ], 'stage' => [ 'shape' => 'Stage', ], 'enableNotification' => [ 'shape' => 'EnableNotification', ], 'enableAutoBuild' => [ 'shape' => 'EnableAutoBuild', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'basicAuthCredentials' => [ 'shape' => 'BasicAuthCredentials', ], 'enableBasicAuth' => [ 'shape' => 'EnableBasicAuth', ], 'enablePerformanceMode' => [ 'shape' => 'EnablePerformanceMode', ], 'buildSpec' => [ 'shape' => 'BuildSpec', ], 'ttl' => [ 'shape' => 'TTL', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'enablePullRequestPreview' => [ 'shape' => 'EnablePullRequestPreview', ], 'pullRequestEnvironmentName' => [ 'shape' => 'PullRequestEnvironmentName', ], 'backendEnvironmentArn' => [ 'shape' => 'BackendEnvironmentArn', ], 'backend' => [ 'shape' => 'Backend', ], ], ], 'UpdateBranchResult' => [ 'type' => 'structure', 'required' => [ 'branch', ], 'members' => [ 'branch' => [ 'shape' => 'Branch', ], ], ], 'UpdateDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'domainName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'domainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'domainName', ], 'enableAutoSubDomain' => [ 'shape' => 'EnableAutoSubDomain', ], 'subDomainSettings' => [ 'shape' => 'SubDomainSettings', ], 'autoSubDomainCreationPatterns' => [ 'shape' => 'AutoSubDomainCreationPatterns', ], 'autoSubDomainIAMRole' => [ 'shape' => 'AutoSubDomainIAMRole', ], 'certificateSettings' => [ 'shape' => 'CertificateSettings', ], ], ], 'UpdateDomainAssociationResult' => [ 'type' => 'structure', 'required' => [ 'domainAssociation', ], 'members' => [ 'domainAssociation' => [ 'shape' => 'DomainAssociation', ], ], ], 'UpdateStatus' => [ 'type' => 'string', 'enum' => [ 'REQUESTING_CERTIFICATE', 'PENDING_VERIFICATION', 'IMPORTING_CUSTOM_CERTIFICATE', 'PENDING_DEPLOYMENT', 'AWAITING_APP_CNAME', 'UPDATE_COMPLETE', 'UPDATE_FAILED', ], ], 'UpdateTime' => [ 'type' => 'timestamp', ], 'UpdateWebhookRequest' => [ 'type' => 'structure', 'required' => [ 'webhookId', ], 'members' => [ 'webhookId' => [ 'shape' => 'WebhookId', 'location' => 'uri', 'locationName' => 'webhookId', ], 'branchName' => [ 'shape' => 'BranchName', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateWebhookResult' => [ 'type' => 'structure', 'required' => [ 'webhook', ], 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'UploadUrl' => [ 'type' => 'string', 'max' => 1000, ], 'Verified' => [ 'type' => 'boolean', ], 'Webhook' => [ 'type' => 'structure', 'required' => [ 'webhookArn', 'webhookId', 'webhookUrl', 'branchName', 'description', 'createTime', 'updateTime', ], 'members' => [ 'webhookArn' => [ 'shape' => 'WebhookArn', ], 'webhookId' => [ 'shape' => 'WebhookId', ], 'webhookUrl' => [ 'shape' => 'WebhookUrl', ], 'branchName' => [ 'shape' => 'BranchName', ], 'description' => [ 'shape' => 'Description', ], 'createTime' => [ 'shape' => 'CreateTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'WebhookArn' => [ 'type' => 'string', 'max' => 1000, ], 'WebhookId' => [ 'type' => 'string', 'max' => 255, 'pattern' => '(?s).*', ], 'WebhookUrl' => [ 'type' => 'string', 'max' => 1000, ], 'Webhooks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Webhook', ], ], ],];
