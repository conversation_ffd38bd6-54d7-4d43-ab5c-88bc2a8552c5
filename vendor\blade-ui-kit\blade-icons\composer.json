{"name": "blade-ui-kit/blade-icons", "description": "A package to easily make use of icons in your Laravel Blade views.", "keywords": ["Blade", "Icons", "<PERSON><PERSON>", "SVG"], "homepage": "https://github.com/blade-ui-kit/blade-icons", "license": "MIT", "support": {"issues": "https://github.com/blade-ui-kit/blade-icons/issues", "source": "https://github.com/blade-ui-kit/blade-icons"}, "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://driesvints.com"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/driesvints"}, {"type": "paypal", "url": "https://www.paypal.com/paypalme/driesvints"}], "require": {"php": "^7.4|^8.0", "illuminate/contracts": "^8.0|^9.0|^10.0|^11.0", "illuminate/filesystem": "^8.0|^9.0|^10.0|^11.0", "illuminate/support": "^8.0|^9.0|^10.0|^11.0", "illuminate/view": "^8.0|^9.0|^10.0|^11.0", "symfony/console": "^5.3|^6.0|^7.0", "symfony/finder": "^5.3|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "^1.5.1", "orchestra/testbench": "^6.0|^7.0|^8.0|^9.0", "phpunit/phpunit": "^9.0|^10.5|^11.0"}, "bin": ["bin/blade-icons-generate"], "autoload": {"psr-4": {"BladeUI\\Icons\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests"}}, "extra": {"laravel": {"providers": ["BladeUI\\Icons\\BladeIconsServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}