{"name": "doctrine/cache", "type": "library", "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "keywords": ["php", "cache", "caching", "abstraction", "redis", "memcached", "couchdb", "xcache", "apcu"], "homepage": "https://www.doctrine-project.org/projects/cache.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "require": {"php": "~7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "doctrine/coding-standard": "^9", "psr/cache": "^1.0 || ^2.0 || ^3.0", "cache/integration-tests": "dev-master", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests/Doctrine/Tests"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}