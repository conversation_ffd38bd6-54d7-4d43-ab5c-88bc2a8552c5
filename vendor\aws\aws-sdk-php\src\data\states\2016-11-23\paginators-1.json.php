<?php
// This file was auto-generated from sdk-root/src/data/states/2016-11-23/paginators-1.json
return [ 'pagination' => [ 'GetExecutionHistory' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'events', ], 'ListActivities' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'activities', ], 'ListExecutions' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'executions', ], 'ListMapRuns' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'mapRuns', ], 'ListStateMachines' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'stateMachines', ], ],];
