<?php
// This file was auto-generated from sdk-root/src/data/connectcases/2022-10-03/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-10-03', 'endpointPrefix' => 'cases', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'ConnectCases', 'serviceFullName' => 'Amazon Connect Cases', 'serviceId' => 'ConnectCases', 'signatureVersion' => 'v4', 'signingName' => 'cases', 'uid' => 'connectcases-2022-10-03', ], 'operations' => [ 'BatchGetField' => [ 'name' => 'BatchGetField', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/fields-batch', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetFieldRequest', ], 'output' => [ 'shape' => 'BatchGetFieldResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchPutFieldOptions' => [ 'name' => 'BatchPutFieldOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/fields/{fieldId}/options', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutFieldOptionsRequest', ], 'output' => [ 'shape' => 'BatchPutFieldOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateCase' => [ 'name' => 'CreateCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCaseRequest', ], 'output' => [ 'shape' => 'CreateCaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateField' => [ 'name' => 'CreateField', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/fields', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFieldRequest', ], 'output' => [ 'shape' => 'CreateFieldResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateLayout' => [ 'name' => 'CreateLayout', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/layouts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLayoutRequest', ], 'output' => [ 'shape' => 'CreateLayoutResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateRelatedItem' => [ 'name' => 'CreateRelatedItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases/{caseId}/related-items/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRelatedItemRequest', ], 'output' => [ 'shape' => 'CreateRelatedItemResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateTemplate' => [ 'name' => 'CreateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTemplateRequest', ], 'output' => [ 'shape' => 'CreateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domains/{domainId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetCase' => [ 'name' => 'GetCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases/{caseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCaseRequest', ], 'output' => [ 'shape' => 'GetCaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCaseAuditEvents' => [ 'name' => 'GetCaseAuditEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases/{caseId}/audit-history', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCaseAuditEventsRequest', ], 'output' => [ 'shape' => 'GetCaseAuditEventsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCaseEventConfiguration' => [ 'name' => 'GetCaseEventConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/case-event-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCaseEventConfigurationRequest', ], 'output' => [ 'shape' => 'GetCaseEventConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDomain' => [ 'name' => 'GetDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDomainRequest', ], 'output' => [ 'shape' => 'GetDomainResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetLayout' => [ 'name' => 'GetLayout', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/layouts/{layoutId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLayoutRequest', ], 'output' => [ 'shape' => 'GetLayoutResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetTemplate' => [ 'name' => 'GetTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/templates/{templateId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTemplateRequest', ], 'output' => [ 'shape' => 'GetTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCasesForContact' => [ 'name' => 'ListCasesForContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/list-cases-for-contact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCasesForContactRequest', ], 'output' => [ 'shape' => 'ListCasesForContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDomains' => [ 'name' => 'ListDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDomainsRequest', ], 'output' => [ 'shape' => 'ListDomainsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFieldOptions' => [ 'name' => 'ListFieldOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/fields/{fieldId}/options-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFieldOptionsRequest', ], 'output' => [ 'shape' => 'ListFieldOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFields' => [ 'name' => 'ListFields', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/fields-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFieldsRequest', ], 'output' => [ 'shape' => 'ListFieldsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLayouts' => [ 'name' => 'ListLayouts', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/layouts-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLayoutsRequest', ], 'output' => [ 'shape' => 'ListLayoutsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ListTemplates' => [ 'name' => 'ListTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/templates-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplatesRequest', ], 'output' => [ 'shape' => 'ListTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutCaseEventConfiguration' => [ 'name' => 'PutCaseEventConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/case-event-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutCaseEventConfigurationRequest', ], 'output' => [ 'shape' => 'PutCaseEventConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SearchCases' => [ 'name' => 'SearchCases', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases-search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchCasesRequest', ], 'output' => [ 'shape' => 'SearchCasesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SearchRelatedItems' => [ 'name' => 'SearchRelatedItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/domains/{domainId}/cases/{caseId}/related-items-search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchRelatedItemsRequest', ], 'output' => [ 'shape' => 'SearchRelatedItemsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCase' => [ 'name' => 'UpdateCase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/cases/{caseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCaseRequest', ], 'output' => [ 'shape' => 'UpdateCaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateField' => [ 'name' => 'UpdateField', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/fields/{fieldId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFieldRequest', ], 'output' => [ 'shape' => 'UpdateFieldResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateLayout' => [ 'name' => 'UpdateLayout', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/layouts/{layoutId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLayoutRequest', ], 'output' => [ 'shape' => 'UpdateLayoutResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateTemplate' => [ 'name' => 'UpdateTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domains/{domainId}/templates/{templateId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTemplateRequest', ], 'output' => [ 'shape' => 'UpdateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'AssociationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'AuditEvent' => [ 'type' => 'structure', 'required' => [ 'eventId', 'fields', 'performedTime', 'type', ], 'members' => [ 'eventId' => [ 'shape' => 'AuditEventId', ], 'fields' => [ 'shape' => 'AuditEventFieldList', ], 'performedBy' => [ 'shape' => 'AuditEventPerformedBy', ], 'performedTime' => [ 'shape' => 'AuditEventDateTime', ], 'relatedItemType' => [ 'shape' => 'RelatedItemType', ], 'type' => [ 'shape' => 'AuditEventType', ], ], ], 'AuditEventDateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'AuditEventField' => [ 'type' => 'structure', 'required' => [ 'eventFieldId', 'newValue', ], 'members' => [ 'eventFieldId' => [ 'shape' => 'AuditEventFieldId', ], 'newValue' => [ 'shape' => 'AuditEventFieldValueUnion', ], 'oldValue' => [ 'shape' => 'AuditEventFieldValueUnion', ], ], ], 'AuditEventFieldId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'AuditEventFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditEventField', ], ], 'AuditEventFieldValueUnion' => [ 'type' => 'structure', 'members' => [ 'booleanValue' => [ 'shape' => 'Boolean', ], 'doubleValue' => [ 'shape' => 'Double', ], 'emptyValue' => [ 'shape' => 'EmptyFieldValue', ], 'stringValue' => [ 'shape' => 'AuditEventFieldValueUnionStringValueString', ], 'userArnValue' => [ 'shape' => 'String', ], ], 'union' => true, ], 'AuditEventFieldValueUnionStringValueString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'AuditEventId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'AuditEventPerformedBy' => [ 'type' => 'structure', 'required' => [ 'iamPrincipalArn', ], 'members' => [ 'iamPrincipalArn' => [ 'shape' => 'IamPrincipalArn', ], 'user' => [ 'shape' => 'UserUnion', ], ], ], 'AuditEventType' => [ 'type' => 'string', 'enum' => [ 'Case.Created', 'Case.Updated', 'RelatedItem.Created', ], ], 'BasicLayout' => [ 'type' => 'structure', 'members' => [ 'moreInfo' => [ 'shape' => 'LayoutSections', ], 'topPanel' => [ 'shape' => 'LayoutSections', ], ], ], 'BatchGetFieldIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldIdentifier', ], 'max' => 50, 'min' => 1, ], 'BatchGetFieldRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'fields', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fields' => [ 'shape' => 'BatchGetFieldIdentifierList', ], ], ], 'BatchGetFieldResponse' => [ 'type' => 'structure', 'required' => [ 'errors', 'fields', ], 'members' => [ 'errors' => [ 'shape' => 'BatchGetFieldResponseErrorsList', ], 'fields' => [ 'shape' => 'BatchGetFieldResponseFieldsList', ], ], ], 'BatchGetFieldResponseErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldError', ], 'max' => 50, 'min' => 0, ], 'BatchGetFieldResponseFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetFieldResponse', ], 'max' => 50, 'min' => 0, ], 'BatchPutFieldOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'fieldId', 'options', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fieldId' => [ 'shape' => 'FieldId', 'location' => 'uri', 'locationName' => 'fieldId', ], 'options' => [ 'shape' => 'BatchPutFieldOptionsRequestOptionsList', ], ], ], 'BatchPutFieldOptionsRequestOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldOption', ], 'max' => 50, 'min' => 0, ], 'BatchPutFieldOptionsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchPutFieldOptionsResponseErrorsList', ], ], ], 'BatchPutFieldOptionsResponseErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldOptionError', ], 'max' => 50, 'min' => 0, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CaseArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'CaseEventIncludedData' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'CaseEventIncludedDataFieldsList', ], ], ], 'CaseEventIncludedDataFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldIdentifier', ], 'max' => 200, 'min' => 0, ], 'CaseFilter' => [ 'type' => 'structure', 'members' => [ 'andAll' => [ 'shape' => 'CaseFilterAndAllList', ], 'field' => [ 'shape' => 'FieldFilter', ], 'not' => [ 'shape' => 'CaseFilter', ], 'orAll' => [ 'shape' => 'CaseFilterOrAllList', ], ], 'union' => true, ], 'CaseFilterAndAllList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaseFilter', ], 'max' => 10, 'min' => 0, ], 'CaseFilterOrAllList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaseFilter', ], 'max' => 10, 'min' => 0, ], 'CaseId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'CaseSummary' => [ 'type' => 'structure', 'required' => [ 'caseId', 'templateId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'Channel' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CommentBody' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, ], 'CommentBodyTextType' => [ 'type' => 'string', 'enum' => [ 'Text/Plain', ], ], 'CommentContent' => [ 'type' => 'structure', 'required' => [ 'body', 'contentType', ], 'members' => [ 'body' => [ 'shape' => 'CommentBody', ], 'contentType' => [ 'shape' => 'CommentBodyTextType', ], ], ], 'CommentFilter' => [ 'type' => 'structure', 'members' => [], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectedToSystemTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Contact' => [ 'type' => 'structure', 'required' => [ 'contactArn', ], 'members' => [ 'contactArn' => [ 'shape' => 'ContactArn', ], ], ], 'ContactArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ContactContent' => [ 'type' => 'structure', 'required' => [ 'channel', 'connectedToSystemTime', 'contactArn', ], 'members' => [ 'channel' => [ 'shape' => 'Channel', ], 'connectedToSystemTime' => [ 'shape' => 'ConnectedToSystemTime', ], 'contactArn' => [ 'shape' => 'ContactArn', ], ], ], 'ContactFilter' => [ 'type' => 'structure', 'members' => [ 'channel' => [ 'shape' => 'ContactFilterChannelList', ], 'contactArn' => [ 'shape' => 'ContactArn', ], ], ], 'ContactFilterChannelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], 'max' => 3, 'min' => 0, ], 'CreateCaseRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'fields', 'templateId', ], 'members' => [ 'clientToken' => [ 'shape' => 'CreateCaseRequestClientTokenString', 'idempotencyToken' => true, ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fields' => [ 'shape' => 'CreateCaseRequestFieldsList', ], 'performedBy' => [ 'shape' => 'UserUnion', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'CreateCaseRequestClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'CreateCaseRequestFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValue', ], 'max' => 100, 'min' => 0, ], 'CreateCaseResponse' => [ 'type' => 'structure', 'required' => [ 'caseArn', 'caseId', ], 'members' => [ 'caseArn' => [ 'shape' => 'CaseArn', ], 'caseId' => [ 'shape' => 'CaseId', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DomainName', ], ], ], 'CreateDomainResponse' => [ 'type' => 'structure', 'required' => [ 'domainArn', 'domainId', 'domainStatus', ], 'members' => [ 'domainArn' => [ 'shape' => 'DomainArn', ], 'domainId' => [ 'shape' => 'DomainId', ], 'domainStatus' => [ 'shape' => 'DomainStatus', ], ], ], 'CreateFieldRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'name', 'type', ], 'members' => [ 'description' => [ 'shape' => 'FieldDescription', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'name' => [ 'shape' => 'FieldName', ], 'type' => [ 'shape' => 'FieldType', ], ], ], 'CreateFieldResponse' => [ 'type' => 'structure', 'required' => [ 'fieldArn', 'fieldId', ], 'members' => [ 'fieldArn' => [ 'shape' => 'FieldArn', ], 'fieldId' => [ 'shape' => 'FieldId', ], ], ], 'CreateLayoutRequest' => [ 'type' => 'structure', 'required' => [ 'content', 'domainId', 'name', ], 'members' => [ 'content' => [ 'shape' => 'LayoutContent', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'name' => [ 'shape' => 'LayoutName', ], ], ], 'CreateLayoutResponse' => [ 'type' => 'structure', 'required' => [ 'layoutArn', 'layoutId', ], 'members' => [ 'layoutArn' => [ 'shape' => 'LayoutArn', ], 'layoutId' => [ 'shape' => 'LayoutId', ], ], ], 'CreateRelatedItemRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'content', 'domainId', 'type', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'content' => [ 'shape' => 'RelatedItemInputContent', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'performedBy' => [ 'shape' => 'UserUnion', ], 'type' => [ 'shape' => 'RelatedItemType', ], ], ], 'CreateRelatedItemResponse' => [ 'type' => 'structure', 'required' => [ 'relatedItemArn', 'relatedItemId', ], 'members' => [ 'relatedItemArn' => [ 'shape' => 'RelatedItemArn', ], 'relatedItemId' => [ 'shape' => 'RelatedItemId', ], ], ], 'CreateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'name', ], 'members' => [ 'description' => [ 'shape' => 'TemplateDescription', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'layoutConfiguration' => [ 'shape' => 'LayoutConfiguration', ], 'name' => [ 'shape' => 'TemplateName', ], 'requiredFields' => [ 'shape' => 'RequiredFieldList', ], 'status' => [ 'shape' => 'TemplateStatus', ], ], ], 'CreateTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'templateArn', 'templateId', ], 'members' => [ 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'CreatedTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], ], ], 'DeleteDomainResponse' => [ 'type' => 'structure', 'members' => [], ], 'DomainArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DomainId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DomainName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'CreationInProgress', 'CreationFailed', ], ], 'DomainSummary' => [ 'type' => 'structure', 'required' => [ 'domainArn', 'domainId', 'name', ], 'members' => [ 'domainArn' => [ 'shape' => 'DomainArn', ], 'domainId' => [ 'shape' => 'DomainId', ], 'name' => [ 'shape' => 'DomainName', ], ], ], 'DomainSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainSummary', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EmptyFieldValue' => [ 'type' => 'structure', 'members' => [], ], 'EventBridgeConfiguration' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'includedData' => [ 'shape' => 'EventIncludedData', ], ], ], 'EventIncludedData' => [ 'type' => 'structure', 'members' => [ 'caseData' => [ 'shape' => 'CaseEventIncludedData', ], 'relatedItemData' => [ 'shape' => 'RelatedItemEventIncludedData', ], ], ], 'FieldArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FieldDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'FieldError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'id', ], 'members' => [ 'errorCode' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'FieldId', ], 'message' => [ 'shape' => 'String', ], ], ], 'FieldFilter' => [ 'type' => 'structure', 'members' => [ 'contains' => [ 'shape' => 'FieldValue', ], 'equalTo' => [ 'shape' => 'FieldValue', ], 'greaterThan' => [ 'shape' => 'FieldValue', ], 'greaterThanOrEqualTo' => [ 'shape' => 'FieldValue', ], 'lessThan' => [ 'shape' => 'FieldValue', ], 'lessThanOrEqualTo' => [ 'shape' => 'FieldValue', ], ], 'union' => true, ], 'FieldGroup' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'FieldGroupFieldsList', ], 'name' => [ 'shape' => 'FieldGroupNameString', ], ], ], 'FieldGroupFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldItem', ], 'max' => 100, 'min' => 0, ], 'FieldGroupNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'FieldId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FieldIdentifier' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'FieldId', ], ], ], 'FieldItem' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'FieldId', ], ], ], 'FieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'FieldNamespace' => [ 'type' => 'string', 'enum' => [ 'System', 'Custom', ], ], 'FieldOption' => [ 'type' => 'structure', 'required' => [ 'active', 'name', 'value', ], 'members' => [ 'active' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'FieldOptionName', ], 'value' => [ 'shape' => 'FieldOptionValue', ], ], ], 'FieldOptionError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'message', 'value', ], 'members' => [ 'errorCode' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'FieldOptionValue', ], ], ], 'FieldOptionName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'FieldOptionValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'FieldOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldOption', ], ], 'FieldSummary' => [ 'type' => 'structure', 'required' => [ 'fieldArn', 'fieldId', 'name', 'namespace', 'type', ], 'members' => [ 'fieldArn' => [ 'shape' => 'FieldArn', ], 'fieldId' => [ 'shape' => 'FieldId', ], 'name' => [ 'shape' => 'FieldName', ], 'namespace' => [ 'shape' => 'FieldNamespace', ], 'type' => [ 'shape' => 'FieldType', ], ], ], 'FieldType' => [ 'type' => 'string', 'enum' => [ 'Text', 'Number', 'Boolean', 'DateTime', 'SingleSelect', 'Url', 'User', ], ], 'FieldValue' => [ 'type' => 'structure', 'required' => [ 'id', 'value', ], 'members' => [ 'id' => [ 'shape' => 'FieldId', ], 'value' => [ 'shape' => 'FieldValueUnion', ], ], ], 'FieldValueUnion' => [ 'type' => 'structure', 'members' => [ 'booleanValue' => [ 'shape' => 'Boolean', ], 'doubleValue' => [ 'shape' => 'Double', ], 'emptyValue' => [ 'shape' => 'EmptyFieldValue', ], 'stringValue' => [ 'shape' => 'FieldValueUnionStringValueString', ], 'userArnValue' => [ 'shape' => 'String', ], ], 'union' => true, ], 'FieldValueUnionStringValueString' => [ 'type' => 'string', 'max' => 1500, 'min' => 0, ], 'GetCaseAuditEventsRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'domainId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'maxResults' => [ 'shape' => 'GetCaseAuditEventsRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCaseAuditEventsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'GetCaseAuditEventsResponse' => [ 'type' => 'structure', 'required' => [ 'auditEvents', ], 'members' => [ 'auditEvents' => [ 'shape' => 'GetCaseAuditEventsResponseAuditEventsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCaseAuditEventsResponseAuditEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditEvent', ], 'max' => 25, 'min' => 0, ], 'GetCaseEventConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], ], ], 'GetCaseEventConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'eventBridge', ], 'members' => [ 'eventBridge' => [ 'shape' => 'EventBridgeConfiguration', ], ], ], 'GetCaseRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'domainId', 'fields', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fields' => [ 'shape' => 'GetCaseRequestFieldsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCaseRequestFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldIdentifier', ], 'max' => 100, 'min' => 1, ], 'GetCaseResponse' => [ 'type' => 'structure', 'required' => [ 'fields', 'templateId', ], 'members' => [ 'fields' => [ 'shape' => 'GetCaseResponseFieldsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'tags' => [ 'shape' => 'Tags', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'GetCaseResponseFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValue', ], 'max' => 100, 'min' => 0, ], 'GetDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], ], ], 'GetDomainResponse' => [ 'type' => 'structure', 'required' => [ 'createdTime', 'domainArn', 'domainId', 'domainStatus', 'name', ], 'members' => [ 'createdTime' => [ 'shape' => 'CreatedTime', ], 'domainArn' => [ 'shape' => 'DomainArn', ], 'domainId' => [ 'shape' => 'DomainId', ], 'domainStatus' => [ 'shape' => 'DomainStatus', ], 'name' => [ 'shape' => 'DomainName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'GetFieldResponse' => [ 'type' => 'structure', 'required' => [ 'fieldArn', 'fieldId', 'name', 'namespace', 'type', ], 'members' => [ 'description' => [ 'shape' => 'FieldDescription', ], 'fieldArn' => [ 'shape' => 'FieldArn', ], 'fieldId' => [ 'shape' => 'FieldId', ], 'name' => [ 'shape' => 'FieldName', ], 'namespace' => [ 'shape' => 'FieldNamespace', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'FieldType', ], ], ], 'GetLayoutRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'layoutId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'layoutId' => [ 'shape' => 'LayoutId', 'location' => 'uri', 'locationName' => 'layoutId', ], ], ], 'GetLayoutResponse' => [ 'type' => 'structure', 'required' => [ 'content', 'layoutArn', 'layoutId', 'name', ], 'members' => [ 'content' => [ 'shape' => 'LayoutContent', ], 'layoutArn' => [ 'shape' => 'LayoutArn', ], 'layoutId' => [ 'shape' => 'LayoutId', ], 'name' => [ 'shape' => 'LayoutName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'GetTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'templateId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'templateId', ], ], ], 'GetTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'status', 'templateArn', 'templateId', ], 'members' => [ 'description' => [ 'shape' => 'TemplateDescription', ], 'layoutConfiguration' => [ 'shape' => 'LayoutConfiguration', ], 'name' => [ 'shape' => 'TemplateName', ], 'requiredFields' => [ 'shape' => 'RequiredFieldList', ], 'status' => [ 'shape' => 'TemplateStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'IamPrincipalArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'LayoutArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'LayoutConfiguration' => [ 'type' => 'structure', 'members' => [ 'defaultLayout' => [ 'shape' => 'LayoutId', ], ], ], 'LayoutContent' => [ 'type' => 'structure', 'members' => [ 'basic' => [ 'shape' => 'BasicLayout', ], ], 'union' => true, ], 'LayoutId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'LayoutName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'LayoutSections' => [ 'type' => 'structure', 'members' => [ 'sections' => [ 'shape' => 'SectionsList', ], ], ], 'LayoutSummary' => [ 'type' => 'structure', 'required' => [ 'layoutArn', 'layoutId', 'name', ], 'members' => [ 'layoutArn' => [ 'shape' => 'LayoutArn', ], 'layoutId' => [ 'shape' => 'LayoutId', ], 'name' => [ 'shape' => 'LayoutName', ], ], ], 'LayoutSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayoutSummary', ], ], 'ListCasesForContactRequest' => [ 'type' => 'structure', 'required' => [ 'contactArn', 'domainId', ], 'members' => [ 'contactArn' => [ 'shape' => 'ContactArn', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'maxResults' => [ 'shape' => 'ListCasesForContactRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCasesForContactRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'ListCasesForContactResponse' => [ 'type' => 'structure', 'required' => [ 'cases', ], 'members' => [ 'cases' => [ 'shape' => 'ListCasesForContactResponseCasesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCasesForContactResponseCasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaseSummary', ], 'max' => 10, 'min' => 0, ], 'ListDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListDomainsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDomainsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'ListDomainsResponse' => [ 'type' => 'structure', 'required' => [ 'domains', ], 'members' => [ 'domains' => [ 'shape' => 'DomainSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFieldOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'fieldId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fieldId' => [ 'shape' => 'FieldId', 'location' => 'uri', 'locationName' => 'fieldId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'values' => [ 'shape' => 'ValuesList', 'location' => 'querystring', 'locationName' => 'values', ], ], ], 'ListFieldOptionsResponse' => [ 'type' => 'structure', 'required' => [ 'options', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'options' => [ 'shape' => 'FieldOptionsList', ], ], ], 'ListFieldsRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFieldsResponse' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'ListFieldsResponseFieldsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFieldsResponseFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldSummary', ], 'max' => 100, 'min' => 0, ], 'ListLayoutsRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListLayoutsResponse' => [ 'type' => 'structure', 'required' => [ 'layouts', ], 'members' => [ 'layouts' => [ 'shape' => 'LayoutSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'TemplateStatusFilters', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'templates', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templates' => [ 'shape' => 'ListTemplatesResponseTemplatesList', ], ], ], 'ListTemplatesResponseTemplatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], 'max' => 100, 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 9000, 'min' => 0, ], 'Order' => [ 'type' => 'string', 'enum' => [ 'Asc', 'Desc', ], ], 'PutCaseEventConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'eventBridge', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'eventBridge' => [ 'shape' => 'EventBridgeConfiguration', ], ], ], 'PutCaseEventConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'RelatedItemArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'RelatedItemContent' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'CommentContent', ], 'contact' => [ 'shape' => 'ContactContent', ], ], 'union' => true, ], 'RelatedItemEventIncludedData' => [ 'type' => 'structure', 'required' => [ 'includeContent', ], 'members' => [ 'includeContent' => [ 'shape' => 'Boolean', ], ], ], 'RelatedItemId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'RelatedItemInputContent' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'CommentContent', ], 'contact' => [ 'shape' => 'Contact', ], ], 'union' => true, ], 'RelatedItemType' => [ 'type' => 'string', 'enum' => [ 'Contact', 'Comment', ], ], 'RelatedItemTypeFilter' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'CommentFilter', ], 'contact' => [ 'shape' => 'ContactFilter', ], ], 'union' => true, ], 'RequiredField' => [ 'type' => 'structure', 'required' => [ 'fieldId', ], 'members' => [ 'fieldId' => [ 'shape' => 'FieldId', ], ], ], 'RequiredFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequiredField', ], 'max' => 100, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SearchCasesRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', ], 'members' => [ 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fields' => [ 'shape' => 'SearchCasesRequestFieldsList', ], 'filter' => [ 'shape' => 'CaseFilter', ], 'maxResults' => [ 'shape' => 'SearchCasesRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'searchTerm' => [ 'shape' => 'SearchCasesRequestSearchTermString', ], 'sorts' => [ 'shape' => 'SearchCasesRequestSortsList', ], ], ], 'SearchCasesRequestFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldIdentifier', ], 'max' => 10, 'min' => 0, ], 'SearchCasesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'SearchCasesRequestSearchTermString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'SearchCasesRequestSortsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sort', ], 'max' => 2, 'min' => 0, ], 'SearchCasesResponse' => [ 'type' => 'structure', 'required' => [ 'cases', ], 'members' => [ 'cases' => [ 'shape' => 'SearchCasesResponseCasesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchCasesResponseCasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchCasesResponseItem', ], 'max' => 25, 'min' => 0, ], 'SearchCasesResponseItem' => [ 'type' => 'structure', 'required' => [ 'caseId', 'fields', 'templateId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', ], 'fields' => [ 'shape' => 'SearchCasesResponseItemFieldsList', ], 'tags' => [ 'shape' => 'Tags', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'SearchCasesResponseItemFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValue', ], 'max' => 10, 'min' => 0, ], 'SearchRelatedItemsRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'domainId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'filters' => [ 'shape' => 'SearchRelatedItemsRequestFiltersList', ], 'maxResults' => [ 'shape' => 'SearchRelatedItemsRequestMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchRelatedItemsRequestFiltersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedItemTypeFilter', ], 'max' => 10, 'min' => 0, ], 'SearchRelatedItemsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'SearchRelatedItemsResponse' => [ 'type' => 'structure', 'required' => [ 'relatedItems', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'relatedItems' => [ 'shape' => 'SearchRelatedItemsResponseRelatedItemsList', ], ], ], 'SearchRelatedItemsResponseItem' => [ 'type' => 'structure', 'required' => [ 'associationTime', 'content', 'relatedItemId', 'type', ], 'members' => [ 'associationTime' => [ 'shape' => 'AssociationTime', ], 'content' => [ 'shape' => 'RelatedItemContent', ], 'performedBy' => [ 'shape' => 'UserUnion', ], 'relatedItemId' => [ 'shape' => 'RelatedItemId', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'RelatedItemType', ], ], ], 'SearchRelatedItemsResponseRelatedItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchRelatedItemsResponseItem', ], 'max' => 25, 'min' => 0, ], 'Section' => [ 'type' => 'structure', 'members' => [ 'fieldGroup' => [ 'shape' => 'FieldGroup', ], ], 'union' => true, ], 'SectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Section', ], 'max' => 1, 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Sort' => [ 'type' => 'structure', 'required' => [ 'fieldId', 'sortOrder', ], 'members' => [ 'fieldId' => [ 'shape' => 'FieldId', ], 'sortOrder' => [ 'shape' => 'Order', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TemplateArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TemplateDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'TemplateId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TemplateName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'TemplateStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'TemplateStatusFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateStatus', ], 'max' => 2, 'min' => 1, ], 'TemplateSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'status', 'templateArn', 'templateId', ], 'members' => [ 'name' => [ 'shape' => 'TemplateName', ], 'status' => [ 'shape' => 'TemplateStatus', ], 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateId' => [ 'shape' => 'TemplateId', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tagKeys', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateCaseRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'domainId', 'fields', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fields' => [ 'shape' => 'UpdateCaseRequestFieldsList', ], 'performedBy' => [ 'shape' => 'UserUnion', ], ], ], 'UpdateCaseRequestFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValue', ], 'max' => 100, 'min' => 0, ], 'UpdateCaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFieldRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'fieldId', ], 'members' => [ 'description' => [ 'shape' => 'FieldDescription', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'fieldId' => [ 'shape' => 'FieldId', 'location' => 'uri', 'locationName' => 'fieldId', ], 'name' => [ 'shape' => 'FieldName', ], ], ], 'UpdateFieldResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLayoutRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'layoutId', ], 'members' => [ 'content' => [ 'shape' => 'LayoutContent', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'layoutId' => [ 'shape' => 'LayoutId', 'location' => 'uri', 'locationName' => 'layoutId', ], 'name' => [ 'shape' => 'LayoutName', ], ], ], 'UpdateLayoutResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'domainId', 'templateId', ], 'members' => [ 'description' => [ 'shape' => 'TemplateDescription', ], 'domainId' => [ 'shape' => 'DomainId', 'location' => 'uri', 'locationName' => 'domainId', ], 'layoutConfiguration' => [ 'shape' => 'LayoutConfiguration', ], 'name' => [ 'shape' => 'TemplateName', ], 'requiredFields' => [ 'shape' => 'RequiredFieldList', ], 'status' => [ 'shape' => 'TemplateStatus', ], 'templateId' => [ 'shape' => 'TemplateId', 'location' => 'uri', 'locationName' => 'templateId', ], ], ], 'UpdateTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'UserUnion' => [ 'type' => 'structure', 'members' => [ 'userArn' => [ 'shape' => 'UserArn', ], ], 'union' => true, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], 'max' => 1, 'min' => 0, ], ],];
