<?php
// This file was auto-generated from sdk-root/src/data/securitylake/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'GetDataLakeSources' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'dataLakeSources', ], 'ListDataLakeExceptions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'exceptions', ], 'ListLogSources' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'sources', ], 'ListSubscribers' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'subscribers', ], ],];
