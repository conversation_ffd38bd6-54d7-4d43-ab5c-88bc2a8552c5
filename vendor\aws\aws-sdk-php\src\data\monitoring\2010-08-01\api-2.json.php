<?php
// This file was auto-generated from sdk-root/src/data/monitoring/2010-08-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2010-08-01', 'endpointPrefix' => 'monitoring', 'protocol' => 'query', 'serviceAbbreviation' => 'CloudWatch', 'serviceFullName' => 'Amazon CloudWatch', 'serviceId' => 'CloudWatch', 'signatureVersion' => 'v4', 'uid' => 'monitoring-2010-08-01', 'xmlNamespace' => 'http://monitoring.amazonaws.com/doc/2010-08-01/', ], 'operations' => [ 'DeleteAlarms' => [ 'name' => 'DeleteAlarms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAlarmsInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteAnomalyDetector' => [ 'name' => 'DeleteAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAnomalyDetectorInput', ], 'output' => [ 'shape' => 'DeleteAnomalyDetectorOutput', 'resultWrapper' => 'DeleteAnomalyDetectorResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteDashboards' => [ 'name' => 'DeleteDashboards', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDashboardsInput', ], 'output' => [ 'shape' => 'DeleteDashboardsOutput', 'resultWrapper' => 'DeleteDashboardsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'DashboardNotFoundError', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'DeleteInsightRules' => [ 'name' => 'DeleteInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInsightRulesInput', ], 'output' => [ 'shape' => 'DeleteInsightRulesOutput', 'resultWrapper' => 'DeleteInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'DeleteMetricStream' => [ 'name' => 'DeleteMetricStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMetricStreamInput', ], 'output' => [ 'shape' => 'DeleteMetricStreamOutput', 'resultWrapper' => 'DeleteMetricStreamResult', ], 'errors' => [ [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'DescribeAlarmHistory' => [ 'name' => 'DescribeAlarmHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlarmHistoryInput', ], 'output' => [ 'shape' => 'DescribeAlarmHistoryOutput', 'resultWrapper' => 'DescribeAlarmHistoryResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeAlarms' => [ 'name' => 'DescribeAlarms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlarmsInput', ], 'output' => [ 'shape' => 'DescribeAlarmsOutput', 'resultWrapper' => 'DescribeAlarmsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], ], ], 'DescribeAlarmsForMetric' => [ 'name' => 'DescribeAlarmsForMetric', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlarmsForMetricInput', ], 'output' => [ 'shape' => 'DescribeAlarmsForMetricOutput', 'resultWrapper' => 'DescribeAlarmsForMetricResult', ], ], 'DescribeAnomalyDetectors' => [ 'name' => 'DescribeAnomalyDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAnomalyDetectorsInput', ], 'output' => [ 'shape' => 'DescribeAnomalyDetectorsOutput', 'resultWrapper' => 'DescribeAnomalyDetectorsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeInsightRules' => [ 'name' => 'DescribeInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInsightRulesInput', ], 'output' => [ 'shape' => 'DescribeInsightRulesOutput', 'resultWrapper' => 'DescribeInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], ], ], 'DisableAlarmActions' => [ 'name' => 'DisableAlarmActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableAlarmActionsInput', ], ], 'DisableInsightRules' => [ 'name' => 'DisableInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableInsightRulesInput', ], 'output' => [ 'shape' => 'DisableInsightRulesOutput', 'resultWrapper' => 'DisableInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'EnableAlarmActions' => [ 'name' => 'EnableAlarmActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableAlarmActionsInput', ], ], 'EnableInsightRules' => [ 'name' => 'EnableInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableInsightRulesInput', ], 'output' => [ 'shape' => 'EnableInsightRulesOutput', 'resultWrapper' => 'EnableInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetDashboard' => [ 'name' => 'GetDashboard', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDashboardInput', ], 'output' => [ 'shape' => 'GetDashboardOutput', 'resultWrapper' => 'GetDashboardResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'DashboardNotFoundError', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'GetInsightRuleReport' => [ 'name' => 'GetInsightRuleReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInsightRuleReportInput', ], 'output' => [ 'shape' => 'GetInsightRuleReportOutput', 'resultWrapper' => 'GetInsightRuleReportResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMetricData' => [ 'name' => 'GetMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMetricDataInput', ], 'output' => [ 'shape' => 'GetMetricDataOutput', 'resultWrapper' => 'GetMetricDataResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], ], ], 'GetMetricStatistics' => [ 'name' => 'GetMetricStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMetricStatisticsInput', ], 'output' => [ 'shape' => 'GetMetricStatisticsOutput', 'resultWrapper' => 'GetMetricStatisticsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'GetMetricStream' => [ 'name' => 'GetMetricStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMetricStreamInput', ], 'output' => [ 'shape' => 'GetMetricStreamOutput', 'resultWrapper' => 'GetMetricStreamResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'GetMetricWidgetImage' => [ 'name' => 'GetMetricWidgetImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMetricWidgetImageInput', ], 'output' => [ 'shape' => 'GetMetricWidgetImageOutput', 'resultWrapper' => 'GetMetricWidgetImageResult', ], ], 'ListDashboards' => [ 'name' => 'ListDashboards', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDashboardsInput', ], 'output' => [ 'shape' => 'ListDashboardsOutput', 'resultWrapper' => 'ListDashboardsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'ListManagedInsightRules' => [ 'name' => 'ListManagedInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListManagedInsightRulesInput', ], 'output' => [ 'shape' => 'ListManagedInsightRulesOutput', 'resultWrapper' => 'ListManagedInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidNextToken', ], ], ], 'ListMetricStreams' => [ 'name' => 'ListMetricStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMetricStreamsInput', ], 'output' => [ 'shape' => 'ListMetricStreamsOutput', 'resultWrapper' => 'ListMetricStreamsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextToken', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'ListMetrics' => [ 'name' => 'ListMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMetricsInput', ], 'output' => [ 'shape' => 'ListMetricsOutput', 'resultWrapper' => 'ListMetricsResult', ], 'errors' => [ [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', 'resultWrapper' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'PutAnomalyDetector' => [ 'name' => 'PutAnomalyDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAnomalyDetectorInput', ], 'output' => [ 'shape' => 'PutAnomalyDetectorOutput', 'resultWrapper' => 'PutAnomalyDetectorResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'PutCompositeAlarm' => [ 'name' => 'PutCompositeAlarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutCompositeAlarmInput', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], ], ], 'PutDashboard' => [ 'name' => 'PutDashboard', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDashboardInput', ], 'output' => [ 'shape' => 'PutDashboardOutput', 'resultWrapper' => 'PutDashboardResult', ], 'errors' => [ [ 'shape' => 'DashboardInvalidInputError', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'PutInsightRule' => [ 'name' => 'PutInsightRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInsightRuleInput', ], 'output' => [ 'shape' => 'PutInsightRuleOutput', 'resultWrapper' => 'PutInsightRuleResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'PutManagedInsightRules' => [ 'name' => 'PutManagedInsightRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutManagedInsightRulesInput', ], 'output' => [ 'shape' => 'PutManagedInsightRulesOutput', 'resultWrapper' => 'PutManagedInsightRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'PutMetricAlarm' => [ 'name' => 'PutMetricAlarm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMetricAlarmInput', ], 'errors' => [ [ 'shape' => 'LimitExceededFault', ], ], ], 'PutMetricData' => [ 'name' => 'PutMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMetricDataInput', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InternalServiceFault', ], ], 'requestcompression' => [ 'encodings' => [ 'gzip', ], ], ], 'PutMetricStream' => [ 'name' => 'PutMetricStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMetricStreamInput', ], 'output' => [ 'shape' => 'PutMetricStreamOutput', 'resultWrapper' => 'PutMetricStreamResult', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'SetAlarmState' => [ 'name' => 'SetAlarmState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetAlarmStateInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'InvalidFormatFault', ], ], ], 'StartMetricStreams' => [ 'name' => 'StartMetricStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMetricStreamsInput', ], 'output' => [ 'shape' => 'StartMetricStreamsOutput', 'resultWrapper' => 'StartMetricStreamsResult', ], 'errors' => [ [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'StopMetricStreams' => [ 'name' => 'StopMetricStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopMetricStreamsInput', ], 'output' => [ 'shape' => 'StopMetricStreamsOutput', 'resultWrapper' => 'StopMetricStreamsResult', ], 'errors' => [ [ 'shape' => 'InternalServiceFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingRequiredParameterException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', 'resultWrapper' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceFault', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', 'resultWrapper' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceFault', ], ], ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ActionPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ActionsEnabled' => [ 'type' => 'boolean', ], 'ActionsSuppressedBy' => [ 'type' => 'string', 'enum' => [ 'WaitPeriod', 'ExtensionPeriod', 'Alarm', ], ], 'ActionsSuppressedReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'AlarmArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'AlarmDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'AlarmHistoryItem' => [ 'type' => 'structure', 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmType' => [ 'shape' => 'AlarmType', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'HistoryItemType' => [ 'shape' => 'HistoryItemType', ], 'HistorySummary' => [ 'shape' => 'HistorySummary', ], 'HistoryData' => [ 'shape' => 'HistoryData', ], ], ], 'AlarmHistoryItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmHistoryItem', ], ], 'AlarmName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'AlarmNamePrefix' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'AlarmNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmName', ], 'max' => 100, ], 'AlarmRule' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'AlarmType' => [ 'type' => 'string', 'enum' => [ 'CompositeAlarm', 'MetricAlarm', ], ], 'AlarmTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmType', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'AnomalyDetector' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.Namespace property.', ], 'MetricName' => [ 'shape' => 'MetricName', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.MetricName property.', ], 'Dimensions' => [ 'shape' => 'Dimensions', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.Dimensions property.', ], 'Stat' => [ 'shape' => 'AnomalyDetectorMetricStat', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.Stat property.', ], 'Configuration' => [ 'shape' => 'AnomalyDetectorConfiguration', ], 'StateValue' => [ 'shape' => 'AnomalyDetectorStateValue', ], 'MetricCharacteristics' => [ 'shape' => 'MetricCharacteristics', ], 'SingleMetricAnomalyDetector' => [ 'shape' => 'SingleMetricAnomalyDetector', ], 'MetricMathAnomalyDetector' => [ 'shape' => 'MetricMathAnomalyDetector', ], ], ], 'AnomalyDetectorConfiguration' => [ 'type' => 'structure', 'members' => [ 'ExcludedTimeRanges' => [ 'shape' => 'AnomalyDetectorExcludedTimeRanges', ], 'MetricTimezone' => [ 'shape' => 'AnomalyDetectorMetricTimezone', ], ], ], 'AnomalyDetectorExcludedTimeRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', ], ], 'AnomalyDetectorMetricStat' => [ 'type' => 'string', 'max' => 50, 'pattern' => '(SampleCount|Average|Sum|Minimum|Maximum|IQM|(p|tc|tm|ts|wm)(\\d{1,2}(\\.\\d{0,10})?|100)|[ou]\\d+(\\.\\d*)?)(_E|_L|_H)?|(TM|TC|TS|WM)\\(((((\\d{1,2})(\\.\\d{0,10})?|100(\\.0{0,10})?)%)?:((\\d{1,2})(\\.\\d{0,10})?|100(\\.0{0,10})?)%|((\\d{1,2})(\\.\\d{0,10})?|100(\\.0{0,10})?)%:(((\\d{1,2})(\\.\\d{0,10})?|100(\\.0{0,10})?)%)?)\\)|(TM|TC|TS|WM|PR)\\(((\\d+(\\.\\d{0,10})?|(\\d+(\\.\\d{0,10})?[Ee][+-]?\\d+)):((\\d+(\\.\\d{0,10})?|(\\d+(\\.\\d{0,10})?[Ee][+-]?\\d+)))?|((\\d+(\\.\\d{0,10})?|(\\d+(\\.\\d{0,10})?[Ee][+-]?\\d+)))?:(\\d+(\\.\\d{0,10})?|(\\d+(\\.\\d{0,10})?[Ee][+-]?\\d+)))\\)', ], 'AnomalyDetectorMetricTimezone' => [ 'type' => 'string', 'max' => 50, 'pattern' => '.*', ], 'AnomalyDetectorStateValue' => [ 'type' => 'string', 'enum' => [ 'PENDING_TRAINING', 'TRAINED_INSUFFICIENT_DATA', 'TRAINED', ], ], 'AnomalyDetectorType' => [ 'type' => 'string', 'enum' => [ 'SINGLE_METRIC', 'METRIC_MATH', ], ], 'AnomalyDetectorTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyDetectorType', ], 'max' => 2, ], 'AnomalyDetectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyDetector', ], ], 'AwsQueryErrorMessage' => [ 'type' => 'string', ], 'BatchFailures' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartialFailure', ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'GreaterThanOrEqualToThreshold', 'GreaterThanThreshold', 'LessThanThreshold', 'LessThanOrEqualToThreshold', 'LessThanLowerOrGreaterThanUpperThreshold', 'LessThanLowerThreshold', 'GreaterThanUpperThreshold', ], ], 'CompositeAlarm' => [ 'type' => 'structure', 'members' => [ 'ActionsEnabled' => [ 'shape' => 'ActionsEnabled', ], 'AlarmActions' => [ 'shape' => 'ResourceList', ], 'AlarmArn' => [ 'shape' => 'AlarmArn', ], 'AlarmConfigurationUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'AlarmDescription' => [ 'shape' => 'AlarmDescription', ], 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmRule' => [ 'shape' => 'AlarmRule', ], 'InsufficientDataActions' => [ 'shape' => 'ResourceList', ], 'OKActions' => [ 'shape' => 'ResourceList', ], 'StateReason' => [ 'shape' => 'StateReason', ], 'StateReasonData' => [ 'shape' => 'StateReasonData', ], 'StateUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'StateValue' => [ 'shape' => 'StateValue', ], 'StateTransitionedTimestamp' => [ 'shape' => 'Timestamp', ], 'ActionsSuppressedBy' => [ 'shape' => 'ActionsSuppressedBy', ], 'ActionsSuppressedReason' => [ 'shape' => 'ActionsSuppressedReason', ], 'ActionsSuppressor' => [ 'shape' => 'AlarmArn', ], 'ActionsSuppressorWaitPeriod' => [ 'shape' => 'SuppressorPeriod', ], 'ActionsSuppressorExtensionPeriod' => [ 'shape' => 'SuppressorPeriod', ], ], 'xmlOrder' => [ 'ActionsEnabled', 'AlarmActions', 'AlarmArn', 'AlarmConfigurationUpdatedTimestamp', 'AlarmDescription', 'AlarmName', 'AlarmRule', 'InsufficientDataActions', 'OKActions', 'StateReason', 'StateReasonData', 'StateUpdatedTimestamp', 'StateValue', 'StateTransitionedTimestamp', 'ActionsSuppressedBy', 'ActionsSuppressedReason', 'ActionsSuppressor', 'ActionsSuppressorWaitPeriod', 'ActionsSuppressorExtensionPeriod', ], ], 'CompositeAlarms' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositeAlarm', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ConcurrentModificationException', 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Counts' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatapointValue', ], ], 'DashboardArn' => [ 'type' => 'string', ], 'DashboardBody' => [ 'type' => 'string', ], 'DashboardEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardEntry', ], ], 'DashboardEntry' => [ 'type' => 'structure', 'members' => [ 'DashboardName' => [ 'shape' => 'DashboardName', ], 'DashboardArn' => [ 'shape' => 'DashboardArn', ], 'LastModified' => [ 'shape' => 'LastModified', ], 'Size' => [ 'shape' => 'Size', ], ], ], 'DashboardErrorMessage' => [ 'type' => 'string', ], 'DashboardInvalidInputError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'DashboardErrorMessage', ], 'dashboardValidationMessages' => [ 'shape' => 'DashboardValidationMessages', ], ], 'error' => [ 'code' => 'InvalidParameterInput', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DashboardName' => [ 'type' => 'string', ], 'DashboardNamePrefix' => [ 'type' => 'string', ], 'DashboardNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardName', ], ], 'DashboardNotFoundError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'DashboardErrorMessage', ], ], 'error' => [ 'code' => 'ResourceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DashboardValidationMessage' => [ 'type' => 'structure', 'members' => [ 'DataPath' => [ 'shape' => 'DataPath', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'DashboardValidationMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardValidationMessage', ], ], 'DataPath' => [ 'type' => 'string', ], 'Datapoint' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'SampleCount' => [ 'shape' => 'DatapointValue', ], 'Average' => [ 'shape' => 'DatapointValue', ], 'Sum' => [ 'shape' => 'DatapointValue', ], 'Minimum' => [ 'shape' => 'DatapointValue', ], 'Maximum' => [ 'shape' => 'DatapointValue', ], 'Unit' => [ 'shape' => 'StandardUnit', ], 'ExtendedStatistics' => [ 'shape' => 'DatapointValueMap', ], ], 'xmlOrder' => [ 'Timestamp', 'SampleCount', 'Average', 'Sum', 'Minimum', 'Maximum', 'Unit', 'ExtendedStatistics', ], ], 'DatapointValue' => [ 'type' => 'double', ], 'DatapointValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExtendedStatistic', ], 'value' => [ 'shape' => 'DatapointValue', ], ], 'DatapointValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatapointValue', ], ], 'Datapoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'Datapoint', ], ], 'DatapointsToAlarm' => [ 'type' => 'integer', 'min' => 1, ], 'DeleteAlarmsInput' => [ 'type' => 'structure', 'required' => [ 'AlarmNames', ], 'members' => [ 'AlarmNames' => [ 'shape' => 'AlarmNames', ], ], ], 'DeleteAnomalyDetectorInput' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'MetricName' => [ 'shape' => 'MetricName', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'Dimensions' => [ 'shape' => 'Dimensions', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'Stat' => [ 'shape' => 'AnomalyDetectorMetricStat', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'SingleMetricAnomalyDetector' => [ 'shape' => 'SingleMetricAnomalyDetector', ], 'MetricMathAnomalyDetector' => [ 'shape' => 'MetricMathAnomalyDetector', ], ], ], 'DeleteAnomalyDetectorOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDashboardsInput' => [ 'type' => 'structure', 'required' => [ 'DashboardNames', ], 'members' => [ 'DashboardNames' => [ 'shape' => 'DashboardNames', ], ], ], 'DeleteDashboardsOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInsightRulesInput' => [ 'type' => 'structure', 'required' => [ 'RuleNames', ], 'members' => [ 'RuleNames' => [ 'shape' => 'InsightRuleNames', ], ], ], 'DeleteInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchFailures', ], ], ], 'DeleteMetricStreamInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MetricStreamName', ], ], ], 'DeleteMetricStreamOutput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAlarmHistoryInput' => [ 'type' => 'structure', 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmTypes' => [ 'shape' => 'AlarmTypes', ], 'HistoryItemType' => [ 'shape' => 'HistoryItemType', ], 'StartDate' => [ 'shape' => 'Timestamp', ], 'EndDate' => [ 'shape' => 'Timestamp', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ScanBy' => [ 'shape' => 'ScanBy', ], ], ], 'DescribeAlarmHistoryOutput' => [ 'type' => 'structure', 'members' => [ 'AlarmHistoryItems' => [ 'shape' => 'AlarmHistoryItems', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAlarmsForMetricInput' => [ 'type' => 'structure', 'required' => [ 'MetricName', 'Namespace', ], 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'ExtendedStatistic' => [ 'shape' => 'ExtendedStatistic', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Period' => [ 'shape' => 'Period', ], 'Unit' => [ 'shape' => 'StandardUnit', ], ], ], 'DescribeAlarmsForMetricOutput' => [ 'type' => 'structure', 'members' => [ 'MetricAlarms' => [ 'shape' => 'MetricAlarms', ], ], ], 'DescribeAlarmsInput' => [ 'type' => 'structure', 'members' => [ 'AlarmNames' => [ 'shape' => 'AlarmNames', ], 'AlarmNamePrefix' => [ 'shape' => 'AlarmNamePrefix', ], 'AlarmTypes' => [ 'shape' => 'AlarmTypes', ], 'ChildrenOfAlarmName' => [ 'shape' => 'AlarmName', ], 'ParentsOfAlarmName' => [ 'shape' => 'AlarmName', ], 'StateValue' => [ 'shape' => 'StateValue', ], 'ActionPrefix' => [ 'shape' => 'ActionPrefix', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAlarmsOutput' => [ 'type' => 'structure', 'members' => [ 'CompositeAlarms' => [ 'shape' => 'CompositeAlarms', ], 'MetricAlarms' => [ 'shape' => 'MetricAlarms', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAnomalyDetectorsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxReturnedResultsCount', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'AnomalyDetectorTypes' => [ 'shape' => 'AnomalyDetectorTypes', ], ], ], 'DescribeAnomalyDetectorsOutput' => [ 'type' => 'structure', 'members' => [ 'AnomalyDetectors' => [ 'shape' => 'AnomalyDetectors', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInsightRulesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'InsightRuleMaxResults', ], ], ], 'DescribeInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'InsightRules' => [ 'shape' => 'InsightRules', ], ], ], 'Dimension' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'DimensionName', ], 'Value' => [ 'shape' => 'DimensionValue', ], ], 'xmlOrder' => [ 'Name', 'Value', ], ], 'DimensionFilter' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DimensionName', ], 'Value' => [ 'shape' => 'DimensionValue', ], ], ], 'DimensionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionFilter', ], 'max' => 10, ], 'DimensionName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DimensionValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Dimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dimension', ], 'max' => 30, ], 'DisableAlarmActionsInput' => [ 'type' => 'structure', 'required' => [ 'AlarmNames', ], 'members' => [ 'AlarmNames' => [ 'shape' => 'AlarmNames', ], ], ], 'DisableInsightRulesInput' => [ 'type' => 'structure', 'required' => [ 'RuleNames', ], 'members' => [ 'RuleNames' => [ 'shape' => 'InsightRuleNames', ], ], ], 'DisableInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchFailures', ], ], ], 'EnableAlarmActionsInput' => [ 'type' => 'structure', 'required' => [ 'AlarmNames', ], 'members' => [ 'AlarmNames' => [ 'shape' => 'AlarmNames', ], ], ], 'EnableInsightRulesInput' => [ 'type' => 'structure', 'required' => [ 'RuleNames', ], 'members' => [ 'RuleNames' => [ 'shape' => 'InsightRuleNames', ], ], ], 'EnableInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchFailures', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EvaluateLowSampleCountPercentile' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EvaluationPeriods' => [ 'type' => 'integer', 'min' => 1, ], 'EvaluationState' => [ 'type' => 'string', 'enum' => [ 'PARTIAL_DATA', ], ], 'ExceptionType' => [ 'type' => 'string', ], 'ExtendedStatistic' => [ 'type' => 'string', ], 'ExtendedStatistics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExtendedStatistic', ], 'max' => 10, 'min' => 1, ], 'FailureCode' => [ 'type' => 'string', ], 'FailureDescription' => [ 'type' => 'string', ], 'FailureResource' => [ 'type' => 'string', ], 'FaultDescription' => [ 'type' => 'string', ], 'GetDashboardInput' => [ 'type' => 'structure', 'required' => [ 'DashboardName', ], 'members' => [ 'DashboardName' => [ 'shape' => 'DashboardName', ], ], ], 'GetDashboardOutput' => [ 'type' => 'structure', 'members' => [ 'DashboardArn' => [ 'shape' => 'DashboardArn', ], 'DashboardBody' => [ 'shape' => 'DashboardBody', ], 'DashboardName' => [ 'shape' => 'DashboardName', ], ], ], 'GetInsightRuleReportInput' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'StartTime', 'EndTime', 'Period', ], 'members' => [ 'RuleName' => [ 'shape' => 'InsightRuleName', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Period' => [ 'shape' => 'Period', ], 'MaxContributorCount' => [ 'shape' => 'InsightRuleUnboundInteger', ], 'Metrics' => [ 'shape' => 'InsightRuleMetricList', ], 'OrderBy' => [ 'shape' => 'InsightRuleOrderBy', ], ], ], 'GetInsightRuleReportOutput' => [ 'type' => 'structure', 'members' => [ 'KeyLabels' => [ 'shape' => 'InsightRuleContributorKeyLabels', ], 'AggregationStatistic' => [ 'shape' => 'InsightRuleAggregationStatistic', ], 'AggregateValue' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'ApproximateUniqueCount' => [ 'shape' => 'InsightRuleUnboundLong', ], 'Contributors' => [ 'shape' => 'InsightRuleContributors', ], 'MetricDatapoints' => [ 'shape' => 'InsightRuleMetricDatapoints', ], ], ], 'GetMetricDataInput' => [ 'type' => 'structure', 'required' => [ 'MetricDataQueries', 'StartTime', 'EndTime', ], 'members' => [ 'MetricDataQueries' => [ 'shape' => 'MetricDataQueries', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ScanBy' => [ 'shape' => 'ScanBy', ], 'MaxDatapoints' => [ 'shape' => 'GetMetricDataMaxDatapoints', ], 'LabelOptions' => [ 'shape' => 'LabelOptions', ], ], ], 'GetMetricDataLabelTimezone' => [ 'type' => 'string', ], 'GetMetricDataMaxDatapoints' => [ 'type' => 'integer', ], 'GetMetricDataOutput' => [ 'type' => 'structure', 'members' => [ 'MetricDataResults' => [ 'shape' => 'MetricDataResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Messages' => [ 'shape' => 'MetricDataResultMessages', ], ], ], 'GetMetricStatisticsInput' => [ 'type' => 'structure', 'required' => [ 'Namespace', 'MetricName', 'StartTime', 'EndTime', 'Period', ], 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Period' => [ 'shape' => 'Period', ], 'Statistics' => [ 'shape' => 'Statistics', ], 'ExtendedStatistics' => [ 'shape' => 'ExtendedStatistics', ], 'Unit' => [ 'shape' => 'StandardUnit', ], ], ], 'GetMetricStatisticsOutput' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'MetricLabel', ], 'Datapoints' => [ 'shape' => 'Datapoints', ], ], ], 'GetMetricStreamInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MetricStreamName', ], ], ], 'GetMetricStreamOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'AmazonResourceName', ], 'Name' => [ 'shape' => 'MetricStreamName', ], 'IncludeFilters' => [ 'shape' => 'MetricStreamFilters', ], 'ExcludeFilters' => [ 'shape' => 'MetricStreamFilters', ], 'FirehoseArn' => [ 'shape' => 'AmazonResourceName', ], 'RoleArn' => [ 'shape' => 'AmazonResourceName', ], 'State' => [ 'shape' => 'MetricStreamState', ], 'CreationDate' => [ 'shape' => 'Timestamp', ], 'LastUpdateDate' => [ 'shape' => 'Timestamp', ], 'OutputFormat' => [ 'shape' => 'MetricStreamOutputFormat', ], 'StatisticsConfigurations' => [ 'shape' => 'MetricStreamStatisticsConfigurations', ], 'IncludeLinkedAccountsMetrics' => [ 'shape' => 'IncludeLinkedAccountsMetrics', ], ], ], 'GetMetricWidgetImageInput' => [ 'type' => 'structure', 'required' => [ 'MetricWidget', ], 'members' => [ 'MetricWidget' => [ 'shape' => 'MetricWidget', ], 'OutputFormat' => [ 'shape' => 'OutputFormat', ], ], ], 'GetMetricWidgetImageOutput' => [ 'type' => 'structure', 'members' => [ 'MetricWidgetImage' => [ 'shape' => 'MetricWidgetImage', ], ], ], 'HistoryData' => [ 'type' => 'string', 'max' => 4095, 'min' => 1, ], 'HistoryItemType' => [ 'type' => 'string', 'enum' => [ 'ConfigurationUpdate', 'StateUpdate', 'Action', ], ], 'HistorySummary' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'IncludeLinkedAccounts' => [ 'type' => 'boolean', ], 'IncludeLinkedAccountsMetrics' => [ 'type' => 'boolean', ], 'InsightRule' => [ 'type' => 'structure', 'required' => [ 'Name', 'State', 'Schema', 'Definition', ], 'members' => [ 'Name' => [ 'shape' => 'InsightRuleName', ], 'State' => [ 'shape' => 'InsightRuleState', ], 'Schema' => [ 'shape' => 'InsightRuleSchema', ], 'Definition' => [ 'shape' => 'InsightRuleDefinition', ], 'ManagedRule' => [ 'shape' => 'InsightRuleIsManaged', ], ], ], 'InsightRuleAggregationStatistic' => [ 'type' => 'string', ], 'InsightRuleContributor' => [ 'type' => 'structure', 'required' => [ 'Keys', 'ApproximateAggregateValue', 'Datapoints', ], 'members' => [ 'Keys' => [ 'shape' => 'InsightRuleContributorKeys', ], 'ApproximateAggregateValue' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'Datapoints' => [ 'shape' => 'InsightRuleContributorDatapoints', ], ], ], 'InsightRuleContributorDatapoint' => [ 'type' => 'structure', 'required' => [ 'Timestamp', 'ApproximateValue', ], 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ApproximateValue' => [ 'shape' => 'InsightRuleUnboundDouble', ], ], ], 'InsightRuleContributorDatapoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleContributorDatapoint', ], ], 'InsightRuleContributorKey' => [ 'type' => 'string', ], 'InsightRuleContributorKeyLabel' => [ 'type' => 'string', ], 'InsightRuleContributorKeyLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleContributorKeyLabel', ], ], 'InsightRuleContributorKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleContributorKey', ], ], 'InsightRuleContributors' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleContributor', ], ], 'InsightRuleDefinition' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '[\\x00-\\x7F]+', ], 'InsightRuleIsManaged' => [ 'type' => 'boolean', ], 'InsightRuleMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'InsightRuleMetricDatapoint' => [ 'type' => 'structure', 'required' => [ 'Timestamp', ], 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'UniqueContributors' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'MaxContributorValue' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'SampleCount' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'Average' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'Sum' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'Minimum' => [ 'shape' => 'InsightRuleUnboundDouble', ], 'Maximum' => [ 'shape' => 'InsightRuleUnboundDouble', ], ], ], 'InsightRuleMetricDatapoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleMetricDatapoint', ], ], 'InsightRuleMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleMetricName', ], ], 'InsightRuleMetricName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\x20-\\x7E]+', ], 'InsightRuleName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\x20-\\x7E]+', ], 'InsightRuleNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRuleName', ], ], 'InsightRuleOrderBy' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\x20-\\x7E]+', ], 'InsightRuleSchema' => [ 'type' => 'string', ], 'InsightRuleState' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\x20-\\x7E]+', ], 'InsightRuleUnboundDouble' => [ 'type' => 'double', ], 'InsightRuleUnboundInteger' => [ 'type' => 'integer', ], 'InsightRuleUnboundLong' => [ 'type' => 'long', ], 'InsightRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightRule', ], ], 'InternalServiceFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FaultDescription', ], ], 'error' => [ 'code' => 'InternalServiceError', 'httpStatusCode' => 500, ], 'exception' => true, 'xmlOrder' => [ 'Message', ], ], 'InvalidFormatFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'code' => 'InvalidFormat', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidNextToken' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'code' => 'InvalidNextToken', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'error' => [ 'code' => 'InvalidParameterCombination', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'synthetic' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'error' => [ 'code' => 'InvalidParameterValue', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'synthetic' => true, ], 'LabelOptions' => [ 'type' => 'structure', 'members' => [ 'Timezone' => [ 'shape' => 'GetMetricDataLabelTimezone', ], ], ], 'LastModified' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'LimitExceededException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LimitExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'code' => 'LimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ListDashboardsInput' => [ 'type' => 'structure', 'members' => [ 'DashboardNamePrefix' => [ 'shape' => 'DashboardNamePrefix', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDashboardsOutput' => [ 'type' => 'structure', 'members' => [ 'DashboardEntries' => [ 'shape' => 'DashboardEntries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagedInsightRulesInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'InsightRuleMaxResults', ], ], ], 'ListManagedInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'ManagedRules' => [ 'shape' => 'ManagedRuleDescriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricStreamsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListMetricStreamsMaxResults', ], ], ], 'ListMetricStreamsMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'ListMetricStreamsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Entries' => [ 'shape' => 'MetricStreamEntries', ], ], ], 'ListMetricsInput' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'DimensionFilters', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'RecentlyActive' => [ 'shape' => 'RecentlyActive', ], 'IncludeLinkedAccounts' => [ 'shape' => 'IncludeLinkedAccounts', ], 'OwningAccount' => [ 'shape' => 'AccountId', ], ], ], 'ListMetricsOutput' => [ 'type' => 'structure', 'members' => [ 'Metrics' => [ 'shape' => 'Metrics', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'OwningAccounts' => [ 'shape' => 'OwningAccounts', ], ], 'xmlOrder' => [ 'Metrics', 'NextToken', 'OwningAccounts', ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ManagedRule' => [ 'type' => 'structure', 'required' => [ 'TemplateName', 'ResourceARN', ], 'members' => [ 'TemplateName' => [ 'shape' => 'TemplateName', ], 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ManagedRuleDescription' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => 'TemplateName', ], 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'RuleState' => [ 'shape' => 'ManagedRuleState', ], ], ], 'ManagedRuleDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleDescription', ], ], 'ManagedRuleState' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'State', ], 'members' => [ 'RuleName' => [ 'shape' => 'InsightRuleName', ], 'State' => [ 'shape' => 'InsightRuleState', ], ], ], 'ManagedRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRule', ], ], 'MaxRecords' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxReturnedResultsCount' => [ 'type' => 'integer', 'min' => 1, ], 'Message' => [ 'type' => 'string', ], 'MessageData' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'MessageDataCode', ], 'Value' => [ 'shape' => 'MessageDataValue', ], ], ], 'MessageDataCode' => [ 'type' => 'string', ], 'MessageDataValue' => [ 'type' => 'string', ], 'Metric' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], ], 'xmlOrder' => [ 'Namespace', 'MetricName', 'Dimensions', ], ], 'MetricAlarm' => [ 'type' => 'structure', 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmArn' => [ 'shape' => 'AlarmArn', ], 'AlarmDescription' => [ 'shape' => 'AlarmDescription', ], 'AlarmConfigurationUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ActionsEnabled' => [ 'shape' => 'ActionsEnabled', ], 'OKActions' => [ 'shape' => 'ResourceList', ], 'AlarmActions' => [ 'shape' => 'ResourceList', ], 'InsufficientDataActions' => [ 'shape' => 'ResourceList', ], 'StateValue' => [ 'shape' => 'StateValue', ], 'StateReason' => [ 'shape' => 'StateReason', ], 'StateReasonData' => [ 'shape' => 'StateReasonData', ], 'StateUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'ExtendedStatistic' => [ 'shape' => 'ExtendedStatistic', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Period' => [ 'shape' => 'Period', ], 'Unit' => [ 'shape' => 'StandardUnit', ], 'EvaluationPeriods' => [ 'shape' => 'EvaluationPeriods', ], 'DatapointsToAlarm' => [ 'shape' => 'DatapointsToAlarm', ], 'Threshold' => [ 'shape' => 'Threshold', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'TreatMissingData' => [ 'shape' => 'TreatMissingData', ], 'EvaluateLowSampleCountPercentile' => [ 'shape' => 'EvaluateLowSampleCountPercentile', ], 'Metrics' => [ 'shape' => 'MetricDataQueries', ], 'ThresholdMetricId' => [ 'shape' => 'MetricId', ], 'EvaluationState' => [ 'shape' => 'EvaluationState', ], 'StateTransitionedTimestamp' => [ 'shape' => 'Timestamp', ], ], 'xmlOrder' => [ 'AlarmName', 'AlarmArn', 'AlarmDescription', 'AlarmConfigurationUpdatedTimestamp', 'ActionsEnabled', 'OKActions', 'AlarmActions', 'InsufficientDataActions', 'StateValue', 'StateReason', 'StateReasonData', 'StateUpdatedTimestamp', 'MetricName', 'Namespace', 'Statistic', 'Dimensions', 'Period', 'Unit', 'EvaluationPeriods', 'Threshold', 'ComparisonOperator', 'ExtendedStatistic', 'TreatMissingData', 'EvaluateLowSampleCountPercentile', 'DatapointsToAlarm', 'Metrics', 'ThresholdMetricId', 'EvaluationState', 'StateTransitionedTimestamp', ], ], 'MetricAlarms' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricAlarm', ], ], 'MetricCharacteristics' => [ 'type' => 'structure', 'members' => [ 'PeriodicSpikes' => [ 'shape' => 'PeriodicSpikes', ], ], ], 'MetricData' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDatum', ], ], 'MetricDataQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataQuery', ], ], 'MetricDataQuery' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'MetricId', ], 'MetricStat' => [ 'shape' => 'MetricStat', ], 'Expression' => [ 'shape' => 'MetricExpression', ], 'Label' => [ 'shape' => 'MetricLabel', ], 'ReturnData' => [ 'shape' => 'ReturnData', ], 'Period' => [ 'shape' => 'Period', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'MetricDataResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'MetricId', ], 'Label' => [ 'shape' => 'MetricLabel', ], 'Timestamps' => [ 'shape' => 'Timestamps', ], 'Values' => [ 'shape' => 'DatapointValues', ], 'StatusCode' => [ 'shape' => 'StatusCode', ], 'Messages' => [ 'shape' => 'MetricDataResultMessages', ], ], ], 'MetricDataResultMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageData', ], ], 'MetricDataResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataResult', ], ], 'MetricDatum' => [ 'type' => 'structure', 'required' => [ 'MetricName', ], 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Value' => [ 'shape' => 'DatapointValue', ], 'StatisticValues' => [ 'shape' => 'StatisticSet', ], 'Values' => [ 'shape' => 'Values', ], 'Counts' => [ 'shape' => 'Counts', ], 'Unit' => [ 'shape' => 'StandardUnit', ], 'StorageResolution' => [ 'shape' => 'StorageResolution', ], ], ], 'MetricExpression' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'MetricId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'MetricLabel' => [ 'type' => 'string', ], 'MetricMathAnomalyDetector' => [ 'type' => 'structure', 'members' => [ 'MetricDataQueries' => [ 'shape' => 'MetricDataQueries', ], ], ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'MetricStat' => [ 'type' => 'structure', 'required' => [ 'Metric', 'Period', 'Stat', ], 'members' => [ 'Metric' => [ 'shape' => 'Metric', ], 'Period' => [ 'shape' => 'Period', ], 'Stat' => [ 'shape' => 'Stat', ], 'Unit' => [ 'shape' => 'StandardUnit', ], ], ], 'MetricStreamEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamEntry', ], ], 'MetricStreamEntry' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'AmazonResourceName', ], 'CreationDate' => [ 'shape' => 'Timestamp', ], 'LastUpdateDate' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'MetricStreamName', ], 'FirehoseArn' => [ 'shape' => 'AmazonResourceName', ], 'State' => [ 'shape' => 'MetricStreamState', ], 'OutputFormat' => [ 'shape' => 'MetricStreamOutputFormat', ], ], ], 'MetricStreamFilter' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricNames' => [ 'shape' => 'MetricStreamFilterMetricNames', ], ], ], 'MetricStreamFilterMetricNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricName', ], ], 'MetricStreamFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamFilter', ], ], 'MetricStreamName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'MetricStreamNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamName', ], ], 'MetricStreamOutputFormat' => [ 'type' => 'string', 'enum' => [ 'json', 'opentelemetry0.7', 'opentelemetry1.0', ], 'max' => 255, 'min' => 1, ], 'MetricStreamState' => [ 'type' => 'string', ], 'MetricStreamStatistic' => [ 'type' => 'string', ], 'MetricStreamStatisticsAdditionalStatistics' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamStatistic', ], ], 'MetricStreamStatisticsConfiguration' => [ 'type' => 'structure', 'required' => [ 'IncludeMetrics', 'AdditionalStatistics', ], 'members' => [ 'IncludeMetrics' => [ 'shape' => 'MetricStreamStatisticsIncludeMetrics', ], 'AdditionalStatistics' => [ 'shape' => 'MetricStreamStatisticsAdditionalStatistics', ], ], ], 'MetricStreamStatisticsConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamStatisticsConfiguration', ], ], 'MetricStreamStatisticsIncludeMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStreamStatisticsMetric', ], ], 'MetricStreamStatisticsMetric' => [ 'type' => 'structure', 'required' => [ 'Namespace', 'MetricName', ], 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], ], ], 'MetricWidget' => [ 'type' => 'string', ], 'MetricWidgetImage' => [ 'type' => 'blob', ], 'Metrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'Metric', ], ], 'MissingRequiredParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'error' => [ 'code' => 'MissingParameter', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'synthetic' => true, ], 'Namespace' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^:].*', ], 'NextToken' => [ 'type' => 'string', ], 'OutputFormat' => [ 'type' => 'string', ], 'OwningAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'PartialFailure' => [ 'type' => 'structure', 'members' => [ 'FailureResource' => [ 'shape' => 'FailureResource', ], 'ExceptionType' => [ 'shape' => 'ExceptionType', ], 'FailureCode' => [ 'shape' => 'FailureCode', ], 'FailureDescription' => [ 'shape' => 'FailureDescription', ], ], ], 'Period' => [ 'type' => 'integer', 'min' => 1, ], 'PeriodicSpikes' => [ 'type' => 'boolean', ], 'PutAnomalyDetectorInput' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'MetricName' => [ 'shape' => 'MetricName', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'Dimensions' => [ 'shape' => 'Dimensions', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'Stat' => [ 'shape' => 'AnomalyDetectorMetricStat', 'deprecated' => true, 'deprecatedMessage' => 'Use SingleMetricAnomalyDetector.', ], 'Configuration' => [ 'shape' => 'AnomalyDetectorConfiguration', ], 'MetricCharacteristics' => [ 'shape' => 'MetricCharacteristics', ], 'SingleMetricAnomalyDetector' => [ 'shape' => 'SingleMetricAnomalyDetector', ], 'MetricMathAnomalyDetector' => [ 'shape' => 'MetricMathAnomalyDetector', ], ], ], 'PutAnomalyDetectorOutput' => [ 'type' => 'structure', 'members' => [], ], 'PutCompositeAlarmInput' => [ 'type' => 'structure', 'required' => [ 'AlarmName', 'AlarmRule', ], 'members' => [ 'ActionsEnabled' => [ 'shape' => 'ActionsEnabled', ], 'AlarmActions' => [ 'shape' => 'ResourceList', ], 'AlarmDescription' => [ 'shape' => 'AlarmDescription', ], 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmRule' => [ 'shape' => 'AlarmRule', ], 'InsufficientDataActions' => [ 'shape' => 'ResourceList', ], 'OKActions' => [ 'shape' => 'ResourceList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ActionsSuppressor' => [ 'shape' => 'AlarmArn', ], 'ActionsSuppressorWaitPeriod' => [ 'shape' => 'SuppressorPeriod', ], 'ActionsSuppressorExtensionPeriod' => [ 'shape' => 'SuppressorPeriod', ], ], ], 'PutDashboardInput' => [ 'type' => 'structure', 'required' => [ 'DashboardName', 'DashboardBody', ], 'members' => [ 'DashboardName' => [ 'shape' => 'DashboardName', ], 'DashboardBody' => [ 'shape' => 'DashboardBody', ], ], ], 'PutDashboardOutput' => [ 'type' => 'structure', 'members' => [ 'DashboardValidationMessages' => [ 'shape' => 'DashboardValidationMessages', ], ], ], 'PutInsightRuleInput' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'RuleDefinition', ], 'members' => [ 'RuleName' => [ 'shape' => 'InsightRuleName', ], 'RuleState' => [ 'shape' => 'InsightRuleState', ], 'RuleDefinition' => [ 'shape' => 'InsightRuleDefinition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PutInsightRuleOutput' => [ 'type' => 'structure', 'members' => [], ], 'PutManagedInsightRulesInput' => [ 'type' => 'structure', 'required' => [ 'ManagedRules', ], 'members' => [ 'ManagedRules' => [ 'shape' => 'ManagedRules', ], ], ], 'PutManagedInsightRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchFailures', ], ], ], 'PutMetricAlarmInput' => [ 'type' => 'structure', 'required' => [ 'AlarmName', 'EvaluationPeriods', 'ComparisonOperator', ], 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], 'AlarmDescription' => [ 'shape' => 'AlarmDescription', ], 'ActionsEnabled' => [ 'shape' => 'ActionsEnabled', ], 'OKActions' => [ 'shape' => 'ResourceList', ], 'AlarmActions' => [ 'shape' => 'ResourceList', ], 'InsufficientDataActions' => [ 'shape' => 'ResourceList', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'Statistic' => [ 'shape' => 'Statistic', ], 'ExtendedStatistic' => [ 'shape' => 'ExtendedStatistic', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Period' => [ 'shape' => 'Period', ], 'Unit' => [ 'shape' => 'StandardUnit', ], 'EvaluationPeriods' => [ 'shape' => 'EvaluationPeriods', ], 'DatapointsToAlarm' => [ 'shape' => 'DatapointsToAlarm', ], 'Threshold' => [ 'shape' => 'Threshold', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'TreatMissingData' => [ 'shape' => 'TreatMissingData', ], 'EvaluateLowSampleCountPercentile' => [ 'shape' => 'EvaluateLowSampleCountPercentile', ], 'Metrics' => [ 'shape' => 'MetricDataQueries', ], 'Tags' => [ 'shape' => 'TagList', ], 'ThresholdMetricId' => [ 'shape' => 'MetricId', ], ], ], 'PutMetricDataInput' => [ 'type' => 'structure', 'required' => [ 'Namespace', 'MetricData', ], 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricData' => [ 'shape' => 'MetricData', ], ], ], 'PutMetricStreamInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'FirehoseArn', 'RoleArn', 'OutputFormat', ], 'members' => [ 'Name' => [ 'shape' => 'MetricStreamName', ], 'IncludeFilters' => [ 'shape' => 'MetricStreamFilters', ], 'ExcludeFilters' => [ 'shape' => 'MetricStreamFilters', ], 'FirehoseArn' => [ 'shape' => 'AmazonResourceName', ], 'RoleArn' => [ 'shape' => 'AmazonResourceName', ], 'OutputFormat' => [ 'shape' => 'MetricStreamOutputFormat', ], 'Tags' => [ 'shape' => 'TagList', ], 'StatisticsConfigurations' => [ 'shape' => 'MetricStreamStatisticsConfigurations', ], 'IncludeLinkedAccountsMetrics' => [ 'shape' => 'IncludeLinkedAccountsMetrics', ], ], ], 'PutMetricStreamOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'Range' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], 'xmlOrder' => [ 'StartTime', 'EndTime', ], ], 'RecentlyActive' => [ 'type' => 'string', 'enum' => [ 'PT3H', ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceName', ], 'max' => 5, ], 'ResourceName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ResourceNotFound' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'code' => 'ResourceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], ], 'error' => [ 'code' => 'ResourceNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'ReturnData' => [ 'type' => 'boolean', ], 'ScanBy' => [ 'type' => 'string', 'enum' => [ 'TimestampDescending', 'TimestampAscending', ], ], 'SetAlarmStateInput' => [ 'type' => 'structure', 'required' => [ 'AlarmName', 'StateValue', 'StateReason', ], 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], 'StateValue' => [ 'shape' => 'StateValue', ], 'StateReason' => [ 'shape' => 'StateReason', ], 'StateReasonData' => [ 'shape' => 'StateReasonData', ], ], ], 'SingleMetricAnomalyDetector' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Stat' => [ 'shape' => 'AnomalyDetectorMetricStat', ], ], ], 'Size' => [ 'type' => 'long', ], 'StandardUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'StartMetricStreamsInput' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'MetricStreamNames', ], ], ], 'StartMetricStreamsOutput' => [ 'type' => 'structure', 'members' => [], ], 'Stat' => [ 'type' => 'string', ], 'StateReason' => [ 'type' => 'string', 'max' => 1023, 'min' => 0, ], 'StateReasonData' => [ 'type' => 'string', 'max' => 4000, 'min' => 0, ], 'StateValue' => [ 'type' => 'string', 'enum' => [ 'OK', 'ALARM', 'INSUFFICIENT_DATA', ], ], 'Statistic' => [ 'type' => 'string', 'enum' => [ 'SampleCount', 'Average', 'Sum', 'Minimum', 'Maximum', ], ], 'StatisticSet' => [ 'type' => 'structure', 'required' => [ 'SampleCount', 'Sum', 'Minimum', 'Maximum', ], 'members' => [ 'SampleCount' => [ 'shape' => 'DatapointValue', ], 'Sum' => [ 'shape' => 'DatapointValue', ], 'Minimum' => [ 'shape' => 'DatapointValue', ], 'Maximum' => [ 'shape' => 'DatapointValue', ], ], ], 'Statistics' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statistic', ], 'max' => 5, 'min' => 1, ], 'StatusCode' => [ 'type' => 'string', 'enum' => [ 'Complete', 'InternalError', 'PartialData', 'Forbidden', ], ], 'StopMetricStreamsInput' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'MetricStreamNames', ], ], ], 'StopMetricStreamsOutput' => [ 'type' => 'structure', 'members' => [], ], 'StorageResolution' => [ 'type' => 'integer', 'min' => 1, ], 'SuppressorPeriod' => [ 'type' => 'integer', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TemplateName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9A-Za-z][\\-\\.\\_0-9A-Za-z]{0,126}[0-9A-Za-z]', ], 'Threshold' => [ 'type' => 'double', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], ], 'TreatMissingData' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatapointValue', ], ], ],];
