<?php
// This file was auto-generated from sdk-root/src/data/grandfathered-services.json
return [ 'grandfathered-services' => [ 'AccessAnalyzer', 'Account', 'ACMPCA', 'ACM', 'AlexaForBusiness', 'PrometheusService', 'Amplify', 'AmplifyBackend', 'AmplifyUIBuilder', 'APIGateway', 'ApiGatewayManagementApi', 'ApiGatewayV2', 'AppConfig', 'AppConfigData', 'Appflow', 'AppIntegrationsService', 'ApplicationAutoScaling', 'ApplicationInsights', 'ApplicationCostProfiler', 'AppMesh', 'AppRunner', 'AppStream', 'AppSync', 'Athena', 'AuditManager', 'AutoScalingPlans', 'AutoScaling', 'BackupGateway', 'Backup', 'Batch', 'BillingConductor', 'Braket', 'Budgets', 'CostExplorer', 'ChimeSDKIdentity', 'ChimeSDKMediaPipelines', 'ChimeSDKMeetings', 'ChimeSDKMessaging', 'Chime', 'Cloud9', 'CloudControlApi', 'CloudDirectory', 'CloudFormation', 'CloudFront', 'CloudHSM', 'CloudHSMV2', 'CloudSearch', 'CloudSearchDomain', 'CloudTrail', 'CodeArtifact', 'CodeBuild', 'CodeCommit', 'CodeDeploy', 'CodeGuruReviewer', 'CodeGuruProfiler', 'CodePipeline', 'CodeStarconnections', 'CodeStarNotifications', 'CodeStar', 'CognitoIdentity', 'CognitoIdentityProvider', 'CognitoSync', 'Comprehend', 'ComprehendMedical', 'ComputeOptimizer', 'ConfigService', 'ConnectContactLens', 'Connect', 'ConnectCampaignService', 'ConnectParticipant', 'CostandUsageReportService', 'CustomerProfiles', 'IoTDataPlane', 'GlueDataBrew', 'DataExchange', 'DataPipeline', 'DataSync', 'DAX', 'Detective', 'DeviceFarm', 'DevOpsGuru', 'DirectConnect', 'ApplicationDiscoveryService', 'DLM', 'DatabaseMigrationService', 'DocDB', 'drs', 'DirectoryService', 'DynamoDB', 'EBS', 'EC2InstanceConnect', 'EC2', 'ECRPublic', 'ECR', 'ECS', 'EKS', 'ElasticInference', 'ElastiCache', 'ElasticBeanstalk', 'EFS', 'ElasticLoadBalancing', 'ElasticLoadBalancingv2', 'EMR', 'ElasticTranscoder', 'SES', 'EMRContainers', 'EMRServerless', 'MarketplaceEntitlementService', 'ElasticsearchService', 'EventBridge', 'CloudWatchEvents', 'CloudWatchEvidently', 'FinSpaceData', 'finspace', 'Firehose', 'FIS', 'FMS', 'ForecastService', 'ForecastQueryService', 'FraudDetector', 'FSx', 'GameLift', 'Glacier', 'GlobalAccelerator', 'Glue', 'ManagedGrafana', 'Greengrass', 'GreengrassV2', 'GroundStation', 'GuardDuty', 'Health', 'HealthLake', 'Honeycode', 'IAM', 'IdentityStore', 'imagebuilder', 'ImportExport', 'Inspector', 'Inspector2', 'IoTJobsDataPlane', 'IoT', 'IoT1ClickDevicesService', 'IoT1ClickProjects', 'IoTAnalytics', 'IoTDeviceAdvisor', 'IoTEventsData', 'IoTEvents', 'IoTFleetHub', 'IoTSecureTunneling', 'IoTSiteWise', 'IoTThingsGraph', 'IoTTwinMaker', 'IoTWireless', 'IVS', 'ivschat', 'Kafka', 'KafkaConnect', 'kendra', 'Keyspaces', 'KinesisVideoArchivedMedia', 'KinesisVideoMedia', 'KinesisVideoSignalingChannels', 'Kinesis', 'KinesisAnalytics', 'KinesisAnalyticsV2', 'KinesisVideo', 'KMS', 'LakeFormation', 'Lambda', 'LexModelBuildingService', 'LicenseManager', 'Lightsail', 'LocationService', 'CloudWatchLogs', 'LookoutEquipment', 'LookoutMetrics', 'LookoutforVision', 'MainframeModernization', 'MachineLearning', 'Macie2', 'ManagedBlockchain', 'MarketplaceCatalog', 'MarketplaceCommerceAnalytics', 'MediaConnect', 'MediaConvert', 'MediaLive', 'MediaPackageVod', 'MediaPackage', 'MediaStoreData', 'MediaStore', 'MediaTailor', 'MemoryDB', 'MarketplaceMetering', 'MigrationHub', 'mgn', 'MigrationHubRefactorSpaces', 'MigrationHubConfig', 'MigrationHubStrategyRecommendations', 'Mobile', 'LexModelsV2', 'CloudWatch', 'MQ', 'MTurk', 'MWAA', 'Neptune', 'NetworkFirewall', 'NetworkManager', 'NimbleStudio', 'OpenSearchService', 'OpsWorks', 'OpsWorksCM', 'Organizations', 'Outposts', 'Panorama', 'PersonalizeEvents', 'PersonalizeRuntime', 'Personalize', 'PI', 'PinpointEmail', 'PinpointSMSVoiceV2', 'Pinpoint', 'Polly', 'Pricing', 'Proton', 'QLDBSession', 'QLDB', 'QuickSight', 'RAM', 'RecycleBin', 'RDSDataService', 'RDS', 'RedshiftDataAPIService', 'RedshiftServerless', 'Redshift', 'Rekognition', 'ResilienceHub', 'ResourceGroups', 'ResourceGroupsTaggingAPI', 'RoboMaker', 'Route53RecoveryCluster', 'Route53RecoveryControlConfig', 'Route53RecoveryReadiness', 'Route53', 'Route53Domains', 'Route53Resolver', 'CloudWatchRUM', 'LexRuntimeV2', 'LexRuntimeService', 'SageMakerRuntime', 'S3', 'S3Control', 'S3Outposts', 'AugmentedAIRuntime', 'SagemakerEdgeManager', 'SageMakerFeatureStoreRuntime', 'SageMaker', 'SavingsPlans', 'Schemas', 'SecretsManager', 'SecurityHub', 'ServerlessApplicationRepository', 'ServiceQuotas', 'AppRegistry', 'ServiceCatalog', 'ServiceDiscovery', 'SESV2', 'Shield', 'signer', 'PinpointSMSVoice', 'SMS', 'SnowDeviceManagement', 'Snowball', 'SNS', 'SQS', 'SSMContacts', 'SSMIncidents', 'SSM', 'SSOAdmin', 'SSOOIDC', 'SSO', 'SFN', 'StorageGateway', 'DynamoDBStreams', 'STS', 'Support', 'SWF', 'Synthetics', 'Textract', 'TimestreamQuery', 'TimestreamWrite', 'TranscribeService', 'Transfer', 'Translate', 'VoiceID', 'WAFRegional', 'WAF', 'WAFV2', 'WellArchitected', 'ConnectWisdomService', 'WorkDocs', 'WorkLink', 'WorkMail', 'WorkMailMessageFlow', 'WorkSpacesWeb', 'WorkSpaces', 'XRay', ],];
