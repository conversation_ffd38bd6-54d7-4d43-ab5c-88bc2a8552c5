// Image

.img-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

// Clearfix

.floatfix {
    &:before,
    &:after {
        content: '';
        display: table;
    }
    &:after {
        clear: both;
    }
}

// Overflows

.overflow-visible {
    overflow: visible !important;
}
.overflow-hidden {
    overflow: hidden !important;
}

// Opacity classes

.opacity-1 {
    opacity: .1 !important;
}
.opacity-2 {
    opacity: .2 !important;
}
.opacity-3 {
    opacity: .3 !important;
}
.opacity-4 {
    opacity: .4 !important;
}
.opacity-5 {
    opacity: .5 !important;
}
.opacity-6 {
    opacity: .6 !important;
}
.opacity-7 {
    opacity: .7 !important;
}
.opacity-8 {
    opacity: .8 !important;
}
.opacity-9 {
    opacity: .9 !important;
}
.opacity-10 {
    opacity: 1 !important;
}
