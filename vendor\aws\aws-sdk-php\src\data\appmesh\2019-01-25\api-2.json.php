<?php
// This file was auto-generated from sdk-root/src/data/appmesh/2019-01-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-01-25', 'endpointPrefix' => 'appmesh', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS App Mesh', 'serviceId' => 'App Mesh', 'signatureVersion' => 'v4', 'signingName' => 'appmesh', 'uid' => 'appmesh-2019-01-25', ], 'operations' => [ 'CreateGatewayRoute' => [ 'name' => 'CreateGatewayRoute', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateway/{virtualGatewayName}/gatewayRoutes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateGatewayRouteInput', ], 'output' => [ 'shape' => 'CreateGatewayRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateMesh' => [ 'name' => 'CreateMesh', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMeshInput', ], 'output' => [ 'shape' => 'CreateMeshOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateRoute' => [ 'name' => 'CreateRoute', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouter/{virtualRouterName}/routes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRouteInput', ], 'output' => [ 'shape' => 'CreateRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateVirtualGateway' => [ 'name' => 'CreateVirtualGateway', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateways', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVirtualGatewayInput', ], 'output' => [ 'shape' => 'CreateVirtualGatewayOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateVirtualNode' => [ 'name' => 'CreateVirtualNode', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualNodes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVirtualNodeInput', ], 'output' => [ 'shape' => 'CreateVirtualNodeOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateVirtualRouter' => [ 'name' => 'CreateVirtualRouter', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVirtualRouterInput', ], 'output' => [ 'shape' => 'CreateVirtualRouterOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateVirtualService' => [ 'name' => 'CreateVirtualService', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualServices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVirtualServiceInput', ], 'output' => [ 'shape' => 'CreateVirtualServiceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'DeleteGatewayRoute' => [ 'name' => 'DeleteGatewayRoute', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateway/{virtualGatewayName}/gatewayRoutes/{gatewayRouteName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGatewayRouteInput', ], 'output' => [ 'shape' => 'DeleteGatewayRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteMesh' => [ 'name' => 'DeleteMesh', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMeshInput', ], 'output' => [ 'shape' => 'DeleteMeshOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteRoute' => [ 'name' => 'DeleteRoute', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouter/{virtualRouterName}/routes/{routeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRouteInput', ], 'output' => [ 'shape' => 'DeleteRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteVirtualGateway' => [ 'name' => 'DeleteVirtualGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateways/{virtualGatewayName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVirtualGatewayInput', ], 'output' => [ 'shape' => 'DeleteVirtualGatewayOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteVirtualNode' => [ 'name' => 'DeleteVirtualNode', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualNodes/{virtualNodeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVirtualNodeInput', ], 'output' => [ 'shape' => 'DeleteVirtualNodeOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteVirtualRouter' => [ 'name' => 'DeleteVirtualRouter', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouters/{virtualRouterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVirtualRouterInput', ], 'output' => [ 'shape' => 'DeleteVirtualRouterOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DeleteVirtualService' => [ 'name' => 'DeleteVirtualService', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v20190125/meshes/{meshName}/virtualServices/{virtualServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVirtualServiceInput', ], 'output' => [ 'shape' => 'DeleteVirtualServiceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'DescribeGatewayRoute' => [ 'name' => 'DescribeGatewayRoute', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateway/{virtualGatewayName}/gatewayRoutes/{gatewayRouteName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGatewayRouteInput', ], 'output' => [ 'shape' => 'DescribeGatewayRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeMesh' => [ 'name' => 'DescribeMesh', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMeshInput', ], 'output' => [ 'shape' => 'DescribeMeshOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeRoute' => [ 'name' => 'DescribeRoute', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouter/{virtualRouterName}/routes/{routeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRouteInput', ], 'output' => [ 'shape' => 'DescribeRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeVirtualGateway' => [ 'name' => 'DescribeVirtualGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateways/{virtualGatewayName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVirtualGatewayInput', ], 'output' => [ 'shape' => 'DescribeVirtualGatewayOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeVirtualNode' => [ 'name' => 'DescribeVirtualNode', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualNodes/{virtualNodeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVirtualNodeInput', ], 'output' => [ 'shape' => 'DescribeVirtualNodeOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeVirtualRouter' => [ 'name' => 'DescribeVirtualRouter', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouters/{virtualRouterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVirtualRouterInput', ], 'output' => [ 'shape' => 'DescribeVirtualRouterOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DescribeVirtualService' => [ 'name' => 'DescribeVirtualService', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualServices/{virtualServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVirtualServiceInput', ], 'output' => [ 'shape' => 'DescribeVirtualServiceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListGatewayRoutes' => [ 'name' => 'ListGatewayRoutes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateway/{virtualGatewayName}/gatewayRoutes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGatewayRoutesInput', ], 'output' => [ 'shape' => 'ListGatewayRoutesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListMeshes' => [ 'name' => 'ListMeshes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMeshesInput', ], 'output' => [ 'shape' => 'ListMeshesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListRoutes' => [ 'name' => 'ListRoutes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouter/{virtualRouterName}/routes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRoutesInput', ], 'output' => [ 'shape' => 'ListRoutesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListVirtualGateways' => [ 'name' => 'ListVirtualGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateways', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVirtualGatewaysInput', ], 'output' => [ 'shape' => 'ListVirtualGatewaysOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListVirtualNodes' => [ 'name' => 'ListVirtualNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualNodes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVirtualNodesInput', ], 'output' => [ 'shape' => 'ListVirtualNodesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListVirtualRouters' => [ 'name' => 'ListVirtualRouters', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVirtualRoutersInput', ], 'output' => [ 'shape' => 'ListVirtualRoutersOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListVirtualServices' => [ 'name' => 'ListVirtualServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/v20190125/meshes/{meshName}/virtualServices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVirtualServicesInput', ], 'output' => [ 'shape' => 'ListVirtualServicesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/tag', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/untag', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'UpdateGatewayRoute' => [ 'name' => 'UpdateGatewayRoute', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateway/{virtualGatewayName}/gatewayRoutes/{gatewayRouteName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGatewayRouteInput', ], 'output' => [ 'shape' => 'UpdateGatewayRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UpdateMesh' => [ 'name' => 'UpdateMesh', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMeshInput', ], 'output' => [ 'shape' => 'UpdateMeshOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], ], 'idempotent' => true, ], 'UpdateRoute' => [ 'name' => 'UpdateRoute', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouter/{virtualRouterName}/routes/{routeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRouteInput', ], 'output' => [ 'shape' => 'UpdateRouteOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UpdateVirtualGateway' => [ 'name' => 'UpdateVirtualGateway', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualGateways/{virtualGatewayName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVirtualGatewayInput', ], 'output' => [ 'shape' => 'UpdateVirtualGatewayOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UpdateVirtualNode' => [ 'name' => 'UpdateVirtualNode', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualNodes/{virtualNodeName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVirtualNodeInput', ], 'output' => [ 'shape' => 'UpdateVirtualNodeOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UpdateVirtualRouter' => [ 'name' => 'UpdateVirtualRouter', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualRouters/{virtualRouterName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVirtualRouterInput', ], 'output' => [ 'shape' => 'UpdateVirtualRouterOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'UpdateVirtualService' => [ 'name' => 'UpdateVirtualService', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v20190125/meshes/{meshName}/virtualServices/{virtualServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVirtualServiceInput', ], 'output' => [ 'shape' => 'UpdateVirtualServiceOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessLog' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'FileAccessLog', ], ], 'union' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, ], 'Arn' => [ 'type' => 'string', ], 'AwsCloudMapInstanceAttribute' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'AwsCloudMapInstanceAttributeKey', ], 'value' => [ 'shape' => 'AwsCloudMapInstanceAttributeValue', ], ], ], 'AwsCloudMapInstanceAttributeKey' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9!-~]+$', ], 'AwsCloudMapInstanceAttributeValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([a-zA-Z0-9!-~][ a-zA-Z0-9!-~]*){0,1}[a-zA-Z0-9!-~]{0,1}$', ], 'AwsCloudMapInstanceAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudMapInstanceAttribute', ], ], 'AwsCloudMapName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'AwsCloudMapServiceDiscovery' => [ 'type' => 'structure', 'required' => [ 'namespaceName', 'serviceName', ], 'members' => [ 'attributes' => [ 'shape' => 'AwsCloudMapInstanceAttributes', ], 'ipPreference' => [ 'shape' => 'IpPreference', ], 'namespaceName' => [ 'shape' => 'AwsCloudMapName', ], 'serviceName' => [ 'shape' => 'AwsCloudMapName', ], ], ], 'Backend' => [ 'type' => 'structure', 'members' => [ 'virtualService' => [ 'shape' => 'VirtualServiceBackend', ], ], 'union' => true, ], 'BackendDefaults' => [ 'type' => 'structure', 'members' => [ 'clientPolicy' => [ 'shape' => 'ClientPolicy', ], ], ], 'Backends' => [ 'type' => 'list', 'member' => [ 'shape' => 'Backend', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CertificateAuthorityArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 3, 'min' => 1, ], 'ClientPolicy' => [ 'type' => 'structure', 'members' => [ 'tls' => [ 'shape' => 'ClientPolicyTls', ], ], ], 'ClientPolicyTls' => [ 'type' => 'structure', 'required' => [ 'validation', ], 'members' => [ 'certificate' => [ 'shape' => 'ClientTlsCertificate', ], 'enforce' => [ 'shape' => 'Boolean', ], 'ports' => [ 'shape' => 'PortSet', ], 'validation' => [ 'shape' => 'TlsValidationContext', ], ], ], 'ClientTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'ListenerTlsFileCertificate', ], 'sds' => [ 'shape' => 'ListenerTlsSdsCertificate', ], ], 'union' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateGatewayRouteInput' => [ 'type' => 'structure', 'required' => [ 'gatewayRouteName', 'meshName', 'spec', 'virtualGatewayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'gatewayRouteName' => [ 'shape' => 'ResourceName', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'GatewayRouteSpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'CreateGatewayRouteOutput' => [ 'type' => 'structure', 'required' => [ 'gatewayRoute', ], 'members' => [ 'gatewayRoute' => [ 'shape' => 'GatewayRouteData', ], ], 'payload' => 'gatewayRoute', ], 'CreateMeshInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'MeshSpec', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMeshOutput' => [ 'type' => 'structure', 'required' => [ 'mesh', ], 'members' => [ 'mesh' => [ 'shape' => 'MeshData', ], ], 'payload' => 'mesh', ], 'CreateRouteInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'routeName', 'spec', 'virtualRouterName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'routeName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'RouteSpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'CreateRouteOutput' => [ 'type' => 'structure', 'required' => [ 'route', ], 'members' => [ 'route' => [ 'shape' => 'RouteData', ], ], 'payload' => 'route', ], 'CreateVirtualGatewayInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualGatewayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualGatewaySpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateVirtualGatewayOutput' => [ 'type' => 'structure', 'required' => [ 'virtualGateway', ], 'members' => [ 'virtualGateway' => [ 'shape' => 'VirtualGatewayData', ], ], 'payload' => 'virtualGateway', ], 'CreateVirtualNodeInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualNodeName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualNodeSpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateVirtualNodeOutput' => [ 'type' => 'structure', 'required' => [ 'virtualNode', ], 'members' => [ 'virtualNode' => [ 'shape' => 'VirtualNodeData', ], ], 'payload' => 'virtualNode', ], 'CreateVirtualRouterInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualRouterName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualRouterSpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateVirtualRouterOutput' => [ 'type' => 'structure', 'required' => [ 'virtualRouter', ], 'members' => [ 'virtualRouter' => [ 'shape' => 'VirtualRouterData', ], ], 'payload' => 'virtualRouter', ], 'CreateVirtualServiceInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualServiceName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualServiceSpec', ], 'tags' => [ 'shape' => 'TagList', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', ], ], ], 'CreateVirtualServiceOutput' => [ 'type' => 'structure', 'required' => [ 'virtualService', ], 'members' => [ 'virtualService' => [ 'shape' => 'VirtualServiceData', ], ], 'payload' => 'virtualService', ], 'DefaultGatewayRouteRewrite' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DeleteGatewayRouteInput' => [ 'type' => 'structure', 'required' => [ 'gatewayRouteName', 'meshName', 'virtualGatewayName', ], 'members' => [ 'gatewayRouteName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'gatewayRouteName', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'DeleteGatewayRouteOutput' => [ 'type' => 'structure', 'required' => [ 'gatewayRoute', ], 'members' => [ 'gatewayRoute' => [ 'shape' => 'GatewayRouteData', ], ], 'payload' => 'gatewayRoute', ], 'DeleteMeshInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], ], ], 'DeleteMeshOutput' => [ 'type' => 'structure', 'required' => [ 'mesh', ], 'members' => [ 'mesh' => [ 'shape' => 'MeshData', ], ], 'payload' => 'mesh', ], 'DeleteRouteInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'routeName', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'routeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'routeName', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'DeleteRouteOutput' => [ 'type' => 'structure', 'required' => [ 'route', ], 'members' => [ 'route' => [ 'shape' => 'RouteData', ], ], 'payload' => 'route', ], 'DeleteVirtualGatewayInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualGatewayName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'DeleteVirtualGatewayOutput' => [ 'type' => 'structure', 'required' => [ 'virtualGateway', ], 'members' => [ 'virtualGateway' => [ 'shape' => 'VirtualGatewayData', ], ], 'payload' => 'virtualGateway', ], 'DeleteVirtualNodeInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualNodeName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualNodeName', ], ], ], 'DeleteVirtualNodeOutput' => [ 'type' => 'structure', 'required' => [ 'virtualNode', ], 'members' => [ 'virtualNode' => [ 'shape' => 'VirtualNodeData', ], ], 'payload' => 'virtualNode', ], 'DeleteVirtualRouterInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'DeleteVirtualRouterOutput' => [ 'type' => 'structure', 'required' => [ 'virtualRouter', ], 'members' => [ 'virtualRouter' => [ 'shape' => 'VirtualRouterData', ], ], 'payload' => 'virtualRouter', ], 'DeleteVirtualServiceInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualServiceName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', 'location' => 'uri', 'locationName' => 'virtualServiceName', ], ], ], 'DeleteVirtualServiceOutput' => [ 'type' => 'structure', 'required' => [ 'virtualService', ], 'members' => [ 'virtualService' => [ 'shape' => 'VirtualServiceData', ], ], 'payload' => 'virtualService', ], 'DescribeGatewayRouteInput' => [ 'type' => 'structure', 'required' => [ 'gatewayRouteName', 'meshName', 'virtualGatewayName', ], 'members' => [ 'gatewayRouteName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'gatewayRouteName', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'DescribeGatewayRouteOutput' => [ 'type' => 'structure', 'required' => [ 'gatewayRoute', ], 'members' => [ 'gatewayRoute' => [ 'shape' => 'GatewayRouteData', ], ], 'payload' => 'gatewayRoute', ], 'DescribeMeshInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], ], ], 'DescribeMeshOutput' => [ 'type' => 'structure', 'required' => [ 'mesh', ], 'members' => [ 'mesh' => [ 'shape' => 'MeshData', ], ], 'payload' => 'mesh', ], 'DescribeRouteInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'routeName', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'routeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'routeName', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'DescribeRouteOutput' => [ 'type' => 'structure', 'required' => [ 'route', ], 'members' => [ 'route' => [ 'shape' => 'RouteData', ], ], 'payload' => 'route', ], 'DescribeVirtualGatewayInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualGatewayName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'DescribeVirtualGatewayOutput' => [ 'type' => 'structure', 'required' => [ 'virtualGateway', ], 'members' => [ 'virtualGateway' => [ 'shape' => 'VirtualGatewayData', ], ], 'payload' => 'virtualGateway', ], 'DescribeVirtualNodeInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualNodeName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualNodeName', ], ], ], 'DescribeVirtualNodeOutput' => [ 'type' => 'structure', 'required' => [ 'virtualNode', ], 'members' => [ 'virtualNode' => [ 'shape' => 'VirtualNodeData', ], ], 'payload' => 'virtualNode', ], 'DescribeVirtualRouterInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'DescribeVirtualRouterOutput' => [ 'type' => 'structure', 'required' => [ 'virtualRouter', ], 'members' => [ 'virtualRouter' => [ 'shape' => 'VirtualRouterData', ], ], 'payload' => 'virtualRouter', ], 'DescribeVirtualServiceInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualServiceName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', 'location' => 'uri', 'locationName' => 'virtualServiceName', ], ], ], 'DescribeVirtualServiceOutput' => [ 'type' => 'structure', 'required' => [ 'virtualService', ], 'members' => [ 'virtualService' => [ 'shape' => 'VirtualServiceData', ], ], 'payload' => 'virtualService', ], 'DnsResponseType' => [ 'type' => 'string', 'enum' => [ 'LOADBALANCER', 'ENDPOINTS', ], ], 'DnsServiceDiscovery' => [ 'type' => 'structure', 'required' => [ 'hostname', ], 'members' => [ 'hostname' => [ 'shape' => 'Hostname', ], 'ipPreference' => [ 'shape' => 'IpPreference', ], 'responseType' => [ 'shape' => 'DnsResponseType', ], ], ], 'Duration' => [ 'type' => 'structure', 'members' => [ 'unit' => [ 'shape' => 'DurationUnit', ], 'value' => [ 'shape' => 'DurationValue', ], ], ], 'DurationUnit' => [ 'type' => 'string', 'enum' => [ 's', 'ms', ], ], 'DurationValue' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'EgressFilter' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'EgressFilterType', ], ], ], 'EgressFilterType' => [ 'type' => 'string', 'enum' => [ 'ALLOW_ALL', 'DROP_ALL', ], ], 'ExactHostName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, ], 'FileAccessLog' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'format' => [ 'shape' => 'LoggingFormat', ], 'path' => [ 'shape' => 'FilePath', ], ], ], 'FilePath' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'GatewayRouteData' => [ 'type' => 'structure', 'required' => [ 'gatewayRouteName', 'meshName', 'metadata', 'spec', 'status', 'virtualGatewayName', ], 'members' => [ 'gatewayRouteName' => [ 'shape' => 'ResourceName', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'GatewayRouteSpec', ], 'status' => [ 'shape' => 'GatewayRouteStatus', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', ], ], ], 'GatewayRouteHostnameMatch' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'ExactHostName', ], 'suffix' => [ 'shape' => 'SuffixHostname', ], ], ], 'GatewayRouteHostnameRewrite' => [ 'type' => 'structure', 'members' => [ 'defaultTargetHostname' => [ 'shape' => 'DefaultGatewayRouteRewrite', ], ], ], 'GatewayRouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayRouteRef', ], ], 'GatewayRoutePriority' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'GatewayRouteRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'gatewayRouteName', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', 'virtualGatewayName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'gatewayRouteName' => [ 'shape' => 'ResourceName', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', ], ], ], 'GatewayRouteSpec' => [ 'type' => 'structure', 'members' => [ 'grpcRoute' => [ 'shape' => 'GrpcGatewayRoute', ], 'http2Route' => [ 'shape' => 'HttpGatewayRoute', ], 'httpRoute' => [ 'shape' => 'HttpGatewayRoute', ], 'priority' => [ 'shape' => 'GatewayRoutePriority', ], ], ], 'GatewayRouteStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'GatewayRouteStatusCode', ], ], ], 'GatewayRouteStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'GatewayRouteTarget' => [ 'type' => 'structure', 'required' => [ 'virtualService', ], 'members' => [ 'port' => [ 'shape' => 'ListenerPort', ], 'virtualService' => [ 'shape' => 'GatewayRouteVirtualService', ], ], ], 'GatewayRouteVirtualService' => [ 'type' => 'structure', 'required' => [ 'virtualServiceName', ], 'members' => [ 'virtualServiceName' => [ 'shape' => 'ResourceName', ], ], ], 'GrpcGatewayRoute' => [ 'type' => 'structure', 'required' => [ 'action', 'match', ], 'members' => [ 'action' => [ 'shape' => 'GrpcGatewayRouteAction', ], 'match' => [ 'shape' => 'GrpcGatewayRouteMatch', ], ], ], 'GrpcGatewayRouteAction' => [ 'type' => 'structure', 'required' => [ 'target', ], 'members' => [ 'rewrite' => [ 'shape' => 'GrpcGatewayRouteRewrite', ], 'target' => [ 'shape' => 'GatewayRouteTarget', ], ], ], 'GrpcGatewayRouteMatch' => [ 'type' => 'structure', 'members' => [ 'hostname' => [ 'shape' => 'GatewayRouteHostnameMatch', ], 'metadata' => [ 'shape' => 'GrpcGatewayRouteMetadataList', ], 'port' => [ 'shape' => 'ListenerPort', ], 'serviceName' => [ 'shape' => 'ServiceName', ], ], ], 'GrpcGatewayRouteMetadata' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'invert' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'GrpcMetadataMatchMethod', ], 'name' => [ 'shape' => 'HeaderName', ], ], ], 'GrpcGatewayRouteMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrpcGatewayRouteMetadata', ], 'max' => 10, 'min' => 1, ], 'GrpcGatewayRouteRewrite' => [ 'type' => 'structure', 'members' => [ 'hostname' => [ 'shape' => 'GatewayRouteHostnameRewrite', ], ], ], 'GrpcMetadataMatchMethod' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'HeaderMatch', ], 'prefix' => [ 'shape' => 'HeaderMatch', ], 'range' => [ 'shape' => 'MatchRange', ], 'regex' => [ 'shape' => 'HeaderMatch', ], 'suffix' => [ 'shape' => 'HeaderMatch', ], ], 'union' => true, ], 'GrpcRetryPolicy' => [ 'type' => 'structure', 'required' => [ 'maxRetries', 'perRetryTimeout', ], 'members' => [ 'grpcRetryEvents' => [ 'shape' => 'GrpcRetryPolicyEvents', ], 'httpRetryEvents' => [ 'shape' => 'HttpRetryPolicyEvents', ], 'maxRetries' => [ 'shape' => 'MaxRetries', ], 'perRetryTimeout' => [ 'shape' => 'Duration', ], 'tcpRetryEvents' => [ 'shape' => 'TcpRetryPolicyEvents', ], ], ], 'GrpcRetryPolicyEvent' => [ 'type' => 'string', 'enum' => [ 'cancelled', 'deadline-exceeded', 'internal', 'resource-exhausted', 'unavailable', ], ], 'GrpcRetryPolicyEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrpcRetryPolicyEvent', ], 'max' => 5, 'min' => 1, ], 'GrpcRoute' => [ 'type' => 'structure', 'required' => [ 'action', 'match', ], 'members' => [ 'action' => [ 'shape' => 'GrpcRouteAction', ], 'match' => [ 'shape' => 'GrpcRouteMatch', ], 'retryPolicy' => [ 'shape' => 'GrpcRetryPolicy', ], 'timeout' => [ 'shape' => 'GrpcTimeout', ], ], ], 'GrpcRouteAction' => [ 'type' => 'structure', 'required' => [ 'weightedTargets', ], 'members' => [ 'weightedTargets' => [ 'shape' => 'WeightedTargets', ], ], ], 'GrpcRouteMatch' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'GrpcRouteMetadataList', ], 'methodName' => [ 'shape' => 'MethodName', ], 'port' => [ 'shape' => 'ListenerPort', ], 'serviceName' => [ 'shape' => 'ServiceName', ], ], ], 'GrpcRouteMetadata' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'invert' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'GrpcRouteMetadataMatchMethod', ], 'name' => [ 'shape' => 'HeaderName', ], ], ], 'GrpcRouteMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrpcRouteMetadata', ], 'max' => 10, 'min' => 1, ], 'GrpcRouteMetadataMatchMethod' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'HeaderMatch', ], 'prefix' => [ 'shape' => 'HeaderMatch', ], 'range' => [ 'shape' => 'MatchRange', ], 'regex' => [ 'shape' => 'HeaderMatch', ], 'suffix' => [ 'shape' => 'HeaderMatch', ], ], 'union' => true, ], 'GrpcTimeout' => [ 'type' => 'structure', 'members' => [ 'idle' => [ 'shape' => 'Duration', ], 'perRequest' => [ 'shape' => 'Duration', ], ], ], 'HeaderMatch' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HeaderMatchMethod' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'HeaderMatch', ], 'prefix' => [ 'shape' => 'HeaderMatch', ], 'range' => [ 'shape' => 'MatchRange', ], 'regex' => [ 'shape' => 'HeaderMatch', ], 'suffix' => [ 'shape' => 'HeaderMatch', ], ], 'union' => true, ], 'HeaderName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'HealthCheckIntervalMillis' => [ 'type' => 'long', 'box' => true, 'max' => 300000, 'min' => 5000, ], 'HealthCheckPolicy' => [ 'type' => 'structure', 'required' => [ 'healthyThreshold', 'intervalMillis', 'protocol', 'timeoutMillis', 'unhealthyThreshold', ], 'members' => [ 'healthyThreshold' => [ 'shape' => 'HealthCheckThreshold', ], 'intervalMillis' => [ 'shape' => 'HealthCheckIntervalMillis', ], 'path' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'PortNumber', ], 'protocol' => [ 'shape' => 'PortProtocol', ], 'timeoutMillis' => [ 'shape' => 'HealthCheckTimeoutMillis', ], 'unhealthyThreshold' => [ 'shape' => 'HealthCheckThreshold', ], ], ], 'HealthCheckThreshold' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 2, ], 'HealthCheckTimeoutMillis' => [ 'type' => 'long', 'box' => true, 'max' => 60000, 'min' => 2000, ], 'Hostname' => [ 'type' => 'string', ], 'HttpGatewayRoute' => [ 'type' => 'structure', 'required' => [ 'action', 'match', ], 'members' => [ 'action' => [ 'shape' => 'HttpGatewayRouteAction', ], 'match' => [ 'shape' => 'HttpGatewayRouteMatch', ], ], ], 'HttpGatewayRouteAction' => [ 'type' => 'structure', 'required' => [ 'target', ], 'members' => [ 'rewrite' => [ 'shape' => 'HttpGatewayRouteRewrite', ], 'target' => [ 'shape' => 'GatewayRouteTarget', ], ], ], 'HttpGatewayRouteHeader' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'invert' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'HeaderMatchMethod', ], 'name' => [ 'shape' => 'HeaderName', ], ], ], 'HttpGatewayRouteHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpGatewayRouteHeader', ], 'max' => 10, 'min' => 1, ], 'HttpGatewayRouteMatch' => [ 'type' => 'structure', 'members' => [ 'headers' => [ 'shape' => 'HttpGatewayRouteHeaders', ], 'hostname' => [ 'shape' => 'GatewayRouteHostnameMatch', ], 'method' => [ 'shape' => 'HttpMethod', ], 'path' => [ 'shape' => 'HttpPathMatch', ], 'port' => [ 'shape' => 'ListenerPort', ], 'prefix' => [ 'shape' => 'String', ], 'queryParameters' => [ 'shape' => 'HttpQueryParameters', ], ], ], 'HttpGatewayRoutePathRewrite' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'HttpPathExact', ], ], ], 'HttpGatewayRoutePrefix' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HttpGatewayRoutePrefixRewrite' => [ 'type' => 'structure', 'members' => [ 'defaultPrefix' => [ 'shape' => 'DefaultGatewayRouteRewrite', ], 'value' => [ 'shape' => 'HttpGatewayRoutePrefix', ], ], ], 'HttpGatewayRouteRewrite' => [ 'type' => 'structure', 'members' => [ 'hostname' => [ 'shape' => 'GatewayRouteHostnameRewrite', ], 'path' => [ 'shape' => 'HttpGatewayRoutePathRewrite', ], 'prefix' => [ 'shape' => 'HttpGatewayRoutePrefixRewrite', ], ], ], 'HttpMethod' => [ 'type' => 'string', 'enum' => [ 'GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH', ], ], 'HttpPathExact' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HttpPathMatch' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'HttpPathExact', ], 'regex' => [ 'shape' => 'HttpPathRegex', ], ], ], 'HttpPathRegex' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HttpQueryParameter' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'match' => [ 'shape' => 'QueryParameterMatch', ], 'name' => [ 'shape' => 'QueryParameterName', ], ], ], 'HttpQueryParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpQueryParameter', ], 'max' => 10, 'min' => 1, ], 'HttpRetryPolicy' => [ 'type' => 'structure', 'required' => [ 'maxRetries', 'perRetryTimeout', ], 'members' => [ 'httpRetryEvents' => [ 'shape' => 'HttpRetryPolicyEvents', ], 'maxRetries' => [ 'shape' => 'MaxRetries', ], 'perRetryTimeout' => [ 'shape' => 'Duration', ], 'tcpRetryEvents' => [ 'shape' => 'TcpRetryPolicyEvents', ], ], ], 'HttpRetryPolicyEvent' => [ 'type' => 'string', 'max' => 25, 'min' => 1, ], 'HttpRetryPolicyEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpRetryPolicyEvent', ], 'max' => 25, 'min' => 1, ], 'HttpRoute' => [ 'type' => 'structure', 'required' => [ 'action', 'match', ], 'members' => [ 'action' => [ 'shape' => 'HttpRouteAction', ], 'match' => [ 'shape' => 'HttpRouteMatch', ], 'retryPolicy' => [ 'shape' => 'HttpRetryPolicy', ], 'timeout' => [ 'shape' => 'HttpTimeout', ], ], ], 'HttpRouteAction' => [ 'type' => 'structure', 'required' => [ 'weightedTargets', ], 'members' => [ 'weightedTargets' => [ 'shape' => 'WeightedTargets', ], ], ], 'HttpRouteHeader' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'invert' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'HeaderMatchMethod', ], 'name' => [ 'shape' => 'HeaderName', ], ], ], 'HttpRouteHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpRouteHeader', ], 'max' => 10, 'min' => 1, ], 'HttpRouteMatch' => [ 'type' => 'structure', 'members' => [ 'headers' => [ 'shape' => 'HttpRouteHeaders', ], 'method' => [ 'shape' => 'HttpMethod', ], 'path' => [ 'shape' => 'HttpPathMatch', ], 'port' => [ 'shape' => 'ListenerPort', ], 'prefix' => [ 'shape' => 'String', ], 'queryParameters' => [ 'shape' => 'HttpQueryParameters', ], 'scheme' => [ 'shape' => 'HttpScheme', ], ], ], 'HttpScheme' => [ 'type' => 'string', 'enum' => [ 'http', 'https', ], ], 'HttpTimeout' => [ 'type' => 'structure', 'members' => [ 'idle' => [ 'shape' => 'Duration', ], 'perRequest' => [ 'shape' => 'Duration', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpPreference' => [ 'type' => 'string', 'enum' => [ 'IPv6_PREFERRED', 'IPv4_PREFERRED', 'IPv4_ONLY', 'IPv6_ONLY', ], ], 'JsonFormat' => [ 'type' => 'list', 'member' => [ 'shape' => 'JsonFormatRef', ], ], 'JsonFormatRef' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'JsonKey', ], 'value' => [ 'shape' => 'JsonValue', ], ], ], 'JsonKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'JsonValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ListGatewayRoutesInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualGatewayName', ], 'members' => [ 'limit' => [ 'shape' => 'ListGatewayRoutesLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'ListGatewayRoutesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGatewayRoutesOutput' => [ 'type' => 'structure', 'required' => [ 'gatewayRoutes', ], 'members' => [ 'gatewayRoutes' => [ 'shape' => 'GatewayRouteList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListMeshesInput' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'ListMeshesLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMeshesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListMeshesOutput' => [ 'type' => 'structure', 'required' => [ 'meshes', ], 'members' => [ 'meshes' => [ 'shape' => 'MeshList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListRoutesInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'virtualRouterName', ], 'members' => [ 'limit' => [ 'shape' => 'ListRoutesLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'ListRoutesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRoutesOutput' => [ 'type' => 'structure', 'required' => [ 'routes', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'routes' => [ 'shape' => 'RouteList', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'limit' => [ 'shape' => 'TagsLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'ListVirtualGatewaysInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'limit' => [ 'shape' => 'ListVirtualGatewaysLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVirtualGatewaysLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVirtualGatewaysOutput' => [ 'type' => 'structure', 'required' => [ 'virtualGateways', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'virtualGateways' => [ 'shape' => 'VirtualGatewayList', ], ], ], 'ListVirtualNodesInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'limit' => [ 'shape' => 'ListVirtualNodesLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVirtualNodesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVirtualNodesOutput' => [ 'type' => 'structure', 'required' => [ 'virtualNodes', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'virtualNodes' => [ 'shape' => 'VirtualNodeList', ], ], ], 'ListVirtualRoutersInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'limit' => [ 'shape' => 'ListVirtualRoutersLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVirtualRoutersLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVirtualRoutersOutput' => [ 'type' => 'structure', 'required' => [ 'virtualRouters', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'virtualRouters' => [ 'shape' => 'VirtualRouterList', ], ], ], 'ListVirtualServicesInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'limit' => [ 'shape' => 'ListVirtualServicesLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVirtualServicesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListVirtualServicesOutput' => [ 'type' => 'structure', 'required' => [ 'virtualServices', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'virtualServices' => [ 'shape' => 'VirtualServiceList', ], ], ], 'Listener' => [ 'type' => 'structure', 'required' => [ 'portMapping', ], 'members' => [ 'connectionPool' => [ 'shape' => 'VirtualNodeConnectionPool', ], 'healthCheck' => [ 'shape' => 'HealthCheckPolicy', ], 'outlierDetection' => [ 'shape' => 'OutlierDetection', ], 'portMapping' => [ 'shape' => 'PortMapping', ], 'timeout' => [ 'shape' => 'ListenerTimeout', ], 'tls' => [ 'shape' => 'ListenerTls', ], ], ], 'ListenerPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1, ], 'ListenerTimeout' => [ 'type' => 'structure', 'members' => [ 'grpc' => [ 'shape' => 'GrpcTimeout', ], 'http' => [ 'shape' => 'HttpTimeout', ], 'http2' => [ 'shape' => 'HttpTimeout', ], 'tcp' => [ 'shape' => 'TcpTimeout', ], ], 'union' => true, ], 'ListenerTls' => [ 'type' => 'structure', 'required' => [ 'certificate', 'mode', ], 'members' => [ 'certificate' => [ 'shape' => 'ListenerTlsCertificate', ], 'mode' => [ 'shape' => 'ListenerTlsMode', ], 'validation' => [ 'shape' => 'ListenerTlsValidationContext', ], ], ], 'ListenerTlsAcmCertificate' => [ 'type' => 'structure', 'required' => [ 'certificateArn', ], 'members' => [ 'certificateArn' => [ 'shape' => 'Arn', ], ], ], 'ListenerTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'acm' => [ 'shape' => 'ListenerTlsAcmCertificate', ], 'file' => [ 'shape' => 'ListenerTlsFileCertificate', ], 'sds' => [ 'shape' => 'ListenerTlsSdsCertificate', ], ], 'union' => true, ], 'ListenerTlsFileCertificate' => [ 'type' => 'structure', 'required' => [ 'certificateChain', 'privateKey', ], 'members' => [ 'certificateChain' => [ 'shape' => 'FilePath', ], 'privateKey' => [ 'shape' => 'FilePath', ], ], ], 'ListenerTlsMode' => [ 'type' => 'string', 'enum' => [ 'STRICT', 'PERMISSIVE', 'DISABLED', ], ], 'ListenerTlsSdsCertificate' => [ 'type' => 'structure', 'required' => [ 'secretName', ], 'members' => [ 'secretName' => [ 'shape' => 'SdsSecretName', ], ], ], 'ListenerTlsValidationContext' => [ 'type' => 'structure', 'required' => [ 'trust', ], 'members' => [ 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNames', ], 'trust' => [ 'shape' => 'ListenerTlsValidationContextTrust', ], ], ], 'ListenerTlsValidationContextTrust' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'TlsValidationContextFileTrust', ], 'sds' => [ 'shape' => 'TlsValidationContextSdsTrust', ], ], 'union' => true, ], 'Listeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'Listener', ], ], 'Logging' => [ 'type' => 'structure', 'members' => [ 'accessLog' => [ 'shape' => 'AccessLog', ], ], ], 'LoggingFormat' => [ 'type' => 'structure', 'members' => [ 'json' => [ 'shape' => 'JsonFormat', ], 'text' => [ 'shape' => 'TextFormat', ], ], 'union' => true, ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MatchRange' => [ 'type' => 'structure', 'required' => [ 'end', 'start', ], 'members' => [ 'end' => [ 'shape' => 'Long', ], 'start' => [ 'shape' => 'Long', ], ], ], 'MaxConnections' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MaxPendingRequests' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MaxRequests' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MaxRetries' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'MeshData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'spec', 'status', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'MeshSpec', ], 'status' => [ 'shape' => 'MeshStatus', ], ], ], 'MeshList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MeshRef', ], ], 'MeshRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], ], ], 'MeshServiceDiscovery' => [ 'type' => 'structure', 'members' => [ 'ipPreference' => [ 'shape' => 'IpPreference', ], ], ], 'MeshSpec' => [ 'type' => 'structure', 'members' => [ 'egressFilter' => [ 'shape' => 'EgressFilter', ], 'serviceDiscovery' => [ 'shape' => 'MeshServiceDiscovery', ], ], ], 'MeshStatus' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'MeshStatusCode', ], ], ], 'MeshStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'MethodName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OutlierDetection' => [ 'type' => 'structure', 'required' => [ 'baseEjectionDuration', 'interval', 'maxEjectionPercent', 'maxServerErrors', ], 'members' => [ 'baseEjectionDuration' => [ 'shape' => 'Duration', ], 'interval' => [ 'shape' => 'Duration', ], 'maxEjectionPercent' => [ 'shape' => 'OutlierDetectionMaxEjectionPercent', ], 'maxServerErrors' => [ 'shape' => 'OutlierDetectionMaxServerErrors', ], ], ], 'OutlierDetectionMaxEjectionPercent' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'OutlierDetectionMaxServerErrors' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'PercentInt' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'PortMapping' => [ 'type' => 'structure', 'required' => [ 'port', 'protocol', ], 'members' => [ 'port' => [ 'shape' => 'PortNumber', ], 'protocol' => [ 'shape' => 'PortProtocol', ], ], ], 'PortNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1, ], 'PortProtocol' => [ 'type' => 'string', 'enum' => [ 'http', 'tcp', 'http2', 'grpc', ], ], 'PortSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortNumber', ], ], 'QueryParameterMatch' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'String', ], ], ], 'QueryParameterName' => [ 'type' => 'string', ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResourceMetadata' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshOwner', 'resourceOwner', 'uid', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'uid' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'Long', ], ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'RouteData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'routeName', 'spec', 'status', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'routeName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'RouteSpec', ], 'status' => [ 'shape' => 'RouteStatus', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'RouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteRef', ], ], 'RoutePriority' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'RouteRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'routeName', 'version', 'virtualRouterName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'routeName' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'Long', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'RouteSpec' => [ 'type' => 'structure', 'members' => [ 'grpcRoute' => [ 'shape' => 'GrpcRoute', ], 'http2Route' => [ 'shape' => 'HttpRoute', ], 'httpRoute' => [ 'shape' => 'HttpRoute', ], 'priority' => [ 'shape' => 'RoutePriority', ], 'tcpRoute' => [ 'shape' => 'TcpRoute', ], ], ], 'RouteStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'RouteStatusCode', ], ], ], 'RouteStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'SdsSecretName' => [ 'type' => 'string', ], 'ServiceDiscovery' => [ 'type' => 'structure', 'members' => [ 'awsCloudMap' => [ 'shape' => 'AwsCloudMapServiceDiscovery', ], 'dns' => [ 'shape' => 'DnsServiceDiscovery', ], ], 'union' => true, ], 'ServiceName' => [ 'type' => 'string', ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'String' => [ 'type' => 'string', ], 'SubjectAlternativeName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'SubjectAlternativeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubjectAlternativeName', ], ], 'SubjectAlternativeNameMatchers' => [ 'type' => 'structure', 'required' => [ 'exact', ], 'members' => [ 'exact' => [ 'shape' => 'SubjectAlternativeNameList', ], ], ], 'SubjectAlternativeNames' => [ 'type' => 'structure', 'required' => [ 'match', ], 'members' => [ 'match' => [ 'shape' => 'SubjectAlternativeNameMatchers', ], ], ], 'SuffixHostname' => [ 'type' => 'string', 'max' => 253, 'min' => 1, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagRef', ], 'max' => 50, 'min' => 0, ], 'TagRef' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'TcpRetryPolicyEvent' => [ 'type' => 'string', 'enum' => [ 'connection-error', ], ], 'TcpRetryPolicyEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'TcpRetryPolicyEvent', ], 'max' => 1, 'min' => 1, ], 'TcpRoute' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'TcpRouteAction', ], 'match' => [ 'shape' => 'TcpRouteMatch', ], 'timeout' => [ 'shape' => 'TcpTimeout', ], ], ], 'TcpRouteAction' => [ 'type' => 'structure', 'required' => [ 'weightedTargets', ], 'members' => [ 'weightedTargets' => [ 'shape' => 'WeightedTargets', ], ], ], 'TcpRouteMatch' => [ 'type' => 'structure', 'members' => [ 'port' => [ 'shape' => 'ListenerPort', ], ], ], 'TcpTimeout' => [ 'type' => 'structure', 'members' => [ 'idle' => [ 'shape' => 'Duration', ], ], ], 'TextFormat' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TlsValidationContext' => [ 'type' => 'structure', 'required' => [ 'trust', ], 'members' => [ 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNames', ], 'trust' => [ 'shape' => 'TlsValidationContextTrust', ], ], ], 'TlsValidationContextAcmTrust' => [ 'type' => 'structure', 'required' => [ 'certificateAuthorityArns', ], 'members' => [ 'certificateAuthorityArns' => [ 'shape' => 'CertificateAuthorityArns', ], ], ], 'TlsValidationContextFileTrust' => [ 'type' => 'structure', 'required' => [ 'certificateChain', ], 'members' => [ 'certificateChain' => [ 'shape' => 'FilePath', ], ], ], 'TlsValidationContextSdsTrust' => [ 'type' => 'structure', 'required' => [ 'secretName', ], 'members' => [ 'secretName' => [ 'shape' => 'SdsSecretName', ], ], ], 'TlsValidationContextTrust' => [ 'type' => 'structure', 'members' => [ 'acm' => [ 'shape' => 'TlsValidationContextAcmTrust', ], 'file' => [ 'shape' => 'TlsValidationContextFileTrust', ], 'sds' => [ 'shape' => 'TlsValidationContextSdsTrust', ], ], 'union' => true, ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGatewayRouteInput' => [ 'type' => 'structure', 'required' => [ 'gatewayRouteName', 'meshName', 'spec', 'virtualGatewayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'gatewayRouteName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'gatewayRouteName', ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'GatewayRouteSpec', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'UpdateGatewayRouteOutput' => [ 'type' => 'structure', 'required' => [ 'gatewayRoute', ], 'members' => [ 'gatewayRoute' => [ 'shape' => 'GatewayRouteData', ], ], 'payload' => 'gatewayRoute', ], 'UpdateMeshInput' => [ 'type' => 'structure', 'required' => [ 'meshName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'spec' => [ 'shape' => 'MeshSpec', ], ], ], 'UpdateMeshOutput' => [ 'type' => 'structure', 'required' => [ 'mesh', ], 'members' => [ 'mesh' => [ 'shape' => 'MeshData', ], ], 'payload' => 'mesh', ], 'UpdateRouteInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'routeName', 'spec', 'virtualRouterName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'routeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'routeName', ], 'spec' => [ 'shape' => 'RouteSpec', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'UpdateRouteOutput' => [ 'type' => 'structure', 'required' => [ 'route', ], 'members' => [ 'route' => [ 'shape' => 'RouteData', ], ], 'payload' => 'route', ], 'UpdateVirtualGatewayInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualGatewayName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualGatewaySpec', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualGatewayName', ], ], ], 'UpdateVirtualGatewayOutput' => [ 'type' => 'structure', 'required' => [ 'virtualGateway', ], 'members' => [ 'virtualGateway' => [ 'shape' => 'VirtualGatewayData', ], ], 'payload' => 'virtualGateway', ], 'UpdateVirtualNodeInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualNodeName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualNodeSpec', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualNodeName', ], ], ], 'UpdateVirtualNodeOutput' => [ 'type' => 'structure', 'required' => [ 'virtualNode', ], 'members' => [ 'virtualNode' => [ 'shape' => 'VirtualNodeData', ], ], 'payload' => 'virtualNode', ], 'UpdateVirtualRouterInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualRouterName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualRouterSpec', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'virtualRouterName', ], ], ], 'UpdateVirtualRouterOutput' => [ 'type' => 'structure', 'required' => [ 'virtualRouter', ], 'members' => [ 'virtualRouter' => [ 'shape' => 'VirtualRouterData', ], ], 'payload' => 'virtualRouter', ], 'UpdateVirtualServiceInput' => [ 'type' => 'structure', 'required' => [ 'meshName', 'spec', 'virtualServiceName', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'meshName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'meshName', ], 'meshOwner' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'meshOwner', ], 'spec' => [ 'shape' => 'VirtualServiceSpec', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', 'location' => 'uri', 'locationName' => 'virtualServiceName', ], ], ], 'UpdateVirtualServiceOutput' => [ 'type' => 'structure', 'required' => [ 'virtualService', ], 'members' => [ 'virtualService' => [ 'shape' => 'VirtualServiceData', ], ], 'payload' => 'virtualService', ], 'VirtualGatewayAccessLog' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'VirtualGatewayFileAccessLog', ], ], 'union' => true, ], 'VirtualGatewayBackendDefaults' => [ 'type' => 'structure', 'members' => [ 'clientPolicy' => [ 'shape' => 'VirtualGatewayClientPolicy', ], ], ], 'VirtualGatewayCertificateAuthorityArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 3, 'min' => 1, ], 'VirtualGatewayClientPolicy' => [ 'type' => 'structure', 'members' => [ 'tls' => [ 'shape' => 'VirtualGatewayClientPolicyTls', ], ], ], 'VirtualGatewayClientPolicyTls' => [ 'type' => 'structure', 'required' => [ 'validation', ], 'members' => [ 'certificate' => [ 'shape' => 'VirtualGatewayClientTlsCertificate', ], 'enforce' => [ 'shape' => 'Boolean', ], 'ports' => [ 'shape' => 'PortSet', ], 'validation' => [ 'shape' => 'VirtualGatewayTlsValidationContext', ], ], ], 'VirtualGatewayClientTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'VirtualGatewayListenerTlsFileCertificate', ], 'sds' => [ 'shape' => 'VirtualGatewayListenerTlsSdsCertificate', ], ], 'union' => true, ], 'VirtualGatewayConnectionPool' => [ 'type' => 'structure', 'members' => [ 'grpc' => [ 'shape' => 'VirtualGatewayGrpcConnectionPool', ], 'http' => [ 'shape' => 'VirtualGatewayHttpConnectionPool', ], 'http2' => [ 'shape' => 'VirtualGatewayHttp2ConnectionPool', ], ], 'union' => true, ], 'VirtualGatewayData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'spec', 'status', 'virtualGatewayName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'VirtualGatewaySpec', ], 'status' => [ 'shape' => 'VirtualGatewayStatus', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualGatewayFileAccessLog' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'format' => [ 'shape' => 'LoggingFormat', ], 'path' => [ 'shape' => 'FilePath', ], ], ], 'VirtualGatewayGrpcConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxRequests', ], 'members' => [ 'maxRequests' => [ 'shape' => 'MaxRequests', ], ], ], 'VirtualGatewayHealthCheckIntervalMillis' => [ 'type' => 'long', 'box' => true, 'max' => 300000, 'min' => 5000, ], 'VirtualGatewayHealthCheckPolicy' => [ 'type' => 'structure', 'required' => [ 'healthyThreshold', 'intervalMillis', 'protocol', 'timeoutMillis', 'unhealthyThreshold', ], 'members' => [ 'healthyThreshold' => [ 'shape' => 'VirtualGatewayHealthCheckThreshold', ], 'intervalMillis' => [ 'shape' => 'VirtualGatewayHealthCheckIntervalMillis', ], 'path' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'PortNumber', ], 'protocol' => [ 'shape' => 'VirtualGatewayPortProtocol', ], 'timeoutMillis' => [ 'shape' => 'VirtualGatewayHealthCheckTimeoutMillis', ], 'unhealthyThreshold' => [ 'shape' => 'VirtualGatewayHealthCheckThreshold', ], ], ], 'VirtualGatewayHealthCheckThreshold' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 2, ], 'VirtualGatewayHealthCheckTimeoutMillis' => [ 'type' => 'long', 'box' => true, 'max' => 60000, 'min' => 2000, ], 'VirtualGatewayHttp2ConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxRequests', ], 'members' => [ 'maxRequests' => [ 'shape' => 'MaxRequests', ], ], ], 'VirtualGatewayHttpConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxConnections', ], 'members' => [ 'maxConnections' => [ 'shape' => 'MaxConnections', ], 'maxPendingRequests' => [ 'shape' => 'MaxPendingRequests', ], ], ], 'VirtualGatewayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualGatewayRef', ], ], 'VirtualGatewayListener' => [ 'type' => 'structure', 'required' => [ 'portMapping', ], 'members' => [ 'connectionPool' => [ 'shape' => 'VirtualGatewayConnectionPool', ], 'healthCheck' => [ 'shape' => 'VirtualGatewayHealthCheckPolicy', ], 'portMapping' => [ 'shape' => 'VirtualGatewayPortMapping', ], 'tls' => [ 'shape' => 'VirtualGatewayListenerTls', ], ], ], 'VirtualGatewayListenerTls' => [ 'type' => 'structure', 'required' => [ 'certificate', 'mode', ], 'members' => [ 'certificate' => [ 'shape' => 'VirtualGatewayListenerTlsCertificate', ], 'mode' => [ 'shape' => 'VirtualGatewayListenerTlsMode', ], 'validation' => [ 'shape' => 'VirtualGatewayListenerTlsValidationContext', ], ], ], 'VirtualGatewayListenerTlsAcmCertificate' => [ 'type' => 'structure', 'required' => [ 'certificateArn', ], 'members' => [ 'certificateArn' => [ 'shape' => 'Arn', ], ], ], 'VirtualGatewayListenerTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'acm' => [ 'shape' => 'VirtualGatewayListenerTlsAcmCertificate', ], 'file' => [ 'shape' => 'VirtualGatewayListenerTlsFileCertificate', ], 'sds' => [ 'shape' => 'VirtualGatewayListenerTlsSdsCertificate', ], ], 'union' => true, ], 'VirtualGatewayListenerTlsFileCertificate' => [ 'type' => 'structure', 'required' => [ 'certificateChain', 'privateKey', ], 'members' => [ 'certificateChain' => [ 'shape' => 'FilePath', ], 'privateKey' => [ 'shape' => 'FilePath', ], ], ], 'VirtualGatewayListenerTlsMode' => [ 'type' => 'string', 'enum' => [ 'STRICT', 'PERMISSIVE', 'DISABLED', ], ], 'VirtualGatewayListenerTlsSdsCertificate' => [ 'type' => 'structure', 'required' => [ 'secretName', ], 'members' => [ 'secretName' => [ 'shape' => 'VirtualGatewaySdsSecretName', ], ], ], 'VirtualGatewayListenerTlsValidationContext' => [ 'type' => 'structure', 'required' => [ 'trust', ], 'members' => [ 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNames', ], 'trust' => [ 'shape' => 'VirtualGatewayListenerTlsValidationContextTrust', ], ], ], 'VirtualGatewayListenerTlsValidationContextTrust' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'VirtualGatewayTlsValidationContextFileTrust', ], 'sds' => [ 'shape' => 'VirtualGatewayTlsValidationContextSdsTrust', ], ], 'union' => true, ], 'VirtualGatewayListeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualGatewayListener', ], ], 'VirtualGatewayLogging' => [ 'type' => 'structure', 'members' => [ 'accessLog' => [ 'shape' => 'VirtualGatewayAccessLog', ], ], ], 'VirtualGatewayPortMapping' => [ 'type' => 'structure', 'required' => [ 'port', 'protocol', ], 'members' => [ 'port' => [ 'shape' => 'PortNumber', ], 'protocol' => [ 'shape' => 'VirtualGatewayPortProtocol', ], ], ], 'VirtualGatewayPortProtocol' => [ 'type' => 'string', 'enum' => [ 'http', 'http2', 'grpc', ], ], 'VirtualGatewayRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', 'virtualGatewayName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], 'virtualGatewayName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualGatewaySdsSecretName' => [ 'type' => 'string', ], 'VirtualGatewaySpec' => [ 'type' => 'structure', 'required' => [ 'listeners', ], 'members' => [ 'backendDefaults' => [ 'shape' => 'VirtualGatewayBackendDefaults', ], 'listeners' => [ 'shape' => 'VirtualGatewayListeners', ], 'logging' => [ 'shape' => 'VirtualGatewayLogging', ], ], ], 'VirtualGatewayStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'VirtualGatewayStatusCode', ], ], ], 'VirtualGatewayStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'VirtualGatewayTlsValidationContext' => [ 'type' => 'structure', 'required' => [ 'trust', ], 'members' => [ 'subjectAlternativeNames' => [ 'shape' => 'SubjectAlternativeNames', ], 'trust' => [ 'shape' => 'VirtualGatewayTlsValidationContextTrust', ], ], ], 'VirtualGatewayTlsValidationContextAcmTrust' => [ 'type' => 'structure', 'required' => [ 'certificateAuthorityArns', ], 'members' => [ 'certificateAuthorityArns' => [ 'shape' => 'VirtualGatewayCertificateAuthorityArns', ], ], ], 'VirtualGatewayTlsValidationContextFileTrust' => [ 'type' => 'structure', 'required' => [ 'certificateChain', ], 'members' => [ 'certificateChain' => [ 'shape' => 'FilePath', ], ], ], 'VirtualGatewayTlsValidationContextSdsTrust' => [ 'type' => 'structure', 'required' => [ 'secretName', ], 'members' => [ 'secretName' => [ 'shape' => 'VirtualGatewaySdsSecretName', ], ], ], 'VirtualGatewayTlsValidationContextTrust' => [ 'type' => 'structure', 'members' => [ 'acm' => [ 'shape' => 'VirtualGatewayTlsValidationContextAcmTrust', ], 'file' => [ 'shape' => 'VirtualGatewayTlsValidationContextFileTrust', ], 'sds' => [ 'shape' => 'VirtualGatewayTlsValidationContextSdsTrust', ], ], 'union' => true, ], 'VirtualNodeConnectionPool' => [ 'type' => 'structure', 'members' => [ 'grpc' => [ 'shape' => 'VirtualNodeGrpcConnectionPool', ], 'http' => [ 'shape' => 'VirtualNodeHttpConnectionPool', ], 'http2' => [ 'shape' => 'VirtualNodeHttp2ConnectionPool', ], 'tcp' => [ 'shape' => 'VirtualNodeTcpConnectionPool', ], ], 'union' => true, ], 'VirtualNodeData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'spec', 'status', 'virtualNodeName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'VirtualNodeSpec', ], 'status' => [ 'shape' => 'VirtualNodeStatus', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualNodeGrpcConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxRequests', ], 'members' => [ 'maxRequests' => [ 'shape' => 'MaxRequests', ], ], ], 'VirtualNodeHttp2ConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxRequests', ], 'members' => [ 'maxRequests' => [ 'shape' => 'MaxRequests', ], ], ], 'VirtualNodeHttpConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxConnections', ], 'members' => [ 'maxConnections' => [ 'shape' => 'MaxConnections', ], 'maxPendingRequests' => [ 'shape' => 'MaxPendingRequests', ], ], ], 'VirtualNodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualNodeRef', ], ], 'VirtualNodeRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', 'virtualNodeName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], 'virtualNodeName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualNodeServiceProvider' => [ 'type' => 'structure', 'required' => [ 'virtualNodeName', ], 'members' => [ 'virtualNodeName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualNodeSpec' => [ 'type' => 'structure', 'members' => [ 'backendDefaults' => [ 'shape' => 'BackendDefaults', ], 'backends' => [ 'shape' => 'Backends', ], 'listeners' => [ 'shape' => 'Listeners', ], 'logging' => [ 'shape' => 'Logging', ], 'serviceDiscovery' => [ 'shape' => 'ServiceDiscovery', ], ], ], 'VirtualNodeStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'VirtualNodeStatusCode', ], ], ], 'VirtualNodeStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'VirtualNodeTcpConnectionPool' => [ 'type' => 'structure', 'required' => [ 'maxConnections', ], 'members' => [ 'maxConnections' => [ 'shape' => 'MaxConnections', ], ], ], 'VirtualRouterData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'spec', 'status', 'virtualRouterName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'VirtualRouterSpec', ], 'status' => [ 'shape' => 'VirtualRouterStatus', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualRouterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualRouterRef', ], ], 'VirtualRouterListener' => [ 'type' => 'structure', 'required' => [ 'portMapping', ], 'members' => [ 'portMapping' => [ 'shape' => 'PortMapping', ], ], ], 'VirtualRouterListeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualRouterListener', ], ], 'VirtualRouterRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', 'virtualRouterName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualRouterServiceProvider' => [ 'type' => 'structure', 'required' => [ 'virtualRouterName', ], 'members' => [ 'virtualRouterName' => [ 'shape' => 'ResourceName', ], ], ], 'VirtualRouterSpec' => [ 'type' => 'structure', 'members' => [ 'listeners' => [ 'shape' => 'VirtualRouterListeners', ], ], ], 'VirtualRouterStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'VirtualRouterStatusCode', ], ], ], 'VirtualRouterStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'VirtualServiceBackend' => [ 'type' => 'structure', 'required' => [ 'virtualServiceName', ], 'members' => [ 'clientPolicy' => [ 'shape' => 'ClientPolicy', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', ], ], ], 'VirtualServiceData' => [ 'type' => 'structure', 'required' => [ 'meshName', 'metadata', 'spec', 'status', 'virtualServiceName', ], 'members' => [ 'meshName' => [ 'shape' => 'ResourceName', ], 'metadata' => [ 'shape' => 'ResourceMetadata', ], 'spec' => [ 'shape' => 'VirtualServiceSpec', ], 'status' => [ 'shape' => 'VirtualServiceStatus', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', ], ], ], 'VirtualServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualServiceRef', ], ], 'VirtualServiceProvider' => [ 'type' => 'structure', 'members' => [ 'virtualNode' => [ 'shape' => 'VirtualNodeServiceProvider', ], 'virtualRouter' => [ 'shape' => 'VirtualRouterServiceProvider', ], ], 'union' => true, ], 'VirtualServiceRef' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastUpdatedAt', 'meshName', 'meshOwner', 'resourceOwner', 'version', 'virtualServiceName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'meshName' => [ 'shape' => 'ResourceName', ], 'meshOwner' => [ 'shape' => 'AccountId', ], 'resourceOwner' => [ 'shape' => 'AccountId', ], 'version' => [ 'shape' => 'Long', ], 'virtualServiceName' => [ 'shape' => 'ServiceName', ], ], ], 'VirtualServiceSpec' => [ 'type' => 'structure', 'members' => [ 'provider' => [ 'shape' => 'VirtualServiceProvider', ], ], ], 'VirtualServiceStatus' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'VirtualServiceStatusCode', ], ], ], 'VirtualServiceStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETED', ], ], 'WeightedTarget' => [ 'type' => 'structure', 'required' => [ 'virtualNode', 'weight', ], 'members' => [ 'port' => [ 'shape' => 'ListenerPort', ], 'virtualNode' => [ 'shape' => 'ResourceName', ], 'weight' => [ 'shape' => 'PercentInt', ], ], ], 'WeightedTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'WeightedTarget', ], 'max' => 10, 'min' => 1, ], ],];
