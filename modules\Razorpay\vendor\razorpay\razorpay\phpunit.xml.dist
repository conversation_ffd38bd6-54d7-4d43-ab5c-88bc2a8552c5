<?xml version="1.0" encoding="UTF-8"?>
<!-- http://www.phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit
        backupGlobals               = "false"
        backupStaticAttributes      = "false"
        colors                      = "true"
        convertErrorsToExceptions   = "true"
        convertNoticesToExceptions  = "true"
        convertWarningsToExceptions = "true"
        processIsolation            = "false"
        stopOnFailure               = "false"
        syntaxCheck                 = "false"
        bootstrap                   = "tests/bootstrap.php" >

    <php>
        <!-- copy this file to phpunit.xml and replace with your API key to run tests -->
        <!-- Also uncomment the following two lines-->
        <!--<server name="KEY_ID" value="" />
        <server name="KEY_SECRET" value="" />
        -->
    </php>

    <testsuites>
        <testsuite>
            <directory>./non_composer_tests/</directory>
        </testsuite>
        <testsuite>
            <directory>./tests</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist>
            <directory>./src</directory>
            <exclude>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
