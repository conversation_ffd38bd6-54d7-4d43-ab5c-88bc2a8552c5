{"name": "filament/notifications", "description": "Easily add beautiful notifications to any Livewire app.", "license": "MIT", "homepage": "https://github.com/filamentphp/filament", "support": {"issues": "https://github.com/filamentphp/filament/issues", "source": "https://github.com/filamentphp/filament"}, "require": {"php": "^8.1", "filament/actions": "self.version", "filament/support": "self.version", "illuminate/contracts": "^10.45|^11.0", "illuminate/filesystem": "^10.45|^11.0", "illuminate/notifications": "^10.45|^11.0", "illuminate/support": "^10.45|^11.0", "spatie/laravel-package-tools": "^1.9"}, "autoload": {"psr-4": {"Filament\\Notifications\\": "src"}, "files": ["src/Testing/Autoload.php"]}, "extra": {"laravel": {"providers": ["Filament\\Notifications\\NotificationsServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}