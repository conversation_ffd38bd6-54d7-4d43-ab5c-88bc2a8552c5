<?php
// This file was auto-generated from sdk-root/src/data/tnb/2008-10-21/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2008-10-21', 'endpointPrefix' => 'tnb', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Telco Network Builder', 'serviceId' => 'tnb', 'signatureVersion' => 'v4', 'signingName' => 'tnb', 'uid' => 'tnb-2008-10-21', ], 'operations' => [ 'CancelSolNetworkOperation' => [ 'name' => 'CancelSolNetworkOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nslcm/v1/ns_lcm_op_occs/{nsLcmOpOccId}/cancel', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CancelSolNetworkOperationInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateSolFunctionPackage' => [ 'name' => 'CreateSolFunctionPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSolFunctionPackageInput', ], 'output' => [ 'shape' => 'CreateSolFunctionPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateSolNetworkInstance' => [ 'name' => 'CreateSolNetworkInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nslcm/v1/ns_instances', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSolNetworkInstanceInput', ], 'output' => [ 'shape' => 'CreateSolNetworkInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateSolNetworkPackage' => [ 'name' => 'CreateSolNetworkPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nsd/v1/ns_descriptors', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSolNetworkPackageInput', ], 'output' => [ 'shape' => 'CreateSolNetworkPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteSolFunctionPackage' => [ 'name' => 'DeleteSolFunctionPackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSolFunctionPackageInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSolNetworkInstance' => [ 'name' => 'DeleteSolNetworkInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sol/nslcm/v1/ns_instances/{nsInstanceId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSolNetworkInstanceInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSolNetworkPackage' => [ 'name' => 'DeleteSolNetworkPackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSolNetworkPackageInput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetSolFunctionInstance' => [ 'name' => 'GetSolFunctionInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnflcm/v1/vnf_instances/{vnfInstanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolFunctionInstanceInput', ], 'output' => [ 'shape' => 'GetSolFunctionInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolFunctionPackage' => [ 'name' => 'GetSolFunctionPackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolFunctionPackageInput', ], 'output' => [ 'shape' => 'GetSolFunctionPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolFunctionPackageContent' => [ 'name' => 'GetSolFunctionPackageContent', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}/package_content', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolFunctionPackageContentInput', ], 'output' => [ 'shape' => 'GetSolFunctionPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolFunctionPackageDescriptor' => [ 'name' => 'GetSolFunctionPackageDescriptor', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}/vnfd', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolFunctionPackageDescriptorInput', ], 'output' => [ 'shape' => 'GetSolFunctionPackageDescriptorOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolNetworkInstance' => [ 'name' => 'GetSolNetworkInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nslcm/v1/ns_instances/{nsInstanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolNetworkInstanceInput', ], 'output' => [ 'shape' => 'GetSolNetworkInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolNetworkOperation' => [ 'name' => 'GetSolNetworkOperation', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nslcm/v1/ns_lcm_op_occs/{nsLcmOpOccId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolNetworkOperationInput', ], 'output' => [ 'shape' => 'GetSolNetworkOperationOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolNetworkPackage' => [ 'name' => 'GetSolNetworkPackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolNetworkPackageInput', ], 'output' => [ 'shape' => 'GetSolNetworkPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolNetworkPackageContent' => [ 'name' => 'GetSolNetworkPackageContent', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}/nsd_content', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolNetworkPackageContentInput', ], 'output' => [ 'shape' => 'GetSolNetworkPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSolNetworkPackageDescriptor' => [ 'name' => 'GetSolNetworkPackageDescriptor', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}/nsd', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSolNetworkPackageDescriptorInput', ], 'output' => [ 'shape' => 'GetSolNetworkPackageDescriptorOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'InstantiateSolNetworkInstance' => [ 'name' => 'InstantiateSolNetworkInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nslcm/v1/ns_instances/{nsInstanceId}/instantiate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'InstantiateSolNetworkInstanceInput', ], 'output' => [ 'shape' => 'InstantiateSolNetworkInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSolFunctionInstances' => [ 'name' => 'ListSolFunctionInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnflcm/v1/vnf_instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSolFunctionInstancesInput', ], 'output' => [ 'shape' => 'ListSolFunctionInstancesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSolFunctionPackages' => [ 'name' => 'ListSolFunctionPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSolFunctionPackagesInput', ], 'output' => [ 'shape' => 'ListSolFunctionPackagesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSolNetworkInstances' => [ 'name' => 'ListSolNetworkInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nslcm/v1/ns_instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSolNetworkInstancesInput', ], 'output' => [ 'shape' => 'ListSolNetworkInstancesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSolNetworkOperations' => [ 'name' => 'ListSolNetworkOperations', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nslcm/v1/ns_lcm_op_occs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSolNetworkOperationsInput', ], 'output' => [ 'shape' => 'ListSolNetworkOperationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSolNetworkPackages' => [ 'name' => 'ListSolNetworkPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/sol/nsd/v1/ns_descriptors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSolNetworkPackagesInput', ], 'output' => [ 'shape' => 'ListSolNetworkPackagesOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutSolFunctionPackageContent' => [ 'name' => 'PutSolFunctionPackageContent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}/package_content', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PutSolFunctionPackageContentInput', ], 'output' => [ 'shape' => 'PutSolFunctionPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'PutSolNetworkPackageContent' => [ 'name' => 'PutSolNetworkPackageContent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}/nsd_content', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutSolNetworkPackageContentInput', ], 'output' => [ 'shape' => 'PutSolNetworkPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'TerminateSolNetworkInstance' => [ 'name' => 'TerminateSolNetworkInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nslcm/v1/ns_instances/{nsInstanceId}/terminate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'TerminateSolNetworkInstanceInput', ], 'output' => [ 'shape' => 'TerminateSolNetworkInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateSolFunctionPackage' => [ 'name' => 'UpdateSolFunctionPackage', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSolFunctionPackageInput', ], 'output' => [ 'shape' => 'UpdateSolFunctionPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateSolNetworkInstance' => [ 'name' => 'UpdateSolNetworkInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/sol/nslcm/v1/ns_instances/{nsInstanceId}/update', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateSolNetworkInstanceInput', ], 'output' => [ 'shape' => 'UpdateSolNetworkInstanceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateSolNetworkPackage' => [ 'name' => 'UpdateSolNetworkPackage', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSolNetworkPackageInput', ], 'output' => [ 'shape' => 'UpdateSolNetworkPackageOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ValidateSolFunctionPackageContent' => [ 'name' => 'ValidateSolFunctionPackageContent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sol/vnfpkgm/v1/vnf_packages/{vnfPkgId}/package_content/validate', 'responseCode' => 202, ], 'input' => [ 'shape' => 'ValidateSolFunctionPackageContentInput', ], 'output' => [ 'shape' => 'ValidateSolFunctionPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ValidateSolNetworkPackageContent' => [ 'name' => 'ValidateSolNetworkPackageContent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sol/nsd/v1/ns_descriptors/{nsdInfoId}/nsd_content/validate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ValidateSolNetworkPackageContentInput', ], 'output' => [ 'shape' => 'ValidateSolNetworkPackageContentOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelSolNetworkOperationInput' => [ 'type' => 'structure', 'required' => [ 'nsLcmOpOccId', ], 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', 'location' => 'uri', 'locationName' => 'nsLcmOpOccId', ], ], ], 'CreateSolFunctionPackageInput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSolFunctionPackageOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'onboardingState', 'operationalState', 'usageState', ], 'members' => [ 'arn' => [ 'shape' => 'VnfPkgArn', ], 'id' => [ 'shape' => 'VnfPkgId', ], 'onboardingState' => [ 'shape' => 'OnboardingState', ], 'operationalState' => [ 'shape' => 'OperationalState', ], 'tags' => [ 'shape' => 'TagMap', ], 'usageState' => [ 'shape' => 'UsageState', ], ], ], 'CreateSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsName', 'nsdInfoId', ], 'members' => [ 'nsDescription' => [ 'shape' => 'CreateSolNetworkInstanceInputNsDescriptionString', ], 'nsName' => [ 'shape' => 'CreateSolNetworkInstanceInputNsNameString', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSolNetworkInstanceInputNsDescriptionString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'CreateSolNetworkInstanceInputNsNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CreateSolNetworkInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'nsInstanceName', 'nsdInfoId', ], 'members' => [ 'arn' => [ 'shape' => 'NsInstanceArn', ], 'id' => [ 'shape' => 'NsInstanceId', ], 'nsInstanceName' => [ 'shape' => 'String', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSolNetworkPackageInput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSolNetworkPackageOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'nsdOnboardingState', 'nsdOperationalState', 'nsdUsageState', ], 'members' => [ 'arn' => [ 'shape' => 'NsdInfoArn', ], 'id' => [ 'shape' => 'NsdInfoId', ], 'nsdOnboardingState' => [ 'shape' => 'NsdOnboardingState', ], 'nsdOperationalState' => [ 'shape' => 'NsdOperationalState', ], 'nsdUsageState' => [ 'shape' => 'NsdUsageState', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DeleteSolFunctionPackageInput' => [ 'type' => 'structure', 'required' => [ 'vnfPkgId', ], 'members' => [ 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], ], 'DeleteSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsInstanceId', ], 'members' => [ 'nsInstanceId' => [ 'shape' => 'NsInstanceId', 'location' => 'uri', 'locationName' => 'nsInstanceId', ], ], ], 'DeleteSolNetworkPackageInput' => [ 'type' => 'structure', 'required' => [ 'nsdInfoId', ], 'members' => [ 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], ], 'DescriptorContentType' => [ 'type' => 'string', 'enum' => [ 'text/plain', ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ErrorCause' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'ErrorDetails' => [ 'type' => 'string', 'max' => 10240, 'min' => 0, ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'cause' => [ 'shape' => 'ErrorCause', ], 'details' => [ 'shape' => 'ErrorDetails', ], ], ], 'FunctionArtifactMeta' => [ 'type' => 'structure', 'members' => [ 'overrides' => [ 'shape' => 'OverrideList', ], ], ], 'GetSolFunctionInstanceInput' => [ 'type' => 'structure', 'required' => [ 'vnfInstanceId', ], 'members' => [ 'vnfInstanceId' => [ 'shape' => 'VnfInstanceId', 'location' => 'uri', 'locationName' => 'vnfInstanceId', ], ], ], 'GetSolFunctionInstanceMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetSolFunctionInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'instantiationState', 'metadata', 'nsInstanceId', 'vnfPkgId', 'vnfdId', ], 'members' => [ 'arn' => [ 'shape' => 'VnfInstanceArn', ], 'id' => [ 'shape' => 'VnfInstanceId', ], 'instantiatedVnfInfo' => [ 'shape' => 'GetSolVnfInfo', ], 'instantiationState' => [ 'shape' => 'VnfInstantiationState', ], 'metadata' => [ 'shape' => 'GetSolFunctionInstanceMetadata', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', ], 'tags' => [ 'shape' => 'TagMap', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', ], 'vnfProductName' => [ 'shape' => 'String', ], 'vnfProvider' => [ 'shape' => 'String', ], 'vnfdId' => [ 'shape' => 'VnfdId', ], 'vnfdVersion' => [ 'shape' => 'String', ], ], ], 'GetSolFunctionPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'accept', 'vnfPkgId', ], 'members' => [ 'accept' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Accept', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], ], 'GetSolFunctionPackageContentOutput' => [ 'type' => 'structure', 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'packageContent' => [ 'shape' => 'Blob', ], ], 'payload' => 'packageContent', ], 'GetSolFunctionPackageDescriptorInput' => [ 'type' => 'structure', 'required' => [ 'accept', 'vnfPkgId', ], 'members' => [ 'accept' => [ 'shape' => 'DescriptorContentType', 'location' => 'header', 'locationName' => 'Accept', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], ], 'GetSolFunctionPackageDescriptorOutput' => [ 'type' => 'structure', 'members' => [ 'contentType' => [ 'shape' => 'DescriptorContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'vnfd' => [ 'shape' => 'Blob', ], ], 'payload' => 'vnfd', ], 'GetSolFunctionPackageInput' => [ 'type' => 'structure', 'required' => [ 'vnfPkgId', ], 'members' => [ 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], ], 'GetSolFunctionPackageMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'vnfd' => [ 'shape' => 'FunctionArtifactMeta', ], ], ], 'GetSolFunctionPackageOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'onboardingState', 'operationalState', 'usageState', ], 'members' => [ 'arn' => [ 'shape' => 'VnfPkgArn', ], 'id' => [ 'shape' => 'VnfPkgId', ], 'metadata' => [ 'shape' => 'GetSolFunctionPackageMetadata', ], 'onboardingState' => [ 'shape' => 'OnboardingState', ], 'operationalState' => [ 'shape' => 'OperationalState', ], 'tags' => [ 'shape' => 'TagMap', ], 'usageState' => [ 'shape' => 'UsageState', ], 'vnfProductName' => [ 'shape' => 'String', ], 'vnfProvider' => [ 'shape' => 'String', ], 'vnfdId' => [ 'shape' => 'String', ], 'vnfdVersion' => [ 'shape' => 'String', ], ], ], 'GetSolInstantiatedVnfInfo' => [ 'type' => 'structure', 'members' => [ 'vnfState' => [ 'shape' => 'VnfOperationalState', ], ], ], 'GetSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsInstanceId', ], 'members' => [ 'nsInstanceId' => [ 'shape' => 'NsInstanceId', 'location' => 'uri', 'locationName' => 'nsInstanceId', ], ], ], 'GetSolNetworkInstanceMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetSolNetworkInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsInstanceDescription', 'nsInstanceName', 'nsdId', 'nsdInfoId', ], 'members' => [ 'arn' => [ 'shape' => 'NsInstanceArn', ], 'id' => [ 'shape' => 'NsInstanceId', ], 'lcmOpInfo' => [ 'shape' => 'LcmOperationInfo', ], 'metadata' => [ 'shape' => 'GetSolNetworkInstanceMetadata', ], 'nsInstanceDescription' => [ 'shape' => 'String', ], 'nsInstanceName' => [ 'shape' => 'String', ], 'nsState' => [ 'shape' => 'NsState', ], 'nsdId' => [ 'shape' => 'NsdId', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetSolNetworkOperationInput' => [ 'type' => 'structure', 'required' => [ 'nsLcmOpOccId', ], 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', 'location' => 'uri', 'locationName' => 'nsLcmOpOccId', ], ], ], 'GetSolNetworkOperationMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetSolNetworkOperationOutput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NsLcmOpOccArn', ], 'error' => [ 'shape' => 'ProblemDetails', ], 'id' => [ 'shape' => 'NsLcmOpOccId', ], 'lcmOperationType' => [ 'shape' => 'LcmOperationType', ], 'metadata' => [ 'shape' => 'GetSolNetworkOperationMetadata', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', ], 'operationState' => [ 'shape' => 'NsLcmOperationState', ], 'tags' => [ 'shape' => 'TagMap', ], 'tasks' => [ 'shape' => 'GetSolNetworkOperationTasksList', ], ], ], 'GetSolNetworkOperationTaskDetails' => [ 'type' => 'structure', 'members' => [ 'taskContext' => [ 'shape' => 'StringMap', ], 'taskEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'taskErrorDetails' => [ 'shape' => 'ErrorInfo', ], 'taskName' => [ 'shape' => 'String', ], 'taskStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'taskStatus' => [ 'shape' => 'TaskStatus', ], ], ], 'GetSolNetworkOperationTasksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetSolNetworkOperationTaskDetails', ], ], 'GetSolNetworkPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'accept', 'nsdInfoId', ], 'members' => [ 'accept' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Accept', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], ], 'GetSolNetworkPackageContentOutput' => [ 'type' => 'structure', 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'nsdContent' => [ 'shape' => 'Blob', ], ], 'payload' => 'nsdContent', ], 'GetSolNetworkPackageDescriptorInput' => [ 'type' => 'structure', 'required' => [ 'nsdInfoId', ], 'members' => [ 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], ], 'GetSolNetworkPackageDescriptorOutput' => [ 'type' => 'structure', 'members' => [ 'contentType' => [ 'shape' => 'DescriptorContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'nsd' => [ 'shape' => 'Blob', ], ], 'payload' => 'nsd', ], 'GetSolNetworkPackageInput' => [ 'type' => 'structure', 'required' => [ 'nsdInfoId', ], 'members' => [ 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], ], 'GetSolNetworkPackageMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'nsd' => [ 'shape' => 'NetworkArtifactMeta', ], ], ], 'GetSolNetworkPackageOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsdId', 'nsdName', 'nsdOnboardingState', 'nsdOperationalState', 'nsdUsageState', 'nsdVersion', 'vnfPkgIds', ], 'members' => [ 'arn' => [ 'shape' => 'NsdInfoArn', ], 'id' => [ 'shape' => 'NsdInfoId', ], 'metadata' => [ 'shape' => 'GetSolNetworkPackageMetadata', ], 'nsdId' => [ 'shape' => 'NsdId', ], 'nsdName' => [ 'shape' => 'String', ], 'nsdOnboardingState' => [ 'shape' => 'NsdOnboardingState', ], 'nsdOperationalState' => [ 'shape' => 'NsdOperationalState', ], 'nsdUsageState' => [ 'shape' => 'NsdUsageState', ], 'nsdVersion' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'vnfPkgIds' => [ 'shape' => 'VnfPkgIdList', ], ], ], 'GetSolVnfInfo' => [ 'type' => 'structure', 'members' => [ 'vnfState' => [ 'shape' => 'VnfOperationalState', ], 'vnfcResourceInfo' => [ 'shape' => 'GetSolVnfcResourceInfoList', ], ], ], 'GetSolVnfcResourceInfo' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'GetSolVnfcResourceInfoMetadata', ], ], ], 'GetSolVnfcResourceInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetSolVnfcResourceInfo', ], ], 'GetSolVnfcResourceInfoMetadata' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'String', ], 'helmChart' => [ 'shape' => 'String', ], 'nodeGroup' => [ 'shape' => 'String', ], ], ], 'InstantiateSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsInstanceId', ], 'members' => [ 'additionalParamsForNs' => [ 'shape' => 'Document', ], 'dryRun' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'dry_run', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', 'location' => 'uri', 'locationName' => 'nsInstanceId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'InstantiateSolNetworkInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'nsLcmOpOccId', ], 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LcmOperationInfo' => [ 'type' => 'structure', 'required' => [ 'nsLcmOpOccId', ], 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', ], ], ], 'LcmOperationType' => [ 'type' => 'string', 'enum' => [ 'INSTANTIATE', 'UPDATE', 'TERMINATE', ], ], 'ListSolFunctionInstanceInfo' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'instantiationState', 'metadata', 'nsInstanceId', 'vnfPkgId', ], 'members' => [ 'arn' => [ 'shape' => 'VnfInstanceArn', ], 'id' => [ 'shape' => 'VnfInstanceId', ], 'instantiatedVnfInfo' => [ 'shape' => 'GetSolInstantiatedVnfInfo', ], 'instantiationState' => [ 'shape' => 'VnfInstantiationState', ], 'metadata' => [ 'shape' => 'ListSolFunctionInstanceMetadata', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', ], 'vnfPkgName' => [ 'shape' => 'String', ], ], ], 'ListSolFunctionInstanceMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ListSolFunctionInstanceResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSolFunctionInstanceInfo', ], ], 'ListSolFunctionInstancesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSolFunctionInstancesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'max_results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextpage_opaque_marker', ], ], ], 'ListSolFunctionInstancesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSolFunctionInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'functionInstances' => [ 'shape' => 'ListSolFunctionInstanceResources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSolFunctionPackageInfo' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'onboardingState', 'operationalState', 'usageState', ], 'members' => [ 'arn' => [ 'shape' => 'VnfPkgArn', ], 'id' => [ 'shape' => 'VnfPkgId', ], 'metadata' => [ 'shape' => 'ListSolFunctionPackageMetadata', ], 'onboardingState' => [ 'shape' => 'OnboardingState', ], 'operationalState' => [ 'shape' => 'OperationalState', ], 'usageState' => [ 'shape' => 'UsageState', ], 'vnfProductName' => [ 'shape' => 'String', ], 'vnfProvider' => [ 'shape' => 'String', ], 'vnfdId' => [ 'shape' => 'String', ], 'vnfdVersion' => [ 'shape' => 'String', ], ], ], 'ListSolFunctionPackageMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ListSolFunctionPackageResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSolFunctionPackageInfo', ], ], 'ListSolFunctionPackagesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSolFunctionPackagesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'max_results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextpage_opaque_marker', ], ], ], 'ListSolFunctionPackagesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSolFunctionPackagesOutput' => [ 'type' => 'structure', 'required' => [ 'functionPackages', ], 'members' => [ 'functionPackages' => [ 'shape' => 'ListSolFunctionPackageResources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSolNetworkInstanceInfo' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsInstanceDescription', 'nsInstanceName', 'nsState', 'nsdId', 'nsdInfoId', ], 'members' => [ 'arn' => [ 'shape' => 'NsInstanceArn', ], 'id' => [ 'shape' => 'NsInstanceId', ], 'metadata' => [ 'shape' => 'ListSolNetworkInstanceMetadata', ], 'nsInstanceDescription' => [ 'shape' => 'String', ], 'nsInstanceName' => [ 'shape' => 'String', ], 'nsState' => [ 'shape' => 'NsState', ], 'nsdId' => [ 'shape' => 'NsdId', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', ], ], ], 'ListSolNetworkInstanceMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ListSolNetworkInstanceResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSolNetworkInstanceInfo', ], ], 'ListSolNetworkInstancesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSolNetworkInstancesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'max_results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextpage_opaque_marker', ], ], ], 'ListSolNetworkInstancesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSolNetworkInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'networkInstances' => [ 'shape' => 'ListSolNetworkInstanceResources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSolNetworkOperationsInfo' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'lcmOperationType', 'nsInstanceId', 'operationState', ], 'members' => [ 'arn' => [ 'shape' => 'NsLcmOpOccArn', ], 'error' => [ 'shape' => 'ProblemDetails', ], 'id' => [ 'shape' => 'NsLcmOpOccId', ], 'lcmOperationType' => [ 'shape' => 'LcmOperationType', ], 'metadata' => [ 'shape' => 'ListSolNetworkOperationsMetadata', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', ], 'operationState' => [ 'shape' => 'NsLcmOperationState', ], ], ], 'ListSolNetworkOperationsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSolNetworkOperationsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'max_results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextpage_opaque_marker', ], ], ], 'ListSolNetworkOperationsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSolNetworkOperationsMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ListSolNetworkOperationsOutput' => [ 'type' => 'structure', 'members' => [ 'networkOperations' => [ 'shape' => 'ListSolNetworkOperationsResources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListSolNetworkOperationsResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSolNetworkOperationsInfo', ], ], 'ListSolNetworkPackageInfo' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsdOnboardingState', 'nsdOperationalState', 'nsdUsageState', ], 'members' => [ 'arn' => [ 'shape' => 'NsdInfoArn', ], 'id' => [ 'shape' => 'NsdInfoId', ], 'metadata' => [ 'shape' => 'ListSolNetworkPackageMetadata', ], 'nsdDesigner' => [ 'shape' => 'String', ], 'nsdId' => [ 'shape' => 'String', ], 'nsdInvariantId' => [ 'shape' => 'String', ], 'nsdName' => [ 'shape' => 'String', ], 'nsdOnboardingState' => [ 'shape' => 'NsdOnboardingState', ], 'nsdOperationalState' => [ 'shape' => 'NsdOperationalState', ], 'nsdUsageState' => [ 'shape' => 'NsdUsageState', ], 'nsdVersion' => [ 'shape' => 'String', ], 'vnfPkgIds' => [ 'shape' => 'VnfPkgIdList', ], ], ], 'ListSolNetworkPackageMetadata' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastModified', ], 'members' => [ 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModified' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ListSolNetworkPackageResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSolNetworkPackageInfo', ], ], 'ListSolNetworkPackagesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListSolNetworkPackagesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'max_results', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextpage_opaque_marker', ], ], ], 'ListSolNetworkPackagesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListSolNetworkPackagesOutput' => [ 'type' => 'structure', 'required' => [ 'networkPackages', ], 'members' => [ 'networkPackages' => [ 'shape' => 'ListSolNetworkPackageResources', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TNBResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'NetworkArtifactMeta' => [ 'type' => 'structure', 'members' => [ 'overrides' => [ 'shape' => 'OverrideList', ], ], ], 'NsInstanceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-b|aws-us-gov):tnb:([a-z]{2}(-(gov|isob|iso))?-(east|west|north|south|central){1,2}-[0-9]):\\d{12}:(network-instance/ni-[a-f0-9]{17})$', ], 'NsInstanceId' => [ 'type' => 'string', 'pattern' => '^ni-[a-f0-9]{17}$', ], 'NsLcmOpOccArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-b|aws-us-gov):tnb:([a-z]{2}(-(gov|isob|iso))?-(east|west|north|south|central){1,2}-[0-9]):\\d{12}:(network-operation/no-[a-f0-9]{17})$', ], 'NsLcmOpOccId' => [ 'type' => 'string', 'pattern' => '^no-[a-f0-9]{17}$', ], 'NsLcmOperationState' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLING', 'CANCELLED', ], ], 'NsState' => [ 'type' => 'string', 'enum' => [ 'INSTANTIATED', 'NOT_INSTANTIATED', 'IMPAIRED', 'STOPPED', 'DELETED', 'INSTANTIATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'TERMINATE_IN_PROGRESS', ], ], 'NsdId' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'NsdInfoArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-b|aws-us-gov):tnb:([a-z]{2}(-(gov|isob|iso))?-(east|west|north|south|central){1,2}-[0-9]):\\d{12}:(network-package/np-[a-f0-9]{17})$', ], 'NsdInfoId' => [ 'type' => 'string', 'pattern' => '^np-[a-f0-9]{17}$', ], 'NsdOnboardingState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'ONBOARDED', 'ERROR', ], ], 'NsdOperationalState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'NsdUsageState' => [ 'type' => 'string', 'enum' => [ 'IN_USE', 'NOT_IN_USE', ], ], 'OnboardingState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'ONBOARDED', 'ERROR', ], ], 'OperationalState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'OverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToscaOverride', ], ], 'PackageContentType' => [ 'type' => 'string', 'enum' => [ 'application/zip', ], ], 'PaginationToken' => [ 'type' => 'string', ], 'ProblemDetails' => [ 'type' => 'structure', 'required' => [ 'detail', ], 'members' => [ 'detail' => [ 'shape' => 'String', ], 'title' => [ 'shape' => 'String', ], ], ], 'PutSolFunctionPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'file', 'vnfPkgId', ], 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'file' => [ 'shape' => 'Blob', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], 'payload' => 'file', ], 'PutSolFunctionPackageContentMetadata' => [ 'type' => 'structure', 'members' => [ 'vnfd' => [ 'shape' => 'FunctionArtifactMeta', ], ], ], 'PutSolFunctionPackageContentOutput' => [ 'type' => 'structure', 'required' => [ 'id', 'metadata', 'vnfProductName', 'vnfProvider', 'vnfdId', 'vnfdVersion', ], 'members' => [ 'id' => [ 'shape' => 'VnfPkgId', ], 'metadata' => [ 'shape' => 'PutSolFunctionPackageContentMetadata', ], 'vnfProductName' => [ 'shape' => 'String', ], 'vnfProvider' => [ 'shape' => 'String', ], 'vnfdId' => [ 'shape' => 'VnfdId', ], 'vnfdVersion' => [ 'shape' => 'String', ], ], ], 'PutSolNetworkPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'file', 'nsdInfoId', ], 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'file' => [ 'shape' => 'Blob', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], 'payload' => 'file', ], 'PutSolNetworkPackageContentMetadata' => [ 'type' => 'structure', 'members' => [ 'nsd' => [ 'shape' => 'NetworkArtifactMeta', ], ], ], 'PutSolNetworkPackageContentOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsdId', 'nsdName', 'nsdVersion', 'vnfPkgIds', ], 'members' => [ 'arn' => [ 'shape' => 'NsdInfoArn', ], 'id' => [ 'shape' => 'NsdInfoId', ], 'metadata' => [ 'shape' => 'PutSolNetworkPackageContentMetadata', ], 'nsdId' => [ 'shape' => 'NsdId', ], 'nsdName' => [ 'shape' => 'String', ], 'nsdVersion' => [ 'shape' => 'String', ], 'vnfPkgIds' => [ 'shape' => 'VnfPkgIdList', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TNBResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:tnb:[a-z0-9-]+:[^:]*:.*$', ], 'TagKey' => [ 'type' => 'string', 'pattern' => '^(?!aws:).{1,128}$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TNBResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaskStatus' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'STARTED', 'IN_PROGRESS', 'COMPLETED', 'ERROR', 'SKIPPED', 'CANCELLED', ], ], 'TerminateSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsInstanceId', ], 'members' => [ 'nsInstanceId' => [ 'shape' => 'NsInstanceId', 'location' => 'uri', 'locationName' => 'nsInstanceId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TerminateSolNetworkInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'ToscaOverride' => [ 'type' => 'structure', 'members' => [ 'defaultValue' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TNBResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSolFunctionPackageInput' => [ 'type' => 'structure', 'required' => [ 'operationalState', 'vnfPkgId', ], 'members' => [ 'operationalState' => [ 'shape' => 'OperationalState', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], ], 'UpdateSolFunctionPackageOutput' => [ 'type' => 'structure', 'required' => [ 'operationalState', ], 'members' => [ 'operationalState' => [ 'shape' => 'OperationalState', ], ], ], 'UpdateSolNetworkInstanceInput' => [ 'type' => 'structure', 'required' => [ 'nsInstanceId', 'updateType', ], 'members' => [ 'modifyVnfInfoData' => [ 'shape' => 'UpdateSolNetworkModify', ], 'nsInstanceId' => [ 'shape' => 'NsInstanceId', 'location' => 'uri', 'locationName' => 'nsInstanceId', ], 'tags' => [ 'shape' => 'TagMap', ], 'updateType' => [ 'shape' => 'UpdateSolNetworkType', ], ], ], 'UpdateSolNetworkInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'nsLcmOpOccId' => [ 'shape' => 'NsLcmOpOccId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateSolNetworkModify' => [ 'type' => 'structure', 'required' => [ 'vnfConfigurableProperties', 'vnfInstanceId', ], 'members' => [ 'vnfConfigurableProperties' => [ 'shape' => 'Document', ], 'vnfInstanceId' => [ 'shape' => 'VnfInstanceId', ], ], ], 'UpdateSolNetworkPackageInput' => [ 'type' => 'structure', 'required' => [ 'nsdInfoId', 'nsdOperationalState', ], 'members' => [ 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], 'nsdOperationalState' => [ 'shape' => 'NsdOperationalState', ], ], ], 'UpdateSolNetworkPackageOutput' => [ 'type' => 'structure', 'required' => [ 'nsdOperationalState', ], 'members' => [ 'nsdOperationalState' => [ 'shape' => 'NsdOperationalState', ], ], ], 'UpdateSolNetworkType' => [ 'type' => 'string', 'enum' => [ 'MODIFY_VNF_INFORMATION', ], ], 'UsageState' => [ 'type' => 'string', 'enum' => [ 'IN_USE', 'NOT_IN_USE', ], ], 'ValidateSolFunctionPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'file', 'vnfPkgId', ], 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'file' => [ 'shape' => 'Blob', ], 'vnfPkgId' => [ 'shape' => 'VnfPkgId', 'location' => 'uri', 'locationName' => 'vnfPkgId', ], ], 'payload' => 'file', ], 'ValidateSolFunctionPackageContentMetadata' => [ 'type' => 'structure', 'members' => [ 'vnfd' => [ 'shape' => 'FunctionArtifactMeta', ], ], ], 'ValidateSolFunctionPackageContentOutput' => [ 'type' => 'structure', 'required' => [ 'id', 'metadata', 'vnfProductName', 'vnfProvider', 'vnfdId', 'vnfdVersion', ], 'members' => [ 'id' => [ 'shape' => 'VnfPkgId', ], 'metadata' => [ 'shape' => 'ValidateSolFunctionPackageContentMetadata', ], 'vnfProductName' => [ 'shape' => 'String', ], 'vnfProvider' => [ 'shape' => 'String', ], 'vnfdId' => [ 'shape' => 'VnfdId', ], 'vnfdVersion' => [ 'shape' => 'String', ], ], ], 'ValidateSolNetworkPackageContentInput' => [ 'type' => 'structure', 'required' => [ 'file', 'nsdInfoId', ], 'members' => [ 'contentType' => [ 'shape' => 'PackageContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'file' => [ 'shape' => 'Blob', ], 'nsdInfoId' => [ 'shape' => 'NsdInfoId', 'location' => 'uri', 'locationName' => 'nsdInfoId', ], ], 'payload' => 'file', ], 'ValidateSolNetworkPackageContentMetadata' => [ 'type' => 'structure', 'members' => [ 'nsd' => [ 'shape' => 'NetworkArtifactMeta', ], ], ], 'ValidateSolNetworkPackageContentOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'metadata', 'nsdId', 'nsdName', 'nsdVersion', 'vnfPkgIds', ], 'members' => [ 'arn' => [ 'shape' => 'NsdInfoArn', ], 'id' => [ 'shape' => 'NsdInfoId', ], 'metadata' => [ 'shape' => 'ValidateSolNetworkPackageContentMetadata', ], 'nsdId' => [ 'shape' => 'NsdId', ], 'nsdName' => [ 'shape' => 'String', ], 'nsdVersion' => [ 'shape' => 'String', ], 'vnfPkgIds' => [ 'shape' => 'VnfPkgIdList', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VnfInstanceArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-b|aws-us-gov):tnb:([a-z]{2}(-(gov|isob|iso))?-(east|west|north|south|central){1,2}-[0-9]):\\d{12}:(function-instance/fi-[a-f0-9]{17})$', ], 'VnfInstanceId' => [ 'type' => 'string', 'pattern' => '^fi-[a-f0-9]{17}$', ], 'VnfInstantiationState' => [ 'type' => 'string', 'enum' => [ 'INSTANTIATED', 'NOT_INSTANTIATED', ], ], 'VnfOperationalState' => [ 'type' => 'string', 'enum' => [ 'STARTED', 'STOPPED', ], ], 'VnfPkgArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-b|aws-us-gov):tnb:([a-z]{2}(-(gov|isob|iso))?-(east|west|north|south|central){1,2}-[0-9]):\\d{12}:(function-package/fp-[a-f0-9]{17})$', ], 'VnfPkgId' => [ 'type' => 'string', 'pattern' => '^fp-[a-f0-9]{17}$', ], 'VnfPkgIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VnfPkgId', ], ], 'VnfdId' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], ],];
