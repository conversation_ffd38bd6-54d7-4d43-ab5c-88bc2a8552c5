<?php
// This file was auto-generated from sdk-root/src/data/pinpoint/2016-12-01/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2016-12-01', 'endpointPrefix' => 'pinpoint', 'signingName' => 'mobiletargeting', 'serviceFullName' => 'Amazon Pinpoint', 'serviceId' => 'Pinpoint', 'protocol' => 'rest-json', 'jsonVersion' => '1.1', 'uid' => 'pinpoint-2016-12-01', 'signatureVersion' => 'v4', ], 'operations' => [ 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateCampaign' => [ 'name' => 'CreateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/campaigns', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCampaignRequest', ], 'output' => [ 'shape' => 'CreateCampaignResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateEmailTemplate' => [ 'name' => 'CreateEmailTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/templates/{template-name}/email', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEmailTemplateRequest', ], 'output' => [ 'shape' => 'CreateEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'CreateExportJob' => [ 'name' => 'CreateExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/jobs/export', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateExportJobRequest', ], 'output' => [ 'shape' => 'CreateExportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateImportJob' => [ 'name' => 'CreateImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/jobs/import', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateImportJobRequest', ], 'output' => [ 'shape' => 'CreateImportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateInAppTemplate' => [ 'name' => 'CreateInAppTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/templates/{template-name}/inapp', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInAppTemplateRequest', ], 'output' => [ 'shape' => 'CreateInAppTemplateResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'CreateJourney' => [ 'name' => 'CreateJourney', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/journeys', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJourneyRequest', ], 'output' => [ 'shape' => 'CreateJourneyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreatePushTemplate' => [ 'name' => 'CreatePushTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/templates/{template-name}/push', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePushTemplateRequest', ], 'output' => [ 'shape' => 'CreatePushTemplateResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'CreateRecommenderConfiguration' => [ 'name' => 'CreateRecommenderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/recommenders', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRecommenderConfigurationRequest', ], 'output' => [ 'shape' => 'CreateRecommenderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateSegment' => [ 'name' => 'CreateSegment', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/segments', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSegmentRequest', ], 'output' => [ 'shape' => 'CreateSegmentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateSmsTemplate' => [ 'name' => 'CreateSmsTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/templates/{template-name}/sms', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSmsTemplateRequest', ], 'output' => [ 'shape' => 'CreateSmsTemplateResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'CreateVoiceTemplate' => [ 'name' => 'CreateVoiceTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/templates/{template-name}/voice', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateVoiceTemplateRequest', ], 'output' => [ 'shape' => 'CreateVoiceTemplateResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DeleteAdmChannel' => [ 'name' => 'DeleteAdmChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/adm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAdmChannelRequest', ], 'output' => [ 'shape' => 'DeleteAdmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteApnsChannel' => [ 'name' => 'DeleteApnsChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/apns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApnsChannelRequest', ], 'output' => [ 'shape' => 'DeleteApnsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteApnsSandboxChannel' => [ 'name' => 'DeleteApnsSandboxChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/apns_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApnsSandboxChannelRequest', ], 'output' => [ 'shape' => 'DeleteApnsSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteApnsVoipChannel' => [ 'name' => 'DeleteApnsVoipChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApnsVoipChannelRequest', ], 'output' => [ 'shape' => 'DeleteApnsVoipChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteApnsVoipSandboxChannel' => [ 'name' => 'DeleteApnsVoipSandboxChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApnsVoipSandboxChannelRequest', ], 'output' => [ 'shape' => 'DeleteApnsVoipSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'output' => [ 'shape' => 'DeleteAppResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteBaiduChannel' => [ 'name' => 'DeleteBaiduChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/baidu', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBaiduChannelRequest', ], 'output' => [ 'shape' => 'DeleteBaiduChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteCampaign' => [ 'name' => 'DeleteCampaign', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCampaignRequest', ], 'output' => [ 'shape' => 'DeleteCampaignResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteEmailChannel' => [ 'name' => 'DeleteEmailChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/email', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEmailChannelRequest', ], 'output' => [ 'shape' => 'DeleteEmailChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteEmailTemplate' => [ 'name' => 'DeleteEmailTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/templates/{template-name}/email', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteEmailTemplateRequest', ], 'output' => [ 'shape' => 'DeleteEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/endpoints/{endpoint-id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteEndpointRequest', ], 'output' => [ 'shape' => 'DeleteEndpointResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteEventStream' => [ 'name' => 'DeleteEventStream', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/eventstream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEventStreamRequest', ], 'output' => [ 'shape' => 'DeleteEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteGcmChannel' => [ 'name' => 'DeleteGcmChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/gcm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGcmChannelRequest', ], 'output' => [ 'shape' => 'DeleteGcmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteInAppTemplate' => [ 'name' => 'DeleteInAppTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/templates/{template-name}/inapp', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteInAppTemplateRequest', ], 'output' => [ 'shape' => 'DeleteInAppTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteJourney' => [ 'name' => 'DeleteJourney', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteJourneyRequest', ], 'output' => [ 'shape' => 'DeleteJourneyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeletePushTemplate' => [ 'name' => 'DeletePushTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/templates/{template-name}/push', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeletePushTemplateRequest', ], 'output' => [ 'shape' => 'DeletePushTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteRecommenderConfiguration' => [ 'name' => 'DeleteRecommenderConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/recommenders/{recommender-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRecommenderConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteRecommenderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteSegment' => [ 'name' => 'DeleteSegment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSegmentRequest', ], 'output' => [ 'shape' => 'DeleteSegmentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteSmsChannel' => [ 'name' => 'DeleteSmsChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/sms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSmsChannelRequest', ], 'output' => [ 'shape' => 'DeleteSmsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteSmsTemplate' => [ 'name' => 'DeleteSmsTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/templates/{template-name}/sms', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteSmsTemplateRequest', ], 'output' => [ 'shape' => 'DeleteSmsTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteUserEndpoints' => [ 'name' => 'DeleteUserEndpoints', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/users/{user-id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteUserEndpointsRequest', ], 'output' => [ 'shape' => 'DeleteUserEndpointsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteVoiceChannel' => [ 'name' => 'DeleteVoiceChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/apps/{application-id}/channels/voice', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVoiceChannelRequest', ], 'output' => [ 'shape' => 'DeleteVoiceChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteVoiceTemplate' => [ 'name' => 'DeleteVoiceTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/templates/{template-name}/voice', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteVoiceTemplateRequest', ], 'output' => [ 'shape' => 'DeleteVoiceTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetAdmChannel' => [ 'name' => 'GetAdmChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/adm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAdmChannelRequest', ], 'output' => [ 'shape' => 'GetAdmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApnsChannel' => [ 'name' => 'GetApnsChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/apns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApnsChannelRequest', ], 'output' => [ 'shape' => 'GetApnsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApnsSandboxChannel' => [ 'name' => 'GetApnsSandboxChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/apns_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApnsSandboxChannelRequest', ], 'output' => [ 'shape' => 'GetApnsSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApnsVoipChannel' => [ 'name' => 'GetApnsVoipChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApnsVoipChannelRequest', ], 'output' => [ 'shape' => 'GetApnsVoipChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApnsVoipSandboxChannel' => [ 'name' => 'GetApnsVoipSandboxChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApnsVoipSandboxChannelRequest', ], 'output' => [ 'shape' => 'GetApnsVoipSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApp' => [ 'name' => 'GetApp', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppRequest', ], 'output' => [ 'shape' => 'GetAppResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApplicationDateRangeKpi' => [ 'name' => 'GetApplicationDateRangeKpi', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/kpis/daterange/{kpi-name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationDateRangeKpiRequest', ], 'output' => [ 'shape' => 'GetApplicationDateRangeKpiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApplicationSettings' => [ 'name' => 'GetApplicationSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationSettingsRequest', ], 'output' => [ 'shape' => 'GetApplicationSettingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetApps' => [ 'name' => 'GetApps', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAppsRequest', ], 'output' => [ 'shape' => 'GetAppsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetBaiduChannel' => [ 'name' => 'GetBaiduChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/baidu', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBaiduChannelRequest', ], 'output' => [ 'shape' => 'GetBaiduChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaign' => [ 'name' => 'GetCampaign', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignRequest', ], 'output' => [ 'shape' => 'GetCampaignResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaignActivities' => [ 'name' => 'GetCampaignActivities', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}/activities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignActivitiesRequest', ], 'output' => [ 'shape' => 'GetCampaignActivitiesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaignDateRangeKpi' => [ 'name' => 'GetCampaignDateRangeKpi', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}/kpis/daterange/{kpi-name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignDateRangeKpiRequest', ], 'output' => [ 'shape' => 'GetCampaignDateRangeKpiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaignVersion' => [ 'name' => 'GetCampaignVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}/versions/{version}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignVersionRequest', ], 'output' => [ 'shape' => 'GetCampaignVersionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaignVersions' => [ 'name' => 'GetCampaignVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignVersionsRequest', ], 'output' => [ 'shape' => 'GetCampaignVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetCampaigns' => [ 'name' => 'GetCampaigns', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/campaigns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignsRequest', ], 'output' => [ 'shape' => 'GetCampaignsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetChannels' => [ 'name' => 'GetChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelsRequest', ], 'output' => [ 'shape' => 'GetChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetEmailChannel' => [ 'name' => 'GetEmailChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/email', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEmailChannelRequest', ], 'output' => [ 'shape' => 'GetEmailChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetEmailTemplate' => [ 'name' => 'GetEmailTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/email', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEmailTemplateRequest', ], 'output' => [ 'shape' => 'GetEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetEndpoint' => [ 'name' => 'GetEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/endpoints/{endpoint-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEndpointRequest', ], 'output' => [ 'shape' => 'GetEndpointResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetEventStream' => [ 'name' => 'GetEventStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/eventstream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventStreamRequest', ], 'output' => [ 'shape' => 'GetEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetExportJob' => [ 'name' => 'GetExportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/jobs/export/{job-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExportJobRequest', ], 'output' => [ 'shape' => 'GetExportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetExportJobs' => [ 'name' => 'GetExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/jobs/export', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExportJobsRequest', ], 'output' => [ 'shape' => 'GetExportJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetGcmChannel' => [ 'name' => 'GetGcmChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/gcm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGcmChannelRequest', ], 'output' => [ 'shape' => 'GetGcmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetImportJob' => [ 'name' => 'GetImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/jobs/import/{job-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportJobRequest', ], 'output' => [ 'shape' => 'GetImportJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetImportJobs' => [ 'name' => 'GetImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/jobs/import', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportJobsRequest', ], 'output' => [ 'shape' => 'GetImportJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetInAppMessages' => [ 'name' => 'GetInAppMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/endpoints/{endpoint-id}/inappmessages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInAppMessagesRequest', ], 'output' => [ 'shape' => 'GetInAppMessagesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetInAppTemplate' => [ 'name' => 'GetInAppTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/inapp', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInAppTemplateRequest', ], 'output' => [ 'shape' => 'GetInAppTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourney' => [ 'name' => 'GetJourney', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyRequest', ], 'output' => [ 'shape' => 'GetJourneyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyDateRangeKpi' => [ 'name' => 'GetJourneyDateRangeKpi', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/kpis/daterange/{kpi-name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyDateRangeKpiRequest', ], 'output' => [ 'shape' => 'GetJourneyDateRangeKpiResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyExecutionActivityMetrics' => [ 'name' => 'GetJourneyExecutionActivityMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/activities/{journey-activity-id}/execution-metrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyExecutionActivityMetricsRequest', ], 'output' => [ 'shape' => 'GetJourneyExecutionActivityMetricsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyExecutionMetrics' => [ 'name' => 'GetJourneyExecutionMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/execution-metrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyExecutionMetricsRequest', ], 'output' => [ 'shape' => 'GetJourneyExecutionMetricsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyRunExecutionActivityMetrics' => [ 'name' => 'GetJourneyRunExecutionActivityMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/runs/{run-id}/activities/{journey-activity-id}/execution-metrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyRunExecutionActivityMetricsRequest', ], 'output' => [ 'shape' => 'GetJourneyRunExecutionActivityMetricsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyRunExecutionMetrics' => [ 'name' => 'GetJourneyRunExecutionMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/runs/{run-id}/execution-metrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyRunExecutionMetricsRequest', ], 'output' => [ 'shape' => 'GetJourneyRunExecutionMetricsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetJourneyRuns' => [ 'name' => 'GetJourneyRuns', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/runs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJourneyRunsRequest', ], 'output' => [ 'shape' => 'GetJourneyRunsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetPushTemplate' => [ 'name' => 'GetPushTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/push', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPushTemplateRequest', ], 'output' => [ 'shape' => 'GetPushTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetRecommenderConfiguration' => [ 'name' => 'GetRecommenderConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/recommenders/{recommender-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecommenderConfigurationRequest', ], 'output' => [ 'shape' => 'GetRecommenderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetRecommenderConfigurations' => [ 'name' => 'GetRecommenderConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/recommenders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecommenderConfigurationsRequest', ], 'output' => [ 'shape' => 'GetRecommenderConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegment' => [ 'name' => 'GetSegment', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentRequest', ], 'output' => [ 'shape' => 'GetSegmentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegmentExportJobs' => [ 'name' => 'GetSegmentExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}/jobs/export', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentExportJobsRequest', ], 'output' => [ 'shape' => 'GetSegmentExportJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegmentImportJobs' => [ 'name' => 'GetSegmentImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}/jobs/import', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentImportJobsRequest', ], 'output' => [ 'shape' => 'GetSegmentImportJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegmentVersion' => [ 'name' => 'GetSegmentVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}/versions/{version}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentVersionRequest', ], 'output' => [ 'shape' => 'GetSegmentVersionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegmentVersions' => [ 'name' => 'GetSegmentVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentVersionsRequest', ], 'output' => [ 'shape' => 'GetSegmentVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSegments' => [ 'name' => 'GetSegments', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/segments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSegmentsRequest', ], 'output' => [ 'shape' => 'GetSegmentsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSmsChannel' => [ 'name' => 'GetSmsChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/sms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSmsChannelRequest', ], 'output' => [ 'shape' => 'GetSmsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetSmsTemplate' => [ 'name' => 'GetSmsTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/sms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSmsTemplateRequest', ], 'output' => [ 'shape' => 'GetSmsTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetUserEndpoints' => [ 'name' => 'GetUserEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/users/{user-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserEndpointsRequest', ], 'output' => [ 'shape' => 'GetUserEndpointsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetVoiceChannel' => [ 'name' => 'GetVoiceChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/channels/voice', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceChannelRequest', ], 'output' => [ 'shape' => 'GetVoiceChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'GetVoiceTemplate' => [ 'name' => 'GetVoiceTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/voice', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceTemplateRequest', ], 'output' => [ 'shape' => 'GetVoiceTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListJourneys' => [ 'name' => 'ListJourneys', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/apps/{application-id}/journeys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJourneysRequest', ], 'output' => [ 'shape' => 'ListJourneysResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resource-arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [], ], 'ListTemplateVersions' => [ 'name' => 'ListTemplateVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates/{template-name}/{template-type}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplateVersionsRequest', ], 'output' => [ 'shape' => 'ListTemplateVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTemplates' => [ 'name' => 'ListTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplatesRequest', ], 'output' => [ 'shape' => 'ListTemplatesResponse', ], 'errors' => [ [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'PhoneNumberValidate' => [ 'name' => 'PhoneNumberValidate', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/phone/number/validate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PhoneNumberValidateRequest', ], 'output' => [ 'shape' => 'PhoneNumberValidateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'PutEventStream' => [ 'name' => 'PutEventStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/eventstream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutEventStreamRequest', ], 'output' => [ 'shape' => 'PutEventStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'PutEvents' => [ 'name' => 'PutEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/events', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PutEventsRequest', ], 'output' => [ 'shape' => 'PutEventsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'RemoveAttributes' => [ 'name' => 'RemoveAttributes', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/attributes/{attribute-type}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveAttributesRequest', ], 'output' => [ 'shape' => 'RemoveAttributesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'SendMessages' => [ 'name' => 'SendMessages', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/messages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendMessagesRequest', ], 'output' => [ 'shape' => 'SendMessagesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'SendOTPMessage' => [ 'name' => 'SendOTPMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/otp', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendOTPMessageRequest', ], 'output' => [ 'shape' => 'SendOTPMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'SendUsersMessages' => [ 'name' => 'SendUsersMessages', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/users-messages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendUsersMessagesRequest', ], 'output' => [ 'shape' => 'SendUsersMessagesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resource-arn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resource-arn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [], ], 'UpdateAdmChannel' => [ 'name' => 'UpdateAdmChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/adm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAdmChannelRequest', ], 'output' => [ 'shape' => 'UpdateAdmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateApnsChannel' => [ 'name' => 'UpdateApnsChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/apns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApnsChannelRequest', ], 'output' => [ 'shape' => 'UpdateApnsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateApnsSandboxChannel' => [ 'name' => 'UpdateApnsSandboxChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/apns_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApnsSandboxChannelRequest', ], 'output' => [ 'shape' => 'UpdateApnsSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateApnsVoipChannel' => [ 'name' => 'UpdateApnsVoipChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApnsVoipChannelRequest', ], 'output' => [ 'shape' => 'UpdateApnsVoipChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateApnsVoipSandboxChannel' => [ 'name' => 'UpdateApnsVoipSandboxChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/apns_voip_sandbox', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApnsVoipSandboxChannelRequest', ], 'output' => [ 'shape' => 'UpdateApnsVoipSandboxChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateApplicationSettings' => [ 'name' => 'UpdateApplicationSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/settings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationSettingsRequest', ], 'output' => [ 'shape' => 'UpdateApplicationSettingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateBaiduChannel' => [ 'name' => 'UpdateBaiduChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/baidu', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBaiduChannelRequest', ], 'output' => [ 'shape' => 'UpdateBaiduChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateCampaign' => [ 'name' => 'UpdateCampaign', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/campaigns/{campaign-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignRequest', ], 'output' => [ 'shape' => 'UpdateCampaignResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateEmailChannel' => [ 'name' => 'UpdateEmailChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/email', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEmailChannelRequest', ], 'output' => [ 'shape' => 'UpdateEmailChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateEmailTemplate' => [ 'name' => 'UpdateEmailTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/email', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateEmailTemplateRequest', ], 'output' => [ 'shape' => 'UpdateEmailTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateEndpoint' => [ 'name' => 'UpdateEndpoint', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/endpoints/{endpoint-id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateEndpointRequest', ], 'output' => [ 'shape' => 'UpdateEndpointResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateEndpointsBatch' => [ 'name' => 'UpdateEndpointsBatch', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/endpoints', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateEndpointsBatchRequest', ], 'output' => [ 'shape' => 'UpdateEndpointsBatchResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateGcmChannel' => [ 'name' => 'UpdateGcmChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/gcm', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGcmChannelRequest', ], 'output' => [ 'shape' => 'UpdateGcmChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateInAppTemplate' => [ 'name' => 'UpdateInAppTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/inapp', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateInAppTemplateRequest', ], 'output' => [ 'shape' => 'UpdateInAppTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateJourney' => [ 'name' => 'UpdateJourney', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateJourneyRequest', ], 'output' => [ 'shape' => 'UpdateJourneyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateJourneyState' => [ 'name' => 'UpdateJourneyState', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/journeys/{journey-id}/state', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateJourneyStateRequest', ], 'output' => [ 'shape' => 'UpdateJourneyStateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdatePushTemplate' => [ 'name' => 'UpdatePushTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/push', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdatePushTemplateRequest', ], 'output' => [ 'shape' => 'UpdatePushTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateRecommenderConfiguration' => [ 'name' => 'UpdateRecommenderConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/recommenders/{recommender-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRecommenderConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateRecommenderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateSegment' => [ 'name' => 'UpdateSegment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/segments/{segment-id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSegmentRequest', ], 'output' => [ 'shape' => 'UpdateSegmentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateSmsChannel' => [ 'name' => 'UpdateSmsChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/sms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSmsChannelRequest', ], 'output' => [ 'shape' => 'UpdateSmsChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateSmsTemplate' => [ 'name' => 'UpdateSmsTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/sms', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateSmsTemplateRequest', ], 'output' => [ 'shape' => 'UpdateSmsTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateTemplateActiveVersion' => [ 'name' => 'UpdateTemplateActiveVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/{template-type}/active-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTemplateActiveVersionRequest', ], 'output' => [ 'shape' => 'UpdateTemplateActiveVersionResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateVoiceChannel' => [ 'name' => 'UpdateVoiceChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/apps/{application-id}/channels/voice', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVoiceChannelRequest', ], 'output' => [ 'shape' => 'UpdateVoiceChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateVoiceTemplate' => [ 'name' => 'UpdateVoiceTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/templates/{template-name}/voice', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateVoiceTemplateRequest', ], 'output' => [ 'shape' => 'UpdateVoiceTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'VerifyOTPMessage' => [ 'name' => 'VerifyOTPMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/apps/{application-id}/verify-otp', 'responseCode' => 200, ], 'input' => [ 'shape' => 'VerifyOTPMessageRequest', ], 'output' => [ 'shape' => 'VerifyOTPMessageResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'PayloadTooLargeException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], ], 'shapes' => [ 'ADMChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ClientId' => [ 'shape' => '__string', ], 'ClientSecret' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], ], 'required' => [ 'ClientSecret', 'ClientId', ], ], 'ADMChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'ADMMessage' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'ConsolidationKey' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => 'MapOf__string', ], 'ExpiresAfter' => [ 'shape' => '__string', ], 'IconReference' => [ 'shape' => '__string', ], 'ImageIconUrl' => [ 'shape' => '__string', ], 'ImageUrl' => [ 'shape' => '__string', ], 'MD5' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'SmallImageIconUrl' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'APNSChannelRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => '__string', ], 'Certificate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'PrivateKey' => [ 'shape' => '__string', ], 'TeamId' => [ 'shape' => '__string', ], 'TokenKey' => [ 'shape' => '__string', ], 'TokenKeyId' => [ 'shape' => '__string', ], ], ], 'APNSChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'HasTokenKey' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'APNSMessage' => [ 'type' => 'structure', 'members' => [ 'APNSPushType' => [ 'shape' => '__string', ], 'Action' => [ 'shape' => 'Action', ], 'Badge' => [ 'shape' => '__integer', ], 'Body' => [ 'shape' => '__string', ], 'Category' => [ 'shape' => '__string', ], 'CollapseId' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => 'MapOf__string', ], 'MediaUrl' => [ 'shape' => '__string', ], 'PreferredAuthenticationMethod' => [ 'shape' => '__string', ], 'Priority' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'Sound' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'ThreadId' => [ 'shape' => '__string', ], 'TimeToLive' => [ 'shape' => '__integer', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'APNSPushNotificationTemplate' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'MediaUrl' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'APNSSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => '__string', ], 'Certificate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'PrivateKey' => [ 'shape' => '__string', ], 'TeamId' => [ 'shape' => '__string', ], 'TokenKey' => [ 'shape' => '__string', ], 'TokenKeyId' => [ 'shape' => '__string', ], ], ], 'APNSSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'HasTokenKey' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'APNSVoipChannelRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => '__string', ], 'Certificate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'PrivateKey' => [ 'shape' => '__string', ], 'TeamId' => [ 'shape' => '__string', ], 'TokenKey' => [ 'shape' => '__string', ], 'TokenKeyId' => [ 'shape' => '__string', ], ], ], 'APNSVoipChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'HasTokenKey' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'APNSVoipSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => '__string', ], 'Certificate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'PrivateKey' => [ 'shape' => '__string', ], 'TeamId' => [ 'shape' => '__string', ], 'TokenKey' => [ 'shape' => '__string', ], 'TokenKeyId' => [ 'shape' => '__string', ], ], ], 'APNSVoipSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'HasTokenKey' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'Action' => [ 'type' => 'string', 'enum' => [ 'OPEN_APP', 'DEEP_LINK', 'URL', ], ], 'ActivitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfActivityResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'Activity' => [ 'type' => 'structure', 'members' => [ 'CUSTOM' => [ 'shape' => 'CustomMessageActivity', ], 'ConditionalSplit' => [ 'shape' => 'ConditionalSplitActivity', ], 'Description' => [ 'shape' => '__string', ], 'EMAIL' => [ 'shape' => 'EmailMessageActivity', ], 'Holdout' => [ 'shape' => 'HoldoutActivity', ], 'MultiCondition' => [ 'shape' => 'MultiConditionalSplitActivity', ], 'PUSH' => [ 'shape' => 'PushMessageActivity', ], 'RandomSplit' => [ 'shape' => 'RandomSplitActivity', ], 'SMS' => [ 'shape' => 'SMSMessageActivity', ], 'Wait' => [ 'shape' => 'WaitActivity', ], 'ContactCenter' => [ 'shape' => 'ContactCenterActivity', ], ], ], 'ActivityResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CampaignId' => [ 'shape' => '__string', ], 'End' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'Result' => [ 'shape' => '__string', ], 'ScheduledStart' => [ 'shape' => '__string', ], 'Start' => [ 'shape' => '__string', ], 'State' => [ 'shape' => '__string', ], 'SuccessfulEndpointCount' => [ 'shape' => '__integer', ], 'TimezonesCompletedCount' => [ 'shape' => '__integer', ], 'TimezonesTotalCount' => [ 'shape' => '__integer', ], 'TotalEndpointCount' => [ 'shape' => '__integer', ], 'TreatmentId' => [ 'shape' => '__string', ], 'ExecutionMetrics' => [ 'shape' => 'MapOf__string', ], ], 'required' => [ 'CampaignId', 'Id', 'ApplicationId', ], ], 'AddressConfiguration' => [ 'type' => 'structure', 'members' => [ 'BodyOverride' => [ 'shape' => '__string', ], 'ChannelType' => [ 'shape' => 'ChannelType', ], 'Context' => [ 'shape' => 'MapOf__string', ], 'RawContent' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'TitleOverride' => [ 'shape' => '__string', ], ], ], 'Alignment' => [ 'type' => 'string', 'enum' => [ 'LEFT', 'CENTER', 'RIGHT', ], ], 'AndroidPushNotificationTemplate' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'ImageIconUrl' => [ 'shape' => '__string', ], 'ImageUrl' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'SmallImageIconUrl' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'ApplicationDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'EndTime' => [ 'shape' => '__timestampIso8601', ], 'KpiName' => [ 'shape' => '__string', ], 'KpiResult' => [ 'shape' => 'BaseKpiResult', ], 'NextToken' => [ 'shape' => '__string', ], 'StartTime' => [ 'shape' => '__timestampIso8601', ], ], 'required' => [ 'KpiResult', 'KpiName', 'EndTime', 'StartTime', 'ApplicationId', ], ], 'ApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'CreationDate' => [ 'shape' => '__string', ], ], 'required' => [ 'Id', 'Arn', 'Name', ], ], 'ApplicationSettingsJourneyLimits' => [ 'type' => 'structure', 'members' => [ 'DailyCap' => [ 'shape' => '__integer', ], 'TimeframeCap' => [ 'shape' => 'JourneyTimeframeCap', ], 'TotalCap' => [ 'shape' => '__integer', ], ], ], 'ApplicationSettingsResource' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CampaignHook' => [ 'shape' => 'CampaignHook', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Limits' => [ 'shape' => 'CampaignLimits', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], 'JourneyLimits' => [ 'shape' => 'ApplicationSettingsJourneyLimits', ], ], 'required' => [ 'ApplicationId', ], ], 'ApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfApplicationResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'AttributeDimension' => [ 'type' => 'structure', 'members' => [ 'AttributeType' => [ 'shape' => 'AttributeType', ], 'Values' => [ 'shape' => 'ListOf__string', ], ], 'required' => [ 'Values', ], ], 'AttributeType' => [ 'type' => 'string', 'enum' => [ 'INCLUSIVE', 'EXCLUSIVE', 'CONTAINS', 'BEFORE', 'AFTER', 'BETWEEN', 'ON', ], ], 'AttributesResource' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'AttributeType' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'ListOf__string', ], ], 'required' => [ 'AttributeType', 'ApplicationId', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'BaiduChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApiKey' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'SecretKey' => [ 'shape' => '__string', ], ], 'required' => [ 'SecretKey', 'ApiKey', ], ], 'BaiduChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Credential' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Credential', 'Platform', ], ], 'BaiduMessage' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => 'MapOf__string', ], 'IconReference' => [ 'shape' => '__string', ], 'ImageIconUrl' => [ 'shape' => '__string', ], 'ImageUrl' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'SmallImageIconUrl' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'TimeToLive' => [ 'shape' => '__integer', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'BaseKpiResult' => [ 'type' => 'structure', 'members' => [ 'Rows' => [ 'shape' => 'ListOfResultRow', ], ], 'required' => [ 'Rows', ], ], 'ButtonAction' => [ 'type' => 'string', 'enum' => [ 'LINK', 'DEEP_LINK', 'CLOSE', ], ], 'CampaignCustomMessage' => [ 'type' => 'structure', 'members' => [ 'Data' => [ 'shape' => '__string', ], ], ], 'CampaignDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CampaignId' => [ 'shape' => '__string', ], 'EndTime' => [ 'shape' => '__timestampIso8601', ], 'KpiName' => [ 'shape' => '__string', ], 'KpiResult' => [ 'shape' => 'BaseKpiResult', ], 'NextToken' => [ 'shape' => '__string', ], 'StartTime' => [ 'shape' => '__timestampIso8601', ], ], 'required' => [ 'KpiResult', 'KpiName', 'EndTime', 'CampaignId', 'StartTime', 'ApplicationId', ], ], 'CampaignEmailMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'FromAddress' => [ 'shape' => '__string', ], 'HtmlBody' => [ 'shape' => '__string', ], 'Title' => [ 'shape' => '__string', ], ], ], 'CampaignEventFilter' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'EventDimensions', ], 'FilterType' => [ 'shape' => 'FilterType', ], ], 'required' => [ 'FilterType', 'Dimensions', ], ], 'CampaignHook' => [ 'type' => 'structure', 'members' => [ 'LambdaFunctionName' => [ 'shape' => '__string', ], 'Mode' => [ 'shape' => 'Mode', ], 'WebUrl' => [ 'shape' => '__string', ], ], ], 'CampaignInAppMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'Content' => [ 'shape' => 'ListOfInAppMessageContent', ], 'CustomConfig' => [ 'shape' => 'MapOf__string', ], 'Layout' => [ 'shape' => 'Layout', ], ], ], 'CampaignLimits' => [ 'type' => 'structure', 'members' => [ 'Daily' => [ 'shape' => '__integer', ], 'MaximumDuration' => [ 'shape' => '__integer', ], 'MessagesPerSecond' => [ 'shape' => '__integer', ], 'Total' => [ 'shape' => '__integer', ], 'Session' => [ 'shape' => '__integer', ], ], ], 'CampaignResponse' => [ 'type' => 'structure', 'members' => [ 'AdditionalTreatments' => [ 'shape' => 'ListOfTreatmentResource', ], 'ApplicationId' => [ 'shape' => '__string', ], 'Arn' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'CustomDeliveryConfiguration' => [ 'shape' => 'CustomDeliveryConfiguration', ], 'DefaultState' => [ 'shape' => 'CampaignState', ], 'Description' => [ 'shape' => '__string', ], 'HoldoutPercent' => [ 'shape' => '__integer', ], 'Hook' => [ 'shape' => 'CampaignHook', ], 'Id' => [ 'shape' => '__string', ], 'IsPaused' => [ 'shape' => '__boolean', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Limits' => [ 'shape' => 'CampaignLimits', ], 'MessageConfiguration' => [ 'shape' => 'MessageConfiguration', ], 'Name' => [ 'shape' => '__string', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentVersion' => [ 'shape' => '__integer', ], 'State' => [ 'shape' => 'CampaignState', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TreatmentDescription' => [ 'shape' => '__string', ], 'TreatmentName' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], 'Priority' => [ 'shape' => '__integer', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'SegmentId', 'SegmentVersion', 'Id', 'Arn', 'ApplicationId', ], ], 'CampaignSmsMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'OriginationNumber' => [ 'shape' => '__string', ], 'SenderId' => [ 'shape' => '__string', ], 'EntityId' => [ 'shape' => '__string', ], 'TemplateId' => [ 'shape' => '__string', ], ], ], 'CampaignState' => [ 'type' => 'structure', 'members' => [ 'CampaignStatus' => [ 'shape' => 'CampaignStatus', ], ], ], 'CampaignStatus' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'EXECUTING', 'PENDING_NEXT_RUN', 'COMPLETED', 'PAUSED', 'DELETED', 'INVALID', ], ], 'CampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfCampaignResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'ChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], ], 'ChannelType' => [ 'type' => 'string', 'enum' => [ 'PUSH', 'GCM', 'APNS', 'APNS_SANDBOX', 'APNS_VOIP', 'APNS_VOIP_SANDBOX', 'ADM', 'SMS', 'VOICE', 'EMAIL', 'BAIDU', 'CUSTOM', 'IN_APP', ], ], 'ChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => 'MapOfChannelResponse', ], ], 'required' => [ 'Channels', ], ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'Conditions' => [ 'shape' => 'ListOfSimpleCondition', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'ConditionalSplitActivity' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'Condition', ], 'EvaluationWaitTime' => [ 'shape' => 'WaitTime', ], 'FalseActivity' => [ 'shape' => '__string', ], 'TrueActivity' => [ 'shape' => '__string', ], ], ], 'ContactCenterActivity' => [ 'type' => 'structure', 'members' => [ 'NextActivity' => [ 'shape' => '__string', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 409, ], ], 'CreateAppRequest' => [ 'type' => 'structure', 'members' => [ 'CreateApplicationRequest' => [ 'shape' => 'CreateApplicationRequest', ], ], 'required' => [ 'CreateApplicationRequest', ], 'payload' => 'CreateApplicationRequest', ], 'CreateAppResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationResponse' => [ 'shape' => 'ApplicationResponse', ], ], 'required' => [ 'ApplicationResponse', ], 'payload' => 'ApplicationResponse', ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'Name', ], ], 'CreateCampaignRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'WriteCampaignRequest' => [ 'shape' => 'WriteCampaignRequest', ], ], 'required' => [ 'ApplicationId', 'WriteCampaignRequest', ], 'payload' => 'WriteCampaignRequest', ], 'CreateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignResponse' => [ 'shape' => 'CampaignResponse', ], ], 'required' => [ 'CampaignResponse', ], 'payload' => 'CampaignResponse', ], 'CreateEmailTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'EmailTemplateRequest' => [ 'shape' => 'EmailTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], ], 'required' => [ 'TemplateName', 'EmailTemplateRequest', ], 'payload' => 'EmailTemplateRequest', ], 'CreateEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'CreateTemplateMessageBody' => [ 'shape' => 'CreateTemplateMessageBody', ], ], 'required' => [ 'CreateTemplateMessageBody', ], 'payload' => 'CreateTemplateMessageBody', ], 'CreateExportJobRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'ExportJobRequest' => [ 'shape' => 'ExportJobRequest', ], ], 'required' => [ 'ApplicationId', 'ExportJobRequest', ], 'payload' => 'ExportJobRequest', ], 'CreateExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ExportJobResponse' => [ 'shape' => 'ExportJobResponse', ], ], 'required' => [ 'ExportJobResponse', ], 'payload' => 'ExportJobResponse', ], 'CreateImportJobRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'ImportJobRequest' => [ 'shape' => 'ImportJobRequest', ], ], 'required' => [ 'ApplicationId', 'ImportJobRequest', ], 'payload' => 'ImportJobRequest', ], 'CreateImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ImportJobResponse' => [ 'shape' => 'ImportJobResponse', ], ], 'required' => [ 'ImportJobResponse', ], 'payload' => 'ImportJobResponse', ], 'CreateInAppTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'InAppTemplateRequest' => [ 'shape' => 'InAppTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], ], 'required' => [ 'TemplateName', 'InAppTemplateRequest', ], 'payload' => 'InAppTemplateRequest', ], 'CreateInAppTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateCreateMessageBody' => [ 'shape' => 'TemplateCreateMessageBody', ], ], 'required' => [ 'TemplateCreateMessageBody', ], 'payload' => 'TemplateCreateMessageBody', ], 'CreateJourneyRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'WriteJourneyRequest' => [ 'shape' => 'WriteJourneyRequest', ], ], 'required' => [ 'ApplicationId', 'WriteJourneyRequest', ], 'payload' => 'WriteJourneyRequest', ], 'CreateJourneyResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyResponse' => [ 'shape' => 'JourneyResponse', ], ], 'required' => [ 'JourneyResponse', ], 'payload' => 'JourneyResponse', ], 'CreatePushTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'PushNotificationTemplateRequest' => [ 'shape' => 'PushNotificationTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], ], 'required' => [ 'TemplateName', 'PushNotificationTemplateRequest', ], 'payload' => 'PushNotificationTemplateRequest', ], 'CreatePushTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'CreateTemplateMessageBody' => [ 'shape' => 'CreateTemplateMessageBody', ], ], 'required' => [ 'CreateTemplateMessageBody', ], 'payload' => 'CreateTemplateMessageBody', ], 'CreateRecommenderConfiguration' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapOf__string', ], 'Description' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], 'RecommendationProviderIdType' => [ 'shape' => '__string', ], 'RecommendationProviderRoleArn' => [ 'shape' => '__string', ], 'RecommendationProviderUri' => [ 'shape' => '__string', ], 'RecommendationTransformerUri' => [ 'shape' => '__string', ], 'RecommendationsDisplayName' => [ 'shape' => '__string', ], 'RecommendationsPerMessage' => [ 'shape' => '__integer', ], ], 'required' => [ 'RecommendationProviderUri', 'RecommendationProviderRoleArn', ], ], 'CreateRecommenderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'CreateRecommenderConfiguration' => [ 'shape' => 'CreateRecommenderConfiguration', ], ], 'required' => [ 'CreateRecommenderConfiguration', ], 'payload' => 'CreateRecommenderConfiguration', ], 'CreateRecommenderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RecommenderConfigurationResponse' => [ 'shape' => 'RecommenderConfigurationResponse', ], ], 'required' => [ 'RecommenderConfigurationResponse', ], 'payload' => 'RecommenderConfigurationResponse', ], 'CreateSegmentRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'WriteSegmentRequest' => [ 'shape' => 'WriteSegmentRequest', ], ], 'required' => [ 'ApplicationId', 'WriteSegmentRequest', ], 'payload' => 'WriteSegmentRequest', ], 'CreateSegmentResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentResponse' => [ 'shape' => 'SegmentResponse', ], ], 'required' => [ 'SegmentResponse', ], 'payload' => 'SegmentResponse', ], 'CreateSmsTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'SMSTemplateRequest' => [ 'shape' => 'SMSTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], ], 'required' => [ 'TemplateName', 'SMSTemplateRequest', ], 'payload' => 'SMSTemplateRequest', ], 'CreateSmsTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'CreateTemplateMessageBody' => [ 'shape' => 'CreateTemplateMessageBody', ], ], 'required' => [ 'CreateTemplateMessageBody', ], 'payload' => 'CreateTemplateMessageBody', ], 'CreateTemplateMessageBody' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], ], 'CreateVoiceTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'VoiceTemplateRequest' => [ 'shape' => 'VoiceTemplateRequest', ], ], 'required' => [ 'TemplateName', 'VoiceTemplateRequest', ], 'payload' => 'VoiceTemplateRequest', ], 'CreateVoiceTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'CreateTemplateMessageBody' => [ 'shape' => 'CreateTemplateMessageBody', ], ], 'required' => [ 'CreateTemplateMessageBody', ], 'payload' => 'CreateTemplateMessageBody', ], 'CustomDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'DeliveryUri' => [ 'shape' => '__string', ], 'EndpointTypes' => [ 'shape' => 'ListOf__EndpointTypesElement', ], ], 'required' => [ 'DeliveryUri', ], ], 'CustomMessageActivity' => [ 'type' => 'structure', 'members' => [ 'DeliveryUri' => [ 'shape' => '__string', ], 'EndpointTypes' => [ 'shape' => 'ListOf__EndpointTypesElement', ], 'MessageConfig' => [ 'shape' => 'JourneyCustomMessage', ], 'NextActivity' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateVersion' => [ 'shape' => '__string', ], ], ], 'DefaultButtonConfiguration' => [ 'type' => 'structure', 'members' => [ 'BackgroundColor' => [ 'shape' => '__string', ], 'BorderRadius' => [ 'shape' => '__integer', ], 'ButtonAction' => [ 'shape' => 'ButtonAction', ], 'Link' => [ 'shape' => '__string', ], 'Text' => [ 'shape' => '__string', ], 'TextColor' => [ 'shape' => '__string', ], ], 'required' => [ 'ButtonAction', 'Text', ], ], 'DefaultMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], ], ], 'DefaultPushNotificationMessage' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => 'MapOf__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'DefaultPushNotificationTemplate' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'DeleteAdmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteAdmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ADMChannelResponse' => [ 'shape' => 'ADMChannelResponse', ], ], 'required' => [ 'ADMChannelResponse', ], 'payload' => 'ADMChannelResponse', ], 'DeleteApnsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteApnsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSChannelResponse' => [ 'shape' => 'APNSChannelResponse', ], ], 'required' => [ 'APNSChannelResponse', ], 'payload' => 'APNSChannelResponse', ], 'DeleteApnsSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteApnsSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSSandboxChannelResponse' => [ 'shape' => 'APNSSandboxChannelResponse', ], ], 'required' => [ 'APNSSandboxChannelResponse', ], 'payload' => 'APNSSandboxChannelResponse', ], 'DeleteApnsVoipChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteApnsVoipChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipChannelResponse' => [ 'shape' => 'APNSVoipChannelResponse', ], ], 'required' => [ 'APNSVoipChannelResponse', ], 'payload' => 'APNSVoipChannelResponse', ], 'DeleteApnsVoipSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteApnsVoipSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipSandboxChannelResponse' => [ 'shape' => 'APNSVoipSandboxChannelResponse', ], ], 'required' => [ 'APNSVoipSandboxChannelResponse', ], 'payload' => 'APNSVoipSandboxChannelResponse', ], 'DeleteAppRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteAppResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationResponse' => [ 'shape' => 'ApplicationResponse', ], ], 'required' => [ 'ApplicationResponse', ], 'payload' => 'ApplicationResponse', ], 'DeleteBaiduChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteBaiduChannelResponse' => [ 'type' => 'structure', 'members' => [ 'BaiduChannelResponse' => [ 'shape' => 'BaiduChannelResponse', ], ], 'required' => [ 'BaiduChannelResponse', ], 'payload' => 'BaiduChannelResponse', ], 'DeleteCampaignRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], ], 'required' => [ 'CampaignId', 'ApplicationId', ], ], 'DeleteCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignResponse' => [ 'shape' => 'CampaignResponse', ], ], 'required' => [ 'CampaignResponse', ], 'payload' => 'CampaignResponse', ], 'DeleteEmailChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteEmailChannelResponse' => [ 'type' => 'structure', 'members' => [ 'EmailChannelResponse' => [ 'shape' => 'EmailChannelResponse', ], ], 'required' => [ 'EmailChannelResponse', ], 'payload' => 'EmailChannelResponse', ], 'DeleteEmailTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'DeleteEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'DeleteEndpointRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndpointId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'endpoint-id', ], ], 'required' => [ 'ApplicationId', 'EndpointId', ], ], 'DeleteEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointResponse' => [ 'shape' => 'EndpointResponse', ], ], 'required' => [ 'EndpointResponse', ], 'payload' => 'EndpointResponse', ], 'DeleteEventStreamRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteEventStreamResponse' => [ 'type' => 'structure', 'members' => [ 'EventStream' => [ 'shape' => 'EventStream', ], ], 'required' => [ 'EventStream', ], 'payload' => 'EventStream', ], 'DeleteGcmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteGcmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'GCMChannelResponse' => [ 'shape' => 'GCMChannelResponse', ], ], 'required' => [ 'GCMChannelResponse', ], 'payload' => 'GCMChannelResponse', ], 'DeleteInAppTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'DeleteInAppTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'DeleteJourneyRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], ], 'required' => [ 'JourneyId', 'ApplicationId', ], ], 'DeleteJourneyResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyResponse' => [ 'shape' => 'JourneyResponse', ], ], 'required' => [ 'JourneyResponse', ], 'payload' => 'JourneyResponse', ], 'DeletePushTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'DeletePushTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'DeleteRecommenderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'RecommenderId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'recommender-id', ], ], 'required' => [ 'RecommenderId', ], ], 'DeleteRecommenderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RecommenderConfigurationResponse' => [ 'shape' => 'RecommenderConfigurationResponse', ], ], 'required' => [ 'RecommenderConfigurationResponse', ], 'payload' => 'RecommenderConfigurationResponse', ], 'DeleteSegmentRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], ], 'required' => [ 'SegmentId', 'ApplicationId', ], ], 'DeleteSegmentResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentResponse' => [ 'shape' => 'SegmentResponse', ], ], 'required' => [ 'SegmentResponse', ], 'payload' => 'SegmentResponse', ], 'DeleteSmsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteSmsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'SMSChannelResponse' => [ 'shape' => 'SMSChannelResponse', ], ], 'required' => [ 'SMSChannelResponse', ], 'payload' => 'SMSChannelResponse', ], 'DeleteSmsTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'DeleteSmsTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'DeleteUserEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'UserId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'user-id', ], ], 'required' => [ 'ApplicationId', 'UserId', ], ], 'DeleteUserEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointsResponse' => [ 'shape' => 'EndpointsResponse', ], ], 'required' => [ 'EndpointsResponse', ], 'payload' => 'EndpointsResponse', ], 'DeleteVoiceChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'DeleteVoiceChannelResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceChannelResponse' => [ 'shape' => 'VoiceChannelResponse', ], ], 'required' => [ 'VoiceChannelResponse', ], 'payload' => 'VoiceChannelResponse', ], 'DeleteVoiceTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'DeleteVoiceTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'DeliveryStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESSFUL', 'THROTTLED', 'TEMPORARY_FAILURE', 'PERMANENT_FAILURE', 'UNKNOWN_FAILURE', 'OPT_OUT', 'DUPLICATE', ], ], 'DimensionType' => [ 'type' => 'string', 'enum' => [ 'INCLUSIVE', 'EXCLUSIVE', ], ], 'DirectMessageConfiguration' => [ 'type' => 'structure', 'members' => [ 'ADMMessage' => [ 'shape' => 'ADMMessage', ], 'APNSMessage' => [ 'shape' => 'APNSMessage', ], 'BaiduMessage' => [ 'shape' => 'BaiduMessage', ], 'DefaultMessage' => [ 'shape' => 'DefaultMessage', ], 'DefaultPushNotificationMessage' => [ 'shape' => 'DefaultPushNotificationMessage', ], 'EmailMessage' => [ 'shape' => 'EmailMessage', ], 'GCMMessage' => [ 'shape' => 'GCMMessage', ], 'SMSMessage' => [ 'shape' => 'SMSMessage', ], 'VoiceMessage' => [ 'shape' => 'VoiceMessage', ], ], ], 'Duration' => [ 'type' => 'string', 'enum' => [ 'HR_24', 'DAY_7', 'DAY_14', 'DAY_30', ], ], 'EmailChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSet' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'FromAddress' => [ 'shape' => '__string', ], 'Identity' => [ 'shape' => '__string', ], 'RoleArn' => [ 'shape' => '__string', ], 'OrchestrationSendingRoleArn' => [ 'shape' => '__string', ], ], 'required' => [ 'FromAddress', 'Identity', ], ], 'EmailChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'ConfigurationSet' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'FromAddress' => [ 'shape' => '__string', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'Identity' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'MessagesPerSecond' => [ 'shape' => '__integer', ], 'Platform' => [ 'shape' => '__string', ], 'RoleArn' => [ 'shape' => '__string', ], 'OrchestrationSendingRoleArn' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'EmailMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'FeedbackForwardingAddress' => [ 'shape' => '__string', ], 'FromAddress' => [ 'shape' => '__string', ], 'RawEmail' => [ 'shape' => 'RawEmail', ], 'ReplyToAddresses' => [ 'shape' => 'ListOf__string', ], 'SimpleEmail' => [ 'shape' => 'SimpleEmail', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], ], ], 'EmailMessageActivity' => [ 'type' => 'structure', 'members' => [ 'MessageConfig' => [ 'shape' => 'JourneyEmailMessage', ], 'NextActivity' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateVersion' => [ 'shape' => '__string', ], ], ], 'EmailTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'HtmlPart' => [ 'shape' => '__string', ], 'RecommenderId' => [ 'shape' => '__string', ], 'Subject' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TextPart' => [ 'shape' => '__string', ], ], ], 'EmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'HtmlPart' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'RecommenderId' => [ 'shape' => '__string', ], 'Subject' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'TextPart' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'EndpointBatchItem' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'MapOfListOf__string', ], 'ChannelType' => [ 'shape' => 'ChannelType', ], 'Demographic' => [ 'shape' => 'EndpointDemographic', ], 'EffectiveDate' => [ 'shape' => '__string', ], 'EndpointStatus' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'Location' => [ 'shape' => 'EndpointLocation', ], 'Metrics' => [ 'shape' => 'MapOf__double', ], 'OptOut' => [ 'shape' => '__string', ], 'RequestId' => [ 'shape' => '__string', ], 'User' => [ 'shape' => 'EndpointUser', ], ], ], 'EndpointBatchRequest' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfEndpointBatchItem', ], ], 'required' => [ 'Item', ], ], 'EndpointDemographic' => [ 'type' => 'structure', 'members' => [ 'AppVersion' => [ 'shape' => '__string', ], 'Locale' => [ 'shape' => '__string', ], 'Make' => [ 'shape' => '__string', ], 'Model' => [ 'shape' => '__string', ], 'ModelVersion' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'PlatformVersion' => [ 'shape' => '__string', ], 'Timezone' => [ 'shape' => '__string', ], ], ], 'EndpointItemResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'StatusCode' => [ 'shape' => '__integer', ], ], ], 'EndpointLocation' => [ 'type' => 'structure', 'members' => [ 'City' => [ 'shape' => '__string', ], 'Country' => [ 'shape' => '__string', ], 'Latitude' => [ 'shape' => '__double', ], 'Longitude' => [ 'shape' => '__double', ], 'PostalCode' => [ 'shape' => '__string', ], 'Region' => [ 'shape' => '__string', ], ], ], 'EndpointMessageResult' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => '__string', ], 'DeliveryStatus' => [ 'shape' => 'DeliveryStatus', ], 'MessageId' => [ 'shape' => '__string', ], 'StatusCode' => [ 'shape' => '__integer', ], 'StatusMessage' => [ 'shape' => '__string', ], 'UpdatedToken' => [ 'shape' => '__string', ], ], 'required' => [ 'DeliveryStatus', 'StatusCode', ], ], 'EndpointRequest' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'MapOfListOf__string', ], 'ChannelType' => [ 'shape' => 'ChannelType', ], 'Demographic' => [ 'shape' => 'EndpointDemographic', ], 'EffectiveDate' => [ 'shape' => '__string', ], 'EndpointStatus' => [ 'shape' => '__string', ], 'Location' => [ 'shape' => 'EndpointLocation', ], 'Metrics' => [ 'shape' => 'MapOf__double', ], 'OptOut' => [ 'shape' => '__string', ], 'RequestId' => [ 'shape' => '__string', ], 'User' => [ 'shape' => 'EndpointUser', ], ], ], 'EndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => '__string', ], 'ApplicationId' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'MapOfListOf__string', ], 'ChannelType' => [ 'shape' => 'ChannelType', ], 'CohortId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Demographic' => [ 'shape' => 'EndpointDemographic', ], 'EffectiveDate' => [ 'shape' => '__string', ], 'EndpointStatus' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'Location' => [ 'shape' => 'EndpointLocation', ], 'Metrics' => [ 'shape' => 'MapOf__double', ], 'OptOut' => [ 'shape' => '__string', ], 'RequestId' => [ 'shape' => '__string', ], 'User' => [ 'shape' => 'EndpointUser', ], ], ], 'EndpointSendConfiguration' => [ 'type' => 'structure', 'members' => [ 'BodyOverride' => [ 'shape' => '__string', ], 'Context' => [ 'shape' => 'MapOf__string', ], 'RawContent' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'TitleOverride' => [ 'shape' => '__string', ], ], ], 'EndpointUser' => [ 'type' => 'structure', 'members' => [ 'UserAttributes' => [ 'shape' => 'MapOfListOf__string', ], 'UserId' => [ 'shape' => '__string', ], ], ], 'EndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfEndpointResponse', ], ], 'required' => [ 'Item', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'AppPackageName' => [ 'shape' => '__string', ], 'AppTitle' => [ 'shape' => '__string', ], 'AppVersionCode' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'MapOf__string', ], 'ClientSdkVersion' => [ 'shape' => '__string', ], 'EventType' => [ 'shape' => '__string', ], 'Metrics' => [ 'shape' => 'MapOf__double', ], 'SdkName' => [ 'shape' => '__string', ], 'Session' => [ 'shape' => 'Session', ], 'Timestamp' => [ 'shape' => '__string', ], ], 'required' => [ 'EventType', 'Timestamp', ], ], 'EventCondition' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'EventDimensions', ], 'MessageActivity' => [ 'shape' => '__string', ], ], ], 'EventDimensions' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapOfAttributeDimension', ], 'EventType' => [ 'shape' => 'SetDimension', ], 'Metrics' => [ 'shape' => 'MapOfMetricDimension', ], ], ], 'EventFilter' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'EventDimensions', ], 'FilterType' => [ 'shape' => 'FilterType', ], ], 'required' => [ 'FilterType', 'Dimensions', ], ], 'EventItemResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'StatusCode' => [ 'shape' => '__integer', ], ], ], 'EventStartCondition' => [ 'type' => 'structure', 'members' => [ 'EventFilter' => [ 'shape' => 'EventFilter', ], 'SegmentId' => [ 'shape' => '__string', ], ], ], 'EventStream' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'DestinationStreamArn' => [ 'shape' => '__string', ], 'ExternalId' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'LastUpdatedBy' => [ 'shape' => '__string', ], 'RoleArn' => [ 'shape' => '__string', ], ], 'required' => [ 'ApplicationId', 'RoleArn', 'DestinationStreamArn', ], ], 'EventsBatch' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'PublicEndpoint', ], 'Events' => [ 'shape' => 'MapOfEvent', ], ], 'required' => [ 'Endpoint', 'Events', ], ], 'EventsRequest' => [ 'type' => 'structure', 'members' => [ 'BatchItem' => [ 'shape' => 'MapOfEventsBatch', ], ], 'required' => [ 'BatchItem', ], ], 'EventsResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'MapOfItemResponse', ], ], ], 'ExportJobRequest' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => '__string', ], 'S3UrlPrefix' => [ 'shape' => '__string', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentVersion' => [ 'shape' => '__integer', ], ], 'required' => [ 'S3UrlPrefix', 'RoleArn', ], ], 'ExportJobResource' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => '__string', ], 'S3UrlPrefix' => [ 'shape' => '__string', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentVersion' => [ 'shape' => '__integer', ], ], 'required' => [ 'S3UrlPrefix', 'RoleArn', ], ], 'ExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CompletedPieces' => [ 'shape' => '__integer', ], 'CompletionDate' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Definition' => [ 'shape' => 'ExportJobResource', ], 'FailedPieces' => [ 'shape' => '__integer', ], 'Failures' => [ 'shape' => 'ListOf__string', ], 'Id' => [ 'shape' => '__string', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'TotalFailures' => [ 'shape' => '__integer', ], 'TotalPieces' => [ 'shape' => '__integer', ], 'TotalProcessed' => [ 'shape' => '__integer', ], 'Type' => [ 'shape' => '__string', ], ], 'required' => [ 'JobStatus', 'CreationDate', 'Type', 'Definition', 'Id', 'ApplicationId', ], ], 'ExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfExportJobResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'FilterType' => [ 'type' => 'string', 'enum' => [ 'SYSTEM', 'ENDPOINT', ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 403, ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'Frequency' => [ 'type' => 'string', 'enum' => [ 'ONCE', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'EVENT', 'IN_APP_EVENT', ], ], 'GCMChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApiKey' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'ServiceJson' => [ 'shape' => '__string', ], ], ], 'GCMChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Credential' => [ 'shape' => '__string', ], 'DefaultAuthenticationMethod' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'HasFcmServiceCredentials' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'GCMMessage' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'CollapseKey' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => 'MapOf__string', ], 'IconReference' => [ 'shape' => '__string', ], 'ImageIconUrl' => [ 'shape' => '__string', ], 'ImageUrl' => [ 'shape' => '__string', ], 'PreferredAuthenticationMethod' => [ 'shape' => '__string', ], 'Priority' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'RestrictedPackageName' => [ 'shape' => '__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'SmallImageIconUrl' => [ 'shape' => '__string', ], 'Sound' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'TimeToLive' => [ 'shape' => '__integer', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'GPSCoordinates' => [ 'type' => 'structure', 'members' => [ 'Latitude' => [ 'shape' => '__double', ], 'Longitude' => [ 'shape' => '__double', ], ], 'required' => [ 'Latitude', 'Longitude', ], ], 'GPSPointDimension' => [ 'type' => 'structure', 'members' => [ 'Coordinates' => [ 'shape' => 'GPSCoordinates', ], 'RangeInKilometers' => [ 'shape' => '__double', ], ], 'required' => [ 'Coordinates', ], ], 'GetAdmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetAdmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ADMChannelResponse' => [ 'shape' => 'ADMChannelResponse', ], ], 'required' => [ 'ADMChannelResponse', ], 'payload' => 'ADMChannelResponse', ], 'GetApnsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetApnsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSChannelResponse' => [ 'shape' => 'APNSChannelResponse', ], ], 'required' => [ 'APNSChannelResponse', ], 'payload' => 'APNSChannelResponse', ], 'GetApnsSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetApnsSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSSandboxChannelResponse' => [ 'shape' => 'APNSSandboxChannelResponse', ], ], 'required' => [ 'APNSSandboxChannelResponse', ], 'payload' => 'APNSSandboxChannelResponse', ], 'GetApnsVoipChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetApnsVoipChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipChannelResponse' => [ 'shape' => 'APNSVoipChannelResponse', ], ], 'required' => [ 'APNSVoipChannelResponse', ], 'payload' => 'APNSVoipChannelResponse', ], 'GetApnsVoipSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetApnsVoipSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipSandboxChannelResponse' => [ 'shape' => 'APNSVoipSandboxChannelResponse', ], ], 'required' => [ 'APNSVoipSandboxChannelResponse', ], 'payload' => 'APNSVoipSandboxChannelResponse', ], 'GetAppRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetAppResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationResponse' => [ 'shape' => 'ApplicationResponse', ], ], 'required' => [ 'ApplicationResponse', ], 'payload' => 'ApplicationResponse', ], 'GetApplicationDateRangeKpiRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'end-time', ], 'KpiName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'kpi-name', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'StartTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'start-time', ], ], 'required' => [ 'ApplicationId', 'KpiName', ], ], 'GetApplicationDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationDateRangeKpiResponse' => [ 'shape' => 'ApplicationDateRangeKpiResponse', ], ], 'required' => [ 'ApplicationDateRangeKpiResponse', ], 'payload' => 'ApplicationDateRangeKpiResponse', ], 'GetApplicationSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetApplicationSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationSettingsResource' => [ 'shape' => 'ApplicationSettingsResource', ], ], 'required' => [ 'ApplicationSettingsResource', ], 'payload' => 'ApplicationSettingsResource', ], 'GetAppsRequest' => [ 'type' => 'structure', 'members' => [ 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], ], 'GetAppsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationsResponse' => [ 'shape' => 'ApplicationsResponse', ], ], 'required' => [ 'ApplicationsResponse', ], 'payload' => 'ApplicationsResponse', ], 'GetBaiduChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetBaiduChannelResponse' => [ 'type' => 'structure', 'members' => [ 'BaiduChannelResponse' => [ 'shape' => 'BaiduChannelResponse', ], ], 'required' => [ 'BaiduChannelResponse', ], 'payload' => 'BaiduChannelResponse', ], 'GetCampaignActivitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', 'CampaignId', ], ], 'GetCampaignActivitiesResponse' => [ 'type' => 'structure', 'members' => [ 'ActivitiesResponse' => [ 'shape' => 'ActivitiesResponse', ], ], 'required' => [ 'ActivitiesResponse', ], 'payload' => 'ActivitiesResponse', ], 'GetCampaignDateRangeKpiRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'end-time', ], 'KpiName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'kpi-name', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'StartTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'start-time', ], ], 'required' => [ 'ApplicationId', 'KpiName', 'CampaignId', ], ], 'GetCampaignDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignDateRangeKpiResponse' => [ 'shape' => 'CampaignDateRangeKpiResponse', ], ], 'required' => [ 'CampaignDateRangeKpiResponse', ], 'payload' => 'CampaignDateRangeKpiResponse', ], 'GetCampaignRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], ], 'required' => [ 'CampaignId', 'ApplicationId', ], ], 'GetCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignResponse' => [ 'shape' => 'CampaignResponse', ], ], 'required' => [ 'CampaignResponse', ], 'payload' => 'CampaignResponse', ], 'GetCampaignVersionRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], 'Version' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'version', ], ], 'required' => [ 'Version', 'ApplicationId', 'CampaignId', ], ], 'GetCampaignVersionResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignResponse' => [ 'shape' => 'CampaignResponse', ], ], 'required' => [ 'CampaignResponse', ], 'payload' => 'CampaignResponse', ], 'GetCampaignVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', 'CampaignId', ], ], 'GetCampaignVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignsResponse' => [ 'shape' => 'CampaignsResponse', ], ], 'required' => [ 'CampaignsResponse', ], 'payload' => 'CampaignsResponse', ], 'GetCampaignsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', ], ], 'GetCampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignsResponse' => [ 'shape' => 'CampaignsResponse', ], ], 'required' => [ 'CampaignsResponse', ], 'payload' => 'CampaignsResponse', ], 'GetChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelsResponse' => [ 'shape' => 'ChannelsResponse', ], ], 'required' => [ 'ChannelsResponse', ], 'payload' => 'ChannelsResponse', ], 'GetEmailChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetEmailChannelResponse' => [ 'type' => 'structure', 'members' => [ 'EmailChannelResponse' => [ 'shape' => 'EmailChannelResponse', ], ], 'required' => [ 'EmailChannelResponse', ], 'payload' => 'EmailChannelResponse', ], 'GetEmailTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'GetEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'EmailTemplateResponse' => [ 'shape' => 'EmailTemplateResponse', ], ], 'required' => [ 'EmailTemplateResponse', ], 'payload' => 'EmailTemplateResponse', ], 'GetEndpointRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndpointId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'endpoint-id', ], ], 'required' => [ 'ApplicationId', 'EndpointId', ], ], 'GetEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointResponse' => [ 'shape' => 'EndpointResponse', ], ], 'required' => [ 'EndpointResponse', ], 'payload' => 'EndpointResponse', ], 'GetEventStreamRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetEventStreamResponse' => [ 'type' => 'structure', 'members' => [ 'EventStream' => [ 'shape' => 'EventStream', ], ], 'required' => [ 'EventStream', ], 'payload' => 'EventStream', ], 'GetExportJobRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'job-id', ], ], 'required' => [ 'ApplicationId', 'JobId', ], ], 'GetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ExportJobResponse' => [ 'shape' => 'ExportJobResponse', ], ], 'required' => [ 'ExportJobResponse', ], 'payload' => 'ExportJobResponse', ], 'GetExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', ], ], 'GetExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ExportJobsResponse' => [ 'shape' => 'ExportJobsResponse', ], ], 'required' => [ 'ExportJobsResponse', ], 'payload' => 'ExportJobsResponse', ], 'GetGcmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetGcmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'GCMChannelResponse' => [ 'shape' => 'GCMChannelResponse', ], ], 'required' => [ 'GCMChannelResponse', ], 'payload' => 'GCMChannelResponse', ], 'GetImportJobRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'job-id', ], ], 'required' => [ 'ApplicationId', 'JobId', ], ], 'GetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ImportJobResponse' => [ 'shape' => 'ImportJobResponse', ], ], 'required' => [ 'ImportJobResponse', ], 'payload' => 'ImportJobResponse', ], 'GetImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', ], ], 'GetImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ImportJobsResponse' => [ 'shape' => 'ImportJobsResponse', ], ], 'required' => [ 'ImportJobsResponse', ], 'payload' => 'ImportJobsResponse', ], 'GetInAppMessagesRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndpointId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'endpoint-id', ], ], 'required' => [ 'ApplicationId', 'EndpointId', ], ], 'GetInAppMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'InAppMessagesResponse' => [ 'shape' => 'InAppMessagesResponse', ], ], 'required' => [ 'InAppMessagesResponse', ], 'payload' => 'InAppMessagesResponse', ], 'GetInAppTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'GetInAppTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'InAppTemplateResponse' => [ 'shape' => 'InAppTemplateResponse', ], ], 'required' => [ 'InAppTemplateResponse', ], 'payload' => 'InAppTemplateResponse', ], 'GetJourneyDateRangeKpiRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'end-time', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'KpiName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'kpi-name', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'StartTime' => [ 'shape' => '__timestampIso8601', 'location' => 'querystring', 'locationName' => 'start-time', ], ], 'required' => [ 'JourneyId', 'ApplicationId', 'KpiName', ], ], 'GetJourneyDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyDateRangeKpiResponse' => [ 'shape' => 'JourneyDateRangeKpiResponse', ], ], 'required' => [ 'JourneyDateRangeKpiResponse', ], 'payload' => 'JourneyDateRangeKpiResponse', ], 'GetJourneyExecutionActivityMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyActivityId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-activity-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], ], 'required' => [ 'JourneyActivityId', 'ApplicationId', 'JourneyId', ], ], 'GetJourneyExecutionActivityMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyExecutionActivityMetricsResponse' => [ 'shape' => 'JourneyExecutionActivityMetricsResponse', ], ], 'required' => [ 'JourneyExecutionActivityMetricsResponse', ], 'payload' => 'JourneyExecutionActivityMetricsResponse', ], 'GetJourneyExecutionMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], ], 'required' => [ 'ApplicationId', 'JourneyId', ], ], 'GetJourneyExecutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyExecutionMetricsResponse' => [ 'shape' => 'JourneyExecutionMetricsResponse', ], ], 'required' => [ 'JourneyExecutionMetricsResponse', ], 'payload' => 'JourneyExecutionMetricsResponse', ], 'GetJourneyRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], ], 'required' => [ 'JourneyId', 'ApplicationId', ], ], 'GetJourneyResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyResponse' => [ 'shape' => 'JourneyResponse', ], ], 'required' => [ 'JourneyResponse', ], 'payload' => 'JourneyResponse', ], 'GetJourneyRunExecutionActivityMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyActivityId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-activity-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'RunId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'run-id', ], ], 'required' => [ 'RunId', 'JourneyActivityId', 'JourneyId', 'ApplicationId', ], ], 'GetJourneyRunExecutionActivityMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyRunExecutionActivityMetricsResponse' => [ 'shape' => 'JourneyRunExecutionActivityMetricsResponse', ], ], 'required' => [ 'JourneyRunExecutionActivityMetricsResponse', ], 'payload' => 'JourneyRunExecutionActivityMetricsResponse', ], 'GetJourneyRunExecutionMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'RunId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'run-id', ], ], 'required' => [ 'RunId', 'ApplicationId', 'JourneyId', ], ], 'GetJourneyRunExecutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyRunExecutionMetricsResponse' => [ 'shape' => 'JourneyRunExecutionMetricsResponse', ], ], 'required' => [ 'JourneyRunExecutionMetricsResponse', ], 'payload' => 'JourneyRunExecutionMetricsResponse', ], 'GetJourneyRunsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', 'JourneyId', ], ], 'GetJourneyRunsResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyRunsResponse' => [ 'shape' => 'JourneyRunsResponse', ], ], 'required' => [ 'JourneyRunsResponse', ], 'payload' => 'JourneyRunsResponse', ], 'GetPushTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'GetPushTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'PushNotificationTemplateResponse' => [ 'shape' => 'PushNotificationTemplateResponse', ], ], 'required' => [ 'PushNotificationTemplateResponse', ], 'payload' => 'PushNotificationTemplateResponse', ], 'GetRecommenderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'RecommenderId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'recommender-id', ], ], 'required' => [ 'RecommenderId', ], ], 'GetRecommenderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RecommenderConfigurationResponse' => [ 'shape' => 'RecommenderConfigurationResponse', ], ], 'required' => [ 'RecommenderConfigurationResponse', ], 'payload' => 'RecommenderConfigurationResponse', ], 'GetRecommenderConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], ], 'GetRecommenderConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'ListRecommenderConfigurationsResponse' => [ 'shape' => 'ListRecommenderConfigurationsResponse', ], ], 'required' => [ 'ListRecommenderConfigurationsResponse', ], 'payload' => 'ListRecommenderConfigurationsResponse', ], 'GetSegmentExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'SegmentId', 'ApplicationId', ], ], 'GetSegmentExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ExportJobsResponse' => [ 'shape' => 'ExportJobsResponse', ], ], 'required' => [ 'ExportJobsResponse', ], 'payload' => 'ExportJobsResponse', ], 'GetSegmentImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'SegmentId', 'ApplicationId', ], ], 'GetSegmentImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ImportJobsResponse' => [ 'shape' => 'ImportJobsResponse', ], ], 'required' => [ 'ImportJobsResponse', ], 'payload' => 'ImportJobsResponse', ], 'GetSegmentRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], ], 'required' => [ 'SegmentId', 'ApplicationId', ], ], 'GetSegmentResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentResponse' => [ 'shape' => 'SegmentResponse', ], ], 'required' => [ 'SegmentResponse', ], 'payload' => 'SegmentResponse', ], 'GetSegmentVersionRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], 'Version' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'version', ], ], 'required' => [ 'SegmentId', 'Version', 'ApplicationId', ], ], 'GetSegmentVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentResponse' => [ 'shape' => 'SegmentResponse', ], ], 'required' => [ 'SegmentResponse', ], 'payload' => 'SegmentResponse', ], 'GetSegmentVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'SegmentId', 'ApplicationId', ], ], 'GetSegmentVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentsResponse' => [ 'shape' => 'SegmentsResponse', ], ], 'required' => [ 'SegmentsResponse', ], 'payload' => 'SegmentsResponse', ], 'GetSegmentsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', ], ], 'GetSegmentsResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentsResponse' => [ 'shape' => 'SegmentsResponse', ], ], 'required' => [ 'SegmentsResponse', ], 'payload' => 'SegmentsResponse', ], 'GetSmsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetSmsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'SMSChannelResponse' => [ 'shape' => 'SMSChannelResponse', ], ], 'required' => [ 'SMSChannelResponse', ], 'payload' => 'SMSChannelResponse', ], 'GetSmsTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'GetSmsTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'SMSTemplateResponse' => [ 'shape' => 'SMSTemplateResponse', ], ], 'required' => [ 'SMSTemplateResponse', ], 'payload' => 'SMSTemplateResponse', ], 'GetUserEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'UserId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'user-id', ], ], 'required' => [ 'ApplicationId', 'UserId', ], ], 'GetUserEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointsResponse' => [ 'shape' => 'EndpointsResponse', ], ], 'required' => [ 'EndpointsResponse', ], 'payload' => 'EndpointsResponse', ], 'GetVoiceChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', ], ], 'GetVoiceChannelResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceChannelResponse' => [ 'shape' => 'VoiceChannelResponse', ], ], 'required' => [ 'VoiceChannelResponse', ], 'payload' => 'VoiceChannelResponse', ], 'GetVoiceTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', ], ], 'GetVoiceTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceTemplateResponse' => [ 'shape' => 'VoiceTemplateResponse', ], ], 'required' => [ 'VoiceTemplateResponse', ], 'payload' => 'VoiceTemplateResponse', ], 'HoldoutActivity' => [ 'type' => 'structure', 'members' => [ 'NextActivity' => [ 'shape' => '__string', ], 'Percentage' => [ 'shape' => '__integer', ], ], 'required' => [ 'Percentage', ], ], 'ImportJobRequest' => [ 'type' => 'structure', 'members' => [ 'DefineSegment' => [ 'shape' => '__boolean', ], 'ExternalId' => [ 'shape' => '__string', ], 'Format' => [ 'shape' => 'Format', ], 'RegisterEndpoints' => [ 'shape' => '__boolean', ], 'RoleArn' => [ 'shape' => '__string', ], 'S3Url' => [ 'shape' => '__string', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentName' => [ 'shape' => '__string', ], ], 'required' => [ 'Format', 'S3Url', 'RoleArn', ], ], 'ImportJobResource' => [ 'type' => 'structure', 'members' => [ 'DefineSegment' => [ 'shape' => '__boolean', ], 'ExternalId' => [ 'shape' => '__string', ], 'Format' => [ 'shape' => 'Format', ], 'RegisterEndpoints' => [ 'shape' => '__boolean', ], 'RoleArn' => [ 'shape' => '__string', ], 'S3Url' => [ 'shape' => '__string', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentName' => [ 'shape' => '__string', ], ], 'required' => [ 'Format', 'S3Url', 'RoleArn', ], ], 'ImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CompletedPieces' => [ 'shape' => '__integer', ], 'CompletionDate' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Definition' => [ 'shape' => 'ImportJobResource', ], 'FailedPieces' => [ 'shape' => '__integer', ], 'Failures' => [ 'shape' => 'ListOf__string', ], 'Id' => [ 'shape' => '__string', ], 'JobStatus' => [ 'shape' => 'JobStatus', ], 'TotalFailures' => [ 'shape' => '__integer', ], 'TotalPieces' => [ 'shape' => '__integer', ], 'TotalProcessed' => [ 'shape' => '__integer', ], 'Type' => [ 'shape' => '__string', ], ], 'required' => [ 'JobStatus', 'CreationDate', 'Type', 'Definition', 'Id', 'ApplicationId', ], ], 'ImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfImportJobResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'InAppCampaignSchedule' => [ 'type' => 'structure', 'members' => [ 'EndDate' => [ 'shape' => '__string', ], 'EventFilter' => [ 'shape' => 'CampaignEventFilter', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], ], ], 'InAppMessage' => [ 'type' => 'structure', 'members' => [ 'Content' => [ 'shape' => 'ListOfInAppMessageContent', ], 'CustomConfig' => [ 'shape' => 'MapOf__string', ], 'Layout' => [ 'shape' => 'Layout', ], ], ], 'InAppMessageBodyConfig' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'Alignment', ], 'Body' => [ 'shape' => '__string', ], 'TextColor' => [ 'shape' => '__string', ], ], 'required' => [ 'Alignment', 'TextColor', 'Body', ], ], 'InAppMessageButton' => [ 'type' => 'structure', 'members' => [ 'Android' => [ 'shape' => 'OverrideButtonConfiguration', ], 'DefaultConfig' => [ 'shape' => 'DefaultButtonConfiguration', ], 'IOS' => [ 'shape' => 'OverrideButtonConfiguration', ], 'Web' => [ 'shape' => 'OverrideButtonConfiguration', ], ], ], 'InAppMessageCampaign' => [ 'type' => 'structure', 'members' => [ 'CampaignId' => [ 'shape' => '__string', ], 'DailyCap' => [ 'shape' => '__integer', ], 'InAppMessage' => [ 'shape' => 'InAppMessage', ], 'Priority' => [ 'shape' => '__integer', ], 'Schedule' => [ 'shape' => 'InAppCampaignSchedule', ], 'SessionCap' => [ 'shape' => '__integer', ], 'TotalCap' => [ 'shape' => '__integer', ], 'TreatmentId' => [ 'shape' => '__string', ], ], ], 'InAppMessageContent' => [ 'type' => 'structure', 'members' => [ 'BackgroundColor' => [ 'shape' => '__string', ], 'BodyConfig' => [ 'shape' => 'InAppMessageBodyConfig', ], 'HeaderConfig' => [ 'shape' => 'InAppMessageHeaderConfig', ], 'ImageUrl' => [ 'shape' => '__string', ], 'PrimaryBtn' => [ 'shape' => 'InAppMessageButton', ], 'SecondaryBtn' => [ 'shape' => 'InAppMessageButton', ], ], ], 'InAppMessageHeaderConfig' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'Alignment', ], 'Header' => [ 'shape' => '__string', ], 'TextColor' => [ 'shape' => '__string', ], ], 'required' => [ 'Alignment', 'Header', 'TextColor', ], ], 'InAppMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'InAppMessageCampaigns' => [ 'shape' => 'ListOfInAppMessageCampaign', ], ], ], 'InAppTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Content' => [ 'shape' => 'ListOfInAppMessageContent', ], 'CustomConfig' => [ 'shape' => 'MapOf__string', ], 'Layout' => [ 'shape' => 'Layout', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], ], ], 'InAppTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Content' => [ 'shape' => 'ListOfInAppMessageContent', ], 'CreationDate' => [ 'shape' => '__string', ], 'CustomConfig' => [ 'shape' => 'MapOf__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Layout' => [ 'shape' => 'Layout', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'Include' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ANY', 'NONE', ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 500, ], ], 'ItemResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointItemResponse' => [ 'shape' => 'EndpointItemResponse', ], 'EventsItemResponse' => [ 'shape' => 'MapOfEventItemResponse', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PREPARING_FOR_INITIALIZATION', 'INITIALIZING', 'PROCESSING', 'PENDING_JOB', 'COMPLETING', 'COMPLETED', 'FAILING', 'FAILED', ], ], 'JourneyCustomMessage' => [ 'type' => 'structure', 'members' => [ 'Data' => [ 'shape' => '__string', ], ], ], 'JourneyDateRangeKpiResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'EndTime' => [ 'shape' => '__timestampIso8601', ], 'JourneyId' => [ 'shape' => '__string', ], 'KpiName' => [ 'shape' => '__string', ], 'KpiResult' => [ 'shape' => 'BaseKpiResult', ], 'NextToken' => [ 'shape' => '__string', ], 'StartTime' => [ 'shape' => '__timestampIso8601', ], ], 'required' => [ 'KpiResult', 'KpiName', 'JourneyId', 'EndTime', 'StartTime', 'ApplicationId', ], ], 'JourneyEmailMessage' => [ 'type' => 'structure', 'members' => [ 'FromAddress' => [ 'shape' => '__string', ], ], ], 'JourneyExecutionActivityMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'ActivityType' => [ 'shape' => '__string', ], 'ApplicationId' => [ 'shape' => '__string', ], 'JourneyActivityId' => [ 'shape' => '__string', ], 'JourneyId' => [ 'shape' => '__string', ], 'LastEvaluatedTime' => [ 'shape' => '__string', ], 'Metrics' => [ 'shape' => 'MapOf__string', ], ], 'required' => [ 'Metrics', 'JourneyId', 'LastEvaluatedTime', 'JourneyActivityId', 'ActivityType', 'ApplicationId', ], ], 'JourneyExecutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'JourneyId' => [ 'shape' => '__string', ], 'LastEvaluatedTime' => [ 'shape' => '__string', ], 'Metrics' => [ 'shape' => 'MapOf__string', ], ], 'required' => [ 'Metrics', 'JourneyId', 'LastEvaluatedTime', 'ApplicationId', ], ], 'JourneyLimits' => [ 'type' => 'structure', 'members' => [ 'DailyCap' => [ 'shape' => '__integer', ], 'EndpointReentryCap' => [ 'shape' => '__integer', ], 'MessagesPerSecond' => [ 'shape' => '__integer', ], 'EndpointReentryInterval' => [ 'shape' => '__string', ], 'TimeframeCap' => [ 'shape' => 'JourneyTimeframeCap', ], 'TotalCap' => [ 'shape' => '__integer', ], ], ], 'JourneyPushMessage' => [ 'type' => 'structure', 'members' => [ 'TimeToLive' => [ 'shape' => '__string', ], ], ], 'JourneyChannelSettings' => [ 'type' => 'structure', 'members' => [ 'ConnectCampaignArn' => [ 'shape' => '__string', ], 'ConnectCampaignExecutionRoleArn' => [ 'shape' => '__string', ], ], ], 'JourneyResponse' => [ 'type' => 'structure', 'members' => [ 'Activities' => [ 'shape' => 'MapOfActivity', ], 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Limits' => [ 'shape' => 'JourneyLimits', ], 'LocalTime' => [ 'shape' => '__boolean', ], 'Name' => [ 'shape' => '__string', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], 'RefreshFrequency' => [ 'shape' => '__string', ], 'Schedule' => [ 'shape' => 'JourneySchedule', ], 'StartActivity' => [ 'shape' => '__string', ], 'StartCondition' => [ 'shape' => 'StartCondition', ], 'State' => [ 'shape' => 'State', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'WaitForQuietTime' => [ 'shape' => '__boolean', ], 'RefreshOnSegmentUpdate' => [ 'shape' => '__boolean', ], 'JourneyChannelSettings' => [ 'shape' => 'JourneyChannelSettings', ], 'SendingSchedule' => [ 'shape' => '__boolean', ], 'OpenHours' => [ 'shape' => 'OpenHours', ], 'ClosedDays' => [ 'shape' => 'ClosedDays', ], 'TimezoneEstimationMethods' => [ 'shape' => 'ListOf__TimezoneEstimationMethodsElement', ], ], 'required' => [ 'Name', 'Id', 'ApplicationId', ], ], 'JourneyRunExecutionActivityMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'ActivityType' => [ 'shape' => '__string', ], 'ApplicationId' => [ 'shape' => '__string', ], 'JourneyActivityId' => [ 'shape' => '__string', ], 'JourneyId' => [ 'shape' => '__string', ], 'LastEvaluatedTime' => [ 'shape' => '__string', ], 'Metrics' => [ 'shape' => 'MapOf__string', ], 'RunId' => [ 'shape' => '__string', ], ], 'required' => [ 'Metrics', 'JourneyId', 'LastEvaluatedTime', 'JourneyActivityId', 'ActivityType', 'RunId', 'ApplicationId', ], ], 'JourneyRunExecutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'JourneyId' => [ 'shape' => '__string', ], 'LastEvaluatedTime' => [ 'shape' => '__string', ], 'Metrics' => [ 'shape' => 'MapOf__string', ], 'RunId' => [ 'shape' => '__string', ], ], 'required' => [ 'Metrics', 'JourneyId', 'LastEvaluatedTime', 'RunId', 'ApplicationId', ], ], 'JourneyRunResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => '__string', ], 'LastUpdateTime' => [ 'shape' => '__string', ], 'RunId' => [ 'shape' => '__string', ], 'Status' => [ 'shape' => 'JourneyRunStatus', ], ], 'required' => [ 'Status', 'LastUpdateTime', 'CreationTime', 'RunId', ], ], 'JourneyRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfJourneyRunResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'JourneyRunStatus' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'RUNNING', 'COMPLETED', 'CANCELLED', ], ], 'JourneySMSMessage' => [ 'type' => 'structure', 'members' => [ 'MessageType' => [ 'shape' => 'MessageType', ], 'OriginationNumber' => [ 'shape' => '__string', ], 'SenderId' => [ 'shape' => '__string', ], 'EntityId' => [ 'shape' => '__string', ], 'TemplateId' => [ 'shape' => '__string', ], ], ], 'JourneySchedule' => [ 'type' => 'structure', 'members' => [ 'EndTime' => [ 'shape' => '__timestampIso8601', ], 'StartTime' => [ 'shape' => '__timestampIso8601', ], 'Timezone' => [ 'shape' => '__string', ], ], ], 'JourneyStateRequest' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'State', ], ], ], 'JourneyTimeframeCap' => [ 'type' => 'structure', 'members' => [ 'Cap' => [ 'shape' => '__integer', ], 'Days' => [ 'shape' => '__integer', ], ], ], 'JourneysResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfJourneyResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'Layout' => [ 'type' => 'string', 'enum' => [ 'BOTTOM_BANNER', 'TOP_BANNER', 'OVERLAYS', 'MOBILE_FEED', 'MIDDLE_BANNER', 'CAROUSEL', ], ], 'ListJourneysRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Token' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'token', ], ], 'required' => [ 'ApplicationId', ], ], 'ListJourneysResponse' => [ 'type' => 'structure', 'members' => [ 'JourneysResponse' => [ 'shape' => 'JourneysResponse', ], ], 'required' => [ 'JourneysResponse', ], 'payload' => 'JourneysResponse', ], 'ListRecommenderConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfRecommenderConfigurationResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], ], 'required' => [ 'ResourceArn', ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagsModel' => [ 'shape' => 'TagsModel', ], ], 'required' => [ 'TagsModel', ], 'payload' => 'TagsModel', ], 'ListTemplateVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'TemplateType' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-type', ], ], 'required' => [ 'TemplateName', 'TemplateType', ], ], 'ListTemplateVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateVersionsResponse' => [ 'shape' => 'TemplateVersionsResponse', ], ], 'required' => [ 'TemplateVersionsResponse', ], 'payload' => 'TemplateVersionsResponse', ], 'ListTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'PageSize' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'page-size', ], 'Prefix' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'prefix', ], 'TemplateType' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'template-type', ], ], ], 'ListTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'TemplatesResponse' => [ 'shape' => 'TemplatesResponse', ], ], 'required' => [ 'TemplatesResponse', ], 'payload' => 'TemplatesResponse', ], 'Message' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'Action', ], 'Body' => [ 'shape' => '__string', ], 'ImageIconUrl' => [ 'shape' => '__string', ], 'ImageSmallIconUrl' => [ 'shape' => '__string', ], 'ImageUrl' => [ 'shape' => '__string', ], 'JsonBody' => [ 'shape' => '__string', ], 'MediaUrl' => [ 'shape' => '__string', ], 'RawContent' => [ 'shape' => '__string', ], 'SilentPush' => [ 'shape' => '__boolean', ], 'TimeToLive' => [ 'shape' => '__integer', ], 'Title' => [ 'shape' => '__string', ], 'Url' => [ 'shape' => '__string', ], ], ], 'MessageBody' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], ], 'MessageConfiguration' => [ 'type' => 'structure', 'members' => [ 'ADMMessage' => [ 'shape' => 'Message', ], 'APNSMessage' => [ 'shape' => 'Message', ], 'BaiduMessage' => [ 'shape' => 'Message', ], 'CustomMessage' => [ 'shape' => 'CampaignCustomMessage', ], 'DefaultMessage' => [ 'shape' => 'Message', ], 'EmailMessage' => [ 'shape' => 'CampaignEmailMessage', ], 'GCMMessage' => [ 'shape' => 'Message', ], 'SMSMessage' => [ 'shape' => 'CampaignSmsMessage', ], 'InAppMessage' => [ 'shape' => 'CampaignInAppMessage', ], ], ], 'MessageRequest' => [ 'type' => 'structure', 'members' => [ 'Addresses' => [ 'shape' => 'MapOfAddressConfiguration', ], 'Context' => [ 'shape' => 'MapOf__string', ], 'Endpoints' => [ 'shape' => 'MapOfEndpointSendConfiguration', ], 'MessageConfiguration' => [ 'shape' => 'DirectMessageConfiguration', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TraceId' => [ 'shape' => '__string', ], ], 'required' => [ 'MessageConfiguration', ], ], 'MessageResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'EndpointResult' => [ 'shape' => 'MapOfEndpointMessageResult', ], 'RequestId' => [ 'shape' => '__string', ], 'Result' => [ 'shape' => 'MapOfMessageResult', ], ], 'required' => [ 'ApplicationId', ], ], 'MessageResult' => [ 'type' => 'structure', 'members' => [ 'DeliveryStatus' => [ 'shape' => 'DeliveryStatus', ], 'MessageId' => [ 'shape' => '__string', ], 'StatusCode' => [ 'shape' => '__integer', ], 'StatusMessage' => [ 'shape' => '__string', ], 'UpdatedToken' => [ 'shape' => '__string', ], ], 'required' => [ 'DeliveryStatus', 'StatusCode', ], ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'TRANSACTIONAL', 'PROMOTIONAL', ], ], 'MethodNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 405, ], ], 'MetricDimension' => [ 'type' => 'structure', 'members' => [ 'ComparisonOperator' => [ 'shape' => '__string', ], 'Value' => [ 'shape' => '__double', ], ], 'required' => [ 'ComparisonOperator', 'Value', ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'DELIVERY', 'FILTER', ], ], 'MultiConditionalBranch' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'SimpleCondition', ], 'NextActivity' => [ 'shape' => '__string', ], ], ], 'MultiConditionalSplitActivity' => [ 'type' => 'structure', 'members' => [ 'Branches' => [ 'shape' => 'ListOfMultiConditionalBranch', ], 'DefaultActivity' => [ 'shape' => '__string', ], 'EvaluationWaitTime' => [ 'shape' => 'WaitTime', ], ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'NumberValidateRequest' => [ 'type' => 'structure', 'members' => [ 'IsoCountryCode' => [ 'shape' => '__string', ], 'PhoneNumber' => [ 'shape' => '__string', ], ], ], 'NumberValidateResponse' => [ 'type' => 'structure', 'members' => [ 'Carrier' => [ 'shape' => '__string', ], 'City' => [ 'shape' => '__string', ], 'CleansedPhoneNumberE164' => [ 'shape' => '__string', ], 'CleansedPhoneNumberNational' => [ 'shape' => '__string', ], 'Country' => [ 'shape' => '__string', ], 'CountryCodeIso2' => [ 'shape' => '__string', ], 'CountryCodeNumeric' => [ 'shape' => '__string', ], 'County' => [ 'shape' => '__string', ], 'OriginalCountryCodeIso2' => [ 'shape' => '__string', ], 'OriginalPhoneNumber' => [ 'shape' => '__string', ], 'PhoneType' => [ 'shape' => '__string', ], 'PhoneTypeCode' => [ 'shape' => '__integer', ], 'Timezone' => [ 'shape' => '__string', ], 'ZipCode' => [ 'shape' => '__string', ], ], ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ANY', ], ], 'OverrideButtonConfiguration' => [ 'type' => 'structure', 'members' => [ 'ButtonAction' => [ 'shape' => 'ButtonAction', ], 'Link' => [ 'shape' => '__string', ], ], 'required' => [ 'ButtonAction', ], ], 'PayloadTooLargeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 413, ], ], 'PhoneNumberValidateRequest' => [ 'type' => 'structure', 'members' => [ 'NumberValidateRequest' => [ 'shape' => 'NumberValidateRequest', ], ], 'required' => [ 'NumberValidateRequest', ], 'payload' => 'NumberValidateRequest', ], 'PhoneNumberValidateResponse' => [ 'type' => 'structure', 'members' => [ 'NumberValidateResponse' => [ 'shape' => 'NumberValidateResponse', ], ], 'required' => [ 'NumberValidateResponse', ], 'payload' => 'NumberValidateResponse', ], 'PublicEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => '__string', ], 'Attributes' => [ 'shape' => 'MapOfListOf__string', ], 'ChannelType' => [ 'shape' => 'ChannelType', ], 'Demographic' => [ 'shape' => 'EndpointDemographic', ], 'EffectiveDate' => [ 'shape' => '__string', ], 'EndpointStatus' => [ 'shape' => '__string', ], 'Location' => [ 'shape' => 'EndpointLocation', ], 'Metrics' => [ 'shape' => 'MapOf__double', ], 'OptOut' => [ 'shape' => '__string', ], 'RequestId' => [ 'shape' => '__string', ], 'User' => [ 'shape' => 'EndpointUser', ], ], ], 'PushMessageActivity' => [ 'type' => 'structure', 'members' => [ 'MessageConfig' => [ 'shape' => 'JourneyPushMessage', ], 'NextActivity' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateVersion' => [ 'shape' => '__string', ], ], ], 'PushNotificationTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'ADM' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'APNS' => [ 'shape' => 'APNSPushNotificationTemplate', ], 'Baidu' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'Default' => [ 'shape' => 'DefaultPushNotificationTemplate', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'GCM' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'RecommenderId' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], ], ], 'PushNotificationTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'ADM' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'APNS' => [ 'shape' => 'APNSPushNotificationTemplate', ], 'Arn' => [ 'shape' => '__string', ], 'Baidu' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'CreationDate' => [ 'shape' => '__string', ], 'Default' => [ 'shape' => 'DefaultPushNotificationTemplate', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'GCM' => [ 'shape' => 'AndroidPushNotificationTemplate', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'RecommenderId' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateType', 'TemplateName', ], ], 'PutEventStreamRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'WriteEventStream' => [ 'shape' => 'WriteEventStream', ], ], 'required' => [ 'ApplicationId', 'WriteEventStream', ], 'payload' => 'WriteEventStream', ], 'PutEventStreamResponse' => [ 'type' => 'structure', 'members' => [ 'EventStream' => [ 'shape' => 'EventStream', ], ], 'required' => [ 'EventStream', ], 'payload' => 'EventStream', ], 'PutEventsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EventsRequest' => [ 'shape' => 'EventsRequest', ], ], 'required' => [ 'ApplicationId', 'EventsRequest', ], 'payload' => 'EventsRequest', ], 'PutEventsResponse' => [ 'type' => 'structure', 'members' => [ 'EventsResponse' => [ 'shape' => 'EventsResponse', ], ], 'required' => [ 'EventsResponse', ], 'payload' => 'EventsResponse', ], 'QuietTime' => [ 'type' => 'structure', 'members' => [ 'End' => [ 'shape' => '__string', ], 'Start' => [ 'shape' => '__string', ], ], ], 'RandomSplitActivity' => [ 'type' => 'structure', 'members' => [ 'Branches' => [ 'shape' => 'ListOfRandomSplitEntry', ], ], ], 'RandomSplitEntry' => [ 'type' => 'structure', 'members' => [ 'NextActivity' => [ 'shape' => '__string', ], 'Percentage' => [ 'shape' => '__integer', ], ], ], 'RawEmail' => [ 'type' => 'structure', 'members' => [ 'Data' => [ 'shape' => '__blob', ], ], ], '__blob' => [ 'type' => 'blob', ], 'RecencyDimension' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => 'Duration', ], 'RecencyType' => [ 'shape' => 'RecencyType', ], ], 'required' => [ 'Duration', 'RecencyType', ], ], 'RecencyType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'RecommenderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapOf__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Description' => [ 'shape' => '__string', ], 'Id' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], 'RecommendationProviderIdType' => [ 'shape' => '__string', ], 'RecommendationProviderRoleArn' => [ 'shape' => '__string', ], 'RecommendationProviderUri' => [ 'shape' => '__string', ], 'RecommendationTransformerUri' => [ 'shape' => '__string', ], 'RecommendationsDisplayName' => [ 'shape' => '__string', ], 'RecommendationsPerMessage' => [ 'shape' => '__integer', ], ], 'required' => [ 'RecommendationProviderUri', 'LastModifiedDate', 'CreationDate', 'RecommendationProviderRoleArn', 'Id', ], ], 'RemoveAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'AttributeType' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'attribute-type', ], 'UpdateAttributesRequest' => [ 'shape' => 'UpdateAttributesRequest', ], ], 'required' => [ 'AttributeType', 'ApplicationId', 'UpdateAttributesRequest', ], 'payload' => 'UpdateAttributesRequest', ], 'RemoveAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AttributesResource' => [ 'shape' => 'AttributesResource', ], ], 'required' => [ 'AttributesResource', ], 'payload' => 'AttributesResource', ], 'ResultRow' => [ 'type' => 'structure', 'members' => [ 'GroupedBys' => [ 'shape' => 'ListOfResultRowValue', ], 'Values' => [ 'shape' => 'ListOfResultRowValue', ], ], 'required' => [ 'GroupedBys', 'Values', ], ], 'ResultRowValue' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => '__string', ], 'Type' => [ 'shape' => '__string', ], 'Value' => [ 'shape' => '__string', ], ], 'required' => [ 'Type', 'Value', 'Key', ], ], 'SMSChannelRequest' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', ], 'SenderId' => [ 'shape' => '__string', ], 'ShortCode' => [ 'shape' => '__string', ], ], ], 'SMSChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'PromotionalMessagesPerSecond' => [ 'shape' => '__integer', ], 'SenderId' => [ 'shape' => '__string', ], 'ShortCode' => [ 'shape' => '__string', ], 'TransactionalMessagesPerSecond' => [ 'shape' => '__integer', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'SMSMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'Keyword' => [ 'shape' => '__string', ], 'MediaUrl' => [ 'shape' => '__string', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'OriginationNumber' => [ 'shape' => '__string', ], 'SenderId' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'EntityId' => [ 'shape' => '__string', ], 'TemplateId' => [ 'shape' => '__string', ], ], ], 'SMSMessageActivity' => [ 'type' => 'structure', 'members' => [ 'MessageConfig' => [ 'shape' => 'JourneySMSMessage', ], 'NextActivity' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateVersion' => [ 'shape' => '__string', ], ], ], 'SMSTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'RecommenderId' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], ], ], 'SMSTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Body' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'RecommenderId' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'EndTime' => [ 'shape' => '__string', ], 'EventFilter' => [ 'shape' => 'CampaignEventFilter', ], 'Frequency' => [ 'shape' => 'Frequency', ], 'IsLocalTime' => [ 'shape' => '__boolean', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], 'StartTime' => [ 'shape' => '__string', ], 'Timezone' => [ 'shape' => '__string', ], ], 'required' => [ 'StartTime', ], ], 'SegmentBehaviors' => [ 'type' => 'structure', 'members' => [ 'Recency' => [ 'shape' => 'RecencyDimension', ], ], ], 'SegmentCondition' => [ 'type' => 'structure', 'members' => [ 'SegmentId' => [ 'shape' => '__string', ], ], 'required' => [ 'SegmentId', ], ], 'SegmentDemographics' => [ 'type' => 'structure', 'members' => [ 'AppVersion' => [ 'shape' => 'SetDimension', ], 'Channel' => [ 'shape' => 'SetDimension', ], 'DeviceType' => [ 'shape' => 'SetDimension', ], 'Make' => [ 'shape' => 'SetDimension', ], 'Model' => [ 'shape' => 'SetDimension', ], 'Platform' => [ 'shape' => 'SetDimension', ], ], ], 'SegmentDimensions' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapOfAttributeDimension', ], 'Behavior' => [ 'shape' => 'SegmentBehaviors', ], 'Demographic' => [ 'shape' => 'SegmentDemographics', ], 'Location' => [ 'shape' => 'SegmentLocation', ], 'Metrics' => [ 'shape' => 'MapOfMetricDimension', ], 'UserAttributes' => [ 'shape' => 'MapOfAttributeDimension', ], ], ], 'SegmentGroup' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'ListOfSegmentDimensions', ], 'SourceSegments' => [ 'shape' => 'ListOfSegmentReference', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'SegmentGroupList' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'ListOfSegmentGroup', ], 'Include' => [ 'shape' => 'Include', ], ], ], 'SegmentImportResource' => [ 'type' => 'structure', 'members' => [ 'ChannelCounts' => [ 'shape' => 'MapOf__integer', ], 'ExternalId' => [ 'shape' => '__string', ], 'Format' => [ 'shape' => 'Format', ], 'RoleArn' => [ 'shape' => '__string', ], 'S3Url' => [ 'shape' => '__string', ], 'Size' => [ 'shape' => '__integer', ], ], 'required' => [ 'Format', 'S3Url', 'Size', 'ExternalId', 'RoleArn', ], ], 'SegmentLocation' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'SetDimension', ], 'GPSPoint' => [ 'shape' => 'GPSPointDimension', ], ], ], 'SegmentReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Id', ], ], 'SegmentResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'Arn' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Dimensions' => [ 'shape' => 'SegmentDimensions', ], 'Id' => [ 'shape' => '__string', ], 'ImportDefinition' => [ 'shape' => 'SegmentImportResource', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], 'SegmentGroups' => [ 'shape' => 'SegmentGroupList', ], 'SegmentType' => [ 'shape' => 'SegmentType', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'SegmentType', 'CreationDate', 'Id', 'Arn', 'ApplicationId', ], ], 'SegmentType' => [ 'type' => 'string', 'enum' => [ 'DIMENSIONAL', 'IMPORT', ], ], 'SegmentsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfSegmentResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'SendMessagesRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'MessageRequest' => [ 'shape' => 'MessageRequest', ], ], 'required' => [ 'ApplicationId', 'MessageRequest', ], 'payload' => 'MessageRequest', ], 'SendMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'MessageResponse' => [ 'shape' => 'MessageResponse', ], ], 'required' => [ 'MessageResponse', ], 'payload' => 'MessageResponse', ], 'SendOTPMessageRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SendOTPMessageRequestParameters' => [ 'shape' => 'SendOTPMessageRequestParameters', ], ], 'required' => [ 'ApplicationId', 'SendOTPMessageRequestParameters', ], 'payload' => 'SendOTPMessageRequestParameters', ], 'SendOTPMessageRequestParameters' => [ 'type' => 'structure', 'members' => [ 'AllowedAttempts' => [ 'shape' => '__integer', ], 'BrandName' => [ 'shape' => '__string', ], 'Channel' => [ 'shape' => '__string', ], 'CodeLength' => [ 'shape' => '__integer', ], 'DestinationIdentity' => [ 'shape' => '__string', ], 'EntityId' => [ 'shape' => '__string', ], 'Language' => [ 'shape' => '__string', ], 'OriginationIdentity' => [ 'shape' => '__string', ], 'ReferenceId' => [ 'shape' => '__string', ], 'TemplateId' => [ 'shape' => '__string', ], 'ValidityPeriod' => [ 'shape' => '__integer', ], ], 'required' => [ 'BrandName', 'ReferenceId', 'Channel', 'DestinationIdentity', 'OriginationIdentity', ], ], 'SendOTPMessageResponse' => [ 'type' => 'structure', 'members' => [ 'MessageResponse' => [ 'shape' => 'MessageResponse', ], ], 'required' => [ 'MessageResponse', ], 'payload' => 'MessageResponse', ], 'SendUsersMessageRequest' => [ 'type' => 'structure', 'members' => [ 'Context' => [ 'shape' => 'MapOf__string', ], 'MessageConfiguration' => [ 'shape' => 'DirectMessageConfiguration', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TraceId' => [ 'shape' => '__string', ], 'Users' => [ 'shape' => 'MapOfEndpointSendConfiguration', ], ], 'required' => [ 'MessageConfiguration', 'Users', ], ], 'SendUsersMessageResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'RequestId' => [ 'shape' => '__string', ], 'Result' => [ 'shape' => 'MapOfMapOfEndpointMessageResult', ], ], 'required' => [ 'ApplicationId', ], ], 'SendUsersMessagesRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SendUsersMessageRequest' => [ 'shape' => 'SendUsersMessageRequest', ], ], 'required' => [ 'ApplicationId', 'SendUsersMessageRequest', ], 'payload' => 'SendUsersMessageRequest', ], 'SendUsersMessagesResponse' => [ 'type' => 'structure', 'members' => [ 'SendUsersMessageResponse' => [ 'shape' => 'SendUsersMessageResponse', ], ], 'required' => [ 'SendUsersMessageResponse', ], 'payload' => 'SendUsersMessageResponse', ], 'Session' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__integer', ], 'Id' => [ 'shape' => '__string', ], 'StartTimestamp' => [ 'shape' => '__string', ], 'StopTimestamp' => [ 'shape' => '__string', ], ], 'required' => [ 'StartTimestamp', 'Id', ], ], 'SetDimension' => [ 'type' => 'structure', 'members' => [ 'DimensionType' => [ 'shape' => 'DimensionType', ], 'Values' => [ 'shape' => 'ListOf__string', ], ], 'required' => [ 'Values', ], ], 'SimpleCondition' => [ 'type' => 'structure', 'members' => [ 'EventCondition' => [ 'shape' => 'EventCondition', ], 'SegmentCondition' => [ 'shape' => 'SegmentCondition', ], 'SegmentDimensions' => [ 'shape' => 'SegmentDimensions', 'locationName' => 'segmentDimensions', ], ], ], 'SimpleEmail' => [ 'type' => 'structure', 'members' => [ 'HtmlPart' => [ 'shape' => 'SimpleEmailPart', ], 'Subject' => [ 'shape' => 'SimpleEmailPart', ], 'TextPart' => [ 'shape' => 'SimpleEmailPart', ], ], ], 'SimpleEmailPart' => [ 'type' => 'structure', 'members' => [ 'Charset' => [ 'shape' => '__string', ], 'Data' => [ 'shape' => '__string', ], ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ANY', 'NONE', ], ], 'StartCondition' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__string', ], 'EventStartCondition' => [ 'shape' => 'EventStartCondition', ], 'SegmentStartCondition' => [ 'shape' => 'SegmentCondition', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'CLOSED', 'PAUSED', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], 'TagsModel' => [ 'shape' => 'TagsModel', ], ], 'required' => [ 'ResourceArn', 'TagsModel', ], 'payload' => 'TagsModel', ], 'TagsModel' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'tags', ], ], 'Template' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__string', ], ], ], 'TemplateActiveVersionRequest' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => '__string', ], ], ], 'TemplateConfiguration' => [ 'type' => 'structure', 'members' => [ 'EmailTemplate' => [ 'shape' => 'Template', ], 'PushTemplate' => [ 'shape' => 'Template', ], 'SMSTemplate' => [ 'shape' => 'Template', ], 'VoiceTemplate' => [ 'shape' => 'Template', ], 'InAppTemplate' => [ 'shape' => 'Template', ], ], ], 'TemplateCreateMessageBody' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], ], 'TemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'TemplateType' => [ 'type' => 'string', 'enum' => [ 'EMAIL', 'SMS', 'VOICE', 'PUSH', 'INAPP', ], ], 'TemplateVersionResponse' => [ 'type' => 'structure', 'members' => [ 'CreationDate' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'TemplateVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfTemplateVersionResponse', ], 'Message' => [ 'shape' => '__string', ], 'NextToken' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'TemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'Item' => [ 'shape' => 'ListOfTemplateResponse', ], 'NextToken' => [ 'shape' => '__string', ], ], 'required' => [ 'Item', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], 'RequestID' => [ 'shape' => '__string', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'TreatmentResource' => [ 'type' => 'structure', 'members' => [ 'CustomDeliveryConfiguration' => [ 'shape' => 'CustomDeliveryConfiguration', ], 'Id' => [ 'shape' => '__string', ], 'MessageConfiguration' => [ 'shape' => 'MessageConfiguration', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'SizePercent' => [ 'shape' => '__integer', ], 'State' => [ 'shape' => 'CampaignState', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TreatmentDescription' => [ 'shape' => '__string', ], 'TreatmentName' => [ 'shape' => '__string', ], ], 'required' => [ 'Id', 'SizePercent', ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ANY', 'NONE', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], 'TagKeys' => [ 'shape' => 'ListOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], 'required' => [ 'TagKeys', 'ResourceArn', ], ], 'UpdateAdmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ADMChannelRequest' => [ 'shape' => 'ADMChannelRequest', ], 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', 'ADMChannelRequest', ], 'payload' => 'ADMChannelRequest', ], 'UpdateAdmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ADMChannelResponse' => [ 'shape' => 'ADMChannelResponse', ], ], 'required' => [ 'ADMChannelResponse', ], 'payload' => 'ADMChannelResponse', ], 'UpdateApnsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'APNSChannelRequest' => [ 'shape' => 'APNSChannelRequest', ], 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', 'APNSChannelRequest', ], 'payload' => 'APNSChannelRequest', ], 'UpdateApnsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSChannelResponse' => [ 'shape' => 'APNSChannelResponse', ], ], 'required' => [ 'APNSChannelResponse', ], 'payload' => 'APNSChannelResponse', ], 'UpdateApnsSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'APNSSandboxChannelRequest' => [ 'shape' => 'APNSSandboxChannelRequest', ], 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', 'APNSSandboxChannelRequest', ], 'payload' => 'APNSSandboxChannelRequest', ], 'UpdateApnsSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSSandboxChannelResponse' => [ 'shape' => 'APNSSandboxChannelResponse', ], ], 'required' => [ 'APNSSandboxChannelResponse', ], 'payload' => 'APNSSandboxChannelResponse', ], 'UpdateApnsVoipChannelRequest' => [ 'type' => 'structure', 'members' => [ 'APNSVoipChannelRequest' => [ 'shape' => 'APNSVoipChannelRequest', ], 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', 'APNSVoipChannelRequest', ], 'payload' => 'APNSVoipChannelRequest', ], 'UpdateApnsVoipChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipChannelResponse' => [ 'shape' => 'APNSVoipChannelResponse', ], ], 'required' => [ 'APNSVoipChannelResponse', ], 'payload' => 'APNSVoipChannelResponse', ], 'UpdateApnsVoipSandboxChannelRequest' => [ 'type' => 'structure', 'members' => [ 'APNSVoipSandboxChannelRequest' => [ 'shape' => 'APNSVoipSandboxChannelRequest', ], 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], ], 'required' => [ 'ApplicationId', 'APNSVoipSandboxChannelRequest', ], 'payload' => 'APNSVoipSandboxChannelRequest', ], 'UpdateApnsVoipSandboxChannelResponse' => [ 'type' => 'structure', 'members' => [ 'APNSVoipSandboxChannelResponse' => [ 'shape' => 'APNSVoipSandboxChannelResponse', ], ], 'required' => [ 'APNSVoipSandboxChannelResponse', ], 'payload' => 'APNSVoipSandboxChannelResponse', ], 'UpdateApplicationSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'WriteApplicationSettingsRequest' => [ 'shape' => 'WriteApplicationSettingsRequest', ], ], 'required' => [ 'ApplicationId', 'WriteApplicationSettingsRequest', ], 'payload' => 'WriteApplicationSettingsRequest', ], 'UpdateApplicationSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationSettingsResource' => [ 'shape' => 'ApplicationSettingsResource', ], ], 'required' => [ 'ApplicationSettingsResource', ], 'payload' => 'ApplicationSettingsResource', ], 'UpdateAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'Blacklist' => [ 'shape' => 'ListOf__string', ], ], ], 'UpdateBaiduChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'BaiduChannelRequest' => [ 'shape' => 'BaiduChannelRequest', ], ], 'required' => [ 'ApplicationId', 'BaiduChannelRequest', ], 'payload' => 'BaiduChannelRequest', ], 'UpdateBaiduChannelResponse' => [ 'type' => 'structure', 'members' => [ 'BaiduChannelResponse' => [ 'shape' => 'BaiduChannelResponse', ], ], 'required' => [ 'BaiduChannelResponse', ], 'payload' => 'BaiduChannelResponse', ], 'UpdateCampaignRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'CampaignId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'campaign-id', ], 'WriteCampaignRequest' => [ 'shape' => 'WriteCampaignRequest', ], ], 'required' => [ 'CampaignId', 'ApplicationId', 'WriteCampaignRequest', ], 'payload' => 'WriteCampaignRequest', ], 'UpdateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'CampaignResponse' => [ 'shape' => 'CampaignResponse', ], ], 'required' => [ 'CampaignResponse', ], 'payload' => 'CampaignResponse', ], 'UpdateEmailChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EmailChannelRequest' => [ 'shape' => 'EmailChannelRequest', ], ], 'required' => [ 'ApplicationId', 'EmailChannelRequest', ], 'payload' => 'EmailChannelRequest', ], 'UpdateEmailChannelResponse' => [ 'type' => 'structure', 'members' => [ 'EmailChannelResponse' => [ 'shape' => 'EmailChannelResponse', ], ], 'required' => [ 'EmailChannelResponse', ], 'payload' => 'EmailChannelResponse', ], 'UpdateEmailTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'CreateNewVersion' => [ 'shape' => '__boolean', 'location' => 'querystring', 'locationName' => 'create-new-version', ], 'EmailTemplateRequest' => [ 'shape' => 'EmailTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', 'EmailTemplateRequest', ], 'payload' => 'EmailTemplateRequest', ], 'UpdateEmailTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateEndpointRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndpointId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'endpoint-id', ], 'EndpointRequest' => [ 'shape' => 'EndpointRequest', ], ], 'required' => [ 'ApplicationId', 'EndpointId', 'EndpointRequest', ], 'payload' => 'EndpointRequest', ], 'UpdateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateEndpointsBatchRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'EndpointBatchRequest' => [ 'shape' => 'EndpointBatchRequest', ], ], 'required' => [ 'ApplicationId', 'EndpointBatchRequest', ], 'payload' => 'EndpointBatchRequest', ], 'UpdateEndpointsBatchResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateGcmChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'GCMChannelRequest' => [ 'shape' => 'GCMChannelRequest', ], ], 'required' => [ 'ApplicationId', 'GCMChannelRequest', ], 'payload' => 'GCMChannelRequest', ], 'UpdateGcmChannelResponse' => [ 'type' => 'structure', 'members' => [ 'GCMChannelResponse' => [ 'shape' => 'GCMChannelResponse', ], ], 'required' => [ 'GCMChannelResponse', ], 'payload' => 'GCMChannelResponse', ], 'UpdateInAppTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'CreateNewVersion' => [ 'shape' => '__boolean', 'location' => 'querystring', 'locationName' => 'create-new-version', ], 'InAppTemplateRequest' => [ 'shape' => 'InAppTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', 'InAppTemplateRequest', ], 'payload' => 'InAppTemplateRequest', ], 'UpdateInAppTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateJourneyRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'WriteJourneyRequest' => [ 'shape' => 'WriteJourneyRequest', ], ], 'required' => [ 'JourneyId', 'ApplicationId', 'WriteJourneyRequest', ], 'payload' => 'WriteJourneyRequest', ], 'UpdateJourneyResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyResponse' => [ 'shape' => 'JourneyResponse', ], ], 'required' => [ 'JourneyResponse', ], 'payload' => 'JourneyResponse', ], 'UpdateJourneyStateRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'JourneyId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'journey-id', ], 'JourneyStateRequest' => [ 'shape' => 'JourneyStateRequest', ], ], 'required' => [ 'JourneyId', 'ApplicationId', 'JourneyStateRequest', ], 'payload' => 'JourneyStateRequest', ], 'UpdateJourneyStateResponse' => [ 'type' => 'structure', 'members' => [ 'JourneyResponse' => [ 'shape' => 'JourneyResponse', ], ], 'required' => [ 'JourneyResponse', ], 'payload' => 'JourneyResponse', ], 'UpdatePushTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'CreateNewVersion' => [ 'shape' => '__boolean', 'location' => 'querystring', 'locationName' => 'create-new-version', ], 'PushNotificationTemplateRequest' => [ 'shape' => 'PushNotificationTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', 'PushNotificationTemplateRequest', ], 'payload' => 'PushNotificationTemplateRequest', ], 'UpdatePushTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateRecommenderConfiguration' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'MapOf__string', ], 'Description' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], 'RecommendationProviderIdType' => [ 'shape' => '__string', ], 'RecommendationProviderRoleArn' => [ 'shape' => '__string', ], 'RecommendationProviderUri' => [ 'shape' => '__string', ], 'RecommendationTransformerUri' => [ 'shape' => '__string', ], 'RecommendationsDisplayName' => [ 'shape' => '__string', ], 'RecommendationsPerMessage' => [ 'shape' => '__integer', ], ], 'required' => [ 'RecommendationProviderUri', 'RecommendationProviderRoleArn', ], ], 'UpdateRecommenderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'RecommenderId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'recommender-id', ], 'UpdateRecommenderConfiguration' => [ 'shape' => 'UpdateRecommenderConfiguration', ], ], 'required' => [ 'RecommenderId', 'UpdateRecommenderConfiguration', ], 'payload' => 'UpdateRecommenderConfiguration', ], 'UpdateRecommenderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RecommenderConfigurationResponse' => [ 'shape' => 'RecommenderConfigurationResponse', ], ], 'required' => [ 'RecommenderConfigurationResponse', ], 'payload' => 'RecommenderConfigurationResponse', ], 'UpdateSegmentRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SegmentId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'segment-id', ], 'WriteSegmentRequest' => [ 'shape' => 'WriteSegmentRequest', ], ], 'required' => [ 'SegmentId', 'ApplicationId', 'WriteSegmentRequest', ], 'payload' => 'WriteSegmentRequest', ], 'UpdateSegmentResponse' => [ 'type' => 'structure', 'members' => [ 'SegmentResponse' => [ 'shape' => 'SegmentResponse', ], ], 'required' => [ 'SegmentResponse', ], 'payload' => 'SegmentResponse', ], 'UpdateSmsChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'SMSChannelRequest' => [ 'shape' => 'SMSChannelRequest', ], ], 'required' => [ 'ApplicationId', 'SMSChannelRequest', ], 'payload' => 'SMSChannelRequest', ], 'UpdateSmsChannelResponse' => [ 'type' => 'structure', 'members' => [ 'SMSChannelResponse' => [ 'shape' => 'SMSChannelResponse', ], ], 'required' => [ 'SMSChannelResponse', ], 'payload' => 'SMSChannelResponse', ], 'UpdateSmsTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'CreateNewVersion' => [ 'shape' => '__boolean', 'location' => 'querystring', 'locationName' => 'create-new-version', ], 'SMSTemplateRequest' => [ 'shape' => 'SMSTemplateRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], ], 'required' => [ 'TemplateName', 'SMSTemplateRequest', ], 'payload' => 'SMSTemplateRequest', ], 'UpdateSmsTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateTemplateActiveVersionRequest' => [ 'type' => 'structure', 'members' => [ 'TemplateActiveVersionRequest' => [ 'shape' => 'TemplateActiveVersionRequest', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'TemplateType' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-type', ], ], 'required' => [ 'TemplateName', 'TemplateType', 'TemplateActiveVersionRequest', ], 'payload' => 'TemplateActiveVersionRequest', ], 'UpdateTemplateActiveVersionResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'UpdateVoiceChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'VoiceChannelRequest' => [ 'shape' => 'VoiceChannelRequest', ], ], 'required' => [ 'ApplicationId', 'VoiceChannelRequest', ], 'payload' => 'VoiceChannelRequest', ], 'UpdateVoiceChannelResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceChannelResponse' => [ 'shape' => 'VoiceChannelResponse', ], ], 'required' => [ 'VoiceChannelResponse', ], 'payload' => 'VoiceChannelResponse', ], 'UpdateVoiceTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'CreateNewVersion' => [ 'shape' => '__boolean', 'location' => 'querystring', 'locationName' => 'create-new-version', ], 'TemplateName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'template-name', ], 'Version' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'version', ], 'VoiceTemplateRequest' => [ 'shape' => 'VoiceTemplateRequest', ], ], 'required' => [ 'TemplateName', 'VoiceTemplateRequest', ], 'payload' => 'VoiceTemplateRequest', ], 'UpdateVoiceTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'MessageBody' => [ 'shape' => 'MessageBody', ], ], 'required' => [ 'MessageBody', ], 'payload' => 'MessageBody', ], 'VerificationResponse' => [ 'type' => 'structure', 'members' => [ 'Valid' => [ 'shape' => '__boolean', ], ], ], 'VerifyOTPMessageRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'application-id', ], 'VerifyOTPMessageRequestParameters' => [ 'shape' => 'VerifyOTPMessageRequestParameters', ], ], 'required' => [ 'ApplicationId', 'VerifyOTPMessageRequestParameters', ], 'payload' => 'VerifyOTPMessageRequestParameters', ], 'VerifyOTPMessageRequestParameters' => [ 'type' => 'structure', 'members' => [ 'DestinationIdentity' => [ 'shape' => '__string', ], 'Otp' => [ 'shape' => '__string', ], 'ReferenceId' => [ 'shape' => '__string', ], ], 'required' => [ 'ReferenceId', 'Otp', 'DestinationIdentity', ], ], 'VerifyOTPMessageResponse' => [ 'type' => 'structure', 'members' => [ 'VerificationResponse' => [ 'shape' => 'VerificationResponse', ], ], 'required' => [ 'VerificationResponse', ], 'payload' => 'VerificationResponse', ], 'VoiceChannelRequest' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', ], ], ], 'VoiceChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'Enabled' => [ 'shape' => '__boolean', ], 'HasCredential' => [ 'shape' => '__boolean', ], 'Id' => [ 'shape' => '__string', ], 'IsArchived' => [ 'shape' => '__boolean', ], 'LastModifiedBy' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Platform' => [ 'shape' => '__string', ], 'Version' => [ 'shape' => '__integer', ], ], 'required' => [ 'Platform', ], ], 'VoiceMessage' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'LanguageCode' => [ 'shape' => '__string', ], 'OriginationNumber' => [ 'shape' => '__string', ], 'Substitutions' => [ 'shape' => 'MapOfListOf__string', ], 'VoiceId' => [ 'shape' => '__string', ], ], ], 'VoiceTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'LanguageCode' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'VoiceId' => [ 'shape' => '__string', ], ], ], 'VoiceTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Body' => [ 'shape' => '__string', ], 'CreationDate' => [ 'shape' => '__string', ], 'DefaultSubstitutions' => [ 'shape' => '__string', ], 'LanguageCode' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateDescription' => [ 'shape' => '__string', ], 'TemplateName' => [ 'shape' => '__string', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Version' => [ 'shape' => '__string', ], 'VoiceId' => [ 'shape' => '__string', ], ], 'required' => [ 'LastModifiedDate', 'CreationDate', 'TemplateName', 'TemplateType', ], ], 'WaitActivity' => [ 'type' => 'structure', 'members' => [ 'NextActivity' => [ 'shape' => '__string', ], 'WaitTime' => [ 'shape' => 'WaitTime', ], ], ], 'WaitTime' => [ 'type' => 'structure', 'members' => [ 'WaitFor' => [ 'shape' => '__string', ], 'WaitUntil' => [ 'shape' => '__string', ], ], ], 'WriteApplicationSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'CampaignHook' => [ 'shape' => 'CampaignHook', ], 'CloudWatchMetricsEnabled' => [ 'shape' => '__boolean', ], 'Limits' => [ 'shape' => 'CampaignLimits', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], 'JourneyLimits' => [ 'shape' => 'ApplicationSettingsJourneyLimits', ], ], ], 'WriteCampaignRequest' => [ 'type' => 'structure', 'members' => [ 'AdditionalTreatments' => [ 'shape' => 'ListOfWriteTreatmentResource', ], 'CustomDeliveryConfiguration' => [ 'shape' => 'CustomDeliveryConfiguration', ], 'Description' => [ 'shape' => '__string', ], 'HoldoutPercent' => [ 'shape' => '__integer', ], 'Hook' => [ 'shape' => 'CampaignHook', ], 'IsPaused' => [ 'shape' => '__boolean', ], 'Limits' => [ 'shape' => 'CampaignLimits', ], 'MessageConfiguration' => [ 'shape' => 'MessageConfiguration', ], 'Name' => [ 'shape' => '__string', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'SegmentId' => [ 'shape' => '__string', ], 'SegmentVersion' => [ 'shape' => '__integer', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TreatmentDescription' => [ 'shape' => '__string', ], 'TreatmentName' => [ 'shape' => '__string', ], 'Priority' => [ 'shape' => '__integer', ], ], ], 'WriteEventStream' => [ 'type' => 'structure', 'members' => [ 'DestinationStreamArn' => [ 'shape' => '__string', ], 'RoleArn' => [ 'shape' => '__string', ], ], 'required' => [ 'RoleArn', 'DestinationStreamArn', ], ], 'WriteJourneyRequest' => [ 'type' => 'structure', 'members' => [ 'Activities' => [ 'shape' => 'MapOfActivity', ], 'CreationDate' => [ 'shape' => '__string', ], 'LastModifiedDate' => [ 'shape' => '__string', ], 'Limits' => [ 'shape' => 'JourneyLimits', ], 'LocalTime' => [ 'shape' => '__boolean', ], 'Name' => [ 'shape' => '__string', ], 'QuietTime' => [ 'shape' => 'QuietTime', ], 'RefreshFrequency' => [ 'shape' => '__string', ], 'Schedule' => [ 'shape' => 'JourneySchedule', ], 'StartActivity' => [ 'shape' => '__string', ], 'StartCondition' => [ 'shape' => 'StartCondition', ], 'State' => [ 'shape' => 'State', ], 'WaitForQuietTime' => [ 'shape' => '__boolean', ], 'RefreshOnSegmentUpdate' => [ 'shape' => '__boolean', ], 'JourneyChannelSettings' => [ 'shape' => 'JourneyChannelSettings', ], 'SendingSchedule' => [ 'shape' => '__boolean', ], 'OpenHours' => [ 'shape' => 'OpenHours', ], 'ClosedDays' => [ 'shape' => 'ClosedDays', ], 'TimezoneEstimationMethods' => [ 'shape' => 'ListOf__TimezoneEstimationMethodsElement', ], ], 'required' => [ 'Name', ], ], 'WriteSegmentRequest' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'SegmentDimensions', ], 'Name' => [ 'shape' => '__string', ], 'SegmentGroups' => [ 'shape' => 'SegmentGroupList', ], 'tags' => [ 'shape' => 'MapOf__string', 'locationName' => 'tags', ], ], ], 'WriteTreatmentResource' => [ 'type' => 'structure', 'members' => [ 'CustomDeliveryConfiguration' => [ 'shape' => 'CustomDeliveryConfiguration', ], 'MessageConfiguration' => [ 'shape' => 'MessageConfiguration', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'SizePercent' => [ 'shape' => '__integer', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TreatmentDescription' => [ 'shape' => '__string', ], 'TreatmentName' => [ 'shape' => '__string', ], ], 'required' => [ 'SizePercent', ], ], '__EndpointTypesElement' => [ 'type' => 'string', 'enum' => [ 'PUSH', 'GCM', 'APNS', 'APNS_SANDBOX', 'APNS_VOIP', 'APNS_VOIP_SANDBOX', 'ADM', 'SMS', 'VOICE', 'EMAIL', 'BAIDU', 'CUSTOM', 'IN_APP', ], ], '__TimezoneEstimationMethodsElement' => [ 'type' => 'string', 'enum' => [ 'PHONE_NUMBER', 'POSTAL_CODE', ], ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], 'ListOfActivityResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActivityResponse', ], ], 'ListOfApplicationResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationResponse', ], ], 'ListOfCampaignResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignResponse', ], ], 'ListOfEndpointBatchItem' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointBatchItem', ], ], 'ListOfEndpointResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointResponse', ], ], 'ListOfExportJobResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportJobResponse', ], ], 'ListOfImportJobResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportJobResponse', ], ], 'ListOfInAppMessageCampaign' => [ 'type' => 'list', 'member' => [ 'shape' => 'InAppMessageCampaign', ], ], 'ListOfInAppMessageContent' => [ 'type' => 'list', 'member' => [ 'shape' => 'InAppMessageContent', ], ], 'ListOfJourneyResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'JourneyResponse', ], ], 'ListOfJourneyRunResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'JourneyRunResponse', ], ], 'ListOfMultiConditionalBranch' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiConditionalBranch', ], ], 'ListOfRandomSplitEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'RandomSplitEntry', ], ], 'ListOfRecommenderConfigurationResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommenderConfigurationResponse', ], ], 'ListOfResultRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultRow', ], ], 'ListOfResultRowValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultRowValue', ], ], 'ListOfSegmentDimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentDimensions', ], ], 'ListOfSegmentGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentGroup', ], ], 'ListOfSegmentReference' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentReference', ], ], 'ListOfSegmentResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentResponse', ], ], 'ListOfSimpleCondition' => [ 'type' => 'list', 'member' => [ 'shape' => 'SimpleCondition', ], ], 'ListOfTemplateResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateResponse', ], ], 'ListOfTemplateVersionResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateVersionResponse', ], ], 'ListOfTreatmentResource' => [ 'type' => 'list', 'member' => [ 'shape' => 'TreatmentResource', ], ], 'ListOfWriteTreatmentResource' => [ 'type' => 'list', 'member' => [ 'shape' => 'WriteTreatmentResource', ], ], 'ListOf__EndpointTypesElement' => [ 'type' => 'list', 'member' => [ 'shape' => '__EndpointTypesElement', ], ], 'ListOf__TimezoneEstimationMethodsElement' => [ 'type' => 'list', 'member' => [ 'shape' => '__TimezoneEstimationMethodsElement', ], ], 'ListOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', ], 'MapOfActivity' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'Activity', ], ], 'MapOfAddressConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'AddressConfiguration', ], ], 'MapOfAttributeDimension' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'AttributeDimension', ], ], 'MapOfChannelResponse' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'ChannelResponse', ], ], 'MapOfEndpointMessageResult' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'EndpointMessageResult', ], ], 'MapOfEndpointSendConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'EndpointSendConfiguration', ], ], 'MapOfEvent' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'Event', ], ], 'MapOfEventItemResponse' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'EventItemResponse', ], ], 'MapOfEventsBatch' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'EventsBatch', ], ], 'MapOfItemResponse' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'ItemResponse', ], ], 'MapOfMessageResult' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MessageResult', ], ], 'MapOfMetricDimension' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MetricDimension', ], ], 'MapOf__double' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__double', ], ], 'MapOf__integer' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__integer', ], ], 'MapOfListOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'ListOf__string', ], ], 'MapOfMapOfEndpointMessageResult' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MapOfEndpointMessageResult', ], ], 'MapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], '__string' => [ 'type' => 'string', ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'DayOfWeek' => [ 'type' => 'string', 'enum' => [ 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY', ], ], 'ListOfOpenHoursRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpenHoursRule', ], ], 'OpenHoursRule' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => '__string', ], 'EndTime' => [ 'shape' => '__string', ], ], ], 'MapOfListOfOpenHoursRules' => [ 'type' => 'map', 'key' => [ 'shape' => 'DayOfWeek', ], 'value' => [ 'shape' => 'ListOfOpenHoursRules', ], ], 'OpenHours' => [ 'type' => 'structure', 'members' => [ 'EMAIL' => [ 'shape' => 'MapOfListOfOpenHoursRules', ], 'SMS' => [ 'shape' => 'MapOfListOfOpenHoursRules', ], 'PUSH' => [ 'shape' => 'MapOfListOfOpenHoursRules', ], 'VOICE' => [ 'shape' => 'MapOfListOfOpenHoursRules', ], 'CUSTOM' => [ 'shape' => 'MapOfListOfOpenHoursRules', ], ], ], 'ClosedDaysRule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', ], 'StartDateTime' => [ 'shape' => '__string', ], 'EndDateTime' => [ 'shape' => '__string', ], ], ], 'ListOfClosedDaysRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClosedDaysRule', ], ], 'ClosedDays' => [ 'type' => 'structure', 'members' => [ 'EMAIL' => [ 'shape' => 'ListOfClosedDaysRules', ], 'SMS' => [ 'shape' => 'ListOfClosedDaysRules', ], 'PUSH' => [ 'shape' => 'ListOfClosedDaysRules', ], 'VOICE' => [ 'shape' => 'ListOfClosedDaysRules', ], 'CUSTOM' => [ 'shape' => 'ListOfClosedDaysRules', ], ], ], ],];
