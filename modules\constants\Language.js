export default Language = {
   just_created:"Just created",
   accepted_by_admin:"Accepted by admin",
   accepted_by_restaurant:"Accept",
   assigned_to_driver:"Assigned to driver",
   prepared:"Prepared",
   picked_up:"Picked up",
   delivered:"Delivered",
   rejected_by_admin:"Reject",
   rejected_by_restaurant:"Reject",
   updated:"Updated",
   closed:"Closed",
   rejected_by_driver:"Reject",
   accepted_by_driver:"Accept",
   prepared_order:"Order prepared",
   prepared_order_info:"Make this action only when order is ready.",
   VendorCreated:"Vendor is created",
   today:"Today",
   this_week:"This week",
   this_month:"This month",
   previous_month:"Previous month",
   by_doing:"by doing",
   hi:"Hi",
   today_you_have_made:"Today you have made",
   earnings:"Earnings",
   call:"Call",  
   directions:"Directions",
   deliver_order:"Order delivered",
   deliver_order_info:"Make this action only when you have delivered the order.",
   pickup_order:"Pickup order",
   pickup_order_info:"Make this action only when you have picked up the order.",
   order_is_delivered:"Make this action only when you have delivered up the order.",
   order_is_paid:"Order is already payed by the client.",
   order_need_to_be_paid:"Order is not paid yet. Client needs to give you",
   order_is_prepared:"Prepared. You may head up to to venue to pick up.",
   you_will_earn:"You will earn:",
   no_items:"There are no items to show.",
   order_is_delivered:"You have delivered this order.",
   order_is_in_making:"At the moment order is in preparing. No action for you at the moment. Based on desired delivery time, you may head up to the venue.",
   accept_order:"Accept order",
   accept_order_info:"This action will accept the order.",
   reject_order:"Reject order",
   reject_order_info:"This action will reject the order",
   actions:"Actions",
   driverWorkingStatus:"I'm available for receiving orders",
   DriverCreated:"Thanks for signing up. Driver account is created, but it requires admin approval. You will be contacted on yhe phone number you have provided",
   Restaurant: "Restaurants",
   Client: "Client",
   Area: "Area",
   searchRestaurants: "What are you looking for?",
   searchInRestaurant: "What are you looking for eating?",
   addToCart:"Add to cart",
   addToOrder:"Add to order",
   quatity:"Quantity",
   pay_with_paypal:"PayPal",
   pay_with_paystack:"PayStack",
   pay_with_mollie:"Mollie",
   item:"Item",
   items:"Items",
   itemAddedInCart:"Item is added in cart",
   itemsAddedInCart:"Items are added in cart",
   order:"Order",
   cartSubtotal:"Cart Subtotal",
   subtotal:"Subtotal",
   proceedToCheckout:'Proceed To Checkout',
   noOrders:'There are no orders',
   orders:"Orders",
   noItemsInCart:'There are no items in cart',
   profile:'Profile',
   home:"Home",
   myOrders:"My Orders",
   addresses:"Addresses",
   login:"Login",
   logout:"Logout",
   forgotPassword:"Forgot Password",
   register:"Register",
   userIsNowLoggedIn:"User is now logged in",
   proceedToLoginFirst:"Login or register to continue",
   selectAddress:"Select Address",
   noAddressesAddNew:"No addresses found!",
   addAddress:"Add Address",
   enterYourAddress:"Enter your address",
   continue:"Continue",
   addressDetails:"Address Details",
   addressNumber:"Address Number",
   intercom:"Intercom",
   appartment:"Appartment number",
   entry:"Entry number",
   floor:"Floor",
   addressForDelivery:"Address for delivery",
   checkout:"Checkout",
   deliveryOrPickup:"Deliver / Pickup",
   deliveryTime:"Delivery Time",
   pickupTime:"Pickup Time",
   restaurantInformations:"Restaurant Informations",
   delivery:"Delivery",
   delivered:"Delivered",
   pickup:"Pickup",
   addNewAddress:"Add new address",
   summary:"Summary",
   deliveryMethod:"Delivery Method",
   total:"Total",
   paymentMethod:"Payment method",
   pay_with_card:"Pay with card",
   cod:"Cash on deliver",
   cop:"Cash on pickup",
   place_order:"Place order",
   orderSuccesfull:"Order has been received succefully",
   trackOrder:"You can track and see the status of the order in the orders tab.",
   ok:"Confirm",
   cancel:"Cancel",
   errorOnOrder:"Error on placing the order.",
   status:"Status",
   created:"Created",
   orderDetails:"Order details",
   restaurant:"Restaurant",
   deliveryAddress:"Delivery address",
   orderTracking:"Order tracking",
   orderNumber:"Order number",
   driver:"Delivery driver",
   driverName:"Driver name",
   driverPhone:"Driver phone",
   orderOptions:"Order options",
   all:"All",
   notifications:"Notifications",
   noNotifications:"No notifications",
   restaurantClosed:"Looks like restaurant is closed at this time. Order can't be made.",
   you_can_order_items_from_different_restaurants:"You can't order items from different restaurants in single order.",
   options:"Options",
   extras:"Extras",
   notInRange:"Not in range",
   selectYourCity:"Select your city",
   drop_off:"Destination",
   distance:"Distance",
   at_location:"At location",
   inform_at_location:"Inform client that you are at the pickup location",
   message_when_approved:"Hi, I headed to your pickup location",
   message_when_approved_tracking:"Here, You can track my current location here ->",
   message_when_rejected:"Hi, unfortunately I will not be able to do the requested ride.",
   message_at_location:"I'm on the marked pickup location",
   client_picked_up:"Client picked up",
   client_picked_up_info:"Make this action when client is in the vehicle",
   change_price:"Change ride cost",
   change_price_info:"Enter new cost for the ride",
   deliver_client:"Ride done",
   deliver_client_info:"Make this action when you have finished the ride and you make sure that the ride price is the one displayed. If not, you can change it",
   close_order:"Order closed",
   close_order_info:"Make this action only when order is fully realized and paid.",
   table:"Table",
   deleteAccount:"Delete account",
   close_account:"Close account",
   close_account_info:"Please note. After deleting your account you will not be able to login and your account will be closed",
   orders:"Orders",
   earnings:"Earnings",
   notifications:"Notifications",
   chats:"Chats",
   chat_details:"Chat details",
}