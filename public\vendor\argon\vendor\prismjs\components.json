{"core": {"meta": {"path": "components/prism-core.js", "option": "mandatory"}, "core": "Core"}, "themes": {"meta": {"path": "themes/{id}.css", "link": "index.html?theme={id}", "exclusive": true}, "prism": {"title": "<PERSON><PERSON><PERSON>", "option": "default"}, "prism-dark": "Dark", "prism-funky": "Funky", "prism-okaidia": {"title": "Okaidia", "owner": "ocodia"}, "prism-twilight": {"title": "Twilight", "owner": "<PERSON><PERSON><PERSON>"}, "prism-coy": {"title": "Coy", "owner": "t<PERSON>or"}, "prism-solarizedlight": {"title": "Solarized Light", "owner": "hectormatos2011 "}, "prism-tomorrow": {"title": "Tomorrow Night", "owner": "<PERSON><PERSON>"}}, "languages": {"meta": {"path": "components/prism-{id}", "noCSS": true, "examplesPath": "examples/prism-{id}", "addCheckAll": true}, "markup": {"title": "<PERSON><PERSON>", "alias": ["html", "xml", "svg", "mathml"], "aliasTitles": {"html": "HTML", "xml": "XML", "svg": "SVG", "mathml": "MathML"}, "option": "default"}, "css": {"title": "CSS", "option": "default", "peerDependencies": "markup"}, "clike": {"title": "C-like", "option": "default", "overrideExampleHeader": true}, "javascript": {"title": "JavaScript", "require": "clike", "peerDependencies": "markup", "alias": "js", "option": "default"}, "abap": {"title": "ABAP", "owner": "<PERSON><PERSON><PERSON>"}, "actionscript": {"title": "ActionScript", "require": "javascript", "peerDependencies": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "ada": {"title": "Ada", "owner": "Lucretia"}, "apacheconf": {"title": "Apache Configuration", "owner": "GuiTeK"}, "apl": {"title": "APL", "owner": "ngn"}, "applescript": {"title": "AppleScript", "owner": "<PERSON><PERSON><PERSON>"}, "arduino": {"title": "<PERSON><PERSON><PERSON><PERSON>", "require": "cpp", "owner": "eisbehr-"}, "arff": {"title": "ARFF", "owner": "<PERSON><PERSON><PERSON>"}, "asciidoc": {"title": "AsciiDoc", "owner": "<PERSON><PERSON><PERSON>"}, "asm6502": {"title": "6502 Assembly", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "aspnet": {"title": "ASP.NET (C#)", "require": ["markup", "csharp"], "owner": "nauzilus"}, "autohotkey": {"title": "AutoHotkey", "owner": "aviaryan"}, "autoit": {"title": "AutoIt", "owner": "<PERSON><PERSON><PERSON>"}, "bash": {"title": "<PERSON><PERSON>", "owner": "zeitgeist87"}, "basic": {"title": "BASIC", "owner": "<PERSON><PERSON><PERSON>"}, "batch": {"title": "<PERSON><PERSON>", "alias": "shell", "owner": "<PERSON><PERSON><PERSON>"}, "bison": {"title": "<PERSON>ison", "require": "c", "owner": "<PERSON><PERSON><PERSON>"}, "brainfuck": {"title": "Brainfuck", "owner": "<PERSON><PERSON><PERSON>"}, "bro": {"title": "<PERSON><PERSON>", "owner": "wayward710"}, "c": {"title": "C", "require": "clike", "owner": "zeitgeist87"}, "csharp": {"title": "C#", "require": "clike", "alias": "dotnet", "owner": "m<PERSON><PERSON><PERSON>"}, "cpp": {"title": "C++", "require": "c", "owner": "zeitgeist87"}, "coffeescript": {"title": "CoffeeScript", "require": "javascript", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "clojure": {"title": "Clojure", "owner": "troglotit"}, "crystal": {"title": "Crystal", "require": "ruby", "owner": "MakeNowJust"}, "csp": {"title": "Content-Security-Policy", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "css-extras": {"title": "CSS Extras", "require": "css", "owner": "milesj"}, "d": {"title": "D", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "dart": {"title": "Dart", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "diff": {"title": "Diff", "owner": "uranusjr"}, "django": {"title": "Django/Jinja2", "require": "markup", "peerDependencies": ["css", "javascript"], "alias": "jinja2", "owner": "romanvm"}, "docker": {"title": "<PERSON>er", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "eiffel": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "elixir": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "elm": {"title": "Elm", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "erb": {"title": "ERB", "require": ["ruby", "markup-templating"], "owner": "<PERSON><PERSON><PERSON>"}, "erlang": {"title": "Erl<PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "fsharp": {"title": "F#", "require": "clike", "owner": "simonreynolds7"}, "flow": {"title": "Flow", "require": "javascript", "owner": "<PERSON><PERSON><PERSON>"}, "fortran": {"title": "Fortran", "owner": "<PERSON><PERSON><PERSON>"}, "gedcom": {"title": "GEDCOM", "owner": "<PERSON><PERSON><PERSON>"}, "gherkin": {"title": "<PERSON><PERSON><PERSON>", "owner": "hason"}, "git": {"title": "Git", "owner": "lgiraudel"}, "glsl": {"title": "GLSL", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "go": {"title": "Go", "require": "clike", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "graphql": {"title": "GraphQL", "owner": "<PERSON><PERSON><PERSON>"}, "groovy": {"title": "Groovy", "require": "clike", "owner": "robfletcher"}, "haml": {"title": "<PERSON><PERSON>", "require": "ruby", "peerDependencies": ["css", "coffeescript", "erb", "javascript", "less", "markdown", "ruby", "scss", "textile"], "owner": "<PERSON><PERSON><PERSON>"}, "handlebars": {"title": "Handlebars", "require": "markup-templating", "owner": "<PERSON><PERSON><PERSON>"}, "haskell": {"title": "<PERSON><PERSON>", "owner": "bholst"}, "haxe": {"title": "Haxe", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "http": {"title": "HTTP", "peerDependencies": ["javascript", "markup"], "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hpkp": {"title": "HTTP Public-Key-Pins", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "hsts": {"title": "HTTP Strict-Transport-Security", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "ichigojam": {"title": "IchigoJam", "owner": "BlueCocoa"}, "icon": {"title": "Icon", "owner": "<PERSON><PERSON><PERSON>"}, "inform7": {"title": "Inform 7", "owner": "<PERSON><PERSON><PERSON>"}, "ini": {"title": "Ini", "owner": "aviaryan"}, "io": {"title": "Io", "owner": "AlesTsurko"}, "j": {"title": "J", "owner": "<PERSON><PERSON><PERSON>"}, "java": {"title": "Java", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "jolie": {"title": "<PERSON><PERSON>", "require": "clike", "owner": "thesave"}, "json": {"title": "JSON", "owner": "CupOfTea696"}, "julia": {"title": "<PERSON>", "owner": "cdagnino"}, "keyman": {"title": "Keyman", "owner": "mc<PERSON><PERSON><PERSON>"}, "kotlin": {"title": "<PERSON><PERSON><PERSON>", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "latex": {"title": "LaTeX", "owner": "j<PERSON><PERSON><PERSON>"}, "less": {"title": "Less", "require": "css", "owner": "<PERSON><PERSON><PERSON>"}, "liquid": {"title": "Liquid", "owner": "cinhtau"}, "lisp": {"title": "Lisp", "owner": "JuanCaicedo", "alias": ["emacs", "elisp", "emacs-lisp"]}, "livescript": {"title": "LiveScript", "owner": "<PERSON><PERSON><PERSON>"}, "lolcode": {"title": "LOLCODE", "owner": "<PERSON><PERSON><PERSON>"}, "lua": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "makefile": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "markdown": {"title": "<PERSON><PERSON>", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "markup-templating": {"title": "Markup templating", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "matlab": {"title": "MATLAB", "owner": "<PERSON><PERSON><PERSON>"}, "mel": {"title": "MEL", "owner": "<PERSON><PERSON><PERSON>"}, "mizar": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "monkey": {"title": "Monkey", "owner": "<PERSON><PERSON><PERSON>"}, "n4js": {"title": "N4JS", "require": "javascript", "owner": "bsmith-n4"}, "nasm": {"title": "NASM", "owner": "rbmj"}, "nginx": {"title": "nginx", "owner": "westonganger", "require": "clike"}, "nim": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "nix": {"title": "<PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "nsis": {"title": "NSIS", "owner": "<PERSON>berg"}, "objectivec": {"title": "Objective-C", "require": "c", "owner": "uranusjr"}, "ocaml": {"title": "OCaml", "owner": "<PERSON><PERSON><PERSON>"}, "opencl": {"title": "OpenCL", "require": "cpp", "peerDependencies": ["c", "cpp"], "overrideExampleHeader": true, "owner": "Milania1"}, "oz": {"title": "Oz", "owner": "<PERSON><PERSON><PERSON>"}, "parigp": {"title": "PARI/GP", "owner": "<PERSON><PERSON><PERSON>"}, "parser": {"title": "<PERSON><PERSON><PERSON>", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "pascal": {"title": "<PERSON>", "alias": "objectpascal", "aliasTitles": {"objectpascal": "Object Pascal"}, "owner": "<PERSON><PERSON><PERSON>"}, "perl": {"title": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "php": {"title": "PHP", "require": ["clike", "markup-templating"], "owner": "milesj"}, "php-extras": {"title": "PHP Extras", "require": "php", "owner": "milesj"}, "plsql": {"title": "PL/SQL", "require": "sql", "owner": "<PERSON><PERSON><PERSON>"}, "powershell": {"title": "PowerShell", "owner": "nauzilus"}, "processing": {"title": "Processing", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "prolog": {"title": "Prolog", "owner": "<PERSON><PERSON><PERSON>"}, "properties": {"title": ".properties", "owner": "<PERSON><PERSON><PERSON>"}, "protobuf": {"title": "Protocol Buffers", "require": "clike", "owner": "just-boris"}, "pug": {"title": "<PERSON><PERSON>", "require": "javascript", "peerDependencies": ["coffeescript", "ejs", "handlebars", "hogan", "less", "livescript", "markdown", "mustache", "plates", "scss", "stylus", "swig"], "owner": "<PERSON><PERSON><PERSON>"}, "puppet": {"title": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON>"}, "pure": {"title": "Pure", "peerDependencies": ["c", "cpp", "fortran", "ats", "dsp"], "owner": "<PERSON><PERSON><PERSON>"}, "python": {"title": "Python", "owner": "multipetros"}, "q": {"title": "Q (kdb+ database)", "owner": "<PERSON><PERSON><PERSON>"}, "qore": {"title": "<PERSON><PERSON>", "require": "clike", "owner": "temnroegg"}, "r": {"title": "R", "owner": "<PERSON><PERSON><PERSON>"}, "jsx": {"title": "React JSX", "require": ["markup", "javascript"], "owner": "vkbansal"}, "tsx": {"title": "React TSX", "require": ["jsx", "typescript"]}, "renpy": {"title": "Ren'py", "owner": "HyuchiaDiego"}, "reason": {"title": "Reason", "require": "clike", "owner": "<PERSON><PERSON><PERSON>"}, "rest": {"title": "reST (reStructuredText)", "owner": "<PERSON><PERSON><PERSON>"}, "rip": {"title": "<PERSON><PERSON>", "owner": "r<PERSON><PERSON><PERSON>"}, "roboconf": {"title": "Roboconf", "owner": "<PERSON><PERSON><PERSON>"}, "ruby": {"title": "<PERSON>", "require": "clike", "owner": "samflores"}, "rust": {"title": "Rust", "owner": "<PERSON><PERSON><PERSON>"}, "sas": {"title": "SAS", "owner": "<PERSON><PERSON><PERSON>"}, "sass": {"title": "Sass (Sass)", "require": "css", "owner": "<PERSON><PERSON><PERSON>"}, "scss": {"title": "Sass (Scss)", "require": "css", "owner": "MoOx"}, "scala": {"title": "Scala", "require": "java", "owner": "jozic"}, "scheme": {"title": "Scheme", "owner": "bacchus123"}, "smalltalk": {"title": "Smalltalk", "owner": "<PERSON><PERSON><PERSON>"}, "smarty": {"title": "Smarty", "require": "markup-templating", "owner": "<PERSON><PERSON><PERSON>"}, "sql": {"title": "SQL", "owner": "multipetros"}, "soy": {"title": "Soy (Closure Template)", "require": "markup-templating", "owner": "<PERSON><PERSON><PERSON>"}, "stylus": {"title": "<PERSON><PERSON><PERSON>", "owner": "vkbansal"}, "swift": {"title": "Swift", "require": "clike", "owner": "chris<PERSON><PERSON>"}, "tap": {"title": "TAP", "owner": "isaacs", "require": "yaml"}, "tcl": {"title": "Tcl", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "textile": {"title": "Textile", "require": "markup", "peerDependencies": "css", "owner": "<PERSON><PERSON><PERSON>"}, "tt2": {"title": "Template Toolkit 2", "require": ["clike", "markup-templating"], "owner": "gflohr"}, "twig": {"title": "Twig", "require": "markup", "owner": "brandonkelly"}, "typescript": {"title": "TypeScript", "require": "javascript", "alias": "ts", "owner": "vkbansal"}, "vbnet": {"title": "VB.Net", "require": "basic", "owner": "Bigsby"}, "velocity": {"title": "Velocity", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "verilog": {"title": "Verilog", "owner": "a-rey"}, "vhdl": {"title": "VHDL", "owner": "a-rey"}, "vim": {"title": "vim", "owner": "westonganger"}, "visual-basic": {"title": "Visual Basic", "owner": "<PERSON><PERSON><PERSON>", "alias": "vb"}, "wasm": {"title": "WebAssembly", "owner": "<PERSON><PERSON><PERSON>"}, "wiki": {"title": "Wiki markup", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "xeora": {"title": "Xeora", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "xojo": {"title": "<PERSON><PERSON><PERSON> (REALbasic)", "owner": "<PERSON><PERSON><PERSON>"}, "xquery": {"title": "<PERSON><PERSON><PERSON><PERSON>", "require": "markup", "owner": "<PERSON><PERSON><PERSON>"}, "yaml": {"title": "YAML", "owner": "hason"}}, "plugins": {"meta": {"path": "plugins/{id}/prism-{id}", "link": "plugins/{id}/"}, "line-highlight": "Line Highlight", "line-numbers": {"title": "Line Numbers", "owner": "kuba-kubula"}, "show-invisibles": "Show Invisibles", "autolinker": "Autolinker", "wpd": "WebPlatform Docs", "custom-class": {"title": "Custom Class", "owner": "dvkndn", "noCSS": true}, "file-highlight": {"title": "File Highlight", "noCSS": true}, "show-language": {"title": "Show Language", "owner": "nauzilus", "noCSS": true, "require": "toolbar"}, "jsonp-highlight": {"title": "JSONP Highlight", "noCSS": true, "owner": "nauzilus"}, "highlight-keywords": {"title": "Highlight Keywords", "owner": "vkbansal", "noCSS": true}, "remove-initial-line-feed": {"title": "Remove initial line feed", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "previewers": {"title": "Previewers", "owner": "<PERSON><PERSON><PERSON>"}, "autoloader": {"title": "Autoloader", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "keep-markup": {"title": "Keep Markup", "owner": "<PERSON><PERSON><PERSON>", "after": "normalize-whitespace", "noCSS": true}, "command-line": {"title": "Command Line", "owner": "chriswells0"}, "unescaped-markup": "Unescaped Markup", "normalize-whitespace": {"title": "Normalize Whitespace", "owner": "zeitgeist87", "after": "unescaped-markup", "noCSS": true}, "data-uri-highlight": {"title": "Data-URI Highlight", "owner": "<PERSON><PERSON><PERSON>", "noCSS": true}, "toolbar": {"title": "<PERSON><PERSON><PERSON>", "owner": "mAAdha<PERSON>ah"}, "copy-to-clipboard": {"title": "<PERSON><PERSON> to Clipboard Button", "owner": "mAAdha<PERSON>ah", "require": "toolbar", "noCSS": true}}}