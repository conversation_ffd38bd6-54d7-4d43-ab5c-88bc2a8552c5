<?php
// This file was auto-generated from sdk-root/src/data/imagebuilder/2019-12-02/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-12-02', 'endpointPrefix' => 'imagebuilder', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'imagebuilder', 'serviceFullName' => 'EC2 Image Builder', 'serviceId' => 'imagebuilder', 'signatureVersion' => 'v4', 'signingName' => 'imagebuilder', 'uid' => 'imagebuilder-2019-12-02', ], 'operations' => [ 'CancelImageCreation' => [ 'name' => 'CancelImageCreation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CancelImageCreation', ], 'input' => [ 'shape' => 'CancelImageCreationRequest', ], 'output' => [ 'shape' => 'CancelImageCreationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CancelLifecycleExecution' => [ 'name' => 'CancelLifecycleExecution', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CancelLifecycleExecution', ], 'input' => [ 'shape' => 'CancelLifecycleExecutionRequest', ], 'output' => [ 'shape' => 'CancelLifecycleExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CreateComponent' => [ 'name' => 'CreateComponent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateComponent', ], 'input' => [ 'shape' => 'CreateComponentRequest', ], 'output' => [ 'shape' => 'CreateComponentResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'InvalidVersionNumberException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateContainerRecipe' => [ 'name' => 'CreateContainerRecipe', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateContainerRecipe', ], 'input' => [ 'shape' => 'CreateContainerRecipeRequest', ], 'output' => [ 'shape' => 'CreateContainerRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'InvalidVersionNumberException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateDistributionConfiguration' => [ 'name' => 'CreateDistributionConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateDistributionConfiguration', ], 'input' => [ 'shape' => 'CreateDistributionConfigurationRequest', ], 'output' => [ 'shape' => 'CreateDistributionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateImage' => [ 'name' => 'CreateImage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateImage', ], 'input' => [ 'shape' => 'CreateImageRequest', ], 'output' => [ 'shape' => 'CreateImageResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateImagePipeline' => [ 'name' => 'CreateImagePipeline', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateImagePipeline', ], 'input' => [ 'shape' => 'CreateImagePipelineRequest', ], 'output' => [ 'shape' => 'CreateImagePipelineResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateImageRecipe' => [ 'name' => 'CreateImageRecipe', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateImageRecipe', ], 'input' => [ 'shape' => 'CreateImageRecipeRequest', ], 'output' => [ 'shape' => 'CreateImageRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'InvalidVersionNumberException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateInfrastructureConfiguration' => [ 'name' => 'CreateInfrastructureConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateInfrastructureConfiguration', ], 'input' => [ 'shape' => 'CreateInfrastructureConfigurationRequest', ], 'output' => [ 'shape' => 'CreateInfrastructureConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateLifecyclePolicy' => [ 'name' => 'CreateLifecyclePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateLifecyclePolicy', ], 'input' => [ 'shape' => 'CreateLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'CreateLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/CreateWorkflow', ], 'input' => [ 'shape' => 'CreateWorkflowRequest', ], 'output' => [ 'shape' => 'CreateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'InvalidVersionNumberException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteComponent' => [ 'name' => 'DeleteComponent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteComponent', ], 'input' => [ 'shape' => 'DeleteComponentRequest', ], 'output' => [ 'shape' => 'DeleteComponentResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteContainerRecipe' => [ 'name' => 'DeleteContainerRecipe', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteContainerRecipe', ], 'input' => [ 'shape' => 'DeleteContainerRecipeRequest', ], 'output' => [ 'shape' => 'DeleteContainerRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteDistributionConfiguration' => [ 'name' => 'DeleteDistributionConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteDistributionConfiguration', ], 'input' => [ 'shape' => 'DeleteDistributionConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteDistributionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteImage' => [ 'name' => 'DeleteImage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteImage', ], 'input' => [ 'shape' => 'DeleteImageRequest', ], 'output' => [ 'shape' => 'DeleteImageResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteImagePipeline' => [ 'name' => 'DeleteImagePipeline', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteImagePipeline', ], 'input' => [ 'shape' => 'DeleteImagePipelineRequest', ], 'output' => [ 'shape' => 'DeleteImagePipelineResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteImageRecipe' => [ 'name' => 'DeleteImageRecipe', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteImageRecipe', ], 'input' => [ 'shape' => 'DeleteImageRecipeRequest', ], 'output' => [ 'shape' => 'DeleteImageRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteInfrastructureConfiguration' => [ 'name' => 'DeleteInfrastructureConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteInfrastructureConfiguration', ], 'input' => [ 'shape' => 'DeleteInfrastructureConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteInfrastructureConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteLifecyclePolicy' => [ 'name' => 'DeleteLifecyclePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteLifecyclePolicy', ], 'input' => [ 'shape' => 'DeleteLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'DeleteLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/DeleteWorkflow', ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceDependencyException', ], ], ], 'GetComponent' => [ 'name' => 'GetComponent', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetComponent', ], 'input' => [ 'shape' => 'GetComponentRequest', ], 'output' => [ 'shape' => 'GetComponentResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetComponentPolicy' => [ 'name' => 'GetComponentPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetComponentPolicy', ], 'input' => [ 'shape' => 'GetComponentPolicyRequest', ], 'output' => [ 'shape' => 'GetComponentPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetContainerRecipe' => [ 'name' => 'GetContainerRecipe', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetContainerRecipe', ], 'input' => [ 'shape' => 'GetContainerRecipeRequest', ], 'output' => [ 'shape' => 'GetContainerRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetContainerRecipePolicy' => [ 'name' => 'GetContainerRecipePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetContainerRecipePolicy', ], 'input' => [ 'shape' => 'GetContainerRecipePolicyRequest', ], 'output' => [ 'shape' => 'GetContainerRecipePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetDistributionConfiguration' => [ 'name' => 'GetDistributionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetDistributionConfiguration', ], 'input' => [ 'shape' => 'GetDistributionConfigurationRequest', ], 'output' => [ 'shape' => 'GetDistributionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetImage' => [ 'name' => 'GetImage', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetImage', ], 'input' => [ 'shape' => 'GetImageRequest', ], 'output' => [ 'shape' => 'GetImageResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetImagePipeline' => [ 'name' => 'GetImagePipeline', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetImagePipeline', ], 'input' => [ 'shape' => 'GetImagePipelineRequest', ], 'output' => [ 'shape' => 'GetImagePipelineResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetImagePolicy' => [ 'name' => 'GetImagePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetImagePolicy', ], 'input' => [ 'shape' => 'GetImagePolicyRequest', ], 'output' => [ 'shape' => 'GetImagePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetImageRecipe' => [ 'name' => 'GetImageRecipe', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetImageRecipe', ], 'input' => [ 'shape' => 'GetImageRecipeRequest', ], 'output' => [ 'shape' => 'GetImageRecipeResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetImageRecipePolicy' => [ 'name' => 'GetImageRecipePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetImageRecipePolicy', ], 'input' => [ 'shape' => 'GetImageRecipePolicyRequest', ], 'output' => [ 'shape' => 'GetImageRecipePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetInfrastructureConfiguration' => [ 'name' => 'GetInfrastructureConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetInfrastructureConfiguration', ], 'input' => [ 'shape' => 'GetInfrastructureConfigurationRequest', ], 'output' => [ 'shape' => 'GetInfrastructureConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetLifecycleExecution' => [ 'name' => 'GetLifecycleExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetLifecycleExecution', ], 'input' => [ 'shape' => 'GetLifecycleExecutionRequest', ], 'output' => [ 'shape' => 'GetLifecycleExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetLifecyclePolicy' => [ 'name' => 'GetLifecyclePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetLifecyclePolicy', ], 'input' => [ 'shape' => 'GetLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'GetLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetWorkflow', ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetWorkflowExecution' => [ 'name' => 'GetWorkflowExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetWorkflowExecution', ], 'input' => [ 'shape' => 'GetWorkflowExecutionRequest', ], 'output' => [ 'shape' => 'GetWorkflowExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'GetWorkflowStepExecution' => [ 'name' => 'GetWorkflowStepExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/GetWorkflowStepExecution', ], 'input' => [ 'shape' => 'GetWorkflowStepExecutionRequest', ], 'output' => [ 'shape' => 'GetWorkflowStepExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ImportComponent' => [ 'name' => 'ImportComponent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/ImportComponent', ], 'input' => [ 'shape' => 'ImportComponentRequest', ], 'output' => [ 'shape' => 'ImportComponentResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'InvalidVersionNumberException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ImportVmImage' => [ 'name' => 'ImportVmImage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/ImportVmImage', ], 'input' => [ 'shape' => 'ImportVmImageRequest', ], 'output' => [ 'shape' => 'ImportVmImageResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListComponentBuildVersions' => [ 'name' => 'ListComponentBuildVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListComponentBuildVersions', ], 'input' => [ 'shape' => 'ListComponentBuildVersionsRequest', ], 'output' => [ 'shape' => 'ListComponentBuildVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListComponents' => [ 'name' => 'ListComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListComponents', ], 'input' => [ 'shape' => 'ListComponentsRequest', ], 'output' => [ 'shape' => 'ListComponentsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListContainerRecipes' => [ 'name' => 'ListContainerRecipes', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListContainerRecipes', ], 'input' => [ 'shape' => 'ListContainerRecipesRequest', ], 'output' => [ 'shape' => 'ListContainerRecipesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListDistributionConfigurations' => [ 'name' => 'ListDistributionConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListDistributionConfigurations', ], 'input' => [ 'shape' => 'ListDistributionConfigurationsRequest', ], 'output' => [ 'shape' => 'ListDistributionConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImageBuildVersions' => [ 'name' => 'ListImageBuildVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImageBuildVersions', ], 'input' => [ 'shape' => 'ListImageBuildVersionsRequest', ], 'output' => [ 'shape' => 'ListImageBuildVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImagePackages' => [ 'name' => 'ListImagePackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImagePackages', ], 'input' => [ 'shape' => 'ListImagePackagesRequest', ], 'output' => [ 'shape' => 'ListImagePackagesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImagePipelineImages' => [ 'name' => 'ListImagePipelineImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImagePipelineImages', ], 'input' => [ 'shape' => 'ListImagePipelineImagesRequest', ], 'output' => [ 'shape' => 'ListImagePipelineImagesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImagePipelines' => [ 'name' => 'ListImagePipelines', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImagePipelines', ], 'input' => [ 'shape' => 'ListImagePipelinesRequest', ], 'output' => [ 'shape' => 'ListImagePipelinesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImageRecipes' => [ 'name' => 'ListImageRecipes', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImageRecipes', ], 'input' => [ 'shape' => 'ListImageRecipesRequest', ], 'output' => [ 'shape' => 'ListImageRecipesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImageScanFindingAggregations' => [ 'name' => 'ListImageScanFindingAggregations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImageScanFindingAggregations', ], 'input' => [ 'shape' => 'ListImageScanFindingAggregationsRequest', ], 'output' => [ 'shape' => 'ListImageScanFindingAggregationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImageScanFindings' => [ 'name' => 'ListImageScanFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImageScanFindings', ], 'input' => [ 'shape' => 'ListImageScanFindingsRequest', ], 'output' => [ 'shape' => 'ListImageScanFindingsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListImages' => [ 'name' => 'ListImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListImages', ], 'input' => [ 'shape' => 'ListImagesRequest', ], 'output' => [ 'shape' => 'ListImagesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListInfrastructureConfigurations' => [ 'name' => 'ListInfrastructureConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListInfrastructureConfigurations', ], 'input' => [ 'shape' => 'ListInfrastructureConfigurationsRequest', ], 'output' => [ 'shape' => 'ListInfrastructureConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListLifecycleExecutionResources' => [ 'name' => 'ListLifecycleExecutionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLifecycleExecutionResources', ], 'input' => [ 'shape' => 'ListLifecycleExecutionResourcesRequest', ], 'output' => [ 'shape' => 'ListLifecycleExecutionResourcesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListLifecycleExecutions' => [ 'name' => 'ListLifecycleExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLifecycleExecutions', ], 'input' => [ 'shape' => 'ListLifecycleExecutionsRequest', ], 'output' => [ 'shape' => 'ListLifecycleExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListLifecyclePolicies' => [ 'name' => 'ListLifecyclePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLifecyclePolicies', ], 'input' => [ 'shape' => 'ListLifecyclePoliciesRequest', ], 'output' => [ 'shape' => 'ListLifecyclePoliciesResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListWaitingWorkflowSteps' => [ 'name' => 'ListWaitingWorkflowSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListWaitingWorkflowSteps', ], 'input' => [ 'shape' => 'ListWaitingWorkflowStepsRequest', ], 'output' => [ 'shape' => 'ListWaitingWorkflowStepsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListWorkflowBuildVersions' => [ 'name' => 'ListWorkflowBuildVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListWorkflowBuildVersions', ], 'input' => [ 'shape' => 'ListWorkflowBuildVersionsRequest', ], 'output' => [ 'shape' => 'ListWorkflowBuildVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListWorkflowExecutions' => [ 'name' => 'ListWorkflowExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListWorkflowExecutions', ], 'input' => [ 'shape' => 'ListWorkflowExecutionsRequest', ], 'output' => [ 'shape' => 'ListWorkflowExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListWorkflowStepExecutions' => [ 'name' => 'ListWorkflowStepExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListWorkflowStepExecutions', ], 'input' => [ 'shape' => 'ListWorkflowStepExecutionsRequest', ], 'output' => [ 'shape' => 'ListWorkflowStepExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListWorkflows', ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'PutComponentPolicy' => [ 'name' => 'PutComponentPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/PutComponentPolicy', ], 'input' => [ 'shape' => 'PutComponentPolicyRequest', ], 'output' => [ 'shape' => 'PutComponentPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'PutContainerRecipePolicy' => [ 'name' => 'PutContainerRecipePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/PutContainerRecipePolicy', ], 'input' => [ 'shape' => 'PutContainerRecipePolicyRequest', ], 'output' => [ 'shape' => 'PutContainerRecipePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'PutImagePolicy' => [ 'name' => 'PutImagePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/PutImagePolicy', ], 'input' => [ 'shape' => 'PutImagePolicyRequest', ], 'output' => [ 'shape' => 'PutImagePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'PutImageRecipePolicy' => [ 'name' => 'PutImageRecipePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/PutImageRecipePolicy', ], 'input' => [ 'shape' => 'PutImageRecipePolicyRequest', ], 'output' => [ 'shape' => 'PutImageRecipePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], ], ], 'SendWorkflowStepAction' => [ 'name' => 'SendWorkflowStepAction', 'http' => [ 'method' => 'PUT', 'requestUri' => '/SendWorkflowStepAction', ], 'input' => [ 'shape' => 'SendWorkflowStepActionRequest', ], 'output' => [ 'shape' => 'SendWorkflowStepActionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'StartImagePipelineExecution' => [ 'name' => 'StartImagePipelineExecution', 'http' => [ 'method' => 'PUT', 'requestUri' => '/StartImagePipelineExecution', ], 'input' => [ 'shape' => 'StartImagePipelineExecutionRequest', ], 'output' => [ 'shape' => 'StartImagePipelineExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'StartResourceStateUpdate' => [ 'name' => 'StartResourceStateUpdate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/StartResourceStateUpdate', ], 'input' => [ 'shape' => 'StartResourceStateUpdateRequest', ], 'output' => [ 'shape' => 'StartResourceStateUpdateResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateDistributionConfiguration' => [ 'name' => 'UpdateDistributionConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/UpdateDistributionConfiguration', ], 'input' => [ 'shape' => 'UpdateDistributionConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateDistributionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'UpdateImagePipeline' => [ 'name' => 'UpdateImagePipeline', 'http' => [ 'method' => 'PUT', 'requestUri' => '/UpdateImagePipeline', ], 'input' => [ 'shape' => 'UpdateImagePipelineRequest', ], 'output' => [ 'shape' => 'UpdateImagePipelineResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateInfrastructureConfiguration' => [ 'name' => 'UpdateInfrastructureConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/UpdateInfrastructureConfiguration', ], 'input' => [ 'shape' => 'UpdateInfrastructureConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateInfrastructureConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateLifecyclePolicy' => [ 'name' => 'UpdateLifecyclePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/UpdateLifecyclePolicy', ], 'input' => [ 'shape' => 'UpdateLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'UpdateLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'CallRateLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], ], 'shapes' => [ 'AccountAggregation' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 1536, 'min' => 1, ], 'AdditionalInstanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'systemsManagerAgent' => [ 'shape' => 'SystemsManagerAgent', ], 'userDataOverride' => [ 'shape' => 'UserDataOverride', ], ], ], 'Ami' => [ 'type' => 'structure', 'members' => [ 'region' => [ 'shape' => 'NonEmptyString', ], 'image' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'ImageState', ], 'accountId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AmiDistributionConfiguration' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AmiNameString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'targetAccountIds' => [ 'shape' => 'AccountList', ], 'amiTags' => [ 'shape' => 'TagMap', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'launchPermission' => [ 'shape' => 'LaunchPermissionConfiguration', ], ], ], 'AmiList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ami', ], ], 'AmiNameString' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[-_A-Za-z0-9{][-_A-Za-z0-9\\s:{}\\.]+[-_A-Za-z0-9}]$', ], 'Arn' => [ 'type' => 'string', ], 'Boolean' => [ 'type' => 'boolean', ], 'BuildType' => [ 'type' => 'string', 'enum' => [ 'USER_INITIATED', 'SCHEDULED', 'IMPORT', ], ], 'CallRateLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'CancelImageCreationRequest' => [ 'type' => 'structure', 'required' => [ 'imageBuildVersionArn', 'clientToken', ], 'members' => [ 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CancelImageCreationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'CancelLifecycleExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'lifecycleExecutionId', 'clientToken', ], 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CancelLifecycleExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'Component' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'ComponentType', ], 'platform' => [ 'shape' => 'Platform', ], 'supportedOsVersions' => [ 'shape' => 'OsVersionList', ], 'state' => [ 'shape' => 'ComponentState', ], 'parameters' => [ 'shape' => 'ComponentParameterDetailList', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'data' => [ 'shape' => 'ComponentData', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'encrypted' => [ 'shape' => 'NullableBoolean', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'publisher' => [ 'shape' => 'NonEmptyString', ], 'obfuscate' => [ 'shape' => 'Boolean', ], ], ], 'ComponentBuildVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$', ], 'ComponentConfiguration' => [ 'type' => 'structure', 'required' => [ 'componentArn', ], 'members' => [ 'componentArn' => [ 'shape' => 'ComponentVersionArnOrBuildVersionArn', ], 'parameters' => [ 'shape' => 'ComponentParameterList', ], ], ], 'ComponentConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentConfiguration', ], 'min' => 1, ], 'ComponentData' => [ 'type' => 'string', ], 'ComponentFormat' => [ 'type' => 'string', 'enum' => [ 'SHELL', ], ], 'ComponentParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'ComponentParameterName', ], 'value' => [ 'shape' => 'ComponentParameterValueList', ], ], ], 'ComponentParameterDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'ComponentParameterDetail' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'ComponentParameterName', ], 'type' => [ 'shape' => 'ComponentParameterType', ], 'defaultValue' => [ 'shape' => 'ComponentParameterValueList', ], 'description' => [ 'shape' => 'ComponentParameterDescription', ], ], ], 'ComponentParameterDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentParameterDetail', ], ], 'ComponentParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentParameter', ], 'min' => 1, ], 'ComponentParameterName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'ComponentParameterType' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^String|Integer|Boolean|StringList$', ], 'ComponentParameterValue' => [ 'type' => 'string', 'min' => 0, 'pattern' => '[^\\x00]*', ], 'ComponentParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentParameterValue', ], ], 'ComponentState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ComponentStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'ComponentStatus' => [ 'type' => 'string', 'enum' => [ 'DEPRECATED', ], ], 'ComponentSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'platform' => [ 'shape' => 'Platform', ], 'supportedOsVersions' => [ 'shape' => 'OsVersionList', ], 'state' => [ 'shape' => 'ComponentState', ], 'type' => [ 'shape' => 'ComponentType', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'publisher' => [ 'shape' => 'NonEmptyString', ], 'obfuscate' => [ 'shape' => 'Boolean', ], ], ], 'ComponentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentSummary', ], ], 'ComponentType' => [ 'type' => 'string', 'enum' => [ 'BUILD', 'TEST', ], ], 'ComponentVersion' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'supportedOsVersions' => [ 'shape' => 'OsVersionList', ], 'type' => [ 'shape' => 'ComponentType', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], ], ], 'ComponentVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'ComponentVersionArnOrBuildVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/(?:(?:([0-9]+|x)\\.([0-9]+|x)\\.([0-9]+|x))|(?:[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+))$', ], 'ComponentVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentVersion', ], ], 'Container' => [ 'type' => 'structure', 'members' => [ 'region' => [ 'shape' => 'NonEmptyString', ], 'imageUris' => [ 'shape' => 'StringList', ], ], ], 'ContainerDistributionConfiguration' => [ 'type' => 'structure', 'required' => [ 'targetRepository', ], 'members' => [ 'description' => [ 'shape' => 'NonEmptyString', ], 'containerTags' => [ 'shape' => 'StringList', ], 'targetRepository' => [ 'shape' => 'TargetContainerRepository', ], ], ], 'ContainerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Container', ], ], 'ContainerRecipe' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'containerType' => [ 'shape' => 'ContainerType', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'VersionNumber', ], 'components' => [ 'shape' => 'ComponentConfigurationList', ], 'instanceConfiguration' => [ 'shape' => 'InstanceConfiguration', ], 'dockerfileTemplateData' => [ 'shape' => 'DockerFileTemplate', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'encrypted' => [ 'shape' => 'NullableBoolean', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'workingDirectory' => [ 'shape' => 'NonEmptyString', ], 'targetRepository' => [ 'shape' => 'TargetContainerRepository', ], ], ], 'ContainerRecipeArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):container-recipe/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'ContainerRecipeSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'containerType' => [ 'shape' => 'ContainerType', ], 'name' => [ 'shape' => 'ResourceName', ], 'platform' => [ 'shape' => 'Platform', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ContainerRecipeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerRecipeSummary', ], ], 'ContainerRepositoryService' => [ 'type' => 'string', 'enum' => [ 'ECR', ], ], 'ContainerType' => [ 'type' => 'string', 'enum' => [ 'DOCKER', ], ], 'CreateComponentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', 'platform', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'supportedOsVersions' => [ 'shape' => 'OsVersionList', ], 'data' => [ 'shape' => 'InlineComponentData', ], 'uri' => [ 'shape' => 'Uri', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateComponentResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'componentBuildVersionArn' => [ 'shape' => 'ComponentBuildVersionArn', ], ], ], 'CreateContainerRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'containerType', 'name', 'semanticVersion', 'components', 'parentImage', 'targetRepository', 'clientToken', ], 'members' => [ 'containerType' => [ 'shape' => 'ContainerType', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'components' => [ 'shape' => 'ComponentConfigurationList', ], 'instanceConfiguration' => [ 'shape' => 'InstanceConfiguration', ], 'dockerfileTemplateData' => [ 'shape' => 'InlineDockerFileTemplate', ], 'dockerfileTemplateUri' => [ 'shape' => 'Uri', ], 'platformOverride' => [ 'shape' => 'Platform', ], 'imageOsVersionOverride' => [ 'shape' => 'NonEmptyString', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'workingDirectory' => [ 'shape' => 'NonEmptyString', ], 'targetRepository' => [ 'shape' => 'TargetContainerRepository', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateContainerRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], ], ], 'CreateDistributionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'distributions', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'distributions' => [ 'shape' => 'DistributionList', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateDistributionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], ], ], 'CreateImagePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'infrastructureConfigurationArn', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], 'imageTestsConfiguration' => [ 'shape' => 'ImageTestsConfiguration', ], 'enhancedImageMetadataEnabled' => [ 'shape' => 'NullableBoolean', ], 'schedule' => [ 'shape' => 'Schedule', ], 'status' => [ 'shape' => 'PipelineStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'workflows' => [ 'shape' => 'WorkflowConfigurationList', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], ], ], 'CreateImagePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], ], ], 'CreateImageRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', 'components', 'parentImage', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'components' => [ 'shape' => 'ComponentConfigurationList', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'blockDeviceMappings' => [ 'shape' => 'InstanceBlockDeviceMappings', ], 'tags' => [ 'shape' => 'TagMap', ], 'workingDirectory' => [ 'shape' => 'NonEmptyString', ], 'additionalInstanceConfiguration' => [ 'shape' => 'AdditionalInstanceConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateImageRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], ], ], 'CreateImageRequest' => [ 'type' => 'structure', 'required' => [ 'infrastructureConfigurationArn', 'clientToken', ], 'members' => [ 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], 'imageTestsConfiguration' => [ 'shape' => 'ImageTestsConfiguration', ], 'enhancedImageMetadataEnabled' => [ 'shape' => 'NullableBoolean', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'workflows' => [ 'shape' => 'WorkflowConfigurationList', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], ], ], 'CreateImageResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'CreateInfrastructureConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'instanceProfileName', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'instanceTypes' => [ 'shape' => 'InstanceTypeList', ], 'instanceProfileName' => [ 'shape' => 'InstanceProfileNameType', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'logging' => [ 'shape' => 'Logging', ], 'keyPair' => [ 'shape' => 'NonEmptyString', ], 'terminateInstanceOnFailure' => [ 'shape' => 'NullableBoolean', ], 'snsTopicArn' => [ 'shape' => 'SnsTopicArn', ], 'resourceTags' => [ 'shape' => 'ResourceTagMap', ], 'instanceMetadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateInfrastructureConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], ], ], 'CreateLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'executionRole', 'resourceType', 'policyDetails', 'resourceSelection', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'LifecyclePolicyStatus', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'resourceType' => [ 'shape' => 'LifecyclePolicyResourceType', ], 'policyDetails' => [ 'shape' => 'LifecyclePolicyDetails', ], 'resourceSelection' => [ 'shape' => 'LifecyclePolicyResourceSelection', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', ], ], ], 'CreateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', 'clientToken', 'type', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'data' => [ 'shape' => 'InlineWorkflowData', ], 'uri' => [ 'shape' => 'Uri', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'type' => [ 'shape' => 'WorkflowType', ], ], ], 'CreateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], ], ], 'CvssScore' => [ 'type' => 'structure', 'members' => [ 'baseScore' => [ 'shape' => 'NonNegativeDouble', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], 'source' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustment' => [ 'type' => 'structure', 'members' => [ 'metric' => [ 'shape' => 'NonEmptyString', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'CvssScoreAdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScoreAdjustment', ], ], 'CvssScoreDetails' => [ 'type' => 'structure', 'members' => [ 'scoreSource' => [ 'shape' => 'NonEmptyString', ], 'cvssSource' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], 'score' => [ 'shape' => 'NonNegativeDouble', ], 'scoringVector' => [ 'shape' => 'NonEmptyString', ], 'adjustments' => [ 'shape' => 'CvssScoreAdjustmentList', ], ], ], 'CvssScoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScore', ], ], 'DateTime' => [ 'type' => 'string', ], 'DateTimeTimestamp' => [ 'type' => 'timestamp', ], 'DeleteComponentRequest' => [ 'type' => 'structure', 'required' => [ 'componentBuildVersionArn', ], 'members' => [ 'componentBuildVersionArn' => [ 'shape' => 'ComponentBuildVersionArn', 'location' => 'querystring', 'locationName' => 'componentBuildVersionArn', ], ], ], 'DeleteComponentResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'componentBuildVersionArn' => [ 'shape' => 'ComponentBuildVersionArn', ], ], ], 'DeleteContainerRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'containerRecipeArn', ], 'members' => [ 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', 'location' => 'querystring', 'locationName' => 'containerRecipeArn', ], ], ], 'DeleteContainerRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], ], ], 'DeleteDistributionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'distributionConfigurationArn', ], 'members' => [ 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', 'location' => 'querystring', 'locationName' => 'distributionConfigurationArn', ], ], ], 'DeleteDistributionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], ], ], 'DeleteImagePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'imagePipelineArn', ], 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', 'location' => 'querystring', 'locationName' => 'imagePipelineArn', ], ], ], 'DeleteImagePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], ], ], 'DeleteImageRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'imageRecipeArn', ], 'members' => [ 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', 'location' => 'querystring', 'locationName' => 'imageRecipeArn', ], ], ], 'DeleteImageRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], ], ], 'DeleteImageRequest' => [ 'type' => 'structure', 'required' => [ 'imageBuildVersionArn', ], 'members' => [ 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', 'location' => 'querystring', 'locationName' => 'imageBuildVersionArn', ], ], ], 'DeleteImageResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'DeleteInfrastructureConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'infrastructureConfigurationArn', ], 'members' => [ 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', 'location' => 'querystring', 'locationName' => 'infrastructureConfigurationArn', ], ], ], 'DeleteInfrastructureConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], ], ], 'DeleteLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'lifecyclePolicyArn', ], 'members' => [ 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', 'location' => 'querystring', 'locationName' => 'lifecyclePolicyArn', ], ], ], 'DeleteLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', ], ], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'workflowBuildVersionArn', ], 'members' => [ 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', 'location' => 'querystring', 'locationName' => 'workflowBuildVersionArn', ], ], ], 'DeleteWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], ], ], 'DiskImageFormat' => [ 'type' => 'string', 'enum' => [ 'VMDK', 'RAW', 'VHD', ], ], 'Distribution' => [ 'type' => 'structure', 'required' => [ 'region', ], 'members' => [ 'region' => [ 'shape' => 'NonEmptyString', ], 'amiDistributionConfiguration' => [ 'shape' => 'AmiDistributionConfiguration', ], 'containerDistributionConfiguration' => [ 'shape' => 'ContainerDistributionConfiguration', ], 'licenseConfigurationArns' => [ 'shape' => 'LicenseConfigurationArnList', ], 'launchTemplateConfigurations' => [ 'shape' => 'LaunchTemplateConfigurationList', ], 's3ExportConfiguration' => [ 'shape' => 'S3ExportConfiguration', ], 'fastLaunchConfigurations' => [ 'shape' => 'FastLaunchConfigurationList', ], ], ], 'DistributionConfiguration' => [ 'type' => 'structure', 'required' => [ 'timeoutMinutes', ], 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'distributions' => [ 'shape' => 'DistributionList', ], 'timeoutMinutes' => [ 'shape' => 'DistributionTimeoutMinutes', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'dateUpdated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DistributionConfigurationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):distribution-configuration/[a-z0-9-_]+$', ], 'DistributionConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'dateUpdated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'regions' => [ 'shape' => 'RegionList', ], ], ], 'DistributionConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributionConfigurationSummary', ], ], 'DistributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Distribution', ], ], 'DistributionTimeoutMinutes' => [ 'type' => 'integer', 'max' => 720, 'min' => 30, ], 'DockerFileTemplate' => [ 'type' => 'string', ], 'EbsInstanceBlockDeviceSpecification' => [ 'type' => 'structure', 'members' => [ 'encrypted' => [ 'shape' => 'NullableBoolean', ], 'deleteOnTermination' => [ 'shape' => 'NullableBoolean', ], 'iops' => [ 'shape' => 'EbsIopsInteger', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'snapshotId' => [ 'shape' => 'NonEmptyString', ], 'volumeSize' => [ 'shape' => 'EbsVolumeSizeInteger', ], 'volumeType' => [ 'shape' => 'EbsVolumeType', ], 'throughput' => [ 'shape' => 'EbsVolumeThroughput', ], ], ], 'EbsIopsInteger' => [ 'type' => 'integer', 'max' => 64000, 'min' => 100, ], 'EbsVolumeSizeInteger' => [ 'type' => 'integer', 'max' => 16000, 'min' => 1, ], 'EbsVolumeThroughput' => [ 'type' => 'integer', 'max' => 1000, 'min' => 125, ], 'EbsVolumeType' => [ 'type' => 'string', 'enum' => [ 'standard', 'io1', 'io2', 'gp2', 'gp3', 'sc1', 'st1', ], ], 'EcrConfiguration' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'NonEmptyString', ], 'containerTags' => [ 'shape' => 'StringList', ], ], ], 'EmptyString' => [ 'type' => 'string', 'max' => 0, 'min' => 0, ], 'ErrorMessage' => [ 'type' => 'string', ], 'FastLaunchConfiguration' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'snapshotConfiguration' => [ 'shape' => 'FastLaunchSnapshotConfiguration', ], 'maxParallelLaunches' => [ 'shape' => 'MaxParallelLaunches', ], 'launchTemplate' => [ 'shape' => 'FastLaunchLaunchTemplateSpecification', ], 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'FastLaunchConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FastLaunchConfiguration', ], 'max' => 1000, 'min' => 1, ], 'FastLaunchLaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'launchTemplateId' => [ 'shape' => 'LaunchTemplateId', ], 'launchTemplateName' => [ 'shape' => 'NonEmptyString', ], 'launchTemplateVersion' => [ 'shape' => 'NonEmptyString', ], ], ], 'FastLaunchSnapshotConfiguration' => [ 'type' => 'structure', 'members' => [ 'targetResourceCount' => [ 'shape' => 'TargetResourceCount', ], ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 10, 'min' => 1, ], 'FilterName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]{1,1024}$', ], 'FilterValue' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z./_ :-]{1,1024}$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 10, 'min' => 1, ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'GetComponentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'componentArn', ], 'members' => [ 'componentArn' => [ 'shape' => 'ComponentBuildVersionArn', 'location' => 'querystring', 'locationName' => 'componentArn', ], ], ], 'GetComponentPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'GetComponentRequest' => [ 'type' => 'structure', 'required' => [ 'componentBuildVersionArn', ], 'members' => [ 'componentBuildVersionArn' => [ 'shape' => 'ComponentVersionArnOrBuildVersionArn', 'location' => 'querystring', 'locationName' => 'componentBuildVersionArn', ], ], ], 'GetComponentResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'component' => [ 'shape' => 'Component', ], ], ], 'GetContainerRecipePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'containerRecipeArn', ], 'members' => [ 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', 'location' => 'querystring', 'locationName' => 'containerRecipeArn', ], ], ], 'GetContainerRecipePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'GetContainerRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'containerRecipeArn', ], 'members' => [ 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', 'location' => 'querystring', 'locationName' => 'containerRecipeArn', ], ], ], 'GetContainerRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'containerRecipe' => [ 'shape' => 'ContainerRecipe', ], ], ], 'GetDistributionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'distributionConfigurationArn', ], 'members' => [ 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', 'location' => 'querystring', 'locationName' => 'distributionConfigurationArn', ], ], ], 'GetDistributionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'distributionConfiguration' => [ 'shape' => 'DistributionConfiguration', ], ], ], 'GetImagePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'imagePipelineArn', ], 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', 'location' => 'querystring', 'locationName' => 'imagePipelineArn', ], ], ], 'GetImagePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imagePipeline' => [ 'shape' => 'ImagePipeline', ], ], ], 'GetImagePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'imageArn', ], 'members' => [ 'imageArn' => [ 'shape' => 'ImageBuildVersionArn', 'location' => 'querystring', 'locationName' => 'imageArn', ], ], ], 'GetImagePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'GetImageRecipePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'imageRecipeArn', ], 'members' => [ 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', 'location' => 'querystring', 'locationName' => 'imageRecipeArn', ], ], ], 'GetImageRecipePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'GetImageRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'imageRecipeArn', ], 'members' => [ 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', 'location' => 'querystring', 'locationName' => 'imageRecipeArn', ], ], ], 'GetImageRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageRecipe' => [ 'shape' => 'ImageRecipe', ], ], ], 'GetImageRequest' => [ 'type' => 'structure', 'required' => [ 'imageBuildVersionArn', ], 'members' => [ 'imageBuildVersionArn' => [ 'shape' => 'ImageVersionArnOrBuildVersionArn', 'location' => 'querystring', 'locationName' => 'imageBuildVersionArn', ], ], ], 'GetImageResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'image' => [ 'shape' => 'Image', ], ], ], 'GetInfrastructureConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'infrastructureConfigurationArn', ], 'members' => [ 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', 'location' => 'querystring', 'locationName' => 'infrastructureConfigurationArn', ], ], ], 'GetInfrastructureConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'infrastructureConfiguration' => [ 'shape' => 'InfrastructureConfiguration', ], ], ], 'GetLifecycleExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'lifecycleExecutionId', ], 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', 'location' => 'querystring', 'locationName' => 'lifecycleExecutionId', ], ], ], 'GetLifecycleExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecution' => [ 'shape' => 'LifecycleExecution', ], ], ], 'GetLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'lifecyclePolicyArn', ], 'members' => [ 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', 'location' => 'querystring', 'locationName' => 'lifecyclePolicyArn', ], ], ], 'GetLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'lifecyclePolicy' => [ 'shape' => 'LifecyclePolicy', ], ], ], 'GetWorkflowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'workflowExecutionId', ], 'members' => [ 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', 'location' => 'querystring', 'locationName' => 'workflowExecutionId', ], ], ], 'GetWorkflowExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'type' => [ 'shape' => 'WorkflowType', ], 'status' => [ 'shape' => 'WorkflowExecutionStatus', ], 'message' => [ 'shape' => 'WorkflowExecutionMessage', ], 'totalStepCount' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsSucceeded' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsFailed' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsSkipped' => [ 'shape' => 'WorkflowStepCount', ], 'startTime' => [ 'shape' => 'DateTime', ], 'endTime' => [ 'shape' => 'DateTime', ], 'parallelGroup' => [ 'shape' => 'ParallelGroup', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'workflowBuildVersionArn', ], 'members' => [ 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowVersionArnOrBuildVersionArn', 'location' => 'querystring', 'locationName' => 'workflowBuildVersionArn', ], ], ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'workflow' => [ 'shape' => 'Workflow', ], ], ], 'GetWorkflowStepExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'stepExecutionId', ], 'members' => [ 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', 'location' => 'querystring', 'locationName' => 'stepExecutionId', ], ], ], 'GetWorkflowStepExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', ], 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'name' => [ 'shape' => 'WorkflowStepName', ], 'description' => [ 'shape' => 'WorkflowStepDescription', ], 'action' => [ 'shape' => 'WorkflowStepAction', ], 'status' => [ 'shape' => 'WorkflowStepExecutionStatus', ], 'rollbackStatus' => [ 'shape' => 'WorkflowStepExecutionRollbackStatus', ], 'message' => [ 'shape' => 'WorkflowStepMessage', ], 'inputs' => [ 'shape' => 'WorkflowStepInputs', ], 'outputs' => [ 'shape' => 'WorkflowStepOutputs', ], 'startTime' => [ 'shape' => 'DateTime', ], 'endTime' => [ 'shape' => 'DateTime', ], 'onFailure' => [ 'shape' => 'NonEmptyString', ], 'timeoutSeconds' => [ 'shape' => 'WorkflowStepTimeoutSecondsInteger', ], ], ], 'HttpPutResponseHopLimit' => [ 'type' => 'integer', 'max' => 64, 'min' => 1, ], 'HttpTokens' => [ 'type' => 'string', 'pattern' => 'optional|required', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Image' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'type' => [ 'shape' => 'ImageType', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'platform' => [ 'shape' => 'Platform', ], 'enhancedImageMetadataEnabled' => [ 'shape' => 'NullableBoolean', ], 'osVersion' => [ 'shape' => 'OsVersion', ], 'state' => [ 'shape' => 'ImageState', ], 'imageRecipe' => [ 'shape' => 'ImageRecipe', ], 'containerRecipe' => [ 'shape' => 'ContainerRecipe', ], 'sourcePipelineName' => [ 'shape' => 'ResourceName', ], 'sourcePipelineArn' => [ 'shape' => 'Arn', ], 'infrastructureConfiguration' => [ 'shape' => 'InfrastructureConfiguration', ], 'distributionConfiguration' => [ 'shape' => 'DistributionConfiguration', ], 'imageTestsConfiguration' => [ 'shape' => 'ImageTestsConfiguration', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'outputResources' => [ 'shape' => 'OutputResources', ], 'tags' => [ 'shape' => 'TagMap', ], 'buildType' => [ 'shape' => 'BuildType', ], 'imageSource' => [ 'shape' => 'ImageSource', ], 'scanState' => [ 'shape' => 'ImageScanState', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'deprecationTime' => [ 'shape' => 'DateTimeTimestamp', ], 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'workflows' => [ 'shape' => 'WorkflowConfigurationList', ], ], ], 'ImageAggregation' => [ 'type' => 'structure', 'members' => [ 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'ImageBuildMessage' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'ImageBuildVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$', ], 'ImageBuilderArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):(?:image-recipe|container-recipe|infrastructure-configuration|distribution-configuration|component|image|image-pipeline|lifecycle-policy|workflow\\/(?:build|test|distribution))/[a-z0-9-_]+(?:/(?:(?:x|[0-9]+)\\.(?:x|[0-9]+)\\.(?:x|[0-9]+))(?:/[0-9]+)?)?$', ], 'ImagePackage' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'NonEmptyString', ], 'packageVersion' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImagePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImagePackage', ], ], 'ImagePipeline' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'enhancedImageMetadataEnabled' => [ 'shape' => 'NullableBoolean', ], 'imageRecipeArn' => [ 'shape' => 'Arn', ], 'containerRecipeArn' => [ 'shape' => 'Arn', ], 'infrastructureConfigurationArn' => [ 'shape' => 'Arn', ], 'distributionConfigurationArn' => [ 'shape' => 'Arn', ], 'imageTestsConfiguration' => [ 'shape' => 'ImageTestsConfiguration', ], 'schedule' => [ 'shape' => 'Schedule', ], 'status' => [ 'shape' => 'PipelineStatus', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'dateUpdated' => [ 'shape' => 'DateTime', ], 'dateLastRun' => [ 'shape' => 'DateTime', ], 'dateNextRun' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'workflows' => [ 'shape' => 'WorkflowConfigurationList', ], ], ], 'ImagePipelineAggregation' => [ 'type' => 'structure', 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'ImagePipelineArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image-pipeline/[a-z0-9-_]+$', ], 'ImagePipelineList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImagePipeline', ], ], 'ImageRecipe' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'type' => [ 'shape' => 'ImageType', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'VersionNumber', ], 'components' => [ 'shape' => 'ComponentConfigurationList', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'blockDeviceMappings' => [ 'shape' => 'InstanceBlockDeviceMappings', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'workingDirectory' => [ 'shape' => 'NonEmptyString', ], 'additionalInstanceConfiguration' => [ 'shape' => 'AdditionalInstanceConfiguration', ], ], ], 'ImageRecipeArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image-recipe/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'ImageRecipeSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'platform' => [ 'shape' => 'Platform', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'parentImage' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ImageRecipeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageRecipeSummary', ], ], 'ImageScanFinding' => [ 'type' => 'structure', 'members' => [ 'awsAccountId' => [ 'shape' => 'NonEmptyString', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], 'type' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'title' => [ 'shape' => 'NonEmptyString', ], 'remediation' => [ 'shape' => 'Remediation', ], 'severity' => [ 'shape' => 'NonEmptyString', ], 'firstObservedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'updatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'inspectorScore' => [ 'shape' => 'NonNegativeDouble', ], 'inspectorScoreDetails' => [ 'shape' => 'InspectorScoreDetails', ], 'packageVulnerabilityDetails' => [ 'shape' => 'PackageVulnerabilityDetails', ], 'fixAvailable' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImageScanFindingAggregation' => [ 'type' => 'structure', 'members' => [ 'accountAggregation' => [ 'shape' => 'AccountAggregation', ], 'imageAggregation' => [ 'shape' => 'ImageAggregation', ], 'imagePipelineAggregation' => [ 'shape' => 'ImagePipelineAggregation', ], 'vulnerabilityIdAggregation' => [ 'shape' => 'VulnerabilityIdAggregation', ], ], ], 'ImageScanFindingAggregationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageScanFindingAggregation', ], ], 'ImageScanFindingsFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FilterName', ], 'values' => [ 'shape' => 'ImageScanFindingsFilterValues', ], ], ], 'ImageScanFindingsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageScanFindingsFilter', ], 'max' => 1, 'min' => 1, ], 'ImageScanFindingsFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 1, 'min' => 1, ], 'ImageScanFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageScanFinding', ], 'max' => 25, ], 'ImageScanState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ImageScanStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImageScanStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SCANNING', 'COLLECTING', 'COMPLETED', 'ABANDONED', 'FAILED', 'TIMED_OUT', ], ], 'ImageScanningConfiguration' => [ 'type' => 'structure', 'members' => [ 'imageScanningEnabled' => [ 'shape' => 'NullableBoolean', ], 'ecrConfiguration' => [ 'shape' => 'EcrConfiguration', ], ], ], 'ImageSource' => [ 'type' => 'string', 'enum' => [ 'AMAZON_MANAGED', 'AWS_MARKETPLACE', 'IMPORTED', 'CUSTOM', ], ], 'ImageState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ImageStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImageStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CREATING', 'BUILDING', 'TESTING', 'DISTRIBUTING', 'INTEGRATING', 'AVAILABLE', 'CANCELLED', 'FAILED', 'DEPRECATED', 'DELETED', 'DISABLED', ], ], 'ImageSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'type' => [ 'shape' => 'ImageType', ], 'version' => [ 'shape' => 'VersionNumber', ], 'platform' => [ 'shape' => 'Platform', ], 'osVersion' => [ 'shape' => 'OsVersion', ], 'state' => [ 'shape' => 'ImageState', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'outputResources' => [ 'shape' => 'OutputResources', ], 'tags' => [ 'shape' => 'TagMap', ], 'buildType' => [ 'shape' => 'BuildType', ], 'imageSource' => [ 'shape' => 'ImageSource', ], 'deprecationTime' => [ 'shape' => 'DateTimeTimestamp', ], 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], ], ], 'ImageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageSummary', ], ], 'ImageTestsConfiguration' => [ 'type' => 'structure', 'members' => [ 'imageTestsEnabled' => [ 'shape' => 'NullableBoolean', ], 'timeoutMinutes' => [ 'shape' => 'ImageTestsTimeoutMinutes', ], ], ], 'ImageTestsTimeoutMinutes' => [ 'type' => 'integer', 'max' => 1440, 'min' => 60, ], 'ImageType' => [ 'type' => 'string', 'enum' => [ 'AMI', 'DOCKER', ], ], 'ImageVersion' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'type' => [ 'shape' => 'ImageType', ], 'version' => [ 'shape' => 'VersionNumber', ], 'platform' => [ 'shape' => 'Platform', ], 'osVersion' => [ 'shape' => 'OsVersion', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'buildType' => [ 'shape' => 'BuildType', ], 'imageSource' => [ 'shape' => 'ImageSource', ], ], ], 'ImageVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'ImageVersionArnOrBuildVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/(?:(?:([0-9]+|x)\\.([0-9]+|x)\\.([0-9]+|x))|(?:[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+))$', ], 'ImageVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageVersion', ], ], 'ImportComponentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', 'type', 'format', 'platform', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'ComponentType', ], 'format' => [ 'shape' => 'ComponentFormat', ], 'platform' => [ 'shape' => 'Platform', ], 'data' => [ 'shape' => 'NonEmptyString', ], 'uri' => [ 'shape' => 'Uri', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ImportComponentResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'componentBuildVersionArn' => [ 'shape' => 'ComponentBuildVersionArn', ], ], ], 'ImportVmImageRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', 'platform', 'vmImportTaskId', 'clientToken', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'platform' => [ 'shape' => 'Platform', ], 'osVersion' => [ 'shape' => 'OsVersion', ], 'vmImportTaskId' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ImportVmImageResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'InfrastructureConfiguration' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'instanceTypes' => [ 'shape' => 'InstanceTypeList', ], 'instanceProfileName' => [ 'shape' => 'InstanceProfileNameType', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'logging' => [ 'shape' => 'Logging', ], 'keyPair' => [ 'shape' => 'NonEmptyString', ], 'terminateInstanceOnFailure' => [ 'shape' => 'NullableBoolean', ], 'snsTopicArn' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'dateUpdated' => [ 'shape' => 'DateTime', ], 'resourceTags' => [ 'shape' => 'ResourceTagMap', ], 'instanceMetadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'InfrastructureConfigurationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):infrastructure-configuration/[a-z0-9-_]+$', ], 'InfrastructureConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ImageBuilderArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'dateUpdated' => [ 'shape' => 'DateTime', ], 'resourceTags' => [ 'shape' => 'ResourceTagMap', ], 'tags' => [ 'shape' => 'TagMap', ], 'instanceTypes' => [ 'shape' => 'InstanceTypeList', ], 'instanceProfileName' => [ 'shape' => 'InstanceProfileNameType', ], ], ], 'InfrastructureConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InfrastructureConfigurationSummary', ], ], 'InlineComponentData' => [ 'type' => 'string', 'max' => 16000, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'InlineDockerFileTemplate' => [ 'type' => 'string', 'max' => 16000, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'InlineWorkflowData' => [ 'type' => 'string', 'max' => 16000, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'InspectorScoreDetails' => [ 'type' => 'structure', 'members' => [ 'adjustedCvss' => [ 'shape' => 'CvssScoreDetails', ], ], ], 'InstanceBlockDeviceMapping' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'NonEmptyString', ], 'ebs' => [ 'shape' => 'EbsInstanceBlockDeviceSpecification', ], 'virtualName' => [ 'shape' => 'NonEmptyString', ], 'noDevice' => [ 'shape' => 'EmptyString', ], ], ], 'InstanceBlockDeviceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceBlockDeviceMapping', ], ], 'InstanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'NonEmptyString', ], 'blockDeviceMappings' => [ 'shape' => 'InstanceBlockDeviceMappings', ], ], ], 'InstanceMetadataOptions' => [ 'type' => 'structure', 'members' => [ 'httpTokens' => [ 'shape' => 'HttpTokens', ], 'httpPutResponseHopLimit' => [ 'shape' => 'HttpPutResponseHopLimit', ], ], ], 'InstanceProfileNameType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[\\w+=,.@-]+$', ], 'InstanceType' => [ 'type' => 'string', ], 'InstanceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceType', ], ], 'InvalidPaginationTokenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidVersionNumberException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LaunchPermissionConfiguration' => [ 'type' => 'structure', 'members' => [ 'userIds' => [ 'shape' => 'AccountList', ], 'userGroups' => [ 'shape' => 'StringList', ], 'organizationArns' => [ 'shape' => 'OrganizationArnList', ], 'organizationalUnitArns' => [ 'shape' => 'OrganizationalUnitArnList', ], ], ], 'LaunchTemplateConfiguration' => [ 'type' => 'structure', 'required' => [ 'launchTemplateId', ], 'members' => [ 'launchTemplateId' => [ 'shape' => 'LaunchTemplateId', ], 'accountId' => [ 'shape' => 'AccountId', ], 'setDefaultVersion' => [ 'shape' => 'Boolean', ], ], ], 'LaunchTemplateConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchTemplateConfiguration', ], 'max' => 100, 'min' => 1, ], 'LaunchTemplateId' => [ 'type' => 'string', 'pattern' => '^lt-[a-z0-9-_]{17}$', ], 'LicenseConfigurationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:license-manager:[^:]+:[0-9]{12}:license-configuration:lic-[a-z0-9-_]{32}$', ], 'LicenseConfigurationArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConfigurationArn', ], 'max' => 50, 'min' => 1, ], 'LifecycleExecution' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', ], 'resourcesImpactedSummary' => [ 'shape' => 'LifecycleExecutionResourcesImpactedSummary', ], 'state' => [ 'shape' => 'LifecycleExecutionState', ], 'startTime' => [ 'shape' => 'DateTimeTimestamp', ], 'endTime' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'LifecycleExecutionId' => [ 'type' => 'string', 'pattern' => '^lce-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'LifecycleExecutionResource' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'NonEmptyString', ], 'resourceId' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'LifecycleExecutionResourceState', ], 'action' => [ 'shape' => 'LifecycleExecutionResourceAction', ], 'region' => [ 'shape' => 'NonEmptyString', ], 'snapshots' => [ 'shape' => 'LifecycleExecutionSnapshotResourceList', ], 'imageUris' => [ 'shape' => 'StringList', ], 'startTime' => [ 'shape' => 'DateTimeTimestamp', ], 'endTime' => [ 'shape' => 'DateTimeTimestamp', ], ], ], 'LifecycleExecutionResourceAction' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'LifecycleExecutionResourceActionName', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'LifecycleExecutionResourceActionName' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETE', 'DEPRECATE', 'DISABLE', ], ], 'LifecycleExecutionResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleExecutionResource', ], ], 'LifecycleExecutionResourceState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'LifecycleExecutionResourceStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'LifecycleExecutionResourceStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'IN_PROGRESS', 'SKIPPED', 'SUCCESS', ], ], 'LifecycleExecutionResourcesImpactedSummary' => [ 'type' => 'structure', 'members' => [ 'hasImpactedResources' => [ 'shape' => 'Boolean', ], ], ], 'LifecycleExecutionSnapshotResource' => [ 'type' => 'structure', 'members' => [ 'snapshotId' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'LifecycleExecutionResourceState', ], ], ], 'LifecycleExecutionSnapshotResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleExecutionSnapshotResource', ], ], 'LifecycleExecutionState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'LifecycleExecutionStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'LifecycleExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'CANCELLED', 'CANCELLING', 'FAILED', 'SUCCESS', 'PENDING', ], ], 'LifecycleExecutionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecycleExecution', ], ], 'LifecyclePolicy' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LifecyclePolicyArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'LifecyclePolicyStatus', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'resourceType' => [ 'shape' => 'LifecyclePolicyResourceType', ], 'policyDetails' => [ 'shape' => 'LifecyclePolicyDetails', ], 'resourceSelection' => [ 'shape' => 'LifecyclePolicyResourceSelection', ], 'dateCreated' => [ 'shape' => 'DateTimeTimestamp', ], 'dateUpdated' => [ 'shape' => 'DateTimeTimestamp', ], 'dateLastRun' => [ 'shape' => 'DateTimeTimestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LifecyclePolicyArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):lifecycle-policy/[a-z0-9-_]+$', ], 'LifecyclePolicyDetail' => [ 'type' => 'structure', 'required' => [ 'action', 'filter', ], 'members' => [ 'action' => [ 'shape' => 'LifecyclePolicyDetailAction', ], 'filter' => [ 'shape' => 'LifecyclePolicyDetailFilter', ], 'exclusionRules' => [ 'shape' => 'LifecyclePolicyDetailExclusionRules', ], ], ], 'LifecyclePolicyDetailAction' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'LifecyclePolicyDetailActionType', ], 'includeResources' => [ 'shape' => 'LifecyclePolicyDetailActionIncludeResources', ], ], ], 'LifecyclePolicyDetailActionIncludeResources' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'Boolean', ], 'snapshots' => [ 'shape' => 'Boolean', ], 'containers' => [ 'shape' => 'Boolean', ], ], ], 'LifecyclePolicyDetailActionType' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'DEPRECATE', 'DISABLE', ], ], 'LifecyclePolicyDetailExclusionRules' => [ 'type' => 'structure', 'members' => [ 'tagMap' => [ 'shape' => 'TagMap', ], 'amis' => [ 'shape' => 'LifecyclePolicyDetailExclusionRulesAmis', ], ], ], 'LifecyclePolicyDetailExclusionRulesAmis' => [ 'type' => 'structure', 'members' => [ 'isPublic' => [ 'shape' => 'Boolean', ], 'regions' => [ 'shape' => 'StringList', ], 'sharedAccounts' => [ 'shape' => 'AccountList', ], 'lastLaunched' => [ 'shape' => 'LifecyclePolicyDetailExclusionRulesAmisLastLaunched', ], 'tagMap' => [ 'shape' => 'TagMap', ], ], ], 'LifecyclePolicyDetailExclusionRulesAmisLastLaunched' => [ 'type' => 'structure', 'required' => [ 'value', 'unit', ], 'members' => [ 'value' => [ 'shape' => 'LifecyclePolicyDetailExclusionRulesAmisLastLaunchedValue', ], 'unit' => [ 'shape' => 'LifecyclePolicyTimeUnit', ], ], ], 'LifecyclePolicyDetailExclusionRulesAmisLastLaunchedValue' => [ 'type' => 'integer', 'max' => 365, 'min' => 1, ], 'LifecyclePolicyDetailFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'LifecyclePolicyDetailFilterType', ], 'value' => [ 'shape' => 'LifecyclePolicyDetailFilterValue', ], 'unit' => [ 'shape' => 'LifecyclePolicyTimeUnit', ], 'retainAtLeast' => [ 'shape' => 'LifecyclePolicyDetailFilterRetainAtLeast', ], ], ], 'LifecyclePolicyDetailFilterRetainAtLeast' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'LifecyclePolicyDetailFilterType' => [ 'type' => 'string', 'enum' => [ 'AGE', 'COUNT', ], ], 'LifecyclePolicyDetailFilterValue' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'LifecyclePolicyDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecyclePolicyDetail', ], 'max' => 3, 'min' => 1, ], 'LifecyclePolicyResourceSelection' => [ 'type' => 'structure', 'members' => [ 'recipes' => [ 'shape' => 'LifecyclePolicyResourceSelectionRecipes', ], 'tagMap' => [ 'shape' => 'TagMap', ], ], ], 'LifecyclePolicyResourceSelectionRecipe' => [ 'type' => 'structure', 'required' => [ 'name', 'semanticVersion', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'semanticVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'LifecyclePolicyResourceSelectionRecipes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecyclePolicyResourceSelectionRecipe', ], 'max' => 50, 'min' => 1, ], 'LifecyclePolicyResourceType' => [ 'type' => 'string', 'enum' => [ 'AMI_IMAGE', 'CONTAINER_IMAGE', ], ], 'LifecyclePolicyStatus' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'LifecyclePolicySummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'LifecyclePolicyArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'LifecyclePolicyStatus', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'resourceType' => [ 'shape' => 'LifecyclePolicyResourceType', ], 'dateCreated' => [ 'shape' => 'DateTimeTimestamp', ], 'dateUpdated' => [ 'shape' => 'DateTimeTimestamp', ], 'dateLastRun' => [ 'shape' => 'DateTimeTimestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LifecyclePolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecyclePolicySummary', ], ], 'LifecyclePolicyTimeUnit' => [ 'type' => 'string', 'enum' => [ 'DAYS', 'WEEKS', 'MONTHS', 'YEARS', ], ], 'ListComponentBuildVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'componentVersionArn', ], 'members' => [ 'componentVersionArn' => [ 'shape' => 'ComponentVersionArn', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListComponentBuildVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'componentSummaryList' => [ 'shape' => 'ComponentSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListComponentsRequest' => [ 'type' => 'structure', 'members' => [ 'owner' => [ 'shape' => 'Ownership', ], 'filters' => [ 'shape' => 'FilterList', ], 'byName' => [ 'shape' => 'Boolean', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'componentVersionList' => [ 'shape' => 'ComponentVersionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListContainerRecipesRequest' => [ 'type' => 'structure', 'members' => [ 'owner' => [ 'shape' => 'Ownership', ], 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListContainerRecipesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'containerRecipeSummaryList' => [ 'shape' => 'ContainerRecipeSummaryList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListDistributionConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDistributionConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'distributionConfigurationSummaryList' => [ 'shape' => 'DistributionConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageBuildVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'imageVersionArn', ], 'members' => [ 'imageVersionArn' => [ 'shape' => 'ImageVersionArn', ], 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageBuildVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageSummaryList' => [ 'shape' => 'ImageSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePackagesRequest' => [ 'type' => 'structure', 'required' => [ 'imageBuildVersionArn', ], 'members' => [ 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePackagesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imagePackageList' => [ 'shape' => 'ImagePackageList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePipelineImagesRequest' => [ 'type' => 'structure', 'required' => [ 'imagePipelineArn', ], 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePipelineImagesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageSummaryList' => [ 'shape' => 'ImageSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagePipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imagePipelineList' => [ 'shape' => 'ImagePipelineList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageRecipesRequest' => [ 'type' => 'structure', 'members' => [ 'owner' => [ 'shape' => 'Ownership', ], 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageRecipesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageRecipeSummaryList' => [ 'shape' => 'ImageRecipeSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageScanFindingAggregationsRequest' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'Filter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageScanFindingAggregationsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'aggregationType' => [ 'shape' => 'NonEmptyString', ], 'responses' => [ 'shape' => 'ImageScanFindingAggregationsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageScanFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'ImageScanFindingsFilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImageScanFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'findings' => [ 'shape' => 'ImageScanFindingsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListImagesRequest' => [ 'type' => 'structure', 'members' => [ 'owner' => [ 'shape' => 'Ownership', ], 'filters' => [ 'shape' => 'FilterList', ], 'byName' => [ 'shape' => 'Boolean', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'includeDeprecated' => [ 'shape' => 'NullableBoolean', ], ], ], 'ListImagesResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageVersionList' => [ 'shape' => 'ImageVersionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListInfrastructureConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListInfrastructureConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'infrastructureConfigurationSummaryList' => [ 'shape' => 'InfrastructureConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLifecycleExecutionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'lifecycleExecutionId', ], 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'parentResourceId' => [ 'shape' => 'NonEmptyString', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLifecycleExecutionResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'lifecycleExecutionState' => [ 'shape' => 'LifecycleExecutionState', ], 'resources' => [ 'shape' => 'LifecycleExecutionResourceList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLifecycleExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'resourceArn' => [ 'shape' => 'ImageBuilderArn', ], ], ], 'ListLifecycleExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecutions' => [ 'shape' => 'LifecycleExecutionsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLifecyclePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLifecyclePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'lifecyclePolicySummaryList' => [ 'shape' => 'LifecyclePolicySummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ImageBuilderArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWaitingWorkflowStepsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWaitingWorkflowStepsResponse' => [ 'type' => 'structure', 'members' => [ 'steps' => [ 'shape' => 'WorkflowStepExecutionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowBuildVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'workflowVersionArn', ], 'members' => [ 'workflowVersionArn' => [ 'shape' => 'WorkflowWildcardVersionArn', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowBuildVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'workflowSummaryList' => [ 'shape' => 'WorkflowSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'imageBuildVersionArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'ListWorkflowExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'workflowExecutions' => [ 'shape' => 'WorkflowExecutionsList', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'message' => [ 'shape' => 'ImageBuildMessage', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowStepExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'workflowExecutionId', ], 'members' => [ 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], ], ], 'ListWorkflowStepExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'steps' => [ 'shape' => 'WorkflowStepExecutionsList', ], 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'message' => [ 'shape' => 'ImageBuildMessage', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'owner' => [ 'shape' => 'Ownership', ], 'filters' => [ 'shape' => 'FilterList', ], 'byName' => [ 'shape' => 'Boolean', ], 'maxResults' => [ 'shape' => 'RestrictedInteger', 'box' => true, ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'workflowVersionList' => [ 'shape' => 'WorkflowVersionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'Logging' => [ 'type' => 'structure', 'members' => [ 's3Logs' => [ 'shape' => 'S3Logs', ], ], ], 'MaxParallelLaunches' => [ 'type' => 'integer', 'min' => 1, ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'min' => 1, ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0, ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'OnWorkflowFailure' => [ 'type' => 'string', 'enum' => [ 'CONTINUE', 'ABORT', ], ], 'OrganizationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:organizations::[0-9]{12}:organization/o-[a-z0-9]{10,32}$', ], 'OrganizationArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationArn', ], 'max' => 25, 'min' => 1, ], 'OrganizationalUnitArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:organizations::[0-9]{12}:ou/o-[a-z0-9]{10,32}/ou-[0-9a-z]{4,32}-[0-9a-z]{8,32}', ], 'OrganizationalUnitArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitArn', ], 'max' => 25, 'min' => 1, ], 'OsVersion' => [ 'type' => 'string', 'min' => 1, ], 'OsVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OsVersion', ], 'max' => 25, 'min' => 1, ], 'OutputResources' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'AmiList', ], 'containers' => [ 'shape' => 'ContainerList', ], ], ], 'Ownership' => [ 'type' => 'string', 'enum' => [ 'Self', 'Shared', 'Amazon', 'ThirdParty', ], ], 'PackageArchitecture' => [ 'type' => 'string', ], 'PackageEpoch' => [ 'type' => 'integer', ], 'PackageVulnerabilityDetails' => [ 'type' => 'structure', 'required' => [ 'vulnerabilityId', ], 'members' => [ 'vulnerabilityId' => [ 'shape' => 'VulnerabilityId', ], 'vulnerablePackages' => [ 'shape' => 'VulnerablePackageList', ], 'source' => [ 'shape' => 'NonEmptyString', ], 'cvss' => [ 'shape' => 'CvssScoreList', ], 'relatedVulnerabilities' => [ 'shape' => 'VulnerabilityIdList', ], 'sourceUrl' => [ 'shape' => 'NonEmptyString', ], 'vendorSeverity' => [ 'shape' => 'NonEmptyString', ], 'vendorCreatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'vendorUpdatedAt' => [ 'shape' => 'DateTimeTimestamp', ], 'referenceUrls' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'ParallelGroup' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9-_+#]{0,99}$', ], 'PipelineExecutionStartCondition' => [ 'type' => 'string', 'enum' => [ 'EXPRESSION_MATCH_ONLY', 'EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE', ], ], 'PipelineStatus' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Platform' => [ 'type' => 'string', 'enum' => [ 'Windows', 'Linux', ], ], 'PutComponentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'componentArn', 'policy', ], 'members' => [ 'componentArn' => [ 'shape' => 'ComponentBuildVersionArn', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'PutComponentPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'componentArn' => [ 'shape' => 'ComponentBuildVersionArn', ], ], ], 'PutContainerRecipePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'containerRecipeArn', 'policy', ], 'members' => [ 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'PutContainerRecipePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], ], ], 'PutImagePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'imageArn', 'policy', ], 'members' => [ 'imageArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'PutImagePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'PutImageRecipePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'imageRecipeArn', 'policy', ], 'members' => [ 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], 'policy' => [ 'shape' => 'ResourcePolicyDocument', ], ], ], 'PutImageRecipePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'recommendation' => [ 'shape' => 'RemediationRecommendation', ], ], ], 'RemediationRecommendation' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'NonEmptyString', ], 'url' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceDependencyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceName' => [ 'type' => 'string', 'pattern' => '^[-_A-Za-z-0-9][-_A-Za-z0-9 ]{1,126}[-_A-Za-z-0-9]$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePolicyDocument' => [ 'type' => 'string', 'max' => 30000, 'min' => 1, ], 'ResourceState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ResourceStatus', ], ], ], 'ResourceStateUpdateExclusionRules' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'LifecyclePolicyDetailExclusionRulesAmis', ], ], ], 'ResourceStateUpdateIncludeResources' => [ 'type' => 'structure', 'members' => [ 'amis' => [ 'shape' => 'Boolean', ], 'snapshots' => [ 'shape' => 'Boolean', ], 'containers' => [ 'shape' => 'Boolean', ], ], ], 'ResourceStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETED', 'DEPRECATED', 'DISABLED', ], ], 'ResourceTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 30, 'min' => 1, ], 'RestrictedInteger' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'RoleNameOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(?:arn:aws(?:-[a-z]+)*:iam::[0-9]{12}:role/)?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'S3ExportConfiguration' => [ 'type' => 'structure', 'required' => [ 'roleName', 'diskImageFormat', 's3Bucket', ], 'members' => [ 'roleName' => [ 'shape' => 'NonEmptyString', ], 'diskImageFormat' => [ 'shape' => 'DiskImageFormat', ], 's3Bucket' => [ 'shape' => 'NonEmptyString', ], 's3Prefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'S3Logs' => [ 'type' => 'structure', 'members' => [ 's3BucketName' => [ 'shape' => 'NonEmptyString', ], 's3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'scheduleExpression' => [ 'shape' => 'NonEmptyString', ], 'timezone' => [ 'shape' => 'Timezone', ], 'pipelineExecutionStartCondition' => [ 'shape' => 'PipelineExecutionStartCondition', ], ], ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SendWorkflowStepActionRequest' => [ 'type' => 'structure', 'required' => [ 'stepExecutionId', 'imageBuildVersionArn', 'action', 'clientToken', ], 'members' => [ 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'action' => [ 'shape' => 'WorkflowStepActionType', ], 'reason' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'SendWorkflowStepActionResponse' => [ 'type' => 'structure', 'members' => [ 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, ], 'SeverityCountNumber' => [ 'type' => 'long', ], 'SeverityCounts' => [ 'type' => 'structure', 'members' => [ 'all' => [ 'shape' => 'SeverityCountNumber', ], 'critical' => [ 'shape' => 'SeverityCountNumber', ], 'high' => [ 'shape' => 'SeverityCountNumber', ], 'medium' => [ 'shape' => 'SeverityCountNumber', ], ], ], 'SnsTopicArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[^:]*:sns:[^:]+:[0-9]{12}:[a-zA-Z0-9-_]{1,256}$', ], 'SourceLayerHash' => [ 'type' => 'string', ], 'StartImagePipelineExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'imagePipelineArn', 'clientToken', ], 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartImagePipelineExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'StartResourceStateUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'state', 'clientToken', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'state' => [ 'shape' => 'ResourceState', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'includeResources' => [ 'shape' => 'ResourceStateUpdateIncludeResources', ], 'exclusionRules' => [ 'shape' => 'ResourceStateUpdateExclusionRules', ], 'updateAt' => [ 'shape' => 'DateTimeTimestamp', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartResourceStateUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'lifecycleExecutionId' => [ 'shape' => 'LifecycleExecutionId', ], 'resourceArn' => [ 'shape' => 'ImageBuildVersionArn', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SystemsManagerAgent' => [ 'type' => 'structure', 'members' => [ 'uninstallAfterBuild' => [ 'shape' => 'NullableBoolean', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ImageBuilderArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'TargetContainerRepository' => [ 'type' => 'structure', 'required' => [ 'service', 'repositoryName', ], 'members' => [ 'service' => [ 'shape' => 'ContainerRepositoryService', ], 'repositoryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'TargetResourceCount' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'Timezone' => [ 'type' => 'string', 'max' => 100, 'min' => 3, 'pattern' => '[a-zA-Z0-9]{2,}(?:\\/[a-zA-z0-9-_+]+)*', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ImageBuilderArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDistributionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'distributionConfigurationArn', 'distributions', 'clientToken', ], 'members' => [ 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'distributions' => [ 'shape' => 'DistributionList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateDistributionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], ], ], 'UpdateImagePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'imagePipelineArn', 'infrastructureConfigurationArn', 'clientToken', ], 'members' => [ 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'imageRecipeArn' => [ 'shape' => 'ImageRecipeArn', ], 'containerRecipeArn' => [ 'shape' => 'ContainerRecipeArn', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], 'distributionConfigurationArn' => [ 'shape' => 'DistributionConfigurationArn', ], 'imageTestsConfiguration' => [ 'shape' => 'ImageTestsConfiguration', ], 'enhancedImageMetadataEnabled' => [ 'shape' => 'NullableBoolean', ], 'schedule' => [ 'shape' => 'Schedule', ], 'status' => [ 'shape' => 'PipelineStatus', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'workflows' => [ 'shape' => 'WorkflowConfigurationList', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], ], ], 'UpdateImagePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'imagePipelineArn' => [ 'shape' => 'ImagePipelineArn', ], ], ], 'UpdateInfrastructureConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'infrastructureConfigurationArn', 'instanceProfileName', 'clientToken', ], 'members' => [ 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'instanceTypes' => [ 'shape' => 'InstanceTypeList', ], 'instanceProfileName' => [ 'shape' => 'InstanceProfileNameType', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'logging' => [ 'shape' => 'Logging', ], 'keyPair' => [ 'shape' => 'NonEmptyString', ], 'terminateInstanceOnFailure' => [ 'shape' => 'NullableBoolean', ], 'snsTopicArn' => [ 'shape' => 'SnsTopicArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceTags' => [ 'shape' => 'ResourceTagMap', ], 'instanceMetadataOptions' => [ 'shape' => 'InstanceMetadataOptions', ], ], ], 'UpdateInfrastructureConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'requestId' => [ 'shape' => 'NonEmptyString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'infrastructureConfigurationArn' => [ 'shape' => 'InfrastructureConfigurationArn', ], ], ], 'UpdateLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'lifecyclePolicyArn', 'executionRole', 'resourceType', 'policyDetails', 'resourceSelection', 'clientToken', ], 'members' => [ 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'LifecyclePolicyStatus', ], 'executionRole' => [ 'shape' => 'RoleNameOrArn', ], 'resourceType' => [ 'shape' => 'LifecyclePolicyResourceType', ], 'policyDetails' => [ 'shape' => 'LifecyclePolicyDetails', ], 'resourceSelection' => [ 'shape' => 'LifecyclePolicyResourceSelection', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'lifecyclePolicyArn' => [ 'shape' => 'LifecyclePolicyArn', ], ], ], 'Uri' => [ 'type' => 'string', ], 'UserDataOverride' => [ 'type' => 'string', 'max' => 21847, 'min' => 1, 'pattern' => '^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$', ], 'VersionNumber' => [ 'type' => 'string', 'pattern' => '^[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'VulnerabilityId' => [ 'type' => 'string', ], 'VulnerabilityIdAggregation' => [ 'type' => 'structure', 'members' => [ 'vulnerabilityId' => [ 'shape' => 'NonEmptyString', ], 'severityCounts' => [ 'shape' => 'SeverityCounts', ], ], ], 'VulnerabilityIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerabilityId', ], ], 'VulnerablePackage' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'version' => [ 'shape' => 'NonEmptyString', ], 'sourceLayerHash' => [ 'shape' => 'SourceLayerHash', ], 'epoch' => [ 'shape' => 'PackageEpoch', ], 'release' => [ 'shape' => 'NonEmptyString', ], 'arch' => [ 'shape' => 'PackageArchitecture', ], 'packageManager' => [ 'shape' => 'NonEmptyString', ], 'filePath' => [ 'shape' => 'NonEmptyString', ], 'fixedInVersion' => [ 'shape' => 'NonEmptyString', ], 'remediation' => [ 'shape' => 'NonEmptyString', ], ], ], 'VulnerablePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerablePackage', ], ], 'Workflow' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'WorkflowType', ], 'state' => [ 'shape' => 'WorkflowState', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'data' => [ 'shape' => 'WorkflowData', ], 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], 'parameters' => [ 'shape' => 'WorkflowParameterDetailList', ], ], ], 'WorkflowBuildVersionArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$', ], 'WorkflowConfiguration' => [ 'type' => 'structure', 'required' => [ 'workflowArn', ], 'members' => [ 'workflowArn' => [ 'shape' => 'WorkflowVersionArnOrBuildVersionArn', ], 'parameters' => [ 'shape' => 'WorkflowParameterList', ], 'parallelGroup' => [ 'shape' => 'ParallelGroup', ], 'onFailure' => [ 'shape' => 'OnWorkflowFailure', ], ], ], 'WorkflowConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowConfiguration', ], ], 'WorkflowData' => [ 'type' => 'string', ], 'WorkflowExecutionId' => [ 'type' => 'string', 'pattern' => '^wf-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'WorkflowExecutionMessage' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'WorkflowExecutionMetadata' => [ 'type' => 'structure', 'members' => [ 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], 'type' => [ 'shape' => 'WorkflowType', ], 'status' => [ 'shape' => 'WorkflowExecutionStatus', ], 'message' => [ 'shape' => 'WorkflowExecutionMessage', ], 'totalStepCount' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsSucceeded' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsFailed' => [ 'shape' => 'WorkflowStepCount', ], 'totalStepsSkipped' => [ 'shape' => 'WorkflowStepCount', ], 'startTime' => [ 'shape' => 'DateTime', ], 'endTime' => [ 'shape' => 'DateTime', ], 'parallelGroup' => [ 'shape' => 'ParallelGroup', ], ], ], 'WorkflowExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SKIPPED', 'RUNNING', 'COMPLETED', 'FAILED', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_COMPLETED', 'CANCELLED', ], ], 'WorkflowExecutionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowExecutionMetadata', ], ], 'WorkflowNameArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/x\\.x\\.x$', ], 'WorkflowParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'WorkflowParameterName', ], 'value' => [ 'shape' => 'WorkflowParameterValueList', ], ], ], 'WorkflowParameterDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[^\\x00]+', ], 'WorkflowParameterDetail' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'WorkflowParameterName', ], 'type' => [ 'shape' => 'WorkflowParameterType', ], 'defaultValue' => [ 'shape' => 'WorkflowParameterValueList', ], 'description' => [ 'shape' => 'WorkflowParameterDescription', ], ], ], 'WorkflowParameterDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowParameterDetail', ], ], 'WorkflowParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowParameter', ], 'min' => 1, ], 'WorkflowParameterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'WorkflowParameterType' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^string|integer|boolean|stringList$', ], 'WorkflowParameterValue' => [ 'type' => 'string', 'min' => 0, 'pattern' => '[^\\x00]*', ], 'WorkflowParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowParameterValue', ], ], 'WorkflowState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'WorkflowStatus', ], 'reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'WorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'DEPRECATED', ], ], 'WorkflowStepAction' => [ 'type' => 'string', 'pattern' => '^[A-Za-z][A-Za-z0-9-_]{1,99}$', ], 'WorkflowStepActionType' => [ 'type' => 'string', 'enum' => [ 'RESUME', 'STOP', ], ], 'WorkflowStepCount' => [ 'type' => 'integer', ], 'WorkflowStepDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'WorkflowStepExecution' => [ 'type' => 'structure', 'members' => [ 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', ], 'imageBuildVersionArn' => [ 'shape' => 'ImageBuildVersionArn', ], 'workflowExecutionId' => [ 'shape' => 'WorkflowExecutionId', ], 'workflowBuildVersionArn' => [ 'shape' => 'WorkflowBuildVersionArn', ], 'name' => [ 'shape' => 'WorkflowStepName', ], 'action' => [ 'shape' => 'WorkflowStepAction', ], 'startTime' => [ 'shape' => 'DateTime', ], ], ], 'WorkflowStepExecutionId' => [ 'type' => 'string', 'pattern' => '^step-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'WorkflowStepExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepExecution', ], ], 'WorkflowStepExecutionRollbackStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'SKIPPED', 'FAILED', ], ], 'WorkflowStepExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SKIPPED', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', ], ], 'WorkflowStepExecutionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStepMetadata', ], ], 'WorkflowStepInputs' => [ 'type' => 'string', ], 'WorkflowStepMessage' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'WorkflowStepMetadata' => [ 'type' => 'structure', 'members' => [ 'stepExecutionId' => [ 'shape' => 'WorkflowStepExecutionId', ], 'name' => [ 'shape' => 'WorkflowStepName', ], 'description' => [ 'shape' => 'WorkflowStepDescription', ], 'action' => [ 'shape' => 'WorkflowStepAction', ], 'status' => [ 'shape' => 'WorkflowStepExecutionStatus', ], 'rollbackStatus' => [ 'shape' => 'WorkflowStepExecutionRollbackStatus', ], 'message' => [ 'shape' => 'WorkflowStepMessage', ], 'inputs' => [ 'shape' => 'WorkflowStepInputs', ], 'outputs' => [ 'shape' => 'WorkflowStepOutputs', ], 'startTime' => [ 'shape' => 'DateTime', ], 'endTime' => [ 'shape' => 'DateTime', ], ], ], 'WorkflowStepName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z][A-Za-z0-9-_]{1,99}$', ], 'WorkflowStepOutputs' => [ 'type' => 'string', ], 'WorkflowStepTimeoutSecondsInteger' => [ 'type' => 'integer', 'max' => 43200, 'min' => 0, ], 'WorkflowSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowNameArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'changeDescription' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'WorkflowType', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'WorkflowState', ], 'dateCreated' => [ 'shape' => 'DateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'WorkflowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowSummary', ], ], 'WorkflowType' => [ 'type' => 'string', 'enum' => [ 'BUILD', 'TEST', 'DISTRIBUTION', ], ], 'WorkflowVersion' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WorkflowVersionArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'version' => [ 'shape' => 'VersionNumber', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'WorkflowType', ], 'owner' => [ 'shape' => 'NonEmptyString', ], 'dateCreated' => [ 'shape' => 'DateTime', ], ], ], 'WorkflowVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$', ], 'WorkflowVersionArnOrBuildVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/(?:(?:([0-9]+|x)\\.([0-9]+|x)\\.([0-9]+|x))|(?:[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+))$', ], 'WorkflowVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowVersion', ], ], 'WorkflowWildcardVersionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/(?:[0-9]+|x)\\.(?:[0-9]+|x)\\.(?:[0-9]+|x)$', ], ],];
