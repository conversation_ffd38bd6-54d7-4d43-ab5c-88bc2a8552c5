<?php
// This file was auto-generated from sdk-root/src/data/kms/2014-11-01/paginators-1.json
return [ 'pagination' => [ 'DescribeCustomKeyStores' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'CustomKeyStores', ], 'ListAliases' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'Aliases', ], 'ListGrants' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'Grants', ], 'ListKeyPolicies' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'PolicyNames', ], 'ListKeys' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'Keys', ], 'ListResourceTags' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'Tags', ], 'ListRetirableGrants' => [ 'input_token' => 'Marker', 'limit_key' => 'Limit', 'output_token' => 'NextMarker', 'result_key' => 'Grants', ], ],];
