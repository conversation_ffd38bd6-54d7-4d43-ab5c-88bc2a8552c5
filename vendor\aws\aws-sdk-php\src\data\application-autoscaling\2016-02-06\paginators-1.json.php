<?php
// This file was auto-generated from sdk-root/src/data/application-autoscaling/2016-02-06/paginators-1.json
return [ 'pagination' => [ 'DescribeScalableTargets' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ScalableTargets', ], 'DescribeScalingActivities' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ScalingActivities', ], 'DescribeScalingPolicies' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ScalingPolicies', ], 'DescribeScheduledActions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ScheduledActions', ], ],];
