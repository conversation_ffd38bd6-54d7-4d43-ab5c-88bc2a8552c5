<?php
// This file was auto-generated from sdk-root/src/data/config/2014-11-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-11-12', 'endpointPrefix' => 'config', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Config Service', 'serviceFullName' => 'AWS Config', 'serviceId' => 'Config Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'StarlingDoveService', 'uid' => 'config-2014-11-12', ], 'operations' => [ 'BatchGetAggregateResourceConfig' => [ 'name' => 'BatchGetAggregateResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetAggregateResourceConfigRequest', ], 'output' => [ 'shape' => 'BatchGetAggregateResourceConfigResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'BatchGetResourceConfig' => [ 'name' => 'BatchGetResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetResourceConfigRequest', ], 'output' => [ 'shape' => 'BatchGetResourceConfigResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], ], ], 'DeleteAggregationAuthorization' => [ 'name' => 'DeleteAggregationAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAggregationAuthorizationRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteConfigRule' => [ 'name' => 'DeleteConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConfigRuleRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteConfigurationAggregator' => [ 'name' => 'DeleteConfigurationAggregator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConfigurationAggregatorRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'DeleteConfigurationRecorder' => [ 'name' => 'DeleteConfigurationRecorder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConfigurationRecorderRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationRecorderException', ], ], ], 'DeleteConformancePack' => [ 'name' => 'DeleteConformancePack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConformancePackRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConformancePackException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteDeliveryChannel' => [ 'name' => 'DeleteDeliveryChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeliveryChannelRequest', ], 'errors' => [ [ 'shape' => 'NoSuchDeliveryChannelException', ], [ 'shape' => 'LastDeliveryChannelDeleteFailedException', ], ], ], 'DeleteEvaluationResults' => [ 'name' => 'DeleteEvaluationResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEvaluationResultsRequest', ], 'output' => [ 'shape' => 'DeleteEvaluationResultsResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteOrganizationConfigRule' => [ 'name' => 'DeleteOrganizationConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOrganizationConfigRuleRequest', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConfigRuleException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DeleteOrganizationConformancePack' => [ 'name' => 'DeleteOrganizationConformancePack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOrganizationConformancePackRequest', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConformancePackException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DeletePendingAggregationRequest' => [ 'name' => 'DeletePendingAggregationRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePendingAggregationRequestRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteRemediationConfiguration' => [ 'name' => 'DeleteRemediationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRemediationConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteRemediationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'NoSuchRemediationConfigurationException', ], [ 'shape' => 'RemediationInProgressException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteRemediationExceptions' => [ 'name' => 'DeleteRemediationExceptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRemediationExceptionsRequest', ], 'output' => [ 'shape' => 'DeleteRemediationExceptionsResponse', ], 'errors' => [ [ 'shape' => 'NoSuchRemediationExceptionException', ], ], ], 'DeleteResourceConfig' => [ 'name' => 'DeleteResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceConfigRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NoRunningConfigurationRecorderException', ], ], ], 'DeleteRetentionConfiguration' => [ 'name' => 'DeleteRetentionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRetentionConfigurationRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchRetentionConfigurationException', ], ], ], 'DeleteStoredQuery' => [ 'name' => 'DeleteStoredQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStoredQueryRequest', ], 'output' => [ 'shape' => 'DeleteStoredQueryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeliverConfigSnapshot' => [ 'name' => 'DeliverConfigSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeliverConfigSnapshotRequest', ], 'output' => [ 'shape' => 'DeliverConfigSnapshotResponse', ], 'errors' => [ [ 'shape' => 'NoSuchDeliveryChannelException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], [ 'shape' => 'NoRunningConfigurationRecorderException', ], ], ], 'DescribeAggregateComplianceByConfigRules' => [ 'name' => 'DescribeAggregateComplianceByConfigRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAggregateComplianceByConfigRulesRequest', ], 'output' => [ 'shape' => 'DescribeAggregateComplianceByConfigRulesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'DescribeAggregateComplianceByConformancePacks' => [ 'name' => 'DescribeAggregateComplianceByConformancePacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAggregateComplianceByConformancePacksRequest', ], 'output' => [ 'shape' => 'DescribeAggregateComplianceByConformancePacksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'DescribeAggregationAuthorizations' => [ 'name' => 'DescribeAggregationAuthorizations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAggregationAuthorizationsRequest', ], 'output' => [ 'shape' => 'DescribeAggregationAuthorizationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], ], ], 'DescribeComplianceByConfigRule' => [ 'name' => 'DescribeComplianceByConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeComplianceByConfigRuleRequest', ], 'output' => [ 'shape' => 'DescribeComplianceByConfigRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'DescribeComplianceByResource' => [ 'name' => 'DescribeComplianceByResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeComplianceByResourceRequest', ], 'output' => [ 'shape' => 'DescribeComplianceByResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'DescribeConfigRuleEvaluationStatus' => [ 'name' => 'DescribeConfigRuleEvaluationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigRuleEvaluationStatusRequest', ], 'output' => [ 'shape' => 'DescribeConfigRuleEvaluationStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'DescribeConfigRules' => [ 'name' => 'DescribeConfigRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigRulesRequest', ], 'output' => [ 'shape' => 'DescribeConfigRulesResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeConfigurationAggregatorSourcesStatus' => [ 'name' => 'DescribeConfigurationAggregatorSourcesStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigurationAggregatorSourcesStatusRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationAggregatorSourcesStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], ], ], 'DescribeConfigurationAggregators' => [ 'name' => 'DescribeConfigurationAggregators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigurationAggregatorsRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationAggregatorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], ], ], 'DescribeConfigurationRecorderStatus' => [ 'name' => 'DescribeConfigurationRecorderStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigurationRecorderStatusRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationRecorderStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationRecorderException', ], ], ], 'DescribeConfigurationRecorders' => [ 'name' => 'DescribeConfigurationRecorders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigurationRecordersRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationRecordersResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationRecorderException', ], ], ], 'DescribeConformancePackCompliance' => [ 'name' => 'DescribeConformancePackCompliance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConformancePackComplianceRequest', ], 'output' => [ 'shape' => 'DescribeConformancePackComplianceResponse', ], 'errors' => [ [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchConfigRuleInConformancePackException', ], [ 'shape' => 'NoSuchConformancePackException', ], ], ], 'DescribeConformancePackStatus' => [ 'name' => 'DescribeConformancePackStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConformancePackStatusRequest', ], 'output' => [ 'shape' => 'DescribeConformancePackStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeConformancePacks' => [ 'name' => 'DescribeConformancePacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConformancePacksRequest', ], 'output' => [ 'shape' => 'DescribeConformancePacksResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConformancePackException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeDeliveryChannelStatus' => [ 'name' => 'DescribeDeliveryChannelStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliveryChannelStatusRequest', ], 'output' => [ 'shape' => 'DescribeDeliveryChannelStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchDeliveryChannelException', ], ], ], 'DescribeDeliveryChannels' => [ 'name' => 'DescribeDeliveryChannels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeliveryChannelsRequest', ], 'output' => [ 'shape' => 'DescribeDeliveryChannelsResponse', ], 'errors' => [ [ 'shape' => 'NoSuchDeliveryChannelException', ], ], ], 'DescribeOrganizationConfigRuleStatuses' => [ 'name' => 'DescribeOrganizationConfigRuleStatuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationConfigRuleStatusesRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigRuleStatusesResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConfigRuleException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DescribeOrganizationConfigRules' => [ 'name' => 'DescribeOrganizationConfigRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationConfigRulesRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigRulesResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConfigRuleException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DescribeOrganizationConformancePackStatuses' => [ 'name' => 'DescribeOrganizationConformancePackStatuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationConformancePackStatusesRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConformancePackStatusesResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConformancePackException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DescribeOrganizationConformancePacks' => [ 'name' => 'DescribeOrganizationConformancePacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationConformancePacksRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConformancePacksResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConformancePackException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'DescribePendingAggregationRequests' => [ 'name' => 'DescribePendingAggregationRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePendingAggregationRequestsRequest', ], 'output' => [ 'shape' => 'DescribePendingAggregationRequestsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidLimitException', ], ], ], 'DescribeRemediationConfigurations' => [ 'name' => 'DescribeRemediationConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRemediationConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeRemediationConfigurationsResponse', ], ], 'DescribeRemediationExceptions' => [ 'name' => 'DescribeRemediationExceptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRemediationExceptionsRequest', ], 'output' => [ 'shape' => 'DescribeRemediationExceptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeRemediationExecutionStatus' => [ 'name' => 'DescribeRemediationExecutionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRemediationExecutionStatusRequest', ], 'output' => [ 'shape' => 'DescribeRemediationExecutionStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchRemediationConfigurationException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeRetentionConfigurations' => [ 'name' => 'DescribeRetentionConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRetentionConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeRetentionConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'NoSuchRetentionConfigurationException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetAggregateComplianceDetailsByConfigRule' => [ 'name' => 'GetAggregateComplianceDetailsByConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAggregateComplianceDetailsByConfigRuleRequest', ], 'output' => [ 'shape' => 'GetAggregateComplianceDetailsByConfigRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'GetAggregateConfigRuleComplianceSummary' => [ 'name' => 'GetAggregateConfigRuleComplianceSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAggregateConfigRuleComplianceSummaryRequest', ], 'output' => [ 'shape' => 'GetAggregateConfigRuleComplianceSummaryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'GetAggregateConformancePackComplianceSummary' => [ 'name' => 'GetAggregateConformancePackComplianceSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAggregateConformancePackComplianceSummaryRequest', ], 'output' => [ 'shape' => 'GetAggregateConformancePackComplianceSummaryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'GetAggregateDiscoveredResourceCounts' => [ 'name' => 'GetAggregateDiscoveredResourceCounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAggregateDiscoveredResourceCountsRequest', ], 'output' => [ 'shape' => 'GetAggregateDiscoveredResourceCountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'GetAggregateResourceConfig' => [ 'name' => 'GetAggregateResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAggregateResourceConfigRequest', ], 'output' => [ 'shape' => 'GetAggregateResourceConfigResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], [ 'shape' => 'OversizedConfigurationItemException', ], [ 'shape' => 'ResourceNotDiscoveredException', ], ], ], 'GetComplianceDetailsByConfigRule' => [ 'name' => 'GetComplianceDetailsByConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComplianceDetailsByConfigRuleRequest', ], 'output' => [ 'shape' => 'GetComplianceDetailsByConfigRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigRuleException', ], ], ], 'GetComplianceDetailsByResource' => [ 'name' => 'GetComplianceDetailsByResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComplianceDetailsByResourceRequest', ], 'output' => [ 'shape' => 'GetComplianceDetailsByResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetComplianceSummaryByConfigRule' => [ 'name' => 'GetComplianceSummaryByConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'GetComplianceSummaryByConfigRuleResponse', ], ], 'GetComplianceSummaryByResourceType' => [ 'name' => 'GetComplianceSummaryByResourceType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComplianceSummaryByResourceTypeRequest', ], 'output' => [ 'shape' => 'GetComplianceSummaryByResourceTypeResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetConformancePackComplianceDetails' => [ 'name' => 'GetConformancePackComplianceDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConformancePackComplianceDetailsRequest', ], 'output' => [ 'shape' => 'GetConformancePackComplianceDetailsResponse', ], 'errors' => [ [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConformancePackException', ], [ 'shape' => 'NoSuchConfigRuleInConformancePackException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'GetConformancePackComplianceSummary' => [ 'name' => 'GetConformancePackComplianceSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConformancePackComplianceSummaryRequest', ], 'output' => [ 'shape' => 'GetConformancePackComplianceSummaryResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConformancePackException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetCustomRulePolicy' => [ 'name' => 'GetCustomRulePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCustomRulePolicyRequest', ], 'output' => [ 'shape' => 'GetCustomRulePolicyResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], ], ], 'GetDiscoveredResourceCounts' => [ 'name' => 'GetDiscoveredResourceCounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiscoveredResourceCountsRequest', ], 'output' => [ 'shape' => 'GetDiscoveredResourceCountsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetOrganizationConfigRuleDetailedStatus' => [ 'name' => 'GetOrganizationConfigRuleDetailedStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOrganizationConfigRuleDetailedStatusRequest', ], 'output' => [ 'shape' => 'GetOrganizationConfigRuleDetailedStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConfigRuleException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'GetOrganizationConformancePackDetailedStatus' => [ 'name' => 'GetOrganizationConformancePackDetailedStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOrganizationConformancePackDetailedStatusRequest', ], 'output' => [ 'shape' => 'GetOrganizationConformancePackDetailedStatusResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConformancePackException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'GetOrganizationCustomRulePolicy' => [ 'name' => 'GetOrganizationCustomRulePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOrganizationCustomRulePolicyRequest', ], 'output' => [ 'shape' => 'GetOrganizationCustomRulePolicyResponse', ], 'errors' => [ [ 'shape' => 'NoSuchOrganizationConfigRuleException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], ], ], 'GetResourceConfigHistory' => [ 'name' => 'GetResourceConfigHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceConfigHistoryRequest', ], 'output' => [ 'shape' => 'GetResourceConfigHistoryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidTimeRangeException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], [ 'shape' => 'ResourceNotDiscoveredException', ], ], ], 'GetResourceEvaluationSummary' => [ 'name' => 'GetResourceEvaluationSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceEvaluationSummaryRequest', ], 'output' => [ 'shape' => 'GetResourceEvaluationSummaryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetStoredQuery' => [ 'name' => 'GetStoredQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStoredQueryRequest', ], 'output' => [ 'shape' => 'GetStoredQueryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAggregateDiscoveredResources' => [ 'name' => 'ListAggregateDiscoveredResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAggregateDiscoveredResourcesRequest', ], 'output' => [ 'shape' => 'ListAggregateDiscoveredResourcesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], ], ], 'ListConformancePackComplianceScores' => [ 'name' => 'ListConformancePackComplianceScores', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListConformancePackComplianceScoresRequest', ], 'output' => [ 'shape' => 'ListConformancePackComplianceScoresResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListDiscoveredResources' => [ 'name' => 'ListDiscoveredResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDiscoveredResourcesRequest', ], 'output' => [ 'shape' => 'ListDiscoveredResourcesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], ], ], 'ListResourceEvaluations' => [ 'name' => 'ListResourceEvaluations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceEvaluationsRequest', ], 'output' => [ 'shape' => 'ListResourceEvaluationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidTimeRangeException', ], ], ], 'ListStoredQueries' => [ 'name' => 'ListStoredQueries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStoredQueriesRequest', ], 'output' => [ 'shape' => 'ListStoredQueriesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'PutAggregationAuthorization' => [ 'name' => 'PutAggregationAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAggregationAuthorizationRequest', ], 'output' => [ 'shape' => 'PutAggregationAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], ], ], 'PutConfigRule' => [ 'name' => 'PutConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutConfigRuleRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MaxNumberOfConfigRulesExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], ], ], 'PutConfigurationAggregator' => [ 'name' => 'PutConfigurationAggregator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutConfigurationAggregatorRequest', ], 'output' => [ 'shape' => 'PutConfigurationAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], [ 'shape' => 'NoAvailableOrganizationException', ], [ 'shape' => 'OrganizationAllFeaturesNotEnabledException', ], ], ], 'PutConfigurationRecorder' => [ 'name' => 'PutConfigurationRecorder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutConfigurationRecorderRequest', ], 'errors' => [ [ 'shape' => 'MaxNumberOfConfigurationRecordersExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidConfigurationRecorderNameException', ], [ 'shape' => 'InvalidRoleException', ], [ 'shape' => 'InvalidRecordingGroupException', ], ], ], 'PutConformancePack' => [ 'name' => 'PutConformancePack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutConformancePackRequest', ], 'output' => [ 'shape' => 'PutConformancePackResponse', ], 'errors' => [ [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'ConformancePackTemplateValidationException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MaxNumberOfConformancePacksExceededException', ], ], ], 'PutDeliveryChannel' => [ 'name' => 'PutDeliveryChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDeliveryChannelRequest', ], 'errors' => [ [ 'shape' => 'MaxNumberOfDeliveryChannelsExceededException', ], [ 'shape' => 'NoAvailableConfigurationRecorderException', ], [ 'shape' => 'InvalidDeliveryChannelNameException', ], [ 'shape' => 'NoSuchBucketException', ], [ 'shape' => 'InvalidS3KeyPrefixException', ], [ 'shape' => 'InvalidS3KmsKeyArnException', ], [ 'shape' => 'InvalidSNSTopicARNException', ], [ 'shape' => 'InsufficientDeliveryPolicyException', ], ], ], 'PutEvaluations' => [ 'name' => 'PutEvaluations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEvaluationsRequest', ], 'output' => [ 'shape' => 'PutEvaluationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidResultTokenException', ], [ 'shape' => 'NoSuchConfigRuleException', ], ], ], 'PutExternalEvaluation' => [ 'name' => 'PutExternalEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutExternalEvaluationRequest', ], 'output' => [ 'shape' => 'PutExternalEvaluationResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'PutOrganizationConfigRule' => [ 'name' => 'PutOrganizationConfigRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutOrganizationConfigRuleRequest', ], 'output' => [ 'shape' => 'PutOrganizationConfigRuleResponse', ], 'errors' => [ [ 'shape' => 'MaxNumberOfOrganizationConfigRulesExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], [ 'shape' => 'NoAvailableOrganizationException', ], [ 'shape' => 'OrganizationAllFeaturesNotEnabledException', ], [ 'shape' => 'InsufficientPermissionsException', ], ], ], 'PutOrganizationConformancePack' => [ 'name' => 'PutOrganizationConformancePack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutOrganizationConformancePackRequest', ], 'output' => [ 'shape' => 'PutOrganizationConformancePackResponse', ], 'errors' => [ [ 'shape' => 'MaxNumberOfOrganizationConformancePacksExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'OrganizationAccessDeniedException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'OrganizationConformancePackTemplateValidationException', ], [ 'shape' => 'OrganizationAllFeaturesNotEnabledException', ], [ 'shape' => 'NoAvailableOrganizationException', ], ], ], 'PutRemediationConfigurations' => [ 'name' => 'PutRemediationConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRemediationConfigurationsRequest', ], 'output' => [ 'shape' => 'PutRemediationConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'PutRemediationExceptions' => [ 'name' => 'PutRemediationExceptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRemediationExceptionsRequest', ], 'output' => [ 'shape' => 'PutRemediationExceptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InsufficientPermissionsException', ], ], ], 'PutResourceConfig' => [ 'name' => 'PutResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourceConfigRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'NoRunningConfigurationRecorderException', ], [ 'shape' => 'MaxActiveResourcesExceededException', ], ], ], 'PutRetentionConfiguration' => [ 'name' => 'PutRetentionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRetentionConfigurationRequest', ], 'output' => [ 'shape' => 'PutRetentionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MaxNumberOfRetentionConfigurationsExceededException', ], ], ], 'PutStoredQuery' => [ 'name' => 'PutStoredQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutStoredQueryRequest', ], 'output' => [ 'shape' => 'PutStoredQueryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceConcurrentModificationException', ], ], ], 'SelectAggregateResourceConfig' => [ 'name' => 'SelectAggregateResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SelectAggregateResourceConfigRequest', ], 'output' => [ 'shape' => 'SelectAggregateResourceConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidExpressionException', ], [ 'shape' => 'NoSuchConfigurationAggregatorException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'SelectResourceConfig' => [ 'name' => 'SelectResourceConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SelectResourceConfigRequest', ], 'output' => [ 'shape' => 'SelectResourceConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidExpressionException', ], [ 'shape' => 'InvalidLimitException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'StartConfigRulesEvaluation' => [ 'name' => 'StartConfigRulesEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartConfigRulesEvaluationRequest', ], 'output' => [ 'shape' => 'StartConfigRulesEvaluationResponse', ], 'errors' => [ [ 'shape' => 'NoSuchConfigRuleException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'StartConfigurationRecorder' => [ 'name' => 'StartConfigurationRecorder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartConfigurationRecorderRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationRecorderException', ], [ 'shape' => 'NoAvailableDeliveryChannelException', ], ], ], 'StartRemediationExecution' => [ 'name' => 'StartRemediationExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartRemediationExecutionRequest', ], 'output' => [ 'shape' => 'StartRemediationExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'NoSuchRemediationConfigurationException', ], ], ], 'StartResourceEvaluation' => [ 'name' => 'StartResourceEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartResourceEvaluationRequest', ], 'output' => [ 'shape' => 'StartResourceEvaluationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'IdempotentParameterMismatch', ], ], ], 'StopConfigurationRecorder' => [ 'name' => 'StopConfigurationRecorder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopConfigurationRecorderRequest', ], 'errors' => [ [ 'shape' => 'NoSuchConfigurationRecorderException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', ], 'AccountAggregationSource' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountAggregationSourceAccountList', ], 'AllAwsRegions' => [ 'shape' => 'Boolean', ], 'AwsRegions' => [ 'shape' => 'AggregatorRegionList', ], ], ], 'AccountAggregationSourceAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'min' => 1, ], 'AccountAggregationSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAggregationSource', ], 'max' => 1, 'min' => 0, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AggregateComplianceByConfigRule' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'Compliance' => [ 'shape' => 'Compliance', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'AggregateComplianceByConfigRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateComplianceByConfigRule', ], ], 'AggregateComplianceByConformancePack' => [ 'type' => 'structure', 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'Compliance' => [ 'shape' => 'AggregateConformancePackCompliance', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'AggregateComplianceByConformancePackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateComplianceByConformancePack', ], ], 'AggregateComplianceCount' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'StringWithCharLimit256', ], 'ComplianceSummary' => [ 'shape' => 'ComplianceSummary', ], ], ], 'AggregateComplianceCountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateComplianceCount', ], ], 'AggregateConformancePackCompliance' => [ 'type' => 'structure', 'members' => [ 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], 'CompliantRuleCount' => [ 'shape' => 'Integer', ], 'NonCompliantRuleCount' => [ 'shape' => 'Integer', ], 'TotalRuleCount' => [ 'shape' => 'Integer', ], ], ], 'AggregateConformancePackComplianceCount' => [ 'type' => 'structure', 'members' => [ 'CompliantConformancePackCount' => [ 'shape' => 'Integer', ], 'NonCompliantConformancePackCount' => [ 'shape' => 'Integer', ], ], ], 'AggregateConformancePackComplianceFilters' => [ 'type' => 'structure', 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'AggregateConformancePackComplianceSummary' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummary' => [ 'shape' => 'AggregateConformancePackComplianceCount', ], 'GroupName' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'AggregateConformancePackComplianceSummaryFilters' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'AggregateConformancePackComplianceSummaryGroupKey' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'AWS_REGION', ], ], 'AggregateConformancePackComplianceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateConformancePackComplianceSummary', ], ], 'AggregateEvaluationResult' => [ 'type' => 'structure', 'members' => [ 'EvaluationResultIdentifier' => [ 'shape' => 'EvaluationResultIdentifier', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'ResultRecordedTime' => [ 'shape' => 'Date', ], 'ConfigRuleInvokedTime' => [ 'shape' => 'Date', ], 'Annotation' => [ 'shape' => 'StringWithCharLimit256', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'AggregateEvaluationResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateEvaluationResult', ], ], 'AggregateResourceIdentifier' => [ 'type' => 'structure', 'required' => [ 'SourceAccountId', 'SourceRegion', 'ResourceId', 'ResourceType', ], 'members' => [ 'SourceAccountId' => [ 'shape' => 'AccountId', ], 'SourceRegion' => [ 'shape' => 'AwsRegion', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceName' => [ 'shape' => 'ResourceName', ], ], ], 'AggregatedSourceStatus' => [ 'type' => 'structure', 'members' => [ 'SourceId' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'AggregatedSourceType', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], 'LastUpdateStatus' => [ 'shape' => 'AggregatedSourceStatusType', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], 'LastErrorCode' => [ 'shape' => 'String', ], 'LastErrorMessage' => [ 'shape' => 'String', ], ], ], 'AggregatedSourceStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedSourceStatus', ], ], 'AggregatedSourceStatusType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', 'OUTDATED', ], ], 'AggregatedSourceStatusTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedSourceStatusType', ], 'min' => 1, ], 'AggregatedSourceType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATION', ], ], 'AggregationAuthorization' => [ 'type' => 'structure', 'members' => [ 'AggregationAuthorizationArn' => [ 'shape' => 'String', ], 'AuthorizedAccountId' => [ 'shape' => 'AccountId', ], 'AuthorizedAwsRegion' => [ 'shape' => 'AwsRegion', ], 'CreationTime' => [ 'shape' => 'Date', ], ], ], 'AggregationAuthorizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationAuthorization', ], ], 'AggregatorRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'AllSupported' => [ 'type' => 'boolean', ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Annotation' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'AutoRemediationAttemptSeconds' => [ 'type' => 'long', 'box' => true, 'max' => 2678000, 'min' => 1, ], 'AutoRemediationAttempts' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AwsRegion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'BaseConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'Version', ], 'accountId' => [ 'shape' => 'AccountId', ], 'configurationItemCaptureTime' => [ 'shape' => 'ConfigurationItemCaptureTime', ], 'configurationItemStatus' => [ 'shape' => 'ConfigurationItemStatus', ], 'configurationStateId' => [ 'shape' => 'ConfigurationStateId', ], 'arn' => [ 'shape' => 'ARN', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'resourceCreationTime' => [ 'shape' => 'ResourceCreationTime', ], 'configuration' => [ 'shape' => 'Configuration', ], 'supplementaryConfiguration' => [ 'shape' => 'SupplementaryConfiguration', ], 'recordingFrequency' => [ 'shape' => 'RecordingFrequency', ], 'configurationItemDeliveryTime' => [ 'shape' => 'ConfigurationItemDeliveryTime', ], ], ], 'BaseConfigurationItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BaseConfigurationItem', ], ], 'BaseResourceId' => [ 'type' => 'string', 'max' => 768, 'min' => 1, ], 'BatchGetAggregateResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', 'ResourceIdentifiers', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'ResourceIdentifiers' => [ 'shape' => 'ResourceIdentifiersList', ], ], ], 'BatchGetAggregateResourceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'BaseConfigurationItems' => [ 'shape' => 'BaseConfigurationItems', ], 'UnprocessedResourceIdentifiers' => [ 'shape' => 'UnprocessedResourceIdentifierList', ], ], ], 'BatchGetResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'resourceKeys', ], 'members' => [ 'resourceKeys' => [ 'shape' => 'ResourceKeys', ], ], ], 'BatchGetResourceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'baseConfigurationItems' => [ 'shape' => 'BaseConfigurationItems', ], 'unprocessedResourceKeys' => [ 'shape' => 'ResourceKeys', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ChannelName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ChronologicalOrder' => [ 'type' => 'string', 'enum' => [ 'Reverse', 'Forward', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 64, ], 'Compliance' => [ 'type' => 'structure', 'members' => [ 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'ComplianceContributorCount' => [ 'shape' => 'ComplianceContributorCount', ], ], ], 'ComplianceByConfigRule' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'StringWithCharLimit64', ], 'Compliance' => [ 'shape' => 'Compliance', ], ], ], 'ComplianceByConfigRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceByConfigRule', ], ], 'ComplianceByResource' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'BaseResourceId', ], 'Compliance' => [ 'shape' => 'Compliance', ], ], ], 'ComplianceByResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceByResource', ], ], 'ComplianceContributorCount' => [ 'type' => 'structure', 'members' => [ 'CappedCount' => [ 'shape' => 'Integer', ], 'CapExceeded' => [ 'shape' => 'Boolean', ], ], ], 'ComplianceResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit256', ], 'max' => 100, 'min' => 0, ], 'ComplianceScore' => [ 'type' => 'string', ], 'ComplianceSummariesByResourceType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceSummaryByResourceType', ], ], 'ComplianceSummary' => [ 'type' => 'structure', 'members' => [ 'CompliantResourceCount' => [ 'shape' => 'ComplianceContributorCount', ], 'NonCompliantResourceCount' => [ 'shape' => 'ComplianceContributorCount', ], 'ComplianceSummaryTimestamp' => [ 'shape' => 'Date', ], ], ], 'ComplianceSummaryByResourceType' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ComplianceSummary' => [ 'shape' => 'ComplianceSummary', ], ], ], 'ComplianceType' => [ 'type' => 'string', 'enum' => [ 'COMPLIANT', 'NON_COMPLIANT', 'NOT_APPLICABLE', 'INSUFFICIENT_DATA', ], ], 'ComplianceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceType', ], 'max' => 3, 'min' => 0, ], 'ConfigExportDeliveryInfo' => [ 'type' => 'structure', 'members' => [ 'lastStatus' => [ 'shape' => 'DeliveryStatus', ], 'lastErrorCode' => [ 'shape' => 'String', ], 'lastErrorMessage' => [ 'shape' => 'String', ], 'lastAttemptTime' => [ 'shape' => 'Date', ], 'lastSuccessfulTime' => [ 'shape' => 'Date', ], 'nextDeliveryTime' => [ 'shape' => 'Date', ], ], ], 'ConfigRule' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ConfigRuleArn' => [ 'shape' => 'StringWithCharLimit256', ], 'ConfigRuleId' => [ 'shape' => 'StringWithCharLimit64', ], 'Description' => [ 'shape' => 'EmptiableStringWithCharLimit256', ], 'Scope' => [ 'shape' => 'Scope', ], 'Source' => [ 'shape' => 'Source', ], 'InputParameters' => [ 'shape' => 'StringWithCharLimit1024', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], 'ConfigRuleState' => [ 'shape' => 'ConfigRuleState', ], 'CreatedBy' => [ 'shape' => 'StringWithCharLimit256', ], 'EvaluationModes' => [ 'shape' => 'EvaluationModes', ], ], ], 'ConfigRuleComplianceFilters' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'ConfigRuleComplianceSummaryFilters' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'ConfigRuleComplianceSummaryGroupKey' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'AWS_REGION', ], ], 'ConfigRuleEvaluationStatus' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ConfigRuleArn' => [ 'shape' => 'String', ], 'ConfigRuleId' => [ 'shape' => 'String', ], 'LastSuccessfulInvocationTime' => [ 'shape' => 'Date', ], 'LastFailedInvocationTime' => [ 'shape' => 'Date', ], 'LastSuccessfulEvaluationTime' => [ 'shape' => 'Date', ], 'LastFailedEvaluationTime' => [ 'shape' => 'Date', ], 'FirstActivatedTime' => [ 'shape' => 'Date', ], 'LastDeactivatedTime' => [ 'shape' => 'Date', ], 'LastErrorCode' => [ 'shape' => 'String', ], 'LastErrorMessage' => [ 'shape' => 'String', ], 'FirstEvaluationStarted' => [ 'shape' => 'Boolean', ], 'LastDebugLogDeliveryStatus' => [ 'shape' => 'String', ], 'LastDebugLogDeliveryStatusReason' => [ 'shape' => 'String', ], 'LastDebugLogDeliveryTime' => [ 'shape' => 'Date', ], ], ], 'ConfigRuleEvaluationStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRuleEvaluationStatus', ], ], 'ConfigRuleName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ConfigRuleNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRuleName', ], 'max' => 25, 'min' => 0, ], 'ConfigRuleState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', 'DELETING_RESULTS', 'EVALUATING', ], ], 'ConfigRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRule', ], ], 'ConfigSnapshotDeliveryProperties' => [ 'type' => 'structure', 'members' => [ 'deliveryFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], ], ], 'ConfigStreamDeliveryInfo' => [ 'type' => 'structure', 'members' => [ 'lastStatus' => [ 'shape' => 'DeliveryStatus', ], 'lastErrorCode' => [ 'shape' => 'String', ], 'lastErrorMessage' => [ 'shape' => 'String', ], 'lastStatusChangeTime' => [ 'shape' => 'Date', ], ], ], 'Configuration' => [ 'type' => 'string', ], 'ConfigurationAggregator' => [ 'type' => 'structure', 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'ConfigurationAggregatorArn' => [ 'shape' => 'ConfigurationAggregatorArn', ], 'AccountAggregationSources' => [ 'shape' => 'AccountAggregationSourceList', ], 'OrganizationAggregationSource' => [ 'shape' => 'OrganizationAggregationSource', ], 'CreationTime' => [ 'shape' => 'Date', ], 'LastUpdatedTime' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'ConfigurationAggregatorArn' => [ 'type' => 'string', 'pattern' => 'arn:aws[a-z\\-]*:config:[a-z\\-\\d]+:\\d+:config-aggregator/config-aggregator-[a-z\\d]+', ], 'ConfigurationAggregatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationAggregator', ], ], 'ConfigurationAggregatorName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w\\-]+', ], 'ConfigurationAggregatorNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationAggregatorName', ], 'max' => 10, 'min' => 0, ], 'ConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'Version', ], 'accountId' => [ 'shape' => 'AccountId', ], 'configurationItemCaptureTime' => [ 'shape' => 'ConfigurationItemCaptureTime', ], 'configurationItemStatus' => [ 'shape' => 'ConfigurationItemStatus', ], 'configurationStateId' => [ 'shape' => 'ConfigurationStateId', ], 'configurationItemMD5Hash' => [ 'shape' => 'ConfigurationItemMD5Hash', ], 'arn' => [ 'shape' => 'ARN', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'resourceCreationTime' => [ 'shape' => 'ResourceCreationTime', ], 'tags' => [ 'shape' => 'Tags', ], 'relatedEvents' => [ 'shape' => 'RelatedEventList', ], 'relationships' => [ 'shape' => 'RelationshipList', ], 'configuration' => [ 'shape' => 'Configuration', ], 'supplementaryConfiguration' => [ 'shape' => 'SupplementaryConfiguration', ], 'recordingFrequency' => [ 'shape' => 'RecordingFrequency', ], 'configurationItemDeliveryTime' => [ 'shape' => 'ConfigurationItemDeliveryTime', ], ], ], 'ConfigurationItemCaptureTime' => [ 'type' => 'timestamp', ], 'ConfigurationItemDeliveryTime' => [ 'type' => 'timestamp', ], 'ConfigurationItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationItem', ], ], 'ConfigurationItemMD5Hash' => [ 'type' => 'string', ], 'ConfigurationItemStatus' => [ 'type' => 'string', 'enum' => [ 'OK', 'ResourceDiscovered', 'ResourceNotRecorded', 'ResourceDeleted', 'ResourceDeletedNotRecorded', ], ], 'ConfigurationRecorder' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RecorderName', ], 'roleARN' => [ 'shape' => 'String', ], 'recordingGroup' => [ 'shape' => 'RecordingGroup', ], 'recordingMode' => [ 'shape' => 'RecordingMode', ], ], ], 'ConfigurationRecorderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationRecorder', ], ], 'ConfigurationRecorderNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecorderName', ], ], 'ConfigurationRecorderStatus' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'lastStartTime' => [ 'shape' => 'Date', ], 'lastStopTime' => [ 'shape' => 'Date', ], 'recording' => [ 'shape' => 'Boolean', ], 'lastStatus' => [ 'shape' => 'RecorderStatus', ], 'lastErrorCode' => [ 'shape' => 'String', ], 'lastErrorMessage' => [ 'shape' => 'String', ], 'lastStatusChangeTime' => [ 'shape' => 'Date', ], ], ], 'ConfigurationRecorderStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationRecorderStatus', ], ], 'ConfigurationStateId' => [ 'type' => 'string', ], 'ConformancePackArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ConformancePackComplianceFilters' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConformancePackConfigRuleNames', ], 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], ], ], 'ConformancePackComplianceResourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit256', ], 'max' => 5, 'min' => 0, ], 'ConformancePackComplianceScore' => [ 'type' => 'structure', 'members' => [ 'Score' => [ 'shape' => 'ComplianceScore', ], 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], ], ], 'ConformancePackComplianceScores' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackComplianceScore', ], ], 'ConformancePackComplianceScoresFilters' => [ 'type' => 'structure', 'required' => [ 'ConformancePackNames', ], 'members' => [ 'ConformancePackNames' => [ 'shape' => 'ConformancePackNameFilter', ], ], ], 'ConformancePackComplianceSummary' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', 'ConformancePackComplianceStatus', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ConformancePackComplianceStatus' => [ 'shape' => 'ConformancePackComplianceType', ], ], ], 'ConformancePackComplianceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackComplianceSummary', ], 'max' => 5, 'min' => 1, ], 'ConformancePackComplianceType' => [ 'type' => 'string', 'enum' => [ 'COMPLIANT', 'NON_COMPLIANT', 'INSUFFICIENT_DATA', ], ], 'ConformancePackConfigRuleNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit64', ], 'max' => 10, 'min' => 0, ], 'ConformancePackDetail' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', 'ConformancePackArn', 'ConformancePackId', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ConformancePackArn' => [ 'shape' => 'ConformancePackArn', ], 'ConformancePackId' => [ 'shape' => 'ConformancePackId', ], 'DeliveryS3Bucket' => [ 'shape' => 'DeliveryS3Bucket', ], 'DeliveryS3KeyPrefix' => [ 'shape' => 'DeliveryS3KeyPrefix', ], 'ConformancePackInputParameters' => [ 'shape' => 'ConformancePackInputParameters', ], 'LastUpdateRequestedTime' => [ 'shape' => 'Date', ], 'CreatedBy' => [ 'shape' => 'StringWithCharLimit256', ], 'TemplateSSMDocumentDetails' => [ 'shape' => 'TemplateSSMDocumentDetails', ], ], ], 'ConformancePackDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackDetail', ], 'max' => 25, 'min' => 0, ], 'ConformancePackEvaluationFilters' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConformancePackConfigRuleNames', ], 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceIds' => [ 'shape' => 'ConformancePackComplianceResourceIds', ], ], ], 'ConformancePackEvaluationResult' => [ 'type' => 'structure', 'required' => [ 'ComplianceType', 'EvaluationResultIdentifier', 'ConfigRuleInvokedTime', 'ResultRecordedTime', ], 'members' => [ 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], 'EvaluationResultIdentifier' => [ 'shape' => 'EvaluationResultIdentifier', ], 'ConfigRuleInvokedTime' => [ 'shape' => 'Date', ], 'ResultRecordedTime' => [ 'shape' => 'Date', ], 'Annotation' => [ 'shape' => 'Annotation', ], ], ], 'ConformancePackId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConformancePackInputParameter' => [ 'type' => 'structure', 'required' => [ 'ParameterName', 'ParameterValue', ], 'members' => [ 'ParameterName' => [ 'shape' => 'ParameterName', ], 'ParameterValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ConformancePackInputParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackInputParameter', ], 'max' => 60, 'min' => 0, ], 'ConformancePackName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*', ], 'ConformancePackNameFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackName', ], 'max' => 25, 'min' => 1, ], 'ConformancePackNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackName', ], 'max' => 25, 'min' => 0, ], 'ConformancePackNamesToSummarizeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackName', ], 'max' => 5, 'min' => 1, ], 'ConformancePackRuleCompliance' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ComplianceType' => [ 'shape' => 'ConformancePackComplianceType', ], 'Controls' => [ 'shape' => 'ControlsList', ], ], ], 'ConformancePackRuleComplianceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackRuleCompliance', ], 'max' => 1000, 'min' => 0, ], 'ConformancePackRuleEvaluationResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackEvaluationResult', ], 'max' => 100, 'min' => 0, ], 'ConformancePackState' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'CREATE_FAILED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'ConformancePackStatusDetail' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', 'ConformancePackId', 'ConformancePackArn', 'ConformancePackState', 'StackArn', 'LastUpdateRequestedTime', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ConformancePackId' => [ 'shape' => 'ConformancePackId', ], 'ConformancePackArn' => [ 'shape' => 'ConformancePackArn', ], 'ConformancePackState' => [ 'shape' => 'ConformancePackState', ], 'StackArn' => [ 'shape' => 'StackArn', ], 'ConformancePackStatusReason' => [ 'shape' => 'ConformancePackStatusReason', ], 'LastUpdateRequestedTime' => [ 'shape' => 'Date', ], 'LastUpdateCompletedTime' => [ 'shape' => 'Date', ], ], ], 'ConformancePackStatusDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConformancePackStatusDetail', ], 'max' => 25, 'min' => 0, ], 'ConformancePackStatusReason' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'ConformancePackTemplateValidationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ControlsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit128', ], 'max' => 20, 'min' => 0, ], 'CosmosPageLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'CustomPolicyDetails' => [ 'type' => 'structure', 'required' => [ 'PolicyRuntime', 'PolicyText', ], 'members' => [ 'PolicyRuntime' => [ 'shape' => 'PolicyRuntime', ], 'PolicyText' => [ 'shape' => 'PolicyText', ], 'EnableDebugLogDelivery' => [ 'shape' => 'Boolean', ], ], ], 'Date' => [ 'type' => 'timestamp', ], 'DebugLogDeliveryAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 1000, 'min' => 0, ], 'DeleteAggregationAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'AuthorizedAccountId', 'AuthorizedAwsRegion', ], 'members' => [ 'AuthorizedAccountId' => [ 'shape' => 'AccountId', ], 'AuthorizedAwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'DeleteConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], ], ], 'DeleteConfigurationAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], ], ], 'DeleteConfigurationRecorderRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationRecorderName', ], 'members' => [ 'ConfigurationRecorderName' => [ 'shape' => 'RecorderName', ], ], ], 'DeleteConformancePackRequest' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], ], ], 'DeleteDeliveryChannelRequest' => [ 'type' => 'structure', 'required' => [ 'DeliveryChannelName', ], 'members' => [ 'DeliveryChannelName' => [ 'shape' => 'ChannelName', ], ], ], 'DeleteEvaluationResultsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'StringWithCharLimit64', ], ], ], 'DeleteEvaluationResultsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOrganizationConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], ], ], 'DeleteOrganizationConformancePackRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConformancePackName', ], 'members' => [ 'OrganizationConformancePackName' => [ 'shape' => 'OrganizationConformancePackName', ], ], ], 'DeletePendingAggregationRequestRequest' => [ 'type' => 'structure', 'required' => [ 'RequesterAccountId', 'RequesterAwsRegion', ], 'members' => [ 'RequesterAccountId' => [ 'shape' => 'AccountId', ], 'RequesterAwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'DeleteRemediationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceType' => [ 'shape' => 'String', ], ], ], 'DeleteRemediationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRemediationExceptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'ResourceKeys', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceKeys' => [ 'shape' => 'RemediationExceptionResourceKeys', ], ], ], 'DeleteRemediationExceptionsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedBatches' => [ 'shape' => 'FailedDeleteRemediationExceptionsBatches', ], ], ], 'DeleteResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceId', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeString', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteRetentionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'RetentionConfigurationName', ], 'members' => [ 'RetentionConfigurationName' => [ 'shape' => 'RetentionConfigurationName', ], ], ], 'DeleteStoredQueryRequest' => [ 'type' => 'structure', 'required' => [ 'QueryName', ], 'members' => [ 'QueryName' => [ 'shape' => 'QueryName', ], ], ], 'DeleteStoredQueryResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliverConfigSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'deliveryChannelName', ], 'members' => [ 'deliveryChannelName' => [ 'shape' => 'ChannelName', ], ], ], 'DeliverConfigSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'configSnapshotId' => [ 'shape' => 'String', ], ], ], 'DeliveryChannel' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ChannelName', ], 's3BucketName' => [ 'shape' => 'String', ], 's3KeyPrefix' => [ 'shape' => 'String', ], 's3KmsKeyArn' => [ 'shape' => 'String', ], 'snsTopicARN' => [ 'shape' => 'String', ], 'configSnapshotDeliveryProperties' => [ 'shape' => 'ConfigSnapshotDeliveryProperties', ], ], ], 'DeliveryChannelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryChannel', ], ], 'DeliveryChannelNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelName', ], ], 'DeliveryChannelStatus' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'configSnapshotDeliveryInfo' => [ 'shape' => 'ConfigExportDeliveryInfo', ], 'configHistoryDeliveryInfo' => [ 'shape' => 'ConfigExportDeliveryInfo', ], 'configStreamDeliveryInfo' => [ 'shape' => 'ConfigStreamDeliveryInfo', ], ], ], 'DeliveryChannelStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryChannelStatus', ], ], 'DeliveryS3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 0, ], 'DeliveryS3KeyPrefix' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'DeliveryStatus' => [ 'type' => 'string', 'enum' => [ 'Success', 'Failure', 'Not_Applicable', ], ], 'DescribeAggregateComplianceByConfigRulesRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Filters' => [ 'shape' => 'ConfigRuleComplianceFilters', ], 'Limit' => [ 'shape' => 'GroupByAPILimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAggregateComplianceByConfigRulesResponse' => [ 'type' => 'structure', 'members' => [ 'AggregateComplianceByConfigRules' => [ 'shape' => 'AggregateComplianceByConfigRuleList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAggregateComplianceByConformancePacksRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Filters' => [ 'shape' => 'AggregateConformancePackComplianceFilters', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAggregateComplianceByConformancePacksResponse' => [ 'type' => 'structure', 'members' => [ 'AggregateComplianceByConformancePacks' => [ 'shape' => 'AggregateComplianceByConformancePackList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAggregationAuthorizationsRequest' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeAggregationAuthorizationsResponse' => [ 'type' => 'structure', 'members' => [ 'AggregationAuthorizations' => [ 'shape' => 'AggregationAuthorizationList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeComplianceByConfigRuleRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConfigRuleNames', ], 'ComplianceTypes' => [ 'shape' => 'ComplianceTypes', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeComplianceByConfigRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ComplianceByConfigRules' => [ 'shape' => 'ComplianceByConfigRules', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeComplianceByResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'BaseResourceId', ], 'ComplianceTypes' => [ 'shape' => 'ComplianceTypes', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeComplianceByResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ComplianceByResources' => [ 'shape' => 'ComplianceByResources', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConfigRuleEvaluationStatusRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConfigRuleNames', ], 'NextToken' => [ 'shape' => 'String', ], 'Limit' => [ 'shape' => 'RuleLimit', ], ], ], 'DescribeConfigRuleEvaluationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigRulesEvaluationStatus' => [ 'shape' => 'ConfigRuleEvaluationStatusList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeConfigRulesFilters' => [ 'type' => 'structure', 'members' => [ 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], ], ], 'DescribeConfigRulesRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConfigRuleNames', ], 'NextToken' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'DescribeConfigRulesFilters', ], ], ], 'DescribeConfigRulesResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigRules' => [ 'shape' => 'ConfigRules', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeConfigurationAggregatorSourcesStatusRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'UpdateStatus' => [ 'shape' => 'AggregatedSourceStatusTypeList', ], 'NextToken' => [ 'shape' => 'String', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeConfigurationAggregatorSourcesStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AggregatedSourceStatusList' => [ 'shape' => 'AggregatedSourceStatusList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeConfigurationAggregatorsRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigurationAggregatorNames' => [ 'shape' => 'ConfigurationAggregatorNameList', ], 'NextToken' => [ 'shape' => 'String', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeConfigurationAggregatorsResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationAggregators' => [ 'shape' => 'ConfigurationAggregatorList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeConfigurationRecorderStatusRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigurationRecorderNames' => [ 'shape' => 'ConfigurationRecorderNameList', ], ], ], 'DescribeConfigurationRecorderStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationRecordersStatus' => [ 'shape' => 'ConfigurationRecorderStatusList', ], ], ], 'DescribeConfigurationRecordersRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigurationRecorderNames' => [ 'shape' => 'ConfigurationRecorderNameList', ], ], ], 'DescribeConfigurationRecordersResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationRecorders' => [ 'shape' => 'ConfigurationRecorderList', ], ], ], 'DescribeConformancePackComplianceLimit' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'DescribeConformancePackComplianceRequest' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'Filters' => [ 'shape' => 'ConformancePackComplianceFilters', ], 'Limit' => [ 'shape' => 'DescribeConformancePackComplianceLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConformancePackComplianceResponse' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', 'ConformancePackRuleComplianceList', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ConformancePackRuleComplianceList' => [ 'shape' => 'ConformancePackRuleComplianceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConformancePackStatusRequest' => [ 'type' => 'structure', 'members' => [ 'ConformancePackNames' => [ 'shape' => 'ConformancePackNamesList', ], 'Limit' => [ 'shape' => 'PageSizeLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConformancePackStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ConformancePackStatusDetails' => [ 'shape' => 'ConformancePackStatusDetailsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConformancePacksRequest' => [ 'type' => 'structure', 'members' => [ 'ConformancePackNames' => [ 'shape' => 'ConformancePackNamesList', ], 'Limit' => [ 'shape' => 'PageSizeLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConformancePacksResponse' => [ 'type' => 'structure', 'members' => [ 'ConformancePackDetails' => [ 'shape' => 'ConformancePackDetailList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDeliveryChannelStatusRequest' => [ 'type' => 'structure', 'members' => [ 'DeliveryChannelNames' => [ 'shape' => 'DeliveryChannelNameList', ], ], ], 'DescribeDeliveryChannelStatusResponse' => [ 'type' => 'structure', 'members' => [ 'DeliveryChannelsStatus' => [ 'shape' => 'DeliveryChannelStatusList', ], ], ], 'DescribeDeliveryChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'DeliveryChannelNames' => [ 'shape' => 'DeliveryChannelNameList', ], ], ], 'DescribeDeliveryChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'DeliveryChannels' => [ 'shape' => 'DeliveryChannelList', ], ], ], 'DescribeOrganizationConfigRuleStatusesRequest' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRuleNames' => [ 'shape' => 'OrganizationConfigRuleNames', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConfigRuleStatusesResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRuleStatuses' => [ 'shape' => 'OrganizationConfigRuleStatuses', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConfigRulesRequest' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRuleNames' => [ 'shape' => 'OrganizationConfigRuleNames', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConfigRulesResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRules' => [ 'shape' => 'OrganizationConfigRules', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConformancePackStatusesRequest' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePackNames' => [ 'shape' => 'OrganizationConformancePackNames', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConformancePackStatusesResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePackStatuses' => [ 'shape' => 'OrganizationConformancePackStatuses', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConformancePacksRequest' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePackNames' => [ 'shape' => 'OrganizationConformancePackNames', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeOrganizationConformancePacksResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePacks' => [ 'shape' => 'OrganizationConformancePacks', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribePendingAggregationRequestsLimit' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'DescribePendingAggregationRequestsRequest' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'DescribePendingAggregationRequestsLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribePendingAggregationRequestsResponse' => [ 'type' => 'structure', 'members' => [ 'PendingAggregationRequests' => [ 'shape' => 'PendingAggregationRequestList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRemediationConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleNames', ], 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ConfigRuleNames', ], ], ], 'DescribeRemediationConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'RemediationConfigurations' => [ 'shape' => 'RemediationConfigurations', ], ], ], 'DescribeRemediationExceptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceKeys' => [ 'shape' => 'RemediationExceptionResourceKeys', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRemediationExceptionsResponse' => [ 'type' => 'structure', 'members' => [ 'RemediationExceptions' => [ 'shape' => 'RemediationExceptions', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRemediationExecutionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceKeys' => [ 'shape' => 'ResourceKeys', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRemediationExecutionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'RemediationExecutionStatuses' => [ 'shape' => 'RemediationExecutionStatuses', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeRetentionConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'RetentionConfigurationNames' => [ 'shape' => 'RetentionConfigurationNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRetentionConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionConfigurations' => [ 'shape' => 'RetentionConfigurationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DiscoveredResourceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateResourceIdentifier', ], ], 'EarlierTime' => [ 'type' => 'timestamp', ], 'EmptiableStringWithCharLimit256' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ErrorMessage' => [ 'type' => 'string', ], 'Evaluation' => [ 'type' => 'structure', 'required' => [ 'ComplianceResourceType', 'ComplianceResourceId', 'ComplianceType', 'OrderingTimestamp', ], 'members' => [ 'ComplianceResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ComplianceResourceId' => [ 'shape' => 'BaseResourceId', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'Annotation' => [ 'shape' => 'StringWithCharLimit256', ], 'OrderingTimestamp' => [ 'shape' => 'OrderingTimestamp', ], ], ], 'EvaluationContext' => [ 'type' => 'structure', 'members' => [ 'EvaluationContextIdentifier' => [ 'shape' => 'EvaluationContextIdentifier', ], ], ], 'EvaluationContextIdentifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'EvaluationMode' => [ 'type' => 'string', 'enum' => [ 'DETECTIVE', 'PROACTIVE', ], ], 'EvaluationModeConfiguration' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'EvaluationMode', ], ], ], 'EvaluationModes' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationModeConfiguration', ], ], 'EvaluationResult' => [ 'type' => 'structure', 'members' => [ 'EvaluationResultIdentifier' => [ 'shape' => 'EvaluationResultIdentifier', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'ResultRecordedTime' => [ 'shape' => 'Date', ], 'ConfigRuleInvokedTime' => [ 'shape' => 'Date', ], 'Annotation' => [ 'shape' => 'StringWithCharLimit256', ], 'ResultToken' => [ 'shape' => 'String', ], ], ], 'EvaluationResultIdentifier' => [ 'type' => 'structure', 'members' => [ 'EvaluationResultQualifier' => [ 'shape' => 'EvaluationResultQualifier', ], 'OrderingTimestamp' => [ 'shape' => 'Date', ], 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], ], ], 'EvaluationResultQualifier' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'BaseResourceId', ], 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], ], ], 'EvaluationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationResult', ], ], 'EvaluationStatus' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ResourceEvaluationStatus', ], 'FailureReason' => [ 'shape' => 'StringWithCharLimit1024', ], ], ], 'EvaluationTimeout' => [ 'type' => 'integer', 'max' => 3600, 'min' => 0, ], 'Evaluations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Evaluation', ], 'max' => 100, 'min' => 0, ], 'EventSource' => [ 'type' => 'string', 'enum' => [ 'aws.config', ], ], 'ExcludedAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 1000, 'min' => 0, ], 'ExclusionByResourceTypes' => [ 'type' => 'structure', 'members' => [ 'resourceTypes' => [ 'shape' => 'ResourceTypeList', ], ], ], 'ExecutionControls' => [ 'type' => 'structure', 'members' => [ 'SsmControls' => [ 'shape' => 'SsmControls', ], ], ], 'Expression' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ExternalEvaluation' => [ 'type' => 'structure', 'required' => [ 'ComplianceResourceType', 'ComplianceResourceId', 'ComplianceType', 'OrderingTimestamp', ], 'members' => [ 'ComplianceResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ComplianceResourceId' => [ 'shape' => 'BaseResourceId', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'Annotation' => [ 'shape' => 'StringWithCharLimit256', ], 'OrderingTimestamp' => [ 'shape' => 'OrderingTimestamp', ], ], ], 'FailedDeleteRemediationExceptionsBatch' => [ 'type' => 'structure', 'members' => [ 'FailureMessage' => [ 'shape' => 'String', ], 'FailedItems' => [ 'shape' => 'RemediationExceptionResourceKeys', ], ], ], 'FailedDeleteRemediationExceptionsBatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedDeleteRemediationExceptionsBatch', ], ], 'FailedRemediationBatch' => [ 'type' => 'structure', 'members' => [ 'FailureMessage' => [ 'shape' => 'String', ], 'FailedItems' => [ 'shape' => 'RemediationConfigurations', ], ], ], 'FailedRemediationBatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedRemediationBatch', ], ], 'FailedRemediationExceptionBatch' => [ 'type' => 'structure', 'members' => [ 'FailureMessage' => [ 'shape' => 'String', ], 'FailedItems' => [ 'shape' => 'RemediationExceptions', ], ], ], 'FailedRemediationExceptionBatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedRemediationExceptionBatch', ], ], 'FieldInfo' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FieldName', ], ], ], 'FieldInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldInfo', ], ], 'FieldName' => [ 'type' => 'string', ], 'GetAggregateComplianceDetailsByConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', 'ConfigRuleName', 'AccountId', 'AwsRegion', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsRegion' => [ 'shape' => 'AwsRegion', ], 'ComplianceType' => [ 'shape' => 'ComplianceType', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateComplianceDetailsByConfigRuleResponse' => [ 'type' => 'structure', 'members' => [ 'AggregateEvaluationResults' => [ 'shape' => 'AggregateEvaluationResultList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateConfigRuleComplianceSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Filters' => [ 'shape' => 'ConfigRuleComplianceSummaryFilters', ], 'GroupByKey' => [ 'shape' => 'ConfigRuleComplianceSummaryGroupKey', ], 'Limit' => [ 'shape' => 'GroupByAPILimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateConfigRuleComplianceSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'GroupByKey' => [ 'shape' => 'StringWithCharLimit256', ], 'AggregateComplianceCounts' => [ 'shape' => 'AggregateComplianceCountList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateConformancePackComplianceSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Filters' => [ 'shape' => 'AggregateConformancePackComplianceSummaryFilters', ], 'GroupByKey' => [ 'shape' => 'AggregateConformancePackComplianceSummaryGroupKey', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateConformancePackComplianceSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'AggregateConformancePackComplianceSummaries' => [ 'shape' => 'AggregateConformancePackComplianceSummaryList', ], 'GroupByKey' => [ 'shape' => 'StringWithCharLimit256', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateDiscoveredResourceCountsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Filters' => [ 'shape' => 'ResourceCountFilters', ], 'GroupByKey' => [ 'shape' => 'ResourceCountGroupKey', ], 'Limit' => [ 'shape' => 'GroupByAPILimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateDiscoveredResourceCountsResponse' => [ 'type' => 'structure', 'required' => [ 'TotalDiscoveredResources', ], 'members' => [ 'TotalDiscoveredResources' => [ 'shape' => 'Long', ], 'GroupByKey' => [ 'shape' => 'StringWithCharLimit256', ], 'GroupedResourceCounts' => [ 'shape' => 'GroupedResourceCountList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAggregateResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', 'ResourceIdentifier', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'ResourceIdentifier' => [ 'shape' => 'AggregateResourceIdentifier', ], ], ], 'GetAggregateResourceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationItem' => [ 'shape' => 'ConfigurationItem', ], ], ], 'GetComplianceDetailsByConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'StringWithCharLimit64', ], 'ComplianceTypes' => [ 'shape' => 'ComplianceTypes', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetComplianceDetailsByConfigRuleResponse' => [ 'type' => 'structure', 'members' => [ 'EvaluationResults' => [ 'shape' => 'EvaluationResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetComplianceDetailsByResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'BaseResourceId', ], 'ComplianceTypes' => [ 'shape' => 'ComplianceTypes', ], 'NextToken' => [ 'shape' => 'String', ], 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], ], ], 'GetComplianceDetailsByResourceResponse' => [ 'type' => 'structure', 'members' => [ 'EvaluationResults' => [ 'shape' => 'EvaluationResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetComplianceSummaryByConfigRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummary' => [ 'shape' => 'ComplianceSummary', ], ], ], 'GetComplianceSummaryByResourceTypeRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], ], ], 'GetComplianceSummaryByResourceTypeResponse' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummariesByResourceType' => [ 'shape' => 'ComplianceSummariesByResourceType', ], ], ], 'GetConformancePackComplianceDetailsLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'GetConformancePackComplianceDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'Filters' => [ 'shape' => 'ConformancePackEvaluationFilters', ], 'Limit' => [ 'shape' => 'GetConformancePackComplianceDetailsLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetConformancePackComplianceDetailsResponse' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'ConformancePackRuleEvaluationResults' => [ 'shape' => 'ConformancePackRuleEvaluationResultsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetConformancePackComplianceSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'ConformancePackNames', ], 'members' => [ 'ConformancePackNames' => [ 'shape' => 'ConformancePackNamesToSummarizeList', ], 'Limit' => [ 'shape' => 'PageSizeLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetConformancePackComplianceSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'ConformancePackComplianceSummaryList' => [ 'shape' => 'ConformancePackComplianceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCustomRulePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], ], ], 'GetCustomRulePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyText' => [ 'shape' => 'PolicyText', ], ], ], 'GetDiscoveredResourceCountsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceTypes' => [ 'shape' => 'ResourceTypes', ], 'limit' => [ 'shape' => 'Limit', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDiscoveredResourceCountsResponse' => [ 'type' => 'structure', 'members' => [ 'totalDiscoveredResources' => [ 'shape' => 'Long', ], 'resourceCounts' => [ 'shape' => 'ResourceCounts', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetOrganizationConfigRuleDetailedStatusRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], 'Filters' => [ 'shape' => 'StatusDetailFilters', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetOrganizationConfigRuleDetailedStatusResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRuleDetailedStatus' => [ 'shape' => 'OrganizationConfigRuleDetailedStatus', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetOrganizationConformancePackDetailedStatusRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConformancePackName', ], 'members' => [ 'OrganizationConformancePackName' => [ 'shape' => 'OrganizationConformancePackName', ], 'Filters' => [ 'shape' => 'OrganizationResourceDetailedStatusFilters', ], 'Limit' => [ 'shape' => 'CosmosPageLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetOrganizationConformancePackDetailedStatusResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePackDetailedStatuses' => [ 'shape' => 'OrganizationConformancePackDetailedStatuses', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetOrganizationCustomRulePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], ], ], 'GetOrganizationCustomRulePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyText' => [ 'shape' => 'PolicyText', ], ], ], 'GetResourceConfigHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'resourceId', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'laterTime' => [ 'shape' => 'LaterTime', ], 'earlierTime' => [ 'shape' => 'EarlierTime', ], 'chronologicalOrder' => [ 'shape' => 'ChronologicalOrder', ], 'limit' => [ 'shape' => 'Limit', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetResourceConfigHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'configurationItems' => [ 'shape' => 'ConfigurationItemList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetResourceEvaluationSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceEvaluationId', ], 'members' => [ 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], ], ], 'GetResourceEvaluationSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], 'EvaluationStatus' => [ 'shape' => 'EvaluationStatus', ], 'EvaluationStartTimestamp' => [ 'shape' => 'Date', ], 'Compliance' => [ 'shape' => 'ComplianceType', ], 'EvaluationContext' => [ 'shape' => 'EvaluationContext', ], 'ResourceDetails' => [ 'shape' => 'ResourceDetails', ], ], ], 'GetStoredQueryRequest' => [ 'type' => 'structure', 'required' => [ 'QueryName', ], 'members' => [ 'QueryName' => [ 'shape' => 'QueryName', ], ], ], 'GetStoredQueryResponse' => [ 'type' => 'structure', 'members' => [ 'StoredQuery' => [ 'shape' => 'StoredQuery', ], ], ], 'GroupByAPILimit' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'GroupedResourceCount' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'ResourceCount', ], 'members' => [ 'GroupName' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceCount' => [ 'shape' => 'Long', ], ], ], 'GroupedResourceCountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupedResourceCount', ], ], 'IdempotentParameterMismatch' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'IncludeGlobalResourceTypes' => [ 'type' => 'boolean', ], 'InsufficientDeliveryPolicyException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InsufficientPermissionsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'InvalidConfigurationRecorderNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDeliveryChannelNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidExpressionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidLimitException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRecordingGroupException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidResultTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRoleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidS3KeyPrefixException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidS3KmsKeyArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSNSTopicARNException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTimeRangeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'LastDeliveryChannelDeleteFailedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LaterTime' => [ 'type' => 'timestamp', ], 'Limit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ListAggregateDiscoveredResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', 'ResourceType', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Filters' => [ 'shape' => 'ResourceFilters', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAggregateDiscoveredResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifiers' => [ 'shape' => 'DiscoveredResourceIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConformancePackComplianceScoresRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ConformancePackComplianceScoresFilters', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'SortBy' => [ 'shape' => 'SortBy', ], 'Limit' => [ 'shape' => 'PageSizeLimit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConformancePackComplianceScoresResponse' => [ 'type' => 'structure', 'required' => [ 'ConformancePackComplianceScores', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ConformancePackComplianceScores' => [ 'shape' => 'ConformancePackComplianceScores', ], ], ], 'ListDiscoveredResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceIds' => [ 'shape' => 'ResourceIdList', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'limit' => [ 'shape' => 'Limit', ], 'includeDeletedResources' => [ 'shape' => 'Boolean', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDiscoveredResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'resourceIdentifiers' => [ 'shape' => 'ResourceIdentifierList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceEvaluationsPageItemLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ListResourceEvaluationsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ResourceEvaluationFilters', ], 'Limit' => [ 'shape' => 'ListResourceEvaluationsPageItemLimit', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListResourceEvaluationsResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceEvaluations' => [ 'shape' => 'ResourceEvaluations', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListStoredQueriesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'box' => true, ], 'MaxResults' => [ 'shape' => 'Limit', 'box' => true, ], ], ], 'ListStoredQueriesResponse' => [ 'type' => 'structure', 'members' => [ 'StoredQueryMetadata' => [ 'shape' => 'StoredQueryMetadataList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', ], 'MaxActiveResourcesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfConfigRulesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfConfigurationRecordersExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfConformancePacksExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfDeliveryChannelsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfOrganizationConfigRulesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfOrganizationConformancePacksExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxNumberOfRetentionConfigurationsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumExecutionFrequency' => [ 'type' => 'string', 'enum' => [ 'One_Hour', 'Three_Hours', 'Six_Hours', 'Twelve_Hours', 'TwentyFour_Hours', ], ], 'MemberAccountRuleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_SUCCESSFUL', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_SUCCESSFUL', 'DELETE_FAILED', 'DELETE_IN_PROGRESS', 'UPDATE_SUCCESSFUL', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'MemberAccountStatus' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ConfigRuleName', 'MemberAccountRuleStatus', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ConfigRuleName' => [ 'shape' => 'StringWithCharLimit64', ], 'MemberAccountRuleStatus' => [ 'shape' => 'MemberAccountRuleStatus', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], ], ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'ConfigurationItemChangeNotification', 'ConfigurationSnapshotDeliveryCompleted', 'ScheduledNotification', 'OversizedConfigurationItemChangeNotification', ], ], 'Name' => [ 'type' => 'string', ], 'NextToken' => [ 'type' => 'string', ], 'NoAvailableConfigurationRecorderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoAvailableDeliveryChannelException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoAvailableOrganizationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoRunningConfigurationRecorderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchBucketException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchConfigRuleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchConfigRuleInConformancePackException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchConfigurationAggregatorException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchConfigurationRecorderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchConformancePackException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchDeliveryChannelException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchOrganizationConfigRuleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchOrganizationConformancePackException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchRemediationConfigurationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchRemediationExceptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NoSuchRetentionConfigurationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrderingTimestamp' => [ 'type' => 'timestamp', ], 'OrganizationAccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrganizationAggregationSource' => [ 'type' => 'structure', 'required' => [ 'RoleArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'String', ], 'AwsRegions' => [ 'shape' => 'AggregatorRegionList', ], 'AllAwsRegions' => [ 'shape' => 'Boolean', ], ], ], 'OrganizationAllFeaturesNotEnabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrganizationConfigRule' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', 'OrganizationConfigRuleArn', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], 'OrganizationConfigRuleArn' => [ 'shape' => 'StringWithCharLimit256', ], 'OrganizationManagedRuleMetadata' => [ 'shape' => 'OrganizationManagedRuleMetadata', ], 'OrganizationCustomRuleMetadata' => [ 'shape' => 'OrganizationCustomRuleMetadata', ], 'ExcludedAccounts' => [ 'shape' => 'ExcludedAccounts', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], 'OrganizationCustomPolicyRuleMetadata' => [ 'shape' => 'OrganizationCustomPolicyRuleMetadataNoPolicy', ], ], ], 'OrganizationConfigRuleDetailedStatus' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberAccountStatus', ], ], 'OrganizationConfigRuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*\\S.*', ], 'OrganizationConfigRuleNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit64', ], 'max' => 25, 'min' => 0, ], 'OrganizationConfigRuleStatus' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', 'OrganizationRuleStatus', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], 'OrganizationRuleStatus' => [ 'shape' => 'OrganizationRuleStatus', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], ], ], 'OrganizationConfigRuleStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConfigRuleStatus', ], ], 'OrganizationConfigRuleTriggerType' => [ 'type' => 'string', 'enum' => [ 'ConfigurationItemChangeNotification', 'OversizedConfigurationItemChangeNotification', 'ScheduledNotification', ], ], 'OrganizationConfigRuleTriggerTypeNoSN' => [ 'type' => 'string', 'enum' => [ 'ConfigurationItemChangeNotification', 'OversizedConfigurationItemChangeNotification', ], ], 'OrganizationConfigRuleTriggerTypeNoSNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConfigRuleTriggerTypeNoSN', ], ], 'OrganizationConfigRuleTriggerTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConfigRuleTriggerType', ], ], 'OrganizationConfigRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConfigRule', ], ], 'OrganizationConformancePack' => [ 'type' => 'structure', 'required' => [ 'OrganizationConformancePackName', 'OrganizationConformancePackArn', 'LastUpdateTime', ], 'members' => [ 'OrganizationConformancePackName' => [ 'shape' => 'OrganizationConformancePackName', ], 'OrganizationConformancePackArn' => [ 'shape' => 'StringWithCharLimit256', ], 'DeliveryS3Bucket' => [ 'shape' => 'DeliveryS3Bucket', ], 'DeliveryS3KeyPrefix' => [ 'shape' => 'DeliveryS3KeyPrefix', ], 'ConformancePackInputParameters' => [ 'shape' => 'ConformancePackInputParameters', ], 'ExcludedAccounts' => [ 'shape' => 'ExcludedAccounts', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], ], ], 'OrganizationConformancePackDetailedStatus' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'ConformancePackName', 'Status', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ConformancePackName' => [ 'shape' => 'StringWithCharLimit256', ], 'Status' => [ 'shape' => 'OrganizationResourceDetailedStatus', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], ], ], 'OrganizationConformancePackDetailedStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConformancePackDetailedStatus', ], ], 'OrganizationConformancePackName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*', ], 'OrganizationConformancePackNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConformancePackName', ], 'max' => 25, 'min' => 0, ], 'OrganizationConformancePackStatus' => [ 'type' => 'structure', 'required' => [ 'OrganizationConformancePackName', 'Status', ], 'members' => [ 'OrganizationConformancePackName' => [ 'shape' => 'OrganizationConformancePackName', ], 'Status' => [ 'shape' => 'OrganizationResourceStatus', ], 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'LastUpdateTime' => [ 'shape' => 'Date', ], ], ], 'OrganizationConformancePackStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConformancePackStatus', ], ], 'OrganizationConformancePackTemplateValidationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OrganizationConformancePacks' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationConformancePack', ], ], 'OrganizationCustomPolicyRuleMetadata' => [ 'type' => 'structure', 'required' => [ 'PolicyRuntime', 'PolicyText', ], 'members' => [ 'Description' => [ 'shape' => 'StringWithCharLimit256Min0', ], 'OrganizationConfigRuleTriggerTypes' => [ 'shape' => 'OrganizationConfigRuleTriggerTypeNoSNs', ], 'InputParameters' => [ 'shape' => 'StringWithCharLimit2048', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], 'ResourceTypesScope' => [ 'shape' => 'ResourceTypesScope', ], 'ResourceIdScope' => [ 'shape' => 'StringWithCharLimit768', ], 'TagKeyScope' => [ 'shape' => 'StringWithCharLimit128', ], 'TagValueScope' => [ 'shape' => 'StringWithCharLimit256', ], 'PolicyRuntime' => [ 'shape' => 'PolicyRuntime', ], 'PolicyText' => [ 'shape' => 'PolicyText', ], 'DebugLogDeliveryAccounts' => [ 'shape' => 'DebugLogDeliveryAccounts', ], ], ], 'OrganizationCustomPolicyRuleMetadataNoPolicy' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'StringWithCharLimit256Min0', ], 'OrganizationConfigRuleTriggerTypes' => [ 'shape' => 'OrganizationConfigRuleTriggerTypeNoSNs', ], 'InputParameters' => [ 'shape' => 'StringWithCharLimit2048', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], 'ResourceTypesScope' => [ 'shape' => 'ResourceTypesScope', ], 'ResourceIdScope' => [ 'shape' => 'StringWithCharLimit768', ], 'TagKeyScope' => [ 'shape' => 'StringWithCharLimit128', ], 'TagValueScope' => [ 'shape' => 'StringWithCharLimit256', ], 'PolicyRuntime' => [ 'shape' => 'PolicyRuntime', ], 'DebugLogDeliveryAccounts' => [ 'shape' => 'DebugLogDeliveryAccounts', ], ], ], 'OrganizationCustomRuleMetadata' => [ 'type' => 'structure', 'required' => [ 'LambdaFunctionArn', 'OrganizationConfigRuleTriggerTypes', ], 'members' => [ 'Description' => [ 'shape' => 'StringWithCharLimit256Min0', ], 'LambdaFunctionArn' => [ 'shape' => 'StringWithCharLimit256', ], 'OrganizationConfigRuleTriggerTypes' => [ 'shape' => 'OrganizationConfigRuleTriggerTypes', ], 'InputParameters' => [ 'shape' => 'StringWithCharLimit2048', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], 'ResourceTypesScope' => [ 'shape' => 'ResourceTypesScope', ], 'ResourceIdScope' => [ 'shape' => 'StringWithCharLimit768', ], 'TagKeyScope' => [ 'shape' => 'StringWithCharLimit128', ], 'TagValueScope' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'OrganizationManagedRuleMetadata' => [ 'type' => 'structure', 'required' => [ 'RuleIdentifier', ], 'members' => [ 'Description' => [ 'shape' => 'StringWithCharLimit256Min0', ], 'RuleIdentifier' => [ 'shape' => 'StringWithCharLimit256', ], 'InputParameters' => [ 'shape' => 'StringWithCharLimit2048', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], 'ResourceTypesScope' => [ 'shape' => 'ResourceTypesScope', ], 'ResourceIdScope' => [ 'shape' => 'StringWithCharLimit768', ], 'TagKeyScope' => [ 'shape' => 'StringWithCharLimit128', ], 'TagValueScope' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'OrganizationResourceDetailedStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_SUCCESSFUL', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_SUCCESSFUL', 'DELETE_FAILED', 'DELETE_IN_PROGRESS', 'UPDATE_SUCCESSFUL', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'OrganizationResourceDetailedStatusFilters' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'OrganizationResourceDetailedStatus', ], ], ], 'OrganizationResourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_SUCCESSFUL', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_SUCCESSFUL', 'DELETE_FAILED', 'DELETE_IN_PROGRESS', 'UPDATE_SUCCESSFUL', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'OrganizationRuleStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_SUCCESSFUL', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_SUCCESSFUL', 'DELETE_FAILED', 'DELETE_IN_PROGRESS', 'UPDATE_SUCCESSFUL', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'OversizedConfigurationItemException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Owner' => [ 'type' => 'string', 'enum' => [ 'CUSTOM_LAMBDA', 'AWS', 'CUSTOM_POLICY', ], ], 'PageSizeLimit' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'ParameterName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ParameterValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'PendingAggregationRequest' => [ 'type' => 'structure', 'members' => [ 'RequesterAccountId' => [ 'shape' => 'AccountId', ], 'RequesterAwsRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'PendingAggregationRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingAggregationRequest', ], ], 'Percentage' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PolicyRuntime' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => 'guard\\-2\\.x\\.x', ], 'PolicyText' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'PutAggregationAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'AuthorizedAccountId', 'AuthorizedAwsRegion', ], 'members' => [ 'AuthorizedAccountId' => [ 'shape' => 'AccountId', ], 'AuthorizedAwsRegion' => [ 'shape' => 'AwsRegion', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'PutAggregationAuthorizationResponse' => [ 'type' => 'structure', 'members' => [ 'AggregationAuthorization' => [ 'shape' => 'AggregationAuthorization', ], ], ], 'PutConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRule', ], 'members' => [ 'ConfigRule' => [ 'shape' => 'ConfigRule', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'PutConfigurationAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationAggregatorName', ], 'members' => [ 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'AccountAggregationSources' => [ 'shape' => 'AccountAggregationSourceList', ], 'OrganizationAggregationSource' => [ 'shape' => 'OrganizationAggregationSource', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'PutConfigurationAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationAggregator' => [ 'shape' => 'ConfigurationAggregator', ], ], ], 'PutConfigurationRecorderRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationRecorder', ], 'members' => [ 'ConfigurationRecorder' => [ 'shape' => 'ConfigurationRecorder', ], ], ], 'PutConformancePackRequest' => [ 'type' => 'structure', 'required' => [ 'ConformancePackName', ], 'members' => [ 'ConformancePackName' => [ 'shape' => 'ConformancePackName', ], 'TemplateS3Uri' => [ 'shape' => 'TemplateS3Uri', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'DeliveryS3Bucket' => [ 'shape' => 'DeliveryS3Bucket', ], 'DeliveryS3KeyPrefix' => [ 'shape' => 'DeliveryS3KeyPrefix', ], 'ConformancePackInputParameters' => [ 'shape' => 'ConformancePackInputParameters', ], 'TemplateSSMDocumentDetails' => [ 'shape' => 'TemplateSSMDocumentDetails', ], ], ], 'PutConformancePackResponse' => [ 'type' => 'structure', 'members' => [ 'ConformancePackArn' => [ 'shape' => 'ConformancePackArn', ], ], ], 'PutDeliveryChannelRequest' => [ 'type' => 'structure', 'required' => [ 'DeliveryChannel', ], 'members' => [ 'DeliveryChannel' => [ 'shape' => 'DeliveryChannel', ], ], ], 'PutEvaluationsRequest' => [ 'type' => 'structure', 'required' => [ 'ResultToken', ], 'members' => [ 'Evaluations' => [ 'shape' => 'Evaluations', ], 'ResultToken' => [ 'shape' => 'String', ], 'TestMode' => [ 'shape' => 'Boolean', ], ], ], 'PutEvaluationsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEvaluations' => [ 'shape' => 'Evaluations', ], ], ], 'PutExternalEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'ExternalEvaluation', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ExternalEvaluation' => [ 'shape' => 'ExternalEvaluation', ], ], ], 'PutExternalEvaluationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutOrganizationConfigRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConfigRuleName', ], 'members' => [ 'OrganizationConfigRuleName' => [ 'shape' => 'OrganizationConfigRuleName', ], 'OrganizationManagedRuleMetadata' => [ 'shape' => 'OrganizationManagedRuleMetadata', ], 'OrganizationCustomRuleMetadata' => [ 'shape' => 'OrganizationCustomRuleMetadata', ], 'ExcludedAccounts' => [ 'shape' => 'ExcludedAccounts', ], 'OrganizationCustomPolicyRuleMetadata' => [ 'shape' => 'OrganizationCustomPolicyRuleMetadata', ], ], ], 'PutOrganizationConfigRuleResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConfigRuleArn' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'PutOrganizationConformancePackRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationConformancePackName', ], 'members' => [ 'OrganizationConformancePackName' => [ 'shape' => 'OrganizationConformancePackName', ], 'TemplateS3Uri' => [ 'shape' => 'TemplateS3Uri', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'DeliveryS3Bucket' => [ 'shape' => 'DeliveryS3Bucket', ], 'DeliveryS3KeyPrefix' => [ 'shape' => 'DeliveryS3KeyPrefix', ], 'ConformancePackInputParameters' => [ 'shape' => 'ConformancePackInputParameters', ], 'ExcludedAccounts' => [ 'shape' => 'ExcludedAccounts', ], ], ], 'PutOrganizationConformancePackResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationConformancePackArn' => [ 'shape' => 'StringWithCharLimit256', ], ], ], 'PutRemediationConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'RemediationConfigurations', ], 'members' => [ 'RemediationConfigurations' => [ 'shape' => 'RemediationConfigurations', ], ], ], 'PutRemediationConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedBatches' => [ 'shape' => 'FailedRemediationBatches', ], ], ], 'PutRemediationExceptionsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'ResourceKeys', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceKeys' => [ 'shape' => 'RemediationExceptionResourceKeys', ], 'Message' => [ 'shape' => 'StringWithCharLimit1024', ], 'ExpirationTime' => [ 'shape' => 'Date', ], ], ], 'PutRemediationExceptionsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedBatches' => [ 'shape' => 'FailedRemediationExceptionBatches', ], ], ], 'PutResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'SchemaVersionId', 'ResourceId', 'Configuration', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeString', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceName' => [ 'shape' => 'ResourceName', ], 'Configuration' => [ 'shape' => 'Configuration', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'PutRetentionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'RetentionPeriodInDays', ], 'members' => [ 'RetentionPeriodInDays' => [ 'shape' => 'RetentionPeriodInDays', ], ], ], 'PutRetentionConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RetentionConfiguration' => [ 'shape' => 'RetentionConfiguration', ], ], ], 'PutStoredQueryRequest' => [ 'type' => 'structure', 'required' => [ 'StoredQuery', ], 'members' => [ 'StoredQuery' => [ 'shape' => 'StoredQuery', ], 'Tags' => [ 'shape' => 'TagsList', ], ], ], 'PutStoredQueryResponse' => [ 'type' => 'structure', 'members' => [ 'QueryArn' => [ 'shape' => 'QueryArn', ], ], ], 'QueryArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '^arn:aws[a-z\\-]*:config:[a-z\\-\\d]+:\\d+:stored-query/[a-zA-Z0-9-_]+/query-[a-zA-Z\\d-_/]+$', ], 'QueryDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'QueryExpression' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'QueryId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^\\S+$', ], 'QueryInfo' => [ 'type' => 'structure', 'members' => [ 'SelectFields' => [ 'shape' => 'FieldInfoList', ], ], ], 'QueryName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'RecorderName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RecorderStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Success', 'Failure', ], ], 'RecordingFrequency' => [ 'type' => 'string', 'enum' => [ 'CONTINUOUS', 'DAILY', ], ], 'RecordingGroup' => [ 'type' => 'structure', 'members' => [ 'allSupported' => [ 'shape' => 'AllSupported', ], 'includeGlobalResourceTypes' => [ 'shape' => 'IncludeGlobalResourceTypes', ], 'resourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'exclusionByResourceTypes' => [ 'shape' => 'ExclusionByResourceTypes', ], 'recordingStrategy' => [ 'shape' => 'RecordingStrategy', ], ], ], 'RecordingMode' => [ 'type' => 'structure', 'required' => [ 'recordingFrequency', ], 'members' => [ 'recordingFrequency' => [ 'shape' => 'RecordingFrequency', ], 'recordingModeOverrides' => [ 'shape' => 'RecordingModeOverrides', ], ], ], 'RecordingModeOverride' => [ 'type' => 'structure', 'required' => [ 'resourceTypes', 'recordingFrequency', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'resourceTypes' => [ 'shape' => 'RecordingModeResourceTypesList', ], 'recordingFrequency' => [ 'shape' => 'RecordingFrequency', ], ], ], 'RecordingModeOverrides' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordingModeOverride', ], 'max' => 1, 'min' => 0, ], 'RecordingModeResourceTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'RecordingStrategy' => [ 'type' => 'structure', 'members' => [ 'useOnly' => [ 'shape' => 'RecordingStrategyType', ], ], ], 'RecordingStrategyType' => [ 'type' => 'string', 'enum' => [ 'ALL_SUPPORTED_RESOURCE_TYPES', 'INCLUSION_BY_RESOURCE_TYPES', 'EXCLUSION_BY_RESOURCE_TYPES', ], ], 'ReevaluateConfigRuleNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRuleName', ], 'max' => 25, 'min' => 1, ], 'RelatedEvent' => [ 'type' => 'string', ], 'RelatedEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedEvent', ], ], 'Relationship' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'relationshipName' => [ 'shape' => 'RelationshipName', ], ], ], 'RelationshipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Relationship', ], ], 'RelationshipName' => [ 'type' => 'string', ], 'RemediationConfiguration' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'TargetType', 'TargetId', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'TargetType' => [ 'shape' => 'RemediationTargetType', ], 'TargetId' => [ 'shape' => 'StringWithCharLimit256', ], 'TargetVersion' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'RemediationParameters', ], 'ResourceType' => [ 'shape' => 'String', ], 'Automatic' => [ 'shape' => 'Boolean', ], 'ExecutionControls' => [ 'shape' => 'ExecutionControls', ], 'MaximumAutomaticAttempts' => [ 'shape' => 'AutoRemediationAttempts', ], 'RetryAttemptSeconds' => [ 'shape' => 'AutoRemediationAttemptSeconds', ], 'Arn' => [ 'shape' => 'StringWithCharLimit1024', ], 'CreatedByService' => [ 'shape' => 'StringWithCharLimit1024', ], ], ], 'RemediationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationConfiguration', ], 'max' => 25, 'min' => 0, ], 'RemediationException' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'ResourceType', 'ResourceId', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'StringWithCharLimit1024', ], 'Message' => [ 'shape' => 'StringWithCharLimit1024', ], 'ExpirationTime' => [ 'shape' => 'Date', ], ], ], 'RemediationExceptionResourceKey' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceId' => [ 'shape' => 'StringWithCharLimit1024', ], ], ], 'RemediationExceptionResourceKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationExceptionResourceKey', ], 'max' => 100, 'min' => 1, ], 'RemediationExceptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationException', ], 'max' => 25, 'min' => 0, ], 'RemediationExecutionState' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'RemediationExecutionStatus' => [ 'type' => 'structure', 'members' => [ 'ResourceKey' => [ 'shape' => 'ResourceKey', ], 'State' => [ 'shape' => 'RemediationExecutionState', ], 'StepDetails' => [ 'shape' => 'RemediationExecutionSteps', ], 'InvocationTime' => [ 'shape' => 'Date', ], 'LastUpdatedTime' => [ 'shape' => 'Date', ], ], ], 'RemediationExecutionStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationExecutionStatus', ], ], 'RemediationExecutionStep' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'RemediationExecutionStepState', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'Date', ], 'StopTime' => [ 'shape' => 'Date', ], ], ], 'RemediationExecutionStepState' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'PENDING', 'FAILED', ], ], 'RemediationExecutionSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemediationExecutionStep', ], ], 'RemediationInProgressException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RemediationParameterValue' => [ 'type' => 'structure', 'members' => [ 'ResourceValue' => [ 'shape' => 'ResourceValue', ], 'StaticValue' => [ 'shape' => 'StaticValue', ], ], ], 'RemediationParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringWithCharLimit256', ], 'value' => [ 'shape' => 'RemediationParameterValue', ], 'max' => 25, 'min' => 0, ], 'RemediationTargetType' => [ 'type' => 'string', 'enum' => [ 'SSM_DOCUMENT', ], ], 'ResourceConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceConfiguration' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, ], 'ResourceConfigurationSchemaType' => [ 'type' => 'string', 'enum' => [ 'CFN_RESOURCE_SCHEMA', ], ], 'ResourceCount' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'count' => [ 'shape' => 'Long', ], ], ], 'ResourceCountFilters' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'Region' => [ 'shape' => 'AwsRegion', ], ], ], 'ResourceCountGroupKey' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_TYPE', 'ACCOUNT_ID', 'AWS_REGION', ], ], 'ResourceCounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceCount', ], ], 'ResourceCreationTime' => [ 'type' => 'timestamp', ], 'ResourceDeletionTime' => [ 'type' => 'timestamp', ], 'ResourceDetails' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'ResourceType', 'ResourceConfiguration', ], 'members' => [ 'ResourceId' => [ 'shape' => 'BaseResourceId', ], 'ResourceType' => [ 'shape' => 'StringWithCharLimit256', ], 'ResourceConfiguration' => [ 'shape' => 'ResourceConfiguration', ], 'ResourceConfigurationSchemaType' => [ 'shape' => 'ResourceConfigurationSchemaType', ], ], ], 'ResourceEvaluation' => [ 'type' => 'structure', 'members' => [ 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], 'EvaluationStartTimestamp' => [ 'shape' => 'Date', ], ], ], 'ResourceEvaluationFilters' => [ 'type' => 'structure', 'members' => [ 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], 'TimeWindow' => [ 'shape' => 'TimeWindow', ], 'EvaluationContextIdentifier' => [ 'shape' => 'EvaluationContextIdentifier', ], ], ], 'ResourceEvaluationId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ResourceEvaluationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', ], ], 'ResourceEvaluations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceEvaluation', ], ], 'ResourceFilters' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ResourceName' => [ 'shape' => 'ResourceName', ], 'Region' => [ 'shape' => 'AwsRegion', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 768, 'min' => 1, ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], ], 'ResourceIdentifier' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceDeletionTime' => [ 'shape' => 'ResourceDeletionTime', ], ], ], 'ResourceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], ], 'ResourceIdentifiersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateResourceIdentifier', ], 'max' => 100, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceKey' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'resourceId', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], ], ], 'ResourceKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceKey', ], 'max' => 100, 'min' => 1, ], 'ResourceName' => [ 'type' => 'string', ], 'ResourceNotDiscoveredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::EC2::CustomerGateway', 'AWS::EC2::EIP', 'AWS::EC2::Host', 'AWS::EC2::Instance', 'AWS::EC2::InternetGateway', 'AWS::EC2::NetworkAcl', 'AWS::EC2::NetworkInterface', 'AWS::EC2::RouteTable', 'AWS::EC2::SecurityGroup', 'AWS::EC2::Subnet', 'AWS::CloudTrail::Trail', 'AWS::EC2::Volume', 'AWS::EC2::VPC', 'AWS::EC2::VPNConnection', 'AWS::EC2::VPNGateway', 'AWS::EC2::RegisteredHAInstance', 'AWS::EC2::NatGateway', 'AWS::EC2::EgressOnlyInternetGateway', 'AWS::EC2::VPCEndpoint', 'AWS::EC2::VPCEndpointService', 'AWS::EC2::FlowLog', 'AWS::EC2::VPCPeeringConnection', 'AWS::Elasticsearch::Domain', 'AWS::IAM::Group', 'AWS::IAM::Policy', 'AWS::IAM::Role', 'AWS::IAM::User', 'AWS::ElasticLoadBalancingV2::LoadBalancer', 'AWS::ACM::Certificate', 'AWS::RDS::DBInstance', 'AWS::RDS::DBSubnetGroup', 'AWS::RDS::DBSecurityGroup', 'AWS::RDS::DBSnapshot', 'AWS::RDS::DBCluster', 'AWS::RDS::DBClusterSnapshot', 'AWS::RDS::EventSubscription', 'AWS::S3::Bucket', 'AWS::S3::AccountPublicAccessBlock', 'AWS::Redshift::Cluster', 'AWS::Redshift::ClusterSnapshot', 'AWS::Redshift::ClusterParameterGroup', 'AWS::Redshift::ClusterSecurityGroup', 'AWS::Redshift::ClusterSubnetGroup', 'AWS::Redshift::EventSubscription', 'AWS::SSM::ManagedInstanceInventory', 'AWS::CloudWatch::Alarm', 'AWS::CloudFormation::Stack', 'AWS::ElasticLoadBalancing::LoadBalancer', 'AWS::AutoScaling::AutoScalingGroup', 'AWS::AutoScaling::LaunchConfiguration', 'AWS::AutoScaling::ScalingPolicy', 'AWS::AutoScaling::ScheduledAction', 'AWS::DynamoDB::Table', 'AWS::CodeBuild::Project', 'AWS::WAF::RateBasedRule', 'AWS::WAF::Rule', 'AWS::WAF::RuleGroup', 'AWS::WAF::WebACL', 'AWS::WAFRegional::RateBasedRule', 'AWS::WAFRegional::Rule', 'AWS::WAFRegional::RuleGroup', 'AWS::WAFRegional::WebACL', 'AWS::CloudFront::Distribution', 'AWS::CloudFront::StreamingDistribution', 'AWS::Lambda::Function', 'AWS::NetworkFirewall::Firewall', 'AWS::NetworkFirewall::FirewallPolicy', 'AWS::NetworkFirewall::RuleGroup', 'AWS::ElasticBeanstalk::Application', 'AWS::ElasticBeanstalk::ApplicationVersion', 'AWS::ElasticBeanstalk::Environment', 'AWS::WAFv2::WebACL', 'AWS::WAFv2::RuleGroup', 'AWS::WAFv2::IPSet', 'AWS::WAFv2::RegexPatternSet', 'AWS::WAFv2::ManagedRuleSet', 'AWS::XRay::EncryptionConfig', 'AWS::SSM::AssociationCompliance', 'AWS::SSM::PatchCompliance', 'AWS::Shield::Protection', 'AWS::ShieldRegional::Protection', 'AWS::Config::ConformancePackCompliance', 'AWS::Config::ResourceCompliance', 'AWS::ApiGateway::Stage', 'AWS::ApiGateway::RestApi', 'AWS::ApiGatewayV2::Stage', 'AWS::ApiGatewayV2::Api', 'AWS::CodePipeline::Pipeline', 'AWS::ServiceCatalog::CloudFormationProvisionedProduct', 'AWS::ServiceCatalog::CloudFormationProduct', 'AWS::ServiceCatalog::Portfolio', 'AWS::SQS::Queue', 'AWS::KMS::Key', 'AWS::QLDB::Ledger', 'AWS::SecretsManager::Secret', 'AWS::SNS::Topic', 'AWS::SSM::FileData', 'AWS::Backup::BackupPlan', 'AWS::Backup::BackupSelection', 'AWS::Backup::BackupVault', 'AWS::Backup::RecoveryPoint', 'AWS::ECR::Repository', 'AWS::ECS::Cluster', 'AWS::ECS::Service', 'AWS::ECS::TaskDefinition', 'AWS::EFS::AccessPoint', 'AWS::EFS::FileSystem', 'AWS::EKS::Cluster', 'AWS::OpenSearch::Domain', 'AWS::EC2::TransitGateway', 'AWS::Kinesis::Stream', 'AWS::Kinesis::StreamConsumer', 'AWS::CodeDeploy::Application', 'AWS::CodeDeploy::DeploymentConfig', 'AWS::CodeDeploy::DeploymentGroup', 'AWS::EC2::LaunchTemplate', 'AWS::ECR::PublicRepository', 'AWS::GuardDuty::Detector', 'AWS::EMR::SecurityConfiguration', 'AWS::SageMaker::CodeRepository', 'AWS::Route53Resolver::ResolverEndpoint', 'AWS::Route53Resolver::ResolverRule', 'AWS::Route53Resolver::ResolverRuleAssociation', 'AWS::DMS::ReplicationSubnetGroup', 'AWS::DMS::EventSubscription', 'AWS::MSK::Cluster', 'AWS::StepFunctions::Activity', 'AWS::WorkSpaces::Workspace', 'AWS::WorkSpaces::ConnectionAlias', 'AWS::SageMaker::Model', 'AWS::ElasticLoadBalancingV2::Listener', 'AWS::StepFunctions::StateMachine', 'AWS::Batch::JobQueue', 'AWS::Batch::ComputeEnvironment', 'AWS::AccessAnalyzer::Analyzer', 'AWS::Athena::WorkGroup', 'AWS::Athena::DataCatalog', 'AWS::Detective::Graph', 'AWS::GlobalAccelerator::Accelerator', 'AWS::GlobalAccelerator::EndpointGroup', 'AWS::GlobalAccelerator::Listener', 'AWS::EC2::TransitGatewayAttachment', 'AWS::EC2::TransitGatewayRouteTable', 'AWS::DMS::Certificate', 'AWS::AppConfig::Application', 'AWS::AppSync::GraphQLApi', 'AWS::DataSync::LocationSMB', 'AWS::DataSync::LocationFSxLustre', 'AWS::DataSync::LocationS3', 'AWS::DataSync::LocationEFS', 'AWS::DataSync::Task', 'AWS::DataSync::LocationNFS', 'AWS::EC2::NetworkInsightsAccessScopeAnalysis', 'AWS::EKS::FargateProfile', 'AWS::Glue::Job', 'AWS::GuardDuty::ThreatIntelSet', 'AWS::GuardDuty::IPSet', 'AWS::SageMaker::Workteam', 'AWS::SageMaker::NotebookInstanceLifecycleConfig', 'AWS::ServiceDiscovery::Service', 'AWS::ServiceDiscovery::PublicDnsNamespace', 'AWS::SES::ContactList', 'AWS::SES::ConfigurationSet', 'AWS::Route53::HostedZone', 'AWS::IoTEvents::Input', 'AWS::IoTEvents::DetectorModel', 'AWS::IoTEvents::AlarmModel', 'AWS::ServiceDiscovery::HttpNamespace', 'AWS::Events::EventBus', 'AWS::ImageBuilder::ContainerRecipe', 'AWS::ImageBuilder::DistributionConfiguration', 'AWS::ImageBuilder::InfrastructureConfiguration', 'AWS::DataSync::LocationObjectStorage', 'AWS::DataSync::LocationHDFS', 'AWS::Glue::Classifier', 'AWS::Route53RecoveryReadiness::Cell', 'AWS::Route53RecoveryReadiness::ReadinessCheck', 'AWS::ECR::RegistryPolicy', 'AWS::Backup::ReportPlan', 'AWS::Lightsail::Certificate', 'AWS::RUM::AppMonitor', 'AWS::Events::Endpoint', 'AWS::SES::ReceiptRuleSet', 'AWS::Events::Archive', 'AWS::Events::ApiDestination', 'AWS::Lightsail::Disk', 'AWS::FIS::ExperimentTemplate', 'AWS::DataSync::LocationFSxWindows', 'AWS::SES::ReceiptFilter', 'AWS::GuardDuty::Filter', 'AWS::SES::Template', 'AWS::AmazonMQ::Broker', 'AWS::AppConfig::Environment', 'AWS::AppConfig::ConfigurationProfile', 'AWS::Cloud9::EnvironmentEC2', 'AWS::EventSchemas::Registry', 'AWS::EventSchemas::RegistryPolicy', 'AWS::EventSchemas::Discoverer', 'AWS::FraudDetector::Label', 'AWS::FraudDetector::EntityType', 'AWS::FraudDetector::Variable', 'AWS::FraudDetector::Outcome', 'AWS::IoT::Authorizer', 'AWS::IoT::SecurityProfile', 'AWS::IoT::RoleAlias', 'AWS::IoT::Dimension', 'AWS::IoTAnalytics::Datastore', 'AWS::Lightsail::Bucket', 'AWS::Lightsail::StaticIp', 'AWS::MediaPackage::PackagingGroup', 'AWS::Route53RecoveryReadiness::RecoveryGroup', 'AWS::ResilienceHub::ResiliencyPolicy', 'AWS::Transfer::Workflow', 'AWS::EKS::IdentityProviderConfig', 'AWS::EKS::Addon', 'AWS::Glue::MLTransform', 'AWS::IoT::Policy', 'AWS::IoT::MitigationAction', 'AWS::IoTTwinMaker::Workspace', 'AWS::IoTTwinMaker::Entity', 'AWS::IoTAnalytics::Dataset', 'AWS::IoTAnalytics::Pipeline', 'AWS::IoTAnalytics::Channel', 'AWS::IoTSiteWise::Dashboard', 'AWS::IoTSiteWise::Project', 'AWS::IoTSiteWise::Portal', 'AWS::IoTSiteWise::AssetModel', 'AWS::IVS::Channel', 'AWS::IVS::RecordingConfiguration', 'AWS::IVS::PlaybackKeyPair', 'AWS::KinesisAnalyticsV2::Application', 'AWS::RDS::GlobalCluster', 'AWS::S3::MultiRegionAccessPoint', 'AWS::DeviceFarm::TestGridProject', 'AWS::Budgets::BudgetsAction', 'AWS::Lex::Bot', 'AWS::CodeGuruReviewer::RepositoryAssociation', 'AWS::IoT::CustomMetric', 'AWS::Route53Resolver::FirewallDomainList', 'AWS::RoboMaker::RobotApplicationVersion', 'AWS::EC2::TrafficMirrorSession', 'AWS::IoTSiteWise::Gateway', 'AWS::Lex::BotAlias', 'AWS::LookoutMetrics::Alert', 'AWS::IoT::AccountAuditConfiguration', 'AWS::EC2::TrafficMirrorTarget', 'AWS::S3::StorageLens', 'AWS::IoT::ScheduledAudit', 'AWS::Events::Connection', 'AWS::EventSchemas::Schema', 'AWS::MediaPackage::PackagingConfiguration', 'AWS::KinesisVideo::SignalingChannel', 'AWS::AppStream::DirectoryConfig', 'AWS::LookoutVision::Project', 'AWS::Route53RecoveryControl::Cluster', 'AWS::Route53RecoveryControl::SafetyRule', 'AWS::Route53RecoveryControl::ControlPanel', 'AWS::Route53RecoveryControl::RoutingControl', 'AWS::Route53RecoveryReadiness::ResourceSet', 'AWS::RoboMaker::SimulationApplication', 'AWS::RoboMaker::RobotApplication', 'AWS::HealthLake::FHIRDatastore', 'AWS::Pinpoint::Segment', 'AWS::Pinpoint::ApplicationSettings', 'AWS::Events::Rule', 'AWS::EC2::DHCPOptions', 'AWS::EC2::NetworkInsightsPath', 'AWS::EC2::TrafficMirrorFilter', 'AWS::EC2::IPAM', 'AWS::IoTTwinMaker::Scene', 'AWS::NetworkManager::TransitGatewayRegistration', 'AWS::CustomerProfiles::Domain', 'AWS::AutoScaling::WarmPool', 'AWS::Connect::PhoneNumber', 'AWS::AppConfig::DeploymentStrategy', 'AWS::AppFlow::Flow', 'AWS::AuditManager::Assessment', 'AWS::CloudWatch::MetricStream', 'AWS::DeviceFarm::InstanceProfile', 'AWS::DeviceFarm::Project', 'AWS::EC2::EC2Fleet', 'AWS::EC2::SubnetRouteTableAssociation', 'AWS::ECR::PullThroughCacheRule', 'AWS::GroundStation::Config', 'AWS::ImageBuilder::ImagePipeline', 'AWS::IoT::FleetMetric', 'AWS::IoTWireless::ServiceProfile', 'AWS::NetworkManager::Device', 'AWS::NetworkManager::GlobalNetwork', 'AWS::NetworkManager::Link', 'AWS::NetworkManager::Site', 'AWS::Panorama::Package', 'AWS::Pinpoint::App', 'AWS::Redshift::ScheduledAction', 'AWS::Route53Resolver::FirewallRuleGroupAssociation', 'AWS::SageMaker::AppImageConfig', 'AWS::SageMaker::Image', 'AWS::ECS::TaskSet', 'AWS::Cassandra::Keyspace', 'AWS::Signer::SigningProfile', 'AWS::Amplify::App', 'AWS::AppMesh::VirtualNode', 'AWS::AppMesh::VirtualService', 'AWS::AppRunner::VpcConnector', 'AWS::AppStream::Application', 'AWS::CodeArtifact::Repository', 'AWS::EC2::PrefixList', 'AWS::EC2::SpotFleet', 'AWS::Evidently::Project', 'AWS::Forecast::Dataset', 'AWS::IAM::SAMLProvider', 'AWS::IAM::ServerCertificate', 'AWS::Pinpoint::Campaign', 'AWS::Pinpoint::InAppTemplate', 'AWS::SageMaker::Domain', 'AWS::Transfer::Agreement', 'AWS::Transfer::Connector', 'AWS::KinesisFirehose::DeliveryStream', 'AWS::Amplify::Branch', 'AWS::AppIntegrations::EventIntegration', 'AWS::AppMesh::Route', 'AWS::Athena::PreparedStatement', 'AWS::EC2::IPAMScope', 'AWS::Evidently::Launch', 'AWS::Forecast::DatasetGroup', 'AWS::GreengrassV2::ComponentVersion', 'AWS::GroundStation::MissionProfile', 'AWS::MediaConnect::FlowEntitlement', 'AWS::MediaConnect::FlowVpcInterface', 'AWS::MediaTailor::PlaybackConfiguration', 'AWS::MSK::Configuration', 'AWS::Personalize::Dataset', 'AWS::Personalize::Schema', 'AWS::Personalize::Solution', 'AWS::Pinpoint::EmailTemplate', 'AWS::Pinpoint::EventStream', 'AWS::ResilienceHub::App', 'AWS::ACMPCA::CertificateAuthority', 'AWS::AppConfig::HostedConfigurationVersion', 'AWS::AppMesh::VirtualGateway', 'AWS::AppMesh::VirtualRouter', 'AWS::AppRunner::Service', 'AWS::CustomerProfiles::ObjectType', 'AWS::DMS::Endpoint', 'AWS::EC2::CapacityReservation', 'AWS::EC2::ClientVpnEndpoint', 'AWS::Kendra::Index', 'AWS::KinesisVideo::Stream', 'AWS::Logs::Destination', 'AWS::Pinpoint::EmailChannel', 'AWS::S3::AccessPoint', 'AWS::NetworkManager::CustomerGatewayAssociation', 'AWS::NetworkManager::LinkAssociation', 'AWS::IoTWireless::MulticastGroup', 'AWS::Personalize::DatasetGroup', 'AWS::IoTTwinMaker::ComponentType', 'AWS::CodeBuild::ReportGroup', 'AWS::SageMaker::FeatureGroup', 'AWS::MSK::BatchScramSecret', 'AWS::AppStream::Stack', 'AWS::IoT::JobTemplate', 'AWS::IoTWireless::FuotaTask', 'AWS::IoT::ProvisioningTemplate', 'AWS::InspectorV2::Filter', 'AWS::Route53Resolver::ResolverQueryLoggingConfigAssociation', 'AWS::ServiceDiscovery::Instance', 'AWS::Transfer::Certificate', 'AWS::MediaConnect::FlowSource', 'AWS::APS::RuleGroupsNamespace', 'AWS::CodeGuruProfiler::ProfilingGroup', 'AWS::Route53Resolver::ResolverQueryLoggingConfig', 'AWS::Batch::SchedulingPolicy', 'AWS::ACMPCA::CertificateAuthorityActivation', 'AWS::AppMesh::GatewayRoute', 'AWS::AppMesh::Mesh', 'AWS::Connect::Instance', 'AWS::Connect::QuickConnect', 'AWS::EC2::CarrierGateway', 'AWS::EC2::IPAMPool', 'AWS::EC2::TransitGatewayConnect', 'AWS::EC2::TransitGatewayMulticastDomain', 'AWS::ECS::CapacityProvider', 'AWS::IAM::InstanceProfile', 'AWS::IoT::CACertificate', 'AWS::IoTTwinMaker::SyncJob', 'AWS::KafkaConnect::Connector', 'AWS::Lambda::CodeSigningConfig', 'AWS::NetworkManager::ConnectPeer', 'AWS::ResourceExplorer2::Index', 'AWS::AppStream::Fleet', 'AWS::Cognito::UserPool', 'AWS::Cognito::UserPoolClient', 'AWS::Cognito::UserPoolGroup', 'AWS::EC2::NetworkInsightsAccessScope', 'AWS::EC2::NetworkInsightsAnalysis', 'AWS::Grafana::Workspace', 'AWS::GroundStation::DataflowEndpointGroup', 'AWS::ImageBuilder::ImageRecipe', 'AWS::KMS::Alias', 'AWS::M2::Environment', 'AWS::QuickSight::DataSource', 'AWS::QuickSight::Template', 'AWS::QuickSight::Theme', 'AWS::RDS::OptionGroup', 'AWS::Redshift::EndpointAccess', 'AWS::Route53Resolver::FirewallRuleGroup', 'AWS::SSM::Document', ], ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'ResourceTypeString' => [ 'type' => 'string', 'max' => 196, 'min' => 1, ], 'ResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit256', ], 'max' => 20, 'min' => 0, ], 'ResourceTypesScope' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit256', ], 'max' => 100, 'min' => 0, ], 'ResourceValue' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'ResourceValueType', ], ], ], 'ResourceValueType' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_ID', ], ], 'Results' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'RetentionConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'RetentionPeriodInDays', ], 'members' => [ 'Name' => [ 'shape' => 'RetentionConfigurationName', ], 'RetentionPeriodInDays' => [ 'shape' => 'RetentionPeriodInDays', ], ], ], 'RetentionConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetentionConfiguration', ], ], 'RetentionConfigurationName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w\\-]+', ], 'RetentionConfigurationNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetentionConfigurationName', ], 'max' => 1, 'min' => 0, ], 'RetentionPeriodInDays' => [ 'type' => 'integer', 'max' => 2557, 'min' => 30, ], 'RuleLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 0, ], 'SSMDocumentName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.:/]{3,200}$', ], 'SSMDocumentVersion' => [ 'type' => 'string', 'pattern' => '([$]LATEST|[$]DEFAULT|^[1-9][0-9]*$)', ], 'SchemaVersionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'Scope' => [ 'type' => 'structure', 'members' => [ 'ComplianceResourceTypes' => [ 'shape' => 'ComplianceResourceTypes', ], 'TagKey' => [ 'shape' => 'StringWithCharLimit128', ], 'TagValue' => [ 'shape' => 'StringWithCharLimit256', ], 'ComplianceResourceId' => [ 'shape' => 'BaseResourceId', ], ], ], 'SelectAggregateResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', 'ConfigurationAggregatorName', ], 'members' => [ 'Expression' => [ 'shape' => 'Expression', ], 'ConfigurationAggregatorName' => [ 'shape' => 'ConfigurationAggregatorName', ], 'Limit' => [ 'shape' => 'Limit', ], 'MaxResults' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SelectAggregateResourceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'Results', ], 'QueryInfo' => [ 'shape' => 'QueryInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SelectResourceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'Expression' => [ 'shape' => 'Expression', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SelectResourceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'Results', ], 'QueryInfo' => [ 'shape' => 'QueryInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SortBy' => [ 'type' => 'string', 'enum' => [ 'SCORE', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'Source' => [ 'type' => 'structure', 'required' => [ 'Owner', ], 'members' => [ 'Owner' => [ 'shape' => 'Owner', ], 'SourceIdentifier' => [ 'shape' => 'StringWithCharLimit256', ], 'SourceDetails' => [ 'shape' => 'SourceDetails', ], 'CustomPolicyDetails' => [ 'shape' => 'CustomPolicyDetails', ], ], ], 'SourceDetail' => [ 'type' => 'structure', 'members' => [ 'EventSource' => [ 'shape' => 'EventSource', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'MaximumExecutionFrequency' => [ 'shape' => 'MaximumExecutionFrequency', ], ], ], 'SourceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceDetail', ], 'max' => 25, 'min' => 0, ], 'SsmControls' => [ 'type' => 'structure', 'members' => [ 'ConcurrentExecutionRatePercentage' => [ 'shape' => 'Percentage', ], 'ErrorPercentage' => [ 'shape' => 'Percentage', ], ], ], 'StackArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StartConfigRulesEvaluationRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigRuleNames' => [ 'shape' => 'ReevaluateConfigRuleNames', ], ], ], 'StartConfigRulesEvaluationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartConfigurationRecorderRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationRecorderName', ], 'members' => [ 'ConfigurationRecorderName' => [ 'shape' => 'RecorderName', ], ], ], 'StartRemediationExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigRuleName', 'ResourceKeys', ], 'members' => [ 'ConfigRuleName' => [ 'shape' => 'ConfigRuleName', ], 'ResourceKeys' => [ 'shape' => 'ResourceKeys', ], ], ], 'StartRemediationExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'FailureMessage' => [ 'shape' => 'String', ], 'FailedItems' => [ 'shape' => 'ResourceKeys', ], ], ], 'StartResourceEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceDetails', 'EvaluationMode', ], 'members' => [ 'ResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'EvaluationContext' => [ 'shape' => 'EvaluationContext', ], 'EvaluationMode' => [ 'shape' => 'EvaluationMode', ], 'EvaluationTimeout' => [ 'shape' => 'EvaluationTimeout', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'StartResourceEvaluationResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceEvaluationId' => [ 'shape' => 'ResourceEvaluationId', ], ], ], 'StaticParameterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringWithCharLimit256', ], 'max' => 25, 'min' => 0, ], 'StaticValue' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'StaticParameterValues', ], ], ], 'StatusDetailFilters' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'MemberAccountRuleStatus' => [ 'shape' => 'MemberAccountRuleStatus', ], ], ], 'StopConfigurationRecorderRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationRecorderName', ], 'members' => [ 'ConfigurationRecorderName' => [ 'shape' => 'RecorderName', ], ], ], 'StoredQuery' => [ 'type' => 'structure', 'required' => [ 'QueryName', ], 'members' => [ 'QueryId' => [ 'shape' => 'QueryId', 'box' => true, ], 'QueryArn' => [ 'shape' => 'QueryArn', 'box' => true, ], 'QueryName' => [ 'shape' => 'QueryName', ], 'Description' => [ 'shape' => 'QueryDescription', 'box' => true, ], 'Expression' => [ 'shape' => 'QueryExpression', 'box' => true, ], ], ], 'StoredQueryMetadata' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'QueryArn', 'QueryName', ], 'members' => [ 'QueryId' => [ 'shape' => 'QueryId', ], 'QueryArn' => [ 'shape' => 'QueryArn', ], 'QueryName' => [ 'shape' => 'QueryName', ], 'Description' => [ 'shape' => 'QueryDescription', ], ], ], 'StoredQueryMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StoredQueryMetadata', ], ], 'String' => [ 'type' => 'string', ], 'StringWithCharLimit1024' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'StringWithCharLimit128' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'StringWithCharLimit2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringWithCharLimit256' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'StringWithCharLimit256Min0' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'StringWithCharLimit64' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StringWithCharLimit768' => [ 'type' => 'string', 'max' => 768, 'min' => 1, ], 'SupplementaryConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'SupplementaryConfigurationName', ], 'value' => [ 'shape' => 'SupplementaryConfigurationValue', ], ], 'SupplementaryConfigurationName' => [ 'type' => 'string', ], 'SupplementaryConfigurationValue' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Value', ], ], 'TagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TemplateBody' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, ], 'TemplateS3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://.*', ], 'TemplateSSMDocumentDetails' => [ 'type' => 'structure', 'required' => [ 'DocumentName', ], 'members' => [ 'DocumentName' => [ 'shape' => 'SSMDocumentName', ], 'DocumentVersion' => [ 'shape' => 'SSMDocumentVersion', ], ], ], 'TimeWindow' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Date', ], 'EndTime' => [ 'shape' => 'Date', ], ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UnprocessedResourceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateResourceIdentifier', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Value' => [ 'type' => 'string', ], 'Version' => [ 'type' => 'string', ], ],];
