<?php
// This file was auto-generated from sdk-root/src/data/kinesis-video-archived-media/2017-09-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-09-30', 'endpointPrefix' => 'kinesisvideo', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Kinesis Video Archived Media', 'serviceFullName' => 'Amazon Kinesis Video Streams Archived Media', 'serviceId' => 'Kinesis Video Archived Media', 'signatureVersion' => 'v4', 'uid' => 'kinesis-video-archived-media-2017-09-30', ], 'operations' => [ 'GetClip' => [ 'name' => 'GetClip', 'http' => [ 'method' => 'POST', 'requestUri' => '/getClip', ], 'input' => [ 'shape' => 'GetClipInput', ], 'output' => [ 'shape' => 'GetClipOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnsupportedStreamMediaTypeException', ], [ 'shape' => 'MissingCodecPrivateDataException', ], [ 'shape' => 'InvalidCodecPrivateDataException', ], [ 'shape' => 'InvalidMediaFrameException', ], [ 'shape' => 'NoDataRetentionException', ], ], ], 'GetDASHStreamingSessionURL' => [ 'name' => 'GetDASHStreamingSessionURL', 'http' => [ 'method' => 'POST', 'requestUri' => '/getDASHStreamingSessionURL', ], 'input' => [ 'shape' => 'GetDASHStreamingSessionURLInput', ], 'output' => [ 'shape' => 'GetDASHStreamingSessionURLOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnsupportedStreamMediaTypeException', ], [ 'shape' => 'NoDataRetentionException', ], [ 'shape' => 'MissingCodecPrivateDataException', ], [ 'shape' => 'InvalidCodecPrivateDataException', ], ], ], 'GetHLSStreamingSessionURL' => [ 'name' => 'GetHLSStreamingSessionURL', 'http' => [ 'method' => 'POST', 'requestUri' => '/getHLSStreamingSessionURL', ], 'input' => [ 'shape' => 'GetHLSStreamingSessionURLInput', ], 'output' => [ 'shape' => 'GetHLSStreamingSessionURLOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnsupportedStreamMediaTypeException', ], [ 'shape' => 'NoDataRetentionException', ], [ 'shape' => 'MissingCodecPrivateDataException', ], [ 'shape' => 'InvalidCodecPrivateDataException', ], ], ], 'GetImages' => [ 'name' => 'GetImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/getImages', ], 'input' => [ 'shape' => 'GetImagesInput', ], 'output' => [ 'shape' => 'GetImagesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'NoDataRetentionException', ], ], ], 'GetMediaForFragmentList' => [ 'name' => 'GetMediaForFragmentList', 'http' => [ 'method' => 'POST', 'requestUri' => '/getMediaForFragmentList', ], 'input' => [ 'shape' => 'GetMediaForFragmentListInput', ], 'output' => [ 'shape' => 'GetMediaForFragmentListOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'ListFragments' => [ 'name' => 'ListFragments', 'http' => [ 'method' => 'POST', 'requestUri' => '/listFragments', ], 'input' => [ 'shape' => 'ListFragmentsInput', ], 'output' => [ 'shape' => 'ListFragmentsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ClientLimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], ], 'shapes' => [ 'ClientLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ClipFragmentSelector' => [ 'type' => 'structure', 'required' => [ 'FragmentSelectorType', 'TimestampRange', ], 'members' => [ 'FragmentSelectorType' => [ 'shape' => 'ClipFragmentSelectorType', ], 'TimestampRange' => [ 'shape' => 'ClipTimestampRange', ], ], ], 'ClipFragmentSelectorType' => [ 'type' => 'string', 'enum' => [ 'PRODUCER_TIMESTAMP', 'SERVER_TIMESTAMP', ], ], 'ClipTimestampRange' => [ 'type' => 'structure', 'required' => [ 'StartTimestamp', 'EndTimestamp', ], 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ContainerFormat' => [ 'type' => 'string', 'enum' => [ 'FRAGMENTED_MP4', 'MPEG_TS', ], ], 'ContentType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_\\.\\-]+$', ], 'DASHDisplayFragmentNumber' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'NEVER', ], ], 'DASHDisplayFragmentTimestamp' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'NEVER', ], ], 'DASHFragmentSelector' => [ 'type' => 'structure', 'members' => [ 'FragmentSelectorType' => [ 'shape' => 'DASHFragmentSelectorType', ], 'TimestampRange' => [ 'shape' => 'DASHTimestampRange', ], ], ], 'DASHFragmentSelectorType' => [ 'type' => 'string', 'enum' => [ 'PRODUCER_TIMESTAMP', 'SERVER_TIMESTAMP', ], ], 'DASHMaxResults' => [ 'type' => 'long', 'max' => 5000, 'min' => 1, ], 'DASHPlaybackMode' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'LIVE_REPLAY', 'ON_DEMAND', ], ], 'DASHStreamingSessionURL' => [ 'type' => 'string', ], 'DASHTimestampRange' => [ 'type' => 'structure', 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'Expires' => [ 'type' => 'integer', 'max' => 43200, 'min' => 300, ], 'Format' => [ 'type' => 'string', 'enum' => [ 'JPEG', 'PNG', ], ], 'FormatConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'FormatConfigKey', ], 'value' => [ 'shape' => 'FormatConfigValue', ], 'max' => 1, 'min' => 1, ], 'FormatConfigKey' => [ 'type' => 'string', 'enum' => [ 'JPEGQuality', ], ], 'FormatConfigValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z_0-9]+', ], 'Fragment' => [ 'type' => 'structure', 'members' => [ 'FragmentNumber' => [ 'shape' => 'FragmentNumberString', ], 'FragmentSizeInBytes' => [ 'shape' => 'Long', ], 'ProducerTimestamp' => [ 'shape' => 'Timestamp', ], 'ServerTimestamp' => [ 'shape' => 'Timestamp', ], 'FragmentLengthInMilliseconds' => [ 'shape' => 'Long', ], ], ], 'FragmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Fragment', ], ], 'FragmentNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FragmentNumberString', ], 'max' => 1000, 'min' => 1, ], 'FragmentNumberString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'FragmentSelector' => [ 'type' => 'structure', 'required' => [ 'FragmentSelectorType', 'TimestampRange', ], 'members' => [ 'FragmentSelectorType' => [ 'shape' => 'FragmentSelectorType', ], 'TimestampRange' => [ 'shape' => 'TimestampRange', ], ], ], 'FragmentSelectorType' => [ 'type' => 'string', 'enum' => [ 'PRODUCER_TIMESTAMP', 'SERVER_TIMESTAMP', ], ], 'GetClipInput' => [ 'type' => 'structure', 'required' => [ 'ClipFragmentSelector', ], 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'ClipFragmentSelector' => [ 'shape' => 'ClipFragmentSelector', ], ], ], 'GetClipOutput' => [ 'type' => 'structure', 'members' => [ 'ContentType' => [ 'shape' => 'ContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'Payload' => [ 'shape' => 'Payload', ], ], 'payload' => 'Payload', ], 'GetDASHStreamingSessionURLInput' => [ 'type' => 'structure', 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'PlaybackMode' => [ 'shape' => 'DASHPlaybackMode', ], 'DisplayFragmentTimestamp' => [ 'shape' => 'DASHDisplayFragmentTimestamp', ], 'DisplayFragmentNumber' => [ 'shape' => 'DASHDisplayFragmentNumber', ], 'DASHFragmentSelector' => [ 'shape' => 'DASHFragmentSelector', ], 'Expires' => [ 'shape' => 'Expires', ], 'MaxManifestFragmentResults' => [ 'shape' => 'DASHMaxResults', ], ], ], 'GetDASHStreamingSessionURLOutput' => [ 'type' => 'structure', 'members' => [ 'DASHStreamingSessionURL' => [ 'shape' => 'DASHStreamingSessionURL', ], ], ], 'GetHLSStreamingSessionURLInput' => [ 'type' => 'structure', 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'PlaybackMode' => [ 'shape' => 'HLSPlaybackMode', ], 'HLSFragmentSelector' => [ 'shape' => 'HLSFragmentSelector', ], 'ContainerFormat' => [ 'shape' => 'ContainerFormat', ], 'DiscontinuityMode' => [ 'shape' => 'HLSDiscontinuityMode', ], 'DisplayFragmentTimestamp' => [ 'shape' => 'HLSDisplayFragmentTimestamp', ], 'Expires' => [ 'shape' => 'Expires', ], 'MaxMediaPlaylistFragmentResults' => [ 'shape' => 'HLSMaxResults', ], ], ], 'GetHLSStreamingSessionURLOutput' => [ 'type' => 'structure', 'members' => [ 'HLSStreamingSessionURL' => [ 'shape' => 'HLSStreamingSessionURL', ], ], ], 'GetImagesInput' => [ 'type' => 'structure', 'required' => [ 'ImageSelectorType', 'StartTimestamp', 'EndTimestamp', 'Format', ], 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'ImageSelectorType' => [ 'shape' => 'ImageSelectorType', ], 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], 'SamplingInterval' => [ 'shape' => 'SamplingInterval', ], 'Format' => [ 'shape' => 'Format', ], 'FormatConfig' => [ 'shape' => 'FormatConfig', ], 'WidthPixels' => [ 'shape' => 'WidthPixels', ], 'HeightPixels' => [ 'shape' => 'HeightPixels', ], 'MaxResults' => [ 'shape' => 'GetImagesMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetImagesMaxResults' => [ 'type' => 'long', 'max' => 100, 'min' => 1, ], 'GetImagesOutput' => [ 'type' => 'structure', 'members' => [ 'Images' => [ 'shape' => 'Images', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMediaForFragmentListInput' => [ 'type' => 'structure', 'required' => [ 'Fragments', ], 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'Fragments' => [ 'shape' => 'FragmentNumberList', ], ], ], 'GetMediaForFragmentListOutput' => [ 'type' => 'structure', 'members' => [ 'ContentType' => [ 'shape' => 'ContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'Payload' => [ 'shape' => 'Payload', ], ], 'payload' => 'Payload', ], 'HLSDiscontinuityMode' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'NEVER', 'ON_DISCONTINUITY', ], ], 'HLSDisplayFragmentTimestamp' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'NEVER', ], ], 'HLSFragmentSelector' => [ 'type' => 'structure', 'members' => [ 'FragmentSelectorType' => [ 'shape' => 'HLSFragmentSelectorType', ], 'TimestampRange' => [ 'shape' => 'HLSTimestampRange', ], ], ], 'HLSFragmentSelectorType' => [ 'type' => 'string', 'enum' => [ 'PRODUCER_TIMESTAMP', 'SERVER_TIMESTAMP', ], ], 'HLSMaxResults' => [ 'type' => 'long', 'max' => 5000, 'min' => 1, ], 'HLSPlaybackMode' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'LIVE_REPLAY', 'ON_DEMAND', ], ], 'HLSStreamingSessionURL' => [ 'type' => 'string', ], 'HLSTimestampRange' => [ 'type' => 'structure', 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'HeightPixels' => [ 'type' => 'integer', 'max' => 2160, 'min' => 1, ], 'Image' => [ 'type' => 'structure', 'members' => [ 'TimeStamp' => [ 'shape' => 'Timestamp', ], 'Error' => [ 'shape' => 'ImageError', ], 'ImageContent' => [ 'shape' => 'ImageContent', ], ], ], 'ImageContent' => [ 'type' => 'string', 'max' => 6291456, 'min' => 1, ], 'ImageError' => [ 'type' => 'string', 'enum' => [ 'NO_MEDIA', 'MEDIA_ERROR', ], ], 'ImageSelectorType' => [ 'type' => 'string', 'enum' => [ 'PRODUCER_TIMESTAMP', 'SERVER_TIMESTAMP', ], ], 'Images' => [ 'type' => 'list', 'member' => [ 'shape' => 'Image', ], ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidCodecPrivateDataException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidMediaFrameException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListFragmentsInput' => [ 'type' => 'structure', 'members' => [ 'StreamName' => [ 'shape' => 'StreamName', ], 'StreamARN' => [ 'shape' => 'ResourceARN', ], 'MaxResults' => [ 'shape' => 'ListFragmentsMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'FragmentSelector' => [ 'shape' => 'FragmentSelector', ], ], ], 'ListFragmentsMaxResults' => [ 'type' => 'long', 'max' => 1000, 'min' => 1, ], 'ListFragmentsOutput' => [ 'type' => 'structure', 'members' => [ 'Fragments' => [ 'shape' => 'FragmentList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', ], 'MissingCodecPrivateDataException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[a-zA-Z0-9+/]+={0,2}', ], 'NoDataRetentionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NotAuthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'Payload' => [ 'type' => 'blob', 'streaming' => true, ], 'ResourceARN' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[a-z\\d-]+:kinesisvideo:[a-z0-9-]+:[0-9]+:[a-z]+/[a-zA-Z0-9_.-]+/[0-9]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'SamplingInterval' => [ 'type' => 'integer', ], 'StreamName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampRange' => [ 'type' => 'structure', 'required' => [ 'StartTimestamp', 'EndTimestamp', ], 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UnsupportedStreamMediaTypeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'WidthPixels' => [ 'type' => 'integer', 'max' => 3840, 'min' => 1, ], ],];
