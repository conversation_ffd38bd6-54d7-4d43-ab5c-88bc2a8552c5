<?php
// This file was auto-generated from sdk-root/src/data/proton/2020-07-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-20', 'endpointPrefix' => 'proton', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceFullName' => 'AWS Proton', 'serviceId' => 'Proton', 'signatureVersion' => 'v4', 'signingName' => 'proton', 'targetPrefix' => 'AwsProton20200720', 'uid' => 'proton-2020-07-20', ], 'operations' => [ 'AcceptEnvironmentAccountConnection' => [ 'name' => 'AcceptEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'AcceptEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CancelComponentDeployment' => [ 'name' => 'CancelComponentDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelComponentDeploymentInput', ], 'output' => [ 'shape' => 'CancelComponentDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelEnvironmentDeployment' => [ 'name' => 'CancelEnvironmentDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelEnvironmentDeploymentInput', ], 'output' => [ 'shape' => 'CancelEnvironmentDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelServiceInstanceDeployment' => [ 'name' => 'CancelServiceInstanceDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelServiceInstanceDeploymentInput', ], 'output' => [ 'shape' => 'CancelServiceInstanceDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelServicePipelineDeployment' => [ 'name' => 'CancelServicePipelineDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelServicePipelineDeploymentInput', ], 'output' => [ 'shape' => 'CancelServicePipelineDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateComponent' => [ 'name' => 'CreateComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateComponentInput', ], 'output' => [ 'shape' => 'CreateComponentOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentInput', ], 'output' => [ 'shape' => 'CreateEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateEnvironmentAccountConnection' => [ 'name' => 'CreateEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'CreateEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateEnvironmentTemplate' => [ 'name' => 'CreateEnvironmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentTemplateInput', ], 'output' => [ 'shape' => 'CreateEnvironmentTemplateOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateEnvironmentTemplateVersion' => [ 'name' => 'CreateEnvironmentTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentTemplateVersionInput', ], 'output' => [ 'shape' => 'CreateEnvironmentTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateRepository' => [ 'name' => 'CreateRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRepositoryInput', ], 'output' => [ 'shape' => 'CreateRepositoryOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateService' => [ 'name' => 'CreateService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServiceInput', ], 'output' => [ 'shape' => 'CreateServiceOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceInstance' => [ 'name' => 'CreateServiceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServiceInstanceInput', ], 'output' => [ 'shape' => 'CreateServiceInstanceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceSyncConfig' => [ 'name' => 'CreateServiceSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServiceSyncConfigInput', ], 'output' => [ 'shape' => 'CreateServiceSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceTemplate' => [ 'name' => 'CreateServiceTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServiceTemplateInput', ], 'output' => [ 'shape' => 'CreateServiceTemplateOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceTemplateVersion' => [ 'name' => 'CreateServiceTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServiceTemplateVersionInput', ], 'output' => [ 'shape' => 'CreateServiceTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTemplateSyncConfig' => [ 'name' => 'CreateTemplateSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTemplateSyncConfigInput', ], 'output' => [ 'shape' => 'CreateTemplateSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteComponent' => [ 'name' => 'DeleteComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteComponentInput', ], 'output' => [ 'shape' => 'DeleteComponentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteDeployment' => [ 'name' => 'DeleteDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeploymentInput', ], 'output' => [ 'shape' => 'DeleteDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentInput', ], 'output' => [ 'shape' => 'DeleteEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentAccountConnection' => [ 'name' => 'DeleteEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'DeleteEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentTemplate' => [ 'name' => 'DeleteEnvironmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentTemplateInput', ], 'output' => [ 'shape' => 'DeleteEnvironmentTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentTemplateVersion' => [ 'name' => 'DeleteEnvironmentTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentTemplateVersionInput', ], 'output' => [ 'shape' => 'DeleteEnvironmentTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteRepository' => [ 'name' => 'DeleteRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRepositoryInput', ], 'output' => [ 'shape' => 'DeleteRepositoryOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteService' => [ 'name' => 'DeleteService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServiceInput', ], 'output' => [ 'shape' => 'DeleteServiceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceSyncConfig' => [ 'name' => 'DeleteServiceSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServiceSyncConfigInput', ], 'output' => [ 'shape' => 'DeleteServiceSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceTemplate' => [ 'name' => 'DeleteServiceTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServiceTemplateInput', ], 'output' => [ 'shape' => 'DeleteServiceTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceTemplateVersion' => [ 'name' => 'DeleteServiceTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServiceTemplateVersionInput', ], 'output' => [ 'shape' => 'DeleteServiceTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTemplateSyncConfig' => [ 'name' => 'DeleteTemplateSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTemplateSyncConfigInput', ], 'output' => [ 'shape' => 'DeleteTemplateSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccountSettingsInput', ], 'output' => [ 'shape' => 'GetAccountSettingsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetComponent' => [ 'name' => 'GetComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComponentInput', ], 'output' => [ 'shape' => 'GetComponentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDeployment' => [ 'name' => 'GetDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeploymentInput', ], 'output' => [ 'shape' => 'GetDeploymentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnvironmentInput', ], 'output' => [ 'shape' => 'GetEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEnvironmentAccountConnection' => [ 'name' => 'GetEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'GetEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEnvironmentTemplate' => [ 'name' => 'GetEnvironmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnvironmentTemplateInput', ], 'output' => [ 'shape' => 'GetEnvironmentTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEnvironmentTemplateVersion' => [ 'name' => 'GetEnvironmentTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnvironmentTemplateVersionInput', ], 'output' => [ 'shape' => 'GetEnvironmentTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRepository' => [ 'name' => 'GetRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryInput', ], 'output' => [ 'shape' => 'GetRepositoryOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRepositorySyncStatus' => [ 'name' => 'GetRepositorySyncStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositorySyncStatusInput', ], 'output' => [ 'shape' => 'GetRepositorySyncStatusOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcesSummary' => [ 'name' => 'GetResourcesSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcesSummaryInput', ], 'output' => [ 'shape' => 'GetResourcesSummaryOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetService' => [ 'name' => 'GetService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceInput', ], 'output' => [ 'shape' => 'GetServiceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceInstance' => [ 'name' => 'GetServiceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceInstanceInput', ], 'output' => [ 'shape' => 'GetServiceInstanceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceInstanceSyncStatus' => [ 'name' => 'GetServiceInstanceSyncStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceInstanceSyncStatusInput', ], 'output' => [ 'shape' => 'GetServiceInstanceSyncStatusOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceSyncBlockerSummary' => [ 'name' => 'GetServiceSyncBlockerSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceSyncBlockerSummaryInput', ], 'output' => [ 'shape' => 'GetServiceSyncBlockerSummaryOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceSyncConfig' => [ 'name' => 'GetServiceSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceSyncConfigInput', ], 'output' => [ 'shape' => 'GetServiceSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceTemplate' => [ 'name' => 'GetServiceTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceTemplateInput', ], 'output' => [ 'shape' => 'GetServiceTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceTemplateVersion' => [ 'name' => 'GetServiceTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceTemplateVersionInput', ], 'output' => [ 'shape' => 'GetServiceTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTemplateSyncConfig' => [ 'name' => 'GetTemplateSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateSyncConfigInput', ], 'output' => [ 'shape' => 'GetTemplateSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTemplateSyncStatus' => [ 'name' => 'GetTemplateSyncStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateSyncStatusInput', ], 'output' => [ 'shape' => 'GetTemplateSyncStatusOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListComponentOutputs' => [ 'name' => 'ListComponentOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComponentOutputsInput', ], 'output' => [ 'shape' => 'ListComponentOutputsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListComponentProvisionedResources' => [ 'name' => 'ListComponentProvisionedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComponentProvisionedResourcesInput', ], 'output' => [ 'shape' => 'ListComponentProvisionedResourcesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListComponents' => [ 'name' => 'ListComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComponentsInput', ], 'output' => [ 'shape' => 'ListComponentsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDeployments' => [ 'name' => 'ListDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeploymentsInput', ], 'output' => [ 'shape' => 'ListDeploymentsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironmentAccountConnections' => [ 'name' => 'ListEnvironmentAccountConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentAccountConnectionsInput', ], 'output' => [ 'shape' => 'ListEnvironmentAccountConnectionsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironmentOutputs' => [ 'name' => 'ListEnvironmentOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentOutputsInput', ], 'output' => [ 'shape' => 'ListEnvironmentOutputsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironmentProvisionedResources' => [ 'name' => 'ListEnvironmentProvisionedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentProvisionedResourcesInput', ], 'output' => [ 'shape' => 'ListEnvironmentProvisionedResourcesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironmentTemplateVersions' => [ 'name' => 'ListEnvironmentTemplateVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentTemplateVersionsInput', ], 'output' => [ 'shape' => 'ListEnvironmentTemplateVersionsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironmentTemplates' => [ 'name' => 'ListEnvironmentTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentTemplatesInput', ], 'output' => [ 'shape' => 'ListEnvironmentTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentsInput', ], 'output' => [ 'shape' => 'ListEnvironmentsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRepositories' => [ 'name' => 'ListRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRepositoriesInput', ], 'output' => [ 'shape' => 'ListRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRepositorySyncDefinitions' => [ 'name' => 'ListRepositorySyncDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRepositorySyncDefinitionsInput', ], 'output' => [ 'shape' => 'ListRepositorySyncDefinitionsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceInstanceOutputs' => [ 'name' => 'ListServiceInstanceOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServiceInstanceOutputsInput', ], 'output' => [ 'shape' => 'ListServiceInstanceOutputsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceInstanceProvisionedResources' => [ 'name' => 'ListServiceInstanceProvisionedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServiceInstanceProvisionedResourcesInput', ], 'output' => [ 'shape' => 'ListServiceInstanceProvisionedResourcesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceInstances' => [ 'name' => 'ListServiceInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServiceInstancesInput', ], 'output' => [ 'shape' => 'ListServiceInstancesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServicePipelineOutputs' => [ 'name' => 'ListServicePipelineOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServicePipelineOutputsInput', ], 'output' => [ 'shape' => 'ListServicePipelineOutputsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServicePipelineProvisionedResources' => [ 'name' => 'ListServicePipelineProvisionedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServicePipelineProvisionedResourcesInput', ], 'output' => [ 'shape' => 'ListServicePipelineProvisionedResourcesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceTemplateVersions' => [ 'name' => 'ListServiceTemplateVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServiceTemplateVersionsInput', ], 'output' => [ 'shape' => 'ListServiceTemplateVersionsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceTemplates' => [ 'name' => 'ListServiceTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServiceTemplatesInput', ], 'output' => [ 'shape' => 'ListServiceTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServices' => [ 'name' => 'ListServices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServicesInput', ], 'output' => [ 'shape' => 'ListServicesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'NotifyResourceDeploymentStatusChange' => [ 'name' => 'NotifyResourceDeploymentStatusChange', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'NotifyResourceDeploymentStatusChangeInput', ], 'output' => [ 'shape' => 'NotifyResourceDeploymentStatusChangeOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RejectEnvironmentAccountConnection' => [ 'name' => 'RejectEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'RejectEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateAccountSettings' => [ 'name' => 'UpdateAccountSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAccountSettingsInput', ], 'output' => [ 'shape' => 'UpdateAccountSettingsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateComponent' => [ 'name' => 'UpdateComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateComponentInput', ], 'output' => [ 'shape' => 'UpdateComponentOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEnvironment' => [ 'name' => 'UpdateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnvironmentInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEnvironmentAccountConnection' => [ 'name' => 'UpdateEnvironmentAccountConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnvironmentAccountConnectionInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentAccountConnectionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateEnvironmentTemplate' => [ 'name' => 'UpdateEnvironmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnvironmentTemplateInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEnvironmentTemplateVersion' => [ 'name' => 'UpdateEnvironmentTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnvironmentTemplateVersionInput', ], 'output' => [ 'shape' => 'UpdateEnvironmentTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateService' => [ 'name' => 'UpdateService', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceInput', ], 'output' => [ 'shape' => 'UpdateServiceOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceInstance' => [ 'name' => 'UpdateServiceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceInstanceInput', ], 'output' => [ 'shape' => 'UpdateServiceInstanceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServicePipeline' => [ 'name' => 'UpdateServicePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServicePipelineInput', ], 'output' => [ 'shape' => 'UpdateServicePipelineOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceSyncBlocker' => [ 'name' => 'UpdateServiceSyncBlocker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceSyncBlockerInput', ], 'output' => [ 'shape' => 'UpdateServiceSyncBlockerOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceSyncConfig' => [ 'name' => 'UpdateServiceSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceSyncConfigInput', ], 'output' => [ 'shape' => 'UpdateServiceSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceTemplate' => [ 'name' => 'UpdateServiceTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceTemplateInput', ], 'output' => [ 'shape' => 'UpdateServiceTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceTemplateVersion' => [ 'name' => 'UpdateServiceTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceTemplateVersionInput', ], 'output' => [ 'shape' => 'UpdateServiceTemplateVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateTemplateSyncConfig' => [ 'name' => 'UpdateTemplateSyncConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTemplateSyncConfigInput', ], 'output' => [ 'shape' => 'UpdateTemplateSyncConfigOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AcceptEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], ], ], 'AcceptEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnection', ], 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'pipelineCodebuildRoleArn' => [ 'shape' => 'RoleArnOrEmptyString', ], 'pipelineProvisioningRepository' => [ 'shape' => 'RepositoryBranch', ], 'pipelineServiceRoleArn' => [ 'shape' => 'RoleArnOrEmptyString', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):[a-zA-Z0-9-]+:[a-zA-Z0-9-]*:\\d{12}:([\\w+=,.@-]+[/:])*[\\w+=,.@-]+$', ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'BlockerStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'RESOLVED', ], ], 'BlockerType' => [ 'type' => 'string', 'enum' => [ 'AUTOMATED', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelComponentDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'componentName', ], 'members' => [ 'componentName' => [ 'shape' => 'ResourceName', ], ], ], 'CancelComponentDeploymentOutput' => [ 'type' => 'structure', 'required' => [ 'component', ], 'members' => [ 'component' => [ 'shape' => 'Component', ], ], ], 'CancelEnvironmentDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'environmentName', ], 'members' => [ 'environmentName' => [ 'shape' => 'ResourceName', ], ], ], 'CancelEnvironmentDeploymentOutput' => [ 'type' => 'structure', 'required' => [ 'environment', ], 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'CancelServiceInstanceDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'serviceInstanceName', 'serviceName', ], 'members' => [ 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'CancelServiceInstanceDeploymentOutput' => [ 'type' => 'structure', 'required' => [ 'serviceInstance', ], 'members' => [ 'serviceInstance' => [ 'shape' => 'ServiceInstance', ], ], ], 'CancelServicePipelineDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'CancelServicePipelineDeploymentOutput' => [ 'type' => 'structure', 'required' => [ 'pipeline', ], 'members' => [ 'pipeline' => [ 'shape' => 'ServicePipeline', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[!-~]*$', ], 'CompatibleEnvironmentTemplate' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CompatibleEnvironmentTemplateInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CompatibleEnvironmentTemplateInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompatibleEnvironmentTemplateInput', ], 'max' => 10, 'min' => 1, ], 'CompatibleEnvironmentTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompatibleEnvironmentTemplate', ], ], 'Component' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastClientRequestToken' => [ 'shape' => 'String', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'serviceSpec' => [ 'shape' => 'SpecContents', ], ], ], 'ComponentArn' => [ 'type' => 'string', ], 'ComponentDeploymentIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentId', ], 'max' => 1, 'min' => 0, ], 'ComponentDeploymentUpdateType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'CURRENT_VERSION', ], ], 'ComponentState' => [ 'type' => 'structure', 'members' => [ 'serviceInstanceName' => [ 'shape' => 'ResourceNameOrEmpty', ], 'serviceName' => [ 'shape' => 'ResourceNameOrEmpty', ], 'serviceSpec' => [ 'shape' => 'SpecContents', ], 'templateFile' => [ 'shape' => 'TemplateFileContents', ], ], ], 'ComponentSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ComponentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentSummary', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CountsSummary' => [ 'type' => 'structure', 'members' => [ 'components' => [ 'shape' => 'ResourceCountsSummary', ], 'environmentTemplates' => [ 'shape' => 'ResourceCountsSummary', ], 'environments' => [ 'shape' => 'ResourceCountsSummary', ], 'pipelines' => [ 'shape' => 'ResourceCountsSummary', ], 'serviceInstances' => [ 'shape' => 'ResourceCountsSummary', ], 'serviceTemplates' => [ 'shape' => 'ResourceCountsSummary', ], 'services' => [ 'shape' => 'ResourceCountsSummary', ], ], ], 'CreateComponentInput' => [ 'type' => 'structure', 'required' => [ 'manifest', 'name', 'templateFile', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'manifest' => [ 'shape' => 'TemplateManifestContents', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'serviceSpec' => [ 'shape' => 'SpecContents', ], 'tags' => [ 'shape' => 'TagList', ], 'templateFile' => [ 'shape' => 'TemplateFileContents', ], ], ], 'CreateComponentOutput' => [ 'type' => 'structure', 'required' => [ 'component', ], 'members' => [ 'component' => [ 'shape' => 'Component', ], ], ], 'CreateEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'environmentName', 'managementAccountId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'managementAccountId' => [ 'shape' => 'AwsAccountId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnection', ], 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'CreateEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'name', 'spec', 'templateMajorVersion', 'templateName', ], 'members' => [ 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'description' => [ 'shape' => 'Description', ], 'environmentAccountConnectionId' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'name' => [ 'shape' => 'ResourceName', ], 'protonServiceRoleArn' => [ 'shape' => 'Arn', ], 'provisioningRepository' => [ 'shape' => 'RepositoryBranchInput', ], 'spec' => [ 'shape' => 'SpecContents', ], 'tags' => [ 'shape' => 'TagList', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'environment', ], 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'CreateEnvironmentTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'ResourceName', ], 'provisioning' => [ 'shape' => 'Provisioning', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEnvironmentTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplate', ], 'members' => [ 'environmentTemplate' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'CreateEnvironmentTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'source', 'templateName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'source' => [ 'shape' => 'TemplateVersionSourceInput', ], 'tags' => [ 'shape' => 'TagList', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateEnvironmentTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplateVersion', ], 'members' => [ 'environmentTemplateVersion' => [ 'shape' => 'EnvironmentTemplateVersion', ], ], ], 'CreateRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'connectionArn', 'name', 'provider', ], 'members' => [ 'connectionArn' => [ 'shape' => 'Arn', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRepositoryOutput' => [ 'type' => 'structure', 'required' => [ 'repository', ], 'members' => [ 'repository' => [ 'shape' => 'Repository', ], ], ], 'CreateServiceInput' => [ 'type' => 'structure', 'required' => [ 'name', 'spec', 'templateMajorVersion', 'templateName', ], 'members' => [ 'branchName' => [ 'shape' => 'GitBranchName', ], 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'ResourceName', ], 'repositoryConnectionArn' => [ 'shape' => 'Arn', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'spec' => [ 'shape' => 'SpecContents', ], 'tags' => [ 'shape' => 'TagList', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateServiceInstanceInput' => [ 'type' => 'structure', 'required' => [ 'name', 'serviceName', 'spec', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'SpecContents', ], 'tags' => [ 'shape' => 'TagList', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], ], ], 'CreateServiceInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'serviceInstance', ], 'members' => [ 'serviceInstance' => [ 'shape' => 'ServiceInstance', ], ], ], 'CreateServiceOutput' => [ 'type' => 'structure', 'required' => [ 'service', ], 'members' => [ 'service' => [ 'shape' => 'Service', ], ], ], 'CreateServiceSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'filePath', 'repositoryName', 'repositoryProvider', 'serviceName', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'filePath' => [ 'shape' => 'OpsFilePath', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateServiceSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'serviceSyncConfig' => [ 'shape' => 'ServiceSyncConfig', ], ], ], 'CreateServiceTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'ResourceName', ], 'pipelineProvisioning' => [ 'shape' => 'Provisioning', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateServiceTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplate', ], 'members' => [ 'serviceTemplate' => [ 'shape' => 'ServiceTemplate', ], ], ], 'CreateServiceTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'compatibleEnvironmentTemplates', 'source', 'templateName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'compatibleEnvironmentTemplates' => [ 'shape' => 'CompatibleEnvironmentTemplateInputList', ], 'description' => [ 'shape' => 'Description', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'source' => [ 'shape' => 'TemplateVersionSourceInput', ], 'supportedComponentSources' => [ 'shape' => 'ServiceTemplateSupportedComponentSourceInputList', ], 'tags' => [ 'shape' => 'TagList', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateServiceTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplateVersion', ], 'members' => [ 'serviceTemplateVersion' => [ 'shape' => 'ServiceTemplateVersion', ], ], ], 'CreateTemplateSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'repositoryName', 'repositoryProvider', 'templateName', 'templateType', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'subdirectory' => [ 'shape' => 'Subdirectory', ], 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], ], ], 'CreateTemplateSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'templateSyncConfig' => [ 'shape' => 'TemplateSyncConfig', ], ], ], 'DeleteComponentInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteComponentOutput' => [ 'type' => 'structure', 'members' => [ 'component' => [ 'shape' => 'Component', ], ], ], 'DeleteDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'DeploymentId', ], ], ], 'DeleteDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'deployment' => [ 'shape' => 'Deployment', ], ], ], 'DeleteEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], ], ], 'DeleteEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'DeleteEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteEnvironmentOutput' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'DeleteEnvironmentTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteEnvironmentTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'environmentTemplate' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'DeleteEnvironmentTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteEnvironmentTemplateVersionOutput' => [ 'type' => 'structure', 'members' => [ 'environmentTemplateVersion' => [ 'shape' => 'EnvironmentTemplateVersion', ], ], ], 'DeleteRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'name', 'provider', ], 'members' => [ 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'DeleteRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'Repository', ], ], ], 'DeleteServiceInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteServiceOutput' => [ 'type' => 'structure', 'members' => [ 'service' => [ 'shape' => 'Service', ], ], ], 'DeleteServiceSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteServiceSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'serviceSyncConfig' => [ 'shape' => 'ServiceSyncConfig', ], ], ], 'DeleteServiceTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteServiceTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'serviceTemplate' => [ 'shape' => 'ServiceTemplate', ], ], ], 'DeleteServiceTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteServiceTemplateVersionOutput' => [ 'type' => 'structure', 'members' => [ 'serviceTemplateVersion' => [ 'shape' => 'ServiceTemplateVersion', ], ], ], 'DeleteTemplateSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateType', ], 'members' => [ 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], ], ], 'DeleteTemplateSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'templateSyncConfig' => [ 'shape' => 'TemplateSyncConfig', ], ], ], 'Deployment' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'id', 'lastModifiedAt', 'targetArn', 'targetResourceCreatedAt', 'targetResourceType', ], 'members' => [ 'arn' => [ 'shape' => 'DeploymentArn', ], 'completedAt' => [ 'shape' => 'Timestamp', ], 'componentName' => [ 'shape' => 'ResourceName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'id' => [ 'shape' => 'DeploymentId', ], 'initialState' => [ 'shape' => 'DeploymentState', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'targetArn' => [ 'shape' => 'Arn', ], 'targetResourceCreatedAt' => [ 'shape' => 'Timestamp', ], 'targetResourceType' => [ 'shape' => 'DeploymentTargetResourceType', ], 'targetState' => [ 'shape' => 'DeploymentState', ], ], ], 'DeploymentArn' => [ 'type' => 'string', ], 'DeploymentId' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'DeploymentState' => [ 'type' => 'structure', 'members' => [ 'component' => [ 'shape' => 'ComponentState', ], 'environment' => [ 'shape' => 'EnvironmentState', ], 'serviceInstance' => [ 'shape' => 'ServiceInstanceState', ], 'servicePipeline' => [ 'shape' => 'ServicePipelineState', ], ], 'union' => true, ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETE_COMPLETE', 'CANCELLING', 'CANCELLED', ], ], 'DeploymentSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'id', 'lastModifiedAt', 'targetArn', 'targetResourceCreatedAt', 'targetResourceType', ], 'members' => [ 'arn' => [ 'shape' => 'DeploymentArn', ], 'completedAt' => [ 'shape' => 'Timestamp', ], 'componentName' => [ 'shape' => 'ResourceName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'id' => [ 'shape' => 'DeploymentId', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'targetArn' => [ 'shape' => 'Arn', ], 'targetResourceCreatedAt' => [ 'shape' => 'Timestamp', ], 'targetResourceType' => [ 'shape' => 'DeploymentTargetResourceType', ], ], ], 'DeploymentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentSummary', ], ], 'DeploymentTargetResourceType' => [ 'type' => 'string', 'enum' => [ 'ENVIRONMENT', 'SERVICE_PIPELINE', 'SERVICE_INSTANCE', 'COMPONENT', ], ], 'DeploymentUpdateType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'CURRENT_VERSION', 'MINOR_VERSION', 'MAJOR_VERSION', ], ], 'Description' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'DisplayName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'EmptyNextToken' => [ 'type' => 'string', 'max' => 0, 'min' => 0, ], 'Environment' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'lastDeploymentAttemptedAt', 'lastDeploymentSucceededAt', 'name', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentArn', ], 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentAccountConnectionId' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'environmentAccountId' => [ 'shape' => 'AwsAccountId', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'protonServiceRoleArn' => [ 'shape' => 'Arn', ], 'provisioning' => [ 'shape' => 'Provisioning', ], 'provisioningRepository' => [ 'shape' => 'RepositoryBranch', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentAccountConnection' => [ 'type' => 'structure', 'required' => [ 'arn', 'environmentAccountId', 'environmentName', 'id', 'lastModifiedAt', 'managementAccountId', 'requestedAt', 'roleArn', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentAccountConnectionArn', ], 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'environmentAccountId' => [ 'shape' => 'AwsAccountId', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'managementAccountId' => [ 'shape' => 'AwsAccountId', ], 'requestedAt' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'EnvironmentAccountConnectionStatus', ], ], ], 'EnvironmentAccountConnectionArn' => [ 'type' => 'string', ], 'EnvironmentAccountConnectionId' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'EnvironmentAccountConnectionRequesterAccountType' => [ 'type' => 'string', 'enum' => [ 'MANAGEMENT_ACCOUNT', 'ENVIRONMENT_ACCOUNT', ], ], 'EnvironmentAccountConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CONNECTED', 'REJECTED', ], ], 'EnvironmentAccountConnectionStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentAccountConnectionStatus', ], ], 'EnvironmentAccountConnectionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'environmentAccountId', 'environmentName', 'id', 'lastModifiedAt', 'managementAccountId', 'requestedAt', 'roleArn', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentAccountConnectionArn', ], 'componentRoleArn' => [ 'shape' => 'Arn', ], 'environmentAccountId' => [ 'shape' => 'AwsAccountId', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'managementAccountId' => [ 'shape' => 'AwsAccountId', ], 'requestedAt' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'EnvironmentAccountConnectionStatus', ], ], ], 'EnvironmentAccountConnectionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentAccountConnectionSummary', ], ], 'EnvironmentArn' => [ 'type' => 'string', ], 'EnvironmentState' => [ 'type' => 'structure', 'required' => [ 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'lastDeploymentAttemptedAt', 'lastDeploymentSucceededAt', 'name', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentArn', ], 'componentRoleArn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentAccountConnectionId' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'environmentAccountId' => [ 'shape' => 'AwsAccountId', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'protonServiceRoleArn' => [ 'shape' => 'Arn', ], 'provisioning' => [ 'shape' => 'Provisioning', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentSummary', ], ], 'EnvironmentTemplate' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentTemplateArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'provisioning' => [ 'shape' => 'Provisioning', ], 'recommendedVersion' => [ 'shape' => 'FullTemplateVersionNumber', ], ], ], 'EnvironmentTemplateArn' => [ 'type' => 'string', ], 'EnvironmentTemplateFilter' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentTemplateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentTemplateFilter', ], ], 'EnvironmentTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentTemplateArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'provisioning' => [ 'shape' => 'Provisioning', ], 'recommendedVersion' => [ 'shape' => 'FullTemplateVersionNumber', ], ], ], 'EnvironmentTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentTemplateSummary', ], ], 'EnvironmentTemplateVersion' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'majorVersion', 'minorVersion', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentTemplateVersionArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'recommendedMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'schema' => [ 'shape' => 'TemplateSchema', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentTemplateVersionArn' => [ 'type' => 'string', ], 'EnvironmentTemplateVersionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'majorVersion', 'minorVersion', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'EnvironmentTemplateVersionArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'recommendedMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'EnvironmentTemplateVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentTemplateVersionSummary', ], ], 'ErrorMessage' => [ 'type' => 'string', 'sensitive' => true, ], 'FullTemplateVersionNumber' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '^(0|([1-9]{1}\\d*)).(0|([1-9]{1}\\d*))$', ], 'GetAccountSettingsInput' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'accountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetComponentInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'GetComponentOutput' => [ 'type' => 'structure', 'members' => [ 'component' => [ 'shape' => 'Component', ], ], ], 'GetDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'componentName' => [ 'shape' => 'ResourceName', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'id' => [ 'shape' => 'DeploymentId', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'deployment' => [ 'shape' => 'Deployment', ], ], ], 'GetEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], ], ], 'GetEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnection', ], 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'GetEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'GetEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'environment', ], 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'GetEnvironmentTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'GetEnvironmentTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplate', ], 'members' => [ 'environmentTemplate' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'GetEnvironmentTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'GetEnvironmentTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplateVersion', ], 'members' => [ 'environmentTemplateVersion' => [ 'shape' => 'EnvironmentTemplateVersion', ], ], ], 'GetRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'name', 'provider', ], 'members' => [ 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'GetRepositoryOutput' => [ 'type' => 'structure', 'required' => [ 'repository', ], 'members' => [ 'repository' => [ 'shape' => 'Repository', ], ], ], 'GetRepositorySyncStatusInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'repositoryName', 'repositoryProvider', 'syncType', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'syncType' => [ 'shape' => 'SyncType', ], ], ], 'GetRepositorySyncStatusOutput' => [ 'type' => 'structure', 'members' => [ 'latestSync' => [ 'shape' => 'RepositorySyncAttempt', ], ], ], 'GetResourcesSummaryInput' => [ 'type' => 'structure', 'members' => [], ], 'GetResourcesSummaryOutput' => [ 'type' => 'structure', 'required' => [ 'counts', ], 'members' => [ 'counts' => [ 'shape' => 'CountsSummary', ], ], ], 'GetServiceInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceInstanceInput' => [ 'type' => 'structure', 'required' => [ 'name', 'serviceName', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'serviceInstance', ], 'members' => [ 'serviceInstance' => [ 'shape' => 'ServiceInstance', ], ], ], 'GetServiceInstanceSyncStatusInput' => [ 'type' => 'structure', 'required' => [ 'serviceInstanceName', 'serviceName', ], 'members' => [ 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceInstanceSyncStatusOutput' => [ 'type' => 'structure', 'members' => [ 'desiredState' => [ 'shape' => 'Revision', ], 'latestSuccessfulSync' => [ 'shape' => 'ResourceSyncAttempt', ], 'latestSync' => [ 'shape' => 'ResourceSyncAttempt', ], ], ], 'GetServiceOutput' => [ 'type' => 'structure', 'members' => [ 'service' => [ 'shape' => 'Service', ], ], ], 'GetServiceSyncBlockerSummaryInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceSyncBlockerSummaryOutput' => [ 'type' => 'structure', 'members' => [ 'serviceSyncBlockerSummary' => [ 'shape' => 'ServiceSyncBlockerSummary', ], ], ], 'GetServiceSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'serviceSyncConfig' => [ 'shape' => 'ServiceSyncConfig', ], ], ], 'GetServiceTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplate', ], 'members' => [ 'serviceTemplate' => [ 'shape' => 'ServiceTemplate', ], ], ], 'GetServiceTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'GetServiceTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplateVersion', ], 'members' => [ 'serviceTemplateVersion' => [ 'shape' => 'ServiceTemplateVersion', ], ], ], 'GetTemplateSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateType', ], 'members' => [ 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], ], ], 'GetTemplateSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'templateSyncConfig' => [ 'shape' => 'TemplateSyncConfig', ], ], ], 'GetTemplateSyncStatusInput' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateType', 'templateVersion', ], 'members' => [ 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], 'templateVersion' => [ 'shape' => 'TemplateVersionPart', ], ], ], 'GetTemplateSyncStatusOutput' => [ 'type' => 'structure', 'members' => [ 'desiredState' => [ 'shape' => 'Revision', ], 'latestSuccessfulSync' => [ 'shape' => 'ResourceSyncAttempt', ], 'latestSync' => [ 'shape' => 'ResourceSyncAttempt', ], ], ], 'GitBranchName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'LatestSyncBlockers' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyncBlocker', ], ], 'ListComponentOutputsInput' => [ 'type' => 'structure', 'required' => [ 'componentName', ], 'members' => [ 'componentName' => [ 'shape' => 'ResourceName', ], 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], ], ], 'ListComponentOutputsOutput' => [ 'type' => 'structure', 'required' => [ 'outputs', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'outputs' => [ 'shape' => 'OutputsList', ], ], ], 'ListComponentProvisionedResourcesInput' => [ 'type' => 'structure', 'required' => [ 'componentName', ], 'members' => [ 'componentName' => [ 'shape' => 'ResourceName', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], ], ], 'ListComponentProvisionedResourcesOutput' => [ 'type' => 'structure', 'required' => [ 'provisionedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'provisionedResources' => [ 'shape' => 'ProvisionedResourceList', ], ], ], 'ListComponentsInput' => [ 'type' => 'structure', 'members' => [ 'environmentName' => [ 'shape' => 'ResourceName', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListComponentsOutput' => [ 'type' => 'structure', 'required' => [ 'components', ], 'members' => [ 'components' => [ 'shape' => 'ComponentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentsInput' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'ResourceName', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListDeploymentsOutput' => [ 'type' => 'structure', 'required' => [ 'deployments', ], 'members' => [ 'deployments' => [ 'shape' => 'DeploymentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentAccountConnectionsInput' => [ 'type' => 'structure', 'required' => [ 'requestedBy', ], 'members' => [ 'environmentName' => [ 'shape' => 'ResourceName', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'requestedBy' => [ 'shape' => 'EnvironmentAccountConnectionRequesterAccountType', ], 'statuses' => [ 'shape' => 'EnvironmentAccountConnectionStatusList', ], ], ], 'ListEnvironmentAccountConnectionsOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnections', ], 'members' => [ 'environmentAccountConnections' => [ 'shape' => 'EnvironmentAccountConnectionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentOutputsInput' => [ 'type' => 'structure', 'required' => [ 'environmentName', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], ], ], 'ListEnvironmentOutputsOutput' => [ 'type' => 'structure', 'required' => [ 'outputs', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'outputs' => [ 'shape' => 'OutputsList', ], ], ], 'ListEnvironmentProvisionedResourcesInput' => [ 'type' => 'structure', 'required' => [ 'environmentName', ], 'members' => [ 'environmentName' => [ 'shape' => 'ResourceName', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], ], ], 'ListEnvironmentProvisionedResourcesOutput' => [ 'type' => 'structure', 'required' => [ 'provisionedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'provisionedResources' => [ 'shape' => 'ProvisionedResourceList', ], ], ], 'ListEnvironmentTemplateVersionsInput' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ListEnvironmentTemplateVersionsOutput' => [ 'type' => 'structure', 'required' => [ 'templateVersions', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templateVersions' => [ 'shape' => 'EnvironmentTemplateVersionSummaryList', ], ], ], 'ListEnvironmentTemplatesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'templates', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templates' => [ 'shape' => 'EnvironmentTemplateSummaryList', ], ], ], 'ListEnvironmentsInput' => [ 'type' => 'structure', 'members' => [ 'environmentTemplates' => [ 'shape' => 'EnvironmentTemplateFilterList', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentsOutput' => [ 'type' => 'structure', 'required' => [ 'environments', ], 'members' => [ 'environments' => [ 'shape' => 'EnvironmentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRepositoriesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRepositoriesOutput' => [ 'type' => 'structure', 'required' => [ 'repositories', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'repositories' => [ 'shape' => 'RepositorySummaryList', ], ], ], 'ListRepositorySyncDefinitionsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'repositoryProvider', 'syncType', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'syncType' => [ 'shape' => 'SyncType', ], ], ], 'ListRepositorySyncDefinitionsOutput' => [ 'type' => 'structure', 'required' => [ 'syncDefinitions', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'syncDefinitions' => [ 'shape' => 'RepositorySyncDefinitionList', ], ], ], 'ListServiceInstanceOutputsInput' => [ 'type' => 'structure', 'required' => [ 'serviceInstanceName', 'serviceName', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListServiceInstanceOutputsOutput' => [ 'type' => 'structure', 'required' => [ 'outputs', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'outputs' => [ 'shape' => 'OutputsList', ], ], ], 'ListServiceInstanceProvisionedResourcesInput' => [ 'type' => 'structure', 'required' => [ 'serviceInstanceName', 'serviceName', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListServiceInstanceProvisionedResourcesOutput' => [ 'type' => 'structure', 'required' => [ 'provisionedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'provisionedResources' => [ 'shape' => 'ProvisionedResourceList', ], ], ], 'ListServiceInstancesFilter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'ListServiceInstancesFilterBy', ], 'value' => [ 'shape' => 'ListServiceInstancesFilterValue', ], ], ], 'ListServiceInstancesFilterBy' => [ 'type' => 'string', 'enum' => [ 'name', 'deploymentStatus', 'templateName', 'serviceName', 'deployedTemplateVersionStatus', 'environmentName', 'lastDeploymentAttemptedAtBefore', 'lastDeploymentAttemptedAtAfter', 'createdAtBefore', 'createdAtAfter', ], ], 'ListServiceInstancesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListServiceInstancesFilter', ], ], 'ListServiceInstancesFilterValue' => [ 'type' => 'string', ], 'ListServiceInstancesInput' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'ListServiceInstancesFilterList', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'sortBy' => [ 'shape' => 'ListServiceInstancesSortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListServiceInstancesOutput' => [ 'type' => 'structure', 'required' => [ 'serviceInstances', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'serviceInstances' => [ 'shape' => 'ServiceInstanceSummaryList', ], ], ], 'ListServiceInstancesSortBy' => [ 'type' => 'string', 'enum' => [ 'name', 'deploymentStatus', 'templateName', 'serviceName', 'environmentName', 'lastDeploymentAttemptedAt', 'createdAt', ], ], 'ListServicePipelineOutputsInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListServicePipelineOutputsOutput' => [ 'type' => 'structure', 'required' => [ 'outputs', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'outputs' => [ 'shape' => 'OutputsList', ], ], ], 'ListServicePipelineProvisionedResourcesInput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ListServicePipelineProvisionedResourcesOutput' => [ 'type' => 'structure', 'required' => [ 'provisionedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'EmptyNextToken', ], 'provisionedResources' => [ 'shape' => 'ProvisionedResourceList', ], ], ], 'ListServiceTemplateVersionsInput' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ListServiceTemplateVersionsOutput' => [ 'type' => 'structure', 'required' => [ 'templateVersions', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templateVersions' => [ 'shape' => 'ServiceTemplateVersionSummaryList', ], ], ], 'ListServiceTemplatesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'templates', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'templates' => [ 'shape' => 'ServiceTemplateSummaryList', ], ], ], 'ListServicesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServicesOutput' => [ 'type' => 'structure', 'required' => [ 'services', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'services' => [ 'shape' => 'ServiceSummaryList', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxPageResults', ], 'nextToken' => [ 'shape' => 'String', ], 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'MaxPageResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9+=/]+$', ], 'NotifyResourceDeploymentStatusChangeInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentId', ], 'outputs' => [ 'shape' => 'NotifyResourceDeploymentStatusChangeInputOutputsList', ], 'resourceArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'ResourceDeploymentStatus', ], 'statusMessage' => [ 'shape' => 'NotifyResourceDeploymentStatusChangeInputStatusMessageString', ], ], ], 'NotifyResourceDeploymentStatusChangeInputOutputsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], 'max' => 50, 'min' => 0, ], 'NotifyResourceDeploymentStatusChangeInputStatusMessageString' => [ 'type' => 'string', 'max' => 5000, 'min' => 0, 'sensitive' => true, ], 'NotifyResourceDeploymentStatusChangeOutput' => [ 'type' => 'structure', 'members' => [], ], 'OpsFilePath' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Output' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'OutputKey', ], 'valueString' => [ 'shape' => 'OutputValueString', ], ], 'sensitive' => true, ], 'OutputKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'OutputValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'OutputsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], 'ProvisionedResource' => [ 'type' => 'structure', 'members' => [ 'identifier' => [ 'shape' => 'ProvisionedResourceIdentifier', ], 'name' => [ 'shape' => 'ProvisionedResourceName', ], 'provisioningEngine' => [ 'shape' => 'ProvisionedResourceEngine', ], ], ], 'ProvisionedResourceEngine' => [ 'type' => 'string', 'enum' => [ 'CLOUDFORMATION', 'TERRAFORM', ], ], 'ProvisionedResourceIdentifier' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'ProvisionedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisionedResource', ], ], 'ProvisionedResourceName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'Provisioning' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_MANAGED', ], ], 'RejectEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], ], ], 'RejectEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnection', ], 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'Repository' => [ 'type' => 'structure', 'required' => [ 'arn', 'connectionArn', 'name', 'provider', ], 'members' => [ 'arn' => [ 'shape' => 'RepositoryArn', ], 'connectionArn' => [ 'shape' => 'Arn', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'RepositoryArn' => [ 'type' => 'string', ], 'RepositoryBranch' => [ 'type' => 'structure', 'required' => [ 'arn', 'branch', 'name', 'provider', ], 'members' => [ 'arn' => [ 'shape' => 'RepositoryArn', ], 'branch' => [ 'shape' => 'GitBranchName', ], 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'RepositoryBranchInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'name', 'provider', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'RepositoryId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'RepositoryName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9_.-].*/[A-Za-z0-9_.-].*', ], 'RepositoryProvider' => [ 'type' => 'string', 'enum' => [ 'GITHUB', 'GITHUB_ENTERPRISE', 'BITBUCKET', ], ], 'RepositorySummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'connectionArn', 'name', 'provider', ], 'members' => [ 'arn' => [ 'shape' => 'RepositoryArn', ], 'connectionArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'RepositoryName', ], 'provider' => [ 'shape' => 'RepositoryProvider', ], ], ], 'RepositorySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositorySummary', ], ], 'RepositorySyncAttempt' => [ 'type' => 'structure', 'required' => [ 'events', 'startedAt', 'status', ], 'members' => [ 'events' => [ 'shape' => 'RepositorySyncEvents', ], 'startedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'RepositorySyncStatus', ], ], ], 'RepositorySyncDefinition' => [ 'type' => 'structure', 'required' => [ 'branch', 'directory', 'parent', 'target', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'directory' => [ 'shape' => 'String', ], 'parent' => [ 'shape' => 'String', ], 'target' => [ 'shape' => 'String', ], ], ], 'RepositorySyncDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositorySyncDefinition', ], ], 'RepositorySyncEvent' => [ 'type' => 'structure', 'required' => [ 'event', 'time', 'type', ], 'members' => [ 'event' => [ 'shape' => 'String', ], 'externalId' => [ 'shape' => 'String', ], 'time' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'String', ], ], ], 'RepositorySyncEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositorySyncEvent', ], ], 'RepositorySyncStatus' => [ 'type' => 'string', 'enum' => [ 'INITIATED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'QUEUED', ], ], 'ResourceCountsSummary' => [ 'type' => 'structure', 'required' => [ 'total', ], 'members' => [ 'behindMajor' => [ 'shape' => 'Integer', ], 'behindMinor' => [ 'shape' => 'Integer', ], 'failed' => [ 'shape' => 'Integer', ], 'total' => [ 'shape' => 'Integer', ], 'upToDate' => [ 'shape' => 'Integer', ], ], ], 'ResourceDeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9A-Za-z]+[0-9A-Za-z_\\-]*$', ], 'ResourceNameOrEmpty' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '(^$)|^[0-9A-Za-z]+[0-9A-Za-z_\\-]*$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceSyncAttempt' => [ 'type' => 'structure', 'required' => [ 'events', 'initialRevision', 'startedAt', 'status', 'target', 'targetRevision', ], 'members' => [ 'events' => [ 'shape' => 'ResourceSyncEvents', ], 'initialRevision' => [ 'shape' => 'Revision', ], 'startedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ResourceSyncStatus', ], 'target' => [ 'shape' => 'String', ], 'targetRevision' => [ 'shape' => 'Revision', ], ], ], 'ResourceSyncEvent' => [ 'type' => 'structure', 'required' => [ 'event', 'time', 'type', ], 'members' => [ 'event' => [ 'shape' => 'String', ], 'externalId' => [ 'shape' => 'String', ], 'time' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'String', ], ], ], 'ResourceSyncEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceSyncEvent', ], ], 'ResourceSyncStatus' => [ 'type' => 'string', 'enum' => [ 'INITIATED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'Revision' => [ 'type' => 'structure', 'required' => [ 'branch', 'directory', 'repositoryName', 'repositoryProvider', 'sha', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'directory' => [ 'shape' => 'String', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'sha' => [ 'shape' => 'SHA', ], ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):iam::\\d{12}:role/([\\w+=,.@-]{1,512}[/:])*([\\w+=,.@-]{1,64})$', ], 'RoleArnOrEmptyString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(^$)|(^arn:(aws|aws-cn|aws-us-gov):iam::\\d{12}:role/([\\w+=,.@-]{1,512}[/:])*([\\w+=,.@-]{1,64})$)', ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9]+[a-z0-9-\\.]+[a-z0-9]+$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3ObjectSource' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3Bucket', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'SHA' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Service' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', 'spec', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'branchName' => [ 'shape' => 'GitBranchName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'pipeline' => [ 'shape' => 'ServicePipeline', ], 'repositoryConnectionArn' => [ 'shape' => 'Arn', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'spec' => [ 'shape' => 'SpecContents', ], 'status' => [ 'shape' => 'ServiceStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceArn' => [ 'type' => 'string', ], 'ServiceInstance' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'lastDeploymentAttemptedAt', 'lastDeploymentSucceededAt', 'name', 'serviceName', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceInstanceArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastClientRequestToken' => [ 'shape' => 'String', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceInstanceArn' => [ 'type' => 'string', ], 'ServiceInstanceState' => [ 'type' => 'structure', 'required' => [ 'spec', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'lastSuccessfulComponentDeploymentIds' => [ 'shape' => 'ComponentDeploymentIdList', ], 'lastSuccessfulEnvironmentDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastSuccessfulServicePipelineDeploymentId' => [ 'shape' => 'DeploymentId', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceInstanceSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'environmentName', 'lastDeploymentAttemptedAt', 'lastDeploymentSucceededAt', 'name', 'serviceName', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceInstanceArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'environmentName' => [ 'shape' => 'ResourceName', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceInstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceInstanceSummary', ], ], 'ServicePipeline' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'deploymentStatus', 'lastDeploymentAttemptedAt', 'lastDeploymentSucceededAt', 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'deploymentStatusMessage' => [ 'shape' => 'StatusMessage', ], 'lastAttemptedDeploymentId' => [ 'shape' => 'DeploymentId', ], 'lastDeploymentAttemptedAt' => [ 'shape' => 'Timestamp', ], 'lastDeploymentSucceededAt' => [ 'shape' => 'Timestamp', ], 'lastSucceededDeploymentId' => [ 'shape' => 'DeploymentId', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServicePipelineState' => [ 'type' => 'structure', 'required' => [ 'templateMajorVersion', 'templateMinorVersion', 'templateName', ], 'members' => [ 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ServiceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED_CLEANUP_IN_PROGRESS', 'CREATE_FAILED_CLEANUP_COMPLETE', 'CREATE_FAILED_CLEANUP_FAILED', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED_CLEANUP_IN_PROGRESS', 'UPDATE_FAILED_CLEANUP_COMPLETE', 'UPDATE_FAILED_CLEANUP_FAILED', 'UPDATE_FAILED', 'UPDATE_COMPLETE_CLEANUP_FAILED', ], ], 'ServiceSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'status' => [ 'shape' => 'ServiceStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceSummary', ], ], 'ServiceSyncBlockerSummary' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'latestBlockers' => [ 'shape' => 'LatestSyncBlockers', ], 'serviceInstanceName' => [ 'shape' => 'String', ], 'serviceName' => [ 'shape' => 'String', ], ], ], 'ServiceSyncConfig' => [ 'type' => 'structure', 'required' => [ 'branch', 'filePath', 'repositoryName', 'repositoryProvider', 'serviceName', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'filePath' => [ 'shape' => 'OpsFilePath', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceTemplate' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceTemplateArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'encryptionKey' => [ 'shape' => 'Arn', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'pipelineProvisioning' => [ 'shape' => 'Provisioning', ], 'recommendedVersion' => [ 'shape' => 'FullTemplateVersionNumber', ], ], ], 'ServiceTemplateArn' => [ 'type' => 'string', ], 'ServiceTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceTemplateArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceName', ], 'pipelineProvisioning' => [ 'shape' => 'Provisioning', ], 'recommendedVersion' => [ 'shape' => 'FullTemplateVersionNumber', ], ], ], 'ServiceTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceTemplateSummary', ], ], 'ServiceTemplateSupportedComponentSourceInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceTemplateSupportedComponentSourceType', ], ], 'ServiceTemplateSupportedComponentSourceType' => [ 'type' => 'string', 'enum' => [ 'DIRECTLY_DEFINED', ], ], 'ServiceTemplateVersion' => [ 'type' => 'structure', 'required' => [ 'arn', 'compatibleEnvironmentTemplates', 'createdAt', 'lastModifiedAt', 'majorVersion', 'minorVersion', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceTemplateVersionArn', ], 'compatibleEnvironmentTemplates' => [ 'shape' => 'CompatibleEnvironmentTemplateList', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'recommendedMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'schema' => [ 'shape' => 'TemplateSchema', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'supportedComponentSources' => [ 'shape' => 'ServiceTemplateSupportedComponentSourceInputList', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceTemplateVersionArn' => [ 'type' => 'string', ], 'ServiceTemplateVersionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'lastModifiedAt', 'majorVersion', 'minorVersion', 'status', 'templateName', ], 'members' => [ 'arn' => [ 'shape' => 'ServiceTemplateVersionArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'recommendedMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'ServiceTemplateVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceTemplateVersionSummary', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SpecContents' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, 'sensitive' => true, ], 'StatusMessage' => [ 'type' => 'string', 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'Subdirectory' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'SyncBlocker' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'createdReason', 'id', 'status', 'type', ], 'members' => [ 'contexts' => [ 'shape' => 'SyncBlockerContexts', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdReason' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'resolvedAt' => [ 'shape' => 'Timestamp', ], 'resolvedReason' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'BlockerStatus', ], 'type' => [ 'shape' => 'BlockerType', ], ], ], 'SyncBlockerContext' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'SyncBlockerContexts' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyncBlockerContext', ], ], 'SyncType' => [ 'type' => 'string', 'enum' => [ 'TEMPLATE_SYNC', 'SERVICE_SYNC', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TemplateFileContents' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, 'sensitive' => true, ], 'TemplateManifestContents' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'TemplateSchema' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, 'sensitive' => true, ], 'TemplateSyncConfig' => [ 'type' => 'structure', 'required' => [ 'branch', 'repositoryName', 'repositoryProvider', 'templateName', 'templateType', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'subdirectory' => [ 'shape' => 'Subdirectory', ], 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], ], ], 'TemplateType' => [ 'type' => 'string', 'enum' => [ 'ENVIRONMENT', 'SERVICE', ], ], 'TemplateVersionPart' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^(0|([1-9]{1}\\d*))$', ], 'TemplateVersionSourceInput' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'S3ObjectSource', ], ], 'union' => true, ], 'TemplateVersionStatus' => [ 'type' => 'string', 'enum' => [ 'REGISTRATION_IN_PROGRESS', 'REGISTRATION_FAILED', 'DRAFT', 'PUBLISHED', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccountSettingsInput' => [ 'type' => 'structure', 'members' => [ 'deletePipelineProvisioningRepository' => [ 'shape' => 'Boolean', ], 'pipelineCodebuildRoleArn' => [ 'shape' => 'RoleArnOrEmptyString', ], 'pipelineProvisioningRepository' => [ 'shape' => 'RepositoryBranchInput', ], 'pipelineServiceRoleArn' => [ 'shape' => 'RoleArnOrEmptyString', ], ], ], 'UpdateAccountSettingsOutput' => [ 'type' => 'structure', 'required' => [ 'accountSettings', ], 'members' => [ 'accountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'UpdateComponentInput' => [ 'type' => 'structure', 'required' => [ 'deploymentType', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'deploymentType' => [ 'shape' => 'ComponentDeploymentUpdateType', ], 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceInstanceName' => [ 'shape' => 'ResourceNameOrEmpty', ], 'serviceName' => [ 'shape' => 'ResourceNameOrEmpty', ], 'serviceSpec' => [ 'shape' => 'SpecContents', ], 'templateFile' => [ 'shape' => 'TemplateFileContents', ], ], ], 'UpdateComponentOutput' => [ 'type' => 'structure', 'required' => [ 'component', ], 'members' => [ 'component' => [ 'shape' => 'Component', ], ], ], 'UpdateEnvironmentAccountConnectionInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'id' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateEnvironmentAccountConnectionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentAccountConnection', ], 'members' => [ 'environmentAccountConnection' => [ 'shape' => 'EnvironmentAccountConnection', ], ], ], 'UpdateEnvironmentInput' => [ 'type' => 'structure', 'required' => [ 'deploymentType', 'name', ], 'members' => [ 'codebuildRoleArn' => [ 'shape' => 'RoleArn', ], 'componentRoleArn' => [ 'shape' => 'RoleArn', ], 'deploymentType' => [ 'shape' => 'DeploymentUpdateType', ], 'description' => [ 'shape' => 'Description', ], 'environmentAccountConnectionId' => [ 'shape' => 'EnvironmentAccountConnectionId', ], 'name' => [ 'shape' => 'ResourceName', ], 'protonServiceRoleArn' => [ 'shape' => 'Arn', ], 'provisioningRepository' => [ 'shape' => 'RepositoryBranchInput', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], ], ], 'UpdateEnvironmentOutput' => [ 'type' => 'structure', 'required' => [ 'environment', ], 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateEnvironmentTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'name' => [ 'shape' => 'ResourceName', ], ], ], 'UpdateEnvironmentTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplate', ], 'members' => [ 'environmentTemplate' => [ 'shape' => 'EnvironmentTemplate', ], ], ], 'UpdateEnvironmentTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'UpdateEnvironmentTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'environmentTemplateVersion', ], 'members' => [ 'environmentTemplateVersion' => [ 'shape' => 'EnvironmentTemplateVersion', ], ], ], 'UpdateServiceInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'SpecContents', ], ], ], 'UpdateServiceInstanceInput' => [ 'type' => 'structure', 'required' => [ 'deploymentType', 'name', 'serviceName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'deploymentType' => [ 'shape' => 'DeploymentUpdateType', ], 'name' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], ], ], 'UpdateServiceInstanceOutput' => [ 'type' => 'structure', 'required' => [ 'serviceInstance', ], 'members' => [ 'serviceInstance' => [ 'shape' => 'ServiceInstance', ], ], ], 'UpdateServiceOutput' => [ 'type' => 'structure', 'required' => [ 'service', ], 'members' => [ 'service' => [ 'shape' => 'Service', ], ], ], 'UpdateServicePipelineInput' => [ 'type' => 'structure', 'required' => [ 'deploymentType', 'serviceName', 'spec', ], 'members' => [ 'deploymentType' => [ 'shape' => 'DeploymentUpdateType', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'spec' => [ 'shape' => 'SpecContents', ], 'templateMajorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'templateMinorVersion' => [ 'shape' => 'TemplateVersionPart', ], ], ], 'UpdateServicePipelineOutput' => [ 'type' => 'structure', 'required' => [ 'pipeline', ], 'members' => [ 'pipeline' => [ 'shape' => 'ServicePipeline', ], ], ], 'UpdateServiceSyncBlockerInput' => [ 'type' => 'structure', 'required' => [ 'id', 'resolvedReason', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'resolvedReason' => [ 'shape' => 'String', ], ], ], 'UpdateServiceSyncBlockerOutput' => [ 'type' => 'structure', 'required' => [ 'serviceName', 'serviceSyncBlocker', ], 'members' => [ 'serviceInstanceName' => [ 'shape' => 'ResourceName', ], 'serviceName' => [ 'shape' => 'ResourceName', ], 'serviceSyncBlocker' => [ 'shape' => 'SyncBlocker', ], ], ], 'UpdateServiceSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'filePath', 'repositoryName', 'repositoryProvider', 'serviceName', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'filePath' => [ 'shape' => 'OpsFilePath', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'serviceName' => [ 'shape' => 'ResourceName', ], ], ], 'UpdateServiceSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'serviceSyncConfig' => [ 'shape' => 'ServiceSyncConfig', ], ], ], 'UpdateServiceTemplateInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'name' => [ 'shape' => 'ResourceName', ], ], ], 'UpdateServiceTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplate', ], 'members' => [ 'serviceTemplate' => [ 'shape' => 'ServiceTemplate', ], ], ], 'UpdateServiceTemplateVersionInput' => [ 'type' => 'structure', 'required' => [ 'majorVersion', 'minorVersion', 'templateName', ], 'members' => [ 'compatibleEnvironmentTemplates' => [ 'shape' => 'CompatibleEnvironmentTemplateInputList', ], 'description' => [ 'shape' => 'Description', ], 'majorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'minorVersion' => [ 'shape' => 'TemplateVersionPart', ], 'status' => [ 'shape' => 'TemplateVersionStatus', ], 'supportedComponentSources' => [ 'shape' => 'ServiceTemplateSupportedComponentSourceInputList', ], 'templateName' => [ 'shape' => 'ResourceName', ], ], ], 'UpdateServiceTemplateVersionOutput' => [ 'type' => 'structure', 'required' => [ 'serviceTemplateVersion', ], 'members' => [ 'serviceTemplateVersion' => [ 'shape' => 'ServiceTemplateVersion', ], ], ], 'UpdateTemplateSyncConfigInput' => [ 'type' => 'structure', 'required' => [ 'branch', 'repositoryName', 'repositoryProvider', 'templateName', 'templateType', ], 'members' => [ 'branch' => [ 'shape' => 'GitBranchName', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryProvider' => [ 'shape' => 'RepositoryProvider', ], 'subdirectory' => [ 'shape' => 'Subdirectory', ], 'templateName' => [ 'shape' => 'ResourceName', ], 'templateType' => [ 'shape' => 'TemplateType', ], ], ], 'UpdateTemplateSyncConfigOutput' => [ 'type' => 'structure', 'members' => [ 'templateSyncConfig' => [ 'shape' => 'TemplateSyncConfig', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], ],];
