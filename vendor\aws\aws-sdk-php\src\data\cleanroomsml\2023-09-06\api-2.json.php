<?php
// This file was auto-generated from sdk-root/src/data/cleanroomsml/2023-09-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-06', 'endpointPrefix' => 'cleanrooms-ml', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Clean Rooms ML', 'serviceId' => 'CleanRoomsML', 'signatureVersion' => 'v4', 'signingName' => 'cleanrooms-ml', 'uid' => 'cleanroomsml-2023-09-06', ], 'operations' => [ 'CreateAudienceModel' => [ 'name' => 'CreateAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateConfiguredAudienceModel' => [ 'name' => 'CreateConfiguredAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateTrainingDataset' => [ 'name' => 'CreateTrainingDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrainingDatasetRequest', ], 'output' => [ 'shape' => 'CreateTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAudienceGenerationJob' => [ 'name' => 'DeleteAudienceGenerationJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceGenerationJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAudienceModel' => [ 'name' => 'DeleteAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModel' => [ 'name' => 'DeleteConfiguredAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModelPolicy' => [ 'name' => 'DeleteConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelPolicyRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteTrainingDataset' => [ 'name' => 'DeleteTrainingDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrainingDatasetRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAudienceGenerationJob' => [ 'name' => 'GetAudienceGenerationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'GetAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAudienceModel' => [ 'name' => 'GetAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceModelRequest', ], 'output' => [ 'shape' => 'GetAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModel' => [ 'name' => 'GetConfiguredAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModelPolicy' => [ 'name' => 'GetConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrainingDataset' => [ 'name' => 'GetTrainingDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrainingDatasetRequest', ], 'output' => [ 'shape' => 'GetTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAudienceExportJobs' => [ 'name' => 'ListAudienceExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceExportJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceExportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceGenerationJobs' => [ 'name' => 'ListAudienceGenerationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceGenerationJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceGenerationJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceModels' => [ 'name' => 'ListAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredAudienceModels' => [ 'name' => 'ListConfiguredAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListConfiguredAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTrainingDatasets' => [ 'name' => 'ListTrainingDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrainingDatasetsRequest', ], 'output' => [ 'shape' => 'ListTrainingDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutConfiguredAudienceModelPolicy' => [ 'name' => 'PutConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'PutConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'StartAudienceExportJob' => [ 'name' => 'StartAudienceExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceExportJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartAudienceGenerationJob' => [ 'name' => 'StartAudienceGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'StartAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfiguredAudienceModel' => [ 'name' => 'UpdateConfiguredAudienceModel', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'UpdateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'AudienceDestination' => [ 'type' => 'structure', 'required' => [ 's3Destination', ], 'members' => [ 's3Destination' => [ 'shape' => 'S3ConfigMap', ], ], ], 'AudienceExportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceExportJobSummary', ], ], 'AudienceExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', ], ], 'AudienceExportJobSummary' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', 'audienceSize', 'createTime', 'name', 'status', 'updateTime', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'outputLocation' => [ 'shape' => 'S3Path', ], 'status' => [ 'shape' => 'AudienceExportJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'AudienceGenerationJobArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-generation-job/[-a-zA-Z0-9_/.]+$', ], 'AudienceGenerationJobDataSource' => [ 'type' => 'structure', 'required' => [ 'dataSource', 'roleArn', ], 'members' => [ 'dataSource' => [ 'shape' => 'S3ConfigMap', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'AudienceGenerationJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceGenerationJobSummary', ], ], 'AudienceGenerationJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceGenerationJobSummary' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', 'configuredAudienceModelArn', 'createTime', 'name', 'status', 'updateTime', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'startedBy' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'AudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-model/[-a-zA-Z0-9_/.]+$', ], 'AudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceModelSummary', ], ], 'AudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', 'createTime', 'name', 'status', 'trainingDatasetArn', 'updateTime', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'AudienceQualityMetrics' => [ 'type' => 'structure', 'required' => [ 'relevanceMetrics', ], 'members' => [ 'recallMetric' => [ 'shape' => 'Double', ], 'relevanceMetrics' => [ 'shape' => 'RelevanceMetrics', ], ], ], 'AudienceSize' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'AudienceSizeType', ], 'value' => [ 'shape' => 'AudienceSizeValue', ], ], ], 'AudienceSizeBins' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceSizeValue', ], 'max' => 25, 'min' => 1, ], 'AudienceSizeConfig' => [ 'type' => 'structure', 'required' => [ 'audienceSizeBins', 'audienceSizeType', ], 'members' => [ 'audienceSizeBins' => [ 'shape' => 'AudienceSizeBins', ], 'audienceSizeType' => [ 'shape' => 'AudienceSizeType', ], ], ], 'AudienceSizeType' => [ 'type' => 'string', 'enum' => [ 'ABSOLUTE', 'PERCENTAGE', ], ], 'AudienceSizeValue' => [ 'type' => 'integer', 'box' => true, 'max' => 20000000, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ColumnName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?$', ], 'ColumnSchema' => [ 'type' => 'structure', 'required' => [ 'columnName', 'columnTypes', ], 'members' => [ 'columnName' => [ 'shape' => 'ColumnName', ], 'columnTypes' => [ 'shape' => 'ColumnTypeList', ], ], ], 'ColumnType' => [ 'type' => 'string', 'enum' => [ 'USER_ID', 'ITEM_ID', 'TIMESTAMP', 'CATEGORICAL_FEATURE', 'NUMERICAL_FEATURE', ], ], 'ColumnTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnType', ], 'max' => 1, 'min' => 1, ], 'ConfiguredAudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-audience-model/[-a-zA-Z0-9_/.]+$', ], 'ConfiguredAudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredAudienceModelSummary', ], ], 'ConfiguredAudienceModelOutputConfig' => [ 'type' => 'structure', 'required' => [ 'destination', 'roleArn', ], 'members' => [ 'destination' => [ 'shape' => 'AudienceDestination', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'ConfiguredAudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'ConfiguredAudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', 'configuredAudienceModelArn', 'createTime', 'name', 'outputConfig', 'status', 'updateTime', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'trainingDatasetArn', ], 'members' => [ 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'name' => [ 'shape' => 'NameString', ], 'tags' => [ 'shape' => 'TagMap', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], ], ], 'CreateAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], ], ], 'CreateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', 'name', 'outputConfig', 'sharedAudienceMetrics', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'name' => [ 'shape' => 'NameString', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'CreateTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', 'trainingData', ], 'members' => [ 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'trainingData' => [ 'shape' => 'CreateTrainingDatasetRequestTrainingDataList', ], ], ], 'CreateTrainingDatasetRequestTrainingDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], 'max' => 1, 'min' => 1, ], 'CreateTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'glueDataSource', ], 'members' => [ 'glueDataSource' => [ 'shape' => 'GlueDataSource', ], ], ], 'Dataset' => [ 'type' => 'structure', 'required' => [ 'inputConfig', 'type', ], 'members' => [ 'inputConfig' => [ 'shape' => 'DatasetInputConfig', ], 'type' => [ 'shape' => 'DatasetType', ], ], ], 'DatasetInputConfig' => [ 'type' => 'structure', 'required' => [ 'dataSource', 'schema', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], 'schema' => [ 'shape' => 'DatasetInputConfigSchemaList', ], ], ], 'DatasetInputConfigSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnSchema', ], 'max' => 100, 'min' => 1, ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'INTERACTIONS', ], ], 'DeleteAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'DeleteAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GetAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'GetAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', 'configuredAudienceModelArn', 'createTime', 'name', 'status', 'updateTime', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'metrics' => [ 'shape' => 'AudienceQualityMetrics', ], 'name' => [ 'shape' => 'NameString', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'startedBy' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'tags' => [ 'shape' => 'TagMap', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'GetAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', 'createTime', 'name', 'status', 'trainingDatasetArn', 'updateTime', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'tags' => [ 'shape' => 'TagMap', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'GetConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', 'configuredAudienceModelArn', 'createTime', 'name', 'outputConfig', 'sharedAudienceMetrics', 'status', 'updateTime', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'name' => [ 'shape' => 'NameString', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'GetTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'name', 'roleArn', 'status', 'trainingData', 'trainingDatasetArn', 'updateTime', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'trainingData' => [ 'shape' => 'DatasetList', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GlueDataSource' => [ 'type' => 'structure', 'required' => [ 'databaseName', 'tableName', ], 'members' => [ 'catalogId' => [ 'shape' => 'AccountId', ], 'databaseName' => [ 'shape' => 'GlueDatabaseName', ], 'tableName' => [ 'shape' => 'GlueTableName', ], ], ], 'GlueDatabaseName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_](([a-zA-Z0-9_]+-)*([a-zA-Z0-9_]+))?$', ], 'GlueTableName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?$', ], 'Hash' => [ 'type' => 'string', 'max' => 128, 'min' => 64, 'pattern' => '^[0-9a-f]+$', ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:iam::[0-9]{12}:role/.+$', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:kms:[-a-z0-9]+:[0-9]{12}:key/.+$', ], 'ListAudienceExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'querystring', 'locationName' => 'audienceGenerationJobArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAudienceExportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceExportJobs', ], 'members' => [ 'audienceExportJobs' => [ 'shape' => 'AudienceExportJobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAudienceGenerationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'collaborationId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'collaborationId', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'querystring', 'locationName' => 'configuredAudienceModelArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAudienceGenerationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobs', ], 'members' => [ 'audienceGenerationJobs' => [ 'shape' => 'AudienceGenerationJobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModels', ], 'members' => [ 'audienceModels' => [ 'shape' => 'AudienceModelList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConfiguredAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListConfiguredAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModels', ], 'members' => [ 'configuredAudienceModels' => [ 'shape' => 'ConfiguredAudienceModelList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrainingDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTrainingDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasets', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'trainingDatasets' => [ 'shape' => 'TrainingDatasetList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedAudienceMetrics', ], 'max' => 1, 'min' => 1, ], 'MinMatchingSeedSize' => [ 'type' => 'integer', 'box' => true, 'max' => 500000, 'min' => 25, ], 'NameString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*$', ], 'NextToken' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'PolicyExistenceCondition' => [ 'type' => 'string', 'enum' => [ 'POLICY_MUST_EXIST', 'POLICY_MUST_NOT_EXIST', ], ], 'PutConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyExistenceCondition' => [ 'shape' => 'PolicyExistenceCondition', ], 'previousPolicyHash' => [ 'shape' => 'Hash', ], ], ], 'PutConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'RelevanceMetric' => [ 'type' => 'structure', 'required' => [ 'audienceSize', ], 'members' => [ 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'score' => [ 'shape' => 'Double', ], ], ], 'RelevanceMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelevanceMetric', ], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, ], 'S3ConfigMap' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Path', ], ], ], 'S3Path' => [ 'type' => 'string', 'max' => 1285, 'min' => 1, 'pattern' => '^s3://.+$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SharedAudienceMetrics' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', ], ], 'StartAudienceExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', 'audienceSize', 'name', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], ], ], 'StartAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'name', 'seedAudience', ], 'members' => [ 'collaborationId' => [ 'shape' => 'UUID', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'NameString', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], ], ], 'StatusDetails' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'statusCode' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagOnCreatePolicy' => [ 'type' => 'string', 'enum' => [ 'FROM_PARENT_RESOURCE', 'NONE', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaggableArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(training-dataset|audience-model|configured-audience-model|audience-generation-job)/[-a-zA-Z0-9_/.]+$', ], 'TrainingDatasetArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:training-dataset/[-a-zA-Z0-9_/.]+$', ], 'TrainingDatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingDatasetSummary', ], ], 'TrainingDatasetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'TrainingDatasetSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'name', 'status', 'trainingDatasetArn', 'updateTime', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], ], ], 'UpdateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
