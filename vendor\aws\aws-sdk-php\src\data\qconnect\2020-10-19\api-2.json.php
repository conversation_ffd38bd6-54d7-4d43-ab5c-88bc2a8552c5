<?php
// This file was auto-generated from sdk-root/src/data/qconnect/2020-10-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-10-19', 'endpointPrefix' => 'wisdom', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Q Connect', 'serviceId' => 'QConnect', 'signatureVersion' => 'v4', 'signingName' => 'wisdom', 'uid' => 'qconnect-2020-10-19', ], 'operations' => [ 'CreateAssistant' => [ 'name' => 'CreateAssistant', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAssistantRequest', ], 'output' => [ 'shape' => 'CreateAssistantResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAssistantAssociation' => [ 'name' => 'CreateAssistantAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAssistantAssociationRequest', ], 'output' => [ 'shape' => 'CreateAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateContent' => [ 'name' => 'CreateContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateContentRequest', ], 'output' => [ 'shape' => 'CreateContentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateKnowledgeBase' => [ 'name' => 'CreateKnowledgeBase', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'CreateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateQuickResponse' => [ 'name' => 'CreateQuickResponse', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQuickResponseRequest', ], 'output' => [ 'shape' => 'CreateQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateSession' => [ 'name' => 'CreateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSessionRequest', ], 'output' => [ 'shape' => 'CreateSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAssistant' => [ 'name' => 'DeleteAssistant', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssistantRequest', ], 'output' => [ 'shape' => 'DeleteAssistantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAssistantAssociation' => [ 'name' => 'DeleteAssistantAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/associations/{assistantAssociationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssistantAssociationRequest', ], 'output' => [ 'shape' => 'DeleteAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteContent' => [ 'name' => 'DeleteContent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteContentRequest', ], 'output' => [ 'shape' => 'DeleteContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteImportJob' => [ 'name' => 'DeleteImportJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs/{importJobId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteImportJobRequest', ], 'output' => [ 'shape' => 'DeleteImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteKnowledgeBase' => [ 'name' => 'DeleteKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DeleteKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteQuickResponse' => [ 'name' => 'DeleteQuickResponse', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteQuickResponseRequest', ], 'output' => [ 'shape' => 'DeleteQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAssistant' => [ 'name' => 'GetAssistant', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssistantRequest', ], 'output' => [ 'shape' => 'GetAssistantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAssistantAssociation' => [ 'name' => 'GetAssistantAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/associations/{assistantAssociationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssistantAssociationRequest', ], 'output' => [ 'shape' => 'GetAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetContent' => [ 'name' => 'GetContent', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContentRequest', ], 'output' => [ 'shape' => 'GetContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetContentSummary' => [ 'name' => 'GetContentSummary', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContentSummaryRequest', ], 'output' => [ 'shape' => 'GetContentSummaryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetImportJob' => [ 'name' => 'GetImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs/{importJobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportJobRequest', ], 'output' => [ 'shape' => 'GetImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKnowledgeBase' => [ 'name' => 'GetKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetQuickResponse' => [ 'name' => 'GetQuickResponse', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQuickResponseRequest', ], 'output' => [ 'shape' => 'GetQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetRecommendations' => [ 'name' => 'GetRecommendations', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecommendationsRequest', ], 'output' => [ 'shape' => 'GetRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'GetRecommendations API will be discontinued starting June 1, 2024. To receive generative responses after March 1, 2024 you will need to create a new Assistant in the Connect console and integrate the Amazon Q in Connect JavaScript library (amazon-q-connectjs) into your applications.', ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAssistantAssociations' => [ 'name' => 'ListAssistantAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssistantAssociationsRequest', ], 'output' => [ 'shape' => 'ListAssistantAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAssistants' => [ 'name' => 'ListAssistants', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssistantsRequest', ], 'output' => [ 'shape' => 'ListAssistantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListContents' => [ 'name' => 'ListContents', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListContentsRequest', ], 'output' => [ 'shape' => 'ListContentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListImportJobs' => [ 'name' => 'ListImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListImportJobsRequest', ], 'output' => [ 'shape' => 'ListImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListKnowledgeBases' => [ 'name' => 'ListKnowledgeBases', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListQuickResponses' => [ 'name' => 'ListQuickResponses', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQuickResponsesRequest', ], 'output' => [ 'shape' => 'ListQuickResponsesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'NotifyRecommendationsReceived' => [ 'name' => 'NotifyRecommendationsReceived', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/recommendations/notify', 'responseCode' => 200, ], 'input' => [ 'shape' => 'NotifyRecommendationsReceivedRequest', ], 'output' => [ 'shape' => 'NotifyRecommendationsReceivedResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assistants/{assistantId}/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'output' => [ 'shape' => 'PutFeedbackResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'QueryAssistant' => [ 'name' => 'QueryAssistant', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/query', 'responseCode' => 200, ], 'input' => [ 'shape' => 'QueryAssistantRequest', ], 'output' => [ 'shape' => 'QueryAssistantResponse', ], 'errors' => [ [ 'shape' => 'RequestTimeoutException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'QueryAssistant API will be discontinued starting June 1, 2024. To receive generative responses after March 1, 2024 you will need to create a new Assistant in the Connect console and integrate the Amazon Q in Connect JavaScript library (amazon-q-connectjs) into your applications.', ], 'RemoveKnowledgeBaseTemplateUri' => [ 'name' => 'RemoveKnowledgeBaseTemplateUri', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/templateUri', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveKnowledgeBaseTemplateUriRequest', ], 'output' => [ 'shape' => 'RemoveKnowledgeBaseTemplateUriResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchContent' => [ 'name' => 'SearchContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchContentRequest', ], 'output' => [ 'shape' => 'SearchContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchQuickResponses' => [ 'name' => 'SearchQuickResponses', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/search/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchQuickResponsesRequest', ], 'output' => [ 'shape' => 'SearchQuickResponsesResponse', ], 'errors' => [ [ 'shape' => 'RequestTimeoutException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchSessions' => [ 'name' => 'SearchSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/searchSessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchSessionsRequest', ], 'output' => [ 'shape' => 'SearchSessionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartContentUpload' => [ 'name' => 'StartContentUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/upload', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartContentUploadRequest', ], 'output' => [ 'shape' => 'StartContentUploadResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartImportJob' => [ 'name' => 'StartImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartImportJobRequest', ], 'output' => [ 'shape' => 'StartImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateContent' => [ 'name' => 'UpdateContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateContentRequest', ], 'output' => [ 'shape' => 'UpdateContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateKnowledgeBaseTemplateUri' => [ 'name' => 'UpdateKnowledgeBaseTemplateUri', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/templateUri', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKnowledgeBaseTemplateUriRequest', ], 'output' => [ 'shape' => 'UpdateKnowledgeBaseTemplateUriResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateQuickResponse' => [ 'name' => 'UpdateQuickResponse', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQuickResponseRequest', ], 'output' => [ 'shape' => 'UpdateQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateSession' => [ 'name' => 'UpdateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionRequest', ], 'output' => [ 'shape' => 'UpdateSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AndConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCondition', ], ], 'AppIntegrationsConfiguration' => [ 'type' => 'structure', 'required' => [ 'appIntegrationArn', ], 'members' => [ 'appIntegrationArn' => [ 'shape' => 'GenericArn', ], 'objectFields' => [ 'shape' => 'ObjectFieldsList', ], ], ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})?$', ], 'AssistantAssociationData' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantAssociationArn', 'assistantAssociationId', 'assistantId', 'associationData', 'associationType', ], 'members' => [ 'assistantArn' => [ 'shape' => 'Arn', ], 'assistantAssociationArn' => [ 'shape' => 'Arn', ], 'assistantAssociationId' => [ 'shape' => 'Uuid', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'associationData' => [ 'shape' => 'AssistantAssociationOutputData', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AssistantAssociationInputData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], ], 'union' => true, ], 'AssistantAssociationOutputData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseAssociation' => [ 'shape' => 'KnowledgeBaseAssociationData', ], ], 'union' => true, ], 'AssistantAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantAssociationArn', 'assistantAssociationId', 'assistantId', 'associationData', 'associationType', ], 'members' => [ 'assistantArn' => [ 'shape' => 'Arn', ], 'assistantAssociationArn' => [ 'shape' => 'Arn', ], 'assistantAssociationId' => [ 'shape' => 'Uuid', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'associationData' => [ 'shape' => 'AssistantAssociationOutputData', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AssistantAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssistantAssociationSummary', ], ], 'AssistantCapabilityConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'AssistantCapabilityType', ], ], ], 'AssistantCapabilityType' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'AssistantData' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantId', 'name', 'status', 'type', ], 'members' => [ 'assistantArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'capabilityConfiguration' => [ 'shape' => 'AssistantCapabilityConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'integrationConfiguration' => [ 'shape' => 'AssistantIntegrationConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'status' => [ 'shape' => 'AssistantStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'AssistantType', ], ], ], 'AssistantIntegrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'topicIntegrationArn' => [ 'shape' => 'GenericArn', ], ], ], 'AssistantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssistantSummary', ], ], 'AssistantStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'AssistantSummary' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantId', 'name', 'status', 'type', ], 'members' => [ 'assistantArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'capabilityConfiguration' => [ 'shape' => 'AssistantCapabilityConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'integrationConfiguration' => [ 'shape' => 'AssistantIntegrationConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'status' => [ 'shape' => 'AssistantStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'AssistantType', ], ], ], 'AssistantType' => [ 'type' => 'string', 'enum' => [ 'AGENT', ], ], 'AssociationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Channel' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'sensitive' => true, ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'connectConfiguration' => [ 'shape' => 'ConnectConfiguration', ], ], 'union' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectConfiguration' => [ 'type' => 'structure', 'members' => [ 'instanceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ContactAttributeKey' => [ 'type' => 'string', ], 'ContactAttributeKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactAttributeKey', ], 'sensitive' => true, ], 'ContactAttributeValue' => [ 'type' => 'string', ], 'ContactAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContactAttributeKey', ], 'value' => [ 'shape' => 'ContactAttributeValue', ], 'sensitive' => true, ], 'ContentData' => [ 'type' => 'structure', 'required' => [ 'contentArn', 'contentId', 'contentType', 'knowledgeBaseArn', 'knowledgeBaseId', 'metadata', 'name', 'revisionId', 'status', 'title', 'url', 'urlExpiry', ], 'members' => [ 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'contentType' => [ 'shape' => 'ContentType', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'linkOutUri' => [ 'shape' => 'Uri', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'name' => [ 'shape' => 'Name', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'ContentStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'title' => [ 'shape' => 'ContentTitle', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], ], ], 'ContentDataDetails' => [ 'type' => 'structure', 'required' => [ 'rankingData', 'textData', ], 'members' => [ 'rankingData' => [ 'shape' => 'RankingData', ], 'textData' => [ 'shape' => 'TextData', ], ], ], 'ContentFeedbackData' => [ 'type' => 'structure', 'members' => [ 'generativeContentFeedbackData' => [ 'shape' => 'GenerativeContentFeedbackData', ], ], 'union' => true, ], 'ContentMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 0, ], 'ContentReference' => [ 'type' => 'structure', 'members' => [ 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], ], ], 'ContentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', 'UPDATE_FAILED', ], ], 'ContentSummary' => [ 'type' => 'structure', 'required' => [ 'contentArn', 'contentId', 'contentType', 'knowledgeBaseArn', 'knowledgeBaseId', 'metadata', 'name', 'revisionId', 'status', 'title', ], 'members' => [ 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'contentType' => [ 'shape' => 'ContentType', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'name' => [ 'shape' => 'Name', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'ContentStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'title' => [ 'shape' => 'ContentTitle', ], ], ], 'ContentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentSummary', ], ], 'ContentTitle' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ContentType' => [ 'type' => 'string', 'pattern' => '^(text/(plain|html|csv))|(application/(pdf|vnd\\.openxmlformats-officedocument\\.wordprocessingml\\.document))|(application/x\\.wisdom-json;source=(salesforce|servicenow|zendesk))$', ], 'CreateAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'association', 'associationType', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'association' => [ 'shape' => 'AssistantAssociationInputData', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'assistantAssociation' => [ 'shape' => 'AssistantAssociationData', ], ], ], 'CreateAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'AssistantType', ], ], ], 'CreateAssistantResponse' => [ 'type' => 'structure', 'members' => [ 'assistant' => [ 'shape' => 'AssistantData', ], ], ], 'CreateContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'uploadId', ], 'members' => [ 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'name' => [ 'shape' => 'Name', ], 'overrideLinkOutUri' => [ 'shape' => 'Uri', ], 'tags' => [ 'shape' => 'Tags', ], 'title' => [ 'shape' => 'ContentTitle', ], 'uploadId' => [ 'shape' => 'UploadId', ], ], ], 'CreateContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'CreateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseType', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'name' => [ 'shape' => 'Name', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'CreateQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'content', 'knowledgeBaseId', 'name', ], 'members' => [ 'channels' => [ 'shape' => 'Channels', ], 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'content' => [ 'shape' => 'QuickResponseDataProvider', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'isActive' => [ 'shape' => 'Boolean', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'language' => [ 'shape' => 'LanguageCode', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'CreateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'name', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'name' => [ 'shape' => 'Name', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'DataDetails' => [ 'type' => 'structure', 'members' => [ 'contentData' => [ 'shape' => 'ContentDataDetails', ], 'generativeData' => [ 'shape' => 'GenerativeDataDetails', ], 'sourceContentData' => [ 'shape' => 'SourceContentDataDetails', ], ], 'union' => true, ], 'DataReference' => [ 'type' => 'structure', 'members' => [ 'contentReference' => [ 'shape' => 'ContentReference', ], 'generativeReference' => [ 'shape' => 'GenerativeReference', ], ], 'union' => true, ], 'DataSummary' => [ 'type' => 'structure', 'required' => [ 'details', 'reference', ], 'members' => [ 'details' => [ 'shape' => 'DataDetails', ], 'reference' => [ 'shape' => 'DataReference', ], ], ], 'DataSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSummary', ], ], 'DeleteAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantId', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantAssociationId', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'DeleteAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'DeleteAssistantResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContentRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteContentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'importJobId', 'knowledgeBaseId', ], 'members' => [ 'importJobId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'importJobId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteImportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'quickResponseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], ], ], 'DeleteQuickResponseResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s_.,-]+', ], 'Document' => [ 'type' => 'structure', 'required' => [ 'contentReference', ], 'members' => [ 'contentReference' => [ 'shape' => 'ContentReference', ], 'excerpt' => [ 'shape' => 'DocumentText', ], 'title' => [ 'shape' => 'DocumentText', ], ], ], 'DocumentText' => [ 'type' => 'structure', 'members' => [ 'highlights' => [ 'shape' => 'Highlights', ], 'text' => [ 'shape' => 'SensitiveString', ], ], ], 'ExternalSource' => [ 'type' => 'string', 'enum' => [ 'AMAZON_CONNECT', ], ], 'ExternalSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'configuration', 'source', ], 'members' => [ 'configuration' => [ 'shape' => 'Configuration', ], 'source' => [ 'shape' => 'ExternalSource', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'field', 'operator', 'value', ], 'members' => [ 'field' => [ 'shape' => 'FilterField', ], 'operator' => [ 'shape' => 'FilterOperator', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'FilterField' => [ 'type' => 'string', 'enum' => [ 'NAME', ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'GenerativeContentFeedbackData' => [ 'type' => 'structure', 'required' => [ 'relevance', ], 'members' => [ 'relevance' => [ 'shape' => 'Relevance', ], ], ], 'GenerativeDataDetails' => [ 'type' => 'structure', 'required' => [ 'completion', 'rankingData', 'references', ], 'members' => [ 'completion' => [ 'shape' => 'SensitiveString', ], 'rankingData' => [ 'shape' => 'RankingData', ], 'references' => [ 'shape' => 'DataSummaryList', ], ], ], 'GenerativeReference' => [ 'type' => 'structure', 'members' => [ 'generationId' => [ 'shape' => 'Uuid', ], 'modelId' => [ 'shape' => 'LlmModelId', ], ], ], 'GenericArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[a-z-]+?:[a-z-]+?:[a-z0-9-]*?:([0-9]{12})?:[a-zA-Z0-9-:/]+$', ], 'GetAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantId', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantAssociationId', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'GetAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'assistantAssociation' => [ 'shape' => 'AssistantAssociationData', ], ], ], 'GetAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'GetAssistantResponse' => [ 'type' => 'structure', 'members' => [ 'assistant' => [ 'shape' => 'AssistantData', ], ], ], 'GetContentRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'GetContentSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetContentSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'contentSummary' => [ 'shape' => 'ContentSummary', ], ], ], 'GetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'importJobId', 'knowledgeBaseId', ], 'members' => [ 'importJobId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'importJobId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'importJob' => [ 'shape' => 'ImportJobData', ], ], ], 'GetKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'GetQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'quickResponseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], ], ], 'GetQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'GetRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'waitTimeSeconds' => [ 'shape' => 'WaitTimeSeconds', 'location' => 'querystring', 'locationName' => 'waitTimeSeconds', ], ], ], 'GetRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'recommendations', ], 'members' => [ 'recommendations' => [ 'shape' => 'RecommendationList', ], 'triggers' => [ 'shape' => 'RecommendationTriggerList', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'GroupingConfiguration' => [ 'type' => 'structure', 'members' => [ 'criteria' => [ 'shape' => 'GroupingCriteria', ], 'values' => [ 'shape' => 'GroupingValues', ], ], ], 'GroupingCriteria' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GroupingValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'GroupingValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingValue', ], ], 'Headers' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'Highlight' => [ 'type' => 'structure', 'members' => [ 'beginOffsetInclusive' => [ 'shape' => 'HighlightOffset', ], 'endOffsetExclusive' => [ 'shape' => 'HighlightOffset', ], ], ], 'HighlightOffset' => [ 'type' => 'integer', ], 'Highlights' => [ 'type' => 'list', 'member' => [ 'shape' => 'Highlight', ], ], 'ImportJobData' => [ 'type' => 'structure', 'required' => [ 'createdTime', 'importJobId', 'importJobType', 'knowledgeBaseArn', 'knowledgeBaseId', 'lastModifiedTime', 'status', 'uploadId', 'url', 'urlExpiry', ], 'members' => [ 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], 'failedRecordReport' => [ 'shape' => 'Url', ], 'importJobId' => [ 'shape' => 'Uuid', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'status' => [ 'shape' => 'ImportJobStatus', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], ], ], 'ImportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportJobSummary', ], ], 'ImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'START_IN_PROGRESS', 'FAILED', 'COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'ImportJobSummary' => [ 'type' => 'structure', 'required' => [ 'createdTime', 'importJobId', 'importJobType', 'knowledgeBaseArn', 'knowledgeBaseId', 'lastModifiedTime', 'status', 'uploadId', ], 'members' => [ 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], 'importJobId' => [ 'shape' => 'Uuid', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'status' => [ 'shape' => 'ImportJobStatus', ], 'uploadId' => [ 'shape' => 'UploadId', ], ], ], 'ImportJobType' => [ 'type' => 'string', 'enum' => [ 'QUICK_RESPONSES', ], ], 'KnowledgeBaseAssociationData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], ], ], 'KnowledgeBaseData' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', 'knowledgeBaseId', 'knowledgeBaseType', 'name', 'status', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'lastContentModificationTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'name' => [ 'shape' => 'Name', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'KnowledgeBaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseSummary', ], ], 'KnowledgeBaseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'KnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', 'knowledgeBaseId', 'knowledgeBaseType', 'name', 'status', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'name' => [ 'shape' => 'Name', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'KnowledgeBaseType' => [ 'type' => 'string', 'enum' => [ 'EXTERNAL', 'CUSTOM', 'QUICK_RESPONSES', ], ], 'LanguageCode' => [ 'type' => 'string', 'max' => 5, 'min' => 2, ], 'ListAssistantAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAssistantAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationSummaries', ], 'members' => [ 'assistantAssociationSummaries' => [ 'shape' => 'AssistantAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssistantsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAssistantsResponse' => [ 'type' => 'structure', 'required' => [ 'assistantSummaries', ], 'members' => [ 'assistantSummaries' => [ 'shape' => 'AssistantList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContentsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListContentsResponse' => [ 'type' => 'structure', 'required' => [ 'contentSummaries', ], 'members' => [ 'contentSummaries' => [ 'shape' => 'ContentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'importJobSummaries', ], 'members' => [ 'importJobSummaries' => [ 'shape' => 'ImportJobList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListKnowledgeBasesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseSummaries', ], 'members' => [ 'knowledgeBaseSummaries' => [ 'shape' => 'KnowledgeBaseList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListQuickResponsesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListQuickResponsesResponse' => [ 'type' => 'structure', 'required' => [ 'quickResponseSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'NonEmptyString', ], 'quickResponseSummaries' => [ 'shape' => 'QuickResponseSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'LlmModelId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s_.,-]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'NotifyRecommendationsReceivedError' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NotifyRecommendationsReceivedErrorMessage', ], 'recommendationId' => [ 'shape' => 'RecommendationId', ], ], ], 'NotifyRecommendationsReceivedErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotifyRecommendationsReceivedError', ], ], 'NotifyRecommendationsReceivedErrorMessage' => [ 'type' => 'string', ], 'NotifyRecommendationsReceivedRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'recommendationIds', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'NotifyRecommendationsReceivedResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'NotifyRecommendationsReceivedErrorList', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], ], ], 'ObjectFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'OrCondition' => [ 'type' => 'structure', 'members' => [ 'andConditions' => [ 'shape' => 'AndConditions', ], 'tagCondition' => [ 'shape' => 'TagCondition', ], ], 'union' => true, ], 'OrConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrCondition', ], ], 'Order' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 412, 'senderFault' => true, ], 'exception' => true, ], 'Priority' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'contentFeedback', 'targetId', 'targetType', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'contentFeedback' => [ 'shape' => 'ContentFeedbackData', ], 'targetId' => [ 'shape' => 'Uuid', ], 'targetType' => [ 'shape' => 'TargetType', ], ], ], 'PutFeedbackResponse' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantId', 'contentFeedback', 'targetId', 'targetType', ], 'members' => [ 'assistantArn' => [ 'shape' => 'UuidOrArn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'contentFeedback' => [ 'shape' => 'ContentFeedbackData', ], 'targetId' => [ 'shape' => 'Uuid', ], 'targetType' => [ 'shape' => 'TargetType', ], ], ], 'QueryAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'queryText', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'queryCondition' => [ 'shape' => 'QueryConditionExpression', ], 'queryText' => [ 'shape' => 'QueryText', ], 'sessionId' => [ 'shape' => 'UuidOrArn', ], ], ], 'QueryAssistantResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'results' => [ 'shape' => 'QueryResultsList', ], ], ], 'QueryCondition' => [ 'type' => 'structure', 'members' => [ 'single' => [ 'shape' => 'QueryConditionItem', ], ], 'union' => true, ], 'QueryConditionComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'QueryConditionExpression' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryCondition', ], 'max' => 1, 'min' => 0, ], 'QueryConditionFieldName' => [ 'type' => 'string', 'enum' => [ 'RESULT_TYPE', ], ], 'QueryConditionItem' => [ 'type' => 'structure', 'required' => [ 'comparator', 'field', 'value', ], 'members' => [ 'comparator' => [ 'shape' => 'QueryConditionComparisonOperator', ], 'field' => [ 'shape' => 'QueryConditionFieldName', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'QueryRecommendationTriggerData' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'QueryText', ], ], ], 'QueryResultType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', 'GENERATIVE_ANSWER', ], ], 'QueryResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultData', ], ], 'QueryText' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'QuickResponseContent' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'QuickResponseContentProvider' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'QuickResponseContent', ], ], 'union' => true, ], 'QuickResponseContents' => [ 'type' => 'structure', 'members' => [ 'markdown' => [ 'shape' => 'QuickResponseContentProvider', ], 'plainText' => [ 'shape' => 'QuickResponseContentProvider', ], ], ], 'QuickResponseData' => [ 'type' => 'structure', 'required' => [ 'contentType', 'createdTime', 'knowledgeBaseArn', 'knowledgeBaseId', 'lastModifiedTime', 'name', 'quickResponseArn', 'quickResponseId', 'status', ], 'members' => [ 'channels' => [ 'shape' => 'Channels', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'contents' => [ 'shape' => 'QuickResponseContents', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'isActive' => [ 'shape' => 'Boolean', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'language' => [ 'shape' => 'LanguageCode', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseDataProvider' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'QuickResponseContent', ], ], 'union' => true, ], 'QuickResponseDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'QuickResponseFilterField' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', ], 'members' => [ 'includeNoExistence' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'operator' => [ 'shape' => 'QuickResponseFilterOperator', ], 'values' => [ 'shape' => 'QuickResponseFilterValueList', ], ], ], 'QuickResponseFilterFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseFilterField', ], 'max' => 10, 'min' => 0, ], 'QuickResponseFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', ], ], 'QuickResponseFilterValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'QuickResponseFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseFilterValue', ], 'max' => 5, 'min' => 1, ], 'QuickResponseName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'QuickResponseOrderField' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'order' => [ 'shape' => 'Order', ], ], ], 'QuickResponseQueryField' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'allowFuzziness' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'NonEmptyString', ], 'operator' => [ 'shape' => 'QuickResponseQueryOperator', ], 'priority' => [ 'shape' => 'Priority', ], 'values' => [ 'shape' => 'QuickResponseQueryValueList', ], ], ], 'QuickResponseQueryFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseQueryField', ], 'max' => 4, 'min' => 0, ], 'QuickResponseQueryOperator' => [ 'type' => 'string', 'enum' => [ 'CONTAINS', 'CONTAINS_AND_PREFIX', ], ], 'QuickResponseQueryValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'QuickResponseQueryValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseQueryValue', ], 'max' => 5, 'min' => 1, ], 'QuickResponseSearchExpression' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'QuickResponseFilterFieldList', ], 'orderOnField' => [ 'shape' => 'QuickResponseOrderField', ], 'queries' => [ 'shape' => 'QuickResponseQueryFieldList', ], ], ], 'QuickResponseSearchResultData' => [ 'type' => 'structure', 'required' => [ 'contentType', 'contents', 'createdTime', 'isActive', 'knowledgeBaseArn', 'knowledgeBaseId', 'lastModifiedTime', 'name', 'quickResponseArn', 'quickResponseId', 'status', ], 'members' => [ 'attributesInterpolated' => [ 'shape' => 'ContactAttributeKeys', ], 'attributesNotInterpolated' => [ 'shape' => 'ContactAttributeKeys', ], 'channels' => [ 'shape' => 'Channels', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'contents' => [ 'shape' => 'QuickResponseContents', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'isActive' => [ 'shape' => 'Boolean', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'language' => [ 'shape' => 'LanguageCode', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseSearchResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseSearchResultData', ], ], 'QuickResponseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'QuickResponseSummary' => [ 'type' => 'structure', 'required' => [ 'contentType', 'createdTime', 'knowledgeBaseArn', 'knowledgeBaseId', 'lastModifiedTime', 'name', 'quickResponseArn', 'quickResponseId', 'status', ], 'members' => [ 'channels' => [ 'shape' => 'Channels', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'isActive' => [ 'shape' => 'Boolean', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseSummary', ], ], 'QuickResponseType' => [ 'type' => 'string', 'pattern' => '^(application/x\\.quickresponse;format=(plain|markdown))$', ], 'RankingData' => [ 'type' => 'structure', 'members' => [ 'relevanceLevel' => [ 'shape' => 'RelevanceLevel', ], 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], ], ], 'RecommendationData' => [ 'type' => 'structure', 'required' => [ 'recommendationId', ], 'members' => [ 'data' => [ 'shape' => 'DataSummary', ], 'document' => [ 'shape' => 'Document', ], 'recommendationId' => [ 'shape' => 'RecommendationId', ], 'relevanceLevel' => [ 'shape' => 'RelevanceLevel', ], 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], 'type' => [ 'shape' => 'RecommendationType', ], ], ], 'RecommendationId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationId', ], 'max' => 25, 'min' => 0, ], 'RecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationData', ], ], 'RecommendationSourceType' => [ 'type' => 'string', 'enum' => [ 'ISSUE_DETECTION', 'RULE_EVALUATION', 'OTHER', ], ], 'RecommendationTrigger' => [ 'type' => 'structure', 'required' => [ 'data', 'id', 'recommendationIds', 'source', 'type', ], 'members' => [ 'data' => [ 'shape' => 'RecommendationTriggerData', ], 'id' => [ 'shape' => 'Uuid', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'source' => [ 'shape' => 'RecommendationSourceType', ], 'type' => [ 'shape' => 'RecommendationTriggerType', ], ], ], 'RecommendationTriggerData' => [ 'type' => 'structure', 'members' => [ 'query' => [ 'shape' => 'QueryRecommendationTriggerData', ], ], 'union' => true, ], 'RecommendationTriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTrigger', ], ], 'RecommendationTriggerType' => [ 'type' => 'string', 'enum' => [ 'QUERY', 'GENERATIVE', ], ], 'RecommendationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', 'GENERATIVE_RESPONSE', 'GENERATIVE_ANSWER', ], ], 'Relevance' => [ 'type' => 'string', 'enum' => [ 'HELPFUL', 'NOT_HELPFUL', ], ], 'RelevanceLevel' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'RelevanceScore' => [ 'type' => 'double', 'min' => 0.0, ], 'RemoveKnowledgeBaseTemplateUriRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'RemoveKnowledgeBaseTemplateUriResponse' => [ 'type' => 'structure', 'members' => [], ], 'RenderingConfiguration' => [ 'type' => 'structure', 'members' => [ 'templateUri' => [ 'shape' => 'Uri', ], ], ], 'RequestTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResultData' => [ 'type' => 'structure', 'required' => [ 'resultId', ], 'members' => [ 'data' => [ 'shape' => 'DataSummary', ], 'document' => [ 'shape' => 'Document', ], 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], 'resultId' => [ 'shape' => 'Uuid', ], 'type' => [ 'shape' => 'QueryResultType', ], ], ], 'SearchContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'searchExpression', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'searchExpression' => [ 'shape' => 'SearchExpression', ], ], ], 'SearchContentResponse' => [ 'type' => 'structure', 'required' => [ 'contentSummaries', ], 'members' => [ 'contentSummaries' => [ 'shape' => 'ContentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchExpression' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], ], ], 'SearchQuickResponsesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'searchExpression', ], 'members' => [ 'attributes' => [ 'shape' => 'ContactAttributes', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'searchExpression' => [ 'shape' => 'QuickResponseSearchExpression', ], ], ], 'SearchQuickResponsesResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'nextToken' => [ 'shape' => 'NonEmptyString', ], 'results' => [ 'shape' => 'QuickResponseSearchResultsList', ], ], ], 'SearchSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'searchExpression', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'searchExpression' => [ 'shape' => 'SearchExpression', ], ], ], 'SearchSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessionSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sessionSummaries' => [ 'shape' => 'SessionSummaries', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionData' => [ 'type' => 'structure', 'required' => [ 'name', 'sessionArn', 'sessionId', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'integrationConfiguration' => [ 'shape' => 'SessionIntegrationConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'sessionArn' => [ 'shape' => 'Arn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'SessionIntegrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'topicIntegrationArn' => [ 'shape' => 'GenericArn', ], ], ], 'SessionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSummary', ], ], 'SessionSummary' => [ 'type' => 'structure', 'required' => [ 'assistantArn', 'assistantId', 'sessionArn', 'sessionId', ], 'members' => [ 'assistantArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'sessionArn' => [ 'shape' => 'Arn', ], 'sessionId' => [ 'shape' => 'Uuid', ], ], ], 'ShortCutKey' => [ 'type' => 'string', 'max' => 10, 'min' => 1, ], 'SourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'appIntegrations' => [ 'shape' => 'AppIntegrationsConfiguration', ], ], 'union' => true, ], 'SourceContentDataDetails' => [ 'type' => 'structure', 'required' => [ 'id', 'rankingData', 'textData', 'type', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'rankingData' => [ 'shape' => 'RankingData', ], 'textData' => [ 'shape' => 'TextData', ], 'type' => [ 'shape' => 'SourceContentType', ], ], ], 'SourceContentType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', ], ], 'StartContentUploadRequest' => [ 'type' => 'structure', 'required' => [ 'contentType', 'knowledgeBaseId', ], 'members' => [ 'contentType' => [ 'shape' => 'ContentType', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'presignedUrlTimeToLive' => [ 'shape' => 'TimeToLive', ], ], ], 'StartContentUploadResponse' => [ 'type' => 'structure', 'required' => [ 'headersToInclude', 'uploadId', 'url', 'urlExpiry', ], 'members' => [ 'headersToInclude' => [ 'shape' => 'Headers', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], ], ], 'StartImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'importJobType', 'knowledgeBaseId', 'uploadId', ], 'members' => [ 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'uploadId' => [ 'shape' => 'UploadId', ], ], ], 'StartImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'importJob' => [ 'shape' => 'ImportJobData', ], ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_epoch_seconds' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'TagCondition' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagFilter' => [ 'type' => 'structure', 'members' => [ 'andConditions' => [ 'shape' => 'AndConditions', ], 'orConditions' => [ 'shape' => 'OrConditions', ], 'tagCondition' => [ 'shape' => 'TagCondition', ], ], 'union' => true, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'RECOMMENDATION', 'RESULT', ], ], 'TextData' => [ 'type' => 'structure', 'members' => [ 'excerpt' => [ 'shape' => 'DocumentText', ], 'title' => [ 'shape' => 'DocumentText', ], ], ], 'TimeToLive' => [ 'type' => 'integer', 'box' => true, 'max' => 60, 'min' => 1, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContentRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'overrideLinkOutUri' => [ 'shape' => 'Uri', ], 'removeOverrideLinkOutUri' => [ 'shape' => 'Boolean', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'title' => [ 'shape' => 'ContentTitle', ], 'uploadId' => [ 'shape' => 'UploadId', ], ], ], 'UpdateContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'UpdateKnowledgeBaseTemplateUriRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'templateUri', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'templateUri' => [ 'shape' => 'Uri', ], ], ], 'UpdateKnowledgeBaseTemplateUriResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'UpdateQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'quickResponseId', ], 'members' => [ 'channels' => [ 'shape' => 'Channels', ], 'content' => [ 'shape' => 'QuickResponseDataProvider', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'isActive' => [ 'shape' => 'Boolean', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'language' => [ 'shape' => 'LanguageCode', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], 'removeDescription' => [ 'shape' => 'Boolean', ], 'removeGroupingConfiguration' => [ 'shape' => 'Boolean', ], 'removeShortcutKey' => [ 'shape' => 'Boolean', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], ], ], 'UpdateQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'UpdateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'description' => [ 'shape' => 'Description', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], ], ], 'UpdateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'UploadId' => [ 'type' => 'string', 'max' => 1200, 'min' => 1, ], 'Uri' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Url' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'Uuid' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'UuidOrArn' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$|^arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})?$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'WaitTimeSeconds' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], ],];
