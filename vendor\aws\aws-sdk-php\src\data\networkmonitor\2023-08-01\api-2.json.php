<?php
// This file was auto-generated from sdk-root/src/data/networkmonitor/2023-08-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-08-01', 'endpointPrefix' => 'networkmonitor', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon CloudWatch Network Monitor', 'serviceId' => 'NetworkMonitor', 'signatureVersion' => 'v4', 'signingName' => 'networkmonitor', 'uid' => 'networkmonitor-2023-08-01', ], 'operations' => [ 'CreateMonitor' => [ 'name' => 'CreateMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMonitorInput', ], 'output' => [ 'shape' => 'CreateMonitorOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateProbe' => [ 'name' => 'CreateProbe', 'http' => [ 'method' => 'POST', 'requestUri' => '/monitors/{monitorName}/probes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateProbeInput', ], 'output' => [ 'shape' => 'CreateProbeOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteMonitor' => [ 'name' => 'DeleteMonitor', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMonitorInput', ], 'output' => [ 'shape' => 'DeleteMonitorOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteProbe' => [ 'name' => 'DeleteProbe', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/monitors/{monitorName}/probes/{probeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProbeInput', ], 'output' => [ 'shape' => 'DeleteProbeOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'GetMonitor' => [ 'name' => 'GetMonitor', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMonitorInput', ], 'output' => [ 'shape' => 'GetMonitorOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProbe' => [ 'name' => 'GetProbe', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors/{monitorName}/probes/{probeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProbeInput', ], 'output' => [ 'shape' => 'GetProbeOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListMonitors' => [ 'name' => 'ListMonitors', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMonitorsInput', ], 'output' => [ 'shape' => 'ListMonitorsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateMonitor' => [ 'name' => 'UpdateMonitor', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMonitorInput', ], 'output' => [ 'shape' => 'UpdateMonitorOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateProbe' => [ 'name' => 'UpdateProbe', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/monitors/{monitorName}/probes/{probeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProbeInput', ], 'output' => [ 'shape' => 'UpdateProbeOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddressFamily' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', ], ], 'AggregationPeriod' => [ 'type' => 'long', 'box' => true, 'min' => 30, ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', ], 'probes' => [ 'shape' => 'CreateMonitorProbeInputList', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'state', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'state' => [ 'shape' => 'MonitorState', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMonitorProbeInput' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'probeTags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMonitorProbeInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateMonitorProbeInput', ], ], 'CreateProbeInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'probe', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'probe' => [ 'shape' => 'ProbeInput', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateProbeOutput' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'probeId' => [ 'shape' => 'ProbeId', ], 'probeArn' => [ 'shape' => 'Arn', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'addressFamily' => [ 'shape' => 'AddressFamily', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'state' => [ 'shape' => 'ProbeState', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DeleteMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], ], ], 'DeleteMonitorOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProbeInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'probeId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'probeId' => [ 'shape' => 'ProbeId', 'location' => 'uri', 'locationName' => 'probeId', ], ], ], 'DeleteProbeOutput' => [ 'type' => 'structure', 'members' => [], ], 'Destination' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'GetMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], ], ], 'GetMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'state', 'aggregationPeriod', 'createdAt', 'modifiedAt', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'state' => [ 'shape' => 'MonitorState', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'tags' => [ 'shape' => 'TagMap', ], 'probes' => [ 'shape' => 'ProbeList', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'GetProbeInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'probeId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'probeId' => [ 'shape' => 'ProbeId', 'location' => 'uri', 'locationName' => 'probeId', ], ], ], 'GetProbeOutput' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'probeId' => [ 'shape' => 'ProbeId', ], 'probeArn' => [ 'shape' => 'Arn', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'addressFamily' => [ 'shape' => 'AddressFamily', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'state' => [ 'shape' => 'ProbeState', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'Iso8601Timestamp' => [ 'type' => 'timestamp', ], 'ListMonitorsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'state' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'state', ], ], ], 'ListMonitorsOutput' => [ 'type' => 'structure', 'required' => [ 'monitors', ], 'members' => [ 'monitors' => [ 'shape' => 'MonitorList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'MonitorArn' => [ 'type' => 'string', 'max' => 512, 'min' => 20, 'pattern' => 'arn:.*', ], 'MonitorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorSummary', ], ], 'MonitorState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'INACTIVE', 'ERROR', 'DELETING', ], ], 'MonitorSummary' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'state', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'state' => [ 'shape' => 'MonitorState', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'PacketSize' => [ 'type' => 'integer', 'box' => true, 'max' => 8500, 'min' => 56, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'Port' => [ 'type' => 'integer', 'box' => true, 'max' => 65536, 'min' => 0, ], 'Probe' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'probeId' => [ 'shape' => 'ProbeId', ], 'probeArn' => [ 'shape' => 'Arn', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'addressFamily' => [ 'shape' => 'AddressFamily', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'state' => [ 'shape' => 'ProbeState', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ProbeId' => [ 'type' => 'string', 'pattern' => 'probe-[a-z0-9A-Z-]{21,64}', ], 'ProbeInput' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ProbeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Probe', ], ], 'ProbeState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'INACTIVE', 'ERROR', 'DELETING', 'DELETED', ], ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'ICMP', ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'aggregationPeriod', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], ], ], 'UpdateMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'state', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'state' => [ 'shape' => 'MonitorState', ], 'aggregationPeriod' => [ 'shape' => 'AggregationPeriod', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateProbeInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'probeId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'probeId' => [ 'shape' => 'ProbeId', 'location' => 'uri', 'locationName' => 'probeId', ], 'state' => [ 'shape' => 'ProbeState', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], ], ], 'UpdateProbeOutput' => [ 'type' => 'structure', 'required' => [ 'sourceArn', 'destination', 'protocol', ], 'members' => [ 'probeId' => [ 'shape' => 'ProbeId', ], 'probeArn' => [ 'shape' => 'Arn', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'destination' => [ 'shape' => 'Destination', ], 'destinationPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'Protocol', ], 'packetSize' => [ 'shape' => 'PacketSize', ], 'addressFamily' => [ 'shape' => 'AddressFamily', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'state' => [ 'shape' => 'ProbeState', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VpcId' => [ 'type' => 'string', 'pattern' => 'vpc-[a-zA-Z0-9]{8,32}', ], ],];
