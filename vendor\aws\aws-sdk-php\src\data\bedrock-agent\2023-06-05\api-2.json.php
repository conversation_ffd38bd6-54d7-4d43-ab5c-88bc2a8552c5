<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent/2023-06-05/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-06-05', 'endpointPrefix' => 'bedrock-agent', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Agents for Amazon Bedrock', 'serviceId' => 'Bedrock Agent', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-agent-2023-06-05', ], 'operations' => [ 'AssociateAgentKnowledgeBase' => [ 'name' => 'AssociateAgentKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'AssociateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgent' => [ 'name' => 'CreateAgent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAgentRequest', ], 'output' => [ 'shape' => 'CreateAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgentActionGroup' => [ 'name' => 'CreateAgentActionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAgentActionGroupRequest', ], 'output' => [ 'shape' => 'CreateAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgentAlias' => [ 'name' => 'CreateAgentAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentaliases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAgentAliasRequest', ], 'output' => [ 'shape' => 'CreateAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateKnowledgeBase' => [ 'name' => 'CreateKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'CreateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteAgent' => [ 'name' => 'DeleteAgent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentRequest', ], 'output' => [ 'shape' => 'DeleteAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAgentActionGroup' => [ 'name' => 'DeleteAgentActionGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAgentActionGroupRequest', ], 'output' => [ 'shape' => 'DeleteAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAgentAlias' => [ 'name' => 'DeleteAgentAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentAliasRequest', ], 'output' => [ 'shape' => 'DeleteAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAgentVersion' => [ 'name' => 'DeleteAgentVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentVersionRequest', ], 'output' => [ 'shape' => 'DeleteAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteKnowledgeBase' => [ 'name' => 'DeleteKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DeleteKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateAgentKnowledgeBase' => [ 'name' => 'DisassociateAgentKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DisassociateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetAgent' => [ 'name' => 'GetAgent', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentRequest', ], 'output' => [ 'shape' => 'GetAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentActionGroup' => [ 'name' => 'GetAgentActionGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentActionGroupRequest', ], 'output' => [ 'shape' => 'GetAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentAlias' => [ 'name' => 'GetAgentAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentAliasRequest', ], 'output' => [ 'shape' => 'GetAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentKnowledgeBase' => [ 'name' => 'GetAgentKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentVersion' => [ 'name' => 'GetAgentVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentVersionRequest', ], 'output' => [ 'shape' => 'GetAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetIngestionJob' => [ 'name' => 'GetIngestionJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/{ingestionJobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIngestionJobRequest', ], 'output' => [ 'shape' => 'GetIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKnowledgeBase' => [ 'name' => 'GetKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentActionGroups' => [ 'name' => 'ListAgentActionGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentActionGroupsRequest', ], 'output' => [ 'shape' => 'ListAgentActionGroupsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentAliases' => [ 'name' => 'ListAgentAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentaliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentAliasesRequest', ], 'output' => [ 'shape' => 'ListAgentAliasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentKnowledgeBases' => [ 'name' => 'ListAgentKnowledgeBases', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListAgentKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentVersions' => [ 'name' => 'ListAgentVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentVersionsRequest', ], 'output' => [ 'shape' => 'ListAgentVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgents' => [ 'name' => 'ListAgents', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentsRequest', ], 'output' => [ 'shape' => 'ListAgentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListIngestionJobs' => [ 'name' => 'ListIngestionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIngestionJobsRequest', ], 'output' => [ 'shape' => 'ListIngestionJobsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKnowledgeBases' => [ 'name' => 'ListKnowledgeBases', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PrepareAgent' => [ 'name' => 'PrepareAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PrepareAgentRequest', ], 'output' => [ 'shape' => 'PrepareAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StartIngestionJob' => [ 'name' => 'StartIngestionJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartIngestionJobRequest', ], 'output' => [ 'shape' => 'StartIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateAgent' => [ 'name' => 'UpdateAgent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAgentRequest', ], 'output' => [ 'shape' => 'UpdateAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentActionGroup' => [ 'name' => 'UpdateAgentActionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentActionGroupRequest', ], 'output' => [ 'shape' => 'UpdateAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentAlias' => [ 'name' => 'UpdateAgentAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAgentAliasRequest', ], 'output' => [ 'shape' => 'UpdateAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentKnowledgeBase' => [ 'name' => 'UpdateAgentKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'UpdateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateKnowledgeBase' => [ 'name' => 'UpdateKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'UpdateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'APISchema' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'Payload', ], 's3' => [ 'shape' => 'S3Identifier', ], ], 'union' => true, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionGroupExecutor' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaArn', ], ], 'union' => true, ], 'ActionGroupSignature' => [ 'type' => 'string', 'enum' => [ 'AMAZON.UserInput', ], ], 'ActionGroupState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ActionGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionGroupSummary', ], 'max' => 10, 'min' => 0, ], 'ActionGroupSummary' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'actionGroupState', 'updatedAt', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'description' => [ 'shape' => 'Description', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'Agent' => [ 'type' => 'structure', 'required' => [ 'agentArn', 'agentId', 'agentName', 'agentResourceRoleArn', 'agentStatus', 'agentVersion', 'createdAt', 'idleSessionTTLInSeconds', 'updatedAt', ], 'members' => [ 'agentArn' => [ 'shape' => 'AgentArn', ], 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'DraftVersion', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'preparedAt' => [ 'shape' => 'DateTimestamp', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentActionGroup' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'actionGroupState', 'agentId', 'agentVersion', 'createdAt', 'updatedAt', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupId' => [ 'shape' => 'Id', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'parentActionSignature' => [ 'shape' => 'ActionGroupSignature', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAlias' => [ 'type' => 'structure', 'required' => [ 'agentAliasArn', 'agentAliasId', 'agentAliasName', 'agentAliasStatus', 'agentId', 'createdAt', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'agentAliasArn' => [ 'shape' => 'AgentAliasArn', ], 'agentAliasHistoryEvents' => [ 'shape' => 'AgentAliasHistoryEvents', ], 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'agentId' => [ 'shape' => 'Id', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAliasArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent-alias/[0-9a-zA-Z]{10}/[0-9a-zA-Z]{10}$', ], 'AgentAliasHistoryEvent' => [ 'type' => 'structure', 'members' => [ 'endDate' => [ 'shape' => 'DateTimestamp', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'startDate' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAliasHistoryEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasHistoryEvent', ], 'max' => 10, 'min' => 0, ], 'AgentAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'AgentAliasRoutingConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasRoutingConfigurationListItem', ], 'max' => 1, 'min' => 0, ], 'AgentAliasRoutingConfigurationListItem' => [ 'type' => 'structure', 'required' => [ 'agentVersion', ], 'members' => [ 'agentVersion' => [ 'shape' => 'Version', ], ], ], 'AgentAliasStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'PREPARED', 'FAILED', 'UPDATING', 'DELETING', ], ], 'AgentAliasSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasSummary', ], 'max' => 10, 'min' => 0, ], 'AgentAliasSummary' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasName', 'agentAliasStatus', 'createdAt', 'updatedAt', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent/[0-9a-zA-Z]{10}$', ], 'AgentKnowledgeBase' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'createdAt', 'description', 'knowledgeBaseId', 'knowledgeBaseState', 'updatedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentKnowledgeBaseSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentKnowledgeBaseSummary', ], 'max' => 10, 'min' => 0, ], 'AgentKnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseState', 'updatedAt', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/(service-role/)?AmazonBedrockExecutionRoleForAgents_.+$', ], 'AgentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'PREPARING', 'PREPARED', 'NOT_PREPARED', 'DELETING', 'FAILED', 'VERSIONING', 'UPDATING', ], ], 'AgentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentSummary', ], 'max' => 10, 'min' => 0, ], 'AgentSummary' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentName', 'agentStatus', 'updatedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'description' => [ 'shape' => 'Description', ], 'latestAgentVersion' => [ 'shape' => 'Version', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentVersion' => [ 'type' => 'structure', 'required' => [ 'agentArn', 'agentId', 'agentName', 'agentResourceRoleArn', 'agentStatus', 'createdAt', 'idleSessionTTLInSeconds', 'updatedAt', 'version', ], 'members' => [ 'agentArn' => [ 'shape' => 'AgentArn', ], 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'AgentVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentVersionSummary', ], 'max' => 10, 'min' => 0, ], 'AgentVersionSummary' => [ 'type' => 'structure', 'required' => [ 'agentName', 'agentStatus', 'agentVersion', 'createdAt', 'updatedAt', ], 'members' => [ 'agentName' => [ 'shape' => 'Name', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'Version', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AssociateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'description', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], ], ], 'AssociateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'BasePromptTemplate' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, ], 'BedrockEmbeddingModelArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}))$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'chunkingStrategy', ], 'members' => [ 'chunkingStrategy' => [ 'shape' => 'ChunkingStrategy', ], 'fixedSizeChunkingConfiguration' => [ 'shape' => 'FixedSizeChunkingConfiguration', ], ], ], 'ChunkingStrategy' => [ 'type' => 'string', 'enum' => [ 'FIXED_SIZE', 'NONE', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 33, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ColumnName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\-]+$', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupName', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'parentActionGroupSignature' => [ 'shape' => 'ActionGroupSignature', ], ], ], 'CreateAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'CreateAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasName', 'agentId', ], 'members' => [ 'agentAliasName' => [ 'shape' => 'Name', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'CreateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentName', 'agentResourceRoleArn', ], 'members' => [ 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceConfiguration', 'knowledgeBaseId', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'CreateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseConfiguration', 'name', 'roleArn', 'storageConfiguration', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'CreationMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'OVERRIDDEN', ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataSourceConfiguration', 'dataSourceId', 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 's3Configuration' => [ 'shape' => 'S3DataSourceConfiguration', ], 'type' => [ 'shape' => 'DataSourceType', ], ], ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETING', ], ], 'DataSourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSummary', ], ], 'DataSourceSummary' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'DateTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentActionGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'DeleteAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasStatus', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'agentId' => [ 'shape' => 'Id', ], ], ], 'DeleteAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], ], ], 'DeleteAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentVersionResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', ], ], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', 'status', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'DeleteKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'status', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'DisassociateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DisassociateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DraftVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^DRAFT$', ], 'FailureReason' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'FailureReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureReason', ], 'max' => 2048, 'min' => 0, ], 'FieldName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'FixedSizeChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', 'overlapPercentage', ], 'members' => [ 'maxTokens' => [ 'shape' => 'FixedSizeChunkingConfigurationMaxTokensInteger', ], 'overlapPercentage' => [ 'shape' => 'FixedSizeChunkingConfigurationOverlapPercentageInteger', ], ], ], 'FixedSizeChunkingConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'FixedSizeChunkingConfigurationOverlapPercentageInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 99, 'min' => 1, ], 'GetAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], ], ], 'GetAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'GetAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'GetAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'GetAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'GetAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'GetAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'GetAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], ], ], 'GetAgentVersionResponse' => [ 'type' => 'structure', 'required' => [ 'agentVersion', ], 'members' => [ 'agentVersion' => [ 'shape' => 'AgentVersion', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'GetIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'ingestionJobId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'ingestionJobId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetIngestionJobResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJob', ], 'members' => [ 'ingestionJob' => [ 'shape' => 'IngestionJob', ], ], ], 'GetKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'Id' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z]{10}$', ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maximumLength' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topK' => [ 'shape' => 'TopK', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'IngestionJob' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', 'startedAt', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'ingestionJobId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'startedAt' => [ 'shape' => 'DateTimestamp', ], 'statistics' => [ 'shape' => 'IngestionJobStatistics', ], 'status' => [ 'shape' => 'IngestionJobStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'IngestionJobFilter' => [ 'type' => 'structure', 'required' => [ 'attribute', 'operator', 'values', ], 'members' => [ 'attribute' => [ 'shape' => 'IngestionJobFilterAttribute', ], 'operator' => [ 'shape' => 'IngestionJobFilterOperator', ], 'values' => [ 'shape' => 'IngestionJobFilterValues', ], ], ], 'IngestionJobFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'STATUS', ], ], 'IngestionJobFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', ], ], 'IngestionJobFilterValue' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '^.*$', ], 'IngestionJobFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobFilterValue', ], 'max' => 10, 'min' => 0, ], 'IngestionJobFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobFilter', ], 'max' => 1, 'min' => 1, ], 'IngestionJobSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'IngestionJobSortByAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'IngestionJobSortByAttribute' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'STARTED_AT', ], ], 'IngestionJobStatistics' => [ 'type' => 'structure', 'members' => [ 'numberOfDocumentsDeleted' => [ 'shape' => 'PrimitiveLong', ], 'numberOfDocumentsFailed' => [ 'shape' => 'PrimitiveLong', ], 'numberOfDocumentsScanned' => [ 'shape' => 'PrimitiveLong', ], 'numberOfMetadataDocumentsModified' => [ 'shape' => 'PrimitiveLong', ], 'numberOfMetadataDocumentsScanned' => [ 'shape' => 'PrimitiveLong', ], 'numberOfModifiedDocumentsIndexed' => [ 'shape' => 'PrimitiveLong', ], 'numberOfNewDocumentsIndexed' => [ 'shape' => 'PrimitiveLong', ], ], ], 'IngestionJobStatus' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'IngestionJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobSummary', ], ], 'IngestionJobSummary' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', 'startedAt', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'ingestionJobId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'startedAt' => [ 'shape' => 'DateTimestamp', ], 'statistics' => [ 'shape' => 'IngestionJobStatistics', ], 'status' => [ 'shape' => 'IngestionJobStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'Instruction' => [ 'type' => 'string', 'max' => 1200, 'min' => 40, 'sensitive' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'KnowledgeBase' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'knowledgeBaseArn', 'knowledgeBaseConfiguration', 'knowledgeBaseId', 'name', 'roleArn', 'status', 'storageConfiguration', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'knowledgeBaseArn' => [ 'shape' => 'KnowledgeBaseArn', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'KnowledgeBaseArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:knowledge-base/[0-9a-zA-Z]+$', ], 'KnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'KnowledgeBaseType', ], 'vectorKnowledgeBaseConfiguration' => [ 'shape' => 'VectorKnowledgeBaseConfiguration', ], ], ], 'KnowledgeBaseRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+$', ], 'KnowledgeBaseState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'KnowledgeBaseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'UPDATING', 'FAILED', ], ], 'KnowledgeBaseStorageType' => [ 'type' => 'string', 'enum' => [ 'OPENSEARCH_SERVERLESS', 'PINECONE', 'REDIS_ENTERPRISE_CLOUD', 'RDS', ], ], 'KnowledgeBaseSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseSummary', ], ], 'KnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'KnowledgeBaseType' => [ 'type' => 'string', 'enum' => [ 'VECTOR', ], ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$', ], 'ListAgentActionGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentActionGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'actionGroupSummaries', ], 'members' => [ 'actionGroupSummaries' => [ 'shape' => 'ActionGroupSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentAliasesResponse' => [ 'type' => 'structure', 'required' => [ 'agentAliasSummaries', ], 'members' => [ 'agentAliasSummaries' => [ 'shape' => 'AgentAliasSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentKnowledgeBasesRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBaseSummaries', ], 'members' => [ 'agentKnowledgeBaseSummaries' => [ 'shape' => 'AgentKnowledgeBaseSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'agentVersionSummaries', ], 'members' => [ 'agentVersionSummaries' => [ 'shape' => 'AgentVersionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentsResponse' => [ 'type' => 'structure', 'required' => [ 'agentSummaries', ], 'members' => [ 'agentSummaries' => [ 'shape' => 'AgentSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'required' => [ 'dataSourceSummaries', ], 'members' => [ 'dataSourceSummaries' => [ 'shape' => 'DataSourceSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIngestionJobsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'filters' => [ 'shape' => 'IngestionJobFilters', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'IngestionJobSortBy', ], ], ], 'ListIngestionJobsResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJobSummaries', ], 'members' => [ 'ingestionJobSummaries' => [ 'shape' => 'IngestionJobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListKnowledgeBasesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseSummaries', ], 'members' => [ 'knowledgeBaseSummaries' => [ 'shape' => 'KnowledgeBaseSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaximumLength' => [ 'type' => 'integer', 'box' => true, 'max' => 4096, 'min' => 0, ], 'ModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+)$', ], 'Name' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S*$', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]+$', ], 'NumericalVersion' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,5}$', ], 'OpenSearchServerlessCollectionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:aoss:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:collection/[a-z0-9-]{3,32}$', ], 'OpenSearchServerlessConfiguration' => [ 'type' => 'structure', 'required' => [ 'collectionArn', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'collectionArn' => [ 'shape' => 'OpenSearchServerlessCollectionArn', ], 'fieldMapping' => [ 'shape' => 'OpenSearchServerlessFieldMapping', ], 'vectorIndexName' => [ 'shape' => 'OpenSearchServerlessIndexName', ], ], ], 'OpenSearchServerlessFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'OpenSearchServerlessIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'Payload' => [ 'type' => 'string', 'sensitive' => true, ], 'PineconeConfiguration' => [ 'type' => 'structure', 'required' => [ 'connectionString', 'credentialsSecretArn', 'fieldMapping', ], 'members' => [ 'connectionString' => [ 'shape' => 'PineconeConnectionString', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'fieldMapping' => [ 'shape' => 'PineconeFieldMapping', ], 'namespace' => [ 'shape' => 'PineconeNamespace', ], ], ], 'PineconeConnectionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'PineconeFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], ], ], 'PineconeNamespace' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'PrepareAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'PrepareAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', 'agentVersion', 'preparedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'Version', ], 'preparedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'PrimitiveLong' => [ 'type' => 'long', ], 'PromptConfiguration' => [ 'type' => 'structure', 'members' => [ 'basePromptTemplate' => [ 'shape' => 'BasePromptTemplate', ], 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'promptState' => [ 'shape' => 'PromptState', ], 'promptType' => [ 'shape' => 'PromptType', ], ], ], 'PromptConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptConfiguration', ], 'max' => 10, 'min' => 0, ], 'PromptOverrideConfiguration' => [ 'type' => 'structure', 'required' => [ 'promptConfigurations', ], 'members' => [ 'overrideLambda' => [ 'shape' => 'LambdaArn', ], 'promptConfigurations' => [ 'shape' => 'PromptConfigurations', ], ], 'sensitive' => true, ], 'PromptState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PromptType' => [ 'type' => 'string', 'enum' => [ 'PRE_PROCESSING', 'ORCHESTRATION', 'POST_PROCESSING', 'KNOWLEDGE_BASE_RESPONSE_GENERATION', ], ], 'RdsArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):rds:[a-zA-Z0-9-]*:[0-9]{12}:cluster:[a-zA-Z0-9-]{1,63}$', ], 'RdsConfiguration' => [ 'type' => 'structure', 'required' => [ 'credentialsSecretArn', 'databaseName', 'fieldMapping', 'resourceArn', 'tableName', ], 'members' => [ 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'databaseName' => [ 'shape' => 'RdsDatabaseName', ], 'fieldMapping' => [ 'shape' => 'RdsFieldMapping', ], 'resourceArn' => [ 'shape' => 'RdsArn', ], 'tableName' => [ 'shape' => 'RdsTableName', ], ], ], 'RdsDatabaseName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\-]+$', ], 'RdsFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'primaryKeyField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'ColumnName', ], 'primaryKeyField' => [ 'shape' => 'ColumnName', ], 'textField' => [ 'shape' => 'ColumnName', ], 'vectorField' => [ 'shape' => 'ColumnName', ], ], ], 'RdsTableName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\.\\-]+$', ], 'RecommendedAction' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'RecommendedActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedAction', ], 'max' => 2048, 'min' => 0, ], 'RedisEnterpriseCloudConfiguration' => [ 'type' => 'structure', 'required' => [ 'credentialsSecretArn', 'endpoint', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'endpoint' => [ 'shape' => 'RedisEnterpriseCloudEndpoint', ], 'fieldMapping' => [ 'shape' => 'RedisEnterpriseCloudFieldMapping', ], 'vectorIndexName' => [ 'shape' => 'RedisEnterpriseCloudIndexName', ], ], ], 'RedisEnterpriseCloudEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'RedisEnterpriseCloudFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'RedisEnterpriseCloudIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):s3:::[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3DataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketArn', ], 'members' => [ 'bucketArn' => [ 'shape' => 'S3BucketArn', ], 'inclusionPrefixes' => [ 'shape' => 'S3Prefixes', ], ], ], 'S3Identifier' => [ 'type' => 'structure', 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3ObjectKey' => [ 'shape' => 'S3ObjectKey', ], ], ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\.\\-\\!\\*\\_\\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\\'\\(\\)\\/a-zA-Z0-9]*$', ], 'S3Prefix' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'S3Prefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Prefix', ], 'max' => 1, 'min' => 1, ], 'SecretArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):secretsmanager:[a-z0-9-]{1,20}:([0-9]{12}|):secret:[a-zA-Z0-9!/_+=.@-]{1,512}$', ], 'ServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionTTL' => [ 'type' => 'integer', 'box' => true, 'max' => 3600, 'min' => 60, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'StartIngestionJobResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJob', ], 'members' => [ 'ingestionJob' => [ 'shape' => 'IngestionJob', ], ], ], 'StopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 4, 'min' => 0, ], 'StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'opensearchServerlessConfiguration' => [ 'shape' => 'OpenSearchServerlessConfiguration', ], 'pineconeConfiguration' => [ 'shape' => 'PineconeConfiguration', ], 'rdsConfiguration' => [ 'shape' => 'RdsConfiguration', ], 'redisEnterpriseCloudConfiguration' => [ 'shape' => 'RedisEnterpriseCloudConfiguration', ], 'type' => [ 'shape' => 'KnowledgeBaseStorageType', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TaggableResourcesArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '(^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:(agent|agent-alias|knowledge-base)/[A-Z0-9]{10}(?:/[A-Z0-9]{10})?$)', ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'Temperature' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TopK' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'TopP' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'description' => [ 'shape' => 'Description', ], 'parentActionGroupSignature' => [ 'shape' => 'ActionGroupSignature', ], ], ], 'UpdateAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'UpdateAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasName', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], ], ], 'UpdateAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'UpdateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], ], ], 'UpdateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'UpdateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentName', 'agentResourceRoleArn', 'foundationModel', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], ], ], 'UpdateAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceConfiguration', 'dataSourceId', 'knowledgeBaseId', 'name', ], 'members' => [ 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'UpdateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseConfiguration', 'knowledgeBaseId', 'name', 'roleArn', 'storageConfiguration', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'UpdateKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'name' => [ 'shape' => 'NonBlankString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'VectorIngestionConfiguration' => [ 'type' => 'structure', 'members' => [ 'chunkingConfiguration' => [ 'shape' => 'ChunkingConfiguration', ], ], ], 'VectorKnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'embeddingModelArn', ], 'members' => [ 'embeddingModelArn' => [ 'shape' => 'BedrockEmbeddingModelArn', ], ], ], 'Version' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$', ], ],];
