<?php
// This file was auto-generated from sdk-root/src/data/ce/2017-10-25/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-25', 'endpointPrefix' => 'ce', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'AWS Cost Explorer', 'serviceFullName' => 'AWS Cost Explorer Service', 'serviceId' => 'Cost Explorer', 'signatureVersion' => 'v4', 'signingName' => 'ce', 'targetPrefix' => 'AWSInsightsIndexService', 'uid' => 'ce-2017-10-25', ], 'operations' => [ 'CreateAnomalyMonitor' => [ 'name' => 'CreateAnomalyMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAnomalyMonitorRequest', ], 'output' => [ 'shape' => 'CreateAnomalyMonitorResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'CreateAnomalySubscription' => [ 'name' => 'CreateAnomalySubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAnomalySubscriptionRequest', ], 'output' => [ 'shape' => 'CreateAnomalySubscriptionResponse', ], 'errors' => [ [ 'shape' => 'UnknownMonitorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateCostCategoryDefinition' => [ 'name' => 'CreateCostCategoryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCostCategoryDefinitionRequest', ], 'output' => [ 'shape' => 'CreateCostCategoryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteAnomalyMonitor' => [ 'name' => 'DeleteAnomalyMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAnomalyMonitorRequest', ], 'output' => [ 'shape' => 'DeleteAnomalyMonitorResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownMonitorException', ], ], ], 'DeleteAnomalySubscription' => [ 'name' => 'DeleteAnomalySubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAnomalySubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteAnomalySubscriptionResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownSubscriptionException', ], ], ], 'DeleteCostCategoryDefinition' => [ 'name' => 'DeleteCostCategoryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCostCategoryDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteCostCategoryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DescribeCostCategoryDefinition' => [ 'name' => 'DescribeCostCategoryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCostCategoryDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeCostCategoryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetAnomalies' => [ 'name' => 'GetAnomalies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAnomaliesRequest', ], 'output' => [ 'shape' => 'GetAnomaliesResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetAnomalyMonitors' => [ 'name' => 'GetAnomalyMonitors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAnomalyMonitorsRequest', ], 'output' => [ 'shape' => 'GetAnomalyMonitorsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownMonitorException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetAnomalySubscriptions' => [ 'name' => 'GetAnomalySubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAnomalySubscriptionsRequest', ], 'output' => [ 'shape' => 'GetAnomalySubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownSubscriptionException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetApproximateUsageRecords' => [ 'name' => 'GetApproximateUsageRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApproximateUsageRecordsRequest', ], 'output' => [ 'shape' => 'GetApproximateUsageRecordsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'GetCostAndUsage' => [ 'name' => 'GetCostAndUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCostAndUsageRequest', ], 'output' => [ 'shape' => 'GetCostAndUsageResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BillExpirationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'RequestChangedException', ], ], ], 'GetCostAndUsageWithResources' => [ 'name' => 'GetCostAndUsageWithResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCostAndUsageWithResourcesRequest', ], 'output' => [ 'shape' => 'GetCostAndUsageWithResourcesResponse', ], 'errors' => [ [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BillExpirationException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'RequestChangedException', ], ], ], 'GetCostCategories' => [ 'name' => 'GetCostCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCostCategoriesRequest', ], 'output' => [ 'shape' => 'GetCostCategoriesResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BillExpirationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'RequestChangedException', ], ], ], 'GetCostForecast' => [ 'name' => 'GetCostForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCostForecastRequest', ], 'output' => [ 'shape' => 'GetCostForecastResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'GetDimensionValues' => [ 'name' => 'GetDimensionValues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDimensionValuesRequest', ], 'output' => [ 'shape' => 'GetDimensionValuesResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BillExpirationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'RequestChangedException', ], ], ], 'GetReservationCoverage' => [ 'name' => 'GetReservationCoverage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReservationCoverageRequest', ], 'output' => [ 'shape' => 'GetReservationCoverageResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetReservationPurchaseRecommendation' => [ 'name' => 'GetReservationPurchaseRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReservationPurchaseRecommendationRequest', ], 'output' => [ 'shape' => 'GetReservationPurchaseRecommendationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetReservationUtilization' => [ 'name' => 'GetReservationUtilization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReservationUtilizationRequest', ], 'output' => [ 'shape' => 'GetReservationUtilizationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetRightsizingRecommendation' => [ 'name' => 'GetRightsizingRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRightsizingRecommendationRequest', ], 'output' => [ 'shape' => 'GetRightsizingRecommendationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetSavingsPlanPurchaseRecommendationDetails' => [ 'name' => 'GetSavingsPlanPurchaseRecommendationDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSavingsPlanPurchaseRecommendationDetailsRequest', ], 'output' => [ 'shape' => 'GetSavingsPlanPurchaseRecommendationDetailsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'GetSavingsPlansCoverage' => [ 'name' => 'GetSavingsPlansCoverage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSavingsPlansCoverageRequest', ], 'output' => [ 'shape' => 'GetSavingsPlansCoverageResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetSavingsPlansPurchaseRecommendation' => [ 'name' => 'GetSavingsPlansPurchaseRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSavingsPlansPurchaseRecommendationRequest', ], 'output' => [ 'shape' => 'GetSavingsPlansPurchaseRecommendationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetSavingsPlansUtilization' => [ 'name' => 'GetSavingsPlansUtilization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSavingsPlansUtilizationRequest', ], 'output' => [ 'shape' => 'GetSavingsPlansUtilizationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'GetSavingsPlansUtilizationDetails' => [ 'name' => 'GetSavingsPlansUtilizationDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSavingsPlansUtilizationDetailsRequest', ], 'output' => [ 'shape' => 'GetSavingsPlansUtilizationDetailsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'GetTags' => [ 'name' => 'GetTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTagsRequest', ], 'output' => [ 'shape' => 'GetTagsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BillExpirationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'RequestChangedException', ], ], ], 'GetUsageForecast' => [ 'name' => 'GetUsageForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUsageForecastRequest', ], 'output' => [ 'shape' => 'GetUsageForecastResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'UnresolvableUsageUnitException', ], ], ], 'ListCostAllocationTagBackfillHistory' => [ 'name' => 'ListCostAllocationTagBackfillHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCostAllocationTagBackfillHistoryRequest', ], 'output' => [ 'shape' => 'ListCostAllocationTagBackfillHistoryResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListCostAllocationTags' => [ 'name' => 'ListCostAllocationTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCostAllocationTagsRequest', ], 'output' => [ 'shape' => 'ListCostAllocationTagsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListCostCategoryDefinitions' => [ 'name' => 'ListCostCategoryDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCostCategoryDefinitionsRequest', ], 'output' => [ 'shape' => 'ListCostCategoryDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'ListSavingsPlansPurchaseRecommendationGeneration' => [ 'name' => 'ListSavingsPlansPurchaseRecommendationGeneration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSavingsPlansPurchaseRecommendationGenerationRequest', ], 'output' => [ 'shape' => 'ListSavingsPlansPurchaseRecommendationGenerationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ProvideAnomalyFeedback' => [ 'name' => 'ProvideAnomalyFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ProvideAnomalyFeedbackRequest', ], 'output' => [ 'shape' => 'ProvideAnomalyFeedbackResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'StartCostAllocationTagBackfill' => [ 'name' => 'StartCostAllocationTagBackfill', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCostAllocationTagBackfillRequest', ], 'output' => [ 'shape' => 'StartCostAllocationTagBackfillResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BackfillLimitExceededException', ], ], ], 'StartSavingsPlansPurchaseRecommendationGeneration' => [ 'name' => 'StartSavingsPlansPurchaseRecommendationGeneration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSavingsPlansPurchaseRecommendationGenerationRequest', ], 'output' => [ 'shape' => 'StartSavingsPlansPurchaseRecommendationGenerationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'GenerationExistsException', ], [ 'shape' => 'DataUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateAnomalyMonitor' => [ 'name' => 'UpdateAnomalyMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAnomalyMonitorRequest', ], 'output' => [ 'shape' => 'UpdateAnomalyMonitorResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownMonitorException', ], ], ], 'UpdateAnomalySubscription' => [ 'name' => 'UpdateAnomalySubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAnomalySubscriptionRequest', ], 'output' => [ 'shape' => 'UpdateAnomalySubscriptionResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnknownMonitorException', ], [ 'shape' => 'UnknownSubscriptionException', ], ], ], 'UpdateCostAllocationTagsStatus' => [ 'name' => 'UpdateCostAllocationTagsStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCostAllocationTagsStatusRequest', ], 'output' => [ 'shape' => 'UpdateCostAllocationTagsStatusResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateCostCategoryDefinition' => [ 'name' => 'UpdateCostCategoryDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCostCategoryDefinitionRequest', ], 'output' => [ 'shape' => 'UpdateCostCategoryDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'LimitExceededException', ], ], ], ], 'shapes' => [ 'AccountScope' => [ 'type' => 'string', 'enum' => [ 'PAYER', 'LINKED', ], ], 'AmortizedRecurringFee' => [ 'type' => 'string', ], 'AmortizedUpfrontFee' => [ 'type' => 'string', ], 'Anomalies' => [ 'type' => 'list', 'member' => [ 'shape' => 'Anomaly', ], ], 'Anomaly' => [ 'type' => 'structure', 'required' => [ 'AnomalyId', 'AnomalyScore', 'Impact', 'MonitorArn', ], 'members' => [ 'AnomalyId' => [ 'shape' => 'GenericString', ], 'AnomalyStartDate' => [ 'shape' => 'YearMonthDay', ], 'AnomalyEndDate' => [ 'shape' => 'YearMonthDay', ], 'DimensionValue' => [ 'shape' => 'GenericString', ], 'RootCauses' => [ 'shape' => 'RootCauses', ], 'AnomalyScore' => [ 'shape' => 'AnomalyScore', ], 'Impact' => [ 'shape' => 'Impact', ], 'MonitorArn' => [ 'shape' => 'GenericString', ], 'Feedback' => [ 'shape' => 'AnomalyFeedbackType', ], ], ], 'AnomalyDateInterval' => [ 'type' => 'structure', 'required' => [ 'StartDate', ], 'members' => [ 'StartDate' => [ 'shape' => 'YearMonthDay', ], 'EndDate' => [ 'shape' => 'YearMonthDay', ], ], ], 'AnomalyFeedbackType' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', 'PLANNED_ACTIVITY', ], ], 'AnomalyMonitor' => [ 'type' => 'structure', 'required' => [ 'MonitorName', 'MonitorType', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], 'MonitorName' => [ 'shape' => 'GenericString', ], 'CreationDate' => [ 'shape' => 'YearMonthDay', ], 'LastUpdatedDate' => [ 'shape' => 'YearMonthDay', ], 'LastEvaluatedDate' => [ 'shape' => 'YearMonthDay', ], 'MonitorType' => [ 'shape' => 'MonitorType', ], 'MonitorDimension' => [ 'shape' => 'MonitorDimension', ], 'MonitorSpecification' => [ 'shape' => 'Expression', ], 'DimensionalValueCount' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'AnomalyMonitors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalyMonitor', ], ], 'AnomalyScore' => [ 'type' => 'structure', 'required' => [ 'MaxScore', 'CurrentScore', ], 'members' => [ 'MaxScore' => [ 'shape' => 'GenericDouble', ], 'CurrentScore' => [ 'shape' => 'GenericDouble', ], ], ], 'AnomalySubscription' => [ 'type' => 'structure', 'required' => [ 'MonitorArnList', 'Subscribers', 'Frequency', 'SubscriptionName', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'GenericString', ], 'AccountId' => [ 'shape' => 'GenericString', ], 'MonitorArnList' => [ 'shape' => 'MonitorArnList', ], 'Subscribers' => [ 'shape' => 'Subscribers', ], 'Threshold' => [ 'shape' => 'NullableNonNegativeDouble', 'deprecated' => true, 'deprecatedMessage' => 'Threshold has been deprecated in favor of ThresholdExpression', ], 'Frequency' => [ 'shape' => 'AnomalySubscriptionFrequency', ], 'SubscriptionName' => [ 'shape' => 'GenericString', ], 'ThresholdExpression' => [ 'shape' => 'Expression', ], ], ], 'AnomalySubscriptionFrequency' => [ 'type' => 'string', 'enum' => [ 'DAILY', 'IMMEDIATE', 'WEEKLY', ], ], 'AnomalySubscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalySubscription', ], ], 'ApproximateUsageRecordsPerService' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'NonNegativeLong', ], ], 'ApproximationDimension' => [ 'type' => 'string', 'enum' => [ 'SERVICE', 'RESOURCE', ], ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z0-9]*:[a-z0-9]+:[-a-z0-9]*:[0-9]{12}:[-a-zA-Z0-9/:_]+', ], 'AttributeType' => [ 'type' => 'string', ], 'AttributeValue' => [ 'type' => 'string', ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeType', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'BackfillLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'BillExpirationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Context' => [ 'type' => 'string', 'enum' => [ 'COST_AND_USAGE', 'RESERVATIONS', 'SAVINGS_PLANS', ], ], 'CostAllocationTag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'Type', 'Status', ], 'members' => [ 'TagKey' => [ 'shape' => 'TagKey', ], 'Type' => [ 'shape' => 'CostAllocationTagType', ], 'Status' => [ 'shape' => 'CostAllocationTagStatus', ], 'LastUpdatedDate' => [ 'shape' => 'ZonedDateTime', ], 'LastUsedDate' => [ 'shape' => 'ZonedDateTime', ], ], ], 'CostAllocationTagBackfillRequest' => [ 'type' => 'structure', 'members' => [ 'BackfillFrom' => [ 'shape' => 'ZonedDateTime', ], 'RequestedAt' => [ 'shape' => 'ZonedDateTime', ], 'CompletedAt' => [ 'shape' => 'ZonedDateTime', ], 'BackfillStatus' => [ 'shape' => 'CostAllocationTagBackfillStatus', ], 'LastUpdatedAt' => [ 'shape' => 'ZonedDateTime', ], ], ], 'CostAllocationTagBackfillRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostAllocationTagBackfillRequest', ], 'max' => 1000, 'min' => 0, ], 'CostAllocationTagBackfillStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'PROCESSING', 'FAILED', ], ], 'CostAllocationTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 100, 'min' => 1, ], 'CostAllocationTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostAllocationTag', ], 'max' => 100, 'min' => 0, ], 'CostAllocationTagStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'CostAllocationTagStatusEntry' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'Status', ], 'members' => [ 'TagKey' => [ 'shape' => 'TagKey', ], 'Status' => [ 'shape' => 'CostAllocationTagStatus', ], ], ], 'CostAllocationTagStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostAllocationTagStatusEntry', ], 'max' => 20, 'min' => 1, ], 'CostAllocationTagType' => [ 'type' => 'string', 'enum' => [ 'AWSGenerated', 'UserDefined', ], ], 'CostAllocationTagsMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'CostCategory' => [ 'type' => 'structure', 'required' => [ 'CostCategoryArn', 'EffectiveStart', 'Name', 'RuleVersion', 'Rules', ], 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], 'EffectiveEnd' => [ 'shape' => 'ZonedDateTime', ], 'Name' => [ 'shape' => 'CostCategoryName', ], 'RuleVersion' => [ 'shape' => 'CostCategoryRuleVersion', ], 'Rules' => [ 'shape' => 'CostCategoryRulesList', ], 'SplitChargeRules' => [ 'shape' => 'CostCategorySplitChargeRulesList', ], 'ProcessingStatus' => [ 'shape' => 'CostCategoryProcessingStatusList', ], 'DefaultValue' => [ 'shape' => 'CostCategoryValue', ], ], ], 'CostCategoryInheritedValueDimension' => [ 'type' => 'structure', 'members' => [ 'DimensionName' => [ 'shape' => 'CostCategoryInheritedValueDimensionName', ], 'DimensionKey' => [ 'shape' => 'GenericString', ], ], ], 'CostCategoryInheritedValueDimensionName' => [ 'type' => 'string', 'enum' => [ 'LINKED_ACCOUNT_NAME', 'TAG', ], ], 'CostCategoryMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'CostCategoryName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^(?! )[\\p{L}\\p{N}\\p{Z}-_]*(?<! )$', ], 'CostCategoryNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategoryName', ], ], 'CostCategoryProcessingStatus' => [ 'type' => 'structure', 'members' => [ 'Component' => [ 'shape' => 'CostCategoryStatusComponent', ], 'Status' => [ 'shape' => 'CostCategoryStatus', ], ], ], 'CostCategoryProcessingStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategoryProcessingStatus', ], ], 'CostCategoryReference' => [ 'type' => 'structure', 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'CostCategoryName', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], 'EffectiveEnd' => [ 'shape' => 'ZonedDateTime', ], 'NumberOfRules' => [ 'shape' => 'NonNegativeInteger', ], 'ProcessingStatus' => [ 'shape' => 'CostCategoryProcessingStatusList', ], 'Values' => [ 'shape' => 'CostCategoryValuesList', ], 'DefaultValue' => [ 'shape' => 'CostCategoryValue', ], ], ], 'CostCategoryReferencesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategoryReference', ], ], 'CostCategoryRule' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'CostCategoryValue', ], 'Rule' => [ 'shape' => 'Expression', ], 'InheritedValue' => [ 'shape' => 'CostCategoryInheritedValueDimension', ], 'Type' => [ 'shape' => 'CostCategoryRuleType', ], ], ], 'CostCategoryRuleType' => [ 'type' => 'string', 'enum' => [ 'REGULAR', 'INHERITED_VALUE', ], ], 'CostCategoryRuleVersion' => [ 'type' => 'string', 'enum' => [ 'CostCategoryExpression.v1', ], ], 'CostCategoryRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategoryRule', ], 'max' => 500, 'min' => 1, ], 'CostCategorySplitChargeMethod' => [ 'type' => 'string', 'enum' => [ 'FIXED', 'PROPORTIONAL', 'EVEN', ], ], 'CostCategorySplitChargeRule' => [ 'type' => 'structure', 'required' => [ 'Source', 'Targets', 'Method', ], 'members' => [ 'Source' => [ 'shape' => 'GenericString', ], 'Targets' => [ 'shape' => 'CostCategorySplitChargeRuleTargetsList', ], 'Method' => [ 'shape' => 'CostCategorySplitChargeMethod', ], 'Parameters' => [ 'shape' => 'CostCategorySplitChargeRuleParametersList', ], ], ], 'CostCategorySplitChargeRuleParameter' => [ 'type' => 'structure', 'required' => [ 'Type', 'Values', ], 'members' => [ 'Type' => [ 'shape' => 'CostCategorySplitChargeRuleParameterType', ], 'Values' => [ 'shape' => 'CostCategorySplitChargeRuleParameterValuesList', ], ], ], 'CostCategorySplitChargeRuleParameterType' => [ 'type' => 'string', 'enum' => [ 'ALLOCATION_PERCENTAGES', ], ], 'CostCategorySplitChargeRuleParameterValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 500, 'min' => 1, ], 'CostCategorySplitChargeRuleParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategorySplitChargeRuleParameter', ], 'max' => 10, 'min' => 1, ], 'CostCategorySplitChargeRuleTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 500, 'min' => 1, ], 'CostCategorySplitChargeRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategorySplitChargeRule', ], 'max' => 10, 'min' => 1, ], 'CostCategoryStatus' => [ 'type' => 'string', 'enum' => [ 'PROCESSING', 'APPLIED', ], ], 'CostCategoryStatusComponent' => [ 'type' => 'string', 'enum' => [ 'COST_EXPLORER', ], ], 'CostCategoryValue' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^(?! )[\\p{L}\\p{N}\\p{Z}-_]*(?<! )$', ], 'CostCategoryValues' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'CostCategoryName', ], 'Values' => [ 'shape' => 'Values', ], 'MatchOptions' => [ 'shape' => 'MatchOptions', ], ], ], 'CostCategoryValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CostCategoryValue', ], ], 'Coverage' => [ 'type' => 'structure', 'members' => [ 'CoverageHours' => [ 'shape' => 'CoverageHours', ], 'CoverageNormalizedUnits' => [ 'shape' => 'CoverageNormalizedUnits', ], 'CoverageCost' => [ 'shape' => 'CoverageCost', ], ], ], 'CoverageByTime' => [ 'type' => 'structure', 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Groups' => [ 'shape' => 'ReservationCoverageGroups', ], 'Total' => [ 'shape' => 'Coverage', ], ], ], 'CoverageCost' => [ 'type' => 'structure', 'members' => [ 'OnDemandCost' => [ 'shape' => 'OnDemandCost', ], ], ], 'CoverageHours' => [ 'type' => 'structure', 'members' => [ 'OnDemandHours' => [ 'shape' => 'OnDemandHours', ], 'ReservedHours' => [ 'shape' => 'ReservedHours', ], 'TotalRunningHours' => [ 'shape' => 'TotalRunningHours', ], 'CoverageHoursPercentage' => [ 'shape' => 'CoverageHoursPercentage', ], ], ], 'CoverageHoursPercentage' => [ 'type' => 'string', ], 'CoverageNormalizedUnits' => [ 'type' => 'structure', 'members' => [ 'OnDemandNormalizedUnits' => [ 'shape' => 'OnDemandNormalizedUnits', ], 'ReservedNormalizedUnits' => [ 'shape' => 'ReservedNormalizedUnits', ], 'TotalRunningNormalizedUnits' => [ 'shape' => 'TotalRunningNormalizedUnits', ], 'CoverageNormalizedUnitsPercentage' => [ 'shape' => 'CoverageNormalizedUnitsPercentage', ], ], ], 'CoverageNormalizedUnitsPercentage' => [ 'type' => 'string', ], 'CoveragesByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoverageByTime', ], ], 'CreateAnomalyMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyMonitor', ], 'members' => [ 'AnomalyMonitor' => [ 'shape' => 'AnomalyMonitor', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateAnomalyMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'MonitorArn', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], ], ], 'CreateAnomalySubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalySubscription', ], 'members' => [ 'AnomalySubscription' => [ 'shape' => 'AnomalySubscription', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateAnomalySubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'GenericString', ], ], ], 'CreateCostCategoryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RuleVersion', 'Rules', ], 'members' => [ 'Name' => [ 'shape' => 'CostCategoryName', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], 'RuleVersion' => [ 'shape' => 'CostCategoryRuleVersion', ], 'Rules' => [ 'shape' => 'CostCategoryRulesList', ], 'DefaultValue' => [ 'shape' => 'CostCategoryValue', ], 'SplitChargeRules' => [ 'shape' => 'CostCategorySplitChargeRulesList', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateCostCategoryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], ], ], 'CurrentInstance' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'GenericString', ], 'InstanceName' => [ 'shape' => 'GenericString', ], 'Tags' => [ 'shape' => 'TagValuesList', ], 'ResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'ResourceUtilization' => [ 'shape' => 'ResourceUtilization', ], 'ReservationCoveredHoursInLookbackPeriod' => [ 'shape' => 'GenericString', ], 'SavingsPlansCoveredHoursInLookbackPeriod' => [ 'shape' => 'GenericString', ], 'OnDemandHoursInLookbackPeriod' => [ 'shape' => 'GenericString', ], 'TotalRunningHoursInLookbackPeriod' => [ 'shape' => 'GenericString', ], 'MonthlyCost' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], ], ], 'DataUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DateInterval' => [ 'type' => 'structure', 'required' => [ 'Start', 'End', ], 'members' => [ 'Start' => [ 'shape' => 'YearMonthDay', ], 'End' => [ 'shape' => 'YearMonthDay', ], ], ], 'DeleteAnomalyMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'MonitorArn', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteAnomalyMonitorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAnomalySubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteAnomalySubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCostCategoryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'CostCategoryArn', ], 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteCostCategoryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveEnd' => [ 'shape' => 'ZonedDateTime', ], ], ], 'DescribeCostCategoryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'CostCategoryArn', ], 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveOn' => [ 'shape' => 'ZonedDateTime', ], ], ], 'DescribeCostCategoryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CostCategory' => [ 'shape' => 'CostCategory', ], ], ], 'Dimension' => [ 'type' => 'string', 'enum' => [ 'AZ', 'INSTANCE_TYPE', 'LINKED_ACCOUNT', 'LINKED_ACCOUNT_NAME', 'OPERATION', 'PURCHASE_TYPE', 'REGION', 'SERVICE', 'SERVICE_CODE', 'USAGE_TYPE', 'USAGE_TYPE_GROUP', 'RECORD_TYPE', 'OPERATING_SYSTEM', 'TENANCY', 'SCOPE', 'PLATFORM', 'SUBSCRIPTION_ID', 'LEGAL_ENTITY_NAME', 'DEPLOYMENT_OPTION', 'DATABASE_ENGINE', 'CACHE_ENGINE', 'INSTANCE_TYPE_FAMILY', 'BILLING_ENTITY', 'RESERVATION_ID', 'RESOURCE_ID', 'RIGHTSIZING_TYPE', 'SAVINGS_PLANS_TYPE', 'SAVINGS_PLAN_ARN', 'PAYMENT_OPTION', 'AGREEMENT_END_DATE_TIME_AFTER', 'AGREEMENT_END_DATE_TIME_BEFORE', 'INVOICING_ENTITY', 'ANOMALY_TOTAL_IMPACT_ABSOLUTE', 'ANOMALY_TOTAL_IMPACT_PERCENTAGE', ], ], 'DimensionValues' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'Dimension', ], 'Values' => [ 'shape' => 'Values', ], 'MatchOptions' => [ 'shape' => 'MatchOptions', ], ], ], 'DimensionValuesWithAttributes' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Value', ], 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'DimensionValuesWithAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionValuesWithAttributes', ], ], 'DiskResourceUtilization' => [ 'type' => 'structure', 'members' => [ 'DiskReadOpsPerSecond' => [ 'shape' => 'GenericString', ], 'DiskWriteOpsPerSecond' => [ 'shape' => 'GenericString', ], 'DiskReadBytesPerSecond' => [ 'shape' => 'GenericString', ], 'DiskWriteBytesPerSecond' => [ 'shape' => 'GenericString', ], ], ], 'EBSResourceUtilization' => [ 'type' => 'structure', 'members' => [ 'EbsReadOpsPerSecond' => [ 'shape' => 'GenericString', ], 'EbsWriteOpsPerSecond' => [ 'shape' => 'GenericString', ], 'EbsReadBytesPerSecond' => [ 'shape' => 'GenericString', ], 'EbsWriteBytesPerSecond' => [ 'shape' => 'GenericString', ], ], ], 'EC2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'GenericString', ], 'InstanceType' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'Platform' => [ 'shape' => 'GenericString', ], 'Tenancy' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'EC2ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'HourlyOnDemandRate' => [ 'shape' => 'GenericString', ], 'InstanceType' => [ 'shape' => 'GenericString', ], 'Platform' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'Sku' => [ 'shape' => 'GenericString', ], 'Memory' => [ 'shape' => 'GenericString', ], 'NetworkPerformance' => [ 'shape' => 'GenericString', ], 'Storage' => [ 'shape' => 'GenericString', ], 'Vcpu' => [ 'shape' => 'GenericString', ], ], ], 'EC2ResourceUtilization' => [ 'type' => 'structure', 'members' => [ 'MaxCpuUtilizationPercentage' => [ 'shape' => 'GenericString', ], 'MaxMemoryUtilizationPercentage' => [ 'shape' => 'GenericString', ], 'MaxStorageUtilizationPercentage' => [ 'shape' => 'GenericString', ], 'EBSResourceUtilization' => [ 'shape' => 'EBSResourceUtilization', ], 'DiskResourceUtilization' => [ 'shape' => 'DiskResourceUtilization', ], 'NetworkResourceUtilization' => [ 'shape' => 'NetworkResourceUtilization', ], ], ], 'EC2Specification' => [ 'type' => 'structure', 'members' => [ 'OfferingClass' => [ 'shape' => 'OfferingClass', ], ], ], 'ESInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceClass' => [ 'shape' => 'GenericString', ], 'InstanceSize' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'ElastiCacheInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'GenericString', ], 'NodeType' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'ProductDescription' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'Entity' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'Estimated' => [ 'type' => 'boolean', ], 'Expression' => [ 'type' => 'structure', 'members' => [ 'Or' => [ 'shape' => 'Expressions', ], 'And' => [ 'shape' => 'Expressions', ], 'Not' => [ 'shape' => 'Expression', ], 'Dimensions' => [ 'shape' => 'DimensionValues', ], 'Tags' => [ 'shape' => 'TagValues', ], 'CostCategories' => [ 'shape' => 'CostCategoryValues', ], ], ], 'Expressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Expression', ], ], 'FindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'CPU_OVER_PROVISIONED', 'CPU_UNDER_PROVISIONED', 'MEMORY_OVER_PROVISIONED', 'MEMORY_UNDER_PROVISIONED', 'EBS_THROUGHPUT_OVER_PROVISIONED', 'EBS_THROUGHPUT_UNDER_PROVISIONED', 'EBS_IOPS_OVER_PROVISIONED', 'EBS_IOPS_UNDER_PROVISIONED', 'NETWORK_BANDWIDTH_OVER_PROVISIONED', 'NETWORK_BANDWIDTH_UNDER_PROVISIONED', 'NETWORK_PPS_OVER_PROVISIONED', 'NETWORK_PPS_UNDER_PROVISIONED', 'DISK_IOPS_OVER_PROVISIONED', 'DISK_IOPS_UNDER_PROVISIONED', 'DISK_THROUGHPUT_OVER_PROVISIONED', 'DISK_THROUGHPUT_UNDER_PROVISIONED', ], ], 'FindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingReasonCode', ], ], 'ForecastResult' => [ 'type' => 'structure', 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'MeanValue' => [ 'shape' => 'GenericString', ], 'PredictionIntervalLowerBound' => [ 'shape' => 'GenericString', ], 'PredictionIntervalUpperBound' => [ 'shape' => 'GenericString', ], ], ], 'ForecastResultsByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForecastResult', ], ], 'GenerationExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'GenerationStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'PROCESSING', 'FAILED', ], ], 'GenerationSummary' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'RecommendationId', ], 'GenerationStatus' => [ 'shape' => 'GenerationStatus', ], 'GenerationStartedTime' => [ 'shape' => 'ZonedDateTime', ], 'GenerationCompletionTime' => [ 'shape' => 'ZonedDateTime', ], 'EstimatedCompletionTime' => [ 'shape' => 'ZonedDateTime', ], ], ], 'GenerationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenerationSummary', ], ], 'GenericBoolean' => [ 'type' => 'boolean', ], 'GenericDouble' => [ 'type' => 'double', ], 'GenericString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'GetAnomaliesRequest' => [ 'type' => 'structure', 'required' => [ 'DateInterval', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], 'DateInterval' => [ 'shape' => 'AnomalyDateInterval', ], 'Feedback' => [ 'shape' => 'AnomalyFeedbackType', ], 'TotalImpact' => [ 'shape' => 'TotalImpactFilter', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetAnomaliesResponse' => [ 'type' => 'structure', 'required' => [ 'Anomalies', ], 'members' => [ 'Anomalies' => [ 'shape' => 'Anomalies', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetAnomalyMonitorsRequest' => [ 'type' => 'structure', 'members' => [ 'MonitorArnList' => [ 'shape' => 'Values', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetAnomalyMonitorsResponse' => [ 'type' => 'structure', 'required' => [ 'AnomalyMonitors', ], 'members' => [ 'AnomalyMonitors' => [ 'shape' => 'AnomalyMonitors', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetAnomalySubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'SubscriptionArnList' => [ 'shape' => 'Values', ], 'MonitorArn' => [ 'shape' => 'GenericString', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetAnomalySubscriptionsResponse' => [ 'type' => 'structure', 'required' => [ 'AnomalySubscriptions', ], 'members' => [ 'AnomalySubscriptions' => [ 'shape' => 'AnomalySubscriptions', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetApproximateUsageRecordsRequest' => [ 'type' => 'structure', 'required' => [ 'Granularity', 'ApproximationDimension', ], 'members' => [ 'Granularity' => [ 'shape' => 'Granularity', ], 'Services' => [ 'shape' => 'UsageServices', ], 'ApproximationDimension' => [ 'shape' => 'ApproximationDimension', ], ], ], 'GetApproximateUsageRecordsResponse' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'ApproximateUsageRecordsPerService', ], 'TotalRecords' => [ 'shape' => 'NonNegativeLong', ], 'LookbackPeriod' => [ 'shape' => 'DateInterval', ], ], ], 'GetCostAndUsageRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Granularity', 'Metrics', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'Metrics' => [ 'shape' => 'MetricNames', ], 'GroupBy' => [ 'shape' => 'GroupDefinitions', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetCostAndUsageResponse' => [ 'type' => 'structure', 'members' => [ 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'GroupDefinitions' => [ 'shape' => 'GroupDefinitions', ], 'ResultsByTime' => [ 'shape' => 'ResultsByTime', ], 'DimensionValueAttributes' => [ 'shape' => 'DimensionValuesWithAttributesList', ], ], ], 'GetCostAndUsageWithResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Granularity', 'Filter', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'Metrics' => [ 'shape' => 'MetricNames', ], 'GroupBy' => [ 'shape' => 'GroupDefinitions', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetCostAndUsageWithResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'GroupDefinitions' => [ 'shape' => 'GroupDefinitions', ], 'ResultsByTime' => [ 'shape' => 'ResultsByTime', ], 'DimensionValueAttributes' => [ 'shape' => 'DimensionValuesWithAttributesList', ], ], ], 'GetCostCategoriesRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'SearchString' => [ 'shape' => 'SearchString', ], 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'CostCategoryName' => [ 'shape' => 'CostCategoryName', ], 'Filter' => [ 'shape' => 'Expression', ], 'SortBy' => [ 'shape' => 'SortDefinitions', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetCostCategoriesResponse' => [ 'type' => 'structure', 'required' => [ 'ReturnSize', 'TotalSize', ], 'members' => [ 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'CostCategoryNames' => [ 'shape' => 'CostCategoryNamesList', ], 'CostCategoryValues' => [ 'shape' => 'CostCategoryValuesList', ], 'ReturnSize' => [ 'shape' => 'PageSize', ], 'TotalSize' => [ 'shape' => 'PageSize', ], ], ], 'GetCostForecastRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Metric', 'Granularity', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Metric' => [ 'shape' => 'Metric', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'PredictionIntervalLevel' => [ 'shape' => 'PredictionIntervalLevel', ], ], ], 'GetCostForecastResponse' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'MetricValue', ], 'ForecastResultsByTime' => [ 'shape' => 'ForecastResultsByTime', ], ], ], 'GetDimensionValuesRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Dimension', ], 'members' => [ 'SearchString' => [ 'shape' => 'SearchString', ], 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Dimension' => [ 'shape' => 'Dimension', ], 'Context' => [ 'shape' => 'Context', ], 'Filter' => [ 'shape' => 'Expression', ], 'SortBy' => [ 'shape' => 'SortDefinitions', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetDimensionValuesResponse' => [ 'type' => 'structure', 'required' => [ 'DimensionValues', 'ReturnSize', 'TotalSize', ], 'members' => [ 'DimensionValues' => [ 'shape' => 'DimensionValuesWithAttributesList', ], 'ReturnSize' => [ 'shape' => 'PageSize', ], 'TotalSize' => [ 'shape' => 'PageSize', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetReservationCoverageRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'GroupBy' => [ 'shape' => 'GroupDefinitions', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'Metrics' => [ 'shape' => 'MetricNames', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'SortBy' => [ 'shape' => 'SortDefinition', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'GetReservationCoverageResponse' => [ 'type' => 'structure', 'required' => [ 'CoveragesByTime', ], 'members' => [ 'CoveragesByTime' => [ 'shape' => 'CoveragesByTime', ], 'Total' => [ 'shape' => 'Coverage', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetReservationPurchaseRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'Service', ], 'members' => [ 'AccountId' => [ 'shape' => 'GenericString', ], 'Service' => [ 'shape' => 'GenericString', ], 'Filter' => [ 'shape' => 'Expression', ], 'AccountScope' => [ 'shape' => 'AccountScope', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'TermInYears' => [ 'shape' => 'TermInYears', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'ServiceSpecification' => [ 'shape' => 'ServiceSpecification', ], 'PageSize' => [ 'shape' => 'NonNegativeInteger', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetReservationPurchaseRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'ReservationPurchaseRecommendationMetadata', ], 'Recommendations' => [ 'shape' => 'ReservationPurchaseRecommendations', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetReservationUtilizationRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'GroupBy' => [ 'shape' => 'GroupDefinitions', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'SortBy' => [ 'shape' => 'SortDefinition', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'GetReservationUtilizationResponse' => [ 'type' => 'structure', 'required' => [ 'UtilizationsByTime', ], 'members' => [ 'UtilizationsByTime' => [ 'shape' => 'UtilizationsByTime', ], 'Total' => [ 'shape' => 'ReservationAggregates', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetRightsizingRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'Service', ], 'members' => [ 'Filter' => [ 'shape' => 'Expression', ], 'Configuration' => [ 'shape' => 'RightsizingRecommendationConfiguration', ], 'Service' => [ 'shape' => 'GenericString', ], 'PageSize' => [ 'shape' => 'NonNegativeInteger', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetRightsizingRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'RightsizingRecommendationMetadata', ], 'Summary' => [ 'shape' => 'RightsizingRecommendationSummary', ], 'RightsizingRecommendations' => [ 'shape' => 'RightsizingRecommendationList', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'Configuration' => [ 'shape' => 'RightsizingRecommendationConfiguration', ], ], ], 'GetSavingsPlanPurchaseRecommendationDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'RecommendationDetailId', ], 'members' => [ 'RecommendationDetailId' => [ 'shape' => 'RecommendationDetailId', ], ], ], 'GetSavingsPlanPurchaseRecommendationDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'RecommendationDetailId' => [ 'shape' => 'RecommendationDetailId', ], 'RecommendationDetailData' => [ 'shape' => 'RecommendationDetailData', ], ], ], 'GetSavingsPlansCoverageRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'GroupBy' => [ 'shape' => 'GroupDefinitions', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'Metrics' => [ 'shape' => 'MetricNames', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'SortBy' => [ 'shape' => 'SortDefinition', ], ], ], 'GetSavingsPlansCoverageResponse' => [ 'type' => 'structure', 'required' => [ 'SavingsPlansCoverages', ], 'members' => [ 'SavingsPlansCoverages' => [ 'shape' => 'SavingsPlansCoverages', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetSavingsPlansPurchaseRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'SavingsPlansType', 'TermInYears', 'PaymentOption', 'LookbackPeriodInDays', ], 'members' => [ 'SavingsPlansType' => [ 'shape' => 'SupportedSavingsPlansType', ], 'TermInYears' => [ 'shape' => 'TermInYears', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'AccountScope' => [ 'shape' => 'AccountScope', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'PageSize' => [ 'shape' => 'NonNegativeInteger', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'Filter' => [ 'shape' => 'Expression', ], ], ], 'GetSavingsPlansPurchaseRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'SavingsPlansPurchaseRecommendationMetadata', ], 'SavingsPlansPurchaseRecommendation' => [ 'shape' => 'SavingsPlansPurchaseRecommendation', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetSavingsPlansUtilizationDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Filter' => [ 'shape' => 'Expression', ], 'DataType' => [ 'shape' => 'SavingsPlansDataTypes', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'SortBy' => [ 'shape' => 'SortDefinition', ], ], ], 'GetSavingsPlansUtilizationDetailsResponse' => [ 'type' => 'structure', 'required' => [ 'SavingsPlansUtilizationDetails', 'TimePeriod', ], 'members' => [ 'SavingsPlansUtilizationDetails' => [ 'shape' => 'SavingsPlansUtilizationDetails', ], 'Total' => [ 'shape' => 'SavingsPlansUtilizationAggregates', ], 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetSavingsPlansUtilizationRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'SortBy' => [ 'shape' => 'SortDefinition', ], ], ], 'GetSavingsPlansUtilizationResponse' => [ 'type' => 'structure', 'required' => [ 'Total', ], 'members' => [ 'SavingsPlansUtilizationsByTime' => [ 'shape' => 'SavingsPlansUtilizationsByTime', ], 'Total' => [ 'shape' => 'SavingsPlansUtilizationAggregates', ], ], ], 'GetTagsRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', ], 'members' => [ 'SearchString' => [ 'shape' => 'SearchString', ], 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'Filter' => [ 'shape' => 'Expression', ], 'SortBy' => [ 'shape' => 'SortDefinitions', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'GetTagsResponse' => [ 'type' => 'structure', 'required' => [ 'Tags', 'ReturnSize', 'TotalSize', ], 'members' => [ 'NextPageToken' => [ 'shape' => 'NextPageToken', ], 'Tags' => [ 'shape' => 'TagList', ], 'ReturnSize' => [ 'shape' => 'PageSize', ], 'TotalSize' => [ 'shape' => 'PageSize', ], ], ], 'GetUsageForecastRequest' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Metric', 'Granularity', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Metric' => [ 'shape' => 'Metric', ], 'Granularity' => [ 'shape' => 'Granularity', ], 'Filter' => [ 'shape' => 'Expression', ], 'PredictionIntervalLevel' => [ 'shape' => 'PredictionIntervalLevel', ], ], ], 'GetUsageForecastResponse' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'MetricValue', ], 'ForecastResultsByTime' => [ 'shape' => 'ForecastResultsByTime', ], ], ], 'Granularity' => [ 'type' => 'string', 'enum' => [ 'DAILY', 'MONTHLY', 'HOURLY', ], ], 'Group' => [ 'type' => 'structure', 'members' => [ 'Keys' => [ 'shape' => 'Keys', ], 'Metrics' => [ 'shape' => 'Metrics', ], ], ], 'GroupDefinition' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'GroupDefinitionType', ], 'Key' => [ 'shape' => 'GroupDefinitionKey', ], ], ], 'GroupDefinitionKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'GroupDefinitionType' => [ 'type' => 'string', 'enum' => [ 'DIMENSION', 'TAG', 'COST_CATEGORY', ], ], 'GroupDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupDefinition', ], ], 'Groups' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], ], 'Impact' => [ 'type' => 'structure', 'required' => [ 'MaxImpact', ], 'members' => [ 'MaxImpact' => [ 'shape' => 'GenericDouble', ], 'TotalImpact' => [ 'shape' => 'GenericDouble', ], 'TotalActualSpend' => [ 'shape' => 'NullableNonNegativeDouble', ], 'TotalExpectedSpend' => [ 'shape' => 'NullableNonNegativeDouble', ], 'TotalImpactPercentage' => [ 'shape' => 'NullableNonNegativeDouble', ], ], ], 'InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'EC2InstanceDetails' => [ 'shape' => 'EC2InstanceDetails', ], 'RDSInstanceDetails' => [ 'shape' => 'RDSInstanceDetails', ], 'RedshiftInstanceDetails' => [ 'shape' => 'RedshiftInstanceDetails', ], 'ElastiCacheInstanceDetails' => [ 'shape' => 'ElastiCacheInstanceDetails', ], 'ESInstanceDetails' => [ 'shape' => 'ESInstanceDetails', ], 'MemoryDBInstanceDetails' => [ 'shape' => 'MemoryDBInstanceDetails', ], ], ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Key' => [ 'type' => 'string', ], 'Keys' => [ 'type' => 'list', 'member' => [ 'shape' => 'Key', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListCostAllocationTagBackfillHistoryRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'CostAllocationTagsMaxResults', 'box' => true, ], ], ], 'ListCostAllocationTagBackfillHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'BackfillRequests' => [ 'shape' => 'CostAllocationTagBackfillRequestList', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListCostAllocationTagsRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'CostAllocationTagStatus', ], 'TagKeys' => [ 'shape' => 'CostAllocationTagKeyList', ], 'Type' => [ 'shape' => 'CostAllocationTagType', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'CostAllocationTagsMaxResults', 'box' => true, ], ], ], 'ListCostAllocationTagsResponse' => [ 'type' => 'structure', 'members' => [ 'CostAllocationTags' => [ 'shape' => 'CostAllocationTagList', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListCostCategoryDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'EffectiveOn' => [ 'shape' => 'ZonedDateTime', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], 'MaxResults' => [ 'shape' => 'CostCategoryMaxResults', 'box' => true, ], ], ], 'ListCostCategoryDefinitionsResponse' => [ 'type' => 'structure', 'members' => [ 'CostCategoryReferences' => [ 'shape' => 'CostCategoryReferencesList', ], 'NextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListSavingsPlansPurchaseRecommendationGenerationRequest' => [ 'type' => 'structure', 'members' => [ 'GenerationStatus' => [ 'shape' => 'GenerationStatus', ], 'RecommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'PageSize' => [ 'shape' => 'NonNegativeInteger', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListSavingsPlansPurchaseRecommendationGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'GenerationSummaryList' => [ 'shape' => 'GenerationSummaryList', ], 'NextPageToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'LookbackPeriodInDays' => [ 'type' => 'string', 'enum' => [ 'SEVEN_DAYS', 'THIRTY_DAYS', 'SIXTY_DAYS', ], ], 'MatchOption' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'ABSENT', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', 'CASE_SENSITIVE', 'CASE_INSENSITIVE', 'GREATER_THAN_OR_EQUAL', ], ], 'MatchOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchOption', ], ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'MemoryDBInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'GenericString', ], 'NodeType' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'Metric' => [ 'type' => 'string', 'enum' => [ 'BLENDED_COST', 'UNBLENDED_COST', 'AMORTIZED_COST', 'NET_UNBLENDED_COST', 'NET_AMORTIZED_COST', 'USAGE_QUANTITY', 'NORMALIZED_USAGE_AMOUNT', ], ], 'MetricAmount' => [ 'type' => 'string', ], 'MetricName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'MetricNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricName', ], ], 'MetricUnit' => [ 'type' => 'string', ], 'MetricValue' => [ 'type' => 'structure', 'members' => [ 'Amount' => [ 'shape' => 'MetricAmount', ], 'Unit' => [ 'shape' => 'MetricUnit', ], ], ], 'Metrics' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetricName', ], 'value' => [ 'shape' => 'MetricValue', ], ], 'MetricsOverLookbackPeriod' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationDetailHourlyMetrics', ], ], 'ModifyRecommendationDetail' => [ 'type' => 'structure', 'members' => [ 'TargetInstances' => [ 'shape' => 'TargetInstancesList', ], ], ], 'MonitorArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'MonitorDimension' => [ 'type' => 'string', 'enum' => [ 'SERVICE', ], ], 'MonitorType' => [ 'type' => 'string', 'enum' => [ 'DIMENSIONAL', 'CUSTOM', ], ], 'NetRISavings' => [ 'type' => 'string', ], 'NetworkResourceUtilization' => [ 'type' => 'structure', 'members' => [ 'NetworkInBytesPerSecond' => [ 'shape' => 'GenericString', ], 'NetworkOutBytesPerSecond' => [ 'shape' => 'GenericString', ], 'NetworkPacketsInPerSecond' => [ 'shape' => 'GenericString', ], 'NetworkPacketsOutPerSecond' => [ 'shape' => 'GenericString', ], ], ], 'NextPageToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'NonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NonNegativeLong' => [ 'type' => 'long', 'min' => 0, ], 'NullableNonNegativeDouble' => [ 'type' => 'double', 'min' => 0.0, ], 'NumericOperator' => [ 'type' => 'string', 'enum' => [ 'EQUAL', 'GREATER_THAN_OR_EQUAL', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN', 'LESS_THAN', 'BETWEEN', ], ], 'OfferingClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'CONVERTIBLE', ], ], 'OnDemandCost' => [ 'type' => 'string', ], 'OnDemandCostOfRIHoursUsed' => [ 'type' => 'string', ], 'OnDemandHours' => [ 'type' => 'string', ], 'OnDemandNormalizedUnits' => [ 'type' => 'string', ], 'PageSize' => [ 'type' => 'integer', ], 'PaymentOption' => [ 'type' => 'string', 'enum' => [ 'NO_UPFRONT', 'PARTIAL_UPFRONT', 'ALL_UPFRONT', 'LIGHT_UTILIZATION', 'MEDIUM_UTILIZATION', 'HEAVY_UTILIZATION', ], ], 'PlatformDifference' => [ 'type' => 'string', 'enum' => [ 'HYPERVISOR', 'NETWORK_INTERFACE', 'STORAGE_INTERFACE', 'INSTANCE_STORE_AVAILABILITY', 'VIRTUALIZATION_TYPE', ], ], 'PlatformDifferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatformDifference', ], ], 'PredictionIntervalLevel' => [ 'type' => 'integer', 'max' => 99, 'min' => 51, ], 'ProvideAnomalyFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'AnomalyId', 'Feedback', ], 'members' => [ 'AnomalyId' => [ 'shape' => 'GenericString', ], 'Feedback' => [ 'shape' => 'AnomalyFeedbackType', ], ], ], 'ProvideAnomalyFeedbackResponse' => [ 'type' => 'structure', 'required' => [ 'AnomalyId', ], 'members' => [ 'AnomalyId' => [ 'shape' => 'GenericString', ], ], ], 'PurchasedHours' => [ 'type' => 'string', ], 'PurchasedUnits' => [ 'type' => 'string', ], 'RDSInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'GenericString', ], 'InstanceType' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'DatabaseEngine' => [ 'shape' => 'GenericString', ], 'DatabaseEdition' => [ 'shape' => 'GenericString', ], 'DeploymentOption' => [ 'shape' => 'GenericString', ], 'LicenseModel' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'RICostForUnusedHours' => [ 'type' => 'string', ], 'RealizedSavings' => [ 'type' => 'string', ], 'RecommendationDetailData' => [ 'type' => 'structure', 'members' => [ 'AccountScope' => [ 'shape' => 'AccountScope', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'SavingsPlansType' => [ 'shape' => 'SupportedSavingsPlansType', ], 'TermInYears' => [ 'shape' => 'TermInYears', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'AccountId' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], 'InstanceFamily' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'OfferingId' => [ 'shape' => 'GenericString', ], 'GenerationTimestamp' => [ 'shape' => 'ZonedDateTime', ], 'LatestUsageTimestamp' => [ 'shape' => 'ZonedDateTime', ], 'CurrentAverageHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'CurrentMaximumHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'CurrentMinimumHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'EstimatedAverageUtilization' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'EstimatedOnDemandCost' => [ 'shape' => 'GenericString', ], 'EstimatedOnDemandCostWithCurrentCommitment' => [ 'shape' => 'GenericString', ], 'EstimatedROI' => [ 'shape' => 'GenericString', ], 'EstimatedSPCost' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsAmount' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsPercentage' => [ 'shape' => 'GenericString', ], 'ExistingHourlyCommitment' => [ 'shape' => 'GenericString', ], 'HourlyCommitmentToPurchase' => [ 'shape' => 'GenericString', ], 'UpfrontCost' => [ 'shape' => 'GenericString', ], 'CurrentAverageCoverage' => [ 'shape' => 'GenericString', ], 'EstimatedAverageCoverage' => [ 'shape' => 'GenericString', ], 'MetricsOverLookbackPeriod' => [ 'shape' => 'MetricsOverLookbackPeriod', ], ], ], 'RecommendationDetailHourlyMetrics' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'ZonedDateTime', ], 'EstimatedOnDemandCost' => [ 'shape' => 'GenericString', ], 'CurrentCoverage' => [ 'shape' => 'GenericString', ], 'EstimatedCoverage' => [ 'shape' => 'GenericString', ], 'EstimatedNewCommitmentUtilization' => [ 'shape' => 'GenericString', ], ], ], 'RecommendationDetailId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$', ], 'RecommendationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$', ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationId', ], ], 'RecommendationTarget' => [ 'type' => 'string', 'enum' => [ 'SAME_INSTANCE_FAMILY', 'CROSS_INSTANCE_FAMILY', ], ], 'RedshiftInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'GenericString', ], 'NodeType' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'CurrentGeneration' => [ 'shape' => 'GenericBoolean', ], 'SizeFlexEligible' => [ 'shape' => 'GenericBoolean', ], ], ], 'RequestChangedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ReservationAggregates' => [ 'type' => 'structure', 'members' => [ 'UtilizationPercentage' => [ 'shape' => 'UtilizationPercentage', ], 'UtilizationPercentageInUnits' => [ 'shape' => 'UtilizationPercentageInUnits', ], 'PurchasedHours' => [ 'shape' => 'PurchasedHours', ], 'PurchasedUnits' => [ 'shape' => 'PurchasedUnits', ], 'TotalActualHours' => [ 'shape' => 'TotalActualHours', ], 'TotalActualUnits' => [ 'shape' => 'TotalActualUnits', ], 'UnusedHours' => [ 'shape' => 'UnusedHours', ], 'UnusedUnits' => [ 'shape' => 'UnusedUnits', ], 'OnDemandCostOfRIHoursUsed' => [ 'shape' => 'OnDemandCostOfRIHoursUsed', ], 'NetRISavings' => [ 'shape' => 'NetRISavings', ], 'TotalPotentialRISavings' => [ 'shape' => 'TotalPotentialRISavings', ], 'AmortizedUpfrontFee' => [ 'shape' => 'AmortizedUpfrontFee', ], 'AmortizedRecurringFee' => [ 'shape' => 'AmortizedRecurringFee', ], 'TotalAmortizedFee' => [ 'shape' => 'TotalAmortizedFee', ], 'RICostForUnusedHours' => [ 'shape' => 'RICostForUnusedHours', ], 'RealizedSavings' => [ 'shape' => 'RealizedSavings', ], 'UnrealizedSavings' => [ 'shape' => 'UnrealizedSavings', ], ], ], 'ReservationCoverageGroup' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'Attributes', ], 'Coverage' => [ 'shape' => 'Coverage', ], ], ], 'ReservationCoverageGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservationCoverageGroup', ], ], 'ReservationGroupKey' => [ 'type' => 'string', ], 'ReservationGroupValue' => [ 'type' => 'string', ], 'ReservationPurchaseRecommendation' => [ 'type' => 'structure', 'members' => [ 'AccountScope' => [ 'shape' => 'AccountScope', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'TermInYears' => [ 'shape' => 'TermInYears', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'ServiceSpecification' => [ 'shape' => 'ServiceSpecification', ], 'RecommendationDetails' => [ 'shape' => 'ReservationPurchaseRecommendationDetails', ], 'RecommendationSummary' => [ 'shape' => 'ReservationPurchaseRecommendationSummary', ], ], ], 'ReservationPurchaseRecommendationDetail' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'GenericString', ], 'InstanceDetails' => [ 'shape' => 'InstanceDetails', ], 'RecommendedNumberOfInstancesToPurchase' => [ 'shape' => 'GenericString', ], 'RecommendedNormalizedUnitsToPurchase' => [ 'shape' => 'GenericString', ], 'MinimumNumberOfInstancesUsedPerHour' => [ 'shape' => 'GenericString', ], 'MinimumNormalizedUnitsUsedPerHour' => [ 'shape' => 'GenericString', ], 'MaximumNumberOfInstancesUsedPerHour' => [ 'shape' => 'GenericString', ], 'MaximumNormalizedUnitsUsedPerHour' => [ 'shape' => 'GenericString', ], 'AverageNumberOfInstancesUsedPerHour' => [ 'shape' => 'GenericString', ], 'AverageNormalizedUnitsUsedPerHour' => [ 'shape' => 'GenericString', ], 'AverageUtilization' => [ 'shape' => 'GenericString', ], 'EstimatedBreakEvenInMonths' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavingsPercentage' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlyOnDemandCost' => [ 'shape' => 'GenericString', ], 'EstimatedReservationCostForLookbackPeriod' => [ 'shape' => 'GenericString', ], 'UpfrontCost' => [ 'shape' => 'GenericString', ], 'RecurringStandardMonthlyCost' => [ 'shape' => 'GenericString', ], ], ], 'ReservationPurchaseRecommendationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservationPurchaseRecommendationDetail', ], ], 'ReservationPurchaseRecommendationMetadata' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'GenericString', ], 'GenerationTimestamp' => [ 'shape' => 'GenericString', ], ], ], 'ReservationPurchaseRecommendationSummary' => [ 'type' => 'structure', 'members' => [ 'TotalEstimatedMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'TotalEstimatedMonthlySavingsPercentage' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], ], ], 'ReservationPurchaseRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservationPurchaseRecommendation', ], ], 'ReservationUtilizationGroup' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ReservationGroupKey', ], 'Value' => [ 'shape' => 'ReservationGroupValue', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'Utilization' => [ 'shape' => 'ReservationAggregates', ], ], ], 'ReservationUtilizationGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservationUtilizationGroup', ], ], 'ReservedHours' => [ 'type' => 'string', ], 'ReservedNormalizedUnits' => [ 'type' => 'string', ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'EC2ResourceDetails' => [ 'shape' => 'EC2ResourceDetails', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceName' => [ 'shape' => 'Arn', ], ], 'exception' => true, ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'ResourceTagKey', ], 'Value' => [ 'shape' => 'ResourceTagValue', ], ], ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTagKey', ], 'max' => 200, 'min' => 0, ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], 'max' => 200, 'min' => 0, ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ResourceUtilization' => [ 'type' => 'structure', 'members' => [ 'EC2ResourceUtilization' => [ 'shape' => 'EC2ResourceUtilization', ], ], ], 'ResultByTime' => [ 'type' => 'structure', 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Total' => [ 'shape' => 'Metrics', ], 'Groups' => [ 'shape' => 'Groups', ], 'Estimated' => [ 'shape' => 'Estimated', ], ], ], 'ResultsByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultByTime', ], ], 'RightsizingRecommendation' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'GenericString', ], 'CurrentInstance' => [ 'shape' => 'CurrentInstance', ], 'RightsizingType' => [ 'shape' => 'RightsizingType', ], 'ModifyRecommendationDetail' => [ 'shape' => 'ModifyRecommendationDetail', ], 'TerminateRecommendationDetail' => [ 'shape' => 'TerminateRecommendationDetail', ], 'FindingReasonCodes' => [ 'shape' => 'FindingReasonCodes', ], ], ], 'RightsizingRecommendationConfiguration' => [ 'type' => 'structure', 'required' => [ 'RecommendationTarget', 'BenefitsConsidered', ], 'members' => [ 'RecommendationTarget' => [ 'shape' => 'RecommendationTarget', ], 'BenefitsConsidered' => [ 'shape' => 'GenericBoolean', ], ], ], 'RightsizingRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RightsizingRecommendation', ], ], 'RightsizingRecommendationMetadata' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'GenericString', ], 'GenerationTimestamp' => [ 'shape' => 'GenericString', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'AdditionalMetadata' => [ 'shape' => 'GenericString', ], ], ], 'RightsizingRecommendationSummary' => [ 'type' => 'structure', 'members' => [ 'TotalRecommendationCount' => [ 'shape' => 'GenericString', ], 'EstimatedTotalMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'SavingsCurrencyCode' => [ 'shape' => 'GenericString', ], 'SavingsPercentage' => [ 'shape' => 'GenericString', ], ], ], 'RightsizingType' => [ 'type' => 'string', 'enum' => [ 'TERMINATE', 'MODIFY', ], ], 'RootCause' => [ 'type' => 'structure', 'members' => [ 'Service' => [ 'shape' => 'GenericString', ], 'Region' => [ 'shape' => 'GenericString', ], 'LinkedAccount' => [ 'shape' => 'GenericString', ], 'UsageType' => [ 'shape' => 'GenericString', ], 'LinkedAccountName' => [ 'shape' => 'GenericString', ], ], ], 'RootCauses' => [ 'type' => 'list', 'member' => [ 'shape' => 'RootCause', ], ], 'SavingsPlanArn' => [ 'type' => 'string', ], 'SavingsPlansAmortizedCommitment' => [ 'type' => 'structure', 'members' => [ 'AmortizedRecurringCommitment' => [ 'shape' => 'GenericString', ], 'AmortizedUpfrontCommitment' => [ 'shape' => 'GenericString', ], 'TotalAmortizedCommitment' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansCoverage' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'Attributes', ], 'Coverage' => [ 'shape' => 'SavingsPlansCoverageData', ], 'TimePeriod' => [ 'shape' => 'DateInterval', ], ], ], 'SavingsPlansCoverageData' => [ 'type' => 'structure', 'members' => [ 'SpendCoveredBySavingsPlans' => [ 'shape' => 'GenericString', ], 'OnDemandCost' => [ 'shape' => 'GenericString', ], 'TotalCost' => [ 'shape' => 'GenericString', ], 'CoveragePercentage' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansCoverages' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavingsPlansCoverage', ], ], 'SavingsPlansDataType' => [ 'type' => 'string', 'enum' => [ 'ATTRIBUTES', 'UTILIZATION', 'AMORTIZED_COMMITMENT', 'SAVINGS', ], ], 'SavingsPlansDataTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavingsPlansDataType', ], ], 'SavingsPlansDetails' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'GenericString', ], 'InstanceFamily' => [ 'shape' => 'GenericString', ], 'OfferingId' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansPurchaseRecommendation' => [ 'type' => 'structure', 'members' => [ 'AccountScope' => [ 'shape' => 'AccountScope', ], 'SavingsPlansType' => [ 'shape' => 'SupportedSavingsPlansType', ], 'TermInYears' => [ 'shape' => 'TermInYears', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'LookbackPeriodInDays' => [ 'shape' => 'LookbackPeriodInDays', ], 'SavingsPlansPurchaseRecommendationDetails' => [ 'shape' => 'SavingsPlansPurchaseRecommendationDetailList', ], 'SavingsPlansPurchaseRecommendationSummary' => [ 'shape' => 'SavingsPlansPurchaseRecommendationSummary', ], ], ], 'SavingsPlansPurchaseRecommendationDetail' => [ 'type' => 'structure', 'members' => [ 'SavingsPlansDetails' => [ 'shape' => 'SavingsPlansDetails', ], 'AccountId' => [ 'shape' => 'GenericString', ], 'UpfrontCost' => [ 'shape' => 'GenericString', ], 'EstimatedROI' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], 'EstimatedSPCost' => [ 'shape' => 'GenericString', ], 'EstimatedOnDemandCost' => [ 'shape' => 'GenericString', ], 'EstimatedOnDemandCostWithCurrentCommitment' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsAmount' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsPercentage' => [ 'shape' => 'GenericString', ], 'HourlyCommitmentToPurchase' => [ 'shape' => 'GenericString', ], 'EstimatedAverageUtilization' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'CurrentMinimumHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'CurrentMaximumHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'CurrentAverageHourlyOnDemandSpend' => [ 'shape' => 'GenericString', ], 'RecommendationDetailId' => [ 'shape' => 'RecommendationDetailId', ], ], ], 'SavingsPlansPurchaseRecommendationDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavingsPlansPurchaseRecommendationDetail', ], ], 'SavingsPlansPurchaseRecommendationMetadata' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'GenericString', ], 'GenerationTimestamp' => [ 'shape' => 'GenericString', ], 'AdditionalMetadata' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansPurchaseRecommendationSummary' => [ 'type' => 'structure', 'members' => [ 'EstimatedROI' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], 'EstimatedTotalCost' => [ 'shape' => 'GenericString', ], 'CurrentOnDemandSpend' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsAmount' => [ 'shape' => 'GenericString', ], 'TotalRecommendationCount' => [ 'shape' => 'GenericString', ], 'DailyCommitmentToPurchase' => [ 'shape' => 'GenericString', ], 'HourlyCommitmentToPurchase' => [ 'shape' => 'GenericString', ], 'EstimatedSavingsPercentage' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavingsAmount' => [ 'shape' => 'GenericString', ], 'EstimatedOnDemandCostWithCurrentCommitment' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansSavings' => [ 'type' => 'structure', 'members' => [ 'NetSavings' => [ 'shape' => 'GenericString', ], 'OnDemandCostEquivalent' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansUtilization' => [ 'type' => 'structure', 'members' => [ 'TotalCommitment' => [ 'shape' => 'GenericString', ], 'UsedCommitment' => [ 'shape' => 'GenericString', ], 'UnusedCommitment' => [ 'shape' => 'GenericString', ], 'UtilizationPercentage' => [ 'shape' => 'GenericString', ], ], ], 'SavingsPlansUtilizationAggregates' => [ 'type' => 'structure', 'required' => [ 'Utilization', ], 'members' => [ 'Utilization' => [ 'shape' => 'SavingsPlansUtilization', ], 'Savings' => [ 'shape' => 'SavingsPlansSavings', ], 'AmortizedCommitment' => [ 'shape' => 'SavingsPlansAmortizedCommitment', ], ], ], 'SavingsPlansUtilizationByTime' => [ 'type' => 'structure', 'required' => [ 'TimePeriod', 'Utilization', ], 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Utilization' => [ 'shape' => 'SavingsPlansUtilization', ], 'Savings' => [ 'shape' => 'SavingsPlansSavings', ], 'AmortizedCommitment' => [ 'shape' => 'SavingsPlansAmortizedCommitment', ], ], ], 'SavingsPlansUtilizationDetail' => [ 'type' => 'structure', 'members' => [ 'SavingsPlanArn' => [ 'shape' => 'SavingsPlanArn', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'Utilization' => [ 'shape' => 'SavingsPlansUtilization', ], 'Savings' => [ 'shape' => 'SavingsPlansSavings', ], 'AmortizedCommitment' => [ 'shape' => 'SavingsPlansAmortizedCommitment', ], ], ], 'SavingsPlansUtilizationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavingsPlansUtilizationDetail', ], ], 'SavingsPlansUtilizationsByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavingsPlansUtilizationByTime', ], ], 'SearchString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ServiceSpecification' => [ 'type' => 'structure', 'members' => [ 'EC2Specification' => [ 'shape' => 'EC2Specification', ], ], ], 'SortDefinition' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'SortDefinitionKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortDefinitionKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'SortDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortDefinition', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartCostAllocationTagBackfillRequest' => [ 'type' => 'structure', 'required' => [ 'BackfillFrom', ], 'members' => [ 'BackfillFrom' => [ 'shape' => 'ZonedDateTime', ], ], ], 'StartCostAllocationTagBackfillResponse' => [ 'type' => 'structure', 'members' => [ 'BackfillRequest' => [ 'shape' => 'CostAllocationTagBackfillRequest', ], ], ], 'StartSavingsPlansPurchaseRecommendationGenerationRequest' => [ 'type' => 'structure', 'members' => [], ], 'StartSavingsPlansPurchaseRecommendationGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'RecommendationId', ], 'GenerationStartedTime' => [ 'shape' => 'ZonedDateTime', ], 'EstimatedCompletionTime' => [ 'shape' => 'ZonedDateTime', ], ], ], 'Subscriber' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'SubscriberAddress', ], 'Type' => [ 'shape' => 'SubscriberType', ], 'Status' => [ 'shape' => 'SubscriberStatus', ], ], ], 'SubscriberAddress' => [ 'type' => 'string', 'max' => 302, 'min' => 6, 'pattern' => '(^[a-zA-Z0-9.!#$%&\'*+=?^_‘{|}~-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$)|(^arn:(aws[a-zA-Z-]*):sns:[a-zA-Z0-9-]+:[0-9]{12}:[a-zA-Z0-9_-]+(\\.fifo)?$)', ], 'SubscriberStatus' => [ 'type' => 'string', 'enum' => [ 'CONFIRMED', 'DECLINED', ], ], 'SubscriberType' => [ 'type' => 'string', 'enum' => [ 'EMAIL', 'SNS', ], ], 'Subscribers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subscriber', ], ], 'SupportedSavingsPlansType' => [ 'type' => 'string', 'enum' => [ 'COMPUTE_SP', 'EC2_INSTANCE_SP', 'SAGEMAKER_SP', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entity', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValues' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Values' => [ 'shape' => 'Values', ], 'MatchOptions' => [ 'shape' => 'MatchOptions', ], ], ], 'TagValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagValues', ], ], 'TargetInstance' => [ 'type' => 'structure', 'members' => [ 'EstimatedMonthlyCost' => [ 'shape' => 'GenericString', ], 'EstimatedMonthlySavings' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], 'DefaultTargetInstance' => [ 'shape' => 'GenericBoolean', ], 'ResourceDetails' => [ 'shape' => 'ResourceDetails', ], 'ExpectedResourceUtilization' => [ 'shape' => 'ResourceUtilization', ], 'PlatformDifferences' => [ 'shape' => 'PlatformDifferences', ], ], ], 'TargetInstancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetInstance', ], ], 'TermInYears' => [ 'type' => 'string', 'enum' => [ 'ONE_YEAR', 'THREE_YEARS', ], ], 'TerminateRecommendationDetail' => [ 'type' => 'structure', 'members' => [ 'EstimatedMonthlySavings' => [ 'shape' => 'GenericString', ], 'CurrencyCode' => [ 'shape' => 'GenericString', ], ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceName' => [ 'shape' => 'Arn', ], ], 'exception' => true, ], 'TotalActualHours' => [ 'type' => 'string', ], 'TotalActualUnits' => [ 'type' => 'string', ], 'TotalAmortizedFee' => [ 'type' => 'string', ], 'TotalImpactFilter' => [ 'type' => 'structure', 'required' => [ 'NumericOperator', 'StartValue', ], 'members' => [ 'NumericOperator' => [ 'shape' => 'NumericOperator', ], 'StartValue' => [ 'shape' => 'GenericDouble', ], 'EndValue' => [ 'shape' => 'GenericDouble', ], ], ], 'TotalPotentialRISavings' => [ 'type' => 'string', ], 'TotalRunningHours' => [ 'type' => 'string', ], 'TotalRunningNormalizedUnits' => [ 'type' => 'string', ], 'UnknownMonitorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UnknownSubscriptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UnrealizedSavings' => [ 'type' => 'string', ], 'UnresolvableUsageUnitException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'ResourceTagKeys' => [ 'shape' => 'ResourceTagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UnusedHours' => [ 'type' => 'string', ], 'UnusedUnits' => [ 'type' => 'string', ], 'UpdateAnomalyMonitorRequest' => [ 'type' => 'structure', 'required' => [ 'MonitorArn', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], 'MonitorName' => [ 'shape' => 'GenericString', ], ], ], 'UpdateAnomalyMonitorResponse' => [ 'type' => 'structure', 'required' => [ 'MonitorArn', ], 'members' => [ 'MonitorArn' => [ 'shape' => 'GenericString', ], ], ], 'UpdateAnomalySubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'GenericString', ], 'Threshold' => [ 'shape' => 'NullableNonNegativeDouble', 'deprecated' => true, 'deprecatedMessage' => 'Threshold has been deprecated in favor of ThresholdExpression', ], 'Frequency' => [ 'shape' => 'AnomalySubscriptionFrequency', ], 'MonitorArnList' => [ 'shape' => 'MonitorArnList', ], 'Subscribers' => [ 'shape' => 'Subscribers', ], 'SubscriptionName' => [ 'shape' => 'GenericString', ], 'ThresholdExpression' => [ 'shape' => 'Expression', ], ], ], 'UpdateAnomalySubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'SubscriptionArn', ], 'members' => [ 'SubscriptionArn' => [ 'shape' => 'GenericString', ], ], ], 'UpdateCostAllocationTagsStatusError' => [ 'type' => 'structure', 'members' => [ 'TagKey' => [ 'shape' => 'TagKey', ], 'Code' => [ 'shape' => 'GenericString', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'UpdateCostAllocationTagsStatusErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateCostAllocationTagsStatusError', ], 'max' => 20, 'min' => 0, ], 'UpdateCostAllocationTagsStatusRequest' => [ 'type' => 'structure', 'required' => [ 'CostAllocationTagsStatus', ], 'members' => [ 'CostAllocationTagsStatus' => [ 'shape' => 'CostAllocationTagStatusList', ], ], ], 'UpdateCostAllocationTagsStatusResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'UpdateCostAllocationTagsStatusErrors', ], ], ], 'UpdateCostCategoryDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'CostCategoryArn', 'RuleVersion', 'Rules', ], 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], 'RuleVersion' => [ 'shape' => 'CostCategoryRuleVersion', ], 'Rules' => [ 'shape' => 'CostCategoryRulesList', ], 'DefaultValue' => [ 'shape' => 'CostCategoryValue', ], 'SplitChargeRules' => [ 'shape' => 'CostCategorySplitChargeRulesList', ], ], ], 'UpdateCostCategoryDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'CostCategoryArn' => [ 'shape' => 'Arn', ], 'EffectiveStart' => [ 'shape' => 'ZonedDateTime', ], ], ], 'UsageServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'UtilizationByTime' => [ 'type' => 'structure', 'members' => [ 'TimePeriod' => [ 'shape' => 'DateInterval', ], 'Groups' => [ 'shape' => 'ReservationUtilizationGroups', ], 'Total' => [ 'shape' => 'ReservationAggregates', ], ], ], 'UtilizationPercentage' => [ 'type' => 'string', ], 'UtilizationPercentageInUnits' => [ 'type' => 'string', ], 'UtilizationsByTime' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtilizationByTime', ], ], 'Value' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], ], 'YearMonthDay' => [ 'type' => 'string', 'max' => 40, 'min' => 0, 'pattern' => '(\\d{4}-\\d{2}-\\d{2})(T\\d{2}:\\d{2}:\\d{2}Z)?', ], 'ZonedDateTime' => [ 'type' => 'string', 'max' => 25, 'min' => 20, 'pattern' => '^\\d{4}-\\d\\d-\\d\\dT\\d\\d:\\d\\d:\\d\\d(([+-]\\d\\d:\\d\\d)|Z)$', ], ],];
