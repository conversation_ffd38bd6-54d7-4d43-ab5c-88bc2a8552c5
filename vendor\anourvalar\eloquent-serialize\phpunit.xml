<?xml version="1.0" encoding="UTF-8"?>
<phpunit
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  bootstrap="vendor/autoload.php"
  backupGlobals="false"
  colors="true"
  processIsolation="true"
  stopOnFailure="true"
  xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd"
  cacheDirectory=".phpunit.cache"
  backupStaticProperties="false"
>
  <testsuites>
    <testsuite name="EloquentSerialize">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
  <php>
    <env name="DB_CONNECTION" value="testing"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">src/</directory>
    </include>
  </source>
</phpunit>
