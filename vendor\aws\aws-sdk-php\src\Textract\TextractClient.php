<?php
namespace Aws\Textract;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Textract** service.
 * @method \Aws\Result analyzeDocument(array $args = [])
 * @method \GuzzleHttp\Promise\Promise analyzeDocumentAsync(array $args = [])
 * @method \Aws\Result analyzeExpense(array $args = [])
 * @method \GuzzleHttp\Promise\Promise analyzeExpenseAsync(array $args = [])
 * @method \Aws\Result analyzeID(array $args = [])
 * @method \GuzzleHttp\Promise\Promise analyzeIDAsync(array $args = [])
 * @method \Aws\Result createAdapter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAdapterAsync(array $args = [])
 * @method \Aws\Result createAdapterVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAdapterVersionAsync(array $args = [])
 * @method \Aws\Result deleteAdapter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAdapterAsync(array $args = [])
 * @method \Aws\Result deleteAdapterVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAdapterVersionAsync(array $args = [])
 * @method \Aws\Result detectDocumentText(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detectDocumentTextAsync(array $args = [])
 * @method \Aws\Result getAdapter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAdapterAsync(array $args = [])
 * @method \Aws\Result getAdapterVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAdapterVersionAsync(array $args = [])
 * @method \Aws\Result getDocumentAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDocumentAnalysisAsync(array $args = [])
 * @method \Aws\Result getDocumentTextDetection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDocumentTextDetectionAsync(array $args = [])
 * @method \Aws\Result getExpenseAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getExpenseAnalysisAsync(array $args = [])
 * @method \Aws\Result getLendingAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLendingAnalysisAsync(array $args = [])
 * @method \Aws\Result getLendingAnalysisSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLendingAnalysisSummaryAsync(array $args = [])
 * @method \Aws\Result listAdapterVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAdapterVersionsAsync(array $args = [])
 * @method \Aws\Result listAdapters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAdaptersAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startDocumentAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDocumentAnalysisAsync(array $args = [])
 * @method \Aws\Result startDocumentTextDetection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDocumentTextDetectionAsync(array $args = [])
 * @method \Aws\Result startExpenseAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startExpenseAnalysisAsync(array $args = [])
 * @method \Aws\Result startLendingAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startLendingAnalysisAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAdapter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAdapterAsync(array $args = [])
 */
class TextractClient extends AwsClient {}
